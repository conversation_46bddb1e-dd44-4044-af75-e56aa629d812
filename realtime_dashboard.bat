@echo off
setlocal enabledelayedexpansion
title Selenium 3-Thread Real-Time Dashboard

:MAIN_LOOP
cls
echo ========================================
echo SELENIUM 3-THREAD REAL-TIME DASHBOARD
echo ========================================
echo Current Time: %date% %time%
echo.

:: Check if tests are running
tasklist /FI "IMAGENAME eq java.exe" 2>nul | find /i "java.exe" >nul
if %ERRORLEVEL%==0 (
    echo STATUS: TESTS ARE RUNNING
    set TEST_STATUS=RUNNING
) else (
    echo STATUS: NO TESTS DETECTED
    set TEST_STATUS=IDLE
)
echo.

echo === DOCKER CONTAINER STATUS ===
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" 2>nul | findstr selenium
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Docker containers not accessible!
    goto :WAIT_AND_LOOP
)
echo.

echo === CONTAINER RESOURCE USAGE ===
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}" 2>nul | findstr selenium
echo.

echo === SELENIUM GRID STATUS ===
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:4444/status' -UseBasicParsing; $response.Content } catch { 'Grid not responding' }" 2>nul | findstr "ready\|value"
echo.

echo === ACTIVE BROWSER SESSIONS ===
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:4444/grid/api/sessions' -UseBasicParsing; $sessionCount = ([regex]::Matches($response.Content, 'sessionId')).Count; Write-Output \"Active Sessions: $sessionCount\" } catch { Write-Output 'Cannot check sessions' }" 2>nul
echo.

echo === NETWORK CONNECTIONS TO GRID ===
for /f %%i in ('netstat -an ^| findstr ":4444" ^| findstr "ESTABLISHED" ^| find /c "ESTABLISHED" 2^>nul') do set CONNECTION_COUNT=%%i
echo Established connections to port 4444: !CONNECTION_COUNT!
echo.

echo === SYSTEM MEMORY STATUS ===
for /f "tokens=2 delims==" %%i in ('wmic OS get FreePhysicalMemory /format:list ^| findstr "="') do set FREE_MEM=%%i
for /f "tokens=2 delims==" %%i in ('wmic OS get TotalVisibleMemorySize /format:list ^| findstr "="') do set TOTAL_MEM=%%i
if defined FREE_MEM if defined TOTAL_MEM (
    set /a USED_MEM=!TOTAL_MEM!-!FREE_MEM!
    set /a MEM_PERCENT=!USED_MEM!*100/!TOTAL_MEM!
    echo Memory Usage: !MEM_PERCENT!%% ^(!USED_MEM!/!TOTAL_MEM! KB^)
) else (
    echo Memory Usage: Unable to determine
)
echo.

echo === RECENT CONTAINER LOGS ===
echo --- Hub Logs (Last 2 lines) ---
docker logs selenium-hub --tail 2 2>nul
echo --- Node Logs (Last 2 lines) ---
docker logs node-edge-1 --tail 2 2>nul
echo.

echo === ALERTS AND WARNINGS ===
set ALERT_COUNT=0

:: Check for high memory usage
if defined MEM_PERCENT (
    if !MEM_PERCENT! GTR 80 (
        echo [ALERT] High memory usage: !MEM_PERCENT!%%
        set /a ALERT_COUNT+=1
    )
)

:: Check for too many connections
if defined CONNECTION_COUNT (
    if !CONNECTION_COUNT! GTR 6 (
        echo [ALERT] High connection count: !CONNECTION_COUNT!
        set /a ALERT_COUNT+=1
    )
)

:: Check for container errors
docker logs selenium-hub --tail 5 2>nul | findstr /i "error\|exception" >nul
if !ERRORLEVEL!==0 (
    echo [ALERT] Hub container has recent errors
    set /a ALERT_COUNT+=1
)

docker logs node-edge-1 --tail 5 2>nul | findstr /i "error\|exception\|crash" >nul
if !ERRORLEVEL!==0 (
    echo [ALERT] Node container has recent errors
    set /a ALERT_COUNT+=1
)

if !ALERT_COUNT!==0 (
    echo No alerts - System appears healthy
)

echo.
echo ========================================
echo Press Ctrl+C to stop monitoring
echo Refreshing in 5 seconds...
echo ========================================

:WAIT_AND_LOOP
timeout /t 5 /nobreak >nul
goto MAIN_LOOP
