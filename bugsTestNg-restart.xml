<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd" >

<suite name="Safe Watch Filtering"  verbose="5" preserve-order="true" >
    <parameter name="browserType" value="Chrome" />
    <parameter name="Application" value="SWF" />    <test name="SWF Regression Test - Bugs Suite">
        <parameter name="loginType" value="filtering" />
        <classes >

            <class name="eastnets.screening.regression.bugs.restartServicesRequired.Ticket92216Tests" />
            <class name="eastnets.screening.regression.bugs.restartServicesRequired.Tk92703Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesRequired.Tk93629Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesRequired.Tk100758Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesRequired.Tkt89427Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesRequired.Tkt89438Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesRequired.Tkt90347Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesRequired.Tkt90359Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesRequired.Tkt91542Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesRequired.Tkt91551Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesRequired.Tkt92727Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesRequired.Tkt92810Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesRequired.Tkt97255Test" />
           <class name="eastnets.screening.regression.bugs.restartServicesRequired.Tkt_70179_Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesRequired.Tkt_74566_Test" />
              <class name="eastnets.screening.regression.bugs.restartServicesRequired.Tkt_80310_Test" />
             <class name="eastnets.screening.regression.bugs.restartServicesRequired.Tkt_90550_Test" />
             <class name="eastnets.screening.regression.bugs.restartServicesRequired.Tkt_90148_Test" />
           <class name="eastnets.screening.regression.bugs.restartServicesRequired.TKt_85312_Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesRequired.Tkt_89277_Test" />
              <class name="eastnets.screening.regression.bugs.restartServicesRequired.Tkt_91682_Test" />


        </classes>
    </test>

</suite>