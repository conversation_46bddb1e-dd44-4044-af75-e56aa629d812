{"uuid": "31d1393d-57ed-4233-a2c6-cc29af54a739", "historyId": "276bb51d9fe0a45781c719f8d9643b0a", "fullName": "eastnets.screening.regression.reports.Report_TC008.report_TC008", "labels": [{"name": "package", "value": "eastnets.screening.regression.reports.Report_TC008"}, {"name": "testClass", "value": "eastnets.screening.regression.reports.Report_TC008"}, {"name": "testMethod", "value": "report_TC008"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Report Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.reports.Report_TC008"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "tag", "value": "Reports"}], "links": [], "name": "Verify Compare sessions info Report", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify Compare sessions info Report", "steps": [{"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-99870659', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@6b22219d, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-99870659', officialDate='null', entry=[ListEntry{type='null', name='EntryName-99870659', firstName='EntryFirstName-99870659', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-99870659', firstName='EntryFirstName-99870659', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109326334, "stop": 1754109326334}, {"name": "Connect to Database and Check if User Profile = full-right-profile_08 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109326335, "stop": 1754109326335}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109326336, "stop": 1754109326336}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109326392, "stop": 1754109326392}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_08' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109326392, "stop": 1754109326392}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109326395, "stop": 1754109326395}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109326395, "stop": 1754109326395}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109326396, "stop": 1754109326396}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109326420, "stop": 1754109326420}, {"name": "Delete From tListSetProfile where profile_id in (11)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109326420, "stop": 1754109326420}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109326424, "stop": 1754109326424}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109326424, "stop": 1754109326424}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109326424, "stop": 1754109326424}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109326424, "stop": 1754109326424}, {"name": "Search for list by listName = ListName-99870659 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109346697, "stop": 1754109346697}, {"name": "Set zone : Common Zone 08", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109359613, "stop": 1754109359613}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109363069, "stop": 1754109363069}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109363622, "stop": 1754109363622}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109364091, "stop": 1754109364091}, {"name": "Set template name = templateName-99870659", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109364347, "stop": 1754109364347}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109366991, "stop": 1754109366991}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109367785, "stop": 1754109367785}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109369286, "stop": 1754109369286}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109369994, "stop": 1754109369994}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109410402, "stop": 1754109410402}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109410402, "stop": 1754109410402}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109470560, "stop": 1754109470560}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109470560, "stop": 1754109470560}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109470561, "stop": 1754109470561}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109470610, "stop": 1754109470610}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_08'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109470611, "stop": 1754109470611}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109470613, "stop": 1754109470614}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109470614, "stop": 1754109470614}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109470614, "stop": 1754109470614}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109489498, "stop": 1754109489498}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109510126, "stop": 1754109510126}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109515568, "stop": 1754109515568}, {"name": "Validation message = File sent to the server for processing with id [2806]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109526052, "stop": 1754109526052}, {"name": "Alert Message = File sent to the server for processing with id [2806]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109526052, "stop": 1754109526052}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109527412, "stop": 1754109527412}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109536180, "stop": 1754109536180}, {"name": "Detection ID = 6929", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109537452, "stop": 1754109537452}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109537751, "stop": 1754109537751}, {"name": "Validation message = File sent to the server for processing with id [2808]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109547409, "stop": 1754109547409}, {"name": "Alert Message = File sent to the server for processing with id [2808]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109547409, "stop": 1754109547409}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109547978, "stop": 1754109547978}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109557421, "stop": 1754109557421}, {"name": "Detection ID = 6930", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109558700, "stop": 1754109558700}, {"name": "Report status is: Done", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754109583970, "stop": 1754109583970}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754109326329, "stop": 1754109628120}