{"uuid": "e3faeac4-6140-4a52-bdcf-41dfb1570370", "name": "ISO20022 Configurations Test Cases", "children": ["f0c4e815-24f9-48c7-a5be-43cd43a3142f", "b6999659-e505-4920-94a4-98c8f2fc9be0", "b3666360-03d1-464f-90f9-c28e1da7f97c", "9b16f5bb-3cfc-4696-a999-45576a38ede4", "0ce90a97-284b-4d4f-b139-cc47ce0ed493"], "befores": [{"name": "Setting up selenium web driver before each class run", "status": "passed", "stage": "finished", "description": "Setting up selenium web driver before each class run", "steps": [{"name": "Initialize Selenium driver before tests' Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106381172, "stop": 1754106381172}, {"name": "Selenium webDriver was initialized successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106385351, "stop": 1754106385351}], "attachments": [], "parameters": [], "start": 1754106381172, "stop": 1754106385351}], "afters": [{"name": "Quitting selenium driver after each class run", "status": "passed", "stage": "finished", "description": "Quitting selenium driver after each class run", "steps": [{"name": "Closing selenium WebDriver after Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107123525, "stop": 1754107123525}], "attachments": [], "parameters": [], "start": 1754107123525, "stop": 1754107124184}], "start": 1754106381171, "stop": 1754107124184}