{"uuid": "05c762b6-557b-4f84-a275-4641c665e1b2", "historyId": "b86d509e9ce355a0fe501fe20e66a1a6", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC007.scanManager_TC007", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC007"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC007"}, {"name": "testMethod", "value": "scanManager_TC007"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC007"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is able to add a Good Guy for a detection by clicking accept as shared button for shared black list", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to add a Good Guy for a detection by clicking accept as shared button for shared black list", "steps": [{"name": "Validation Message = null", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105863949, "stop": 1754105863949}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105863949, "stop": 1754105863949}, {"name": "Status for the scanned name = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105874964, "stop": 1754105874964}, {"name": "<PERSON>ose scanned name from result table يا<PERSON><PERSON> عمر", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105874964, "stop": 1754105874964}, {"name": "Actual validation Message = Good guy successfully created!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105880682, "stop": 1754105880682}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754105857880, "stop": 1754105881160}