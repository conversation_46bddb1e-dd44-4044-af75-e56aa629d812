{"uuid": "a5a047e0-ee30-4f33-adc8-630099be0e88", "historyId": "53af5d7e30c68caa1bc374572fd0445f", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC008.scanManager_TC008", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC008"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC008"}, {"name": "testMethod", "value": "scanManager_TC008"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC008"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is able to add an entity as <PERSON> Guy from the results tab for a scanned file with Custom Format type.", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to add an entity as <PERSON> Guy from the results tab for a scanned file with Custom Format type.", "steps": [{"name": "RJE File Content= EntryName1, EntryName1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105926332, "stop": 1754105926332}, {"name": "Start coping data from sample file to another file to be used.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105926332, "stop": 1754105926332}, {"name": "Successfully wrote to the file.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105926341, "stop": 1754105926341}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105961180, "stop": 1754105961180}, {"name": "Validation message = File sent to the server for processing with id [2769]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105973549, "stop": 1754105973549}, {"name": "Alert Message = File sent to the server for processing with id [2769]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105973549, "stop": 1754105973549}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105974785, "stop": 1754105974785}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105984385, "stop": 1754105984385}, {"name": "Detection ID = 6865", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105985919, "stop": 1754105985919}, {"name": "Actual validation Message = Good guy successfully created!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105989060, "stop": 1754105989060}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "FileScan{filePath='/src/test/resources/testDataFiles/fileScanTD/ScanFile.txt', format='Custom Format File', encoding='null', enList=null, result='null', rank='null', detectVessels='true', detectCountries='true', createAlertsAutomatically='false'}"}], "start": 1754105926106, "stop": 1754105989565}