{"uuid": "60d7b361-ab64-42c2-bf8e-b7a032c29b95", "name": "Admin Test Cases", "children": ["5a4a8842-5bb2-4a56-adb8-40132775c333", "58e74c45-6e41-47bd-b8f0-30258f347dcc", "8a1eeda9-e739-4a52-b6e4-57dbb298fdd2", "9759c880-c075-4842-afe9-fe7f623f53ec", "845a08ed-b12a-4ad6-a48a-0a396e4f9051", "d7096402-3bcf-4814-9ef4-427dd8e6498d", "75982e37-eef2-4d05-b80e-5c441e3052d7", "805ac510-a92e-468b-92fb-8bf6a1487000", "3ac34561-f97c-4ffc-b63f-37bf859cc817", "70027311-f1f7-42d2-857c-0d939dd<PERSON>cac", "c2169a51-4bc4-4da8-872e-3d7f7fa13942", "92844ae4-0429-4027-8e09-e5dcb4165bff", "c99e2492-ec4b-4f3d-a119-fd29cb34b87d", "569cade4-1f29-4b6b-816b-7849b98bb465", "2c462d01-4f69-44ee-98c5-0999dc39fb6b", "708a6d0c-78ff-4983-aff3-720c9157f4e7", "78eab8e0-ae24-45ff-9bbc-7da13a1dd0de"], "befores": [{"name": "Setting up selenium web driver before each class run", "status": "passed", "stage": "finished", "description": "Setting up selenium web driver before each class run", "steps": [{"name": "Initialize Selenium driver before tests' Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112602022, "stop": 1754112602022}, {"name": "Selenium webDriver was initialized successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112606074, "stop": 1754112606074}], "attachments": [], "parameters": [], "start": 1754112602022, "stop": 1754112606074}], "afters": [{"name": "Quitting selenium driver after each class run", "status": "passed", "stage": "finished", "description": "Quitting selenium driver after each class run", "steps": [{"name": "Closing selenium WebDriver after Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390315, "stop": 1754115390315}], "attachments": [], "parameters": [], "start": 1754115390315, "stop": 1754115390806}], "start": 1754112602021, "stop": 1754115390806}