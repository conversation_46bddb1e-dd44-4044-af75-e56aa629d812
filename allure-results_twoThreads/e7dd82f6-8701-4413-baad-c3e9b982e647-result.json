{"uuid": "e7dd82f6-8701-4413-baad-c3e9b982e647", "historyId": "4c82ae056e18d47b87509aa34f88cbc0", "fullName": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC019.listManager_TC019", "labels": [{"name": "package", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC019"}, {"name": "testClass", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC019"}, {"name": "testMethod", "value": "listManager_TC019"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "List Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC019"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-1(36)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "List Manager"}], "links": [], "name": "Verify that Good Guys migration for a Private List is functioning properly", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107189007, "stop": 1754107189007}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-02'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107189007, "stop": 1754107189007}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107189013, "stop": 1754107189013}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107189014, "stop": 1754107189014}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107189014, "stop": 1754107189014}, {"name": "Login with User Name = operator-02 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107189209, "stop": 1754107189209}, {"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-344025438', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@1b3b1e07, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-344025438', officialDate='null', entry=[ListEntry{type='null', name='EntryName-344025438', firstName='EntryFirstName-344025438', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-344025438', firstName='EntryFirstName-344025438', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107193496, "stop": 1754107193496}, {"name": "Create new black list", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107193496, "stop": 1754107193496}, {"name": "Connect to Database and Check if User Profile = full-right-profile_002 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107193496, "stop": 1754107193496}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107193496, "stop": 1754107193496}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107193549, "stop": 1754107193549}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_002' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107193550, "stop": 1754107193550}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107193552, "stop": 1754107193552}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107193552, "stop": 1754107193552}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107193553, "stop": 1754107193553}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107193585, "stop": 1754107193585}, {"name": "Delete From tListSetProfile where profile_id in (5)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107193585, "stop": 1754107193585}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107193589, "stop": 1754107193589}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107193589, "stop": 1754107193589}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107193590, "stop": 1754107193590}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107193590, "stop": 1754107193590}, {"name": "Search for list by listName = ListName-344025438 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107204153, "stop": 1754107204153}, {"name": "Set zone : Common Zone 02", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107213729, "stop": 1754107213729}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107215939, "stop": 1754107215939}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107216458, "stop": 1754107216458}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107216874, "stop": 1754107216874}, {"name": "Set template name = templateName-344025438", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107217119, "stop": 1754107217119}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107219501, "stop": 1754107219501}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107220220, "stop": 1754107220220}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107221472, "stop": 1754107221472}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107222171, "stop": 1754107222171}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107251715, "stop": 1754107251715}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107251715, "stop": 1754107251715}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107315151, "stop": 1754107315151}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107315152, "stop": 1754107315152}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107315152, "stop": 1754107315152}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107315205, "stop": 1754107315205}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_002'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107315207, "stop": 1754107315207}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107315210, "stop": 1754107315210}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107315210, "stop": 1754107315210}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107315210, "stop": 1754107315210}, {"name": "Successfully wrote to the file.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107346531, "stop": 1754107346531}, {"name": "Select version : v1 - 2025/02/02", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107374640, "stop": 1754107374640}, {"name": "Click on migrate checkbox", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107381676, "stop": 1754107381676}, {"name": "Click on Extra AKAs checkbox", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107382258, "stop": 1754107382258}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107385270, "stop": 1754107385270}, {"name": "Select list set by name.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107415281, "stop": 1754107415281}, {"name": "Validation message = Good guy successfully created!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107424048, "stop": 1754107424048}, {"name": "Alert message = Good guy successfully created!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107424048, "stop": 1754107424048}, {"name": "Select version : v1 - 2025/04/02", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107475214, "stop": 1754107475214}, {"name": "Click on migrate checkbox", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107483659, "stop": 1754107483659}, {"name": "Click on Extra AKAs checkbox", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107484693, "stop": 1754107484693}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107487620, "stop": 1754107487620}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754107188577, "stop": 1754107518118}