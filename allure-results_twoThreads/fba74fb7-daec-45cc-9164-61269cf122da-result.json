{"uuid": "fba74fb7-daec-45cc-9164-61269cf122da", "historyId": "597ed8afc9675e588c6db9010bd36f72", "fullName": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC016.listManager_TC016", "labels": [{"name": "package", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC016"}, {"name": "testClass", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC016"}, {"name": "testMethod", "value": "listManager_TC016"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "List Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC016"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-1(36)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "List Manager"}], "links": [], "name": "Verify that user is able to search for a List from different zone", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to search for a List from different zone", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106898599, "stop": 1754106898599}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='4eyes01'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106898599, "stop": 1754106898599}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106898605, "stop": 1754106898605}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106898605, "stop": 1754106898605}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106898606, "stop": 1754106898606}, {"name": "Login with User Name = 4eyes01 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106898741, "stop": 1754106898741}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903385, "stop": 1754106903385}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903419, "stop": 1754106903419}, {"name": "select id from tZones tz where tz.DISPLAY_NAME  = 'Four Eyes'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903419, "stop": 1754106903419}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903422, "stop": 1754106903422}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903423, "stop": 1754106903423}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903423, "stop": 1754106903423}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903446, "stop": 1754106903446}, {"name": "DELETE  FROM tSetContent   WHERE id IN ( SELECT tsc.id FROM tSetContent tsc  LEFT JOIN tBlackListVersions tblv on tblv.id =tsc.black_list_id  LEFT JOIN tListSets tls on tls.id = tsc.list_set_id  LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id  WHERE tbl.zone_id ='15'  AND tbl.display_name ='UN Democratic Republic of Congo')", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903446, "stop": 1754106903446}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903458, "stop": 1754106903458}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903459, "stop": 1754106903459}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903459, "stop": 1754106903459}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903459, "stop": 1754106903459}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903483, "stop": 1754106903483}, {"name": "DELETE  FROM tGoodGuys  WHERE id IN ( SELECT tgg.id  FROM tGoodGuys tgg LEFT JOIN tBlackListVersions tblv on tblv.id =tgg.black_list_id LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id WHERE tbl.display_name ='UN Democratic Republic of Congo')", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903483, "stop": 1754106903483}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903498, "stop": 1754106903498}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903498, "stop": 1754106903498}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903498, "stop": 1754106903498}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903499, "stop": 1754106903499}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903521, "stop": 1754106903521}, {"name": "DELETE FROM tBlackLists WHERE display_name ='UN Democratic Republic of Congo'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903521, "stop": 1754106903521}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903694, "stop": 1754106903694}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903694, "stop": 1754106903694}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903695, "stop": 1754106903695}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903695, "stop": 1754106903695}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903724, "stop": 1754106903724}, {"name": "select id from tZones tz where tz.DISPLAY_NAME  = 'Four Eyes'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903724, "stop": 1754106903724}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903726, "stop": 1754106903726}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903726, "stop": 1754106903726}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903727, "stop": 1754106903727}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903750, "stop": 1754106903750}, {"name": "DELETE  FROM tSetContent   WHERE id IN ( SELECT tsc.id FROM tSetContent tsc  LEFT JOIN tBlackListVersions tblv on tblv.id =tsc.black_list_id  LEFT JOIN tListSets tls on tls.id = tsc.list_set_id  LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id  WHERE tbl.zone_id ='15'  AND tbl.display_name ='UN Yemen')", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903750, "stop": 1754106903750}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903763, "stop": 1754106903763}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903763, "stop": 1754106903763}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903763, "stop": 1754106903763}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903763, "stop": 1754106903763}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903788, "stop": 1754106903788}, {"name": "DELETE  FROM tGoodGuys  WHERE id IN ( SELECT tgg.id  FROM tGoodGuys tgg LEFT JOIN tBlackListVersions tblv on tblv.id =tgg.black_list_id LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id WHERE tbl.display_name ='UN Yemen')", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903788, "stop": 1754106903788}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903803, "stop": 1754106903803}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903803, "stop": 1754106903803}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903804, "stop": 1754106903804}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903804, "stop": 1754106903804}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903827, "stop": 1754106903827}, {"name": "DELETE FROM tBlackLists WHERE display_name ='UN Yemen'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903827, "stop": 1754106903827}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903933, "stop": 1754106903933}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903933, "stop": 1754106903933}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903934, "stop": 1754106903934}, {"name": "Import 'UN Democratic Republic of Congo' Black List on user linked on 'Four Eyes' zone", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106903934, "stop": 1754106903934}, {"name": "Share 'UN Democratic Republic of Congo' black list", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106922138, "stop": 1754106922138}, {"name": "Validation Message = null", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106927751, "stop": 1754106927751}, {"name": "Import 'UN Yemen' Black List on user linked on 'Four Eyes' zone", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106927751, "stop": 1754106927751}, {"name": "Log out and login with another user linked to 'Four Eyes' zone", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106941404, "stop": 1754106941404}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106943668, "stop": 1754106943668}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='4eyes02'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106943668, "stop": 1754106943668}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106943675, "stop": 1754106943675}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106943675, "stop": 1754106943675}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106943675, "stop": 1754106943675}, {"name": "Login with User Name = 4eyes02 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106943810, "stop": 1754106943810}, {"name": "Verify that user linked with 'Four Eyes' zone have access to 'UN Democratic Republic of Congo' shared black list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106948646, "stop": 1754106948646}, {"name": "Verify that user linked with 'Four Eyes' zone have access to 'UN Yemen' non-shared black list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106957459, "stop": 1754106957459}, {"name": "Log out and login with another user linked to 'Default Zone' zone", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106964684, "stop": 1754106964684}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106967352, "stop": 1754106967352}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='def-operator-1'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106967352, "stop": 1754106967352}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106967359, "stop": 1754106967359}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106967359, "stop": 1754106967359}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106967359, "stop": 1754106967360}, {"name": "Login with User Name = def-operator-1 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106967541, "stop": 1754106967541}, {"name": "Verify that user linked with 'Default Zone' zone have access to 'UN Democratic Republic of Congo' shared black list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106973187, "stop": 1754106973188}, {"name": "Verify that user linked with 'Default Zone' zone not have access to 'UN Yemen' non-shared black list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106981751, "stop": 1754106981751}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754106898250, "stop": 1754106992429}