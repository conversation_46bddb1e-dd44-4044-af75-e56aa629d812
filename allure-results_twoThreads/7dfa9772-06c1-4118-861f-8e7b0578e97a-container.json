{"uuid": "7dfa9772-06c1-4118-861f-8e7b0578e97a", "name": "Report Manager Test Cases", "children": ["746d2c2f-0226-4435-bca0-0cb697908b09", "935c2492-c443-481d-810f-c4a5a512fe66", "12cfabc8-f613-40a3-a140-ea28735e51cb", "ed88b1d5-c12a-430e-869f-10fc87a8f6db", "cc3be6fe-a322-4ac6-a1d3-19fbd94a4d09", "1b2c292e-868f-4737-b930-4ecb3868103d", "7d9f1f1d-4206-4acc-8a13-c32177499954", "31d1393d-57ed-4233-a2c6-cc29af54a739", "4a0a3a4f-d224-425b-ae20-ac3c41741c78", "eea953d2-c631-4da4-9981-90ccd445dd27", "416cbcba-b4ed-419f-918a-3abe1f0b2944", "46cb2dcd-3fe7-4b04-b835-d9bb19b8b5b8", "e1c1985b-6a28-4bcc-8a45-2f7d057dbf60", "c15da96e-e4d5-4673-92ab-9c4bf52b7e6a", "dbfb9e91-b003-4726-bd2b-2f1bad249510", "3e39e727-1ead-4b9d-ac65-1be35bde2710", "06dcfdf7-4cc0-48e5-9935-955211f82472", "a9576e03-2021-425f-9842-cb6f4ab77b6b", "ec7db16f-5588-4d09-b0ac-cbea44b57040"], "befores": [{"name": "Setting up selenium web driver before each class run", "status": "passed", "stage": "finished", "description": "Setting up selenium web driver before each class run", "steps": [{"name": "Initialize Selenium driver before tests' Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107673835, "stop": 1754107673835}, {"name": "Selenium webDriver was initialized successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107677088, "stop": 1754107677088}], "attachments": [], "parameters": [], "start": 1754107673835, "stop": 1754107677089}], "afters": [{"name": "Quitting selenium driver after each class run", "status": "passed", "stage": "finished", "description": "Quitting selenium driver after each class run", "steps": [{"name": "Closing selenium WebDriver after Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112601052, "stop": 1754112601052}], "attachments": [], "parameters": [], "start": 1754112601052, "stop": 1754112601921}], "start": 1754107673832, "stop": 1754112601921}