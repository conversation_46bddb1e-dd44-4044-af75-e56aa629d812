{"uuid": "2fddcc26-cbf2-45c4-84a7-a9ca763cbe33", "name": "eastnets.admin.AdminTest", "children": ["8a1eeda9-e739-4a52-b6e4-57dbb298fdd2", "9759c880-c075-4842-afe9-fe7f623f53ec", "845a08ed-b12a-4ad6-a48a-0a396e4f9051", "d7096402-3bcf-4814-9ef4-427dd8e6498d", "75982e37-eef2-4d05-b80e-5c441e3052d7", "805ac510-a92e-468b-92fb-8bf6a1487000", "3ac34561-f97c-4ffc-b63f-37bf859cc817", "70027311-f1f7-42d2-857c-0d939dd<PERSON>cac", "c2169a51-4bc4-4da8-872e-3d7f7fa13942", "92844ae4-0429-4027-8e09-e5dcb4165bff", "c99e2492-ec4b-4f3d-a119-fd29cb34b87d", "569cade4-1f29-4b6b-816b-7849b98bb465", "2c462d01-4f69-44ee-98c5-0999dc39fb6b", "708a6d0c-78ff-4983-aff3-720c9157f4e7", "78eab8e0-ae24-45ff-9bbc-7da13a1dd0de"], "befores": [{"name": "createNewOperator", "status": "passed", "stage": "finished", "description": "", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112799026, "stop": 1754112799026}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='sysadmin'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112799027, "stop": 1754112799027}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112799033, "stop": 1754112799033}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112799033, "stop": 1754112799033}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112799034, "stop": 1754112799034}, {"name": "Login with User Name = sysadmin and Password = manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112799328, "stop": 1754112799328}, {"name": "Operator test data = Operator{id=0, loginName='selenium-user-211907537', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone431995152', displayName='Zone (431995152)', description='Zone created with random number '431995152''}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112805256, "stop": 1754112805256}, {"name": "Group test data = Group{id=0, name='selenium-random-group-947819246', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-675595205', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone431995152', displayName='Zone (431995152)', description='Zone created with random number '431995152''}, profileRights={Administration={Archive Viewer=[], Operator Manager=[ALL], Group Manager=[ALL], Profile Manager=[ALL], Report Manager=[ALL], Zone Manager=[ALL], Audit Manager=[], Event Viewer=[]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-211907537', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone431995152', displayName='Zone (431995152)', description='Zone created with random number '431995152''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112805256, "stop": 1754112805256}, {"name": "Create new zone .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112805256, "stop": 1754112805256}, {"name": "Zone test Data = Zone{id=0, name='Zone431995152', displayName='Zone (431995152)', description='Zone created with random number '431995152''}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112805256, "stop": 1754112805256}, {"name": "Check if zone with name = 'Zone431995152' exists", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112807260, "stop": 1754112807260}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112807489, "stop": 1754112807489}, {"name": "Enter name =Zone431995152", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112812901, "stop": 1754112812901}, {"name": "Enter display Name =Zone (431995152)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112813668, "stop": 1754112813668}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112814652, "stop": 1754112814652}, {"name": "Zone doesn't exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112815194, "stop": 1754112815194}, {"name": "Click add new zone button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112815194, "stop": 1754112815194}, {"name": "Set create new zone data.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112815418, "stop": 1754112815418}, {"name": "Set name = Zone431995152", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112815418, "stop": 1754112815418}, {"name": "Set display name = Zone (431995152)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112816731, "stop": 1754112816731}, {"name": "Set description = Zone created with random number '431995152'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112817405, "stop": 1754112817405}, {"name": "Capture zone id from UI = 127", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112818615, "stop": 1754112818615}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112818615, "stop": 1754112818615}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112819965, "stop": 1754112819965}, {"name": "Enter name =Zone431995152", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112825329, "stop": 1754112825329}, {"name": "Enter display Name =Zone (431995152)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112826207, "stop": 1754112826207}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112827124, "stop": 1754112827124}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112829681, "stop": 1754112829681}, {"name": "Enter name =Zone431995152", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112835107, "stop": 1754112835107}, {"name": "Enter display Name =Zone (431995152)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112836157, "stop": 1754112836157}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112836910, "stop": 1754112836910}, {"name": "Create new profile .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112837719, "stop": 1754112837719}, {"name": "Profile test Data = Profile{id=0, name='Test-Profile-675595205', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone431995152', displayName='Zone (431995152)', description='Zone created with random number '431995152''}, profileRights={Administration={Archive Viewer=[], Operator Manager=[ALL], Group Manager=[ALL], Profile Manager=[ALL], Report Manager=[ALL], Zone Manager=[ALL], Audit Manager=[], Event Viewer=[]}}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112837719, "stop": 1754112837719}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112839310, "stop": 1754112839310}, {"name": "Set name = Test-Profile-675595205 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112844712, "stop": 1754112844712}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112845503, "stop": 1754112845503}, {"name": "Click add new profile button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112846277, "stop": 1754112846277}, {"name": "Set name = Test-Profile-675595205 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112846525, "stop": 1754112846525}, {"name": "Set description = Random profile for Selenium testing ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112847884, "stop": 1754112847884}, {"name": "Check enable checkbox to be true ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112848657, "stop": 1754112848657}, {"name": "Check write right checkbox to be Test-Profile-675595205 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112849326, "stop": 1754112849327}, {"name": "Select zone = Test-Profile-675595205 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112849598, "stop": 1754112849598}, {"name": "Add Administration item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112857971, "stop": 1754112857971}, {"name": "Click on the Administration item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112857971, "stop": 1754112857971}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112858581, "stop": 1754112858581}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112859603, "stop": 1754112859603}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112860078, "stop": 1754112860078}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112860127, "stop": 1754112860127}, {"name": "Set name = Test-Profile-675595205 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112866064, "stop": 1754112866064}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112866660, "stop": 1754112866660}, {"name": "Select profile from result view.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112867402, "stop": 1754112867402}, {"name": "Click on Administration", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112868024, "stop": 1754112868024}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112869312, "stop": 1754112869312}, {"name": "Add Archive Viewer item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112870253, "stop": 1754112870253}, {"name": "Click on the Archive Viewer item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112870253, "stop": 1754112870253}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112870699, "stop": 1754112870699}, {"name": "Add Operator Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112872200, "stop": 1754112872200}, {"name": "Click on the Operator Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112872200, "stop": 1754112872200}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112872693, "stop": 1754112872693}, {"name": "Add Group Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112874193, "stop": 1754112874193}, {"name": "Click on the Group Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112874193, "stop": 1754112874193}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112874806, "stop": 1754112874806}, {"name": "Add Profile Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112876281, "stop": 1754112876281}, {"name": "Click on the Profile Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112876281, "stop": 1754112876281}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112876753, "stop": 1754112876753}, {"name": "Add Report Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112878238, "stop": 1754112878238}, {"name": "Click on the Report Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112878238, "stop": 1754112878238}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112878758, "stop": 1754112878758}, {"name": "Add Zone Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112880245, "stop": 1754112880245}, {"name": "Click on the Zone Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112880245, "stop": 1754112880245}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112880590, "stop": 1754112880590}, {"name": "Add Audit Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112882040, "stop": 1754112882040}, {"name": "Click on the Audit Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112882040, "stop": 1754112882040}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112882390, "stop": 1754112882390}, {"name": "Add Event Viewer item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112883763, "stop": 1754112883763}, {"name": "Click on the Event Viewer item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112883763, "stop": 1754112883763}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112884136, "stop": 1754112884136}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112885559, "stop": 1754112885559}, {"name": "Accept <PERSON><PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112885931, "stop": 1754112885931}, {"name": "Click on Administration", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112886929, "stop": 1754112886929}, {"name": "Select Module Rights.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112887681, "stop": 1754112887681}, {"name": "Processing module 'Operator Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112889683, "stop": 1754112889683}, {"name": "Click on Operator Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112889683, "stop": 1754112889683}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112890590, "stop": 1754112890590}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112890814, "stop": 1754112890814}, {"name": "Processing module 'Group Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112893132, "stop": 1754112893132}, {"name": "<PERSON>lick on Group Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112893132, "stop": 1754112893132}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112893975, "stop": 1754112893975}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112894196, "stop": 1754112894196}, {"name": "Processing module 'Profile Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112896448, "stop": 1754112896448}, {"name": "Click on Profile Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112896448, "stop": 1754112896448}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112897411, "stop": 1754112897411}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112897688, "stop": 1754112897688}, {"name": "Processing module 'Report Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112899959, "stop": 1754112899959}, {"name": "Click on Report Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112899959, "stop": 1754112899959}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112900856, "stop": 1754112900856}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112901100, "stop": 1754112901100}, {"name": "Processing module 'Zone Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112903545, "stop": 1754112903545}, {"name": "Click on Zone Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112903545, "stop": 1754112903545}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112904520, "stop": 1754112904520}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112904884, "stop": 1754112904884}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112907989, "stop": 1754112907989}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112908228, "stop": 1754112908228}, {"name": "Check if profile exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112909143, "stop": 1754112909143}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112909143, "stop": 1754112909143}, {"name": "Set name = Test-Profile-675595205 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112914966, "stop": 1754112914966}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112915771, "stop": 1754112915771}, {"name": "Profile exist = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112916523, "stop": 1754112916523}, {"name": "Create new operator.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112916523, "stop": 1754112916523}, {"name": "Operator test Data = Operator{id=0, loginName='selenium-user-211907537', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone431995152', displayName='Zone (431995152)', description='Zone created with random number '431995152''}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112916523, "stop": 1754112916523}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112918717, "stop": 1754112918717}, {"name": "Set login name = selenium-user-211907537", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112924179, "stop": 1754112924179}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112925038, "stop": 1754112925038}, {"name": "Click Create new operator button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112925631, "stop": 1754112925631}, {"name": "Set login name = selenium-user-211907537", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112925911, "stop": 1754112925911}, {"name": "Set first name = Random", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112927172, "stop": 1754112927172}, {"name": "Set last name = User", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112927808, "stop": 1754112927808}, {"name": "Set password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112928466, "stop": 1754112928466}, {"name": "Set confirm password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112929084, "stop": 1754112929084}, {"name": "Set enable flag = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112929709, "stop": 1754112929709}, {"name": "Set reset flag = false", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112930311, "stop": 1754112930311}, {"name": "Select zone  = Zone (431995152)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112931664, "stop": 1754112931664}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112937253, "stop": 1754112937253}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112937570, "stop": 1754112937570}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112944187, "stop": 1754112944187}, {"name": "Set login name = selenium-user-211907537", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112949503, "stop": 1754112949503}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112950475, "stop": 1754112950475}, {"name": "Create new group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112951188, "stop": 1754112951188}, {"name": "Group test Data = Group{id=0, name='selenium-random-group-947819246', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-675595205', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone431995152', displayName='Zone (431995152)', description='Zone created with random number '431995152''}, profileRights={Administration={Archive Viewer=[], Operator Manager=[ALL], Group Manager=[ALL], Profile Manager=[ALL], Report Manager=[ALL], Zone Manager=[ALL], Audit Manager=[], Event Viewer=[]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-211907537', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone431995152', displayName='Zone (431995152)', description='Zone created with random number '431995152''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112951188, "stop": 1754112951188}], "attachments": [], "parameters": [], "start": 1754112798480, "stop": 1754112988299}], "afters": [], "start": 1754112602021, "stop": 1754115390812}