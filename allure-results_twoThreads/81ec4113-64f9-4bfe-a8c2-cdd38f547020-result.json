{"uuid": "81ec4113-64f9-4bfe-a8c2-cdd38f547020", "historyId": "99779744c768e2c3e4baec9a7f47f7c", "fullName": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC018.ListManager_TC018", "labels": [{"name": "package", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC018"}, {"name": "testClass", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC018"}, {"name": "testMethod", "value": "ListManager_TC018"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "List Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC018"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-1(36)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "List Manager"}], "links": [], "name": "Verify that user is able to Add, modify, delete and a World Check flow as PEP type", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to Add a new World Check flow as PEP type\nVerify that user is able to Modify a World Check flow\nVerify that user is able to Delete a World Check flow\nVerify that user is able to search for a World Check flow by Zone", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107020561, "stop": 1754107020561}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-02'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107020561, "stop": 1754107020561}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107020569, "stop": 1754107020569}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107020569, "stop": 1754107020569}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107020569, "stop": 1754107020569}, {"name": "Login with User Name = operator-02 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107020711, "stop": 1754107020711}, {"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-484730350', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@73f739a2, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-484730350', officialDate='null', entry=[ListEntry{type='null', name='EntryName-484730350', firstName='EntryFirstName-484730350', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-484730350', firstName='EntryFirstName-484730350', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107025329, "stop": 1754107025329}, {"name": "Create new black list", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107025329, "stop": 1754107025329}, {"name": "Search for list by listName = ListName-484730350 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107053797, "stop": 1754107053797}, {"name": "Verify that user is able to Add a new World Check flow as PEP type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107058632, "stop": 1754107058632}, {"name": "Verify that user is able to Modify a World Check flow", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107086630, "stop": 1754107086630}, {"name": "Verify that user is able to Delete a World Check flow", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754107095310, "stop": 1754107095310}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "PEP"}], "start": 1754107020149, "stop": 1754107104267}