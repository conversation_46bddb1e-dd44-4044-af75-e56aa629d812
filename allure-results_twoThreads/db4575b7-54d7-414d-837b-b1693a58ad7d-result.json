{"uuid": "db4575b7-54d7-414d-837b-b1693a58ad7d", "historyId": "6da7954c335bba2c9b60e14680184611", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC001.scanManager_TC001", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC001"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC001"}, {"name": "testMethod", "value": "scanManager_TC001"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC001"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is able to scan a custom format file without creating alerts option checked and take decision. ", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to scan a custom format file without creating alerts option checked and take decision. ", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105549253, "stop": 1754105549253}, {"name": "Validation message = File sent to the server for processing with id [2761]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105562189, "stop": 1754105562189}, {"name": "Alert Message = File sent to the server for processing with id [2761]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105562189, "stop": 1754105562189}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105563586, "stop": 1754105563586}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105571967, "stop": 1754105571967}, {"name": "Detection ID = 6845", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105573119, "stop": 1754105573119}, {"name": "Detection Status = REP", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105577245, "stop": 1754105577245}, {"name": "Detection Status = REP", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105580894, "stop": 1754105580894}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "FileScan{filePath='/Generic.txt', format='Custom Format File', encoding='null', enList=null, result='null', rank='null', detectVessels='true', detectCountries='true', createAlertsAutomatically='false'}"}], "start": 1754105515265, "stop": 1754105587029}