{"uuid": "50501509-2ba2-41d1-9e86-e5a649169068", "historyId": "4d14b94ca8716274c64f29b7bbc281f5", "fullName": "eastnets.screening.regression.iso20022configurations.ISO20022_TC002.iso20022_TC0012", "labels": [{"name": "package", "value": "eastnets.screening.regression.iso20022configurations.ISO20022_TC002"}, {"name": "testClass", "value": "eastnets.screening.regression.iso20022configurations.ISO20022_TC002"}, {"name": "testMethod", "value": "iso20022_TC0012"}, {"name": "parentSuite", "value": "Failed Tests Rerun Suite"}, {"name": "suite", "value": "<PERSON><PERSON> Failed Tests"}, {"name": "subSuite", "value": "eastnets.screening.regression.iso20022configurations.ISO20022_TC002"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "tag", "value": "ISO20022"}, {"name": "tag", "value": "Regression"}, {"name": "owner", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "links": [{"name": "87504", "type": "issue"}], "name": "Tfs 87504: Scan ISO message without xml tag having body fields only while <PERSON><PERSON> is added to config and no Wrapper defined.", "status": "failed", "statusDetails": {"known": false, "muted": false, "flaky": false, "message": "Test Failed due to an exception occurred in POM's method while adding ISO message expected [New message [pacs.009.001] with version [pacs.009.001.08] added successfully.] but found [This message already exist!\"]", "trace": "java.lang.AssertionError: Test Failed due to an exception occurred in POM's method while adding ISO message expected [New message [pacs.009.001] with version [pacs.009.001.08] added successfully.] but found [This message already exist!\"]\r\n\tat org.testng.Assert.fail(Assert.java:110)\r\n\tat org.testng.Assert.failNotEquals(Assert.java:1413)\r\n\tat org.testng.Assert.assertEqualsImpl(Assert.java:149)\r\n\tat org.testng.Assert.assertEquals(Assert.java:131)\r\n\tat org.testng.Assert.assertEquals(Assert.java:655)\r\n\tat core.ISOTestMethods.addISOMessage(ISOTestMethods.java:83)\r\n\tat eastnets.screening.regression.iso20022configurations.ISO20022_TC002.iso20022_TC0012(ISO20022_TC002.java:133)\r\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:578)\r\n\tat org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:677)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:221)\r\n\tat org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:50)\r\n\tat org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:969)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:194)\r\n\tat org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148)\r\n\tat org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128)\r\n\tat java.base/java.util.ArrayList.forEach(ArrayList.java:1511)\r\n\tat org.testng.TestRunner.privateRun(TestRunner.java:829)\r\n\tat org.testng.TestRunner.run(TestRunner.java:602)\r\n\tat org.testng.SuiteRunner.runTest(SuiteRunner.java:437)\r\n\tat org.testng.SuiteRunner.runSequentially(SuiteRunner.java:431)\r\n\tat org.testng.SuiteRunner.privateRun(SuiteRunner.java:391)\r\n\tat org.testng.SuiteRunner.run(SuiteRunner.java:330)\r\n\tat org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)\r\n\tat org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)\r\n\tat org.testng.TestNG.runSuitesSequentially(TestNG.java:1256)\r\n\tat org.testng.TestNG.runSuitesLocally(TestNG.java:1176)\r\n\tat org.testng.TestNG.runSuites(TestNG.java:1099)\r\n\tat org.testng.TestNG.run(TestNG.java:1067)\r\n\tat core.BaseTest.rerunFailedTestCases(BaseTest.java:380)\r\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:578)\r\n\tat org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)\r\n\tat org.testng.internal.invokers.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:69)\r\n\tat org.testng.internal.invokers.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:361)\r\n\tat org.testng.internal.invokers.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:296)\r\n\tat org.testng.SuiteRunner.privateRun(SuiteRunner.java:404)\r\n\tat org.testng.SuiteRunner.run(SuiteRunner.java:330)\r\n\tat org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)\r\n\tat org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)\r\n\tat org.testng.TestNG.runSuitesSequentially(TestNG.java:1256)\r\n\tat org.testng.TestNG.runSuitesLocally(TestNG.java:1176)\r\n\tat org.testng.TestNG.runSuites(TestNG.java:1099)\r\n\tat org.testng.TestNG.run(TestNG.java:1067)\r\n\tat com.intellij.rt.testng.IDEARemoteTestNG.run(IDEARemoteTestNG.java:65)\r\n\tat com.intellij.rt.testng.RemoteTestNGStarter.main(RemoteTestNGStarter.java:105)\r\n"}, "stage": "finished", "description": "Tfs 87504: Scan ISO message without xml tag having body fields only while <PERSON><PERSON> is added to config and no Wrapper defined.", "steps": [{"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-130681419', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@2a72ac42, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-130681419', officialDate='null', entry=[ListEntry{type='null', name='EntryName-130681419', firstName='EntryFirstName-130681419', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-130681419', firstName='EntryFirstName-130681419', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116495810, "stop": 1754116495810}, {"name": "Search for list by listName = ListName-130681419 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116512090, "stop": 1754116512090}, {"name": "Navigate to By.xpath: //a[contains(text(),'ISO20022 Format Configuration')]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116517563, "stop": 1754116517563}, {"name": "Navigate to By.xpath: //a[contains(text(),'ISO20022 Format Configuration')]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116519219, "stop": 1754116519219}, {"name": "Create New Group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116522411, "stop": 1754116522411}, {"name": "Connect to Database and Check if User Profile = full-right-profile_07 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116528066, "stop": 1754116528066}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116528066, "stop": 1754116528066}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116528117, "stop": 1754116528117}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_07' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116528117, "stop": 1754116528117}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116528119, "stop": 1754116528119}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116528119, "stop": 1754116528119}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116528119, "stop": 1754116528119}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116528139, "stop": 1754116528139}, {"name": "Delete From tListSetProfile where profile_id in (10)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116528139, "stop": 1754116528139}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116528143, "stop": 1754116528143}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116528143, "stop": 1754116528143}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116528143, "stop": 1754116528143}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116528143, "stop": 1754116528143}, {"name": "Black list already exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116532317, "stop": 1754116532317}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116545156, "stop": 1754116545156}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116545156, "stop": 1754116545156}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116594652, "stop": 1754116594652}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116594653, "stop": 1754116594653}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116594653, "stop": 1754116594653}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116594679, "stop": 1754116594679}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_07'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116594679, "stop": 1754116594679}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116594681, "stop": 1754116594681}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116594681, "stop": 1754116594681}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116594681, "stop": 1754116594681}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116608723, "stop": 1754116608723}, {"name": "Test Data = ISO20022FormatConfiguration{zone='Zone (867793082)', groupName='Group_Name-584402905', iso20022SchemaConfiguration=ISO20022SchemaConfiguration{schemaName='pacs.009.001', schemaVersion='08', headerSwift='head.001.001.01', expectedResults='Schema version [%s] successfully deleted'}, expectedResults='Successfully imported schema [pacs.009.001] with version [08].'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116612314, "stop": 1754116612314}, {"name": "Import schema.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116612314, "stop": 1754116612314}, {"name": "Navigate to By.xpath: //a[contains(text(),'ISO20022 Schema Configuration')]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116613472, "stop": 1754116613472}, {"name": "Navigate to By.xpath: //a[@href='#iso20022:homepage_business:tabViewListManager:tabView:schemaConfiguration']", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116614100, "stop": 1754116614100}, {"name": "Check if schema with Name = pacs.009.001", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116614531, "stop": 1754116614531}, {"name": "Delete schema with name = pacs.009.001", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116614738, "stop": 1754116614738}, {"name": "Validation message = Schema version [pacs.009.001.08] successfully deleted.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116616762, "stop": 1754116616762}, {"name": "Schema File Path = C:\\Users\\<USER>\\IdeaProjects\\SWS_selenium/src/test/resources/uploadsAndDownloads/uploads/ISO/pacs.009.001.08.xsd", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116616762, "stop": 1754116616762}, {"name": "Validation message = Successfully imported schema [pacs.009.001] with version [08].", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116619326, "stop": 1754116619326}, {"name": "Import schema.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116619326, "stop": 1754116619326}, {"name": "Navigate to By.xpath: //a[contains(text(),'ISO20022 Schema Configuration')]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116620665, "stop": 1754116620665}, {"name": "Navigate to By.xpath: //a[@href='#iso20022:homepage_business:tabViewListManager:tabView:schemaConfiguration']", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116621159, "stop": 1754116621159}, {"name": "Check if schema with Name = head.001.001", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116621668, "stop": 1754116621668}, {"name": "Delete schema with name = head.001.001", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116621915, "stop": 1754116621915}, {"name": "Validation message = Schema version [head.001.001.01] successfully deleted.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116623721, "stop": 1754116623721}, {"name": "Schema File Path = C:\\Users\\<USER>\\IdeaProjects\\SWS_selenium/src/test/resources/uploadsAndDownloads/uploads/ISO/head.001.001.01.xsd", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116623722, "stop": 1754116623722}, {"name": "Validation message = Successfully imported schema [head.001.001] with version [01].", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116625292, "stop": 1754116625292}, {"name": "Add new message.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116625292, "stop": 1754116625292}, {"name": "Navigate to By.xpath: //a[contains(text(),'ISO20022 Format Configuration')]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116626727, "stop": 1754116626727}, {"name": "Sort table results by creation to click on the latest created group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116627490, "stop": 1754116627490}, {"name": "Validation message = This message already exist!\"", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116647947, "stop": 1754116647947}, {"name": "Actual result = This message already exist!\"", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116647947, "stop": 1754116647947}], "attachments": [], "parameters": [{"name": "arg0", "value": "ISO20022FormatDetailsConfiguration{headerField='null', bodyField='CdtTrfTxInf/PmtId/TxId', Xpath='null', bodyFieldType='null', SEPA='null', category='SKIPPED', quickLink='null', fieldLinkWith='null', iso20022FormatConfiguration$=ISO20022FormatConfiguration{zone='null', groupName='Group_Name-%s', iso20022SchemaConfiguration=ISO20022SchemaConfiguration{schemaName='head.001.001', schemaVersion='01', headerSwift='head.001.001.01', expectedResults='Schema version [%s] successfully deleted'}, expectedResults='Successfully imported schema [%s] with version [%s].'}}"}], "start": 1754116495808, "stop": 1754116647947}