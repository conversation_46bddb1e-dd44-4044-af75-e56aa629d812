{"uuid": "c15da96e-e4d5-4673-92ab-9c4bf52b7e6a", "historyId": "dfb35eeea8294d054ea460646a487b72", "fullName": "eastnets.screening.regression.reports.Report_TC014.report_TC014", "labels": [{"name": "package", "value": "eastnets.screening.regression.reports.Report_TC014"}, {"name": "testClass", "value": "eastnets.screening.regression.reports.Report_TC014"}, {"name": "testMethod", "value": "report_TC014"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Report Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.reports.Report_TC014"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "tag", "value": "Reports"}], "links": [], "name": "Verify Session Info Report", "status": "failed", "statusDetails": {"known": false, "muted": false, "flaky": false, "message": "Test Failed due to an exception occurred in POM's method", "trace": "java.lang.AssertionError: Test Failed due to an exception occurred in POM's method\r\n\tat org.testng.Assert.fail(Assert.java:98)\r\n\tat core.ExceptionHandler.onExceptionRaised(ExceptionHandler.java:13)\r\n\tat eastnets.screening.regression.reports.Report_TC014.report_TC014(Report_TC014.java:140)\r\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:578)\r\n\tat org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:677)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:221)\r\n\tat org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:50)\r\n\tat org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:969)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:194)\r\n\tat org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148)\r\n\tat org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128)\r\n\tat java.base/java.util.ArrayList.forEach(ArrayList.java:1511)\r\n\tat org.testng.TestRunner.privateRun(TestRunner.java:829)\r\n\tat org.testng.TestRunner.run(TestRunner.java:602)\r\n\tat org.testng.SuiteRunner.runTest(SuiteRunner.java:437)\r\n\tat org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:475)\r\n\tat org.testng.internal.thread.ThreadUtil.lambda$execute$0(ThreadUtil.java:58)\r\n\tat java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)\r\n\tat java.base/java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:132)\r\n\tat java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:113)\r\n\tat java.base/java.util.concurrent.AbstractExecutorService.invokeAll(AbstractExecutorService.java:274)\r\n\tat org.testng.internal.thread.ThreadUtil.execute(ThreadUtil.java:64)\r\n\tat org.testng.SuiteRunner.runInParallelTestMode(SuiteRunner.java:458)\r\n\tat org.testng.SuiteRunner.privateRun(SuiteRunner.java:389)\r\n\tat org.testng.SuiteRunner.run(SuiteRunner.java:330)\r\n\tat org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)\r\n\tat org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)\r\n\tat org.testng.TestNG.runSuitesSequentially(TestNG.java:1256)\r\n\tat org.testng.TestNG.runSuitesLocally(TestNG.java:1176)\r\n\tat org.testng.TestNG.runSuites(TestNG.java:1099)\r\n\tat org.testng.TestNG.run(TestNG.java:1067)\r\n\tat com.intellij.rt.testng.IDEARemoteTestNG.run(IDEARemoteTestNG.java:65)\r\n\tat com.intellij.rt.testng.RemoteTestNGStarter.main(RemoteTestNGStarter.java:105)\r\nCaused by: org.openqa.selenium.TimeoutException: Expected condition failed: waiting for number of elements found by By.xpath: //td[text() ='1456']//ancestor::tr[1]//div[contains(@class,'ui-selectbooleancheckbox')] to be more than \"0\". Current number: \"0\" (tried for 120 second(s) with 10 milliseconds interval)\nBuild info: version: '4.13.0', revision: 'ba948ece5b*'\nSystem info: os.name: 'Windows Server 2022', os.arch: 'amd64', os.version: '10.0', java.version: '19.0.2'\nDriver info: driver.version: unknown\r\n\tat org.openqa.selenium.support.ui.FluentWait.timeoutException(FluentWait.java:262)\r\n\tat org.openqa.selenium.support.ui.FluentWait.until(FluentWait.java:230)\r\n\tat core.gui.Controls.getWebElement(Controls.java:29)\r\n\tat core.gui.Controls.performClick(Controls.java:108)\r\n\tat eastnets.screening.gui.reports.ReportEditor.enterSessionID(ReportEditor.java:107)\r\n\tat eastnets.screening.control.ReportsControl.createNewReportWithSessionData(ReportsControl.java:161)\r\n\tat eastnets.screening.regression.reports.Report_TC014.report_TC014(Report_TC014.java:111)\r\n\t... 34 more\r\n"}, "stage": "finished", "description": "Verify Session Info Report", "steps": [{"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-858765209', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@7ad18ea5, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-858765209', officialDate='null', entry=[ListEntry{type='null', name='EntryName-858765209', firstName='EntryFirstName-858765209', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-858765209', firstName='EntryFirstName-858765209', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754110934802, "stop": 1754110934802}, {"name": "Connect to Database and Check if User Profile = full-right-profile_08 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754110934802, "stop": 1754110934802}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754110934803, "stop": 1754110934803}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754110934861, "stop": 1754110934861}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_08' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754110934861, "stop": 1754110934862}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754110934862, "stop": 1754110934862}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754110934862, "stop": 1754110934862}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754110934862, "stop": 1754110934862}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754110934898, "stop": 1754110934898}, {"name": "Delete From tListSetProfile where profile_id in (11)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754110934898, "stop": 1754110934898}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754110934901, "stop": 1754110934902}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754110934902, "stop": 1754110934902}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754110934902, "stop": 1754110934902}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754110934902, "stop": 1754110934902}, {"name": "Search for list by listName = ListName-858765209 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754110959778, "stop": 1754110959778}, {"name": "Set zone : Common Zone 08", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754110968378, "stop": 1754110968378}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754110970420, "stop": 1754110970420}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754110970935, "stop": 1754110970935}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754110971500, "stop": 1754110971500}, {"name": "Set template name = templateName-858765209", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754110971733, "stop": 1754110971733}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754110973835, "stop": 1754110973835}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754110974767, "stop": 1754110974767}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754110976144, "stop": 1754110976144}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754110976927, "stop": 1754110976927}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111015198, "stop": 1754111015198}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111015198, "stop": 1754111015198}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111079227, "stop": 1754111079227}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111079227, "stop": 1754111079227}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111079227, "stop": 1754111079227}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111079259, "stop": 1754111079259}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_08'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111079259, "stop": 1754111079259}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111079262, "stop": 1754111079262}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111079262, "stop": 1754111079262}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111079263, "stop": 1754111079263}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111097149, "stop": 1754111097149}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111103102, "stop": 1754111103102}, {"name": "Validation message = File sent to the server for processing with id [2821]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111113638, "stop": 1754111113638}, {"name": "Alert Message = File sent to the server for processing with id [2821]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111113638, "stop": 1754111113638}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111114149, "stop": 1754111114149}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111124025, "stop": 1754111124025}, {"name": "Detection ID = 6958", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111125538, "stop": 1754111125538}, {"name": "Attempting to save screenshot...", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111255395, "stop": 1754111255395}, {"name": "Screenshot saved at: C:\\Users\\<USER>\\IdeaProjects\\SWS_selenium\\FailedTestsScreenshots\\screenshot_1754111255395.png", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111255401, "stop": 1754111255401}, {"name": "Error occurred While logging in eastnets.screening.regression.reports.Report_TC014$5.report_TC014 ----> Expected condition failed: waiting for number of elements found by By.xpath: //td[text() ='1456']//ancestor::tr[1]//div[contains(@class,'ui-selectbooleancheckbox')] to be more than \"0\". Current number: \"0\" (tried for 120 second(s) with 10 milliseconds interval)\nBuild info: version: '4.13.0', revision: 'ba948ece5b*'\nSystem info: os.name: 'Windows Server 2022', os.arch: 'amd64', os.version: '10.0', java.version: '19.0.2'\nDriver info: driver.version: unknown", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111255407, "stop": 1754111255407}], "attachments": [{"name": "Screenshots", "source": "4f44199b-6c87-4dc8-aa2c-5d9576119f19-attachment.png"}], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754110934799, "stop": 1754111255408}