{"uuid": "bb315d45-2cef-4141-a27e-3c3ec36ae43d", "historyId": "96ed54623af6312335e7c280a2a9d491", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC009.scanManager_TC009", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC009"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC009"}, {"name": "testMethod", "value": "scanManager_TC009"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC009"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is able to Print an alert report in Excel format", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to Print an alert report in Excel format", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106068896, "stop": 1754106068896}, {"name": "Validation message = File sent to the server for processing with id [2771]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106081241, "stop": 1754106081241}, {"name": "Alert Message = File sent to the server for processing with id [2771]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106081241, "stop": 1754106081241}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106082336, "stop": 1754106082337}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106090631, "stop": 1754106090631}, {"name": "Detection ID = 6870", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106091795, "stop": 1754106091796}, {"name": "Start Exporting Violation With Print Scope all And Document Type Excel", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106091806, "stop": 1754106091806}, {"name": "Click on close dialog button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106099350, "stop": 1754106099350}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "eastnets.screening.entity.Report@694b59a5"}], "start": 1754106067553, "stop": 1754106130377}