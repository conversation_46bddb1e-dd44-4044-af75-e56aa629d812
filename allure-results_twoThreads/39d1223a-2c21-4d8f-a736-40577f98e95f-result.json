{"uuid": "39d1223a-2c21-4d8f-a736-40577f98e95f", "historyId": "3b61debdc1ef290b8ba123eb27e940bf", "fullName": "eastnets.screening.regression.detectionmanager.DetectionManager_TC028.detectionManager_TC028", "labels": [{"name": "package", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC028"}, {"name": "testClass", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC028"}, {"name": "testMethod", "value": "detectionManager_TC028"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Detection Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC028"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-1(36)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Detection Manager"}], "links": [{"name": "81179", "type": "issue"}], "name": "Verify that user is able to Create alert for any open alert that is created from 'Name Checker' from the 'Alerts Section' WITHOUT adding a comment", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "related to Bug 81179: AIX - Cant creat an alert without inserting comment\n*Verify that user is able to Create alert for any open alert that is created from 'Name Checker' from the 'Alerts Section' WITHOUT adding a comment", "steps": [{"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-868974293', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@659bee9b, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-868974293', officialDate='null', entry=[ListEntry{type='null', name='EntryName-868974293', firstName='EntryFirstName-868974293', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-868974293', firstName='EntryFirstName-868974293', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114692041, "stop": 1754114692041}, {"name": "Connect to Database and Check if User Profile = full-right-profile_05 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114692042, "stop": 1754114692042}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114692042, "stop": 1754114692042}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114692099, "stop": 1754114692099}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_05' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114692099, "stop": 1754114692099}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114692102, "stop": 1754114692102}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114692102, "stop": 1754114692102}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114692102, "stop": 1754114692102}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114692127, "stop": 1754114692127}, {"name": "Delete From tListSetProfile where profile_id in (8)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114692127, "stop": 1754114692127}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114692131, "stop": 1754114692131}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114692131, "stop": 1754114692131}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114692132, "stop": 1754114692132}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114692132, "stop": 1754114692132}, {"name": "Search for list by listName = ListName-868974293 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114713518, "stop": 1754114713518}, {"name": "Set zone : Common Zone 05", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114722141, "stop": 1754114722141}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114724192, "stop": 1754114724192}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114724793, "stop": 1754114724793}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114725205, "stop": 1754114725205}, {"name": "Set template name = templateName-868974293", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114725484, "stop": 1754114725484}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114727955, "stop": 1754114727955}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114728744, "stop": 1754114728745}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114729989, "stop": 1754114729989}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114730588, "stop": 1754114730588}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114801242, "stop": 1754114801242}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114801242, "stop": 1754114801242}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114877063, "stop": 1754114877063}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114877063, "stop": 1754114877063}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114877063, "stop": 1754114877063}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114877096, "stop": 1754114877096}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_05'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114877096, "stop": 1754114877096}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114877098, "stop": 1754114877098}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114877098, "stop": 1754114877098}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114877099, "stop": 1754114877099}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114897093, "stop": 1754114897093}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114905095, "stop": 1754114905095}, {"name": "Status for the scanned name = REP", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114919983, "stop": 1754114919983}, {"name": "Detection ID for the scanned name = 7104", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114920263, "stop": 1754114920263}, {"name": "Detection Status = REP", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114924544, "stop": 1754114924544}, {"name": "Validation message = Please, enter a note before saving!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114927919, "stop": 1754114927919}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754114692038, "stop": 1754114927919}