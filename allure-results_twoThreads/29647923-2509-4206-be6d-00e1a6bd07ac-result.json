{"uuid": "*************-4206-be6d-00e1a6bd07ac", "historyId": "67ff074ef3d0c3ac056ab3713a670802", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC009.scanManager_TC009", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC009"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC009"}, {"name": "testMethod", "value": "scanManager_TC009"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC009"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is able to Print an alert report in RTF format", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to Print an alert report in RTF format", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106138403, "stop": 1754106138403}, {"name": "Validation message = File sent to the server for processing with id [2773]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106151680, "stop": 1754106151680}, {"name": "Alert Message = File sent to the server for processing with id [2773]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106151680, "stop": 1754106151680}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106152524, "stop": 1754106152524}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106160868, "stop": 1754106160868}, {"name": "Detection ID = 6873", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106162255, "stop": 1754106162255}, {"name": "Start Exporting Violation With Print Scope all And Document Type RTF", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106162265, "stop": 1754106162265}, {"name": "Click on close dialog button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106169614, "stop": 1754106169614}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "eastnets.screening.entity.Report@48a92c57"}], "start": 1754106138225, "stop": 1754106200595}