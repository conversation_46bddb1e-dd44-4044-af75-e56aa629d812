{"uuid": "78eab8e0-ae24-45ff-9bbc-7da13a1dd0de", "historyId": "c1c14de74bb43bbf5f8d10a287df71e", "fullName": "eastnets.admin.AdminTest.checkPermissionWhenAssignSecGroupProfileZone", "labels": [{"name": "package", "value": "eastnets.admin.AdminTest"}, {"name": "testClass", "value": "eastnets.admin.AdminTest"}, {"name": "testMethod", "value": "checkPermissionWhenAssignSecGroupProfileZone"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Admin Test Cases"}, {"name": "subSuite", "value": "eastnets.admin.AdminTest"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}], "links": [], "name": "Verify that the user that is assigned to Group, Profile, and Zone have permission to Block but don't have the permission to Release is able to release when this user is assigned to a second Group, Profile and Zone that have the permission to Release.", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that the user that is assigned to Group, Profile, and Zone have permission to Block but don't have the permission to Release is able to release when this user is assigned to a second Group, Profile and Zone that have the permission to Release.", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114863636, "stop": 1754114863636}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='sysadmin'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114863636, "stop": 1754114863636}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114863642, "stop": 1754114863642}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114863642, "stop": 1754114863642}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114863643, "stop": 1754114863643}, {"name": "Login with User Name = sysadmin and Password = manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114863801, "stop": 1754114863801}, {"name": "Operator test data = Operator{id=0, loginName='selenium-user-563647478', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone878572785', displayName='Zone (878572785)', description='Zone created with random number '878572785''}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114868809, "stop": 1754114868809}, {"name": "Group test data = Group{id=0, name='selenium-random-group-477065748', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-829081833', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone878572785', displayName='Zone (878572785)', description='Zone created with random number '878572785''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-563647478', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone878572785', displayName='Zone (878572785)', description='Zone created with random number '878572785''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114868809, "stop": 1754114868809}, {"name": "Create new zone .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114868809, "stop": 1754114868809}, {"name": "Zone test Data = Zone{id=0, name='Zone878572785', displayName='Zone (878572785)', description='Zone created with random number '878572785''}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114868809, "stop": 1754114868809}, {"name": "Check if zone with name = 'Zone878572785' exists", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114870354, "stop": 1754114870354}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114870640, "stop": 1754114870640}, {"name": "Enter name =Zone878572785", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114876064, "stop": 1754114876064}, {"name": "Enter display Name =Zone (878572785)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114876739, "stop": 1754114876739}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114877481, "stop": 1754114877481}, {"name": "Zone doesn't exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114878158, "stop": 1754114878158}, {"name": "Click add new zone button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114878158, "stop": 1754114878158}, {"name": "Set create new zone data.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114878390, "stop": 1754114878390}, {"name": "Set name = Zone878572785", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114878390, "stop": 1754114878390}, {"name": "Set display name = Zone (878572785)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114879866, "stop": 1754114879866}, {"name": "Set description = Zone created with random number '878572785'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114880619, "stop": 1754114880619}, {"name": "Capture zone id from UI = 132", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114881852, "stop": 1754114881852}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114881852, "stop": 1754114881852}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114883171, "stop": 1754114883171}, {"name": "Enter name =Zone878572785", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114888502, "stop": 1754114888502}, {"name": "Enter display Name =Zone (878572785)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114889314, "stop": 1754114889314}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114889933, "stop": 1754114889933}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114892460, "stop": 1754114892460}, {"name": "Enter name =Zone878572785", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114897816, "stop": 1754114897816}, {"name": "Enter display Name =Zone (878572785)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114898616, "stop": 1754114898616}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114899360, "stop": 1754114899360}, {"name": "Create first profile .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114900050, "stop": 1754114900050}, {"name": "Profile test Data = Profile{id=0, name='Test-Profile-829081833', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone878572785', displayName='Zone (878572785)', description='Zone created with random number '878572785''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114900050, "stop": 1754114900050}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114902404, "stop": 1754114902404}, {"name": "Set name = Test-Profile-829081833 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114907880, "stop": 1754114907880}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114908720, "stop": 1754114908720}, {"name": "Click add new profile button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114909403, "stop": 1754114909404}, {"name": "Set name = Test-Profile-829081833 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114909628, "stop": 1754114909628}, {"name": "Set description = Random profile for Selenium testing ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114911012, "stop": 1754114911012}, {"name": "Check enable checkbox to be true ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114911839, "stop": 1754114911839}, {"name": "Check write right checkbox to be Test-Profile-829081833 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114912500, "stop": 1754114912500}, {"name": "Select zone = Test-Profile-829081833 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114912789, "stop": 1754114912789}, {"name": "Add SWS item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114919376, "stop": 1754114919376}, {"name": "Click on the SWS item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114919376, "stop": 1754114919376}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114919865, "stop": 1754114919865}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114920880, "stop": 1754114920880}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114921317, "stop": 1754114921317}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114921412, "stop": 1754114921412}, {"name": "Set name = Test-Profile-829081833 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114927526, "stop": 1754114927526}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114928338, "stop": 1754114928338}, {"name": "Select profile from result view.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114929151, "stop": 1754114929151}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114929941, "stop": 1754114929941}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114931295, "stop": 1754114931295}, {"name": "Add en.Reporting Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114932272, "stop": 1754114932272}, {"name": "Click on the en.Reporting Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114932272, "stop": 1754114932272}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114932661, "stop": 1754114932662}, {"name": "Add SWIFT Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114934120, "stop": 1754114934120}, {"name": "Click on the SWIFT Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114934120, "stop": 1754114934120}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114934507, "stop": 1754114934507}, {"name": "Add SWP Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114935931, "stop": 1754114935931}, {"name": "Click on the SWP Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114935931, "stop": 1754114935931}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114936358, "stop": 1754114936358}, {"name": "Add Detection Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114937773, "stop": 1754114937773}, {"name": "Click on the Detection Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114937773, "stop": 1754114937773}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114938165, "stop": 1754114938165}, {"name": "Add Scan Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114939599, "stop": 1754114939599}, {"name": "Click on the Scan Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114939599, "stop": 1754114939599}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114939996, "stop": 1754114939996}, {"name": "Add Format Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114941415, "stop": 1754114941415}, {"name": "Click on the Format Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114941415, "stop": 1754114941415}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114941892, "stop": 1754114941892}, {"name": "Add Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114943333, "stop": 1754114943333}, {"name": "Click on the Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114943333, "stop": 1754114943333}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114943705, "stop": 1754114943705}, {"name": "Add Batch List Management item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114945161, "stop": 1754114945161}, {"name": "Click on the Batch List Management item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114945161, "stop": 1754114945161}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114945520, "stop": 1754114945520}, {"name": "Add ListExplorer item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114946915, "stop": 1754114946915}, {"name": "Click on the ListExplorer item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114946915, "stop": 1754114946915}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114947283, "stop": 1754114947283}, {"name": "Add ListSetManager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114948661, "stop": 1754114948661}, {"name": "Click on the ListSetManager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114948661, "stop": 1754114948661}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114949014, "stop": 1754114949014}, {"name": "Add List Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114950414, "stop": 1754114950414}, {"name": "Click on the List Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114950414, "stop": 1754114950414}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114950819, "stop": 1754114950819}, {"name": "Add File Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114952279, "stop": 1754114952279}, {"name": "Click on the File Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114952279, "stop": 1754114952279}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114952636, "stop": 1754114952636}, {"name": "Add Advanced Settings item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114954010, "stop": 1754114954010}, {"name": "Click on the Advanced Settings item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114954010, "stop": 1754114954010}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114954423, "stop": 1754114954423}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114955840, "stop": 1754114955840}, {"name": "Accept <PERSON><PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114956273, "stop": 1754114956273}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114957868, "stop": 1754114957868}, {"name": "Select Module Rights.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114958624, "stop": 1754114958624}, {"name": "Processing module 'SWIFT Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114960625, "stop": 1754114960625}, {"name": "Click on SWIFT Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114960625, "stop": 1754114960625}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114961489, "stop": 1754114961489}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114961807, "stop": 1754114961807}, {"name": "Processing module 'Detection Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114964948, "stop": 1754114964948}, {"name": "Click on Detection Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114964948, "stop": 1754114964948}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114965859, "stop": 1754114965859}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114966101, "stop": 1754114966101}, {"name": "Processing module 'Scan Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114968278, "stop": 1754114968278}, {"name": "Click on Scan Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114968278, "stop": 1754114968278}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114969130, "stop": 1754114969130}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114969400, "stop": 1754114969400}, {"name": "Processing module 'Format Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114971571, "stop": 1754114971571}, {"name": "Click on Format Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114971571, "stop": 1754114971571}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114972656, "stop": 1754114972656}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114972896, "stop": 1754114972896}, {"name": "Processing module 'Name Checker'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114975033, "stop": 1754114975033}, {"name": "Click on Name Checker", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114975033, "stop": 1754114975033}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114976235, "stop": 1754114976235}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114976452, "stop": 1754114976452}, {"name": "Processing module 'ListExplorer'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114979566, "stop": 1754114979566}, {"name": "Click on ListExplorer", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114979566, "stop": 1754114979566}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114980571, "stop": 1754114980571}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114980769, "stop": 1754114980769}, {"name": "Processing module 'ListSetManager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114982938, "stop": 1754114982938}, {"name": "Click on ListSetManager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114982938, "stop": 1754114982938}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114983899, "stop": 1754114983899}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114984121, "stop": 1754114984121}, {"name": "Processing module 'List Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114986396, "stop": 1754114986396}, {"name": "Click on List Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114986396, "stop": 1754114986396}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114987301, "stop": 1754114987301}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114987607, "stop": 1754114987607}, {"name": "Processing module 'File Scanner'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114989943, "stop": 1754114989943}, {"name": "Click on File Scanner", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114989943, "stop": 1754114989943}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114990966, "stop": 1754114990966}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114991182, "stop": 1754114991182}, {"name": "Processing module 'Advanced Settings'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114993370, "stop": 1754114993370}, {"name": "Click on Advanced Settings", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114993370, "stop": 1754114993370}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114994227, "stop": 1754114994227}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114994441, "stop": 1754114994441}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114995704, "stop": 1754114995704}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114996038, "stop": 1754114996038}, {"name": "Check if profile exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114997553, "stop": 1754114997553}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114997553, "stop": 1754114997553}, {"name": "Set name = Test-Profile-829081833 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115003233, "stop": 1754115003233}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115004034, "stop": 1754115004034}, {"name": "Profile exist = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115004944, "stop": 1754115004944}, {"name": "Create new operator.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115004944, "stop": 1754115004944}, {"name": "Operator test Data = Operator{id=0, loginName='selenium-user-563647478', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone878572785', displayName='Zone (878572785)', description='Zone created with random number '878572785''}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115004944, "stop": 1754115004944}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115006566, "stop": 1754115006566}, {"name": "Set login name = selenium-user-563647478", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115011887, "stop": 1754115011887}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115012535, "stop": 1754115012535}, {"name": "Click Create new operator button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115013195, "stop": 1754115013195}, {"name": "Set login name = selenium-user-563647478", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115013433, "stop": 1754115013433}, {"name": "Set first name = Random", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115014897, "stop": 1754115014897}, {"name": "Set last name = User", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115015541, "stop": 1754115015541}, {"name": "Set password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115016210, "stop": 1754115016210}, {"name": "Set confirm password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115016926, "stop": 1754115016926}, {"name": "Set enable flag = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115017663, "stop": 1754115017663}, {"name": "Set reset flag = false", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115018247, "stop": 1754115018247}, {"name": "Select zone  = Zone (878572785)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115019445, "stop": 1754115019445}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115024963, "stop": 1754115024963}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115025208, "stop": 1754115025208}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115031805, "stop": 1754115031805}, {"name": "Set login name = selenium-user-563647478", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115037097, "stop": 1754115037097}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115037737, "stop": 1754115037737}, {"name": "Create first group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115038450, "stop": 1754115038450}, {"name": "Group test Data = Group{id=0, name='selenium-random-group-477065748', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-829081833', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone878572785', displayName='Zone (878572785)', description='Zone created with random number '878572785''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-563647478', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone878572785', displayName='Zone (878572785)', description='Zone created with random number '878572785''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115038450, "stop": 1754115038450}, {"name": "Connect to database to remove Link between profile = Test-Profile-829081833 and Permission Allow to add attachments to detection", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115070138, "stop": 1754115070138}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115070164, "stop": 1754115070164}, {"name": "SELECT P.NAME as PR<PERSON><PERSON><PERSON>_NAME , PR.PROFILE_ID as PROFILE_ID , PR.GRANT_ID as GRANT_ID , PR.GRANT_RIGHT_ID as GRANT_RIGHT_ID , PR.GRANT_TYPE_ID as GRANT_TYPE_ID   FROM tModuleFunctions M   LEFT JOIN tProfileRights PR On PR.GRANT_ID  = M.ID     LEFT Join tProfiles P ON P.id =PR.PROFILE_ID   WHERE DESCRIPTION ='Allow to add attachments to detection' and P.NAME = 'Test-Profile-829081833'  and PR.GRANT_TYPE_ID =3", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115070164, "stop": 1754115070164}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115070176, "stop": 1754115070176}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115070177, "stop": 1754115070177}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115070177, "stop": 1754115070177}, {"name": "Delete function permission 'Allow to add attachments to detection' from DataBase", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115070177, "stop": 1754115070177}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115070198, "stop": 1754115070198}, {"name": "DELETE FROM tProfileRights  WHERE GRANT_ID  ='137' and PROFILE_ID ='150' and GRANT_TYPE_ID =3", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115070198, "stop": 1754115070198}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115070211, "stop": 1754115070211}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115070211, "stop": 1754115070211}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115070211, "stop": 1754115070211}, {"name": "Create second zone .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115070212, "stop": 1754115070212}, {"name": "Zone test Data = Zone{id=0, name='Zone77473818', displayName='Zone (77473818)', description='Zone created with random number '77473818''}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115070212, "stop": 1754115070212}, {"name": "Check if zone with name = 'Zone77473818' exists", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115071464, "stop": 1754115071464}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115071714, "stop": 1754115071714}, {"name": "Enter name =Zone77473818", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115077018, "stop": 1754115077018}, {"name": "Enter display Name =Zone (77473818)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115077575, "stop": 1754115077575}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115078143, "stop": 1754115078143}, {"name": "Zone doesn't exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115078615, "stop": 1754115078615}, {"name": "Click add new zone button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115078615, "stop": 1754115078615}, {"name": "Set create new zone data.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115078781, "stop": 1754115078781}, {"name": "Set name = Zone77473818", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115078781, "stop": 1754115078781}, {"name": "Set display name = Zone (77473818)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115079739, "stop": 1754115079739}, {"name": "Set description = Zone created with random number '77473818'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115080328, "stop": 1754115080328}, {"name": "Capture zone id from UI = 133", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115081129, "stop": 1754115081129}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115081129, "stop": 1754115081129}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115082154, "stop": 1754115082154}, {"name": "Enter name =Zone77473818", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115087411, "stop": 1754115087411}, {"name": "Enter display Name =Zone (77473818)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115088025, "stop": 1754115088025}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115088545, "stop": 1754115088545}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115090448, "stop": 1754115090448}, {"name": "Enter name =Zone77473818", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115095728, "stop": 1754115095728}, {"name": "Enter display Name =Zone (77473818)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115096509, "stop": 1754115096509}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115097141, "stop": 1754115097141}, {"name": "Create second profile .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115097728, "stop": 1754115097728}, {"name": "Profile test Data = Profile{id=0, name='Test-Profile-748998070', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone77473818', displayName='Zone (77473818)', description='Zone created with random number '77473818''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115097728, "stop": 1754115097728}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115098851, "stop": 1754115098851}, {"name": "Set name = Test-Profile-748998070 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115104146, "stop": 1754115104146}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115104899, "stop": 1754115104899}, {"name": "Click add new profile button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115105520, "stop": 1754115105520}, {"name": "Set name = Test-Profile-748998070 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115105722, "stop": 1754115105722}, {"name": "Set description = Random profile for Selenium testing ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115106912, "stop": 1754115106912}, {"name": "Check enable checkbox to be true ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115107639, "stop": 1754115107639}, {"name": "Check write right checkbox to be Test-Profile-748998070 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115108277, "stop": 1754115108277}, {"name": "Select zone = Test-Profile-748998070 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115108497, "stop": 1754115108497}, {"name": "Add SWS item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115114204, "stop": 1754115114204}, {"name": "Click on the SWS item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115114204, "stop": 1754115114204}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115114635, "stop": 1754115114635}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115115579, "stop": 1754115115579}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115115821, "stop": 1754115115821}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115115890, "stop": 1754115115890}, {"name": "Set name = Test-Profile-748998070 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115121802, "stop": 1754115121802}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115122396, "stop": 1754115122396}, {"name": "Select profile from result view.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115123107, "stop": 1754115123107}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115123588, "stop": 1754115123588}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115124748, "stop": 1754115124748}, {"name": "Add en.Reporting Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115125343, "stop": 1754115125343}, {"name": "Click on the en.Reporting Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115125343, "stop": 1754115125343}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115125721, "stop": 1754115125721}, {"name": "Add SWIFT Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115127161, "stop": 1754115127161}, {"name": "Click on the SWIFT Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115127161, "stop": 1754115127161}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115127539, "stop": 1754115127539}, {"name": "Add SWP Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115128969, "stop": 1754115128969}, {"name": "Click on the SWP Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115128969, "stop": 1754115128969}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115129345, "stop": 1754115129345}, {"name": "Add Detection Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115130742, "stop": 1754115130742}, {"name": "Click on the Detection Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115130742, "stop": 1754115130742}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115131177, "stop": 1754115131177}, {"name": "Add Scan Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115132564, "stop": 1754115132564}, {"name": "Click on the Scan Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115132564, "stop": 1754115132564}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115132948, "stop": 1754115132948}, {"name": "Add Format Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115134453, "stop": 1754115134453}, {"name": "Click on the Format Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115134453, "stop": 1754115134453}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115134965, "stop": 1754115134965}, {"name": "Add Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115136365, "stop": 1754115136365}, {"name": "Click on the Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115136365, "stop": 1754115136365}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115136699, "stop": 1754115136699}, {"name": "Add Batch List Management item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115138106, "stop": 1754115138106}, {"name": "Click on the Batch List Management item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115138106, "stop": 1754115138106}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115138480, "stop": 1754115138480}, {"name": "Add ListExplorer item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115139927, "stop": 1754115139927}, {"name": "Click on the ListExplorer item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115139927, "stop": 1754115139927}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115140289, "stop": 1754115140289}, {"name": "Add ListSetManager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115141673, "stop": 1754115141673}, {"name": "Click on the ListSetManager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115141673, "stop": 1754115141673}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115142044, "stop": 1754115142044}, {"name": "Add List Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115143455, "stop": 1754115143455}, {"name": "Click on the List Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115143455, "stop": 1754115143455}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115143920, "stop": 1754115143920}, {"name": "Add File Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115145400, "stop": 1754115145400}, {"name": "Click on the File Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115145400, "stop": 1754115145400}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115145760, "stop": 1754115145760}, {"name": "Add Advanced Settings item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115147186, "stop": 1754115147186}, {"name": "Click on the Advanced Settings item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115147186, "stop": 1754115147186}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115147510, "stop": 1754115147510}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115148885, "stop": 1754115148885}, {"name": "Accept <PERSON><PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115149193, "stop": 1754115149193}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115150629, "stop": 1754115150629}, {"name": "Select Module Rights.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115151375, "stop": 1754115151375}, {"name": "Processing module 'SWIFT Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115153376, "stop": 1754115153376}, {"name": "Click on SWIFT Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115153376, "stop": 1754115153376}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115154220, "stop": 1754115154220}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115154476, "stop": 1754115154476}, {"name": "Processing module 'Detection Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115157857, "stop": 1754115157857}, {"name": "Click on Detection Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115157857, "stop": 1754115157857}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115158893, "stop": 1754115158893}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115159118, "stop": 1754115159118}, {"name": "Processing module 'Scan Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115161248, "stop": 1754115161248}, {"name": "Click on Scan Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115161248, "stop": 1754115161248}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115162166, "stop": 1754115162166}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115162416, "stop": 1754115162416}, {"name": "Processing module 'Format Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115164623, "stop": 1754115164623}, {"name": "Click on Format Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115164623, "stop": 1754115164623}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115165621, "stop": 1754115165621}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115165831, "stop": 1754115165831}, {"name": "Processing module 'Name Checker'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115168022, "stop": 1754115168022}, {"name": "Click on Name Checker", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115168022, "stop": 1754115168022}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115168887, "stop": 1754115168887}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115169111, "stop": 1754115169111}, {"name": "Processing module 'ListExplorer'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115172218, "stop": 1754115172218}, {"name": "Click on ListExplorer", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115172218, "stop": 1754115172218}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115173269, "stop": 1754115173269}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115173465, "stop": 1754115173465}, {"name": "Processing module 'ListSetManager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115175641, "stop": 1754115175641}, {"name": "Click on ListSetManager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115175641, "stop": 1754115175641}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115176644, "stop": 1754115176644}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115176843, "stop": 1754115176843}, {"name": "Processing module 'List Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115178983, "stop": 1754115178983}, {"name": "Click on List Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115178983, "stop": 1754115178983}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115179845, "stop": 1754115179845}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115180112, "stop": 1754115180112}, {"name": "Processing module 'File Scanner'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115182272, "stop": 1754115182272}, {"name": "Click on File Scanner", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115182272, "stop": 1754115182272}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115183345, "stop": 1754115183345}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115183612, "stop": 1754115183612}, {"name": "Processing module 'Advanced Settings'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115185828, "stop": 1754115185828}, {"name": "Click on Advanced Settings", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115185828, "stop": 1754115185828}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115186694, "stop": 1754115186694}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115186900, "stop": 1754115186900}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115188082, "stop": 1754115188082}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115188318, "stop": 1754115188318}, {"name": "Check if profile exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115189612, "stop": 1754115189612}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115189612, "stop": 1754115189612}, {"name": "Set name = Test-Profile-748998070 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115195294, "stop": 1754115195294}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115196009, "stop": 1754115196009}, {"name": "Profile exist = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115196669, "stop": 1754115196669}, {"name": "Create second group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115196669, "stop": 1754115196669}, {"name": "Group test Data = Group{id=0, name='selenium-random-group-554998217', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-748998070', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone77473818', displayName='Zone (77473818)', description='Zone created with random number '77473818''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-563647478', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone878572785', displayName='Zone (878572785)', description='Zone created with random number '878572785''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115196669, "stop": 1754115196669}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115230302, "stop": 1754115230302}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='selenium-user-563647478'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115230302, "stop": 1754115230302}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115230309, "stop": 1754115230309}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115230309, "stop": 1754115230309}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115230310, "stop": 1754115230310}, {"name": "Login with User Name = selenium-user-563647478 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115230437, "stop": 1754115230437}, {"name": "Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-496351606', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@3e4ac7bf, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-496351606', officialDate='null', entry=[ListEntry{type='null', name='EntryName-496351606', firstName='EntryFirstName-496351606', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-496351606', firstName='EntryFirstName-496351606', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115234636, "stop": 1754115234636}, {"name": "Connect to Database and Check if User Profile = Test-Profile-748998070 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115234641, "stop": 1754115234641}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115234641, "stop": 1754115234641}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115234688, "stop": 1754115234688}, {"name": "SELECT ID FROM tProfiles Where NAME ='Test-Profile-748998070' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115234688, "stop": 1754115234688}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115234690, "stop": 1754115234690}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115234690, "stop": 1754115234690}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115234690, "stop": 1754115234690}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115234714, "stop": 1754115234714}, {"name": "Delete From tListSetProfile where profile_id in (151)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115234714, "stop": 1754115234714}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115234716, "stop": 1754115234716}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115234716, "stop": 1754115234716}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115234716, "stop": 1754115234716}, {"name": "Number of effected rows = 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115234716, "stop": 1754115234716}, {"name": "Search for list by listName = ListName-496351606 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115253730, "stop": 1754115253730}, {"name": "Set zone : Zone (77473818)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115260931, "stop": 1754115260931}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115263062, "stop": 1754115263062}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115263839, "stop": 1754115263839}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115264198, "stop": 1754115264198}, {"name": "Set template name = templateName-496351606", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115264392, "stop": 1754115264392}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115266547, "stop": 1754115266547}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115267250, "stop": 1754115267250}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115268272, "stop": 1754115268272}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115268847, "stop": 1754115268847}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115284374, "stop": 1754115284374}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115284374, "stop": 1754115284374}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115329875, "stop": 1754115329875}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115329875, "stop": 1754115329875}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115329875, "stop": 1754115329875}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115329908, "stop": 1754115329908}, {"name": "SELECT ID FROM tProfiles Where NAME ='Test-Profile-748998070'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115329908, "stop": 1754115329908}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115329911, "stop": 1754115329911}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115329911, "stop": 1754115329911}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115329912, "stop": 1754115329912}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115343380, "stop": 1754115343380}, {"name": "RJE File Content= {1:F01PTSABEMMAXXX0000000001}{2:I564ACCLFRP1XXXXN}{3:{108:ENT144037000001}}{4:\r\n:16R:GENL\r\n:20C::CORP//2015033100010560\r\n:20C::SEME//0105601900000002\r\n:23G:EntryName-496351606, EntryFirstName-496351606\r\n:22F::CAEV//DVCA\r\n:22F::CAMV//MAND\r\n:98A::PREP//20150331\r\n:25D::PROC//COMP\r\n:16R:LINK\r\n:13A::LINK//564\r\n:20C::PREV//0105601900000001\r\n:16S:LINK\r\n:16S:GENL\r\n:16R:USECU\r\n:35B:ISIN DE0005190003\r\nBMW Bayerische Motoren Werke AG\r\n:16R:ACCTINFO\r\n:97A::SAFE//000000190003\r\n:93B::ELIG//UNIT/360,\r\n:93B::PEND//UNIT/360,\r\n:93B::PENR//UNIT/0,\r\n:93B::SETT//UNIT/360,\r\n:16S:ACCTINFO\r\n:16S:USECU\r\n:16R:CADETL\r\n:98A::XDTE//20150514\r\n:98A::RDTE//20150515\r\n:22F::DIVI//FINL\r\n:16S:CADETL\r\n:16R:CAOPTN\r\n:13A::CAON//001\r\n:22F::CAOP//CASH\r\n:17B::DFLT//Y\r\n:16R:CASHMOVE\r\n:22H::CRDB//CRED\r\n:98A::PAYD//20150514\r\n:92A::TAXR//26,375\r\n:92F::GRSS//EUR2,9\r\n:16S:CASHMOVE\r\n:16S:CAOPTN\r\n-}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115350330, "stop": 1754115350330}, {"name": "Start coping data from sample file to another file to be used.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115350330, "stop": 1754115350330}, {"name": "Successfully wrote to the file.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115350338, "stop": 1754115350338}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115352063, "stop": 1754115352063}, {"name": "Validation message = File sent to the server for processing with id [2851]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115364257, "stop": 1754115364257}, {"name": "Alert Message = File sent to the server for processing with id [2851]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115364257, "stop": 1754115364257}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115365245, "stop": 1754115365245}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115373046, "stop": 1754115373046}, {"name": "Detection ID = 7109", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115374003, "stop": 1754115374003}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115377967, "stop": 1754115377967}, {"name": "Check if user can Release detection. ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115378794, "stop": 1754115378794}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115383064, "stop": 1754115383064}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "Operator{id=0, loginName='null', firstName='null', lastName='null', password='null', zone=null}"}], "start": 1754114863290, "stop": 1754115390133}