{"uuid": "489128a8-f4fc-480b-81fb-f4c8c0bb31ac", "name": "Safe Watch Filtering", "children": ["36323cef-c843-499d-8d7c-7fb163319fbc", "941d8d70-e686-4c72-9e5d-2f8debf7b9ae", "e3faeac4-6140-4a52-bdcf-41dfb1570370", "b7fae342-4b4d-461f-bcd4-f508c2ad3f63", "7dfa9772-06c1-4118-861f-8e7b0578e97a", "71fed831-0c35-457f-854a-0448b269e251", "60d7b361-ab64-42c2-bf8e-b7a032c29b95"], "befores": [{"name": "Setting up allure report", "status": "passed", "stage": "finished", "description": "Setting up allure report", "steps": [{"name": "Setting up allure report environment data: Edge", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105128125, "stop": 1754105128128}], "attachments": [], "parameters": [], "start": 1754105128119, "stop": 1754105128305}], "afters": [{"name": "rerunFailedTestCases", "status": "passed", "stage": "finished", "description": "", "steps": [{"name": "========================================", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390828, "stop": 1754115390828}, {"name": "RERUNNING FAILED TEST CASES", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390828, "stop": 1754115390828}, {"name": "========================================", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390828, "stop": 1754115390828}, {"name": "Found 11 failed test(s) to rerun:", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390829, "stop": 1754115390829}, {"name": "  - eastnets.screening.regression.iso20022configurations.ISO20022_TC002.iso20022_TC0012", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390829, "stop": 1754115390829}, {"name": "  - eastnets.screening.regression.iso20022configurations.ISO20022_TC003.iso20022_TC003", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390829, "stop": 1754115390829}, {"name": "  - eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC014.listManager_TC014", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390829, "stop": 1754115390829}, {"name": "  - eastnets.screening.regression.iso20022configurations.ISO20022_TC004.iso20022_TC0012", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390829, "stop": 1754115390829}, {"name": "  - eastnets.screening.regression.iso20022configurations.ISO20022_TC005.iso20022_TC0012", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390829, "stop": 1754115390829}, {"name": "  - eastnets.screening.regression.reports.Report_TC005.report_TC005", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390829, "stop": 1754115390829}, {"name": "  - eastnets.screening.regression.listmanager.prefiltertests.PreFilter_TC003.PreFilter_TC003", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390829, "stop": 1754115390829}, {"name": "  - eastnets.screening.regression.reports.Report_TC011.report_TC011", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390829, "stop": 1754115390829}, {"name": "  - eastnets.screening.regression.reports.Report_TC012.report_TC012", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390829, "stop": 1754115390829}, {"name": "  - eastnets.screening.regression.reports.Report_TC013.report_TC013", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390829, "stop": 1754115390829}, {"name": "  - eastnets.screening.regression.reports.Report_TC014.report_TC014", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390829, "stop": 1754115390829}, {"name": "", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390830, "stop": 1754115390830}, {"name": "Creating new TestNG suite for failed tests...", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390830, "stop": 1754115390830}, {"name": "Starting failed test rerun...", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390830, "stop": 1754115390830}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390857, "stop": 1754115390857}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390858, "stop": 1754115390858}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390858, "stop": 1754115390858}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390864, "stop": 1754115390864}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390864, "stop": 1754115390865}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390865, "stop": 1754115390865}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390867, "stop": 1754115390867}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390867, "stop": 1754115390867}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390867, "stop": 1754115390867}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390872, "stop": 1754115390872}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390872, "stop": 1754115390872}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390872, "stop": 1754115390872}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390875, "stop": 1754115390876}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390876, "stop": 1754115390876}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390876, "stop": 1754115390876}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390880, "stop": 1754115390880}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390880, "stop": 1754115390880}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390880, "stop": 1754115390880}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390883, "stop": 1754115390883}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390883, "stop": 1754115390883}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390883, "stop": 1754115390883}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390886, "stop": 1754115390886}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390887, "stop": 1754115390887}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390887, "stop": 1754115390887}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390887, "stop": 1754115390887}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390890, "stop": 1754115390890}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390891, "stop": 1754115390891}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390891, "stop": 1754115390891}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390893, "stop": 1754115390893}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390894, "stop": 1754115390894}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390894, "stop": 1754115390894}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390897, "stop": 1754115390897}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390897, "stop": 1754115390897}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390897, "stop": 1754115390897}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754115390897, "stop": 1754115390897}], "attachments": [], "parameters": [], "start": 1754115390828, "stop": 1754117419202}], "start": 1754105128073, "stop": 1754117419202}