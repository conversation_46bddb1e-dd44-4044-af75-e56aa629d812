{"uuid": "1291ca29-7fea-4d1c-808e-266005cefd7d", "historyId": "fb1a7c71534fc71f47eab95b765752f6", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC001.scanManager_TC001", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC001"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC001"}, {"name": "testMethod", "value": "scanManager_TC001"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC001"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is able to scan a swift rje record without creating alerts automatically option checked and take decision.", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to scan a swift rje record without creating alerts automatically option checked and take decision.", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105401888, "stop": 1754105401888}, {"name": "Validation message = File sent to the server for processing with id [2759]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105419332, "stop": 1754105419332}, {"name": "Alert Message = File sent to the server for processing with id [2759]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105419333, "stop": 1754105419333}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105420591, "stop": 1754105420591}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105432657, "stop": 1754105432657}, {"name": "Detection ID = 6805", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105434622, "stop": 1754105434622}, {"name": "Detection Status = REP", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105439337, "stop": 1754105439337}, {"name": "Detection Status = REP", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105443935, "stop": 1754105443935}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "FileScan{filePath='/RJE_Swift.txt', format='SWIFT RJE Records', encoding='null', enList=null, result='null', rank='null', detectVessels='true', detectCountries='true', createAlertsAutomatically='false'}"}], "start": 1754105401886, "stop": 1754105451186}