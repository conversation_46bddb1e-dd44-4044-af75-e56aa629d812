{"uuid": "02a6939a-7083-4227-a5e7-2ebef41419ab", "historyId": "af037fb272a0cd830df0e048908f5fb8", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC008.scanManager_TC008", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC008"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC008"}, {"name": "testMethod", "value": "scanManager_TC008"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC008"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is able to add an entity as <PERSON> Guy from the results tab for a scanned file with SWIFT RJE Records type.", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to add an entity as <PERSON> Guy from the results tab for a scanned file with SWIFT RJE Records type.", "steps": [{"name": "RJE File Content= {1:F01PTSABEMMAXXX0000000001}{2:I564ACCLFRP1XXXXN}{3:{108:ENT144037000001}}{4:\r\n:16R:GENL\r\n:20C::CORP//2015033100010560\r\n:20C::SEME//0105601900000002\r\n:23G:EntryName, EntryName\r\n:22F::CAEV//DVCA\r\n:22F::CAMV//MAND\r\n:98A::PREP//20150331\r\n:25D::PROC//COMP\r\n:16R:LINK\r\n:13A::LINK//564\r\n:20C::PREV//0105601900000001\r\n:16S:LINK\r\n:16S:GENL\r\n:16R:USECU\r\n:35B:ISIN DE0005190003\r\nBMW Bayerische Motoren Werke AG\r\n:16R:ACCTINFO\r\n:97A::SAFE//000000190003\r\n:93B::ELIG//UNIT/360,\r\n:93B::PEND//UNIT/360,\r\n:93B::PENR//UNIT/0,\r\n:93B::SETT//UNIT/360,\r\n:16S:ACCTINFO\r\n:16S:USECU\r\n:16R:CADETL\r\n:98A::XDTE//20150514\r\n:98A::RDTE//20150515\r\n:22F::DIVI//FINL\r\n:16S:CADETL\r\n:16R:CAOPTN\r\n:13A::CAON//001\r\n:22F::CAOP//CASH\r\n:17B::DFLT//Y\r\n:16R:CASHMOVE\r\n:22H::CRDB//CRED\r\n:98A::PAYD//20150514\r\n:92A::TAXR//26,375\r\n:92F::GRSS//EUR2,9\r\n:16S:CASHMOVE\r\n:16S:CAOPTN\r\n-}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105889815, "stop": 1754105889815}, {"name": "Start coping data from sample file to another file to be used.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105889815, "stop": 1754105889815}, {"name": "Successfully wrote to the file.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105889818, "stop": 1754105889818}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105889818, "stop": 1754105889818}, {"name": "Validation message = File sent to the server for processing with id [2768]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105902851, "stop": 1754105902852}, {"name": "Alert Message = File sent to the server for processing with id [2768]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105902852, "stop": 1754105902852}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105904036, "stop": 1754105904036}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105913215, "stop": 1754105913215}, {"name": "Detection ID = 6857", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105914264, "stop": 1754105914264}, {"name": "Actual validation Message = Good guy successfully created!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105917632, "stop": 1754105917633}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "FileScan{filePath='/src/test/resources/testDataFiles/fileScanTD/RJE_Swift.txt', format='SWIFT RJE Records', encoding='null', enList=null, result='null', rank='null', detectVessels='true', detectCountries='true', createAlertsAutomatically='false'}"}], "start": 1754105889565, "stop": 1754105918220}