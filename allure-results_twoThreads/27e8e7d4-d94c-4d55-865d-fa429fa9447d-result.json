{"uuid": "27e8e7d4-d94c-4d55-865d-fa429fa9447d", "historyId": "fd4a0c6c6ae00e6075fc19fe594631d3", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC014.scanManager_TC014", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC014"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC014"}, {"name": "testMethod", "value": "scanManager_TC014"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC014"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify <PERSON> with Korean letters", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify <PERSON> with Korean letters", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106361831, "stop": 1754106361831}, {"name": "Status for the scanned name = REP", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106378219, "stop": 1754106378219}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754106361829, "stop": 1754106378219}