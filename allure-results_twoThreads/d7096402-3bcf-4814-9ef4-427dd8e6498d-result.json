{"uuid": "d7096402-3bcf-4814-9ef4-427dd8e6498d", "historyId": "4d979c84bd4ce7b37b23b4bbd0bd2896", "fullName": "eastnets.admin.AdminTest.checkProfilePermissions", "labels": [{"name": "package", "value": "eastnets.admin.AdminTest"}, {"name": "testClass", "value": "eastnets.admin.AdminTest"}, {"name": "testMethod", "value": "checkProfilePermissions"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Admin Test Cases"}, {"name": "subSuite", "value": "eastnets.admin.AdminTest"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}], "links": [], "name": "Verify that user is not able to Enable a newly created Profile when 'Allow to enable profile' permission is not granted", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is not able to Enable a newly created Profile when 'Allow to enable profile' permission is not granted", "steps": [{"name": "Connect to database to remove Link between profile = Test-Profile-675595205 and Permission Allow to enable profile", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754113015025, "stop": 1754113015025}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754113015050, "stop": 1754113015050}, {"name": "SELECT P.NAME as PR<PERSON><PERSON><PERSON>_NAME , PR.PROFILE_ID as PROFILE_ID , PR.GRANT_ID as GRANT_ID , PR.GRANT_RIGHT_ID as GRANT_RIGHT_ID , PR.GRANT_TYPE_ID as GRANT_TYPE_ID   FROM tModuleFunctions M   LEFT JOIN tProfileRights PR On PR.GRANT_ID  = M.ID     LEFT Join tProfiles P ON P.id =PR.PROFILE_ID   WHERE DESCRIPTION ='Allow to enable profile' and P.NAME = 'Test-Profile-675595205'  and PR.GRANT_TYPE_ID =3", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754113015050, "stop": 1754113015050}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754113015060, "stop": 1754113015060}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754113015060, "stop": 1754113015060}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754113015061, "stop": 1754113015061}, {"name": "Delete function permission 'Allow to enable profile' from DataBase", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754113015061, "stop": 1754113015061}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754113015096, "stop": 1754113015096}, {"name": "DELETE FROM tProfileRights  WHERE GRANT_ID  ='11' and PROFILE_ID ='143' and GRANT_TYPE_ID =3", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754113015096, "stop": 1754113015096}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754113015116, "stop": 1754113015116}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754113015116, "stop": 1754113015116}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754113015117, "stop": 1754113015117}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754113015452, "stop": 1754113015452}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='selenium-user-211907537'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754113015452, "stop": 1754113015452}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754113015458, "stop": 1754113015458}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754113015458, "stop": 1754113015458}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754113015459, "stop": 1754113015459}, {"name": "Login with User Name = selenium-user-211907537 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754113015575, "stop": 1754113015576}, {"name": "Check if user can Enable a newly created Profile.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754113020869, "stop": 1754113020869}, {"name": "Click add new profile button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754113020869, "stop": 1754113020869}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754113024034, "stop": 1754113024034}, {"name": " INSERT INTO tProfileRights (PROFILE_ID, GRANT_ID, GRANT_RIGHT_ID, GRANT_TYPE_ID) VALUES (143, 11, 3, 3);", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754113024034, "stop": 1754113024034}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754113024039, "stop": 1754113024039}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754113024039, "stop": 1754113024039}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754113024039, "stop": 1754113024039}, {"name": "Operator logout.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754113024039, "stop": 1754113024039}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "Profile{id=0, name='selenium-full-right-profile ', enabled=false, writeRight=false, description='null', zone=null, profileRights=null}"}], "start": 1754113015025, "stop": 1754113024039}