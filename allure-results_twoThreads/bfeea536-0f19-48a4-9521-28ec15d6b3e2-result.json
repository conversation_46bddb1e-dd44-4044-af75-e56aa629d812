{"uuid": "bfeea536-0f19-48a4-9521-28ec15d6b3e2", "historyId": "113ad67ecf76ff03661b48a08aefe1b3", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC009.scanManager_TC009", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC009"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC009"}, {"name": "testMethod", "value": "scanManager_TC009"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC009"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is able to Print an alert report in Word format", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to Print an alert report in Word format", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106209700, "stop": 1754106209700}, {"name": "Validation message = File sent to the server for processing with id [2774]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106223588, "stop": 1754106223588}, {"name": "Alert Message = File sent to the server for processing with id [2774]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106223588, "stop": 1754106223588}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106224878, "stop": 1754106224878}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106235028, "stop": 1754106235028}, {"name": "Detection ID = 6876", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106236746, "stop": 1754106236746}, {"name": "Start Exporting Violation With Print Scope all And Document Type Word", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106236756, "stop": 1754106236756}, {"name": "Click on close dialog button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106244472, "stop": 1754106244472}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "eastnets.screening.entity.Report@72bb56ef"}], "start": 1754106209434, "stop": 1754106275242}