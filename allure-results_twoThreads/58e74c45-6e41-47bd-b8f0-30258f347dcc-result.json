{"uuid": "58e74c45-6e41-47bd-b8f0-30258f347dcc", "historyId": "39fdefff98ede705eb3c9d2af44b2414", "fullName": "eastnets.admin.AuditManagerTest.checkAuditManager", "labels": [{"name": "package", "value": "eastnets.admin.AuditManagerTest"}, {"name": "testClass", "value": "eastnets.admin.AuditManagerTest"}, {"name": "testMethod", "value": "checkAuditManager"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Admin Test Cases"}, {"name": "subSuite", "value": "eastnets.admin.AuditManagerTest"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "tag", "value": "AuditManager"}], "links": [], "name": "Verify that an event recording audit is appearing properly for the removed Groups/Users", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that an event recording audit is appearing properly for the removed Groups/Users", "steps": [{"name": "Operator test data = Operator{id=0, loginName='selenium-user-697290574', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Common_Zone_01', displayName='Common Zone 01', description='Zone for most of the tests'}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112624770, "stop": 1754112624770}, {"name": "Group test data = Group{id=0, name='selenium-random-group-851735073', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='full-right-profile', enabled=true, writeRight=true, description='Profile with full Rights', zone=Zone{id=0, name='Common_Zone_01', displayName='Common Zone 01', description='Zone for most of the tests'}, profileRights={Administration={Archive Viewer=[], Operator Manager=[ALL], Group Manager=[ALL], Profile Manager=[ALL], Report Manager=[ALL], Zone Manager=[ALL], Audit Manager=[], Event Viewer=[]}, RPTSRV={}, SWS={License Manager=[], Name Checker=[ALL], WorldCheckSettings=[ALL], Batch List Management=[], ISO20022 Format Configuration=[ALL], DowJonesSettings=[ALL], ListSetManager=[ALL], MQ Connector=[ALL], DB Scanner=[ALL], Customer Card=[ALL], Good Guys Migration=[], SAA OFCA Station=[ALL], SAA OFCS Detect=[ALL], List Manager=[ALL], SafeTrade=[ALL], en.Reporting Scanner=[ALL], SWIFT Manager=[ALL], SWP Name Checker=[ALL], File Based Archive=[ALL], Detection Manager=[ALL], Approval Configuration=[ALL], DB Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], SAA OFCS Monitor=[ALL], Archive Management=[ALL], Event Viewer=[], ISO20022-Module=[ALL], ListExplorer=[ALL], ISO20022 Schema Configuration=[ALL], MQ Manager=[ALL], Archive Viewer=[ALL], Replay=[ALL], StrippingDetectorManager=[ALL], Report Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL], GoodGuyExplorer=[ALL], SafeWatch API=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-697290574', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Common_Zone_01', displayName='Common Zone 01', description='Zone for most of the tests'}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112624779, "stop": 1754112624779}, {"name": "Create new operator.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112624779, "stop": 1754112624779}, {"name": "Operator test Data = Operator{id=0, loginName='selenium-user-697290574', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Common_Zone_01', displayName='Common Zone 01', description='Zone for most of the tests'}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112624779, "stop": 1754112624779}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112626330, "stop": 1754112626330}, {"name": "Set login name = selenium-user-697290574", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112631673, "stop": 1754112631673}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112632253, "stop": 1754112632253}, {"name": "Click Create new operator button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112632913, "stop": 1754112632913}, {"name": "Set login name = selenium-user-697290574", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112633118, "stop": 1754112633118}, {"name": "Set first name = Random", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112634708, "stop": 1754112634708}, {"name": "Set last name = User", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112635362, "stop": 1754112635362}, {"name": "Set password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112636001, "stop": 1754112636001}, {"name": "Set confirm password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112636618, "stop": 1754112636618}, {"name": "Set enable flag = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112637363, "stop": 1754112637363}, {"name": "Set reset flag = false", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112638308, "stop": 1754112638308}, {"name": "Select zone  = Common Zone 01", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112639605, "stop": 1754112639605}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112645475, "stop": 1754112645475}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112645733, "stop": 1754112645733}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112652759, "stop": 1754112652759}, {"name": "Set login name = selenium-user-697290574", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112658197, "stop": 1754112658197}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112658884, "stop": 1754112658884}, {"name": "Create first group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112659589, "stop": 1754112659589}, {"name": "Group test Data = Group{id=0, name='selenium-random-group-851735073', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='full-right-profile', enabled=true, writeRight=true, description='Profile with full Rights', zone=Zone{id=0, name='Common_Zone_01', displayName='Common Zone 01', description='Zone for most of the tests'}, profileRights={Administration={Archive Viewer=[], Operator Manager=[ALL], Group Manager=[ALL], Profile Manager=[ALL], Report Manager=[ALL], Zone Manager=[ALL], Audit Manager=[], Event Viewer=[]}, RPTSRV={}, SWS={License Manager=[], Name Checker=[ALL], WorldCheckSettings=[ALL], Batch List Management=[], ISO20022 Format Configuration=[ALL], DowJonesSettings=[ALL], ListSetManager=[ALL], MQ Connector=[ALL], DB Scanner=[ALL], Customer Card=[ALL], Good Guys Migration=[], SAA OFCA Station=[ALL], SAA OFCS Detect=[ALL], List Manager=[ALL], SafeTrade=[ALL], en.Reporting Scanner=[ALL], SWIFT Manager=[ALL], SWP Name Checker=[ALL], File Based Archive=[ALL], Detection Manager=[ALL], Approval Configuration=[ALL], DB Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], SAA OFCS Monitor=[ALL], Archive Management=[ALL], Event Viewer=[], ISO20022-Module=[ALL], ListExplorer=[ALL], ISO20022 Schema Configuration=[ALL], MQ Manager=[ALL], Archive Viewer=[ALL], Replay=[ALL], StrippingDetectorManager=[ALL], Report Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL], GoodGuyExplorer=[ALL], SafeWatch API=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-697290574', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Common_Zone_01', displayName='Common Zone 01', description='Zone for most of the tests'}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112659590, "stop": 1754112659590}, {"name": "Remove operator with login name = selenium-user-697290574.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112694811, "stop": 1754112694811}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112696042, "stop": 1754112696042}, {"name": "Set login name = selenium-user-697290574", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112701375, "stop": 1754112701375}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112702090, "stop": 1754112702090}, {"name": "Select operator and click remove button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112702652, "stop": 1754112702652}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112704089, "stop": 1754112704089}, {"name": "Set login name = selenium-user-697290574", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112709464, "stop": 1754112709464}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112710241, "stop": 1754112710241}, {"name": "Remove group with name = selenium-random-group-851735073.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112710798, "stop": 1754112710798}, {"name": "Select group and click remove button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112719165, "stop": 1754112719165}, {"name": "Asserting on data in result view", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112795896, "stop": 1754112795896}, {"name": "<PERSON><PERSON><PERSON> Passed Successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754112795896, "stop": 1754112795896}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754112624761, "stop": 1754112795896}