{"uuid": "ce4c4b87-4bfe-47fc-9c9f-f65d5679c0d0", "historyId": "42eb3c2d0f8cf06d96aec17e5620434", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC004.ScanManager_TC004", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC004"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC004"}, {"name": "testMethod", "value": "ScanManager_TC004"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC004"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Validate that user is able to Print a detection created from 'File Scan' from Result Tab.", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Validate that user is able to Print a detection created from 'File Scan' from Result Tab.", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105674413, "stop": 1754105674413}, {"name": "Validation message = File sent to the server for processing with id [2764]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105687185, "stop": 1754105687185}, {"name": "Alert Message = File sent to the server for processing with id [2764]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105687185, "stop": 1754105687185}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105688131, "stop": 1754105688131}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105696102, "stop": 1754105696102}, {"name": "Detection ID = 6847", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105697297, "stop": 1754105697297}, {"name": "response.getStatus()200", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105729177, "stop": 1754105729177}, {"name": "UTF-8", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105729177, "stop": 1754105729177}, {"name": "1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105729178, "stop": 1754105729178}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754105674395, "stop": 1754105729178}