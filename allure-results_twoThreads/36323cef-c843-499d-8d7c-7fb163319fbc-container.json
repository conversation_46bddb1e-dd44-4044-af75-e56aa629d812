{"uuid": "36323cef-c843-499d-8d7c-7fb163319fbc", "name": "Scan Manager Test Cases", "children": ["1291ca29-7fea-4d1c-808e-266005cefd7d", "e1df4558-66e2-4297-bc5b-3edc3fe4885e", "db4575b7-54d7-414d-837b-b1693a58ad7d", "f6754de5-529d-4414-acdd-f8011407f18e", "ce4c4b87-4bfe-47fc-9c9f-f65d5679c0d0", "0a60f234-ad3d-4205-ab35-3d6a7549b246", "d411c9ed-7eb6-4571-916f-f29e2895a8dd", "05c762b6-557b-4f84-a275-4641c665e1b2", "02a6939a-7083-4227-a5e7-2ebef41419ab", "a5a047e0-ee30-4f33-adc8-630099be0e88", "c3e803af-6fd0-4071-b04b-7bb2507faf78", "bb315d45-2cef-4141-a27e-3c3ec36ae43d", "*************-4206-be6d-00e1a6bd07ac", "bfeea536-0f19-48a4-9521-28ec15d6b3e2", "5dbeef3a-65ae-4758-9408-fab559df897f", "81487b25-b266-4457-a9f2-df8ed1db01b7", "27e8e7d4-d94c-4d55-865d-fa429fa9447d"], "befores": [{"name": "Setting up selenium web driver before each class run", "status": "passed", "stage": "finished", "description": "Setting up selenium web driver before each class run", "steps": [{"name": "Initialize Selenium driver before tests' Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105128618, "stop": 1754105128618}, {"name": "Selenium webDriver was initialized successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754105134606, "stop": 1754105134606}], "attachments": [], "parameters": [], "start": 1754105128618, "stop": 1754105134607}], "afters": [{"name": "Quitting selenium driver after each class run", "status": "passed", "stage": "finished", "description": "Quitting selenium driver after each class run", "steps": [{"name": "Closing selenium WebDriver after Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106380680, "stop": 1754106380680}], "attachments": [], "parameters": [], "start": 1754106380680, "stop": 1754106381106}], "start": 1754105128336, "stop": 1754106381106}