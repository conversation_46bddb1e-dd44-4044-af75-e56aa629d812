{"uuid": "a1255062-5ee8-4518-a2b9-e13912dcd056", "historyId": "72db89ba58be9b5a9d42cef14f92e1b1", "fullName": "eastnets.screening.regression.detectionmanager.DetectionManager_TC027.detectionManager_TC027", "labels": [{"name": "package", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC027"}, {"name": "testClass", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC027"}, {"name": "testMethod", "value": "detectionManager_TC027"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Detection Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC027"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-1(36)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Detection Manager"}], "links": [], "name": "Filtering  - Verify that columns display properly correct value in 'DetectionManagerReport'", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "* Filtering  - Verify that 'SLA ID' column display properly correct value in 'DetectionManagerReport'\n* Filtering  - Verify that 'UETR' column display properly correct value in 'DetectionManagerReport'\n* Filtering - Verify that 'SLA ID' and 'UETR' display properly with correct values in 'DetectionManagerReport' ", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114387890, "stop": 1754114387890}, {"name": "update tConfig set variable_value = 'Yes' where variable_name = 'Change detection status to DontKnow'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114387890, "stop": 1754114387890}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114387897, "stop": 1754114387897}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114387897, "stop": 1754114387897}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114387897, "stop": 1754114387897}, {"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-130607455', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@78c73705, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-130607455', officialDate='null', entry=[ListEntry{type='null', name='EntryName-130607455', firstName='EntryFirstName-130607455', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-130607455', firstName='EntryFirstName-130607455', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114387901, "stop": 1754114387901}, {"name": "Connect to Database and Check if User Profile = full-right-profile_05 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114387902, "stop": 1754114387902}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114387902, "stop": 1754114387902}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114387955, "stop": 1754114387955}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_05' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114387955, "stop": 1754114387955}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114387958, "stop": 1754114387958}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114387958, "stop": 1754114387958}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114387958, "stop": 1754114387958}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114387978, "stop": 1754114387978}, {"name": "Delete From tListSetProfile where profile_id in (8)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114387978, "stop": 1754114387978}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114387982, "stop": 1754114387982}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114387982, "stop": 1754114387982}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114387982, "stop": 1754114387982}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114387982, "stop": 1754114387982}, {"name": "Search for list by listName = ListName-130607455 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114407808, "stop": 1754114407808}, {"name": "Set zone : Common Zone 05", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114417155, "stop": 1754114417155}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114419268, "stop": 1754114419268}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114419685, "stop": 1754114419685}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114420048, "stop": 1754114420048}, {"name": "Set template name = templateName-130607455", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114420292, "stop": 1754114420292}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114422365, "stop": 1754114422365}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114423076, "stop": 1754114423076}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114424296, "stop": 1754114424296}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114424965, "stop": 1754114424965}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114493571, "stop": 1754114493571}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114493571, "stop": 1754114493571}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114575378, "stop": 1754114575378}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114575378, "stop": 1754114575378}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114575378, "stop": 1754114575378}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114575421, "stop": 1754114575421}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_05'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114575421, "stop": 1754114575421}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114575426, "stop": 1754114575426}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114575426, "stop": 1754114575426}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114575426, "stop": 1754114575426}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114597494, "stop": 1754114597494}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114603591, "stop": 1754114603591}, {"name": "Validation message = File sent to the server for processing with id [2849]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114621794, "stop": 1754114621794}, {"name": "Alert Message = File sent to the server for processing with id [2849]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114621794, "stop": 1754114621794}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114622340, "stop": 1754114622340}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114631466, "stop": 1754114631466}, {"name": "Detection ID = 7061", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114632773, "stop": 1754114632773}, {"name": "Status detection = Reported", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114633012, "stop": 1754114633012}, {"name": "Detection ID = 7061", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114634050, "stop": 1754114634050}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114637936, "stop": 1754114637936}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114649258, "stop": 1754114649258}, {"name": "File Content :General Information\r\nDetection Date\r\nApplication\r\nUser Name\r\nHost Name\r\n2025/08/02 09:03:44\r\nFile Scanner\r\noperator-05\r\n10.12.35.193\r\nDetection Ticket\r\nDetection Status\r\n7061\r\nNew\r\nFile Attached 0\r\n:\r\n:\r\n:\r\n:\r\n:\r\n:\r\n:\r\n:\r\nScanned Data\r\nData {1:F01ABNAMXMMXXX0008741881}{2:I103ABNAMXMMXXXN}{3:{108:EN10393\r\n5}{111:001}{121:8E0FE365-A88E-426B-8484-4FB7FEE92742}}{4:\r\n:20:Osama bin laden\r\n:23B:CRED\r\n:32A:180516USD90,\r\n:33B:USD1000,\r\n:50A:ZYGKBEB0\r\n:59A:/8900683465\r\nZYGTBEB0XXX\r\n:71A:BEN\r\n:71F:JOD25,\r\n-}\r\n:\r\nActive Black Lists\r\nList Set Name\r\nList Set Owner\r\nLast Modification Date\r\nListSetName-130607455\r\noperator-05\r\n2025/08/02 09:02:51\r\nActivated On 2025/08/02 09:02:51\r\nName Official Date Last Modified Date\r\nListName-130607455 2025/08/02\r\n00:00:00\r\n2025/08/02\r\n09:00:07\r\n:\r\n:\r\n:\r\n:\r\nViolations Details\r\nEntity reported in violation # 1\r\nOfficial Name : Osama bin laden\r\nMatched text : Osama bin laden\r\nStatus : Reported\r\nRank : 100\r\nDetection Summary\r\nReported Violations\r\nAccepted Violations\r\n1\r\n0\r\nOut Of Context Violations 0\r\nStatus Rank Matched Name Black List NameID\r\nReported 100 Osama bin laden ListName-1306074551\r\n:\r\n:\r\n:\r\n 2Page 1 ofSaturday, 2 Aug 2025 9.04 AM\r\nIndividual:Category\r\nListed On : ListName-130607455 (Sat Aug 02 00:00:00 AST 2025)\r\nEntity ID : 106783\r\nPosition : 0 to 14\r\nField : 20 (Line 1)\r\nAlert Ticket ID : 2668\r\nAlert Assigned To : operator-05\r\n[2025/08/02 09:03:44] Alert creation:Alert History\r\n 2Page 2 ofSaturday, 2 Aug 2025 9.04 AM\r\n", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114684370, "stop": 1754114684370}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754114387862, "stop": 1754114684370}