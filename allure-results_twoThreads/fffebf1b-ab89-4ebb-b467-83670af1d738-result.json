{"uuid": "fffebf1b-ab89-4ebb-b467-83670af1d738", "historyId": "86f73464a660dbba809ea3142050759a", "fullName": "eastnets.screening.regression.detectionmanager.DetectionManager_TC014.detectionManager_TC014", "labels": [{"name": "package", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC014"}, {"name": "testClass", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC014"}, {"name": "testMethod", "value": "detectionManager_TC014"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Detection Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC014"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-1(36)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Detection Manager"}], "links": [], "name": "Verify that user is able to perform a Pending action on a detection created from 'File Scan' with 'Custom Format' format from the 'Detections Section'Verify that user is able to Assign a detection created from 'File Scan' with 'Custom Format' format to User/Group from the 'Detections Section'", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to perform a Pending action on a detection created from 'File Scan' with 'Custom Format' format from the 'Detections Section'Verify that user is able to Assign a detection created from 'File Scan' with 'Custom Format' format to User/Group from the 'Detections Section'", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615223, "stop": 1754111615223}, {"name": "update tConfig set variable_value = 'Yes' where variable_name = 'Change detection status to DontKnow'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615223, "stop": 1754111615223}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615227, "stop": 1754111615227}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615227, "stop": 1754111615227}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615229, "stop": 1754111615229}, {"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-210593715', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@21e11625, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-210593715', officialDate='null', entry=[ListEntry{type='null', name='EntryName-210593715', firstName='EntryFirstName-210593715', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-210593715', firstName='EntryFirstName-210593715', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615231, "stop": 1754111615231}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615231, "stop": 1754111615231}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615255, "stop": 1754111615255}, {"name": "select id from tZones tz where tz.DISPLAY_NAME  = 'Common Zone 05'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615255, "stop": 1754111615255}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615257, "stop": 1754111615257}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615257, "stop": 1754111615257}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615257, "stop": 1754111615257}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615279, "stop": 1754111615279}, {"name": "DELETE  FROM tSetContent   WHERE id IN ( SELECT tsc.id FROM tSetContent tsc  LEFT JOIN tBlackListVersions tblv on tblv.id =tsc.black_list_id  LEFT JOIN tListSets tls on tls.id = tsc.list_set_id  LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id  WHERE tbl.zone_id ='7'  AND tbl.display_name ='UN Democratic People Republic of Korea')", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615279, "stop": 1754111615279}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615300, "stop": 1754111615300}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615300, "stop": 1754111615300}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615300, "stop": 1754111615300}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615300, "stop": 1754111615300}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615334, "stop": 1754111615334}, {"name": "DELETE  FROM tGoodGuys  WHERE id IN ( SELECT tgg.id  FROM tGoodGuys tgg LEFT JOIN tBlackListVersions tblv on tblv.id =tgg.black_list_id LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id WHERE tbl.display_name ='UN Democratic People Republic of Korea')", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615334, "stop": 1754111615334}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615348, "stop": 1754111615348}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615348, "stop": 1754111615348}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615349, "stop": 1754111615349}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615349, "stop": 1754111615349}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615375, "stop": 1754111615375}, {"name": "DELETE FROM tBlackLists WHERE display_name ='UN Democratic People Republic of Korea'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615375, "stop": 1754111615375}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615518, "stop": 1754111615518}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615518, "stop": 1754111615518}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615518, "stop": 1754111615518}, {"name": "Connect to Database and Check if User Profile = full-right-profile_05 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615519, "stop": 1754111615519}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615519, "stop": 1754111615519}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615562, "stop": 1754111615562}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_05' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615562, "stop": 1754111615562}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615564, "stop": 1754111615564}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615565, "stop": 1754111615565}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615565, "stop": 1754111615565}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615591, "stop": 1754111615591}, {"name": "Delete From tListSetProfile where profile_id in (8)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615591, "stop": 1754111615591}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615596, "stop": 1754111615596}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615596, "stop": 1754111615596}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615596, "stop": 1754111615596}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615596, "stop": 1754111615596}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615596, "stop": 1754111615596}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615620, "stop": 1754111615620}, {"name": "select id from tZones tz where tz.DISPLAY_NAME  = 'Common Zone 05'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615620, "stop": 1754111615620}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615622, "stop": 1754111615622}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615622, "stop": 1754111615622}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615623, "stop": 1754111615623}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615645, "stop": 1754111615645}, {"name": "DELETE  FROM tSetContent   WHERE id IN ( SELECT tsc.id FROM tSetContent tsc  LEFT JOIN tBlackListVersions tblv on tblv.id =tsc.black_list_id  LEFT JOIN tListSets tls on tls.id = tsc.list_set_id  LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id  WHERE tbl.zone_id ='7'  AND tbl.display_name ='UN Democratic People Republic of Korea')", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615645, "stop": 1754111615645}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615647, "stop": 1754111615647}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615647, "stop": 1754111615647}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615648, "stop": 1754111615648}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615648, "stop": 1754111615648}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615669, "stop": 1754111615669}, {"name": "DELETE  FROM tGoodGuys  WHERE id IN ( SELECT tgg.id  FROM tGoodGuys tgg LEFT JOIN tBlackListVersions tblv on tblv.id =tgg.black_list_id LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id WHERE tbl.display_name ='UN Democratic People Republic of Korea')", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615669, "stop": 1754111615669}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615671, "stop": 1754111615671}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615671, "stop": 1754111615671}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615672, "stop": 1754111615672}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615672, "stop": 1754111615672}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615692, "stop": 1754111615692}, {"name": "DELETE FROM tBlackLists WHERE display_name ='UN Democratic People Republic of Korea'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615693, "stop": 1754111615693}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615699, "stop": 1754111615699}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615699, "stop": 1754111615699}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111615699, "stop": 1754111615699}, {"name": "Set zone : Common Zone 05", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111633189, "stop": 1754111633189}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111635279, "stop": 1754111635279}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111635910, "stop": 1754111635910}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111636375, "stop": 1754111636375}, {"name": "Set template name = templateName-210593715", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111636640, "stop": 1754111636640}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111638977, "stop": 1754111638977}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111639867, "stop": 1754111639867}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111641233, "stop": 1754111641233}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111642132, "stop": 1754111642132}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111717161, "stop": 1754111717161}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111717161, "stop": 1754111717161}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111785472, "stop": 1754111785472}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111785473, "stop": 1754111785473}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111785473, "stop": 1754111785473}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111785499, "stop": 1754111785499}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_05'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111785499, "stop": 1754111785499}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111785502, "stop": 1754111785502}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111785502, "stop": 1754111785502}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111785502, "stop": 1754111785502}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111820064, "stop": 1754111820064}, {"name": "Validation message = File sent to the server for processing with id [2829]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111836827, "stop": 1754111836827}, {"name": "Alert Message = File sent to the server for processing with id [2829]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111836827, "stop": 1754111836827}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111838174, "stop": 1754111838174}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111847732, "stop": 1754111847732}, {"name": "Detection ID = 6963", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111849120, "stop": 1754111849120}, {"name": "Status detection = Reported", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111849365, "stop": 1754111849365}, {"name": "Detection ID = 6963", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111850444, "stop": 1754111850444}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111855769, "stop": 1754111855769}, {"name": "Validation message = 1 detection(s) successfully modified!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111862680, "stop": 1754111862680}, {"name": "Detection Status = REPINV", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754111866666, "stop": 1754111866666}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754111615196, "stop": 1754111871671}