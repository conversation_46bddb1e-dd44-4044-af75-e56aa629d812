{"uuid": "0deafc7e-b1bb-4a26-b01f-685f95925365", "historyId": "a20ae7af3f706bb95bc580330942e265", "fullName": "eastnets.screening.regression.reports.Report_TC005.report_TC005", "labels": [{"name": "package", "value": "eastnets.screening.regression.reports.Report_TC005"}, {"name": "testClass", "value": "eastnets.screening.regression.reports.Report_TC005"}, {"name": "testMethod", "value": "report_TC005"}, {"name": "parentSuite", "value": "Failed Tests Rerun Suite"}, {"name": "suite", "value": "<PERSON><PERSON> Failed Tests"}, {"name": "subSuite", "value": "eastnets.screening.regression.reports.Report_TC005"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "tag", "value": "Reports"}], "links": [], "name": "Scenario Verify Alerts Grouped By List Set Report", "status": "failed", "statusDetails": {"known": false, "muted": false, "flaky": false, "message": "Test Failed due to an exception occurred in POM's method expected [Done] but found [Failed]", "trace": "java.lang.AssertionError: Test Failed due to an exception occurred in POM's method expected [Done] but found [Failed]\r\n\tat org.testng.Assert.fail(Assert.java:110)\r\n\tat org.testng.Assert.failNotEquals(Assert.java:1413)\r\n\tat org.testng.Assert.assertEqualsImpl(Assert.java:149)\r\n\tat org.testng.Assert.assertEquals(Assert.java:131)\r\n\tat org.testng.Assert.assertEquals(Assert.java:655)\r\n\tat eastnets.screening.regression.reports.Report_TC005.report_TC005(Report_TC005.java:125)\r\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:578)\r\n\tat org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:677)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:221)\r\n\tat org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:50)\r\n\tat org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:969)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:194)\r\n\tat org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148)\r\n\tat org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128)\r\n\tat java.base/java.util.ArrayList.forEach(ArrayList.java:1511)\r\n\tat org.testng.TestRunner.privateRun(TestRunner.java:829)\r\n\tat org.testng.TestRunner.run(TestRunner.java:602)\r\n\tat org.testng.SuiteRunner.runTest(SuiteRunner.java:437)\r\n\tat org.testng.SuiteRunner.runSequentially(SuiteRunner.java:431)\r\n\tat org.testng.SuiteRunner.privateRun(SuiteRunner.java:391)\r\n\tat org.testng.SuiteRunner.run(SuiteRunner.java:330)\r\n\tat org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)\r\n\tat org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)\r\n\tat org.testng.TestNG.runSuitesSequentially(TestNG.java:1256)\r\n\tat org.testng.TestNG.runSuitesLocally(TestNG.java:1176)\r\n\tat org.testng.TestNG.runSuites(TestNG.java:1099)\r\n\tat org.testng.TestNG.run(TestNG.java:1067)\r\n\tat core.BaseTest.rerunFailedTestCases(BaseTest.java:380)\r\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:578)\r\n\tat org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)\r\n\tat org.testng.internal.invokers.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:69)\r\n\tat org.testng.internal.invokers.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:361)\r\n\tat org.testng.internal.invokers.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:296)\r\n\tat org.testng.SuiteRunner.privateRun(SuiteRunner.java:404)\r\n\tat org.testng.SuiteRunner.run(SuiteRunner.java:330)\r\n\tat org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)\r\n\tat org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)\r\n\tat org.testng.TestNG.runSuitesSequentially(TestNG.java:1256)\r\n\tat org.testng.TestNG.runSuitesLocally(TestNG.java:1176)\r\n\tat org.testng.TestNG.runSuites(TestNG.java:1099)\r\n\tat org.testng.TestNG.run(TestNG.java:1067)\r\n\tat com.intellij.rt.testng.IDEARemoteTestNG.run(IDEARemoteTestNG.java:65)\r\n\tat com.intellij.rt.testng.RemoteTestNGStarter.main(RemoteTestNGStarter.java:105)\r\n"}, "stage": "finished", "description": "Verify Alerts Grouped By List Set Report", "steps": [{"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-544179324', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@13cdd1a1, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-544179324', officialDate='null', entry=[ListEntry{type='null', name='EntryName-544179324', firstName='EntryFirstName-544179324', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-544179324', firstName='EntryFirstName-544179324', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116653774, "stop": 1754116653774}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116653774, "stop": 1754116653774}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116653797, "stop": 1754116653797}, {"name": "select id from tZones tz where tz.DISPLAY_NAME  = 'Common Zone 08'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116653797, "stop": 1754116653797}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116653798, "stop": 1754116653798}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116653798, "stop": 1754116653798}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116653799, "stop": 1754116653799}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116653824, "stop": 1754116653824}, {"name": "DELETE  FROM tSetContent   WHERE id IN ( SELECT tsc.id FROM tSetContent tsc  LEFT JOIN tBlackListVersions tblv on tblv.id =tsc.black_list_id  LEFT JOIN tListSets tls on tls.id = tsc.list_set_id  LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id  WHERE tbl.zone_id ='10'  AND tbl.display_name ='UN Democratic People Republic of Korea')", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116653824, "stop": 1754116653824}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116653838, "stop": 1754116653838}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116653838, "stop": 1754116653838}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116653838, "stop": 1754116653838}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116653838, "stop": 1754116653838}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116653861, "stop": 1754116653861}, {"name": "DELETE  FROM tGoodGuys  WHERE id IN ( SELECT tgg.id  FROM tGoodGuys tgg LEFT JOIN tBlackListVersions tblv on tblv.id =tgg.black_list_id LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id WHERE tbl.display_name ='UN Democratic People Republic of Korea')", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116653861, "stop": 1754116653861}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116653877, "stop": 1754116653877}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116653877, "stop": 1754116653877}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116653877, "stop": 1754116653877}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116653877, "stop": 1754116653877}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116653907, "stop": 1754116653907}, {"name": "DELETE FROM tBlackLists WHERE display_name ='UN Democratic People Republic of Korea'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116653907, "stop": 1754116653907}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654142, "stop": 1754116654142}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654142, "stop": 1754116654142}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654142, "stop": 1754116654142}, {"name": "Connect to Database and Check if User Profile = full-right-profile_08 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654143, "stop": 1754116654143}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654144, "stop": 1754116654144}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654191, "stop": 1754116654192}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_08' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654192, "stop": 1754116654192}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654193, "stop": 1754116654193}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654193, "stop": 1754116654193}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654194, "stop": 1754116654194}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654216, "stop": 1754116654216}, {"name": "Delete From tListSetProfile where profile_id in (11)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654216, "stop": 1754116654216}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654220, "stop": 1754116654220}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654220, "stop": 1754116654220}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654220, "stop": 1754116654220}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654220, "stop": 1754116654220}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654220, "stop": 1754116654220}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654241, "stop": 1754116654241}, {"name": "select id from tZones tz where tz.DISPLAY_NAME  = 'Common Zone 08'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654241, "stop": 1754116654241}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654242, "stop": 1754116654242}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654242, "stop": 1754116654242}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654242, "stop": 1754116654242}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654263, "stop": 1754116654263}, {"name": "DELETE  FROM tSetContent   WHERE id IN ( SELECT tsc.id FROM tSetContent tsc  LEFT JOIN tBlackListVersions tblv on tblv.id =tsc.black_list_id  LEFT JOIN tListSets tls on tls.id = tsc.list_set_id  LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id  WHERE tbl.zone_id ='10'  AND tbl.display_name ='UN Democratic People Republic of Korea')", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654263, "stop": 1754116654263}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654266, "stop": 1754116654266}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654266, "stop": 1754116654266}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654266, "stop": 1754116654266}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654267, "stop": 1754116654267}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654289, "stop": 1754116654289}, {"name": "DELETE  FROM tGoodGuys  WHERE id IN ( SELECT tgg.id  FROM tGoodGuys tgg LEFT JOIN tBlackListVersions tblv on tblv.id =tgg.black_list_id LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id WHERE tbl.display_name ='UN Democratic People Republic of Korea')", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654289, "stop": 1754116654289}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654291, "stop": 1754116654291}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654291, "stop": 1754116654291}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654291, "stop": 1754116654291}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654291, "stop": 1754116654291}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654312, "stop": 1754116654312}, {"name": "DELETE FROM tBlackLists WHERE display_name ='UN Democratic People Republic of Korea'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654312, "stop": 1754116654312}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654316, "stop": 1754116654316}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654316, "stop": 1754116654316}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116654316, "stop": 1754116654316}, {"name": "Set zone : Common Zone 08", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116667988, "stop": 1754116667988}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116669749, "stop": 1754116669749}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116670157, "stop": 1754116670157}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116670431, "stop": 1754116670431}, {"name": "Set template name = templateName-544179324", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116670613, "stop": 1754116670613}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116672327, "stop": 1754116672327}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116672965, "stop": 1754116672965}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116673836, "stop": 1754116673836}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116674402, "stop": 1754116674402}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116706546, "stop": 1754116706546}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116706546, "stop": 1754116706546}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116761626, "stop": 1754116761626}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116761626, "stop": 1754116761626}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116761626, "stop": 1754116761626}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116761661, "stop": 1754116761661}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_08'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116761661, "stop": 1754116761661}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116761663, "stop": 1754116761663}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116761663, "stop": 1754116761663}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116761664, "stop": 1754116761664}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116762236, "stop": 1754116762236}, {"name": "Validation message = File sent to the server for processing with id [2854]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116770819, "stop": 1754116770819}, {"name": "Alert Message = File sent to the server for processing with id [2854]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116770819, "stop": 1754116770819}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116772001, "stop": 1754116772001}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116779937, "stop": 1754116779937}, {"name": "Detection ID = 7148", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116781378, "stop": 1754116781378}, {"name": "Status detection = Reported", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116782008, "stop": 1754116782008}, {"name": "Detection ID = 7148", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116783375, "stop": 1754116783375}, {"name": "Report status is: Failed", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754116817992, "stop": 1754116817992}], "attachments": [], "parameters": [], "start": 1754116653773, "stop": 1754116818240}