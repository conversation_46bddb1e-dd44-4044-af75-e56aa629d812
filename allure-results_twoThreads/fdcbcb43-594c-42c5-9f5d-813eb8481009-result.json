{"uuid": "fdcbcb43-594c-42c5-9f5d-813eb8481009", "historyId": "e9d44a66fb796025c18dc7707e21fe3c", "fullName": "eastnets.screening.regression.detectionmanager.DetectionManager_TC026.detectionManager_TC026", "labels": [{"name": "package", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC026"}, {"name": "testClass", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC026"}, {"name": "testMethod", "value": "detectionManager_TC026"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Detection Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC026"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-1(36)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Detection Manager"}], "links": [], "name": "Detection Manager - Verify that Detections return from RJE msg without and with GPI labels return when selecting 'both' option from  drop down list\n Verify that the detection was found in detection manager", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that the detection was found in detection manager\n* Filtering - Verify that 'SLA ID' column showing the value for '111'  field in RJE msgs in 'Detections' table\n* Filtering - Verify that 'UETR' column showing the value for '121'  field in RJE msgs in 'Detections' table\n* Filtering - Detection Manager - Verify that only Detections return from RJE msgs without gpi lables return when selecting 'RJE' option from 'Filter By' drop down list\n* Filtering - Detection Manager - Verify that only Detections return from msgs with gpi lables return when selecting 'gpi' option from 'Filter By' drop down list\n* Filtering - Verify that Search working properly when searching for RJE msgs using 'SLA ID' and  'UETR' values\n* Filtering - Detection Manager - Verify that new 'UETR' checkboxe dispalys in 'Column Panel'\n* Filtering - Detection Manager - Verify that new 'SLA ID' checkboxe dispalys in 'Column Panel'", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114013747, "stop": 1754114013747}, {"name": "update tConfig set variable_value = 'Yes' where variable_name = 'Change detection status to DontKnow'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114013747, "stop": 1754114013747}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114013750, "stop": 1754114013750}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114013750, "stop": 1754114013750}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114013750, "stop": 1754114013750}, {"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-590414929', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@6de84f13, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-590414929', officialDate='null', entry=[ListEntry{type='null', name='EntryName-590414929', firstName='EntryFirstName-590414929', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-590414929', firstName='EntryFirstName-590414929', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114013752, "stop": 1754114013752}, {"name": "Connect to Database and Check if User Profile = full-right-profile_05 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114013752, "stop": 1754114013752}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114013752, "stop": 1754114013752}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114013813, "stop": 1754114013813}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_05' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114013813, "stop": 1754114013813}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114013816, "stop": 1754114013816}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114013816, "stop": 1754114013816}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114013817, "stop": 1754114013817}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114013850, "stop": 1754114013850}, {"name": "Delete From tListSetProfile where profile_id in (8)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114013850, "stop": 1754114013850}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114013854, "stop": 1754114013854}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114013854, "stop": 1754114013854}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114013854, "stop": 1754114013854}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114013854, "stop": 1754114013854}, {"name": "Search for list by listName = ListName-590414929 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114034588, "stop": 1754114034588}, {"name": "Set zone : Common Zone 05", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114043289, "stop": 1754114043289}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114045964, "stop": 1754114045964}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114046487, "stop": 1754114046487}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114046923, "stop": 1754114046923}, {"name": "Set template name = templateName-590414929", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114047131, "stop": 1754114047131}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114049298, "stop": 1754114049298}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114050165, "stop": 1754114050165}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114051164, "stop": 1754114051164}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114051894, "stop": 1754114051894}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114130502, "stop": 1754114130502}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114130502, "stop": 1754114130502}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114201800, "stop": 1754114201800}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114201801, "stop": 1754114201801}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114201801, "stop": 1754114201801}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114201841, "stop": 1754114201841}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_05'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114201841, "stop": 1754114201841}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114201844, "stop": 1754114201844}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114201844, "stop": 1754114201844}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114201845, "stop": 1754114201845}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114219996, "stop": 1754114219996}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114227505, "stop": 1754114227506}, {"name": "Validation message = File sent to the server for processing with id [2847]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114245235, "stop": 1754114245235}, {"name": "Alert Message = File sent to the server for processing with id [2847]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114245235, "stop": 1754114245235}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114246549, "stop": 1754114246549}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114255660, "stop": 1754114255660}, {"name": "Detection ID = 7059", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114257079, "stop": 1754114257079}, {"name": "Status detection = Reported", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114257374, "stop": 1754114257374}, {"name": "Detection ID = 7059", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114258788, "stop": 1754114258788}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114258788, "stop": 1754114258788}, {"name": "Validation message = File sent to the server for processing with id [2848]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114275053, "stop": 1754114275053}, {"name": "Alert Message = File sent to the server for processing with id [2848]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114275053, "stop": 1754114275053}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114275932, "stop": 1754114275932}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114283989, "stop": 1754114283989}, {"name": "Detection ID = 7060", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114285080, "stop": 1754114285080}, {"name": "Status detection = Reported", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114285319, "stop": 1754114285319}, {"name": "Detection ID = 7060", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114286536, "stop": 1754114286536}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114306133, "stop": 1754114306133}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754114013712, "stop": 1754114379370}