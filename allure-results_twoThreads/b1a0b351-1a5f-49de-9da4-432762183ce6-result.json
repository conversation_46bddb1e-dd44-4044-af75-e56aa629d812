{"uuid": "b1a0b351-1a5f-49de-9da4-432762183ce6", "historyId": "50c043c877a193e3e85843d4bfb97047", "fullName": "eastnets.screening.regression.detectionmanager.DetectionManager_TC010.detectionManager_TC010", "labels": [{"name": "package", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC010"}, {"name": "testClass", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC010"}, {"name": "testMethod", "value": "detectionManager_TC010"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Detection Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC010"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-1(36)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Detection Manager"}], "links": [], "name": "detectionManager_TC010", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754109007934, "stop": 1754109007934}