{"uuid": "708a6d0c-78ff-4983-aff3-720c9157f4e7", "historyId": "c1c14de74bb43bbf5f8d10a287df71e", "fullName": "eastnets.admin.AdminTest.checkPermissionWhenAssignSecGroupProfileZone", "labels": [{"name": "package", "value": "eastnets.admin.AdminTest"}, {"name": "testClass", "value": "eastnets.admin.AdminTest"}, {"name": "testMethod", "value": "checkPermissionWhenAssignSecGroupProfileZone"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Admin Test Cases"}, {"name": "subSuite", "value": "eastnets.admin.AdminTest"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}], "links": [], "name": "Verify that the user that is assigned to Group, Profile, and Zone have permission to Release but don't have the permission to Block is able to block when this user is assigned to a second Group, Profile and Zone that have the permission to Block.", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that the user that is assigned to Group, Profile, and Zone have permission to Release but don't have the permission to Block is able to block when this user is assigned to a second Group, Profile and Zone that have the permission to Block.", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114254010, "stop": 1754114254010}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='sysadmin'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114254010, "stop": 1754114254010}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114254017, "stop": 1754114254017}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114254018, "stop": 1754114254018}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114254018, "stop": 1754114254018}, {"name": "Login with User Name = sysadmin and Password = manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114254162, "stop": 1754114254162}, {"name": "Operator test data = Operator{id=0, loginName='selenium-user-92408015', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone739634261', displayName='Zone (739634261)', description='Zone created with random number '739634261''}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114259034, "stop": 1754114259034}, {"name": "Group test data = Group{id=0, name='selenium-random-group-562760102', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-965634149', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone739634261', displayName='Zone (739634261)', description='Zone created with random number '739634261''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-92408015', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone739634261', displayName='Zone (739634261)', description='Zone created with random number '739634261''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114259034, "stop": 1754114259034}, {"name": "Create new zone .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114259034, "stop": 1754114259034}, {"name": "Zone test Data = Zone{id=0, name='Zone739634261', displayName='Zone (739634261)', description='Zone created with random number '739634261''}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114259034, "stop": 1754114259034}, {"name": "Check if zone with name = 'Zone739634261' exists", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114260709, "stop": 1754114260709}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114260937, "stop": 1754114260937}, {"name": "Enter name =Zone739634261", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114266328, "stop": 1754114266328}, {"name": "Enter display Name =Zone (739634261)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114266981, "stop": 1754114266981}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114267554, "stop": 1754114267554}, {"name": "Zone doesn't exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114268063, "stop": 1754114268063}, {"name": "Click add new zone button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114268063, "stop": 1754114268063}, {"name": "Set create new zone data.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114268496, "stop": 1754114268496}, {"name": "Set name = Zone739634261", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114268496, "stop": 1754114268496}, {"name": "Set display name = Zone (739634261)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114269656, "stop": 1754114269656}, {"name": "Set description = Zone created with random number '739634261'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114270632, "stop": 1754114270632}, {"name": "Capture zone id from UI = 130", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114272029, "stop": 1754114272029}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114272029, "stop": 1754114272029}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114273208, "stop": 1754114273208}, {"name": "Enter name =Zone739634261", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114278549, "stop": 1754114278549}, {"name": "Enter display Name =Zone (739634261)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114279255, "stop": 1754114279255}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114279883, "stop": 1754114279883}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114282183, "stop": 1754114282183}, {"name": "Enter name =Zone739634261", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114287530, "stop": 1754114287530}, {"name": "Enter display Name =Zone (739634261)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114288159, "stop": 1754114288159}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114288855, "stop": 1754114288855}, {"name": "Create first profile .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114289467, "stop": 1754114289467}, {"name": "Profile test Data = Profile{id=0, name='Test-Profile-965634149', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone739634261', displayName='Zone (739634261)', description='Zone created with random number '739634261''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114289467, "stop": 1754114289467}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114290692, "stop": 1754114290692}, {"name": "Set name = Test-Profile-965634149 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114296030, "stop": 1754114296030}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114296810, "stop": 1754114296810}, {"name": "Click add new profile button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114297459, "stop": 1754114297459}, {"name": "Set name = Test-Profile-965634149 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114297717, "stop": 1754114297717}, {"name": "Set description = Random profile for Selenium testing ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114299035, "stop": 1754114299035}, {"name": "Check enable checkbox to be true ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114299844, "stop": 1754114299844}, {"name": "Check write right checkbox to be Test-Profile-965634149 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114300611, "stop": 1754114300611}, {"name": "Select zone = Test-Profile-965634149 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114300904, "stop": 1754114300904}, {"name": "Add SWS item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114307380, "stop": 1754114307380}, {"name": "Click on the SWS item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114307380, "stop": 1754114307380}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114307814, "stop": 1754114307814}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114308836, "stop": 1754114308836}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114309094, "stop": 1754114309094}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114309159, "stop": 1754114309159}, {"name": "Set name = Test-Profile-965634149 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114315212, "stop": 1754114315212}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114315887, "stop": 1754114315887}, {"name": "Select profile from result view.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114316709, "stop": 1754114316709}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114317255, "stop": 1754114317255}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114318362, "stop": 1754114318362}, {"name": "Add en.Reporting Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114318849, "stop": 1754114318849}, {"name": "Click on the en.Reporting Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114318849, "stop": 1754114318849}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114319200, "stop": 1754114319200}, {"name": "Add SWIFT Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114320602, "stop": 1754114320602}, {"name": "Click on the SWIFT Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114320602, "stop": 1754114320602}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114321011, "stop": 1754114321011}, {"name": "Add SWP Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114322390, "stop": 1754114322390}, {"name": "Click on the SWP Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114322390, "stop": 1754114322390}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114322872, "stop": 1754114322872}, {"name": "Add Detection Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114324371, "stop": 1754114324371}, {"name": "Click on the Detection Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114324372, "stop": 1754114324372}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114325009, "stop": 1754114325009}, {"name": "Add Scan Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114326454, "stop": 1754114326454}, {"name": "Click on the Scan Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114326454, "stop": 1754114326454}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114326796, "stop": 1754114326796}, {"name": "Add Format Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114328266, "stop": 1754114328266}, {"name": "Click on the Format Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114328266, "stop": 1754114328266}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114328654, "stop": 1754114328654}, {"name": "Add Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114330088, "stop": 1754114330088}, {"name": "Click on the Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114330088, "stop": 1754114330088}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114330508, "stop": 1754114330508}, {"name": "Add Batch List Management item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114332036, "stop": 1754114332036}, {"name": "Click on the Batch List Management item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114332036, "stop": 1754114332036}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114332539, "stop": 1754114332539}, {"name": "Add ListExplorer item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114333930, "stop": 1754114333930}, {"name": "Click on the ListExplorer item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114333930, "stop": 1754114333930}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114334281, "stop": 1754114334281}, {"name": "Add ListSetManager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114335722, "stop": 1754114335722}, {"name": "Click on the ListSetManager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114335722, "stop": 1754114335722}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114336187, "stop": 1754114336187}, {"name": "Add List Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114337662, "stop": 1754114337662}, {"name": "Click on the List Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114337662, "stop": 1754114337662}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114338099, "stop": 1754114338099}, {"name": "Add File Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114339555, "stop": 1754114339555}, {"name": "Click on the File Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114339555, "stop": 1754114339555}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114339954, "stop": 1754114339954}, {"name": "Add Advanced Settings item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114341332, "stop": 1754114341332}, {"name": "Click on the Advanced Settings item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114341332, "stop": 1754114341332}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114341967, "stop": 1754114341967}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114343426, "stop": 1754114343426}, {"name": "Accept <PERSON><PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114343989, "stop": 1754114343989}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114345631, "stop": 1754114345631}, {"name": "Select Module Rights.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114346373, "stop": 1754114346373}, {"name": "Processing module 'SWIFT Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114348377, "stop": 1754114348377}, {"name": "Click on SWIFT Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114348377, "stop": 1754114348377}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114349353, "stop": 1754114349353}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114349602, "stop": 1754114349602}, {"name": "Processing module 'Detection Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114352835, "stop": 1754114352835}, {"name": "Click on Detection Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114352835, "stop": 1754114352835}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114353796, "stop": 1754114353796}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114354135, "stop": 1754114354135}, {"name": "Processing module 'Scan Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114356599, "stop": 1754114356599}, {"name": "Click on Scan Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114356599, "stop": 1754114356599}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114357506, "stop": 1754114357506}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114357755, "stop": 1754114357755}, {"name": "Processing module 'Format Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114359869, "stop": 1754114359869}, {"name": "Click on Format Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114359869, "stop": 1754114359869}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114360965, "stop": 1754114360965}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114361272, "stop": 1754114361272}, {"name": "Processing module 'Name Checker'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114363666, "stop": 1754114363666}, {"name": "Click on Name Checker", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114363666, "stop": 1754114363666}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114364665, "stop": 1754114364665}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114364999, "stop": 1754114364999}, {"name": "Processing module 'ListExplorer'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114368231, "stop": 1754114368231}, {"name": "Click on ListExplorer", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114368231, "stop": 1754114368231}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114369216, "stop": 1754114369216}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114369443, "stop": 1754114369443}, {"name": "Processing module 'ListSetManager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114371723, "stop": 1754114371723}, {"name": "Click on ListSetManager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114371723, "stop": 1754114371723}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114372810, "stop": 1754114372810}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114373042, "stop": 1754114373042}, {"name": "Processing module 'List Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114375343, "stop": 1754114375343}, {"name": "Click on List Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114375343, "stop": 1754114375343}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114376238, "stop": 1754114376238}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114376508, "stop": 1754114376508}, {"name": "Processing module 'File Scanner'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114378704, "stop": 1754114378704}, {"name": "Click on File Scanner", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114378704, "stop": 1754114378704}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114379767, "stop": 1754114379767}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114380058, "stop": 1754114380058}, {"name": "Processing module 'Advanced Settings'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114382281, "stop": 1754114382281}, {"name": "Click on Advanced Settings", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114382281, "stop": 1754114382281}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114383151, "stop": 1754114383151}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114383429, "stop": 1754114383429}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114384792, "stop": 1754114384792}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114385265, "stop": 1754114385265}, {"name": "Check if profile exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114387418, "stop": 1754114387418}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114387418, "stop": 1754114387418}, {"name": "Set name = Test-Profile-965634149 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114393202, "stop": 1754114393202}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114393984, "stop": 1754114393984}, {"name": "Profile exist = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114394931, "stop": 1754114394931}, {"name": "Create new operator.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114394931, "stop": 1754114394931}, {"name": "Operator test Data = Operator{id=0, loginName='selenium-user-92408015', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone739634261', displayName='Zone (739634261)', description='Zone created with random number '739634261''}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114394931, "stop": 1754114394931}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114396957, "stop": 1754114396957}, {"name": "Set login name = selenium-user-92408015", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114402447, "stop": 1754114402447}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114403248, "stop": 1754114403248}, {"name": "Click Create new operator button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114404048, "stop": 1754114404048}, {"name": "Set login name = selenium-user-92408015", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114404325, "stop": 1754114404325}, {"name": "Set first name = Random", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114405880, "stop": 1754114405880}, {"name": "Set last name = User", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114406689, "stop": 1754114406689}, {"name": "Set password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114407499, "stop": 1754114407499}, {"name": "Set confirm password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114408297, "stop": 1754114408297}, {"name": "Set enable flag = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114409238, "stop": 1754114409238}, {"name": "Set reset flag = false", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114410303, "stop": 1754114410303}, {"name": "Select zone  = Zone (739634261)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114411991, "stop": 1754114411991}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114417759, "stop": 1754114417759}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114418021, "stop": 1754114418021}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114425104, "stop": 1754114425104}, {"name": "Set login name = selenium-user-92408015", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114430501, "stop": 1754114430501}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114431127, "stop": 1754114431127}, {"name": "Create first group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114431956, "stop": 1754114431956}, {"name": "Group test Data = Group{id=0, name='selenium-random-group-562760102', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-965634149', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone739634261', displayName='Zone (739634261)', description='Zone created with random number '739634261''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-92408015', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone739634261', displayName='Zone (739634261)', description='Zone created with random number '739634261''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114431956, "stop": 1754114431956}, {"name": "Connect to database to remove Link between profile = Test-Profile-965634149 and Permission Allow to change detection status to Real Violation(Block detection)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114465369, "stop": 1754114465369}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114465396, "stop": 1754114465396}, {"name": "SELECT P.NAME as PRO<PERSON><PERSON>_NAME , PR.PROFILE_ID as PROFILE_ID , PR.GRANT_ID as GRANT_ID , PR.GRANT_RIGHT_ID as GRANT_RIGHT_ID , PR.GRANT_TYPE_ID as GRANT_TYPE_ID   FROM tModuleFunctions M   LEFT JOIN tProfileRights PR On PR.GRANT_ID  = M.ID     LEFT Join tProfiles P ON P.id =PR.PROFILE_ID   WHERE DESCRIPTION ='Allow to change detection status to Real Violation(Block detection)' and P.NAME = 'Test-Profile-965634149'  and PR.GRANT_TYPE_ID =3", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114465396, "stop": 1754114465396}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114465407, "stop": 1754114465407}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114465407, "stop": 1754114465407}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114465408, "stop": 1754114465408}, {"name": "Delete function permission 'Allow to change detection status to Real Violation(Block detection)' from DataBase", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114465408, "stop": 1754114465408}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114465434, "stop": 1754114465434}, {"name": "DELETE FROM tProfileRights  WHERE GRANT_ID  ='130' and PROFILE_ID ='148' and GRANT_TYPE_ID =3", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114465435, "stop": 1754114465435}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114465453, "stop": 1754114465453}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114465453, "stop": 1754114465453}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114465454, "stop": 1754114465454}, {"name": "Create second zone .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114465455, "stop": 1754114465455}, {"name": "Zone test Data = Zone{id=0, name='Zone430141244', displayName='Zone (430141244)', description='Zone created with random number '430141244''}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114465455, "stop": 1754114465455}, {"name": "Check if zone with name = 'Zone430141244' exists", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114467472, "stop": 1754114467472}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114467703, "stop": 1754114467703}, {"name": "Enter name =Zone430141244", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114473117, "stop": 1754114473117}, {"name": "Enter display Name =Zone (430141244)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114473985, "stop": 1754114473985}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114475300, "stop": 1754114475300}, {"name": "Zone doesn't exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114476526, "stop": 1754114476526}, {"name": "Click add new zone button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114476526, "stop": 1754114476526}, {"name": "Set create new zone data.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114477626, "stop": 1754114477626}, {"name": "Set name = Zone430141244", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114477626, "stop": 1754114477626}, {"name": "Set display name = Zone (430141244)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114479169, "stop": 1754114479169}, {"name": "Set description = Zone created with random number '430141244'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114480095, "stop": 1754114480095}, {"name": "Capture zone id from UI = 131", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114481432, "stop": 1754114481432}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114481432, "stop": 1754114481432}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114482745, "stop": 1754114482745}, {"name": "Enter name =Zone430141244", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114488059, "stop": 1754114488059}, {"name": "Enter display Name =Zone (430141244)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114488862, "stop": 1754114488862}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114489533, "stop": 1754114489533}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114492301, "stop": 1754114492301}, {"name": "Enter name =Zone430141244", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114497809, "stop": 1754114497809}, {"name": "Enter display Name =Zone (430141244)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114498508, "stop": 1754114498508}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114499207, "stop": 1754114499207}, {"name": "Create second profile .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114499783, "stop": 1754114499783}, {"name": "Profile test Data = Profile{id=0, name='Test-Profile-760898737', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone430141244', displayName='Zone (430141244)', description='Zone created with random number '430141244''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114499784, "stop": 1754114499784}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114500992, "stop": 1754114500992}, {"name": "Set name = Test-Profile-760898737 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114506334, "stop": 1754114506334}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114507020, "stop": 1754114507020}, {"name": "Click add new profile button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114507751, "stop": 1754114507751}, {"name": "Set name = Test-Profile-760898737 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114507980, "stop": 1754114507980}, {"name": "Set description = Random profile for Selenium testing ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114509458, "stop": 1754114509458}, {"name": "Check enable checkbox to be true ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114510401, "stop": 1754114510401}, {"name": "Check write right checkbox to be Test-Profile-760898737 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114511273, "stop": 1754114511273}, {"name": "Select zone = Test-Profile-760898737 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114511609, "stop": 1754114511609}, {"name": "Add SWS item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114533306, "stop": 1754114533306}, {"name": "Click on the SWS item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114533306, "stop": 1754114533306}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114533861, "stop": 1754114533861}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114534927, "stop": 1754114534927}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114535638, "stop": 1754114535638}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114535729, "stop": 1754114535729}, {"name": "Set name = Test-Profile-760898737 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114541953, "stop": 1754114541953}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114542913, "stop": 1754114542913}, {"name": "Select profile from result view.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114543762, "stop": 1754114543762}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114544658, "stop": 1754114544658}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114546126, "stop": 1754114546126}, {"name": "Add en.Reporting Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114546785, "stop": 1754114546785}, {"name": "Click on the en.Reporting Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114546785, "stop": 1754114546785}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114547384, "stop": 1754114547384}, {"name": "Add SWIFT Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114548807, "stop": 1754114548807}, {"name": "Click on the SWIFT Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114548807, "stop": 1754114548807}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114549286, "stop": 1754114549286}, {"name": "Add SWP Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114550766, "stop": 1754114550766}, {"name": "Click on the SWP Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114550766, "stop": 1754114550766}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114551198, "stop": 1754114551198}, {"name": "Add Detection Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114552638, "stop": 1754114552638}, {"name": "Click on the Detection Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114552638, "stop": 1754114552638}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114553054, "stop": 1754114553054}, {"name": "Add Scan Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114554542, "stop": 1754114554542}, {"name": "Click on the Scan Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114554542, "stop": 1754114554542}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114555098, "stop": 1754114555098}, {"name": "Add Format Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114556532, "stop": 1754114556532}, {"name": "Click on the Format Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114556532, "stop": 1754114556532}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114557066, "stop": 1754114557066}, {"name": "Add Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114558514, "stop": 1754114558514}, {"name": "Click on the Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114558514, "stop": 1754114558514}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114558985, "stop": 1754114558985}, {"name": "Add Batch List Management item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114560385, "stop": 1754114560385}, {"name": "Click on the Batch List Management item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114560385, "stop": 1754114560385}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114560889, "stop": 1754114560889}, {"name": "Add ListExplorer item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114562394, "stop": 1754114562394}, {"name": "Click on the ListExplorer item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114562394, "stop": 1754114562394}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114563004, "stop": 1754114563004}, {"name": "Add ListSetManager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114564547, "stop": 1754114564547}, {"name": "Click on the ListSetManager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114564547, "stop": 1754114564547}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114565155, "stop": 1754114565155}, {"name": "Add List Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114566557, "stop": 1754114566557}, {"name": "Click on the List Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114566557, "stop": 1754114566557}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114566962, "stop": 1754114566962}, {"name": "Add File Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114568438, "stop": 1754114568438}, {"name": "Click on the File Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114568438, "stop": 1754114568438}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114568890, "stop": 1754114568890}, {"name": "Add Advanced Settings item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114570304, "stop": 1754114570304}, {"name": "Click on the Advanced Settings item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114570304, "stop": 1754114570304}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114570720, "stop": 1754114570720}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114572475, "stop": 1754114572475}, {"name": "Accept <PERSON><PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114573320, "stop": 1754114573320}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114575131, "stop": 1754114575131}, {"name": "Select Module Rights.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114575964, "stop": 1754114575964}, {"name": "Processing module 'SWIFT Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114577966, "stop": 1754114577966}, {"name": "Click on SWIFT Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114577966, "stop": 1754114577966}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114578989, "stop": 1754114578989}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114579309, "stop": 1754114579309}, {"name": "Processing module 'Detection Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114582707, "stop": 1754114582707}, {"name": "Click on Detection Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114582707, "stop": 1754114582707}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114583924, "stop": 1754114583924}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114584215, "stop": 1754114584215}, {"name": "Processing module 'Scan Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114586762, "stop": 1754114586762}, {"name": "Click on Scan Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114586762, "stop": 1754114586762}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114587979, "stop": 1754114587979}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114588232, "stop": 1754114588232}, {"name": "Processing module 'Format Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114590566, "stop": 1754114590566}, {"name": "Click on Format Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114590567, "stop": 1754114590567}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114591910, "stop": 1754114591910}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114592165, "stop": 1754114592165}, {"name": "Processing module 'Name Checker'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114594390, "stop": 1754114594390}, {"name": "Click on Name Checker", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114594390, "stop": 1754114594390}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114595626, "stop": 1754114595626}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114595901, "stop": 1754114595901}, {"name": "Processing module 'ListExplorer'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114599634, "stop": 1754114599634}, {"name": "Click on ListExplorer", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114599634, "stop": 1754114599634}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114600631, "stop": 1754114600631}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114601029, "stop": 1754114601029}, {"name": "Processing module 'ListSetManager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114603677, "stop": 1754114603677}, {"name": "Click on ListSetManager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114603677, "stop": 1754114603677}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114604843, "stop": 1754114604843}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114605535, "stop": 1754114605535}, {"name": "Processing module 'List Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114608206, "stop": 1754114608206}, {"name": "Click on List Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114608206, "stop": 1754114608206}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114609123, "stop": 1754114609123}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114609463, "stop": 1754114609463}, {"name": "Processing module 'File Scanner'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114611750, "stop": 1754114611750}, {"name": "Click on File Scanner", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114611750, "stop": 1754114611750}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114612706, "stop": 1754114612706}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114613003, "stop": 1754114613003}, {"name": "Processing module 'Advanced Settings'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114615343, "stop": 1754114615343}, {"name": "Click on Advanced Settings", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114615343, "stop": 1754114615343}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114616286, "stop": 1754114616286}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114616645, "stop": 1754114616645}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114618017, "stop": 1754114618017}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114618332, "stop": 1754114618332}, {"name": "Check if profile exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114619993, "stop": 1754114619993}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114619993, "stop": 1754114619993}, {"name": "Set name = Test-Profile-760898737 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114625919, "stop": 1754114625919}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114626778, "stop": 1754114626778}, {"name": "Profile exist = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114627558, "stop": 1754114627558}, {"name": "Create second group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114627558, "stop": 1754114627558}, {"name": "Group test Data = Group{id=0, name='selenium-random-group-195040243', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-760898737', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone430141244', displayName='Zone (430141244)', description='Zone created with random number '430141244''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-92408015', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone739634261', displayName='Zone (739634261)', description='Zone created with random number '739634261''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114627559, "stop": 1754114627559}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114666646, "stop": 1754114666646}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='selenium-user-92408015'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114666646, "stop": 1754114666646}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114666653, "stop": 1754114666653}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114666653, "stop": 1754114666653}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114666654, "stop": 1754114666654}, {"name": "Login with User Name = selenium-user-92408015 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114666784, "stop": 1754114666784}, {"name": "Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-160254695', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@5442cfbb, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-160254695', officialDate='null', entry=[ListEntry{type='null', name='EntryName-160254695', firstName='EntryFirstName-160254695', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-160254695', firstName='EntryFirstName-160254695', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114671394, "stop": 1754114671394}, {"name": "Connect to Database and Check if User Profile = Test-Profile-760898737 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114671401, "stop": 1754114671401}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114671401, "stop": 1754114671401}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114671450, "stop": 1754114671450}, {"name": "SELECT ID FROM tProfiles Where NAME ='Test-Profile-760898737' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114671450, "stop": 1754114671450}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114671452, "stop": 1754114671452}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114671452, "stop": 1754114671452}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114671453, "stop": 1754114671453}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114671474, "stop": 1754114671474}, {"name": "Delete From tListSetProfile where profile_id in (149)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114671474, "stop": 1754114671474}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114671476, "stop": 1754114671476}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114671476, "stop": 1754114671476}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114671477, "stop": 1754114671477}, {"name": "Number of effected rows = 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114671477, "stop": 1754114671477}, {"name": "Search for list by listName = ListName-160254695 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114691687, "stop": 1754114691687}, {"name": "Set zone : Zone (430141244)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114700914, "stop": 1754114700914}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114703417, "stop": 1754114703417}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114704181, "stop": 1754114704181}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114704589, "stop": 1754114704589}, {"name": "Set template name = templateName-160254695", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114704913, "stop": 1754114704913}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114707196, "stop": 1754114707196}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114707933, "stop": 1754114707933}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114709099, "stop": 1754114709099}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114709789, "stop": 1754114709789}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114729094, "stop": 1754114729094}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114729094, "stop": 1754114729094}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114774906, "stop": 1754114774906}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114774908, "stop": 1754114774908}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114774909, "stop": 1754114774909}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114774943, "stop": 1754114774943}, {"name": "SELECT ID FROM tProfiles Where NAME ='Test-Profile-760898737'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114774943, "stop": 1754114774943}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114774945, "stop": 1754114774945}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114774945, "stop": 1754114774945}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114774945, "stop": 1754114774945}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114792356, "stop": 1754114792356}, {"name": "RJE File Content= {1:F01PTSABEMMAXXX0000000001}{2:I564ACCLFRP1XXXXN}{3:{108:ENT144037000001}}{4:\r\n:16R:GENL\r\n:20C::CORP//2015033100010560\r\n:20C::SEME//0105601900000002\r\n:23G:EntryName-160254695, EntryFirstName-160254695\r\n:22F::CAEV//DVCA\r\n:22F::CAMV//MAND\r\n:98A::PREP//20150331\r\n:25D::PROC//COMP\r\n:16R:LINK\r\n:13A::LINK//564\r\n:20C::PREV//0105601900000001\r\n:16S:LINK\r\n:16S:GENL\r\n:16R:USECU\r\n:35B:ISIN DE0005190003\r\nBMW Bayerische Motoren Werke AG\r\n:16R:ACCTINFO\r\n:97A::SAFE//000000190003\r\n:93B::ELIG//UNIT/360,\r\n:93B::PEND//UNIT/360,\r\n:93B::PENR//UNIT/0,\r\n:93B::SETT//UNIT/360,\r\n:16S:ACCTINFO\r\n:16S:USECU\r\n:16R:CADETL\r\n:98A::XDTE//20150514\r\n:98A::RDTE//20150515\r\n:22F::DIVI//FINL\r\n:16S:CADETL\r\n:16R:CAOPTN\r\n:13A::CAON//001\r\n:22F::CAOP//CASH\r\n:17B::DFLT//Y\r\n:16R:CASHMOVE\r\n:22H::CRDB//CRED\r\n:98A::PAYD//20150514\r\n:92A::TAXR//26,375\r\n:92F::GRSS//EUR2,9\r\n:16S:CASHMOVE\r\n:16S:CAOPTN\r\n-}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114800773, "stop": 1754114800773}, {"name": "Start coping data from sample file to another file to be used.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114800773, "stop": 1754114800773}, {"name": "Successfully wrote to the file.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114800784, "stop": 1754114800784}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114802867, "stop": 1754114802867}, {"name": "Validation message = File sent to the server for processing with id [2850]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114832355, "stop": 1754114832355}, {"name": "Alert Message = File sent to the server for processing with id [2850]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114832355, "stop": 1754114832355}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114832880, "stop": 1754114832880}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114842523, "stop": 1754114842523}, {"name": "Detection ID = 7066", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114843660, "stop": 1754114843660}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114849087, "stop": 1754114849087}, {"name": "Check if user can block detection. ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114850168, "stop": 1754114850168}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754114855087, "stop": 1754114855087}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "Operator{id=0, loginName='null', firstName='null', lastName='null', password='null', zone=null}"}], "start": 1754114253544, "stop": 1754114863093}