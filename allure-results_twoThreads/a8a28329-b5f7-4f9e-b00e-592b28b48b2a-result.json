{"uuid": "a8a28329-b5f7-4f9e-b00e-592b28b48b2a", "historyId": "d9ee509f3decdb6f6bd976c2a06692b5", "fullName": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC013.listManager_TC013", "labels": [{"name": "package", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC013"}, {"name": "testClass", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC013"}, {"name": "testMethod", "value": "listManager_TC013"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "List Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC013"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-1(36)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "List Manager"}], "links": [], "name": "Verify that user is able to Export Entries from a black list as XML", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to Export Entries from a black list as XML", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106566949, "stop": 1754106566949}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-02'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106566949, "stop": 1754106566949}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106566954, "stop": 1754106566954}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106566954, "stop": 1754106566954}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106566954, "stop": 1754106566954}, {"name": "Login with User Name = operator-02 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106567167, "stop": 1754106567167}, {"name": "response.getStatus()200", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106617147, "stop": 1754106617147}, {"name": "UTF-8", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106617147, "stop": 1754106617147}, {"name": "4", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754106617147, "stop": 1754106617147}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "XML"}], "start": 1754106566468, "stop": 1754106620363}