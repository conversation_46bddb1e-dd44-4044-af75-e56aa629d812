@echo off
setlocal enabledelayedexpansion

echo ========================================
echo SELENIUM 3-THREAD EXECUTION TRACKER
echo ========================================
echo Starting comprehensive monitoring...
echo.

:: Create log directory
if not exist "debug_logs" mkdir debug_logs

:: Set log files
set MAIN_LOG=debug_logs\main_execution_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%.log
set DOCKER_LOG=debug_logs\docker_monitoring_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%.log
set GRID_LOG=debug_logs\grid_status_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%.log

:: Clean log file names (remove spaces)
set MAIN_LOG=%MAIN_LOG: =0%
set DOCKER_LOG=%DOCKER_LOG: =0%
set GRID_LOG=%GRID_LOG: =0%

echo Main log: %MAIN_LOG%
echo Docker log: %DOCKER_LOG%
echo Grid log: %GRID_LOG%
echo.

:: Start background monitoring
echo Starting background monitoring processes...
start /B cmd /c call :MONITOR_DOCKER
start /B cmd /c call :MONITOR_GRID
start /B cmd /c call :MONITOR_SYSTEM

echo.
echo ========================================
echo STARTING TEST EXECUTION
echo ========================================
echo.

:: Record start time
echo [%date% %time%] TEST EXECUTION STARTED >> %MAIN_LOG%

:: Check initial state
echo === PRE-TEST SYSTEM STATE === >> %MAIN_LOG%
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" >> %MAIN_LOG%
echo. >> %MAIN_LOG%

:: Run the tests
echo Running tests with 3 threads...
echo [%date% %time%] Executing: mvn test -Dsurefire.suiteXmlFiles=regressionWithoutRestartServices.xml >> %MAIN_LOG%

mvn test -Dsurefire.suiteXmlFiles=regressionWithoutRestartServices.xml

:: Record end time
echo [%date% %time%] TEST EXECUTION COMPLETED >> %MAIN_LOG%

:: Wait a bit for final monitoring data
timeout /t 10 /nobreak >nul

echo.
echo ========================================
echo TEST EXECUTION COMPLETED
echo ========================================
echo.
echo Check the following log files for detailed analysis:
echo - Main execution log: %MAIN_LOG%
echo - Docker monitoring: %DOCKER_LOG%
echo - Grid status log: %GRID_LOG%
echo.

:: Generate summary report
call :GENERATE_SUMMARY

goto :EOF

:MONITOR_DOCKER
:DOCKER_LOOP
echo [%date% %time%] === DOCKER MONITORING === >> %DOCKER_LOG%
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}\t{{.NetIO}}\t{{.BlockIO}}" >> %DOCKER_LOG%
echo. >> %DOCKER_LOG%

:: Check for container restarts
docker ps -a --format "table {{.Names}}\t{{.Status}}\t{{.RunningFor}}" >> %DOCKER_LOG%
echo. >> %DOCKER_LOG%

:: Check container logs for errors
echo --- HUB ERRORS --- >> %DOCKER_LOG%
docker logs selenium-hub --tail 3 2>&1 | findstr /i "error\|exception\|timeout\|memory" >> %DOCKER_LOG%
echo --- NODE ERRORS --- >> %DOCKER_LOG%
docker logs selenium-node-edge --tail 3 2>&1 | findstr /i "error\|exception\|timeout\|memory\|crash" >> %DOCKER_LOG%
echo. >> %DOCKER_LOG%

timeout /t 5 /nobreak >nul
goto DOCKER_LOOP

:MONITOR_GRID
:GRID_LOOP
echo [%date% %time%] === GRID STATUS === >> %GRID_LOG%

:: Check hub status
curl -s http://localhost:4444/status 2>nul >> %GRID_LOG%
echo. >> %GRID_LOG%

:: Check active sessions
echo --- ACTIVE SESSIONS --- >> %GRID_LOG%
curl -s http://localhost:4444/grid/api/sessions 2>nul >> %GRID_LOG%
echo. >> %GRID_LOG%

:: Check grid configuration
echo --- GRID CONFIG --- >> %GRID_LOG%
curl -s http://localhost:4444/grid/api/hub 2>nul | findstr /i "maxSession\|newSessionRequestCount\|slotCounts" >> %GRID_LOG%
echo. >> %GRID_LOG%

timeout /t 10 /nobreak >nul
goto GRID_LOOP

:MONITOR_SYSTEM
:SYSTEM_LOOP
echo [%date% %time%] === SYSTEM MONITORING === >> %MAIN_LOG%

:: Memory usage
echo --- MEMORY USAGE --- >> %MAIN_LOG%
wmic OS get TotalVisibleMemorySize,FreePhysicalMemory /format:list | findstr "=" >> %MAIN_LOG%

:: Network connections
echo --- NETWORK CONNECTIONS --- >> %MAIN_LOG%
netstat -an | findstr ":4444" | findstr "ESTABLISHED" >> %MAIN_LOG%
set /a CONNECTION_COUNT=0
for /f %%i in ('netstat -an ^| findstr ":4444" ^| findstr "ESTABLISHED" ^| find /c "ESTABLISHED"') do set CONNECTION_COUNT=%%i
echo Total connections to 4444: !CONNECTION_COUNT! >> %MAIN_LOG%

:: Java processes
echo --- JAVA PROCESSES --- >> %MAIN_LOG%
wmic process where "name='java.exe'" get ProcessId,PageFileUsage,WorkingSetSize /format:list | findstr "=" >> %MAIN_LOG%
echo. >> %MAIN_LOG%

timeout /t 15 /nobreak >nul
goto SYSTEM_LOOP

:GENERATE_SUMMARY
echo.
echo ========================================
echo GENERATING ANALYSIS SUMMARY
echo ========================================

set SUMMARY_FILE=debug_logs\execution_summary_%date:~-4,4%%date:~-10,2%%date:~-7,2%_%time:~0,2%%time:~3,2%%time:~6,2%.txt
set SUMMARY_FILE=%SUMMARY_FILE: =0%

echo SELENIUM 3-THREAD EXECUTION ANALYSIS SUMMARY > %SUMMARY_FILE%
echo Generated: %date% %time% >> %SUMMARY_FILE%
echo ================================================ >> %SUMMARY_FILE%
echo. >> %SUMMARY_FILE%

echo === TEST EXECUTION TIMELINE === >> %SUMMARY_FILE%
findstr "TEST EXECUTION" %MAIN_LOG% >> %SUMMARY_FILE%
echo. >> %SUMMARY_FILE%

echo === DOCKER CONTAINER ISSUES === >> %SUMMARY_FILE%
findstr /i "error\|exception\|restart\|exit" %DOCKER_LOG% >> %SUMMARY_FILE%
echo. >> %SUMMARY_FILE%

echo === GRID SESSION ISSUES === >> %SUMMARY_FILE%
findstr /i "error\|timeout\|failed" %GRID_LOG% >> %SUMMARY_FILE%
echo. >> %SUMMARY_FILE%

echo === NETWORK CONNECTION PEAKS === >> %SUMMARY_FILE%
findstr "Total connections" %MAIN_LOG% >> %SUMMARY_FILE%
echo. >> %SUMMARY_FILE%

echo === MEMORY USAGE PATTERNS === >> %SUMMARY_FILE%
findstr "FreePhysicalMemory" %MAIN_LOG% | findstr /v "wmic" >> %SUMMARY_FILE%
echo. >> %SUMMARY_FILE%

echo Summary report generated: %SUMMARY_FILE%
echo.

goto :EOF
