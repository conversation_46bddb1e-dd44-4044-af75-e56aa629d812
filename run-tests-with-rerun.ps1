# PowerShell script for running tests with automatic rerun
Write-Host "========================================" -ForegroundColor Green
Write-Host "Running Test Suite with Auto-Rerun" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

Write-Host ""
Write-Host "[1/3] Running initial test suite..." -ForegroundColor Yellow
& mvn clean test

Write-Host ""
Write-Host "[2/3] Checking for failed tests..." -ForegroundColor Yellow

$failedTestsFile = "target\surefire-reports\testng-failed.xml"

if (Test-Path $failedTestsFile) {
    Write-Host "Found failed tests! Rerunning them..." -ForegroundColor Red
    Write-Host ""
    Write-Host "[3/3] Rerunning failed tests..." -ForegroundColor Yellow
    & mvn test -Dsurefire.suiteXmlFiles=$failedTestsFile
    
    Write-Host ""
    Write-Host "Checking results after rerun..." -ForegroundColor Yellow
    
    if (Test-Path $failedTestsFile) {
        Write-Host "Some tests still failed after rerun." -ForegroundColor Red
        Write-Host "Check $failedTestsFile for details." -ForegroundColor Red
    } else {
        Write-Host "All previously failed tests now pass!" -ForegroundColor Green
    }
} else {
    Write-Host "All tests passed on first run! No rerun needed." -ForegroundColor Green
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Test execution completed!" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Keep window open
Read-Host "Press Enter to continue..."
