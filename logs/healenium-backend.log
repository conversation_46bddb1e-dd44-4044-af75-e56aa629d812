2025-08-03 22:42:42.633  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:42:42.652 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:42.653 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:42:42.661 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:42:42.661097361, enableHealing=true) 
2025-08-03 22:42:42.844 DEBUG 1 - [7878-exec-8] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:42.847 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:42.849 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:42.854 DEBUG 1 - [7878-exec-8] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:42.906  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:tab_new_list_explorer\:name) 
2025-08-03 22:42:42.907 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:tab_new_list_explorer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:42:42.908 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 5a94ee85b6c00ab0c6a2aa5f396c9e16 
2025-08-03 22:42:42.913 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=5a94ee85b6c00ab0c6a2aa5f396c9e16, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:tab_new_list_explorer\:name, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:42:42.913636817, enableHealing=false) 
2025-08-03 22:42:43.103 DEBUG 1 - [7878-exec-3] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:43.103 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:43.104 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:43.108 DEBUG 1 - [7878-exec-3] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:43.224  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:42:43.225 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:43.225 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:42:43.232 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:42:43.232321063, enableHealing=true) 
2025-08-03 22:42:43.382 DEBUG 1 - [7878-exec-7] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:43.384 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:43.385 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:43.389 DEBUG 1 - [7878-exec-7] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:43.412  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:tab_new_list_explorer\:name) 
2025-08-03 22:42:43.413 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:tab_new_list_explorer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:42:43.415 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 5a94ee85b6c00ab0c6a2aa5f396c9e16 
2025-08-03 22:42:43.419 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=5a94ee85b6c00ab0c6a2aa5f396c9e16, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:tab_new_list_explorer\:name, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:42:43.419502235, enableHealing=false) 
2025-08-03 22:42:43.625 DEBUG 1 - [7878-exec-9] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:43.630 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:43.631 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:43.634 DEBUG 1 - [7878-exec-9] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:43.850 DEBUG 1 - [7878-exec-6] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:43.851 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:43.851 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:43.852 DEBUG 1 - [7878-exec-6] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:44.085  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:42:44.086 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:44.090 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:42:44.094 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:42:44.094261662, enableHealing=true) 
2025-08-03 22:42:44.118 DEBUG 1 - [7878-exec-2] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:44.119 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:44.120 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:44.121 DEBUG 1 - [7878-exec-2] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:44.258  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:tab_new_list_explorer\:btnLESearch) 
2025-08-03 22:42:44.259 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:tab_new_list_explorer\:btnLESearch, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:42:44.259 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 797b129854fa214de108101e6c385d2b 
2025-08-03 22:42:44.268 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=797b129854fa214de108101e6c385d2b, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:tab_new_list_explorer\:btnLESearch, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:42:44.268180615, enableHealing=false) 
2025-08-03 22:42:44.354 DEBUG 1 - [878-exec-10] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:44.354 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:44.356 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:44.359 DEBUG 1 - [878-exec-10] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:44.551 DEBUG 1 - [7878-exec-3] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:44.552 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:44.553 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:44.563 DEBUG 1 - [7878-exec-3] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:44.748 DEBUG 1 - [7878-exec-1] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:44.748 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:44.749 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:44.752 DEBUG 1 - [7878-exec-1] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:44.947 DEBUG 1 - [7878-exec-7] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:44.956 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:45.017 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:45.038 DEBUG 1 - [7878-exec-7] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:45.472 DEBUG 1 - [7878-exec-5] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:45.473 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:45.476 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:45.499 DEBUG 1 - [7878-exec-5] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:45.807 DEBUG 1 - [7878-exec-9] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:45.809 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:45.810 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:45.815 DEBUG 1 - [7878-exec-9] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:46.261 DEBUG 1 - [7878-exec-6] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:46.263 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:46.264 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:46.273 DEBUG 1 - [7878-exec-6] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:46.523 DEBUG 1 - [7878-exec-4] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:46.526 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:46.527 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:46.536 DEBUG 1 - [7878-exec-4] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:46.802 DEBUG 1 - [7878-exec-2] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:46.809 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:46.810 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:46.813 DEBUG 1 - [7878-exec-2] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:47.002 DEBUG 1 - [7878-exec-8] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:47.005 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:47.005 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:47.007 DEBUG 1 - [7878-exec-8] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:47.200 DEBUG 1 - [878-exec-10] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:47.200 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:47.201 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:47.205 DEBUG 1 - [878-exec-10] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:47.360 DEBUG 1 - [7878-exec-3] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:47.361 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:47.362 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:47.364 DEBUG 1 - [7878-exec-3] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:47.528 DEBUG 1 - [7878-exec-1] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:47.529 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:47.530 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:47.533 DEBUG 1 - [7878-exec-1] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:47.755 DEBUG 1 - [7878-exec-7] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:47.756 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:47.760 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:47.768 DEBUG 1 - [7878-exec-7] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:47.976 DEBUG 1 - [7878-exec-5] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:47.978 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:47.984 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:47.986 DEBUG 1 - [7878-exec-5] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:48.173 DEBUG 1 - [7878-exec-9] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:48.174 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:48.174 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:48.180 DEBUG 1 - [7878-exec-9] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:48.331 DEBUG 1 - [7878-exec-6] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:48.333 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:48.333 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:48.339 DEBUG 1 - [7878-exec-6] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:48.497 DEBUG 1 - [7878-exec-4] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:48.498 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:48.499 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:48.507 DEBUG 1 - [7878-exec-4] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:48.680 DEBUG 1 - [7878-exec-2] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:48.683 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:48.684 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:48.688 DEBUG 1 - [7878-exec-2] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:48.861 DEBUG 1 - [7878-exec-8] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:48.862 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:48.864 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:48.865 DEBUG 1 - [7878-exec-8] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:49.049 DEBUG 1 - [878-exec-10] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:49.052 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:49.053 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:49.056 DEBUG 1 - [878-exec-10] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:49.239 DEBUG 1 - [7878-exec-3] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:49.239 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:49.240 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:49.243 DEBUG 1 - [7878-exec-3] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:49.432 DEBUG 1 - [7878-exec-1] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:49.433 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:49.434 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:49.440 DEBUG 1 - [7878-exec-1] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:49.612 DEBUG 1 - [7878-exec-7] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:49.613 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:49.614 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:49.625 DEBUG 1 - [7878-exec-7] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:49.823 DEBUG 1 - [7878-exec-5] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:49.824 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:49.825 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:49.827 DEBUG 1 - [7878-exec-5] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:50.014 DEBUG 1 - [7878-exec-9] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:50.015 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:50.019 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:50.023 DEBUG 1 - [7878-exec-9] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:50.231 DEBUG 1 - [7878-exec-6] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:50.232 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:50.232 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:50.237 DEBUG 1 - [7878-exec-6] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:50.451 DEBUG 1 - [7878-exec-4] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:50.452 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:50.452 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:50.456 DEBUG 1 - [7878-exec-4] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:50.607 DEBUG 1 - [7878-exec-2] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:50.611 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:50.612 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:50.619 DEBUG 1 - [7878-exec-2] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:50.802 DEBUG 1 - [7878-exec-8] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:50.803 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:50.803 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:50.810 DEBUG 1 - [7878-exec-8] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:50.993 DEBUG 1 - [878-exec-10] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:50.994 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:50.997 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:51.004 DEBUG 1 - [878-exec-10] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:51.223 DEBUG 1 - [7878-exec-3] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:51.224 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:51.224 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:51.226 DEBUG 1 - [7878-exec-3] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:51.418 DEBUG 1 - [7878-exec-1] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:51.418 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:51.422 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:51.425 DEBUG 1 - [7878-exec-1] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:51.605 DEBUG 1 - [7878-exec-7] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:51.606 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:51.606 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:51.608 DEBUG 1 - [7878-exec-7] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:51.779 DEBUG 1 - [7878-exec-5] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:51.780 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:51.780 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:51.784 DEBUG 1 - [7878-exec-5] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:52.006 DEBUG 1 - [7878-exec-9] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:52.007 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:52.009 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:52.012 DEBUG 1 - [7878-exec-9] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:52.220 DEBUG 1 - [7878-exec-6] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:52.220 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:52.222 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:52.226 DEBUG 1 - [7878-exec-6] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:52.436 DEBUG 1 - [7878-exec-4] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:52.437 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:52.438 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:52.442 DEBUG 1 - [7878-exec-4] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:52.574 DEBUG 1 - [7878-exec-2] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:52.574 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:52.575 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:52.582 DEBUG 1 - [7878-exec-2] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:52.769 DEBUG 1 - [7878-exec-8] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:52.770 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:52.771 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:52.775 DEBUG 1 - [7878-exec-8] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:52.970 DEBUG 1 - [878-exec-10] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:52.970 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:52.972 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:52.974 DEBUG 1 - [878-exec-10] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:53.181 DEBUG 1 - [7878-exec-3] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:53.183 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:53.184 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:53.194 DEBUG 1 - [7878-exec-3] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:53.357 DEBUG 1 - [7878-exec-1] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:53.360 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:53.365 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:53.373 DEBUG 1 - [7878-exec-1] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:53.544 DEBUG 1 - [7878-exec-7] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:53.546 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:53.547 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:53.550 DEBUG 1 - [7878-exec-7] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:53.754 DEBUG 1 - [7878-exec-5] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:53.755 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:53.756 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:53.758 DEBUG 1 - [7878-exec-5] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:53.961 DEBUG 1 - [7878-exec-9] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:53.962 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:53.967 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:53.970 DEBUG 1 - [7878-exec-9] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:54.159 DEBUG 1 - [7878-exec-6] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:54.160 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:54.164 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:54.166 DEBUG 1 - [7878-exec-6] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:54.369 DEBUG 1 - [7878-exec-4] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:54.370 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:54.371 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:54.374 DEBUG 1 - [7878-exec-4] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:54.570 DEBUG 1 - [7878-exec-2] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:54.571 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:54.573 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:54.578 DEBUG 1 - [7878-exec-2] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:54.767 DEBUG 1 - [7878-exec-8] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:54.768 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:54.768 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:54.771 DEBUG 1 - [7878-exec-8] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:54.968 DEBUG 1 - [878-exec-10] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:54.968 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:54.969 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:54.972 DEBUG 1 - [878-exec-10] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:55.177 DEBUG 1 - [7878-exec-3] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:55.178 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:55.182 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:55.189 DEBUG 1 - [7878-exec-3] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:55.385 DEBUG 1 - [7878-exec-1] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:55.386 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:55.386 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:55.390 DEBUG 1 - [7878-exec-1] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:55.567 DEBUG 1 - [7878-exec-7] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:55.570 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:55.572 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:55.577 DEBUG 1 - [7878-exec-7] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:55.748 DEBUG 1 - [7878-exec-5] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:55.750 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:55.751 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:55.753 DEBUG 1 - [7878-exec-5] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:55.965 DEBUG 1 - [7878-exec-9] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:55.968 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:55.972 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:55.974 DEBUG 1 - [7878-exec-9] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:56.154 DEBUG 1 - [7878-exec-6] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:56.155 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:56.156 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:56.166 DEBUG 1 - [7878-exec-6] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:56.345 DEBUG 1 - [7878-exec-4] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:56.349 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:56.350 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:56.353 DEBUG 1 - [7878-exec-4] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:56.517 DEBUG 1 - [7878-exec-2] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:56.517 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:56.518 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:56.522 DEBUG 1 - [7878-exec-2] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:56.738 DEBUG 1 - [7878-exec-8] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:56.739 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:56.739 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:56.741 DEBUG 1 - [7878-exec-8] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:56.970 DEBUG 1 - [878-exec-10] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:56.970 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:56.971 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:56.973 DEBUG 1 - [878-exec-10] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:57.193 DEBUG 1 - [7878-exec-3] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:57.194 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:57.197 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:57.204 DEBUG 1 - [7878-exec-3] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:57.395 DEBUG 1 - [7878-exec-1] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:57.398 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:57.401 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:57.402 DEBUG 1 - [7878-exec-1] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:57.599 DEBUG 1 - [7878-exec-7] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:57.602 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:57.603 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:57.607 DEBUG 1 - [7878-exec-7] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:57.801 DEBUG 1 - [7878-exec-5] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:57.802 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:57.803 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:57.805 DEBUG 1 - [7878-exec-5] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:58.000 DEBUG 1 - [7878-exec-9] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:58.001 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:58.002 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:58.007 DEBUG 1 - [7878-exec-9] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:58.208 DEBUG 1 - [7878-exec-6] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:58.209 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:58.211 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:58.215 DEBUG 1 - [7878-exec-6] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:58.414 DEBUG 1 - [7878-exec-4] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:58.414 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:58.415 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:58.426 DEBUG 1 - [7878-exec-4] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:58.629 DEBUG 1 - [7878-exec-2] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:58.630 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:58.633 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:58.636 DEBUG 1 - [7878-exec-2] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:58.797 DEBUG 1 - [7878-exec-8] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:58.798 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:58.798 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:58.803 DEBUG 1 - [7878-exec-8] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:58.996 DEBUG 1 - [878-exec-10] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:58.997 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:58.998 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:59.002 DEBUG 1 - [878-exec-10] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:59.194 DEBUG 1 - [7878-exec-3] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:59.198 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:59.199 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:59.202 DEBUG 1 - [7878-exec-3] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:59.410 DEBUG 1 - [7878-exec-1] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:59.411 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:59.415 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:59.417 DEBUG 1 - [7878-exec-1] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:59.606 DEBUG 1 - [7878-exec-7] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:59.608 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:59.610 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:59.612 DEBUG 1 - [7878-exec-7] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:42:59.814 DEBUG 1 - [7878-exec-5] healenium                        : [Get Reference] Request: RequestDto(locator=#menuform\:om_HomeListManager, className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml)) 
2025-08-03 22:42:59.819 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:42:59.820 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:42:59.825 DEBUG 1 - [7878-exec-5] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)\n Information\n Operation completed successfully.', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=0, innerText='List Manager\n 0\n List Manager\n List Set Manager\n List Explorer\n Activity\n Zone:\n < Any >\n Zone (840793216)\n Zone (859357564)\n Zone (859357564)\n Search\n 	Id	Name	Creation Date 	Modification Date 	Owner	Modifier 	Rank 	Zone\n \n 	2170	ListSetName-581088420	2025/08/03 22:41:59	2025/08/03 22:41:59	selenium-random-group-78030441	selenium-user-541334194	75	Zone (859357564)\n New Set\n Delete\n <<\n <\n >\n >>\n Pageof 11 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-topbar-wrapper], index=0, innerText='List Manager\n 0', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[menu-wrapper], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=position:relative; height: 100%}, children=[]], Node[tag='div', id='', classes=[], index=0, innerText='', otherAttributes={style=height: 100%; overflow-x: hidden; overflow-y: auto}, children=[]], Node[tag='div', id='', classes=[layout-menu-container], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='form', id='menuform', classes=[], index=0, innerText='', otherAttributes={enctype=application/x-www-form-urlencoded, method=post, name=menuform, action=/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml}, children=[]], Node[tag='ul', id='menuform:j_idt32', classes=[layout-menu, sidebar-navigation], index=1, innerText='', otherAttributes={}, children=[]], Node[tag='li', id='menuform:om_HomeListManager', classes=[], index=2, innerText='', otherAttributes={role=menuitem}, children=[]]]], unsuccessfulLocators=null)) 
