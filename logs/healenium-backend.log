2025-08-02 21:18:17.925 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:17.945 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:17.945370334, enableHealing=true) 
2025-08-02 21:18:18.121  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:18.121 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:18.122 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:18.124 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:18.123963466, enableHealing=true) 
2025-08-02 21:18:18.306  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.xpath(//tbody[@id='listManagerForm:homepage_business:tabViewListManager:Tab_activity:_tblResults_data']//tr[1]//td[5]) 
2025-08-02 21:18:18.309 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: //tbody[@id='listManagerForm:homepage_business:tabViewListManager:Tab_activity:_tblResults_data']//tr[1]//td[5], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:18.309 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 2c2a25776a1cfc316b40856cd290fd79 
2025-08-02 21:18:18.312 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=2c2a25776a1cfc316b40856cd290fd79, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//tbody[@id='listManagerForm:homepage_business:tabViewListManager:Tab_activity:_tblResults_data']//tr[1]//td[5], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:18.312759086, enableHealing=true) 
2025-08-02 21:18:18.940  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:18.941 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:18.942 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:18.946 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:18.946066922, enableHealing=true) 
2025-08-02 21:18:19.076  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.xpath(//*[@title='Exit']) 
2025-08-02 21:18:19.077 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: //*[@title='Exit'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:19.077 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 0e26e61610c3b6044e168b93cc5706ee 
2025-08-02 21:18:19.082 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=0e26e61610c3b6044e168b93cc5706ee, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@title='Exit'], type=By.xpath), command=findElements, createdDate=2025-08-02T18:18:19.081978315, enableHealing=false) 
2025-08-02 21:18:19.223  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:19.225 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:19.226 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:19.228 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:19.228052293, enableHealing=true) 
2025-08-02 21:18:19.358  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.xpath(//*[@title='Exit' or @title ='Quitter']) 
2025-08-02 21:18:19.359 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: //*[@title='Exit' or @title ='Quitter'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:19.360 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: c3eb240102044bb3ca7cb914f3e9f403 
2025-08-02 21:18:19.363 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=c3eb240102044bb3ca7cb914f3e9f403, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@title='Exit' or @title ='Quitter'], type=By.xpath), command=findElements, createdDate=2025-08-02T18:18:19.363217050, enableHealing=false) 
2025-08-02 21:18:19.533  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:19.534 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml#, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:19.534 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:19.536 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml#, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:19.535952603, enableHealing=true) 
2025-08-02 21:18:19.654  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.xpath(//button[.='Yes']) 
2025-08-02 21:18:19.655 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: //button[.='Yes'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml#, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:19.655 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: a7b20c9e34ce109a835745fce2d7552c 
2025-08-02 21:18:19.660 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=a7b20c9e34ce109a835745fce2d7552c, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml#, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//button[.='Yes'], type=By.xpath), command=findElements, createdDate=2025-08-02T18:18:19.660087833, enableHealing=false) 
2025-08-02 21:18:19.971  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:19.972 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml#, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:19.972 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:19.974 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml#, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:19.974200939, enableHealing=true) 
2025-08-02 21:18:20.104  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.cssSelector(#pageLoginForm\:login_business\:UserName) 
2025-08-02 21:18:20.105 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #pageLoginForm\:login_business\:UserName, URL(source): http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:20.105 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: b5602af340e30317feaee6de3bf83c34 
2025-08-02 21:18:20.106 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=b5602af340e30317feaee6de3bf83c34, url=http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#pageLoginForm\:login_business\:UserName, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:18:20.106671468, enableHealing=false) 
2025-08-02 21:18:20.400 DEBUG 1 - [7878-exec-1] healenium                        : [Get Reference] Request: RequestDto(locator=//*[@class='pi pi-spin pi-spinner ajax-loader'], className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml)) 
2025-08-02 21:18:20.402 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:20.403 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:20.404 DEBUG 1 - [7878-exec-1] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n *\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2561	operator-02	2025/08/02 21:15:12	2025/08/02 21:15:47	[v2] 2025/08/02	No	Yes\n \n 	2560	operator-02	2025/08/02 21:11:46	2025/08/02 21:14:30	[v1] 2025/08/01	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n *\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2561	operator-02	2025/08/02 21:15:12	2025/08/02 21:15:47	[v2] 2025/08/02	No	Yes\n \n 	2560	operator-02	2025/08/02 21:11:46	2025/08/02 21:14:30	[v1] 2025/08/01	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=9, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n *\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2561	operator-02	2025/08/02 21:15:12	2025/08/02 21:15:47	[v2] 2025/08/02	No	Yes\n \n 	2560	operator-02	2025/08/02 21:11:46	2025/08/02 21:14:30	[v1] 2025/08/01	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='j_idt1380', classes=[], index=2, innerText='', otherAttributes={style=width:50px;height:50px;position:fixed;right:7px;bottom:7px; z-index: 10000}, children=[]], Node[tag='div', id='j_idt1380_start', classes=[], index=0, innerText='\n                         ', otherAttributes={style=display: none;}, children=[]], Node[tag='i', id='', classes=[pi-spin, pi, ajax-loader, pi-spinner], index=0, innerText='', otherAttributes={aria-hidden=true}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-02 21:18:20.546 DEBUG 1 - [7878-exec-4] healenium                        : [Get Reference] Request: RequestDto(locator=//*[@class='pi pi-spin pi-spinner ajax-loader'], className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml)) 
2025-08-02 21:18:20.552 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:20.552 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:20.554 DEBUG 1 - [7878-exec-4] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n *\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2561	operator-02	2025/08/02 21:15:12	2025/08/02 21:15:47	[v2] 2025/08/02	No	Yes\n \n 	2560	operator-02	2025/08/02 21:11:46	2025/08/02 21:14:30	[v1] 2025/08/01	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n *\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2561	operator-02	2025/08/02 21:15:12	2025/08/02 21:15:47	[v2] 2025/08/02	No	Yes\n \n 	2560	operator-02	2025/08/02 21:11:46	2025/08/02 21:14:30	[v1] 2025/08/01	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=9, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n *\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2561	operator-02	2025/08/02 21:15:12	2025/08/02 21:15:47	[v2] 2025/08/02	No	Yes\n \n 	2560	operator-02	2025/08/02 21:11:46	2025/08/02 21:14:30	[v1] 2025/08/01	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='j_idt1380', classes=[], index=2, innerText='', otherAttributes={style=width:50px;height:50px;position:fixed;right:7px;bottom:7px; z-index: 10000}, children=[]], Node[tag='div', id='j_idt1380_start', classes=[], index=0, innerText='\n                         ', otherAttributes={style=display: none;}, children=[]], Node[tag='i', id='', classes=[pi-spin, pi, ajax-loader, pi-spinner], index=0, innerText='', otherAttributes={aria-hidden=true}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-02 21:18:20.705 DEBUG 1 - [7878-exec-6] healenium                        : [Get Reference] Request: RequestDto(locator=//*[@class='pi pi-spin pi-spinner ajax-loader'], className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml)) 
2025-08-02 21:18:20.707 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:20.708 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:20.710 DEBUG 1 - [7878-exec-6] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n *\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2561	operator-02	2025/08/02 21:15:12	2025/08/02 21:15:47	[v2] 2025/08/02	No	Yes\n \n 	2560	operator-02	2025/08/02 21:11:46	2025/08/02 21:14:30	[v1] 2025/08/01	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n *\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2561	operator-02	2025/08/02 21:15:12	2025/08/02 21:15:47	[v2] 2025/08/02	No	Yes\n \n 	2560	operator-02	2025/08/02 21:11:46	2025/08/02 21:14:30	[v1] 2025/08/01	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=9, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n *\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2561	operator-02	2025/08/02 21:15:12	2025/08/02 21:15:47	[v2] 2025/08/02	No	Yes\n \n 	2560	operator-02	2025/08/02 21:11:46	2025/08/02 21:14:30	[v1] 2025/08/01	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='j_idt1380', classes=[], index=2, innerText='', otherAttributes={style=width:50px;height:50px;position:fixed;right:7px;bottom:7px; z-index: 10000}, children=[]], Node[tag='div', id='j_idt1380_start', classes=[], index=0, innerText='\n                         ', otherAttributes={style=display: none;}, children=[]], Node[tag='i', id='', classes=[pi-spin, pi, ajax-loader, pi-spinner], index=0, innerText='', otherAttributes={aria-hidden=true}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-02 21:18:20.810  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.cssSelector(#pageLoginForm\:login_business\:UserName) 
2025-08-02 21:18:20.811 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: #pageLoginForm\:login_business\:UserName, URL(source): http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:20.811 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: b5602af340e30317feaee6de3bf83c34 
2025-08-02 21:18:20.812 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=b5602af340e30317feaee6de3bf83c34, url=http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#pageLoginForm\:login_business\:UserName, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:18:20.812633193, enableHealing=false) 
2025-08-02 21:18:21.318 DEBUG 1 - [7878-exec-5] healenium                        : [Get Reference] Request: RequestDto(locator=//*[@class='pi pi-spin pi-spinner ajax-loader'], className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml)) 
2025-08-02 21:18:21.318 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:21.319 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:21.322 DEBUG 1 - [7878-exec-5] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n *\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2561	operator-02	2025/08/02 21:15:12	2025/08/02 21:15:47	[v2] 2025/08/02	No	Yes\n \n 	2560	operator-02	2025/08/02 21:11:46	2025/08/02 21:14:30	[v1] 2025/08/01	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n *\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2561	operator-02	2025/08/02 21:15:12	2025/08/02 21:15:47	[v2] 2025/08/02	No	Yes\n \n 	2560	operator-02	2025/08/02 21:11:46	2025/08/02 21:14:30	[v1] 2025/08/01	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=9, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n *\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2561	operator-02	2025/08/02 21:15:12	2025/08/02 21:15:47	[v2] 2025/08/02	No	Yes\n \n 	2560	operator-02	2025/08/02 21:11:46	2025/08/02 21:14:30	[v1] 2025/08/01	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='j_idt1380', classes=[], index=2, innerText='', otherAttributes={style=width:50px;height:50px;position:fixed;right:7px;bottom:7px; z-index: 10000}, children=[]], Node[tag='div', id='j_idt1380_start', classes=[], index=0, innerText='\n                         ', otherAttributes={style=display: none;}, children=[]], Node[tag='i', id='', classes=[pi-spin, pi, ajax-loader, pi-spinner], index=0, innerText='', otherAttributes={aria-hidden=true}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-02 21:18:21.434  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.cssSelector(#pageLoginForm\:login_business\:Password) 
2025-08-02 21:18:21.436 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: #pageLoginForm\:login_business\:Password, URL(source): http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:21.437 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: dfcd153e32be3bbdf9ff3d83828d14f2 
2025-08-02 21:18:21.445 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=dfcd153e32be3bbdf9ff3d83828d14f2, url=http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#pageLoginForm\:login_business\:Password, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:18:21.445626633, enableHealing=false) 
2025-08-02 21:18:21.654 DEBUG 1 - [878-exec-10] healenium                        : [Get Reference] Request: RequestDto(locator=//*[@class='pi pi-spin pi-spinner ajax-loader'], className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml)) 
2025-08-02 21:18:21.655 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:21.656 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:21.657 DEBUG 1 - [878-exec-10] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n *\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2561	operator-02	2025/08/02 21:15:12	2025/08/02 21:15:47	[v2] 2025/08/02	No	Yes\n \n 	2560	operator-02	2025/08/02 21:11:46	2025/08/02 21:14:30	[v1] 2025/08/01	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n *\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2561	operator-02	2025/08/02 21:15:12	2025/08/02 21:15:47	[v2] 2025/08/02	No	Yes\n \n 	2560	operator-02	2025/08/02 21:11:46	2025/08/02 21:14:30	[v1] 2025/08/01	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=9, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n *\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2561	operator-02	2025/08/02 21:15:12	2025/08/02 21:15:47	[v2] 2025/08/02	No	Yes\n \n 	2560	operator-02	2025/08/02 21:11:46	2025/08/02 21:14:30	[v1] 2025/08/01	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='j_idt1380', classes=[], index=2, innerText='', otherAttributes={style=width:50px;height:50px;position:fixed;right:7px;bottom:7px; z-index: 10000}, children=[]], Node[tag='div', id='j_idt1380_start', classes=[], index=0, innerText='\n                         ', otherAttributes={style=display: none;}, children=[]], Node[tag='i', id='', classes=[pi-spin, pi, ajax-loader, pi-spinner], index=0, innerText='', otherAttributes={aria-hidden=true}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-02 21:18:21.763  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.cssSelector(#pageLoginForm\:login_business\:UserName) 
2025-08-02 21:18:21.764 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: #pageLoginForm\:login_business\:UserName, URL(source): http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:21.765 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: b5602af340e30317feaee6de3bf83c34 
2025-08-02 21:18:21.766 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=b5602af340e30317feaee6de3bf83c34, url=http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#pageLoginForm\:login_business\:UserName, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:18:21.766858780, enableHealing=false) 
2025-08-02 21:18:22.025 DEBUG 1 - [7878-exec-8] healenium                        : [Get Reference] Request: RequestDto(locator=//*[@class='pi pi-spin pi-spinner ajax-loader'], className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml)) 
2025-08-02 21:18:22.026 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:22.026 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:22.030 DEBUG 1 - [7878-exec-8] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n *\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2561	operator-02	2025/08/02 21:15:12	2025/08/02 21:15:47	[v2] 2025/08/02	No	Yes\n \n 	2560	operator-02	2025/08/02 21:11:46	2025/08/02 21:14:30	[v1] 2025/08/01	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n *\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2561	operator-02	2025/08/02 21:15:12	2025/08/02 21:15:47	[v2] 2025/08/02	No	Yes\n \n 	2560	operator-02	2025/08/02 21:11:46	2025/08/02 21:14:30	[v1] 2025/08/01	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=9, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n *\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2561	operator-02	2025/08/02 21:15:12	2025/08/02 21:15:47	[v2] 2025/08/02	No	Yes\n \n 	2560	operator-02	2025/08/02 21:11:46	2025/08/02 21:14:30	[v1] 2025/08/01	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='j_idt1380', classes=[], index=2, innerText='', otherAttributes={style=width:50px;height:50px;position:fixed;right:7px;bottom:7px; z-index: 10000}, children=[]], Node[tag='div', id='j_idt1380_start', classes=[], index=0, innerText='\n                         ', otherAttributes={style=display: none;}, children=[]], Node[tag='i', id='', classes=[pi-spin, pi, ajax-loader, pi-spinner], index=0, innerText='', otherAttributes={aria-hidden=true}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-02 21:18:22.133  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.cssSelector(#pageLoginForm\:login_business\:UserName) 
2025-08-02 21:18:22.134 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #pageLoginForm\:login_business\:UserName, URL(source): http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:22.134 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: b5602af340e30317feaee6de3bf83c34 
2025-08-02 21:18:22.135 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=b5602af340e30317feaee6de3bf83c34, url=http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#pageLoginForm\:login_business\:UserName, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:18:22.135566694, enableHealing=false) 
2025-08-02 21:18:22.313 DEBUG 1 - [7878-exec-1] healenium                        : [Get Reference] Request: RequestDto(locator=//*[@class='pi pi-spin pi-spinner ajax-loader'], className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml)) 
2025-08-02 21:18:22.314 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:22.315 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:22.316 DEBUG 1 - [7878-exec-1] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n *\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2561	operator-02	2025/08/02 21:15:12	2025/08/02 21:15:47	[v2] 2025/08/02	No	Yes\n \n 	2560	operator-02	2025/08/02 21:11:46	2025/08/02 21:14:30	[v1] 2025/08/01	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n *\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2561	operator-02	2025/08/02 21:15:12	2025/08/02 21:15:47	[v2] 2025/08/02	No	Yes\n \n 	2560	operator-02	2025/08/02 21:11:46	2025/08/02 21:14:30	[v1] 2025/08/01	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=9, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n *\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2561	operator-02	2025/08/02 21:15:12	2025/08/02 21:15:47	[v2] 2025/08/02	No	Yes\n \n 	2560	operator-02	2025/08/02 21:11:46	2025/08/02 21:14:30	[v1] 2025/08/01	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='j_idt1380', classes=[], index=2, innerText='', otherAttributes={style=width:50px;height:50px;position:fixed;right:7px;bottom:7px; z-index: 10000}, children=[]], Node[tag='div', id='j_idt1380_start', classes=[], index=0, innerText='\n                         ', otherAttributes={style=display: none;}, children=[]], Node[tag='i', id='', classes=[pi-spin, pi, ajax-loader, pi-spinner], index=0, innerText='', otherAttributes={aria-hidden=true}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-02 21:18:22.405  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.cssSelector(#pageLoginForm\:login_business\:Password) 
2025-08-02 21:18:22.405 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: #pageLoginForm\:login_business\:Password, URL(source): http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:22.406 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: dfcd153e32be3bbdf9ff3d83828d14f2 
2025-08-02 21:18:22.407 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=dfcd153e32be3bbdf9ff3d83828d14f2, url=http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#pageLoginForm\:login_business\:Password, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:18:22.407619591, enableHealing=false) 
2025-08-02 21:18:22.566 DEBUG 1 - [7878-exec-6] healenium                        : [Get Reference] Request: RequestDto(locator=//*[@class='pi pi-spin pi-spinner ajax-loader'], className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml)) 
2025-08-02 21:18:22.567 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:22.568 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:22.569 DEBUG 1 - [7878-exec-6] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n *\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2561	operator-02	2025/08/02 21:15:12	2025/08/02 21:15:47	[v2] 2025/08/02	No	Yes\n \n 	2560	operator-02	2025/08/02 21:11:46	2025/08/02 21:14:30	[v1] 2025/08/01	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n *\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2561	operator-02	2025/08/02 21:15:12	2025/08/02 21:15:47	[v2] 2025/08/02	No	Yes\n \n 	2560	operator-02	2025/08/02 21:11:46	2025/08/02 21:14:30	[v1] 2025/08/01	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=9, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n *\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2561	operator-02	2025/08/02 21:15:12	2025/08/02 21:15:47	[v2] 2025/08/02	No	Yes\n \n 	2560	operator-02	2025/08/02 21:11:46	2025/08/02 21:14:30	[v1] 2025/08/01	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='j_idt1380', classes=[], index=2, innerText='', otherAttributes={style=width:50px;height:50px;position:fixed;right:7px;bottom:7px; z-index: 10000}, children=[]], Node[tag='div', id='j_idt1380_start', classes=[], index=0, innerText='\n                         ', otherAttributes={style=display: none;}, children=[]], Node[tag='i', id='', classes=[pi-spin, pi, ajax-loader, pi-spinner], index=0, innerText='', otherAttributes={aria-hidden=true}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-02 21:18:22.640  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.cssSelector(#pageLoginForm\:login_business\:Password) 
2025-08-02 21:18:22.641 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: #pageLoginForm\:login_business\:Password, URL(source): http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:22.641 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: dfcd153e32be3bbdf9ff3d83828d14f2 
2025-08-02 21:18:22.643 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=dfcd153e32be3bbdf9ff3d83828d14f2, url=http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#pageLoginForm\:login_business\:Password, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:18:22.643231247, enableHealing=false) 
2025-08-02 21:18:22.864 DEBUG 1 - [7878-exec-5] healenium                        : [Get Reference] Request: RequestDto(locator=//*[@class='pi pi-spin pi-spinner ajax-loader'], className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml)) 
2025-08-02 21:18:22.865 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:22.866 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:22.868 DEBUG 1 - [7878-exec-5] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n *\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2561	operator-02	2025/08/02 21:15:12	2025/08/02 21:15:47	[v2] 2025/08/02	No	Yes\n \n 	2560	operator-02	2025/08/02 21:11:46	2025/08/02 21:14:30	[v1] 2025/08/01	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n *\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2561	operator-02	2025/08/02 21:15:12	2025/08/02 21:15:47	[v2] 2025/08/02	No	Yes\n \n 	2560	operator-02	2025/08/02 21:11:46	2025/08/02 21:14:30	[v1] 2025/08/01	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=9, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n *\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2561	operator-02	2025/08/02 21:15:12	2025/08/02 21:15:47	[v2] 2025/08/02	No	Yes\n \n 	2560	operator-02	2025/08/02 21:11:46	2025/08/02 21:14:30	[v1] 2025/08/01	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='j_idt1380', classes=[], index=2, innerText='', otherAttributes={style=width:50px;height:50px;position:fixed;right:7px;bottom:7px; z-index: 10000}, children=[]], Node[tag='div', id='j_idt1380_start', classes=[], index=0, innerText='\n                         ', otherAttributes={style=display: none;}, children=[]], Node[tag='i', id='', classes=[pi-spin, pi, ajax-loader, pi-spinner], index=0, innerText='', otherAttributes={aria-hidden=true}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-02 21:18:22.953  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.cssSelector(#pageLoginForm\:login_business\:goButton) 
2025-08-02 21:18:22.953 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: #pageLoginForm\:login_business\:goButton, URL(source): http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:22.954 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: a9431ac117de5706bfee507de4c5c09f 
2025-08-02 21:18:22.964 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=a9431ac117de5706bfee507de4c5c09f, url=http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#pageLoginForm\:login_business\:goButton, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:18:22.964247983, enableHealing=false) 
2025-08-02 21:18:25.088  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.xpath(//*[@class='ui-growl-title']) 
2025-08-02 21:18:25.089 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: //*[@class='ui-growl-title'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:25.091 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 34856943884f3a1c0a7d9f1f13b48b3e 
2025-08-02 21:18:25.093 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=34856943884f3a1c0a7d9f1f13b48b3e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='ui-growl-title'], type=By.xpath), command=findElements, createdDate=2025-08-02T18:18:25.093660200, enableHealing=false) 
2025-08-02 21:18:25.160  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:25.161 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:25.161 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:25.162 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:25.162932028, enableHealing=true) 
2025-08-02 21:18:25.265  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.xpath(//*[@class='ui-growl-title']) 
2025-08-02 21:18:25.266 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: //*[@class='ui-growl-title'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:25.267 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 34856943884f3a1c0a7d9f1f13b48b3e 
2025-08-02 21:18:25.268 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=34856943884f3a1c0a7d9f1f13b48b3e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='ui-growl-title'], type=By.xpath), command=findElements, createdDate=2025-08-02T18:18:25.268613106, enableHealing=false) 
2025-08-02 21:18:25.385  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.cssSelector(#menuform\:om_HomeListManager) 
2025-08-02 21:18:25.385 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:25.386 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-02 21:18:25.387 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=99cddc51be3dae4e40bef1d2069ba63f, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#menuform\:om_HomeListManager, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:18:25.387173302, enableHealing=true) 
2025-08-02 21:18:25.505  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:25.506 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:25.506 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:25.510 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:25.510752639, enableHealing=true) 
2025-08-02 21:18:25.597  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.cssSelector(#menuform\:om_HomeListManager) 
2025-08-02 21:18:25.597 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:25.601 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: a12e0634dcb2612225fc4ac8aef2676f 
2025-08-02 21:18:25.603 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=a12e0634dcb2612225fc4ac8aef2676f, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#menuform\:om_HomeListManager, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:18:25.603404691, enableHealing=false) 
2025-08-02 21:18:26.447  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:26.448 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:26.448 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:26.449 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:26.449780255, enableHealing=true) 
2025-08-02 21:18:26.559  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.xpath(//form[@id='topbar-right']/ul/label) 
2025-08-02 21:18:26.560 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: //form[@id='topbar-right']/ul/label, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:26.560 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 4a9504d175bc9716fe2da0e9f093ac9e 
2025-08-02 21:18:26.561 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=4a9504d175bc9716fe2da0e9f093ac9e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//form[@id='topbar-right']/ul/label, type=By.xpath), command=findElements, createdDate=2025-08-02T18:18:26.561832038, enableHealing=false) 
2025-08-02 21:18:26.644  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.xpath(//a[contains(text(),'List Manager')]) 
2025-08-02 21:18:26.644 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: //a[contains(text(),'List Manager')], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:26.644 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 8f3dc03694a381d6b07aab61a1b897c2 
2025-08-02 21:18:26.646 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=8f3dc03694a381d6b07aab61a1b897c2, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//a[contains(text(),'List Manager')], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:26.646541608, enableHealing=true) 
2025-08-02 21:18:26.801  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:26.802 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:26.802 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:26.804 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:26.804237684, enableHealing=true) 
2025-08-02 21:18:26.933  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.xpath(//a[contains(text(),'List Manager')]) 
2025-08-02 21:18:26.934 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: //a[contains(text(),'List Manager')], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:26.934 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: f57e64d4f8c44329ff06c712afa6a547 
2025-08-02 21:18:26.936 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=f57e64d4f8c44329ff06c712afa6a547, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//a[contains(text(),'List Manager')], type=By.xpath), command=findElements, createdDate=2025-08-02T18:18:26.936259627, enableHealing=false) 
2025-08-02 21:18:27.176  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:zoneCbx) 
2025-08-02 21:18:27.178 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:zoneCbx, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:27.178 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 2d9dd40db149a3b04bc59253096ddc3c 
2025-08-02 21:18:27.180 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=2d9dd40db149a3b04bc59253096ddc3c, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:zoneCbx, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:18:27.180364455, enableHealing=true) 
2025-08-02 21:18:27.314  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:27.315 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:27.315 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:27.318 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:27.318340184, enableHealing=true) 
2025-08-02 21:18:27.425  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.xpath(//span[contains(text(),'Reset')]) 
2025-08-02 21:18:27.426 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: //span[contains(text(),'Reset')], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:27.427 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: fce827adfc56329d1ee0425d66138593 
2025-08-02 21:18:27.429 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=fce827adfc56329d1ee0425d66138593, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//span[contains(text(),'Reset')], type=By.xpath), command=findElements, createdDate=2025-08-02T18:18:27.429762838, enableHealing=false) 
2025-08-02 21:18:27.667  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:27.668 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:27.671 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:27.673 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:27.673373442, enableHealing=true) 
2025-08-02 21:18:27.834  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:27.835 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:27.835 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:27.837 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:27.837076307, enableHealing=true) 
2025-08-02 21:18:27.942  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:zoneCbx_label) 
2025-08-02 21:18:27.942 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:zoneCbx_label, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:27.943 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 094f76901d4289b27135807cd4a8be96 
2025-08-02 21:18:27.945 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=094f76901d4289b27135807cd4a8be96, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:zoneCbx_label, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:18:27.945083396, enableHealing=false) 
2025-08-02 21:18:29.190  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.xpath(//*[@role='option']) 
2025-08-02 21:18:29.191 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: //*[@role='option'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:29.191 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 03a61cd96fa0393b4a3c000bdbe5ce69 
2025-08-02 21:18:29.193 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=03a61cd96fa0393b4a3c000bdbe5ce69, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@role='option'], type=By.xpath), command=findElements, createdDate=2025-08-02T18:18:29.193745988, enableHealing=false) 
2025-08-02 21:18:29.260  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.xpath(//*[@role =  'option' and .='Common Zone 02']) 
2025-08-02 21:18:29.260 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: //*[@role =  'option' and .='Common Zone 02'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:29.263 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: ca3782e68d09d43d8ea8c31ff44e22eb 
2025-08-02 21:18:29.264 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=ca3782e68d09d43d8ea8c31ff44e22eb, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@role =  'option' and .='Common Zone 02'], type=By.xpath), command=findElements, createdDate=2025-08-02T18:18:29.264862004, enableHealing=false) 
2025-08-02 21:18:29.337  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:29.338 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:29.338 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:29.340 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:29.340183123, enableHealing=true) 
2025-08-02 21:18:29.435  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.xpath(//*[@role =  'option' and .='Common Zone 02']) 
2025-08-02 21:18:29.435 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: //*[@role =  'option' and .='Common Zone 02'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:29.436 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: ca3782e68d09d43d8ea8c31ff44e22eb 
2025-08-02 21:18:29.438 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=ca3782e68d09d43d8ea8c31ff44e22eb, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@role =  'option' and .='Common Zone 02'], type=By.xpath), command=findElements, createdDate=2025-08-02T18:18:29.438114928, enableHealing=false) 
2025-08-02 21:18:29.529  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.xpath(//*[@role =  'option' and .='Common Zone 02']) 
2025-08-02 21:18:29.530 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: //*[@role =  'option' and .='Common Zone 02'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:29.530 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: f254b13549be054fd337f33aea613995 
2025-08-02 21:18:29.532 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=f254b13549be054fd337f33aea613995, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@role =  'option' and .='Common Zone 02'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:29.532465661, enableHealing=true) 
2025-08-02 21:18:29.801  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:29.802 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:29.802 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:29.804 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:29.804750243, enableHealing=true) 
2025-08-02 21:18:29.914  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:privateCbx) 
2025-08-02 21:18:29.915 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:privateCbx, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:29.916 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 88cb52fc18c825937effb43d12f0cece 
2025-08-02 21:18:29.918 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=88cb52fc18c825937effb43d12f0cece, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:privateCbx, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:18:29.918596813, enableHealing=false) 
2025-08-02 21:18:31.268  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.xpath(//*[@role='option']) 
2025-08-02 21:18:31.269 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: //*[@role='option'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:31.269 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 03a61cd96fa0393b4a3c000bdbe5ce69 
2025-08-02 21:18:31.271 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=03a61cd96fa0393b4a3c000bdbe5ce69, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@role='option'], type=By.xpath), command=findElements, createdDate=2025-08-02T18:18:31.271741524, enableHealing=false) 
2025-08-02 21:18:31.332  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.xpath(//*[@role =  'option' and .='No']) 
2025-08-02 21:18:31.332 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: //*[@role =  'option' and .='No'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:31.333 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 5e290d4483b09219b237b4d985fa5a60 
2025-08-02 21:18:31.334 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=5e290d4483b09219b237b4d985fa5a60, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@role =  'option' and .='No'], type=By.xpath), command=findElements, createdDate=2025-08-02T18:18:31.334867957, enableHealing=false) 
2025-08-02 21:18:31.410  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:31.411 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:31.411 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:31.413 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:31.413434032, enableHealing=true) 
2025-08-02 21:18:31.522  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.xpath(//*[@role =  'option' and .='No']) 
2025-08-02 21:18:31.523 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: //*[@role =  'option' and .='No'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:31.523 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 5e290d4483b09219b237b4d985fa5a60 
2025-08-02 21:18:31.525 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=5e290d4483b09219b237b4d985fa5a60, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@role =  'option' and .='No'], type=By.xpath), command=findElements, createdDate=2025-08-02T18:18:31.525199502, enableHealing=false) 
2025-08-02 21:18:31.624  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.xpath(//*[@role =  'option' and .='No']) 
2025-08-02 21:18:31.624 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: //*[@role =  'option' and .='No'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:31.625 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 4eee43a99b92875a0cb387176e5e65fb 
2025-08-02 21:18:31.631 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=4eee43a99b92875a0cb387176e5e65fb, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@role =  'option' and .='No'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:31.631614214, enableHealing=true) 
2025-08-02 21:18:31.887  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:31.889 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:31.890 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:31.891 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:31.891623275, enableHealing=true) 
2025-08-02 21:18:32.010  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:btnBLVSearch) 
2025-08-02 21:18:32.011 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:btnBLVSearch, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:32.012 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 3cab7cd93b4234c709b3a423839e146b 
2025-08-02 21:18:32.014 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=3cab7cd93b4234c709b3a423839e146b, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:btnBLVSearch, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:18:32.014121855, enableHealing=false) 
2025-08-02 21:18:32.137  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew) 
2025-08-02 21:18:32.137 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:32.138 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: c46244c86d1af185aa670c4f6f945d13 
2025-08-02 21:18:32.140 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=c46244c86d1af185aa670c4f6f945d13, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:18:32.140368217, enableHealing=true) 
2025-08-02 21:18:32.330  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew) 
2025-08-02 21:18:32.331 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:32.331 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: c46244c86d1af185aa670c4f6f945d13 
2025-08-02 21:18:32.333 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=c46244c86d1af185aa670c4f6f945d13, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:18:32.333863562, enableHealing=true) 
2025-08-02 21:18:32.423  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:32.424 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:32.424 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:32.426 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:32.425970858, enableHealing=true) 
2025-08-02 21:18:32.530  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:32.530 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:32.531 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:32.532 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:32.532460156, enableHealing=true) 
2025-08-02 21:18:32.629  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.xpath(//a[contains(@id,':0:linkId')]) 
2025-08-02 21:18:32.630 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: //a[contains(@id,':0:linkId')], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:32.630 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: fe7638c88e72de3fdf0effa34f604d61 
2025-08-02 21:18:32.634 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=fe7638c88e72de3fdf0effa34f604d61, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//a[contains(@id,':0:linkId')], type=By.xpath), command=findElements, createdDate=2025-08-02T18:18:32.633953510, enableHealing=false) 
2025-08-02 21:18:33.472  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.xpath(//*[.='Black List Edition']) 
2025-08-02 21:18:33.474 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: //*[.='Black List Edition'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:33.475 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 01cfa5ee438ec88df07cc1b81aac6f69 
2025-08-02 21:18:33.477 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=01cfa5ee438ec88df07cc1b81aac6f69, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[.='Black List Edition'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:33.477741298, enableHealing=true) 
2025-08-02 21:18:33.595  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:33.595 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:33.596 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:33.597 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:33.597563147, enableHealing=true) 
2025-08-02 21:18:33.724  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.xpath(//*[.='Black List Edition']) 
2025-08-02 21:18:33.724 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: //*[.='Black List Edition'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:33.725 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: c11d74604f6b3d65a8d280ead026230b 
2025-08-02 21:18:33.728 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=c11d74604f6b3d65a8d280ead026230b, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[.='Black List Edition'], type=By.xpath), command=findElements, createdDate=2025-08-02T18:18:33.728500138, enableHealing=false) 
2025-08-02 21:18:33.780  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:33.782 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:33.783 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:33.784 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:33.784397167, enableHealing=true) 
2025-08-02 21:18:33.891  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.xpath(//div[contains(@id,'listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:Sub_black_list_edition:Sub_black_list_detail_viewer:_tblResults:0:')]) 
2025-08-02 21:18:33.892 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: //div[contains(@id,'listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:Sub_black_list_edition:Sub_black_list_detail_viewer:_tblResults:0:')], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:33.892 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: b3bd307051aef7812145b4b1e52b0d2e 
2025-08-02 21:18:33.895 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=b3bd307051aef7812145b4b1e52b0d2e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//div[contains(@id,'listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:Sub_black_list_edition:Sub_black_list_detail_viewer:_tblResults:0:')], type=By.xpath), command=findElements, createdDate=2025-08-02T18:18:33.895201776, enableHealing=false) 
2025-08-02 21:18:34.119  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:34.120 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:34.120 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:34.122 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:34.122333963, enableHealing=true) 
2025-08-02 21:18:34.228  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_edition\:Sub_black_list_detail_viewer\:_tblResults\:_btnExport) 
2025-08-02 21:18:34.229 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_edition\:Sub_black_list_detail_viewer\:_tblResults\:_btnExport, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:34.230 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 23496ddfebad694dda4b240c0fc1e17d 
2025-08-02 21:18:34.232 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=23496ddfebad694dda4b240c0fc1e17d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_edition\:Sub_black_list_detail_viewer\:_tblResults\:_btnExport, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:18:34.232322931, enableHealing=false) 
2025-08-02 21:18:34.325  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:34.326 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:34.326 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:34.328 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:34.328609031, enableHealing=true) 
2025-08-02 21:18:34.497  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.xpath(//td[.='xml']//div) 
2025-08-02 21:18:34.497 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: //td[.='xml']//div, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:34.498 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 646ef6ce792606f97c5a4b3bb37a1a2d 
2025-08-02 21:18:34.501 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=646ef6ce792606f97c5a4b3bb37a1a2d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//td[.='xml']//div, type=By.xpath), command=findElements, createdDate=2025-08-02T18:18:34.501840287, enableHealing=false) 
2025-08-02 21:18:34.741  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:34.744 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:34.749 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:34.750 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:34.750678834, enableHealing=true) 
2025-08-02 21:18:34.838  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:sub_black_list_export_viewer\:_btnExport) 
2025-08-02 21:18:34.839 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:sub_black_list_export_viewer\:_btnExport, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:34.839 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: edabaec992e40f2b460009bc25957cd3 
2025-08-02 21:18:34.841 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=edabaec992e40f2b460009bc25957cd3, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:sub_black_list_export_viewer\:_btnExport, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:18:34.841816383, enableHealing=false) 
2025-08-02 21:18:39.955  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:39.956 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:39.958 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:39.960 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:39.960620742, enableHealing=true) 
2025-08-02 21:18:40.061  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_activity\:btnASearch) 
2025-08-02 21:18:40.062 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_activity\:btnASearch, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:40.063 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 8b165317420785ed2a3cbec5cd83c8bf 
2025-08-02 21:18:40.065 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=8b165317420785ed2a3cbec5cd83c8bf, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_activity\:btnASearch, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:18:40.065751319, enableHealing=false) 
2025-08-02 21:18:40.186  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:40.199 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:40.200 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:40.210 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:40.210058174, enableHealing=true) 
2025-08-02 21:18:40.377  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:40.379 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:40.379 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:40.383 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:40.383194158, enableHealing=true) 
2025-08-02 21:18:40.573  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:40.574 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:40.574 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:40.577 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:40.576444709, enableHealing=true) 
2025-08-02 21:18:40.709  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.xpath(//a[contains(text(),'Activity')]) 
2025-08-02 21:18:40.710 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: //a[contains(text(),'Activity')], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:40.710 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 4a02d50652635d27c6d7e36eff9c8683 
2025-08-02 21:18:40.713 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=4a02d50652635d27c6d7e36eff9c8683, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//a[contains(text(),'Activity')], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:40.713174762, enableHealing=true) 
2025-08-02 21:18:40.824  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:40.825 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:40.825 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:40.827 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:40.827100605, enableHealing=true) 
2025-08-02 21:18:40.920  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.xpath(//a[contains(text(),'Activity')]) 
2025-08-02 21:18:40.920 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: //a[contains(text(),'Activity')], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:40.921 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 6053d86786c2ab40973202d6aab95327 
2025-08-02 21:18:40.923 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=6053d86786c2ab40973202d6aab95327, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//a[contains(text(),'Activity')], type=By.xpath), command=findElements, createdDate=2025-08-02T18:18:40.923814635, enableHealing=false) 
2025-08-02 21:18:41.156  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_activity\:searchDateFrom) 
2025-08-02 21:18:41.157 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_activity\:searchDateFrom, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:41.158 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: a28204b873ed1bb1e1ad015b5493b52e 
2025-08-02 21:18:41.160 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=a28204b873ed1bb1e1ad015b5493b52e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_activity\:searchDateFrom, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:18:41.160214875, enableHealing=true) 
2025-08-02 21:18:46.291  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:46.292 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:46.298 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:46.300 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:46.300341735, enableHealing=true) 
2025-08-02 21:18:46.414  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_activity\:btnASearch) 
2025-08-02 21:18:46.415 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_activity\:btnASearch, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:46.415 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 8b165317420785ed2a3cbec5cd83c8bf 
2025-08-02 21:18:46.417 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=8b165317420785ed2a3cbec5cd83c8bf, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_activity\:btnASearch, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:18:46.417609655, enableHealing=false) 
2025-08-02 21:18:46.491  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:46.492 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:46.492 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:46.493 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:46.493716892, enableHealing=true) 
2025-08-02 21:18:46.607  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:46.608 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:46.610 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:46.612 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:46.612153775, enableHealing=true) 
2025-08-02 21:18:46.719  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:46.719 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:46.720 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:46.722 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:46.722059105, enableHealing=true) 
2025-08-02 21:18:46.855  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.xpath(//tbody[@id='listManagerForm:homepage_business:tabViewListManager:Tab_activity:_tblResults_data']//tr[1]//td[5]) 
2025-08-02 21:18:46.856 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: //tbody[@id='listManagerForm:homepage_business:tabViewListManager:Tab_activity:_tblResults_data']//tr[1]//td[5], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:46.864 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 2c2a25776a1cfc316b40856cd290fd79 
2025-08-02 21:18:46.866 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=2c2a25776a1cfc316b40856cd290fd79, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//tbody[@id='listManagerForm:homepage_business:tabViewListManager:Tab_activity:_tblResults_data']//tr[1]//td[5], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:46.866699377, enableHealing=true) 
2025-08-02 21:18:47.476  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:47.477 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:47.477 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:47.479 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:47.479146365, enableHealing=true) 
2025-08-02 21:18:47.606  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.xpath(//*[@title='Exit']) 
2025-08-02 21:18:47.606 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: //*[@title='Exit'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:47.607 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 0e26e61610c3b6044e168b93cc5706ee 
2025-08-02 21:18:47.608 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=0e26e61610c3b6044e168b93cc5706ee, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@title='Exit'], type=By.xpath), command=findElements, createdDate=2025-08-02T18:18:47.608708837, enableHealing=false) 
2025-08-02 21:18:47.702  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:47.703 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:47.703 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:47.705 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:47.705133852, enableHealing=true) 
2025-08-02 21:18:47.797  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.xpath(//*[@title='Exit' or @title ='Quitter']) 
2025-08-02 21:18:47.798 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: //*[@title='Exit' or @title ='Quitter'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:47.798 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: c3eb240102044bb3ca7cb914f3e9f403 
2025-08-02 21:18:47.804 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=c3eb240102044bb3ca7cb914f3e9f403, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@title='Exit' or @title ='Quitter'], type=By.xpath), command=findElements, createdDate=2025-08-02T18:18:47.804282364, enableHealing=false) 
2025-08-02 21:18:47.924  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:47.928 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml#, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:47.929 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:47.931 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml#, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:47.931069291, enableHealing=true) 
2025-08-02 21:18:48.018  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.xpath(//button[.='Yes']) 
2025-08-02 21:18:48.018 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: //button[.='Yes'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml#, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:48.026 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: a7b20c9e34ce109a835745fce2d7552c 
2025-08-02 21:18:48.028 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=a7b20c9e34ce109a835745fce2d7552c, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml#, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//button[.='Yes'], type=By.xpath), command=findElements, createdDate=2025-08-02T18:18:48.028733889, enableHealing=false) 
2025-08-02 21:18:48.444  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.cssSelector(#pageLoginForm\:login_business\:UserName) 
2025-08-02 21:18:48.449 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #pageLoginForm\:login_business\:UserName, URL(source): http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:48.449 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: b5602af340e30317feaee6de3bf83c34 
2025-08-02 21:18:48.450 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=b5602af340e30317feaee6de3bf83c34, url=http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#pageLoginForm\:login_business\:UserName, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:18:48.450790046, enableHealing=false) 
2025-08-02 21:18:48.775 DEBUG 1 - [7878-exec-3] healenium                        : [Get Reference] Request: RequestDto(locator=//*[@class='pi pi-spin pi-spinner ajax-loader'], className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml)) 
2025-08-02 21:18:48.776 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:48.776 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:48.778 DEBUG 1 - [7878-exec-3] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2541	operator-02	2025/08/02 20:17:49	2025/08/02 20:17:49	[v2] 2016/10/19	No	Yes\n \n 	2540	operator-02	2025/08/02 20:14:54	2025/08/02 20:14:54	[v1] 2007/07/19	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2541	operator-02	2025/08/02 20:17:49	2025/08/02 20:17:49	[v2] 2016/10/19	No	Yes\n \n 	2540	operator-02	2025/08/02 20:14:54	2025/08/02 20:14:54	[v1] 2007/07/19	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=9, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2541	operator-02	2025/08/02 20:17:49	2025/08/02 20:17:49	[v2] 2016/10/19	No	Yes\n \n 	2540	operator-02	2025/08/02 20:14:54	2025/08/02 20:14:54	[v1] 2007/07/19	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='j_idt1380', classes=[], index=2, innerText='', otherAttributes={style=width:50px;height:50px;position:fixed;right:7px;bottom:7px; z-index: 10000}, children=[]], Node[tag='div', id='j_idt1380_start', classes=[], index=0, innerText='\n                         ', otherAttributes={style=display:none}, children=[]], Node[tag='i', id='', classes=[pi-spin, pi, ajax-loader, pi-spinner], index=0, innerText='', otherAttributes={aria-hidden=true}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-02 21:18:48.923 DEBUG 1 - [878-exec-10] healenium                        : [Get Reference] Request: RequestDto(locator=//*[@class='pi pi-spin pi-spinner ajax-loader'], className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml)) 
2025-08-02 21:18:48.923 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:48.924 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:48.925 DEBUG 1 - [878-exec-10] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2541	operator-02	2025/08/02 20:17:49	2025/08/02 20:17:49	[v2] 2016/10/19	No	Yes\n \n 	2540	operator-02	2025/08/02 20:14:54	2025/08/02 20:14:54	[v1] 2007/07/19	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2541	operator-02	2025/08/02 20:17:49	2025/08/02 20:17:49	[v2] 2016/10/19	No	Yes\n \n 	2540	operator-02	2025/08/02 20:14:54	2025/08/02 20:14:54	[v1] 2007/07/19	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=9, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2541	operator-02	2025/08/02 20:17:49	2025/08/02 20:17:49	[v2] 2016/10/19	No	Yes\n \n 	2540	operator-02	2025/08/02 20:14:54	2025/08/02 20:14:54	[v1] 2007/07/19	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='j_idt1380', classes=[], index=2, innerText='', otherAttributes={style=width:50px;height:50px;position:fixed;right:7px;bottom:7px; z-index: 10000}, children=[]], Node[tag='div', id='j_idt1380_start', classes=[], index=0, innerText='\n                         ', otherAttributes={style=display:none}, children=[]], Node[tag='i', id='', classes=[pi-spin, pi, ajax-loader, pi-spinner], index=0, innerText='', otherAttributes={aria-hidden=true}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-02 21:18:49.033 DEBUG 1 - [7878-exec-9] healenium                        : [Get Reference] Request: RequestDto(locator=//*[@class='pi pi-spin pi-spinner ajax-loader'], className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml)) 
2025-08-02 21:18:49.034 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:49.035 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:49.036 DEBUG 1 - [7878-exec-9] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2541	operator-02	2025/08/02 20:17:49	2025/08/02 20:17:49	[v2] 2016/10/19	No	Yes\n \n 	2540	operator-02	2025/08/02 20:14:54	2025/08/02 20:14:54	[v1] 2007/07/19	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2541	operator-02	2025/08/02 20:17:49	2025/08/02 20:17:49	[v2] 2016/10/19	No	Yes\n \n 	2540	operator-02	2025/08/02 20:14:54	2025/08/02 20:14:54	[v1] 2007/07/19	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=9, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2541	operator-02	2025/08/02 20:17:49	2025/08/02 20:17:49	[v2] 2016/10/19	No	Yes\n \n 	2540	operator-02	2025/08/02 20:14:54	2025/08/02 20:14:54	[v1] 2007/07/19	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='j_idt1380', classes=[], index=2, innerText='', otherAttributes={style=width:50px;height:50px;position:fixed;right:7px;bottom:7px; z-index: 10000}, children=[]], Node[tag='div', id='j_idt1380_start', classes=[], index=0, innerText='\n                         ', otherAttributes={style=display:none}, children=[]], Node[tag='i', id='', classes=[pi-spin, pi, ajax-loader, pi-spinner], index=0, innerText='', otherAttributes={aria-hidden=true}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-02 21:18:49.118  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.cssSelector(#pageLoginForm\:login_business\:UserName) 
2025-08-02 21:18:49.119 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: #pageLoginForm\:login_business\:UserName, URL(source): http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:49.119 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: b5602af340e30317feaee6de3bf83c34 
2025-08-02 21:18:49.120 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=b5602af340e30317feaee6de3bf83c34, url=http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#pageLoginForm\:login_business\:UserName, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:18:49.120831203, enableHealing=false) 
2025-08-02 21:18:49.291 DEBUG 1 - [7878-exec-7] healenium                        : [Get Reference] Request: RequestDto(locator=//*[@class='pi pi-spin pi-spinner ajax-loader'], className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml)) 
2025-08-02 21:18:49.293 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:49.293 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:49.297 DEBUG 1 - [7878-exec-7] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2541	operator-02	2025/08/02 20:17:49	2025/08/02 20:17:49	[v2] 2016/10/19	No	Yes\n \n 	2540	operator-02	2025/08/02 20:14:54	2025/08/02 20:14:54	[v1] 2007/07/19	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2541	operator-02	2025/08/02 20:17:49	2025/08/02 20:17:49	[v2] 2016/10/19	No	Yes\n \n 	2540	operator-02	2025/08/02 20:14:54	2025/08/02 20:14:54	[v1] 2007/07/19	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=9, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2541	operator-02	2025/08/02 20:17:49	2025/08/02 20:17:49	[v2] 2016/10/19	No	Yes\n \n 	2540	operator-02	2025/08/02 20:14:54	2025/08/02 20:14:54	[v1] 2007/07/19	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='j_idt1380', classes=[], index=2, innerText='', otherAttributes={style=width:50px;height:50px;position:fixed;right:7px;bottom:7px; z-index: 10000}, children=[]], Node[tag='div', id='j_idt1380_start', classes=[], index=0, innerText='\n                         ', otherAttributes={style=display:none}, children=[]], Node[tag='i', id='', classes=[pi-spin, pi, ajax-loader, pi-spinner], index=0, innerText='', otherAttributes={aria-hidden=true}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-02 21:18:49.406  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.cssSelector(#pageLoginForm\:login_business\:Password) 
2025-08-02 21:18:49.407 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: #pageLoginForm\:login_business\:Password, URL(source): http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:49.407 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: dfcd153e32be3bbdf9ff3d83828d14f2 
2025-08-02 21:18:49.409 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=dfcd153e32be3bbdf9ff3d83828d14f2, url=http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#pageLoginForm\:login_business\:Password, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:18:49.409120218, enableHealing=false) 
2025-08-02 21:18:49.632 DEBUG 1 - [7878-exec-4] healenium                        : [Get Reference] Request: RequestDto(locator=//*[@class='pi pi-spin pi-spinner ajax-loader'], className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml)) 
2025-08-02 21:18:49.633 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:49.633 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:49.636 DEBUG 1 - [7878-exec-4] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2541	operator-02	2025/08/02 20:17:49	2025/08/02 20:17:49	[v2] 2016/10/19	No	Yes\n \n 	2540	operator-02	2025/08/02 20:14:54	2025/08/02 20:14:54	[v1] 2007/07/19	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2541	operator-02	2025/08/02 20:17:49	2025/08/02 20:17:49	[v2] 2016/10/19	No	Yes\n \n 	2540	operator-02	2025/08/02 20:14:54	2025/08/02 20:14:54	[v1] 2007/07/19	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=9, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2541	operator-02	2025/08/02 20:17:49	2025/08/02 20:17:49	[v2] 2016/10/19	No	Yes\n \n 	2540	operator-02	2025/08/02 20:14:54	2025/08/02 20:14:54	[v1] 2007/07/19	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='j_idt1380', classes=[], index=2, innerText='', otherAttributes={style=width:50px;height:50px;position:fixed;right:7px;bottom:7px; z-index: 10000}, children=[]], Node[tag='div', id='j_idt1380_start', classes=[], index=0, innerText='\n                         ', otherAttributes={style=display:none}, children=[]], Node[tag='i', id='', classes=[pi-spin, pi, ajax-loader, pi-spinner], index=0, innerText='', otherAttributes={aria-hidden=true}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-02 21:18:49.719  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.cssSelector(#pageLoginForm\:login_business\:UserName) 
2025-08-02 21:18:49.719 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: #pageLoginForm\:login_business\:UserName, URL(source): http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:49.720 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: b5602af340e30317feaee6de3bf83c34 
2025-08-02 21:18:49.722 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=b5602af340e30317feaee6de3bf83c34, url=http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#pageLoginForm\:login_business\:UserName, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:18:49.721869086, enableHealing=false) 
2025-08-02 21:18:49.884 DEBUG 1 - [7878-exec-2] healenium                        : [Get Reference] Request: RequestDto(locator=//*[@class='pi pi-spin pi-spinner ajax-loader'], className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml)) 
2025-08-02 21:18:49.885 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:49.885 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:49.887 DEBUG 1 - [7878-exec-2] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2541	operator-02	2025/08/02 20:17:49	2025/08/02 20:17:49	[v2] 2016/10/19	No	Yes\n \n 	2540	operator-02	2025/08/02 20:14:54	2025/08/02 20:14:54	[v1] 2007/07/19	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2541	operator-02	2025/08/02 20:17:49	2025/08/02 20:17:49	[v2] 2016/10/19	No	Yes\n \n 	2540	operator-02	2025/08/02 20:14:54	2025/08/02 20:14:54	[v1] 2007/07/19	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=9, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2541	operator-02	2025/08/02 20:17:49	2025/08/02 20:17:49	[v2] 2016/10/19	No	Yes\n \n 	2540	operator-02	2025/08/02 20:14:54	2025/08/02 20:14:54	[v1] 2007/07/19	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='j_idt1380', classes=[], index=2, innerText='', otherAttributes={style=width:50px;height:50px;position:fixed;right:7px;bottom:7px; z-index: 10000}, children=[]], Node[tag='div', id='j_idt1380_start', classes=[], index=0, innerText='\n                         ', otherAttributes={style=display:none}, children=[]], Node[tag='i', id='', classes=[pi-spin, pi, ajax-loader, pi-spinner], index=0, innerText='', otherAttributes={aria-hidden=true}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-02 21:18:49.970  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.cssSelector(#pageLoginForm\:login_business\:UserName) 
2025-08-02 21:18:49.973 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #pageLoginForm\:login_business\:UserName, URL(source): http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:49.973 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: b5602af340e30317feaee6de3bf83c34 
2025-08-02 21:18:49.975 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=b5602af340e30317feaee6de3bf83c34, url=http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#pageLoginForm\:login_business\:UserName, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:18:49.974965112, enableHealing=false) 
2025-08-02 21:18:50.235 DEBUG 1 - [7878-exec-3] healenium                        : [Get Reference] Request: RequestDto(locator=//*[@class='pi pi-spin pi-spinner ajax-loader'], className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml)) 
2025-08-02 21:18:50.236 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:50.236 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:50.238 DEBUG 1 - [7878-exec-3] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2541	operator-02	2025/08/02 20:17:49	2025/08/02 20:17:49	[v2] 2016/10/19	No	Yes\n \n 	2540	operator-02	2025/08/02 20:14:54	2025/08/02 20:14:54	[v1] 2007/07/19	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2541	operator-02	2025/08/02 20:17:49	2025/08/02 20:17:49	[v2] 2016/10/19	No	Yes\n \n 	2540	operator-02	2025/08/02 20:14:54	2025/08/02 20:14:54	[v1] 2007/07/19	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=9, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2541	operator-02	2025/08/02 20:17:49	2025/08/02 20:17:49	[v2] 2016/10/19	No	Yes\n \n 	2540	operator-02	2025/08/02 20:14:54	2025/08/02 20:14:54	[v1] 2007/07/19	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='j_idt1380', classes=[], index=2, innerText='', otherAttributes={style=width:50px;height:50px;position:fixed;right:7px;bottom:7px; z-index: 10000}, children=[]], Node[tag='div', id='j_idt1380_start', classes=[], index=0, innerText='\n                         ', otherAttributes={style=display:none}, children=[]], Node[tag='i', id='', classes=[pi-spin, pi, ajax-loader, pi-spinner], index=0, innerText='', otherAttributes={aria-hidden=true}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-02 21:18:50.337  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.cssSelector(#pageLoginForm\:login_business\:Password) 
2025-08-02 21:18:50.338 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: #pageLoginForm\:login_business\:Password, URL(source): http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:50.338 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: dfcd153e32be3bbdf9ff3d83828d14f2 
2025-08-02 21:18:50.339 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=dfcd153e32be3bbdf9ff3d83828d14f2, url=http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#pageLoginForm\:login_business\:Password, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:18:50.339742968, enableHealing=false) 
2025-08-02 21:18:50.499 DEBUG 1 - [7878-exec-9] healenium                        : [Get Reference] Request: RequestDto(locator=//*[@class='pi pi-spin pi-spinner ajax-loader'], className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml)) 
2025-08-02 21:18:50.500 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:50.501 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:50.502 DEBUG 1 - [7878-exec-9] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2541	operator-02	2025/08/02 20:17:49	2025/08/02 20:17:49	[v2] 2016/10/19	No	Yes\n \n 	2540	operator-02	2025/08/02 20:14:54	2025/08/02 20:14:54	[v1] 2007/07/19	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2541	operator-02	2025/08/02 20:17:49	2025/08/02 20:17:49	[v2] 2016/10/19	No	Yes\n \n 	2540	operator-02	2025/08/02 20:14:54	2025/08/02 20:14:54	[v1] 2007/07/19	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=9, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2541	operator-02	2025/08/02 20:17:49	2025/08/02 20:17:49	[v2] 2016/10/19	No	Yes\n \n 	2540	operator-02	2025/08/02 20:14:54	2025/08/02 20:14:54	[v1] 2007/07/19	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='j_idt1380', classes=[], index=2, innerText='', otherAttributes={style=width:50px;height:50px;position:fixed;right:7px;bottom:7px; z-index: 10000}, children=[]], Node[tag='div', id='j_idt1380_start', classes=[], index=0, innerText='\n                         ', otherAttributes={style=display:none}, children=[]], Node[tag='i', id='', classes=[pi-spin, pi, ajax-loader, pi-spinner], index=0, innerText='', otherAttributes={aria-hidden=true}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-02 21:18:50.590  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.cssSelector(#pageLoginForm\:login_business\:Password) 
2025-08-02 21:18:50.591 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: #pageLoginForm\:login_business\:Password, URL(source): http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:50.592 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: dfcd153e32be3bbdf9ff3d83828d14f2 
2025-08-02 21:18:50.593 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=dfcd153e32be3bbdf9ff3d83828d14f2, url=http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#pageLoginForm\:login_business\:Password, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:18:50.593401228, enableHealing=false) 
2025-08-02 21:18:50.967 DEBUG 1 - [7878-exec-7] healenium                        : [Get Reference] Request: RequestDto(locator=//*[@class='pi pi-spin pi-spinner ajax-loader'], className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml)) 
2025-08-02 21:18:50.972 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:50.983 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:50.986 DEBUG 1 - [7878-exec-7] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2541	operator-02	2025/08/02 20:17:49	2025/08/02 20:17:49	[v2] 2016/10/19	No	Yes\n \n 	2540	operator-02	2025/08/02 20:14:54	2025/08/02 20:14:54	[v1] 2007/07/19	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2541	operator-02	2025/08/02 20:17:49	2025/08/02 20:17:49	[v2] 2016/10/19	No	Yes\n \n 	2540	operator-02	2025/08/02 20:14:54	2025/08/02 20:14:54	[v1] 2007/07/19	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=9, innerText='List Manager\n 0\n Please confirm\n \n Do you really want to exit from this session ?\n \n No\n Yes\n List Manager\n List Set Manager\n List Explorer\n Good Guys Explorer\n World Check\n Dow Jones\n SafeTrade\n Dow Jones - GG Migration\n Activity\n Black List Edition\n Id:\n Name:\n Private:\n Shared:\n Zone:\n Automatically block detections:\n Version(s) history\n 	Id	Creator	Creation Date	Modification Date	Latest Off. Date	Locked	Assigned\n \n 	2541	operator-02	2025/08/02 20:17:49	2025/08/02 20:17:49	[v2] 2016/10/19	No	Yes\n \n 	2540	operator-02	2025/08/02 20:14:54	2025/08/02 20:14:54	[v1] 2007/07/19	No	No\n Add\n Delete\n Lock\n Upgrade\n Export\n Print\n <<\n <\n >\n >>\n Pageof 12 row(s)\n It's recommended after locking to upgrade the list sets having the selected version(s) by clicking on 'Upgrade' button after Locking\n Save\n Cancel', otherAttributes={}, children=[]], Node[tag='div', id='j_idt1380', classes=[], index=2, innerText='', otherAttributes={style=width:50px;height:50px;position:fixed;right:7px;bottom:7px; z-index: 10000}, children=[]], Node[tag='div', id='j_idt1380_start', classes=[], index=0, innerText='\n                         ', otherAttributes={style=display:none}, children=[]], Node[tag='i', id='', classes=[pi-spin, pi, ajax-loader, pi-spinner], index=0, innerText='', otherAttributes={aria-hidden=true}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-02 21:18:51.360  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.cssSelector(#pageLoginForm\:login_business\:goButton) 
2025-08-02 21:18:51.361 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: #pageLoginForm\:login_business\:goButton, URL(source): http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:51.364 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: a9431ac117de5706bfee507de4c5c09f 
2025-08-02 21:18:51.366 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=a9431ac117de5706bfee507de4c5c09f, url=http://10.12.35.193:8080/AMLUI/SWAF/jsp/login.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#pageLoginForm\:login_business\:goButton, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:18:51.366213408, enableHealing=false) 
2025-08-02 21:18:53.603  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.xpath(//*[@class='ui-growl-title']) 
2025-08-02 21:18:53.603 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: //*[@class='ui-growl-title'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:53.604 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 34856943884f3a1c0a7d9f1f13b48b3e 
2025-08-02 21:18:53.605 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=34856943884f3a1c0a7d9f1f13b48b3e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='ui-growl-title'], type=By.xpath), command=findElements, createdDate=2025-08-02T18:18:53.605555555, enableHealing=false) 
2025-08-02 21:18:53.663  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:53.664 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:53.664 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:53.665 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:53.665845734, enableHealing=true) 
2025-08-02 21:18:53.770  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.xpath(//*[@class='ui-growl-title']) 
2025-08-02 21:18:53.770 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: //*[@class='ui-growl-title'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:53.771 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 34856943884f3a1c0a7d9f1f13b48b3e 
2025-08-02 21:18:53.772 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=34856943884f3a1c0a7d9f1f13b48b3e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='ui-growl-title'], type=By.xpath), command=findElements, createdDate=2025-08-02T18:18:53.772424162, enableHealing=false) 
2025-08-02 21:18:54.015  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.cssSelector(#menuform\:om_HomeListManager) 
2025-08-02 21:18:54.015 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:54.016 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-02 21:18:54.018 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=99cddc51be3dae4e40bef1d2069ba63f, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#menuform\:om_HomeListManager, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:18:54.018023943, enableHealing=true) 
2025-08-02 21:18:54.151  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:54.152 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:54.152 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:54.154 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:54.154156025, enableHealing=true) 
2025-08-02 21:18:54.259  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.cssSelector(#menuform\:om_HomeListManager) 
2025-08-02 21:18:54.259 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:54.260 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: a12e0634dcb2612225fc4ac8aef2676f 
2025-08-02 21:18:54.261 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=a12e0634dcb2612225fc4ac8aef2676f, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#menuform\:om_HomeListManager, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:18:54.261653507, enableHealing=false) 
2025-08-02 21:18:55.178  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:55.180 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:55.181 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:55.184 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:55.184275482, enableHealing=true) 
2025-08-02 21:18:55.309  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.xpath(//form[@id='topbar-right']/ul/label) 
2025-08-02 21:18:55.310 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: //form[@id='topbar-right']/ul/label, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:55.310 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 4a9504d175bc9716fe2da0e9f093ac9e 
2025-08-02 21:18:55.313 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=4a9504d175bc9716fe2da0e9f093ac9e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//form[@id='topbar-right']/ul/label, type=By.xpath), command=findElements, createdDate=2025-08-02T18:18:55.313045726, enableHealing=false) 
2025-08-02 21:18:55.405  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.xpath(//a[contains(text(),'List Manager')]) 
2025-08-02 21:18:55.406 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: //a[contains(text(),'List Manager')], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:55.411 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 8f3dc03694a381d6b07aab61a1b897c2 
2025-08-02 21:18:55.413 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=8f3dc03694a381d6b07aab61a1b897c2, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//a[contains(text(),'List Manager')], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:55.413096566, enableHealing=true) 
2025-08-02 21:18:55.542  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:55.542 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:55.543 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:55.544 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:55.544491767, enableHealing=true) 
2025-08-02 21:18:55.644  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.xpath(//a[contains(text(),'List Manager')]) 
2025-08-02 21:18:55.645 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: //a[contains(text(),'List Manager')], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:55.646 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: f57e64d4f8c44329ff06c712afa6a547 
2025-08-02 21:18:55.647 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=f57e64d4f8c44329ff06c712afa6a547, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//a[contains(text(),'List Manager')], type=By.xpath), command=findElements, createdDate=2025-08-02T18:18:55.647603089, enableHealing=false) 
2025-08-02 21:18:55.876  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:zoneCbx) 
2025-08-02 21:18:55.877 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:zoneCbx, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:55.877 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 2d9dd40db149a3b04bc59253096ddc3c 
2025-08-02 21:18:55.880 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=2d9dd40db149a3b04bc59253096ddc3c, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:zoneCbx, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:18:55.879944575, enableHealing=true) 
2025-08-02 21:18:56.018  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:56.019 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:56.024 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:56.026 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:56.026547512, enableHealing=true) 
2025-08-02 21:18:56.134  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.xpath(//span[contains(text(),'Reset')]) 
2025-08-02 21:18:56.135 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: //span[contains(text(),'Reset')], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:56.136 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: fce827adfc56329d1ee0425d66138593 
2025-08-02 21:18:56.139 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=fce827adfc56329d1ee0425d66138593, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//span[contains(text(),'Reset')], type=By.xpath), command=findElements, createdDate=2025-08-02T18:18:56.139617502, enableHealing=false) 
2025-08-02 21:18:56.351  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:56.354 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:56.355 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:56.357 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:56.357868372, enableHealing=true) 
2025-08-02 21:18:56.517  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:56.518 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:56.518 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:56.520 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:56.520146926, enableHealing=true) 
2025-08-02 21:18:56.620  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.xpath(//span[contains(text(),'Reset')]) 
2025-08-02 21:18:56.622 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: //span[contains(text(),'Reset')], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:56.622 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: fce827adfc56329d1ee0425d66138593 
2025-08-02 21:18:56.624 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=fce827adfc56329d1ee0425d66138593, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//span[contains(text(),'Reset')], type=By.xpath), command=findElements, createdDate=2025-08-02T18:18:56.624594067, enableHealing=false) 
2025-08-02 21:18:56.921  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:56.922 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:56.922 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:56.923 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:56.923912560, enableHealing=true) 
2025-08-02 21:18:57.082  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:57.083 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:57.083 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:57.085 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:57.085130558, enableHealing=true) 
2025-08-02 21:18:57.212  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:zoneCbx_label) 
2025-08-02 21:18:57.213 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:zoneCbx_label, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:57.214 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 094f76901d4289b27135807cd4a8be96 
2025-08-02 21:18:57.221 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=094f76901d4289b27135807cd4a8be96, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:zoneCbx_label, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:18:57.221912384, enableHealing=false) 
2025-08-02 21:18:58.464  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.xpath(//*[@role='option']) 
2025-08-02 21:18:58.465 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: //*[@role='option'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:58.465 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 03a61cd96fa0393b4a3c000bdbe5ce69 
2025-08-02 21:18:58.469 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=03a61cd96fa0393b4a3c000bdbe5ce69, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@role='option'], type=By.xpath), command=findElements, createdDate=2025-08-02T18:18:58.469283265, enableHealing=false) 
2025-08-02 21:18:58.526  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.xpath(//*[@role =  'option' and .='Common Zone 05']) 
2025-08-02 21:18:58.527 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: //*[@role =  'option' and .='Common Zone 05'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:58.527 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 6a2d7e872f168720a8e69f36eee17499 
2025-08-02 21:18:58.529 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=6a2d7e872f168720a8e69f36eee17499, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@role =  'option' and .='Common Zone 05'], type=By.xpath), command=findElements, createdDate=2025-08-02T18:18:58.529074080, enableHealing=false) 
2025-08-02 21:18:58.593  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:58.595 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:58.596 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:58.597 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:58.597780459, enableHealing=true) 
2025-08-02 21:18:58.672  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.xpath(//*[@role =  'option' and .='Common Zone 05']) 
2025-08-02 21:18:58.673 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: //*[@role =  'option' and .='Common Zone 05'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:58.673 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 6a2d7e872f168720a8e69f36eee17499 
2025-08-02 21:18:58.674 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=6a2d7e872f168720a8e69f36eee17499, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@role =  'option' and .='Common Zone 05'], type=By.xpath), command=findElements, createdDate=2025-08-02T18:18:58.674851974, enableHealing=false) 
2025-08-02 21:18:58.761  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.xpath(//*[@role =  'option' and .='Common Zone 05']) 
2025-08-02 21:18:58.769 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: //*[@role =  'option' and .='Common Zone 05'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:58.770 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: ******************************** 
2025-08-02 21:18:58.771 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=********************************, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@role =  'option' and .='Common Zone 05'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:58.771756722, enableHealing=true) 
2025-08-02 21:18:59.006  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:59.007 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:59.010 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:59.014 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:59.014889988, enableHealing=true) 
2025-08-02 21:18:59.135  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:nameField) 
2025-08-02 21:18:59.136 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:nameField, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:59.136 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 3f87a7532a8d1b1a77d406389919a8a8 
2025-08-02 21:18:59.138 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=3f87a7532a8d1b1a77d406389919a8a8, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:nameField, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:18:59.138514528, enableHealing=false) 
2025-08-02 21:18:59.280  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:59.281 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:59.281 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:59.283 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:59.283209066, enableHealing=true) 
2025-08-02 21:18:59.378  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:nameField) 
2025-08-02 21:18:59.379 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:nameField, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:59.380 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 3f87a7532a8d1b1a77d406389919a8a8 
2025-08-02 21:18:59.384 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=3f87a7532a8d1b1a77d406389919a8a8, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:nameField, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:18:59.384041719, enableHealing=false) 
2025-08-02 21:18:59.593  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:59.594 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:59.596 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:59.599 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:59.598922413, enableHealing=true) 
2025-08-02 21:18:59.711  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:btnBLVSearch) 
2025-08-02 21:18:59.711 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:btnBLVSearch, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:18:59.712 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 3cab7cd93b4234c709b3a423839e146b 
2025-08-02 21:18:59.714 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=3cab7cd93b4234c709b3a423839e146b, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:btnBLVSearch, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:18:59.713959106, enableHealing=false) 
2025-08-02 21:18:59.800  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew) 
2025-08-02 21:18:59.801 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:59.802 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: c46244c86d1af185aa670c4f6f945d13 
2025-08-02 21:18:59.804 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=c46244c86d1af185aa670c4f6f945d13, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:18:59.804733934, enableHealing=true) 
2025-08-02 21:18:59.958  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:18:59.960 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:18:59.961 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:18:59.963 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:18:59.962966477, enableHealing=true) 
2025-08-02 21:19:00.085  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:19:00.086 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:00.086 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:19:00.088 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:19:00.088520718, enableHealing=true) 
2025-08-02 21:19:00.215  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew) 
2025-08-02 21:19:00.216 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:00.217 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: c46244c86d1af185aa670c4f6f945d13 
2025-08-02 21:19:00.219 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=c46244c86d1af185aa670c4f6f945d13, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:00.219056218, enableHealing=true) 
2025-08-02 21:19:00.358  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:19:00.359 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:00.359 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:19:00.361 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:19:00.361373732, enableHealing=true) 
2025-08-02 21:19:00.464  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew) 
2025-08-02 21:19:00.465 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:19:00.466 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 46b8cb87b8aaffc76ae815725e7a88ad 
2025-08-02 21:19:00.468 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=46b8cb87b8aaffc76ae815725e7a88ad, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:19:00.467963385, enableHealing=false) 
2025-08-02 21:19:00.556  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:19:00.556 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:00.557 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:19:00.559 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:19:00.559048330, enableHealing=true) 
2025-08-02 21:19:00.719  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:00.720 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:19:00.720 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 169d5252cc72b1269c710e8f0c9464c7 
2025-08-02 21:19:00.722 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=169d5252cc72b1269c710e8f0c9464c7, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:19:00.722535647, enableHealing=false) 
2025-08-02 21:19:00.867  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:19:00.868 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:00.869 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:19:00.870 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:19:00.870520956, enableHealing=true) 
2025-08-02 21:19:00.962  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:00.963 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:19:00.963 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 169d5252cc72b1269c710e8f0c9464c7 
2025-08-02 21:19:00.965 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=169d5252cc72b1269c710e8f0c9464c7, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:19:00.965369997, enableHealing=false) 
2025-08-02 21:19:01.174  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:19:01.174 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:01.175 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:19:01.176 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:19:01.176472294, enableHealing=true) 
2025-08-02 21:19:01.289  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:btnBLCSave) 
2025-08-02 21:19:01.289 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:btnBLCSave, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:19:01.290 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 3de4958250014322cab2e865b798bed2 
2025-08-02 21:19:01.292 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=3de4958250014322cab2e865b798bed2, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:btnBLCSave, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:19:01.292227625, enableHealing=false) 
2025-08-02 21:19:01.485  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:19:01.485 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:01.486 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:19:01.487 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:19:01.487711408, enableHealing=true) 
2025-08-02 21:19:01.599  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:01.600 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:01.601 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:01.602 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:01.602793203, enableHealing=true) 
2025-08-02 21:19:01.723  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:01.724 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:01.724 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:01.727 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:01.727628806, enableHealing=true) 
2025-08-02 21:19:01.842  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:01.842 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:01.846 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:01.848 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:01.848150185, enableHealing=true) 
2025-08-02 21:19:01.947  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:01.948 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:01.948 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:01.950 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:01.950554920, enableHealing=true) 
2025-08-02 21:19:02.072  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:02.073 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:02.074 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:02.075 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:02.075900850, enableHealing=true) 
2025-08-02 21:19:02.175  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:02.175 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:02.176 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:02.178 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:02.178650302, enableHealing=true) 
2025-08-02 21:19:02.312  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:02.313 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:02.313 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:02.315 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:02.315241618, enableHealing=true) 
2025-08-02 21:19:02.416  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:02.417 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:02.417 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:02.422 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:02.422062683, enableHealing=true) 
2025-08-02 21:19:02.503  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:02.503 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:02.504 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:02.505 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:02.505780744, enableHealing=true) 
2025-08-02 21:19:02.594  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:02.595 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:02.596 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:02.597 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:02.597671031, enableHealing=true) 
2025-08-02 21:19:02.707  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:02.707 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:02.708 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:02.709 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:02.709785771, enableHealing=true) 
2025-08-02 21:19:02.811  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:02.812 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:02.812 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:02.814 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:02.814527028, enableHealing=true) 
2025-08-02 21:19:02.905  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:02.905 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:02.906 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:02.908 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:02.908026499, enableHealing=true) 
2025-08-02 21:19:03.011  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:03.012 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:03.012 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:03.014 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:03.014680855, enableHealing=true) 
2025-08-02 21:19:03.105  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:03.105 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:03.109 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:03.111 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:03.111297688, enableHealing=true) 
2025-08-02 21:19:03.210  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:03.211 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:03.212 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:03.215 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:03.214928586, enableHealing=true) 
2025-08-02 21:19:03.339  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:03.339 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:03.340 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:03.342 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:03.342217618, enableHealing=true) 
2025-08-02 21:19:03.432  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:03.432 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:03.433 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:03.434 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:03.434798240, enableHealing=true) 
2025-08-02 21:19:03.526  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:03.527 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:03.528 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:03.530 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:03.530624432, enableHealing=true) 
2025-08-02 21:19:03.648  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:03.649 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:03.650 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:03.651 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:03.651886150, enableHealing=true) 
2025-08-02 21:19:03.755  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:03.756 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:03.756 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:03.759 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:03.758982429, enableHealing=true) 
2025-08-02 21:19:03.855  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:03.857 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:03.857 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:03.859 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:03.859915699, enableHealing=true) 
2025-08-02 21:19:03.958  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:03.967 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:03.971 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:03.973 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:03.973636714, enableHealing=true) 
2025-08-02 21:19:04.079  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:04.081 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:04.086 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:04.087 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:04.087833353, enableHealing=true) 
2025-08-02 21:19:04.175  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:04.176 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:04.177 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:04.178 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:04.178692499, enableHealing=true) 
2025-08-02 21:19:04.273  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:04.274 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:04.274 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:04.276 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:04.276672109, enableHealing=true) 
2025-08-02 21:19:04.388  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:04.388 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:04.389 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:04.391 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:04.391238467, enableHealing=true) 
2025-08-02 21:19:04.487  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:04.490 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:04.490 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:04.494 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:04.493974120, enableHealing=true) 
2025-08-02 21:19:04.606  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:04.606 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:04.607 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:04.609 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:04.609008201, enableHealing=true) 
2025-08-02 21:19:04.713  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:04.714 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:04.714 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:04.716 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:04.716524199, enableHealing=true) 
2025-08-02 21:19:04.804  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:04.805 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:04.806 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:04.809 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:04.809443150, enableHealing=true) 
2025-08-02 21:19:04.916  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:04.916 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:04.917 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:04.919 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:04.919472976, enableHealing=true) 
2025-08-02 21:19:05.029  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:05.030 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:05.030 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:05.034 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:05.034669566, enableHealing=true) 
2025-08-02 21:19:05.162  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:05.162 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:05.163 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:05.169 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:05.168957732, enableHealing=true) 
2025-08-02 21:19:05.285  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:05.286 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:05.286 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:05.288 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:05.288017920, enableHealing=true) 
2025-08-02 21:19:05.425  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:05.428 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:05.428 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:05.433 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:05.433348251, enableHealing=true) 
2025-08-02 21:19:05.528  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:05.528 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:05.529 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:05.531 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:05.531312760, enableHealing=true) 
2025-08-02 21:19:05.639  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:05.642 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:05.643 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:05.647 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:05.647042477, enableHealing=true) 
2025-08-02 21:19:05.750  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:05.750 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:05.757 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:05.760 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:05.760908999, enableHealing=true) 
2025-08-02 21:19:05.879  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:05.880 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:05.880 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:05.882 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:05.882622623, enableHealing=true) 
2025-08-02 21:19:06.236  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:06.237 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:06.237 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:06.239 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:06.239833987, enableHealing=true) 
2025-08-02 21:19:06.382  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:06.383 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:06.385 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:06.390 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:06.390412187, enableHealing=true) 
2025-08-02 21:19:06.518  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:06.524 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:06.529 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:06.530 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:06.530756362, enableHealing=true) 
2025-08-02 21:19:06.642  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:06.643 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:06.643 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:06.645 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:06.645519230, enableHealing=true) 
2025-08-02 21:19:06.753  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:06.754 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:06.756 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:06.758 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:06.758209092, enableHealing=true) 
2025-08-02 21:19:06.905  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:06.906 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:06.907 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:06.909 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:06.908953400, enableHealing=true) 
2025-08-02 21:19:07.031  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:07.031 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:07.032 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:07.033 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:07.033868187, enableHealing=true) 
2025-08-02 21:19:07.155  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:07.155 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:07.156 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:07.157 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:07.157928930, enableHealing=true) 
2025-08-02 21:19:07.293  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:07.294 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:07.294 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:07.296 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:07.296141197, enableHealing=true) 
2025-08-02 21:19:07.407  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:07.407 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:07.408 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:07.410 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:07.410034121, enableHealing=true) 
2025-08-02 21:19:07.514  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:07.515 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:07.515 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:07.517 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:07.517303806, enableHealing=true) 
2025-08-02 21:19:07.618  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:07.618 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:07.619 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:07.620 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:07.620820099, enableHealing=true) 
2025-08-02 21:19:07.721  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:07.722 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:07.722 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:07.724 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:07.724387694, enableHealing=true) 
2025-08-02 21:19:07.815  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:07.817 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:07.817 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:07.819 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:07.819655465, enableHealing=true) 
2025-08-02 21:19:07.936  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:07.937 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:07.937 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:07.939 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:07.939385987, enableHealing=true) 
2025-08-02 21:19:08.046  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:08.047 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:08.047 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:08.049 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:08.049479416, enableHealing=true) 
2025-08-02 21:19:08.137  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:08.138 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:08.138 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:08.140 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:08.140625777, enableHealing=true) 
2025-08-02 21:19:08.245  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:08.246 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:08.246 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:08.248 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:08.248170676, enableHealing=true) 
2025-08-02 21:19:08.373  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:08.374 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:08.374 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:08.377 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:08.376976362, enableHealing=true) 
2025-08-02 21:19:08.487  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:08.487 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:08.488 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:08.490 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:08.490302456, enableHealing=true) 
2025-08-02 21:19:08.623  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:08.624 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:08.624 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:08.626 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:08.626846938, enableHealing=true) 
2025-08-02 21:19:08.728  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:08.729 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:08.729 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:08.731 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:08.731487888, enableHealing=true) 
2025-08-02 21:19:08.837  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:08.838 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:08.838 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:08.841 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:08.841299303, enableHealing=true) 
2025-08-02 21:19:08.944  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:08.945 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:08.946 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:08.948 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:08.948316475, enableHealing=true) 
2025-08-02 21:19:09.071  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:09.072 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:09.072 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:09.074 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:09.074401122, enableHealing=true) 
2025-08-02 21:19:09.179  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:09.180 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:09.181 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:09.184 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:09.184861970, enableHealing=true) 
2025-08-02 21:19:09.336  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:09.337 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:09.338 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:09.339 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:09.339630583, enableHealing=true) 
2025-08-02 21:19:09.506  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:09.506 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:09.507 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:09.508 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:09.508843335, enableHealing=true) 
2025-08-02 21:19:09.666  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:09.668 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:09.668 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:09.670 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:09.670637708, enableHealing=true) 
2025-08-02 21:19:09.794  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:09.795 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:09.795 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:09.797 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:09.797440992, enableHealing=true) 
2025-08-02 21:19:09.917  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:09.918 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:09.919 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:09.920 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:09.920512284, enableHealing=true) 
2025-08-02 21:19:10.011  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:10.012 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:10.012 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:10.014 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:10.014574394, enableHealing=true) 
2025-08-02 21:19:10.107  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:10.107 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:10.108 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:10.109 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:10.109597053, enableHealing=true) 
2025-08-02 21:19:10.202  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:10.203 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:10.203 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:10.205 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:10.205151138, enableHealing=true) 
2025-08-02 21:19:10.329  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:10.330 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:10.331 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:10.333 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:10.333435598, enableHealing=true) 
2025-08-02 21:19:10.455  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:10.456 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:10.456 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:10.459 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:10.459226129, enableHealing=true) 
2025-08-02 21:19:10.555  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:10.556 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:10.556 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:10.559 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:10.559218542, enableHealing=true) 
2025-08-02 21:19:10.675  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:10.675 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:10.676 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:10.678 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:10.677987515, enableHealing=true) 
2025-08-02 21:19:10.770  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:10.770 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:10.771 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:10.772 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:10.772840565, enableHealing=true) 
2025-08-02 21:19:10.862  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:10.865 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:10.866 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:10.873 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:10.873633219, enableHealing=true) 
2025-08-02 21:19:10.984  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:10.985 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:10.985 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:10.987 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:10.987077719, enableHealing=true) 
2025-08-02 21:19:11.090  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:11.090 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:11.091 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:11.093 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:11.093410656, enableHealing=true) 
2025-08-02 21:19:11.211  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:11.211 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:11.212 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:11.214 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:11.214221233, enableHealing=true) 
2025-08-02 21:19:11.323  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:11.323 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:11.324 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:11.326 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:11.326517375, enableHealing=true) 
2025-08-02 21:19:11.428  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:11.429 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:11.431 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:11.433 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:11.433029121, enableHealing=true) 
2025-08-02 21:19:11.564  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-02 21:19:11.564 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:11.565 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-02 21:19:11.566 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:11.566926667, enableHealing=true) 
2025-08-02 21:19:11.660  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:19:11.661 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:11.661 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:19:11.662 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:19:11.662801870, enableHealing=true) 
2025-08-02 21:19:11.793  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:btnBLCSave) 
2025-08-02 21:19:11.798 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:btnBLCSave, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:19:11.799 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 3de4958250014322cab2e865b798bed2 
2025-08-02 21:19:11.808 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=3de4958250014322cab2e865b798bed2, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:btnBLCSave, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:19:11.808336592, enableHealing=false) 
2025-08-02 21:19:12.091  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:19:12.092 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:12.093 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:19:12.095 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:19:12.095907984, enableHealing=true) 
2025-08-02 21:19:12.290  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:19:12.294 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:12.304 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:19:12.308 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:19:12.307938095, enableHealing=true) 
2025-08-02 21:19:12.500  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:19:12.501 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:12.502 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:19:12.503 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:19:12.503834099, enableHealing=true) 
2025-08-02 21:19:12.615  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.cssSelector(#menuform\:om_HomeListManager) 
2025-08-02 21:19:12.616 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:12.616 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-02 21:19:12.623 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=99cddc51be3dae4e40bef1d2069ba63f, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#menuform\:om_HomeListManager, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:12.621948411, enableHealing=true) 
2025-08-02 21:19:12.762  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:19:12.763 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:12.763 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:19:12.766 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:19:12.766762358, enableHealing=true) 
2025-08-02 21:19:12.862  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.cssSelector(#menuform\:om_HomeListManager) 
2025-08-02 21:19:12.866 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:19:12.866 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: a12e0634dcb2612225fc4ac8aef2676f 
2025-08-02 21:19:12.868 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=a12e0634dcb2612225fc4ac8aef2676f, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#menuform\:om_HomeListManager, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:19:12.868723561, enableHealing=false) 
2025-08-02 21:19:13.945  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:19:13.946 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:13.949 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:19:13.952 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:19:13.952750214, enableHealing=true) 
2025-08-02 21:19:14.096  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.xpath(//form[@id='topbar-right']/ul/label) 
2025-08-02 21:19:14.097 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: //form[@id='topbar-right']/ul/label, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:19:14.097 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 4a9504d175bc9716fe2da0e9f093ac9e 
2025-08-02 21:19:14.099 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=4a9504d175bc9716fe2da0e9f093ac9e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//form[@id='topbar-right']/ul/label, type=By.xpath), command=findElements, createdDate=2025-08-02T18:19:14.099540760, enableHealing=false) 
2025-08-02 21:19:14.205  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.xpath(//a[contains(text(),'List Manager')]) 
2025-08-02 21:19:14.206 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: //a[contains(text(),'List Manager')], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:14.206 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 8f3dc03694a381d6b07aab61a1b897c2 
2025-08-02 21:19:14.211 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=8f3dc03694a381d6b07aab61a1b897c2, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//a[contains(text(),'List Manager')], type=By.xpath), command=findElement, createdDate=2025-08-02T18:19:14.211701674, enableHealing=true) 
2025-08-02 21:19:14.377  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:19:14.379 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:14.379 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:19:14.381 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:19:14.380908042, enableHealing=true) 
2025-08-02 21:19:14.492  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.xpath(//a[contains(text(),'List Manager')]) 
2025-08-02 21:19:14.493 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: //a[contains(text(),'List Manager')], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:19:14.496 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: f57e64d4f8c44329ff06c712afa6a547 
2025-08-02 21:19:14.498 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=f57e64d4f8c44329ff06c712afa6a547, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//a[contains(text(),'List Manager')], type=By.xpath), command=findElements, createdDate=2025-08-02T18:19:14.498537729, enableHealing=false) 
2025-08-02 21:19:14.745  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:zoneCbx) 
2025-08-02 21:19:14.746 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:zoneCbx, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:14.747 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 2d9dd40db149a3b04bc59253096ddc3c 
2025-08-02 21:19:14.749 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=2d9dd40db149a3b04bc59253096ddc3c, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:zoneCbx, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:14.749694899, enableHealing=true) 
2025-08-02 21:19:14.894  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:19:14.895 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:14.895 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:19:14.897 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:19:14.897199181, enableHealing=true) 
2025-08-02 21:19:15.000  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.xpath(//span[contains(text(),'Reset')]) 
2025-08-02 21:19:15.002 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: //span[contains(text(),'Reset')], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:19:15.002 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: fce827adfc56329d1ee0425d66138593 
2025-08-02 21:19:15.004 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=fce827adfc56329d1ee0425d66138593, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//span[contains(text(),'Reset')], type=By.xpath), command=findElements, createdDate=2025-08-02T18:19:15.004523352, enableHealing=false) 
2025-08-02 21:19:15.244  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:19:15.251 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:15.252 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:19:15.253 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:19:15.253535215, enableHealing=true) 
2025-08-02 21:19:15.439  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:19:15.444 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:15.444 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:19:15.448 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:19:15.448611978, enableHealing=true) 
2025-08-02 21:19:15.569  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.xpath(//span[contains(text(),'Reset')]) 
2025-08-02 21:19:15.570 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: //span[contains(text(),'Reset')], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:19:15.571 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: fce827adfc56329d1ee0425d66138593 
2025-08-02 21:19:15.573 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=fce827adfc56329d1ee0425d66138593, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//span[contains(text(),'Reset')], type=By.xpath), command=findElements, createdDate=2025-08-02T18:19:15.573805444, enableHealing=false) 
2025-08-02 21:19:15.771  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:19:15.772 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:15.772 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:19:15.776 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:19:15.776022264, enableHealing=true) 
2025-08-02 21:19:15.935  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:19:15.935 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:15.936 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:19:15.937 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:19:15.937506946, enableHealing=true) 
2025-08-02 21:19:16.043  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:zoneCbx_label) 
2025-08-02 21:19:16.044 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:zoneCbx_label, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:19:16.044 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 094f76901d4289b27135807cd4a8be96 
2025-08-02 21:19:16.046 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=094f76901d4289b27135807cd4a8be96, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:zoneCbx_label, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:19:16.046182385, enableHealing=false) 
2025-08-02 21:19:17.276  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.xpath(//*[@role='option']) 
2025-08-02 21:19:17.277 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: //*[@role='option'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:19:17.277 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 03a61cd96fa0393b4a3c000bdbe5ce69 
2025-08-02 21:19:17.279 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=03a61cd96fa0393b4a3c000bdbe5ce69, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@role='option'], type=By.xpath), command=findElements, createdDate=2025-08-02T18:19:17.279171593, enableHealing=false) 
2025-08-02 21:19:17.358  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.xpath(//*[@role =  'option' and .='Common Zone 05']) 
2025-08-02 21:19:17.358 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: //*[@role =  'option' and .='Common Zone 05'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:19:17.359 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 6a2d7e872f168720a8e69f36eee17499 
2025-08-02 21:19:17.360 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=6a2d7e872f168720a8e69f36eee17499, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@role =  'option' and .='Common Zone 05'], type=By.xpath), command=findElements, createdDate=2025-08-02T18:19:17.360388157, enableHealing=false) 
2025-08-02 21:19:17.409  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:19:17.409 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:17.410 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:19:17.411 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:19:17.411540317, enableHealing=true) 
2025-08-02 21:19:17.489  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.xpath(//*[@role =  'option' and .='Common Zone 05']) 
2025-08-02 21:19:17.490 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: //*[@role =  'option' and .='Common Zone 05'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:19:17.490 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 6a2d7e872f168720a8e69f36eee17499 
2025-08-02 21:19:17.492 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=6a2d7e872f168720a8e69f36eee17499, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@role =  'option' and .='Common Zone 05'], type=By.xpath), command=findElements, createdDate=2025-08-02T18:19:17.492205055, enableHealing=false) 
2025-08-02 21:19:17.573  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.xpath(//*[@role =  'option' and .='Common Zone 05']) 
2025-08-02 21:19:17.574 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: //*[@role =  'option' and .='Common Zone 05'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:17.575 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: ******************************** 
2025-08-02 21:19:17.576 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=********************************, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@role =  'option' and .='Common Zone 05'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:19:17.576551876, enableHealing=true) 
2025-08-02 21:19:17.805  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:19:17.805 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:17.806 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:19:17.807 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:19:17.807427231, enableHealing=true) 
2025-08-02 21:19:17.926  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:nameField) 
2025-08-02 21:19:17.926 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:nameField, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:19:17.927 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 3f87a7532a8d1b1a77d406389919a8a8 
2025-08-02 21:19:17.928 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=3f87a7532a8d1b1a77d406389919a8a8, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:nameField, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:19:17.928906210, enableHealing=false) 
2025-08-02 21:19:18.067  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:19:18.068 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:18.069 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:19:18.070 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:19:18.070497597, enableHealing=true) 
2025-08-02 21:19:18.169  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:nameField) 
2025-08-02 21:19:18.169 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:nameField, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:19:18.173 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 3f87a7532a8d1b1a77d406389919a8a8 
2025-08-02 21:19:18.175 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=3f87a7532a8d1b1a77d406389919a8a8, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:nameField, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:19:18.175001327, enableHealing=false) 
2025-08-02 21:19:18.403  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:19:18.404 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:18.405 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:19:18.406 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:19:18.406585417, enableHealing=true) 
2025-08-02 21:19:18.518  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:btnBLVSearch) 
2025-08-02 21:19:18.519 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:btnBLVSearch, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:19:18.519 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 3cab7cd93b4234c709b3a423839e146b 
2025-08-02 21:19:18.521 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=3cab7cd93b4234c709b3a423839e146b, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:btnBLVSearch, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:19:18.521727879, enableHealing=false) 
2025-08-02 21:19:18.629  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew) 
2025-08-02 21:19:18.630 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:18.630 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: c46244c86d1af185aa670c4f6f945d13 
2025-08-02 21:19:18.632 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=c46244c86d1af185aa670c4f6f945d13, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:18.632111113, enableHealing=true) 
2025-08-02 21:19:18.796  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew) 
2025-08-02 21:19:18.797 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:18.797 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: c46244c86d1af185aa670c4f6f945d13 
2025-08-02 21:19:18.800 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=c46244c86d1af185aa670c4f6f945d13, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:18.799976414, enableHealing=true) 
2025-08-02 21:19:18.901  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:19:18.901 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:18.902 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:19:18.903 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:19:18.903802410, enableHealing=true) 
2025-08-02 21:19:18.989  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:19:18.989 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:18.990 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:19:18.992 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:19:18.992011625, enableHealing=true) 
2025-08-02 21:19:19.097  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.xpath(//tbody//tr//td[.='ListName-653444464']) 
2025-08-02 21:19:19.098 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: //tbody//tr//td[.='ListName-653444464'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:19:19.098 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: aead01f58d446551363f3054ef77de6d 
2025-08-02 21:19:19.101 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=aead01f58d446551363f3054ef77de6d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//tbody//tr//td[.='ListName-653444464'], type=By.xpath), command=findElements, createdDate=2025-08-02T18:19:19.099886124, enableHealing=false) 
2025-08-02 21:19:19.161  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.cssSelector(#menuform\:om_HomeSWIFTConfiguration) 
2025-08-02 21:19:19.161 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #menuform\:om_HomeSWIFTConfiguration, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:19.162 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 49f4ec8f4aee2a759a032cffc140c575 
2025-08-02 21:19:19.163 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=49f4ec8f4aee2a759a032cffc140c575, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#menuform\:om_HomeSWIFTConfiguration, type=By.cssSelector), command=findElement, createdDate=2025-08-02T18:19:19.163507508, enableHealing=true) 
2025-08-02 21:19:19.279  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:19:19.279 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:19.280 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:19:19.281 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:19:19.281416609, enableHealing=true) 
2025-08-02 21:19:19.386  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.cssSelector(#menuform\:om_HomeSWIFTConfiguration) 
2025-08-02 21:19:19.386 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: #menuform\:om_HomeSWIFTConfiguration, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:19:19.387 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: b20e65799f3b24cff7e6af7205879e3d 
2025-08-02 21:19:19.388 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=b20e65799f3b24cff7e6af7205879e3d, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#menuform\:om_HomeSWIFTConfiguration, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:19:19.388554071, enableHealing=false) 
2025-08-02 21:19:19.915  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:19:19.918 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:19.919 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:19:19.922 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:19:19.922815763, enableHealing=true) 
2025-08-02 21:19:20.027  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.xpath(//form[@id='topbar-right']/ul/label) 
2025-08-02 21:19:20.027 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: //form[@id='topbar-right']/ul/label, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:19:20.028 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 4a9504d175bc9716fe2da0e9f093ac9e 
2025-08-02 21:19:20.029 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=4a9504d175bc9716fe2da0e9f093ac9e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//form[@id='topbar-right']/ul/label, type=By.xpath), command=findElements, createdDate=2025-08-02T18:19:20.029607666, enableHealing=false) 
2025-08-02 21:19:20.129  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-02 21:19:20.129 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-02 21:19:20.130 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-02 21:19:20.131 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-02T18:19:20.131192111, enableHealing=true) 
2025-08-02 21:19:20.225  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.cssSelector(#swiftConfigurationForm\:homepage_business\:zoneCbx_label) 
2025-08-02 21:19:20.230 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: #swiftConfigurationForm\:homepage_business\:zoneCbx_label, URL(source): http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-02 21:19:20.231 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: c9525f05f743b5e61fadbc818f8e5804 
2025-08-02 21:19:20.234 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=c9525f05f743b5e61fadbc818f8e5804, url=http://10.12.35.193:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#swiftConfigurationForm\:homepage_business\:zoneCbx_label, type=By.cssSelector), command=findElements, createdDate=2025-08-02T18:19:20.234004016, enableHealing=false) 
