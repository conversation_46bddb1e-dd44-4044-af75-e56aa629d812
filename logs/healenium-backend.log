2025-08-03 22:56:15.696 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:15.728 DEBUG 1 - [7878-exec-6] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='Group Manager\n Group Manager\n Name:\n Description:\n Waiting Checker Action:\n Group content\n Operator Login Name:\n Operator Group Name:\n Reset\n Search\n  \n 	\n  \n Id	\n  \n Name	\n  \n Description	\n  \n Email	\n  \n Profile	Zone\n \n 	211	selenium-random-group-301909915	Random group for selenium testing		Test-Profile-761583025	Zone639487746\n New\n Remove\n <<\n <\n >\n >>\n Page\n of 1\n 1 row(s)', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='Group Manager\n Group Manager\n Name:\n Description:\n Waiting Checker Action:\n Group content\n Operator Login Name:\n Operator Group Name:\n Reset\n Search\n  \n 	\n  \n Id	\n  \n Name	\n  \n Description	\n  \n Email	\n  \n Profile	Zone\n \n 	211	selenium-random-group-301909915	Random group for selenium testing		Test-Profile-761583025	Zone639487746\n New\n Remove\n <<\n <\n >\n >>\n Page\n of 1\n 1 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=9, innerText='Group Manager\n Group Manager\n Name:\n Description:\n Waiting Checker Action:\n Group content\n Operator Login Name:\n Operator Group Name:\n Reset\n Search\n  \n 	\n  \n Id	\n  \n Name	\n  \n Description	\n  \n Email	\n  \n Profile	Zone\n \n 	211	selenium-random-group-301909915	Random group for selenium testing		Test-Profile-761583025	Zone639487746\n New\n Remove\n <<\n <\n >\n >>\n Page\n of 1\n 1 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='j_idt255', classes=[], index=2, innerText='', otherAttributes={style=width:50px;height:50px;position:fixed;right:7px;bottom:7px; z-index: 10000}, children=[]], Node[tag='div', id='j_idt255_start', classes=[], index=0, innerText='\n                         ', otherAttributes={style=display:none}, children=[]], Node[tag='i', id='', classes=[pi-spin, pi, ajax-loader, pi-spinner], index=0, innerText='', otherAttributes={aria-hidden=true}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:56:15.823  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.cssSelector(#pageLoginForm\:login_business\:UserName) 
2025-08-03 22:56:15.825 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: #pageLoginForm\:login_business\:UserName, URL(source): http://************:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:15.827 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: b5602af340e30317feaee6de3bf83c34 
2025-08-03 22:56:15.830 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=b5602af340e30317feaee6de3bf83c34, url=http://************:8080/AMLUI/SWAF/jsp/login.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#pageLoginForm\:login_business\:UserName, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:15.830103098, enableHealing=false) 
2025-08-03 22:56:16.070 DEBUG 1 - [7878-exec-4] healenium                        : [Get Reference] Request: RequestDto(locator=//*[@class='pi pi-spin pi-spinner ajax-loader'], className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://************:8080/AMLUI/SWAF/jsp/login.xhtml)) 
2025-08-03 22:56:16.072 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:16.073 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:16.077 DEBUG 1 - [7878-exec-4] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='Group Manager\n Group Manager\n Name:\n Description:\n Waiting Checker Action:\n Group content\n Operator Login Name:\n Operator Group Name:\n Reset\n Search\n  \n 	\n  \n Id	\n  \n Name	\n  \n Description	\n  \n Email	\n  \n Profile	Zone\n \n 	211	selenium-random-group-301909915	Random group for selenium testing		Test-Profile-761583025	Zone639487746\n New\n Remove\n <<\n <\n >\n >>\n Page\n of 1\n 1 row(s)', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='Group Manager\n Group Manager\n Name:\n Description:\n Waiting Checker Action:\n Group content\n Operator Login Name:\n Operator Group Name:\n Reset\n Search\n  \n 	\n  \n Id	\n  \n Name	\n  \n Description	\n  \n Email	\n  \n Profile	Zone\n \n 	211	selenium-random-group-301909915	Random group for selenium testing		Test-Profile-761583025	Zone639487746\n New\n Remove\n <<\n <\n >\n >>\n Page\n of 1\n 1 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=9, innerText='Group Manager\n Group Manager\n Name:\n Description:\n Waiting Checker Action:\n Group content\n Operator Login Name:\n Operator Group Name:\n Reset\n Search\n  \n 	\n  \n Id	\n  \n Name	\n  \n Description	\n  \n Email	\n  \n Profile	Zone\n \n 	211	selenium-random-group-301909915	Random group for selenium testing		Test-Profile-761583025	Zone639487746\n New\n Remove\n <<\n <\n >\n >>\n Page\n of 1\n 1 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='j_idt255', classes=[], index=2, innerText='', otherAttributes={style=width:50px;height:50px;position:fixed;right:7px;bottom:7px; z-index: 10000}, children=[]], Node[tag='div', id='j_idt255_start', classes=[], index=0, innerText='\n                         ', otherAttributes={style=display:none}, children=[]], Node[tag='i', id='', classes=[pi-spin, pi, ajax-loader, pi-spinner], index=0, innerText='', otherAttributes={aria-hidden=true}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:56:16.211  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.cssSelector(#pageLoginForm\:login_business\:Password) 
2025-08-03 22:56:16.212 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #pageLoginForm\:login_business\:Password, URL(source): http://************:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:16.212 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: dfcd153e32be3bbdf9ff3d83828d14f2 
2025-08-03 22:56:16.214 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=dfcd153e32be3bbdf9ff3d83828d14f2, url=http://************:8080/AMLUI/SWAF/jsp/login.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#pageLoginForm\:login_business\:Password, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:16.214720140, enableHealing=false) 
2025-08-03 22:56:16.423 DEBUG 1 - [7878-exec-8] healenium                        : [Get Reference] Request: RequestDto(locator=//*[@class='pi pi-spin pi-spinner ajax-loader'], className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://************:8080/AMLUI/SWAF/jsp/login.xhtml)) 
2025-08-03 22:56:16.424 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:16.425 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:16.434 DEBUG 1 - [7878-exec-8] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='Group Manager\n Group Manager\n Name:\n Description:\n Waiting Checker Action:\n Group content\n Operator Login Name:\n Operator Group Name:\n Reset\n Search\n  \n 	\n  \n Id	\n  \n Name	\n  \n Description	\n  \n Email	\n  \n Profile	Zone\n \n 	211	selenium-random-group-301909915	Random group for selenium testing		Test-Profile-761583025	Zone639487746\n New\n Remove\n <<\n <\n >\n >>\n Page\n of 1\n 1 row(s)', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='Group Manager\n Group Manager\n Name:\n Description:\n Waiting Checker Action:\n Group content\n Operator Login Name:\n Operator Group Name:\n Reset\n Search\n  \n 	\n  \n Id	\n  \n Name	\n  \n Description	\n  \n Email	\n  \n Profile	Zone\n \n 	211	selenium-random-group-301909915	Random group for selenium testing		Test-Profile-761583025	Zone639487746\n New\n Remove\n <<\n <\n >\n >>\n Page\n of 1\n 1 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=9, innerText='Group Manager\n Group Manager\n Name:\n Description:\n Waiting Checker Action:\n Group content\n Operator Login Name:\n Operator Group Name:\n Reset\n Search\n  \n 	\n  \n Id	\n  \n Name	\n  \n Description	\n  \n Email	\n  \n Profile	Zone\n \n 	211	selenium-random-group-301909915	Random group for selenium testing		Test-Profile-761583025	Zone639487746\n New\n Remove\n <<\n <\n >\n >>\n Page\n of 1\n 1 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='j_idt255', classes=[], index=2, innerText='', otherAttributes={style=width:50px;height:50px;position:fixed;right:7px;bottom:7px; z-index: 10000}, children=[]], Node[tag='div', id='j_idt255_start', classes=[], index=0, innerText='\n                         ', otherAttributes={style=display:none}, children=[]], Node[tag='i', id='', classes=[pi-spin, pi, ajax-loader, pi-spinner], index=0, innerText='', otherAttributes={aria-hidden=true}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:56:16.519  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.cssSelector(#pageLoginForm\:login_business\:UserName) 
2025-08-03 22:56:16.520 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #pageLoginForm\:login_business\:UserName, URL(source): http://************:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:16.521 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: b5602af340e30317feaee6de3bf83c34 
2025-08-03 22:56:16.525 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=b5602af340e30317feaee6de3bf83c34, url=http://************:8080/AMLUI/SWAF/jsp/login.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#pageLoginForm\:login_business\:UserName, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:16.525732017, enableHealing=false) 
2025-08-03 22:56:16.740 DEBUG 1 - [7878-exec-9] healenium                        : [Get Reference] Request: RequestDto(locator=//*[@class='pi pi-spin pi-spinner ajax-loader'], className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://************:8080/AMLUI/SWAF/jsp/login.xhtml)) 
2025-08-03 22:56:16.742 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:16.742 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:16.746 DEBUG 1 - [7878-exec-9] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='Group Manager\n Group Manager\n Name:\n Description:\n Waiting Checker Action:\n Group content\n Operator Login Name:\n Operator Group Name:\n Reset\n Search\n  \n 	\n  \n Id	\n  \n Name	\n  \n Description	\n  \n Email	\n  \n Profile	Zone\n \n 	211	selenium-random-group-301909915	Random group for selenium testing		Test-Profile-761583025	Zone639487746\n New\n Remove\n <<\n <\n >\n >>\n Page\n of 1\n 1 row(s)', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='Group Manager\n Group Manager\n Name:\n Description:\n Waiting Checker Action:\n Group content\n Operator Login Name:\n Operator Group Name:\n Reset\n Search\n  \n 	\n  \n Id	\n  \n Name	\n  \n Description	\n  \n Email	\n  \n Profile	Zone\n \n 	211	selenium-random-group-301909915	Random group for selenium testing		Test-Profile-761583025	Zone639487746\n New\n Remove\n <<\n <\n >\n >>\n Page\n of 1\n 1 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=9, innerText='Group Manager\n Group Manager\n Name:\n Description:\n Waiting Checker Action:\n Group content\n Operator Login Name:\n Operator Group Name:\n Reset\n Search\n  \n 	\n  \n Id	\n  \n Name	\n  \n Description	\n  \n Email	\n  \n Profile	Zone\n \n 	211	selenium-random-group-301909915	Random group for selenium testing		Test-Profile-761583025	Zone639487746\n New\n Remove\n <<\n <\n >\n >>\n Page\n of 1\n 1 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='j_idt255', classes=[], index=2, innerText='', otherAttributes={style=width:50px;height:50px;position:fixed;right:7px;bottom:7px; z-index: 10000}, children=[]], Node[tag='div', id='j_idt255_start', classes=[], index=0, innerText='\n                         ', otherAttributes={style=display:none}, children=[]], Node[tag='i', id='', classes=[pi-spin, pi, ajax-loader, pi-spinner], index=0, innerText='', otherAttributes={aria-hidden=true}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:56:16.860  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.cssSelector(#pageLoginForm\:login_business\:UserName) 
2025-08-03 22:56:16.861 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: #pageLoginForm\:login_business\:UserName, URL(source): http://************:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:16.867 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: b5602af340e30317feaee6de3bf83c34 
2025-08-03 22:56:16.870 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=b5602af340e30317feaee6de3bf83c34, url=http://************:8080/AMLUI/SWAF/jsp/login.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#pageLoginForm\:login_business\:UserName, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:16.870060152, enableHealing=false) 
2025-08-03 22:56:17.068 DEBUG 1 - [7878-exec-2] healenium                        : [Get Reference] Request: RequestDto(locator=//*[@class='pi pi-spin pi-spinner ajax-loader'], className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://************:8080/AMLUI/SWAF/jsp/login.xhtml)) 
2025-08-03 22:56:17.070 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:17.071 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:17.074 DEBUG 1 - [7878-exec-2] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='Group Manager\n Group Manager\n Name:\n Description:\n Waiting Checker Action:\n Group content\n Operator Login Name:\n Operator Group Name:\n Reset\n Search\n  \n 	\n  \n Id	\n  \n Name	\n  \n Description	\n  \n Email	\n  \n Profile	Zone\n \n 	211	selenium-random-group-301909915	Random group for selenium testing		Test-Profile-761583025	Zone639487746\n New\n Remove\n <<\n <\n >\n >>\n Page\n of 1\n 1 row(s)', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='Group Manager\n Group Manager\n Name:\n Description:\n Waiting Checker Action:\n Group content\n Operator Login Name:\n Operator Group Name:\n Reset\n Search\n  \n 	\n  \n Id	\n  \n Name	\n  \n Description	\n  \n Email	\n  \n Profile	Zone\n \n 	211	selenium-random-group-301909915	Random group for selenium testing		Test-Profile-761583025	Zone639487746\n New\n Remove\n <<\n <\n >\n >>\n Page\n of 1\n 1 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=9, innerText='Group Manager\n Group Manager\n Name:\n Description:\n Waiting Checker Action:\n Group content\n Operator Login Name:\n Operator Group Name:\n Reset\n Search\n  \n 	\n  \n Id	\n  \n Name	\n  \n Description	\n  \n Email	\n  \n Profile	Zone\n \n 	211	selenium-random-group-301909915	Random group for selenium testing		Test-Profile-761583025	Zone639487746\n New\n Remove\n <<\n <\n >\n >>\n Page\n of 1\n 1 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='j_idt255', classes=[], index=2, innerText='', otherAttributes={style=width:50px;height:50px;position:fixed;right:7px;bottom:7px; z-index: 10000}, children=[]], Node[tag='div', id='j_idt255_start', classes=[], index=0, innerText='\n                         ', otherAttributes={style=display:none}, children=[]], Node[tag='i', id='', classes=[pi-spin, pi, ajax-loader, pi-spinner], index=0, innerText='', otherAttributes={aria-hidden=true}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:56:17.214  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.cssSelector(#pageLoginForm\:login_business\:Password) 
2025-08-03 22:56:17.214 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: #pageLoginForm\:login_business\:Password, URL(source): http://************:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:17.215 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: dfcd153e32be3bbdf9ff3d83828d14f2 
2025-08-03 22:56:17.217 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=dfcd153e32be3bbdf9ff3d83828d14f2, url=http://************:8080/AMLUI/SWAF/jsp/login.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#pageLoginForm\:login_business\:Password, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:17.217900714, enableHealing=false) 
2025-08-03 22:56:17.402 DEBUG 1 - [7878-exec-6] healenium                        : [Get Reference] Request: RequestDto(locator=//*[@class='pi pi-spin pi-spinner ajax-loader'], className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://************:8080/AMLUI/SWAF/jsp/login.xhtml)) 
2025-08-03 22:56:17.403 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:17.405 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:17.416 DEBUG 1 - [7878-exec-6] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='Group Manager\n Group Manager\n Name:\n Description:\n Waiting Checker Action:\n Group content\n Operator Login Name:\n Operator Group Name:\n Reset\n Search\n  \n 	\n  \n Id	\n  \n Name	\n  \n Description	\n  \n Email	\n  \n Profile	Zone\n \n 	211	selenium-random-group-301909915	Random group for selenium testing		Test-Profile-761583025	Zone639487746\n New\n Remove\n <<\n <\n >\n >>\n Page\n of 1\n 1 row(s)', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='Group Manager\n Group Manager\n Name:\n Description:\n Waiting Checker Action:\n Group content\n Operator Login Name:\n Operator Group Name:\n Reset\n Search\n  \n 	\n  \n Id	\n  \n Name	\n  \n Description	\n  \n Email	\n  \n Profile	Zone\n \n 	211	selenium-random-group-301909915	Random group for selenium testing		Test-Profile-761583025	Zone639487746\n New\n Remove\n <<\n <\n >\n >>\n Page\n of 1\n 1 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=9, innerText='Group Manager\n Group Manager\n Name:\n Description:\n Waiting Checker Action:\n Group content\n Operator Login Name:\n Operator Group Name:\n Reset\n Search\n  \n 	\n  \n Id	\n  \n Name	\n  \n Description	\n  \n Email	\n  \n Profile	Zone\n \n 	211	selenium-random-group-301909915	Random group for selenium testing		Test-Profile-761583025	Zone639487746\n New\n Remove\n <<\n <\n >\n >>\n Page\n of 1\n 1 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='j_idt255', classes=[], index=2, innerText='', otherAttributes={style=width:50px;height:50px;position:fixed;right:7px;bottom:7px; z-index: 10000}, children=[]], Node[tag='div', id='j_idt255_start', classes=[], index=0, innerText='\n                         ', otherAttributes={style=display:none}, children=[]], Node[tag='i', id='', classes=[pi-spin, pi, ajax-loader, pi-spinner], index=0, innerText='', otherAttributes={aria-hidden=true}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:56:17.500  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.cssSelector(#pageLoginForm\:login_business\:Password) 
2025-08-03 22:56:17.501 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: #pageLoginForm\:login_business\:Password, URL(source): http://************:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:17.503 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: dfcd153e32be3bbdf9ff3d83828d14f2 
2025-08-03 22:56:17.505 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=dfcd153e32be3bbdf9ff3d83828d14f2, url=http://************:8080/AMLUI/SWAF/jsp/login.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#pageLoginForm\:login_business\:Password, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:17.505568348, enableHealing=false) 
2025-08-03 22:56:17.774 DEBUG 1 - [7878-exec-4] healenium                        : [Get Reference] Request: RequestDto(locator=//*[@class='pi pi-spin pi-spinner ajax-loader'], className=HealeniumFindElementPostRequest, methodName=findElement, command=findElement, url=http://************:8080/AMLUI/SWAF/jsp/login.xhtml)) 
2025-08-03 22:56:17.777 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:17.777 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:17.780 DEBUG 1 - [7878-exec-4] healenium                        : [Get Reference] Response: ReferenceElementsDto(pageContent=null, paths=[[Node[tag='html', id='', classes=[], index=0, innerText='Group Manager\n Group Manager\n Name:\n Description:\n Waiting Checker Action:\n Group content\n Operator Login Name:\n Operator Group Name:\n Reset\n Search\n  \n 	\n  \n Id	\n  \n Name	\n  \n Description	\n  \n Email	\n  \n Profile	Zone\n \n 	211	selenium-random-group-301909915	Random group for selenium testing		Test-Profile-761583025	Zone639487746\n New\n Remove\n <<\n <\n >\n >>\n Page\n of 1\n 1 row(s)', otherAttributes={xmlns=http://www.w3.org/1999/xhtml, lang=en}, children=[]], Node[tag='body', id='', classes=[], index=1, innerText='Group Manager\n Group Manager\n Name:\n Description:\n Waiting Checker Action:\n Group content\n Operator Login Name:\n Operator Group Name:\n Reset\n Search\n  \n 	\n  \n Id	\n  \n Name	\n  \n Description	\n  \n Email	\n  \n Profile	Zone\n \n 	211	selenium-random-group-301909915	Random group for selenium testing		Test-Profile-761583025	Zone639487746\n New\n Remove\n <<\n <\n >\n >>\n Page\n of 1\n 1 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='', classes=[layout-menu-light, layout-slim, layout-wrapper, layout-topbar-light], index=9, innerText='Group Manager\n Group Manager\n Name:\n Description:\n Waiting Checker Action:\n Group content\n Operator Login Name:\n Operator Group Name:\n Reset\n Search\n  \n 	\n  \n Id	\n  \n Name	\n  \n Description	\n  \n Email	\n  \n Profile	Zone\n \n 	211	selenium-random-group-301909915	Random group for selenium testing		Test-Profile-761583025	Zone639487746\n New\n Remove\n <<\n <\n >\n >>\n Page\n of 1\n 1 row(s)', otherAttributes={}, children=[]], Node[tag='div', id='j_idt255', classes=[], index=2, innerText='', otherAttributes={style=width:50px;height:50px;position:fixed;right:7px;bottom:7px; z-index: 10000}, children=[]], Node[tag='div', id='j_idt255_start', classes=[], index=0, innerText='\n                         ', otherAttributes={style=display:none}, children=[]], Node[tag='i', id='', classes=[pi-spin, pi, ajax-loader, pi-spinner], index=0, innerText='', otherAttributes={aria-hidden=true}, children=[]]]], unsuccessfulLocators=null)) 
2025-08-03 22:56:17.974  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.cssSelector(#pageLoginForm\:login_business\:goButton) 
2025-08-03 22:56:17.975 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #pageLoginForm\:login_business\:goButton, URL(source): http://************:8080/AMLUI/SWAF/jsp/login.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:17.976 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: a9431ac117de5706bfee507de4c5c09f 
2025-08-03 22:56:17.981 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=a9431ac117de5706bfee507de4c5c09f, url=http://************:8080/AMLUI/SWAF/jsp/login.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#pageLoginForm\:login_business\:goButton, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:17.981242779, enableHealing=false) 
2025-08-03 22:56:20.225  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:20.226 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/admin/jsp/OperatorGroupManager/Homepage.xhtml?module_name=OperatorGroupManager&app_name=ADMIN, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:20.233 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:20.236 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/admin/jsp/OperatorGroupManager/Homepage.xhtml?module_name=OperatorGroupManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:20.236324419, enableHealing=true) 
2025-08-03 22:56:20.351  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.cssSelector(#operatorGroupForm\:homepage_business\:name) 
2025-08-03 22:56:20.352 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #operatorGroupForm\:homepage_business\:name, URL(source): http://************:8080/AMLUI/Modules/admin/jsp/OperatorGroupManager/Homepage.xhtml?module_name=OperatorGroupManager&app_name=ADMIN, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:20.354 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: d626cff25a59b901312cf1af98a8bb9a 
2025-08-03 22:56:20.360 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=d626cff25a59b901312cf1af98a8bb9a, url=http://************:8080/AMLUI/Modules/admin/jsp/OperatorGroupManager/Homepage.xhtml?module_name=OperatorGroupManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#operatorGroupForm\:homepage_business\:name, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:20.360894890, enableHealing=false) 
2025-08-03 22:56:20.426  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.xpath(//*[@class='ui-growl-title']) 
2025-08-03 22:56:20.427 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: //*[@class='ui-growl-title'], URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:20.429 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 34856943884f3a1c0a7d9f1f13b48b3e 
2025-08-03 22:56:20.437 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=34856943884f3a1c0a7d9f1f13b48b3e, url=http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='ui-growl-title'], type=By.xpath), command=findElements, createdDate=2025-08-03T19:56:20.437623734, enableHealing=false) 
2025-08-03 22:56:20.532  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:20.534 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:20.534 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:20.536 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:20.536023118, enableHealing=true) 
2025-08-03 22:56:20.596  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:20.597 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/admin/jsp/OperatorGroupManager/Homepage.xhtml?module_name=OperatorGroupManager&app_name=ADMIN, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:20.599 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:20.600 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/admin/jsp/OperatorGroupManager/Homepage.xhtml?module_name=OperatorGroupManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:20.600492770, enableHealing=true) 
2025-08-03 22:56:20.722  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.cssSelector(#operatorGroupForm\:homepage_business\:name) 
2025-08-03 22:56:20.723 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: #operatorGroupForm\:homepage_business\:name, URL(source): http://************:8080/AMLUI/Modules/admin/jsp/OperatorGroupManager/Homepage.xhtml?module_name=OperatorGroupManager&app_name=ADMIN, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:20.724 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: d626cff25a59b901312cf1af98a8bb9a 
2025-08-03 22:56:20.726 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=d626cff25a59b901312cf1af98a8bb9a, url=http://************:8080/AMLUI/Modules/admin/jsp/OperatorGroupManager/Homepage.xhtml?module_name=OperatorGroupManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#operatorGroupForm\:homepage_business\:name, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:20.725973351, enableHealing=false) 
2025-08-03 22:56:20.796  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.xpath(//*[@class='ui-growl-title']) 
2025-08-03 22:56:20.796 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: //*[@class='ui-growl-title'], URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:20.796 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 34856943884f3a1c0a7d9f1f13b48b3e 
2025-08-03 22:56:20.798 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=34856943884f3a1c0a7d9f1f13b48b3e, url=http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='ui-growl-title'], type=By.xpath), command=findElements, createdDate=2025-08-03T19:56:20.798153642, enableHealing=false) 
2025-08-03 22:56:21.045  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:21.046 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/admin/jsp/OperatorGroupManager/Homepage.xhtml?module_name=OperatorGroupManager&app_name=ADMIN, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:21.047 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:21.049 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/admin/jsp/OperatorGroupManager/Homepage.xhtml?module_name=OperatorGroupManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:21.049899870, enableHealing=true) 
2025-08-03 22:56:21.086  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.cssSelector(#menuform\:om_HomeListManager) 
2025-08-03 22:56:21.088 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:21.089 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 99cddc51be3dae4e40bef1d2069ba63f 
2025-08-03 22:56:21.093 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=99cddc51be3dae4e40bef1d2069ba63f, url=http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#menuform\:om_HomeListManager, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:21.093471608, enableHealing=true) 
2025-08-03 22:56:21.209  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.cssSelector(#operatorGroupForm\:homepage_business\:description) 
2025-08-03 22:56:21.210 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #operatorGroupForm\:homepage_business\:description, URL(source): http://************:8080/AMLUI/Modules/admin/jsp/OperatorGroupManager/Homepage.xhtml?module_name=OperatorGroupManager&app_name=ADMIN, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:21.211 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 1a96880b4ad1e888ad3bd0cfae29e2e1 
2025-08-03 22:56:21.217 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=1a96880b4ad1e888ad3bd0cfae29e2e1, url=http://************:8080/AMLUI/Modules/admin/jsp/OperatorGroupManager/Homepage.xhtml?module_name=OperatorGroupManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#operatorGroupForm\:homepage_business\:description, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:21.217093718, enableHealing=false) 
2025-08-03 22:56:21.260  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:21.261 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:21.262 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:21.264 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:21.264278422, enableHealing=true) 
2025-08-03 22:56:21.397  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:21.397 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/admin/jsp/OperatorGroupManager/Homepage.xhtml?module_name=OperatorGroupManager&app_name=ADMIN, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:21.398  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.cssSelector(#menuform\:om_HomeListManager) 
2025-08-03 22:56:21.403 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #menuform\:om_HomeListManager, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:21.412 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: a12e0634dcb2612225fc4ac8aef2676f 
2025-08-03 22:56:21.398 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:21.417 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=a12e0634dcb2612225fc4ac8aef2676f, url=http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#menuform\:om_HomeListManager, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:21.417250055, enableHealing=false) 
2025-08-03 22:56:21.417 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/admin/jsp/OperatorGroupManager/Homepage.xhtml?module_name=OperatorGroupManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:21.417763163, enableHealing=true) 
2025-08-03 22:56:21.536  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.cssSelector(#operatorGroupForm\:homepage_business\:description) 
2025-08-03 22:56:21.536 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: #operatorGroupForm\:homepage_business\:description, URL(source): http://************:8080/AMLUI/Modules/admin/jsp/OperatorGroupManager/Homepage.xhtml?module_name=OperatorGroupManager&app_name=ADMIN, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:21.538 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 1a96880b4ad1e888ad3bd0cfae29e2e1 
2025-08-03 22:56:21.543 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=1a96880b4ad1e888ad3bd0cfae29e2e1, url=http://************:8080/AMLUI/Modules/admin/jsp/OperatorGroupManager/Homepage.xhtml?module_name=OperatorGroupManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#operatorGroupForm\:homepage_business\:description, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:21.543729270, enableHealing=false) 
2025-08-03 22:56:21.885  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:21.885 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/admin/jsp/OperatorGroupManager/Homepage.xhtml?module_name=OperatorGroupManager&app_name=ADMIN, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:21.885 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:21.887 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/admin/jsp/OperatorGroupManager/Homepage.xhtml?module_name=OperatorGroupManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:21.887141080, enableHealing=true) 
2025-08-03 22:56:22.037  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.cssSelector(#operatorGroupForm\:homepage_business\:btnSearch) 
2025-08-03 22:56:22.038 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: #operatorGroupForm\:homepage_business\:btnSearch, URL(source): http://************:8080/AMLUI/Modules/admin/jsp/OperatorGroupManager/Homepage.xhtml?module_name=OperatorGroupManager&app_name=ADMIN, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:22.038 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 7209bd0d1e930b4c1b793bb0e6569ee8 
2025-08-03 22:56:22.096 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=7209bd0d1e930b4c1b793bb0e6569ee8, url=http://************:8080/AMLUI/Modules/admin/jsp/OperatorGroupManager/Homepage.xhtml?module_name=OperatorGroupManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#operatorGroupForm\:homepage_business\:btnSearch, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:22.096126688, enableHealing=false) 
2025-08-03 22:56:22.483  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:22.485 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/admin/jsp/OperatorGroupManager/Homepage.xhtml?module_name=OperatorGroupManager&app_name=ADMIN, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:22.486 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:22.490 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/admin/jsp/OperatorGroupManager/Homepage.xhtml?module_name=OperatorGroupManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:22.490438790, enableHealing=true) 
2025-08-03 22:56:22.607  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:22.608 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:22.608 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:22.611 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:22.611034258, enableHealing=true) 
2025-08-03 22:56:22.735  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.xpath(//form[@id='topbar-right']/ul/label) 
2025-08-03 22:56:22.736 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: //form[@id='topbar-right']/ul/label, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:22.737 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 4a9504d175bc9716fe2da0e9f093ac9e 
2025-08-03 22:56:22.739 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=4a9504d175bc9716fe2da0e9f093ac9e, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//form[@id='topbar-right']/ul/label, type=By.xpath), command=findElements, createdDate=2025-08-03T19:56:22.739461487, enableHealing=false) 
2025-08-03 22:56:22.751  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:22.751 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/admin/jsp/OperatorGroupManager/Homepage.xhtml?module_name=OperatorGroupManager&app_name=ADMIN, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:22.753 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:22.756 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/admin/jsp/OperatorGroupManager/Homepage.xhtml?module_name=OperatorGroupManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:22.755964786, enableHealing=true) 
2025-08-03 22:56:22.867  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.xpath(//a[contains(text(),'List Manager')]) 
2025-08-03 22:56:22.868 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: //a[contains(text(),'List Manager')], URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:22.868 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 8f3dc03694a381d6b07aab61a1b897c2 
2025-08-03 22:56:22.871 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=8f3dc03694a381d6b07aab61a1b897c2, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//a[contains(text(),'List Manager')], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:22.871194017, enableHealing=true) 
2025-08-03 22:56:22.971  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.xpath(//tbody//tr//td[.='selenium-random-group-301909915']) 
2025-08-03 22:56:22.971 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: //tbody//tr//td[.='selenium-random-group-301909915'], URL(source): http://************:8080/AMLUI/Modules/admin/jsp/OperatorGroupManager/Homepage.xhtml?module_name=OperatorGroupManager&app_name=ADMIN, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:22.973 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: d9051c357f76abcd0aa9c1721a41dc2f 
2025-08-03 22:56:22.975 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=d9051c357f76abcd0aa9c1721a41dc2f, url=http://************:8080/AMLUI/Modules/admin/jsp/OperatorGroupManager/Homepage.xhtml?module_name=OperatorGroupManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//tbody//tr//td[.='selenium-random-group-301909915'], type=By.xpath), command=findElements, createdDate=2025-08-03T19:56:22.974660253, enableHealing=false) 
2025-08-03 22:56:23.056  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:23.058 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:23.058 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:23.060 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:23.060033154, enableHealing=true) 
2025-08-03 22:56:23.184  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.cssSelector(#menuform\:om_admin_HomeZoneManager) 
2025-08-03 22:56:23.184 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: #menuform\:om_admin_HomeZoneManager, URL(source): http://************:8080/AMLUI/Modules/admin/jsp/OperatorGroupManager/Homepage.xhtml?module_name=OperatorGroupManager&app_name=ADMIN, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:23.185 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 67cd9c56b21382c17214e40021ad17fe 
2025-08-03 22:56:23.186 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=67cd9c56b21382c17214e40021ad17fe, url=http://************:8080/AMLUI/Modules/admin/jsp/OperatorGroupManager/Homepage.xhtml?module_name=OperatorGroupManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#menuform\:om_admin_HomeZoneManager, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:23.186916655, enableHealing=true) 
2025-08-03 22:56:23.219  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.xpath(//a[contains(text(),'List Manager')]) 
2025-08-03 22:56:23.220 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: //a[contains(text(),'List Manager')], URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:23.221 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: f57e64d4f8c44329ff06c712afa6a547 
2025-08-03 22:56:23.224 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=f57e64d4f8c44329ff06c712afa6a547, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//a[contains(text(),'List Manager')], type=By.xpath), command=findElements, createdDate=2025-08-03T19:56:23.224044327, enableHealing=false) 
2025-08-03 22:56:23.372  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:23.375 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/admin/jsp/OperatorGroupManager/Homepage.xhtml?module_name=OperatorGroupManager&app_name=ADMIN, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:23.376 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:23.384 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/admin/jsp/OperatorGroupManager/Homepage.xhtml?module_name=OperatorGroupManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:23.383953531, enableHealing=true) 
2025-08-03 22:56:23.509  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.cssSelector(#menuform\:om_admin_HomeZoneManager) 
2025-08-03 22:56:23.510 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: #menuform\:om_admin_HomeZoneManager, URL(source): http://************:8080/AMLUI/Modules/admin/jsp/OperatorGroupManager/Homepage.xhtml?module_name=OperatorGroupManager&app_name=ADMIN, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:23.511 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 47116588c17ba82cd6e287750b107e24 
2025-08-03 22:56:23.516 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=47116588c17ba82cd6e287750b107e24, url=http://************:8080/AMLUI/Modules/admin/jsp/OperatorGroupManager/Homepage.xhtml?module_name=OperatorGroupManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#menuform\:om_admin_HomeZoneManager, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:23.516209871, enableHealing=false) 
2025-08-03 22:56:23.526  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:zoneCbx) 
2025-08-03 22:56:23.527 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:zoneCbx, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:23.529 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 2d9dd40db149a3b04bc59253096ddc3c 
2025-08-03 22:56:23.533 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=2d9dd40db149a3b04bc59253096ddc3c, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:zoneCbx, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:23.533820105, enableHealing=true) 
2025-08-03 22:56:23.722  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:23.723 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:23.724 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:23.729 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:23.729909379, enableHealing=true) 
2025-08-03 22:56:23.884  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.xpath(//span[contains(text(),'Reset')]) 
2025-08-03 22:56:23.884 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: //span[contains(text(),'Reset')], URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:23.886 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: fce827adfc56329d1ee0425d66138593 
2025-08-03 22:56:23.887 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=fce827adfc56329d1ee0425d66138593, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//span[contains(text(),'Reset')], type=By.xpath), command=findElements, createdDate=2025-08-03T19:56:23.887889074, enableHealing=false) 
2025-08-03 22:56:24.235  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:24.236 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:24.236 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:24.239 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:24.239712167, enableHealing=true) 
2025-08-03 22:56:24.433  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:24.434 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:24.437 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:24.440 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:24.440432623, enableHealing=true) 
2025-08-03 22:56:24.519  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:24.520 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:24.521 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:24.523 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:24.523612459, enableHealing=true) 
2025-08-03 22:56:24.602  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.xpath(//span[contains(text(),'Reset')]) 
2025-08-03 22:56:24.603 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: //span[contains(text(),'Reset')], URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:24.603 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: fce827adfc56329d1ee0425d66138593 
2025-08-03 22:56:24.606 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=fce827adfc56329d1ee0425d66138593, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//span[contains(text(),'Reset')], type=By.xpath), command=findElements, createdDate=2025-08-03T19:56:24.605962418, enableHealing=false) 
2025-08-03 22:56:24.751  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.xpath(//form[@id='topbar-right']/ul/label) 
2025-08-03 22:56:24.754 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: //form[@id='topbar-right']/ul/label, URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:24.758 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 4a9504d175bc9716fe2da0e9f093ac9e 
2025-08-03 22:56:24.761 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=4a9504d175bc9716fe2da0e9f093ac9e, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//form[@id='topbar-right']/ul/label, type=By.xpath), command=findElements, createdDate=2025-08-03T19:56:24.761215935, enableHealing=false) 
2025-08-03 22:56:24.877  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:24.877 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:24.879 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:24.882 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:24.882614573, enableHealing=true) 
2025-08-03 22:56:25.059  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:25.060 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:25.060 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:25.062  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.cssSelector(#zoneForm\:homepage_business\:btnSearch) 
2025-08-03 22:56:25.066 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: #zoneForm\:homepage_business\:btnSearch, URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:25.066 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:25.066160488, enableHealing=true) 
2025-08-03 22:56:25.066 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 610016a09e8f2493db42903843ad4a90 
2025-08-03 22:56:25.068 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=610016a09e8f2493db42903843ad4a90, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#zoneForm\:homepage_business\:btnSearch, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:25.068604106, enableHealing=false) 
2025-08-03 22:56:25.231  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:25.231 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:25.232 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:25.234  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:25.234 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:25.234428264, enableHealing=true) 
2025-08-03 22:56:25.235 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:25.238 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:25.241 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:25.240986955, enableHealing=true) 
2025-08-03 22:56:25.376  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.cssSelector(#zoneForm\:homepage_business\:btnSearch) 
2025-08-03 22:56:25.377 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: #zoneForm\:homepage_business\:btnSearch, URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:25.378 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 610016a09e8f2493db42903843ad4a90 
2025-08-03 22:56:25.384 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=610016a09e8f2493db42903843ad4a90, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#zoneForm\:homepage_business\:btnSearch, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:25.384010478, enableHealing=false) 
2025-08-03 22:56:25.425  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:zoneCbx_label) 
2025-08-03 22:56:25.427 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:zoneCbx_label, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:25.428 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 094f76901d4289b27135807cd4a8be96 
2025-08-03 22:56:25.433 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=094f76901d4289b27135807cd4a8be96, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:zoneCbx_label, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:25.433115989, enableHealing=false) 
2025-08-03 22:56:25.503  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:25.504 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:25.504 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:25.506 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:25.506179980, enableHealing=true) 
2025-08-03 22:56:25.665  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.cssSelector(#zoneForm\:homepage_business\:btnReset) 
2025-08-03 22:56:25.667 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: #zoneForm\:homepage_business\:btnReset, URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:25.668 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: f336011d2ef61b8814fab52899c5448e 
2025-08-03 22:56:25.670 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=f336011d2ef61b8814fab52899c5448e, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#zoneForm\:homepage_business\:btnReset, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:25.670241964, enableHealing=false) 
2025-08-03 22:56:26.802  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.xpath(//*[@role='option']) 
2025-08-03 22:56:26.803 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: //*[@role='option'], URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:26.804 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 03a61cd96fa0393b4a3c000bdbe5ce69 
2025-08-03 22:56:27.874 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=03a61cd96fa0393b4a3c000bdbe5ce69, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@role='option'], type=By.xpath), command=findElements, createdDate=2025-08-03T19:56:27.874339665, enableHealing=false) 
2025-08-03 22:56:27.971  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.xpath(//*[@role =  'option' and .='Common Zone 05']) 
2025-08-03 22:56:27.972 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: //*[@role =  'option' and .='Common Zone 05'], URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:27.973 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 6a2d7e872f168720a8e69f36eee17499 
2025-08-03 22:56:27.974 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=6a2d7e872f168720a8e69f36eee17499, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@role =  'option' and .='Common Zone 05'], type=By.xpath), command=findElements, createdDate=2025-08-03T19:56:27.974505708, enableHealing=false) 
2025-08-03 22:56:28.025  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:28.027 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:28.028 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:28.030 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:28.030021575, enableHealing=true) 
2025-08-03 22:56:28.169  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.xpath(//*[@role =  'option' and .='Common Zone 05']) 
2025-08-03 22:56:28.172 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: //*[@role =  'option' and .='Common Zone 05'], URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:28.172 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 6a2d7e872f168720a8e69f36eee17499 
2025-08-03 22:56:28.177 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=6a2d7e872f168720a8e69f36eee17499, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@role =  'option' and .='Common Zone 05'], type=By.xpath), command=findElements, createdDate=2025-08-03T19:56:28.177005812, enableHealing=false) 
2025-08-03 22:56:28.302  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.xpath(//*[@role =  'option' and .='Common Zone 05']) 
2025-08-03 22:56:28.308 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: //*[@role =  'option' and .='Common Zone 05'], URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:28.311 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: ******************************** 
2025-08-03 22:56:28.320 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=********************************, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@role =  'option' and .='Common Zone 05'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:28.320675618, enableHealing=true) 
2025-08-03 22:56:28.646  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:28.648 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:28.648 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:28.649 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:28.649883827, enableHealing=true) 
2025-08-03 22:56:28.780  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:nameField) 
2025-08-03 22:56:28.782 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:nameField, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:28.783 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 3f87a7532a8d1b1a77d406389919a8a8 
2025-08-03 22:56:28.786 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=3f87a7532a8d1b1a77d406389919a8a8, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:nameField, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:28.786914066, enableHealing=false) 
2025-08-03 22:56:28.969  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:28.970 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:28.971 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:28.973 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:28.972968621, enableHealing=true) 
2025-08-03 22:56:29.111  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:nameField) 
2025-08-03 22:56:29.112 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:nameField, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:29.114 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 3f87a7532a8d1b1a77d406389919a8a8 
2025-08-03 22:56:29.120 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=3f87a7532a8d1b1a77d406389919a8a8, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:nameField, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:29.120419437, enableHealing=false) 
2025-08-03 22:56:29.468  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:29.473 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:29.474 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:29.480 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:29.480276720, enableHealing=true) 
2025-08-03 22:56:29.638  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:btnBLVSearch) 
2025-08-03 22:56:29.640 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:btnBLVSearch, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:29.641 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 3cab7cd93b4234c709b3a423839e146b 
2025-08-03 22:56:29.643 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=3cab7cd93b4234c709b3a423839e146b, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:btnBLVSearch, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:29.643665846, enableHealing=false) 
2025-08-03 22:56:29.767  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew) 
2025-08-03 22:56:29.767 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:29.769 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: c46244c86d1af185aa670c4f6f945d13 
2025-08-03 22:56:29.774 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=c46244c86d1af185aa670c4f6f945d13, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:29.774155462, enableHealing=true) 
2025-08-03 22:56:30.086  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew) 
2025-08-03 22:56:30.087 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:30.088 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: c46244c86d1af185aa670c4f6f945d13 
2025-08-03 22:56:30.094 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=c46244c86d1af185aa670c4f6f945d13, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:30.094861530, enableHealing=true) 
2025-08-03 22:56:30.217  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:30.218 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:30.221 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:30.223 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:30.223891582, enableHealing=true) 
2025-08-03 22:56:30.352  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:30.353 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:30.354 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:30.355 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:30.355661942, enableHealing=true) 
2025-08-03 22:56:30.536  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew) 
2025-08-03 22:56:30.537 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:30.537 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: c46244c86d1af185aa670c4f6f945d13 
2025-08-03 22:56:30.540 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=c46244c86d1af185aa670c4f6f945d13, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:30.540502387, enableHealing=true) 
2025-08-03 22:56:30.728  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:30.729 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:30.730 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:30.731 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:30.731713051, enableHealing=true) 
2025-08-03 22:56:30.873  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew) 
2025-08-03 22:56:30.876 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:30.877 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 46b8cb87b8aaffc76ae815725e7a88ad 
2025-08-03 22:56:30.881 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=46b8cb87b8aaffc76ae815725e7a88ad, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:30.881459839, enableHealing=false) 
2025-08-03 22:56:30.958  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:30.959 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:30.959 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:30.962 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:30.962400867, enableHealing=true) 
2025-08-03 22:56:31.101  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:31.102 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:31.105 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:31.107 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:31.107296008, enableHealing=true) 
2025-08-03 22:56:31.149  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.cssSelector(#zoneForm\:homepage_business\:zoneName) 
2025-08-03 22:56:31.151 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: #zoneForm\:homepage_business\:zoneName, URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:31.152 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 9d43974fa181ab5cdcbc79dc6cfe9d46 
2025-08-03 22:56:31.154 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=9d43974fa181ab5cdcbc79dc6cfe9d46, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#zoneForm\:homepage_business\:zoneName, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:31.153879261, enableHealing=false) 
2025-08-03 22:56:31.288  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:31.290 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:31.295 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 169d5252cc72b1269c710e8f0c9464c7 
2025-08-03 22:56:31.298 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=169d5252cc72b1269c710e8f0c9464c7, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:31.298617184, enableHealing=false) 
2025-08-03 22:56:31.354  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:31.356 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:31.360 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:31.367 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:31.367409242, enableHealing=true) 
2025-08-03 22:56:31.498  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.cssSelector(#zoneForm\:homepage_business\:zoneName) 
2025-08-03 22:56:31.499 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: #zoneForm\:homepage_business\:zoneName, URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:31.500 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 9d43974fa181ab5cdcbc79dc6cfe9d46 
2025-08-03 22:56:31.503 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=9d43974fa181ab5cdcbc79dc6cfe9d46, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#zoneForm\:homepage_business\:zoneName, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:31.503416780, enableHealing=false) 
2025-08-03 22:56:31.522  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:31.523 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:31.523 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:31.527 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:31.527386184, enableHealing=true) 
2025-08-03 22:56:31.758  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:31.760 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:31.761 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 169d5252cc72b1269c710e8f0c9464c7 
2025-08-03 22:56:31.766 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=169d5252cc72b1269c710e8f0c9464c7, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:31.766932999, enableHealing=false) 
2025-08-03 22:56:31.982  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:31.984 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:31.986 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:31.990 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:31.990442405, enableHealing=true) 
2025-08-03 22:56:32.232  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.cssSelector(#zoneForm\:homepage_business\:zoneDispName) 
2025-08-03 22:56:32.240 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #zoneForm\:homepage_business\:zoneDispName, URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:32.241 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 547b2898343148ebd5d27313534616d8 
2025-08-03 22:56:32.244 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=547b2898343148ebd5d27313534616d8, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#zoneForm\:homepage_business\:zoneDispName, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:32.244784388, enableHealing=false) 
2025-08-03 22:56:32.361  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:32.367 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:32.367 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:32.369 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:32.368919388, enableHealing=true) 
2025-08-03 22:56:32.533  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:32.534 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:32.535 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:32.537 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:32.537398988, enableHealing=true) 
2025-08-03 22:56:32.578  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:btnBLCSave) 
2025-08-03 22:56:32.580 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:btnBLCSave, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:32.584 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 3de4958250014322cab2e865b798bed2 
2025-08-03 22:56:32.587 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=3de4958250014322cab2e865b798bed2, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:btnBLCSave, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:32.587091292, enableHealing=false) 
2025-08-03 22:56:32.699  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.cssSelector(#zoneForm\:homepage_business\:zoneDispName) 
2025-08-03 22:56:32.705 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: #zoneForm\:homepage_business\:zoneDispName, URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:32.705 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 547b2898343148ebd5d27313534616d8 
2025-08-03 22:56:32.709 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=547b2898343148ebd5d27313534616d8, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#zoneForm\:homepage_business\:zoneDispName, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:32.709748325, enableHealing=false) 
2025-08-03 22:56:32.911  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:32.912 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:32.916 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:32.918 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:32.918930415, enableHealing=true) 
2025-08-03 22:56:33.075  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:33.076 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:33.076 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:33.079 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:33.078999867, enableHealing=true) 
2025-08-03 22:56:33.111  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:33.115 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:33.116 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:33.119 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:33.119576243, enableHealing=true) 
2025-08-03 22:56:33.331  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:33.333 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:33.339 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:33.365 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:33.365553984, enableHealing=true) 
2025-08-03 22:56:33.373  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.cssSelector(#zoneForm\:homepage_business\:btnSearch) 
2025-08-03 22:56:33.374 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: #zoneForm\:homepage_business\:btnSearch, URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:33.374 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 610016a09e8f2493db42903843ad4a90 
2025-08-03 22:56:33.379 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=610016a09e8f2493db42903843ad4a90, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#zoneForm\:homepage_business\:btnSearch, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:33.379433649, enableHealing=false) 
2025-08-03 22:56:33.523  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:33.526 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:33.529 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:33.534 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:33.534060387, enableHealing=true) 
2025-08-03 22:56:33.683  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:33.684 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:33.684 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:33.685  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:33.686 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:33.686028325, enableHealing=true) 
2025-08-03 22:56:33.686 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:33.691 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:33.696 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:33.696316085, enableHealing=true) 
2025-08-03 22:56:33.835  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:33.835 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:33.836 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:33.841 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:33.841736585, enableHealing=true) 
2025-08-03 22:56:33.878  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:33.882 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:33.882 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:33.884 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:33.883987750, enableHealing=true) 
2025-08-03 22:56:34.021  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:34.022 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:34.023 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:34.024 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:34.024765326, enableHealing=true) 
2025-08-03 22:56:34.063  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.cssSelector(#zoneForm\:homepage_business\:_tblResults\:_btnNew) 
2025-08-03 22:56:34.064 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: #zoneForm\:homepage_business\:_tblResults\:_btnNew, URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:34.068 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 21cf8eeb778e29c4f44ae96972726755 
2025-08-03 22:56:34.073 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=21cf8eeb778e29c4f44ae96972726755, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#zoneForm\:homepage_business\:_tblResults\:_btnNew, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:34.073461118, enableHealing=false) 
2025-08-03 22:56:34.203  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:34.205 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:34.206 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:34.209 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:34.209580169, enableHealing=true) 
2025-08-03 22:56:34.327  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:34.329 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:34.329 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:34.333 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:34.333654461, enableHealing=true) 
2025-08-03 22:56:34.838  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:34.842 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:34.843 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:34.844 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:34.844895317, enableHealing=true) 
2025-08-03 22:56:34.999  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:35.003 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:35.008 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:35.018 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:35.018738722, enableHealing=true) 
2025-08-03 22:56:35.045  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.cssSelector(#zoneManagerDetailForm\:detail_business\:name) 
2025-08-03 22:56:35.046 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: #zoneManagerDetailForm\:detail_business\:name, URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:35.049 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: d75e02203b76ade64fd99a0ee3896209 
2025-08-03 22:56:35.051 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=d75e02203b76ade64fd99a0ee3896209, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#zoneManagerDetailForm\:detail_business\:name, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:35.051681537, enableHealing=false) 
2025-08-03 22:56:35.209  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:35.210 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:35.211 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:35.212 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:35.212835011, enableHealing=true) 
2025-08-03 22:56:35.273  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:35.274 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:35.276 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:35.278 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:35.278853557, enableHealing=true) 
2025-08-03 22:56:35.403  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:35.404 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:35.404 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:35.411 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:35.411814251, enableHealing=true) 
2025-08-03 22:56:35.424  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.cssSelector(#zoneManagerDetailForm\:detail_business\:name) 
2025-08-03 22:56:35.425 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: #zoneManagerDetailForm\:detail_business\:name, URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:35.426 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: d75e02203b76ade64fd99a0ee3896209 
2025-08-03 22:56:35.430 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=d75e02203b76ade64fd99a0ee3896209, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#zoneManagerDetailForm\:detail_business\:name, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:35.430182223, enableHealing=false) 
2025-08-03 22:56:35.559  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:35.560 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:35.561 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:35.562 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:35.562638061, enableHealing=true) 
2025-08-03 22:56:35.714  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:35.715 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:35.716 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:35.718 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:35.718293715, enableHealing=true) 
2025-08-03 22:56:35.801  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:35.801 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:35.802 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:35.818 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:35.818708739, enableHealing=true) 
2025-08-03 22:56:35.980  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.cssSelector(#zoneManagerDetailForm\:detail_business\:displayName) 
2025-08-03 22:56:35.981 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: #zoneManagerDetailForm\:detail_business\:displayName, URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:35.982 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: a087fbaf5408fa0c49d5d91aa3e13eef 
2025-08-03 22:56:35.985 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=a087fbaf5408fa0c49d5d91aa3e13eef, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#zoneManagerDetailForm\:detail_business\:displayName, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:35.985300426, enableHealing=false) 
2025-08-03 22:56:36.061  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:36.062 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:36.062 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:36.065 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:36.065549677, enableHealing=true) 
2025-08-03 22:56:36.156  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:36.157 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:36.158 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:36.162 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:36.162456105, enableHealing=true) 
2025-08-03 22:56:36.233  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:36.240 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:36.241 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:36.245 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:36.245462366, enableHealing=true) 
2025-08-03 22:56:36.322  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.cssSelector(#zoneManagerDetailForm\:detail_business\:displayName) 
2025-08-03 22:56:36.323 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: #zoneManagerDetailForm\:detail_business\:displayName, URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:36.323 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: a087fbaf5408fa0c49d5d91aa3e13eef 
2025-08-03 22:56:36.327 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=a087fbaf5408fa0c49d5d91aa3e13eef, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#zoneManagerDetailForm\:detail_business\:displayName, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:36.327220187, enableHealing=false) 
2025-08-03 22:56:36.383  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:36.385 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:36.386 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:36.389 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:36.389818846, enableHealing=true) 
2025-08-03 22:56:36.539  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:36.540 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:36.540 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:36.544 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:36.544499890, enableHealing=true) 
2025-08-03 22:56:36.712  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:36.715 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:36.716 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:36.719 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:36.719065177, enableHealing=true) 
2025-08-03 22:56:36.730  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:36.730 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:36.731 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:36.735 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:36.735576739, enableHealing=true) 
2025-08-03 22:56:36.828  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.cssSelector(#zoneManagerDetailForm\:detail_business\:zoneDescription) 
2025-08-03 22:56:36.829 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: #zoneManagerDetailForm\:detail_business\:zoneDescription, URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:36.830 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: df604214339cf1165f4b1adba19e4d15 
2025-08-03 22:56:36.833 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=df604214339cf1165f4b1adba19e4d15, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#zoneManagerDetailForm\:detail_business\:zoneDescription, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:36.833762312, enableHealing=false) 
2025-08-03 22:56:36.850  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:36.851 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:36.852 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:36.856 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:36.856263250, enableHealing=true) 
2025-08-03 22:56:36.986  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:36.987 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:36.988 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:36.990 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:36.990603200, enableHealing=true) 
2025-08-03 22:56:37.034  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:37.035 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:37.035 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:37.038 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:37.038560808, enableHealing=true) 
2025-08-03 22:56:37.149  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:37.150 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:37.150 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:37.154 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:37.154238864, enableHealing=true) 
2025-08-03 22:56:37.208  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.cssSelector(#zoneManagerDetailForm\:detail_business\:zoneDescription) 
2025-08-03 22:56:37.210 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: #zoneManagerDetailForm\:detail_business\:zoneDescription, URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:37.212 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: df604214339cf1165f4b1adba19e4d15 
2025-08-03 22:56:37.223 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=df604214339cf1165f4b1adba19e4d15, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#zoneManagerDetailForm\:detail_business\:zoneDescription, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:37.223478320, enableHealing=false) 
2025-08-03 22:56:37.308  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:37.310 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:37.310 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:37.317 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:37.317120257, enableHealing=true) 
2025-08-03 22:56:37.472  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:37.473 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:37.473 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:37.478 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:37.478134768, enableHealing=true) 
2025-08-03 22:56:37.596  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:37.600 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:37.606 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:37.608 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:37.608882725, enableHealing=true) 
2025-08-03 22:56:37.744  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:37.745 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:37.746 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:37.748 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:37.748884685, enableHealing=true) 
2025-08-03 22:56:37.789  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:37.795 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:37.796 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:37.799 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:37.799370111, enableHealing=true) 
2025-08-03 22:56:37.928  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.cssSelector(#zoneManagerDetailForm\:detail_business\:id) 
2025-08-03 22:56:37.931 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: #zoneManagerDetailForm\:detail_business\:id, URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:37.931 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 1e66b4648b97b3a38a04e359b07d8814 
2025-08-03 22:56:37.941 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=1e66b4648b97b3a38a04e359b07d8814, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#zoneManagerDetailForm\:detail_business\:id, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:37.940353967, enableHealing=false) 
2025-08-03 22:56:37.989  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:37.991 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:37.992 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:37.996 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:37.996547650, enableHealing=true) 
2025-08-03 22:56:38.088  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:38.090 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:38.090 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:38.092 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:38.092368399, enableHealing=true) 
2025-08-03 22:56:38.175  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:38.177 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:38.178 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:38.181 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:38.181485795, enableHealing=true) 
2025-08-03 22:56:38.276  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.cssSelector(#zoneManagerDetailForm\:detail_business\:btnSaveCreate) 
2025-08-03 22:56:38.276 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: #zoneManagerDetailForm\:detail_business\:btnSaveCreate, URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:38.277 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: e55b375cf5a1b28449d72f84787082d8 
2025-08-03 22:56:38.283 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=e55b375cf5a1b28449d72f84787082d8, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#zoneManagerDetailForm\:detail_business\:btnSaveCreate, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:38.283066806, enableHealing=false) 
2025-08-03 22:56:38.412  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:38.418 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:38.420 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:38.425 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:38.424980353, enableHealing=true) 
2025-08-03 22:56:38.556  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:38.557 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:38.558 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:38.560 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:38.560290856, enableHealing=true) 
2025-08-03 22:56:38.595  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:38.604 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:38.605 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:38.608 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:38.608039614, enableHealing=true) 
2025-08-03 22:56:38.767  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:38.768 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:38.768 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:38.771 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:38.771216636, enableHealing=true) 
2025-08-03 22:56:38.902  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:38.903 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:38.904 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:38.908 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:38.908097392, enableHealing=true) 
2025-08-03 22:56:39.033  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:39.033 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:39.035 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:39.040 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:39.040498010, enableHealing=true) 
2025-08-03 22:56:39.196  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:39.197 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:39.198 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:39.200 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:39.200352207, enableHealing=true) 
2025-08-03 22:56:39.234  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:39.243 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:39.246 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:39.247 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:39.247729330, enableHealing=true) 
2025-08-03 22:56:39.455  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:39.463 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:39.466 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:39.471  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.cssSelector(#zoneForm\:homepage_business\:btnSearch) 
2025-08-03 22:56:39.475 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: #zoneForm\:homepage_business\:btnSearch, URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:39.473 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:39.473613270, enableHealing=true) 
2025-08-03 22:56:39.483 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 610016a09e8f2493db42903843ad4a90 
2025-08-03 22:56:39.486 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=610016a09e8f2493db42903843ad4a90, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#zoneForm\:homepage_business\:btnSearch, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:39.486527230, enableHealing=false) 
2025-08-03 22:56:39.785  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.xpath(//*[@class='pi pi-spin pi-spinner ajax-loader']) 
2025-08-03 22:56:39.789 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:39.790 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 5b282135dfc5b6a07979963350f4c98e 
2025-08-03 22:56:39.791 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=5b282135dfc5b6a07979963350f4c98e, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=//*[@class='pi pi-spin pi-spinner ajax-loader'], type=By.xpath), command=findElement, createdDate=2025-08-03T19:56:39.791144352, enableHealing=true) 
2025-08-03 22:56:39.794  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:39.795 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:39.796 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:39.799 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:39.799769494, enableHealing=true) 
2025-08-03 22:56:39.956  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.cssSelector(#zoneForm\:homepage_business\:btnReset) 
2025-08-03 22:56:39.958 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: #zoneForm\:homepage_business\:btnReset, URL(source): http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, URL(key): , Command: findElements, KEY_SELECTOR_URL: false 
2025-08-03 22:56:39.958 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: f336011d2ef61b8814fab52899c5448e 
2025-08-03 22:56:39.960 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=f336011d2ef61b8814fab52899c5448e, url=http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#zoneForm\:homepage_business\:btnReset, type=By.cssSelector), command=findElements, createdDate=2025-08-03T19:56:39.960840210, enableHealing=false) 
2025-08-03 22:56:39.996  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:39.998 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:40.007 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:40.015 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:40.015746067, enableHealing=true) 
2025-08-03 22:56:40.151  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:40.155 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:40.160 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:40.162 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:40.162011838, enableHealing=true) 
2025-08-03 22:56:40.355  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:40.358 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:40.359 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:40.364 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:40.364680613, enableHealing=true) 
2025-08-03 22:56:40.514  INFO 1 - [7878-exec-5] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:40.517 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:40.522 DEBUG 1 - [7878-exec-5] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:40.526 DEBUG 1 - [7878-exec-5] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:40.526321585, enableHealing=true) 
2025-08-03 22:56:40.680  INFO 1 - [7878-exec-9] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:40.682 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:40.683 DEBUG 1 - [7878-exec-9] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:40.686 DEBUG 1 - [7878-exec-9] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:40.686767140, enableHealing=true) 
2025-08-03 22:56:40.921  INFO 1 - [7878-exec-3] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:40.924 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:40.924 DEBUG 1 - [7878-exec-3] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:40.928 DEBUG 1 - [7878-exec-3] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:40.928477724, enableHealing=true) 
2025-08-03 22:56:41.103  INFO 1 - [7878-exec-2] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:41.104 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:41.107 DEBUG 1 - [7878-exec-2] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:41.112 DEBUG 1 - [7878-exec-2] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:41.112682497, enableHealing=true) 
2025-08-03 22:56:41.271  INFO 1 - [7878-exec-1] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:41.272 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:41.272 DEBUG 1 - [7878-exec-1] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:41.274 DEBUG 1 - [7878-exec-1] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:41.274735309, enableHealing=true) 
2025-08-03 22:56:41.376  INFO 1 - [7878-exec-6] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:41.377 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:41.377 DEBUG 1 - [7878-exec-6] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:41.387 DEBUG 1 - [7878-exec-6] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:41.387498311, enableHealing=true) 
2025-08-03 22:56:41.534  INFO 1 - [878-exec-10] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:41.535 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:41.536 DEBUG 1 - [878-exec-10] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:41.539 DEBUG 1 - [878-exec-10] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:41.539901982, enableHealing=true) 
2025-08-03 22:56:41.712  INFO 1 - [7878-exec-7] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:41.714 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:41.716 DEBUG 1 - [7878-exec-7] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:41.720 DEBUG 1 - [7878-exec-7] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:41.719775932, enableHealing=true) 
2025-08-03 22:56:41.845  INFO 1 - [7878-exec-4] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:41.846 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:41.847 DEBUG 1 - [7878-exec-4] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:41.849 DEBUG 1 - [7878-exec-4] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:41.849125153, enableHealing=true) 
2025-08-03 22:56:42.020  INFO 1 - [7878-exec-8] healenium                        : [Save Elements] Request: By.cssSelector(#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name) 
2025-08-03 22:56:42.028 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, URL(source): http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, URL(key): , Command: findElement, KEY_SELECTOR_URL: false 
2025-08-03 22:56:42.031 DEBUG 1 - [7878-exec-8] healenium                        : [Selector ID] Result ID: 7dc414dbd88b85cd3a6fa200ee462c8d 
2025-08-03 22:56:42.037 DEBUG 1 - [7878-exec-8] healenium                        : [Save Elements] Selector: Selector(uid=7dc414dbd88b85cd3a6fa200ee462c8d, url=http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP, className=HealeniumFindElementPostRequest, methodName=findElement, locator=Locator(value=#listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_creation_viewer\:name, type=By.cssSelector), command=findElement, createdDate=2025-08-03T19:56:42.037150799, enableHealing=true) 
