2025-08-03 22:39:06.017 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:06.027  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:06.072  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:06.108  WARN 1 - [ttp-epoll-1] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:06.110  WARN 1 - [ttp-epoll-1] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:06.142 DEBUG 1 - [ttp-epoll-1] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:06.163  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:06.186  WARN 1 - [ttp-epoll-1] healenium                        : Trying to heal... 
2025-08-03 22:39:06.234 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:06.270  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:06.270  WARN 1 - [ttp-epoll-1] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:06.293  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:06.351  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:06.373  WARN 1 - [ttp-epoll-1] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:06.374  WARN 1 - [ttp-epoll-1] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:06.415 DEBUG 1 - [ttp-epoll-1] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:06.429 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:06.453  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:06.486  WARN 1 - [ttp-epoll-1] healenium                        : Trying to heal... 
2025-08-03 22:39:06.536  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:06.579  WARN 1 - [ttp-epoll-1] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:06.600 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:06.603  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:06.622  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:06.661  WARN 1 - [ttp-epoll-1] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:06.662  WARN 1 - [ttp-epoll-1] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:06.673  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:06.699 DEBUG 1 - [ttp-epoll-1] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:06.759  WARN 1 - [ttp-epoll-1] healenium                        : Trying to heal... 
2025-08-03 22:39:06.798 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:06.820  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:06.833  WARN 1 - [ttp-epoll-1] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:06.845  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:06.869  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:06.911  WARN 1 - [ttp-epoll-1] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:06.912  WARN 1 - [ttp-epoll-1] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:06.944 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:06.954 DEBUG 1 - [ttp-epoll-1] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:06.961  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:07.019  WARN 1 - [ttp-epoll-1] healenium                        : Trying to heal... 
2025-08-03 22:39:07.050  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:07.104  WARN 1 - [ttp-epoll-1] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:07.125  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:07.130 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:07.149  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:07.192  WARN 1 - [ttp-epoll-1] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:07.196  WARN 1 - [ttp-epoll-1] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:07.207  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:07.234 DEBUG 1 - [ttp-epoll-1] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:07.288  WARN 1 - [ttp-epoll-1] healenium                        : Trying to heal... 
2025-08-03 22:39:07.306 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:07.328  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:07.360  WARN 1 - [ttp-epoll-1] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:07.368  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:07.373  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:07.413 DEBUG 1 - [ttp-epoll-1] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:07.447  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:07.453  WARN 1 - [ttp-epoll-6] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:07.455  WARN 1 - [ttp-epoll-6] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:07.489 DEBUG 1 - [ttp-epoll-6] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:07.548  WARN 1 - [ttp-epoll-6] healenium                        : Trying to heal... 
2025-08-03 22:39:07.601  WARN 1 - [ttp-epoll-6] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:07.615  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:07.663  WARN 1 - [ttp-epoll-6] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:07.664  WARN 1 - [ttp-epoll-6] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:07.682 DEBUG 1 - [ttp-epoll-6] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:07.733  WARN 1 - [ttp-epoll-6] healenium                        : Trying to heal... 
2025-08-03 22:39:07.808  WARN 1 - [ttp-epoll-6] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:07.819  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:07.826  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:07.869  WARN 1 - [ttp-epoll-1] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:07.869  WARN 1 - [ttp-epoll-1] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:07.879 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:07.894  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:07.907 DEBUG 1 - [ttp-epoll-1] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:07.940  WARN 1 - [ttp-epoll-1] healenium                        : Trying to heal... 
2025-08-03 22:39:07.968  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:08.038  WARN 1 - [ttp-epoll-1] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:08.050  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:08.068 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:08.093  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:08.112  WARN 1 - [ttp-epoll-1] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:08.112  WARN 1 - [ttp-epoll-1] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:08.133 DEBUG 1 - [ttp-epoll-1] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:08.138  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:08.182  WARN 1 - [ttp-epoll-1] healenium                        : Trying to heal... 
2025-08-03 22:39:08.211 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:08.242  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:08.266  WARN 1 - [ttp-epoll-1] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:08.288  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:08.313  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:08.346  WARN 1 - [ttp-epoll-1] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:08.346  WARN 1 - [ttp-epoll-1] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:08.363 DEBUG 1 - [ttp-epoll-1] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:08.369 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:08.385  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:08.400  WARN 1 - [ttp-epoll-1] healenium                        : Trying to heal... 
2025-08-03 22:39:08.445  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:08.475  WARN 1 - [ttp-epoll-1] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:08.490  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:08.524 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:08.546  WARN 1 - [ttp-epoll-1] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:08.547  WARN 1 - [ttp-epoll-1] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:08.550  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:08.580 DEBUG 1 - [ttp-epoll-1] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:08.614  WARN 1 - [ttp-epoll-1] healenium                        : Trying to heal... 
2025-08-03 22:39:08.618  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:08.656  WARN 1 - [ttp-epoll-1] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:08.670  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:08.670 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:08.699  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:08.732  WARN 1 - [ttp-epoll-1] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:08.735  WARN 1 - [ttp-epoll-1] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:08.752  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:08.770 DEBUG 1 - [ttp-epoll-1] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:08.836 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:08.845  WARN 1 - [ttp-epoll-1] healenium                        : Trying to heal... 
2025-08-03 22:39:08.855  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:08.894  WARN 1 - [ttp-epoll-1] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:08.901  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:08.907  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:08.961  WARN 1 - [ttp-epoll-1] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:08.963  WARN 1 - [ttp-epoll-1] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:08.980 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:09.000  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:09.008 DEBUG 1 - [ttp-epoll-1] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:09.048  WARN 1 - [ttp-epoll-1] healenium                        : Trying to heal... 
2025-08-03 22:39:09.069  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:09.122  WARN 1 - [ttp-epoll-1] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:09.135  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:09.140 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:09.157  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:09.212  WARN 1 - [ttp-epoll-1] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:09.213  WARN 1 - [ttp-epoll-1] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:09.232  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:09.238 DEBUG 1 - [ttp-epoll-1] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:09.289  WARN 1 - [ttp-epoll-1] healenium                        : Trying to heal... 
2025-08-03 22:39:09.327 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:09.349  WARN 1 - [ttp-epoll-1] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:09.358  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:09.366  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:09.401  WARN 1 - [ttp-epoll-1] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:09.404  WARN 1 - [ttp-epoll-1] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:09.411  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:09.428 DEBUG 1 - [ttp-epoll-1] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:09.481  WARN 1 - [ttp-epoll-1] healenium                        : Trying to heal... 
2025-08-03 22:39:09.501 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:09.521  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:09.563  WARN 1 - [ttp-epoll-1] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:09.585  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:09.590  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:09.628  WARN 1 - [ttp-epoll-1] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:09.629  WARN 1 - [ttp-epoll-1] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:09.653 DEBUG 1 - [ttp-epoll-1] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:09.654 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:09.673  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:09.701  WARN 1 - [ttp-epoll-1] healenium                        : Trying to heal... 
2025-08-03 22:39:09.722  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:09.779  WARN 1 - [ttp-epoll-1] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:09.793  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:09.820 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:09.839  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:09.852  WARN 1 - [ttp-epoll-1] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:09.852  WARN 1 - [ttp-epoll-1] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:09.869 DEBUG 1 - [ttp-epoll-1] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:09.891  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:09.898  WARN 1 - [ttp-epoll-1] healenium                        : Trying to heal... 
2025-08-03 22:39:09.960  WARN 1 - [ttp-epoll-1] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:09.975 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:09.976  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:09.996  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:10.050  WARN 1 - [ttp-epoll-1] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:10.052  WARN 1 - [ttp-epoll-1] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:10.085  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:10.086 DEBUG 1 - [ttp-epoll-1] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:10.126  WARN 1 - [ttp-epoll-1] healenium                        : Trying to heal... 
2025-08-03 22:39:10.141 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:10.158  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:10.183  WARN 1 - [ttp-epoll-1] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:10.196  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:10.223  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:10.253  WARN 1 - [ttp-epoll-1] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:10.253  WARN 1 - [ttp-epoll-1] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:10.286 DEBUG 1 - [ttp-epoll-1] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:10.306 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:10.328  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:10.346  WARN 1 - [ttp-epoll-1] healenium                        : Trying to heal... 
2025-08-03 22:39:10.376  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:10.397  WARN 1 - [ttp-epoll-1] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:10.408  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:10.422 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:10.455  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:10.495  WARN 1 - [ttp-epoll-1] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:10.496  WARN 1 - [ttp-epoll-1] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:10.525 DEBUG 1 - [ttp-epoll-1] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:10.531  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:10.568  WARN 1 - [ttp-epoll-1] healenium                        : Trying to heal... 
2025-08-03 22:39:10.608 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:10.621  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:10.630  WARN 1 - [ttp-epoll-1] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:10.641  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:10.673  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:10.693  WARN 1 - [ttp-epoll-1] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:10.694  WARN 1 - [ttp-epoll-1] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:10.723 DEBUG 1 - [ttp-epoll-1] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:10.753 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:10.775  WARN 1 - [ttp-epoll-1] healenium                        : Trying to heal... 
2025-08-03 22:39:10.778  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:10.850  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:10.849  WARN 1 - [ttp-epoll-1] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:10.861  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:10.900 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:10.904  WARN 1 - [ttp-epoll-1] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:10.906  WARN 1 - [ttp-epoll-1] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:10.924  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:10.951 DEBUG 1 - [ttp-epoll-1] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:10.986  WARN 1 - [ttp-epoll-1] healenium                        : Trying to heal... 
2025-08-03 22:39:10.993  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:11.051  WARN 1 - [ttp-epoll-1] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:11.064  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:11.068 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:11.098  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:11.147  WARN 1 - [ttp-epoll-1] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:11.149  WARN 1 - [ttp-epoll-1] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:11.151  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:11.177 DEBUG 1 - [ttp-epoll-1] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:11.229  WARN 1 - [ttp-epoll-1] healenium                        : Trying to heal... 
2025-08-03 22:39:11.247 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:11.280  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:11.315  WARN 1 - [ttp-epoll-1] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:11.336  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:11.354  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:11.399  WARN 1 - [ttp-epoll-1] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:11.399  WARN 1 - [ttp-epoll-1] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:11.421 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:11.445 DEBUG 1 - [ttp-epoll-1] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:11.457  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:11.518  WARN 1 - [ttp-epoll-1] healenium                        : Trying to heal... 
2025-08-03 22:39:11.545  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:11.586  WARN 1 - [ttp-epoll-1] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:11.600  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:11.614 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:11.635  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:11.682  WARN 1 - [ttp-epoll-1] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:11.683  WARN 1 - [ttp-epoll-1] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:11.689  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:11.721 DEBUG 1 - [ttp-epoll-1] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:11.759 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:11.760  WARN 1 - [ttp-epoll-1] healenium                        : Trying to heal... 
2025-08-03 22:39:11.785  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:11.842  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:11.849  WARN 1 - [ttp-epoll-1] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:11.863  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:11.887 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:11.908  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:11.909  WARN 1 - [ttp-epoll-1] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:11.910  WARN 1 - [ttp-epoll-1] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:11.952 DEBUG 1 - [ttp-epoll-1] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:11.963  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:12.016  WARN 1 - [ttp-epoll-1] healenium                        : Trying to heal... 
2025-08-03 22:39:12.057 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:12.078  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:12.089  WARN 1 - [ttp-epoll-1] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:12.102  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:12.134  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:12.152  WARN 1 - [ttp-epoll-1] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:12.152  WARN 1 - [ttp-epoll-1] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:12.168 DEBUG 1 - [ttp-epoll-1] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:12.210  WARN 1 - [ttp-epoll-1] healenium                        : Trying to heal... 
2025-08-03 22:39:12.218 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:12.240  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:12.284  WARN 1 - [ttp-epoll-1] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:12.295  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:12.303  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:12.360  WARN 1 - [ttp-epoll-6] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:12.360  WARN 1 - [ttp-epoll-6] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:12.362 DEBUG 1 - [ttp-epoll-1] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:12.376  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:12.396 DEBUG 1 - [ttp-epoll-6] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:12.425  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:12.434  WARN 1 - [ttp-epoll-6] healenium                        : Trying to heal... 
2025-08-03 22:39:12.491  WARN 1 - [ttp-epoll-6] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:12.508  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:12.521 DEBUG 1 - [ttp-epoll-1] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:12.541  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:12.576  WARN 1 - [ttp-epoll-6] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:12.577  WARN 1 - [ttp-epoll-6] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:12.601 DEBUG 1 - [ttp-epoll-6] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:12.632  WARN 1 - [ttp-epoll-6] healenium                        : Trying to heal... 
2025-08-03 22:39:12.679  WARN 1 - [ttp-epoll-6] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:12.690  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:12.752  WARN 1 - [ttp-epoll-6] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:12.753  WARN 1 - [ttp-epoll-6] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:12.783 DEBUG 1 - [ttp-epoll-6] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:12.826  WARN 1 - [ttp-epoll-6] healenium                        : Trying to heal... 
2025-08-03 22:39:12.868  WARN 1 - [ttp-epoll-6] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:12.880  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:12.883  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:12.919  WARN 1 - [ttp-epoll-1] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:12.919  WARN 1 - [ttp-epoll-1] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:12.934 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:12.950 DEBUG 1 - [ttp-epoll-1] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:12.952  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:12.994  WARN 1 - [ttp-epoll-1] healenium                        : Trying to heal... 
2025-08-03 22:39:13.031  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:13.066  WARN 1 - [ttp-epoll-1] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:13.080  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:13.097 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:13.111  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:13.128  WARN 1 - [ttp-epoll-1] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:13.130  WARN 1 - [ttp-epoll-1] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:13.161 DEBUG 1 - [ttp-epoll-1] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:13.165  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:13.195  WARN 1 - [ttp-epoll-1] healenium                        : Trying to heal... 
2025-08-03 22:39:13.244 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:13.263  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:13.279  WARN 1 - [ttp-epoll-1] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:13.298  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:13.327  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:13.350  WARN 1 - [ttp-epoll-1] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:13.351  WARN 1 - [ttp-epoll-1] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:13.371 DEBUG 1 - [ttp-epoll-1] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:13.388 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:13.405  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:13.406  WARN 1 - [ttp-epoll-1] healenium                        : Trying to heal... 
2025-08-03 22:39:13.479  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:13.497  WARN 1 - [ttp-epoll-1] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:13.510  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:13.573 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:13.588  WARN 1 - [ttp-epoll-1] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:13.591  WARN 1 - [ttp-epoll-1] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:13.600  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:13.632 DEBUG 1 - [ttp-epoll-1] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:13.653  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:13.678  WARN 1 - [ttp-epoll-1] healenium                        : Trying to heal... 
2025-08-03 22:39:13.739 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:13.783  WARN 1 - [ttp-epoll-1] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:13.803  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:13.812  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:13.861  WARN 1 - [ttp-epoll-1] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:13.862  WARN 1 - [ttp-epoll-1] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:13.870  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:13.885 DEBUG 1 - [ttp-epoll-1] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:13.917 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:13.933  WARN 1 - [ttp-epoll-1] healenium                        : Trying to heal... 
2025-08-03 22:39:13.940  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:14.010  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:14.010  WARN 1 - [ttp-epoll-1] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:14.023  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:14.086 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:14.088  WARN 1 - [ttp-epoll-1] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:14.089  WARN 1 - [ttp-epoll-1] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:14.107  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:14.110 DEBUG 1 - [ttp-epoll-1] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:14.139  WARN 1 - [ttp-epoll-1] healenium                        : Trying to heal... 
2025-08-03 22:39:14.153  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:14.206  WARN 1 - [ttp-epoll-1] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:14.226 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:14.230  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:14.251  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:14.306  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:14.325  WARN 1 - [ttp-epoll-1] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:14.326  WARN 1 - [ttp-epoll-1] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:14.347 DEBUG 1 - [ttp-epoll-1] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:14.373 DEBUG 1 - [ttp-epoll-6] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:14.384  WARN 1 - [ttp-epoll-1] healenium                        : Trying to heal... 
2025-08-03 22:39:14.395  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:14.446  WARN 1 - [ttp-epoll-1] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:14.453  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:14.459  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 22:39:14.519  WARN 1 - [ttp-epoll-6] healenium                        : Failed to find an element using locator By.cssSelector: #menuform\:om_HomeListManager 
2025-08-03 22:39:14.520  WARN 1 - [ttp-epoll-6] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#menuform\:om_HomeListManager"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElement {value=#menuform\:om_HomeListManager, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62 
2025-08-03 22:39:14.539 DEBUG 1 - [ttp-epoll-1] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:14.561 DEBUG 1 - [ttp-epoll-6] healenium                        : [Get Reference Elements] Request. Locator: #menuform\:om_HomeListManager, Command: findElement, Url: http://************:8080/AMLUI/Modules/admin/jsp/EventViewer/Homepage.xhtml 
2025-08-03 22:39:14.565  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:14.608  WARN 1 - [ttp-epoll-6] healenium                        : Trying to heal... 
2025-08-03 22:39:14.634  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 22:39:14.684  WARN 1 - [ttp-epoll-6] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [1d05131bb93987fce4b57ceea1610a62, findElements {value=li#menuform:om_nosession, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39935}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.EF...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 9a83b3ba49dc, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 1d05131bb93987fce4b57ceea1610a62\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 22:39:14.703 DEBUG 1 - [ttp-epoll-1] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 22:39:14.825  INFO 1 - [ttp-epoll-1] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.1D9711549CC60E3F7A6B4B97F216F02D.d.BC7AF49B68707B14DB814C4E80D92E8C.e.37026"}} 
2025-08-03 22:39:14.846  INFO 1 - [ttp-epoll-6] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
