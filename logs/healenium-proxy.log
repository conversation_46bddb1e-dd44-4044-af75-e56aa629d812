2025-08-04 03:00:02.760  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:02.866 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:02.935  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.54A7CA1225C2C8B86AADE3F8D3EAECD1.e.11591"}} 
2025-08-04 03:00:03.008  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#zoneForm\\:homepage_business\\:zoneName",\n   "using": "css selector"\n } 
2025-08-04 03:00:03.100 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #zoneForm\:homepage_business\:zoneName, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:03.116  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.54A7CA1225C2C8B86AADE3F8D3EAECD1.e.11595"}]} 
2025-08-04 03:00:03.214  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:03.275  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:03.318 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:03.333  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.BEA78A1EF3148D0B2F47E609FE1099E1.e.35544"}} 
2025-08-04 03:00:03.356 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:03.368  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.54A7CA1225C2C8B86AADE3F8D3EAECD1.e.11591"}} 
2025-08-04 03:00:03.390  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:Tab_activity\\:btnASearch",\n   "using": "css selector"\n } 
2025-08-04 03:00:03.446  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#zoneForm\\:homepage_business\\:zoneName",\n   "using": "css selector"\n } 
2025-08-04 03:00:03.550 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #zoneForm\:homepage_business\:zoneName, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:03.580  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.54A7CA1225C2C8B86AADE3F8D3EAECD1.e.11595"}]} 
2025-08-04 03:00:03.669 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_activity\:btnASearch, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:03.695  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.BEA78A1EF3148D0B2F47E609FE1099E1.e.35558"}]} 
2025-08-04 03:00:03.761  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:03.865 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:03.878  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.BEA78A1EF3148D0B2F47E609FE1099E1.e.35544"}} 
2025-08-04 03:00:03.956  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:04.236  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:04.260 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:04.272  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.BEA78A1EF3148D0B2F47E609FE1099E1.e.35544"}} 
2025-08-04 03:00:04.345 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:04.356  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fa[contains(text(),'Activity')]"\n } 
2025-08-04 03:00:04.364  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.54A7CA1225C2C8B86AADE3F8D3EAECD1.e.11591"}} 
2025-08-04 03:00:04.406  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#zoneForm\\:homepage_business\\:zoneDispName",\n   "using": "css selector"\n } 
2025-08-04 03:00:04.463 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //a[contains(text(),'Activity')], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:04.484  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.BEA78A1EF3148D0B2F47E609FE1099E1.e.35571"}} 
2025-08-04 03:00:04.509 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #zoneForm\:homepage_business\:zoneDispName, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:04.534  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.54A7CA1225C2C8B86AADE3F8D3EAECD1.e.11597"}]} 
2025-08-04 03:00:04.604  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:04.645  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:04.687 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:04.706  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.BEA78A1EF3148D0B2F47E609FE1099E1.e.35544"}} 
2025-08-04 03:00:04.747  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:04.766 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:04.784  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.54A7CA1225C2C8B86AADE3F8D3EAECD1.e.11591"}} 
2025-08-04 03:00:04.816  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fa[contains(text(),'Activity')]"\n } 
2025-08-04 03:00:04.856 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:04.873  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#zoneForm\\:homepage_business\\:zoneDispName",\n   "using": "css selector"\n } 
2025-08-04 03:00:04.876  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.1A09F6F2E0597E45FF798CAE26448653.e.14873"}} 
2025-08-04 03:00:04.943 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //a[contains(text(),'Activity')], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:04.945  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:tab_list_set_manager\\:_listSetsTbl\\:_colId",\n   "using": "css selector"\n } 
2025-08-04 03:00:04.975  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.BEA78A1EF3148D0B2F47E609FE1099E1.e.35571"}]} 
2025-08-04 03:00:04.994 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #zoneForm\:homepage_business\:zoneDispName, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:05.027  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.54A7CA1225C2C8B86AADE3F8D3EAECD1.e.11597"}]} 
2025-08-04 03:00:05.102 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:tab_list_set_manager\:_listSetsTbl\:_colId, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:05.129  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.1A09F6F2E0597E45FF798CAE26448653.e.14904"}]} 
2025-08-04 03:00:05.332  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:Tab_activity\\:searchDateFrom",\n   "using": "css selector"\n } 
2025-08-04 03:00:05.451  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:05.453 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_activity\:searchDateFrom, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:05.479  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.BEA78A1EF3148D0B2F47E609FE1099E1.e.35573"}} 
2025-08-04 03:00:05.553 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:05.581  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.54A7CA1225C2C8B86AADE3F8D3EAECD1.e.11591"}} 
2025-08-04 03:00:05.628  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#zoneForm\\:homepage_business\\:btnSearch",\n   "using": "css selector"\n } 
2025-08-04 03:00:05.748 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #zoneForm\:homepage_business\:btnSearch, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:05.776  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.54A7CA1225C2C8B86AADE3F8D3EAECD1.e.11601"}]} 
2025-08-04 03:00:05.943  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:06.162 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:06.187  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.54A7CA1225C2C8B86AADE3F8D3EAECD1.e.11591"}} 
2025-08-04 03:00:06.246  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fa[.=\"Zone392489986\"]"\n } 
2025-08-04 03:00:06.323 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //a[.="Zone392489986"], Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:06.337  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.54A7CA1225C2C8B86AADE3F8D3EAECD1.e.11608"}]} 
2025-08-04 03:00:06.342  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:06.381 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:06.392  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.54A7CA1225C2C8B86AADE3F8D3EAECD1.e.11591"}} 
2025-08-04 03:00:06.434  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#zoneForm\\:homepage_business\\:btnSearch",\n   "using": "css selector"\n } 
2025-08-04 03:00:06.500 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #zoneForm\:homepage_business\:btnSearch, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:06.514  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.54A7CA1225C2C8B86AADE3F8D3EAECD1.e.11609"}]} 
2025-08-04 03:00:06.573  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_admin_HomeZoneManager",\n   "using": "css selector"\n } 
2025-08-04 03:00:06.614 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_admin_HomeZoneManager, Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:06.629  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.54A7CA1225C2C8B86AADE3F8D3EAECD1.e.11610"}} 
2025-08-04 03:00:06.708  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:06.775 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:06.789  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.54A7CA1225C2C8B86AADE3F8D3EAECD1.e.11591"}} 
2025-08-04 03:00:06.830  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_admin_HomeZoneManager",\n   "using": "css selector"\n } 
2025-08-04 03:00:06.886 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_admin_HomeZoneManager, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:06.904  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.54A7CA1225C2C8B86AADE3F8D3EAECD1.e.11610"}]} 
2025-08-04 03:00:07.240  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:08.156 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:08.169  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.0852C5D17286DC92DA5EB3AF34009409.e.11614"}} 
2025-08-04 03:00:08.250  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fform[@id='topbar-right']\u002ful\u002flabel"\n } 
2025-08-04 03:00:08.307 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //form[@id='topbar-right']/ul/label, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:08.318  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.0852C5D17286DC92DA5EB3AF34009409.e.11640"}]} 
2025-08-04 03:00:08.357  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:08.394 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:08.408  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.0852C5D17286DC92DA5EB3AF34009409.e.11614"}} 
2025-08-04 03:00:08.447  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#zoneForm\\:homepage_business\\:btnSearch",\n   "using": "css selector"\n } 
2025-08-04 03:00:08.508 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #zoneForm\:homepage_business\:btnSearch, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:08.525  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.0852C5D17286DC92DA5EB3AF34009409.e.11664"}]} 
2025-08-04 03:00:08.570  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:08.612 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:08.622  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.0852C5D17286DC92DA5EB3AF34009409.e.11614"}} 
2025-08-04 03:00:08.662  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#zoneForm\\:homepage_business\\:btnReset",\n   "using": "css selector"\n } 
2025-08-04 03:00:08.728 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #zoneForm\:homepage_business\:btnReset, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:08.744  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.0852C5D17286DC92DA5EB3AF34009409.e.11663"}]} 
2025-08-04 03:00:10.395  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:10.469 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:10.484  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.1A09F6F2E0597E45FF798CAE26448653.e.14873"}} 
2025-08-04 03:00:10.539  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:tab_list_set_manager\\:_listSetsTbl\\:0\\:lnkCode",\n   "using": "css selector"\n } 
2025-08-04 03:00:10.603 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:tab_list_set_manager\:_listSetsTbl\:0\:lnkCode, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:10.611  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:10.620  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.1A09F6F2E0597E45FF798CAE26448653.e.14948"}]} 
2025-08-04 03:00:10.651 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:10.673  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.BEA78A1EF3148D0B2F47E609FE1099E1.e.35544"}} 
2025-08-04 03:00:10.733  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:Tab_activity\\:btnASearch",\n   "using": "css selector"\n } 
2025-08-04 03:00:10.802 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_activity\:btnASearch, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:10.822  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.BEA78A1EF3148D0B2F47E609FE1099E1.e.35574"}]} 
2025-08-04 03:00:10.873  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:10.880  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:10.995 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:11.015  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.BEA78A1EF3148D0B2F47E609FE1099E1.e.35544"}} 
2025-08-04 03:00:11.142 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:11.163  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.1A09F6F2E0597E45FF798CAE26448653.e.14873"}} 
2025-08-04 03:00:11.197  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002ftbody[@id='listManagerForm:homepage_business:tabViewListManager:Tab_activity:_tblResults_data']\u002f\u002ftr[1]\u002f\u002ftd[5]"\n } 
2025-08-04 03:00:11.269  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:11.345 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //tbody[@id='listManagerForm:homepage_business:tabViewListManager:Tab_activity:_tblResults_data']//tr[1]//td[5], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:11.362  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.BEA78A1EF3148D0B2F47E609FE1099E1.e.35587"}} 
2025-08-04 03:00:12.833  WARN 1 - [ttp-epoll-3] healenium                        : [Save Elements] Error during save elements: [f.B7C001BBE6C2F33C574A3A5438A0AE75.d.1A09F6F2E0597E45FF798CAE26448653.e.14873]. Message: stale element reference: stale element not found in the current frame\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [20a57fedf1afb9da426f2a59951fa9c5, executeScript {script=var items = [];var result = {items: null, url: null};var a = arguments[0];while (a != document) {  var child = a;  var i=0; while(child=child.previousElementSibling) i++;  var node = {tag:null,id:null,index:null,classes:[],other:{},innerText:""};  if (a.tagName !== undefined) {    node.tag = a.tagName.toLowerCase();  }  node.id = a.id;  node.index = i;  node.innerText = a.innerText || "";  if (a.hasAttribute("class")) {	  node.classes = a.attributes.class.value.split(' ');  }  for (index = 0; index < a.attributes.length; ++index) {      var attrName = a.attributes[index].name;      if (["id","class"].indexOf(attrName) == -1){		    node.other[attrName] = a.attributes[index].value;      }  };  items.unshift(node);  a = a.parentNode;}result.items = items;result.url = window.location.href;return JSON.stringify(result);, args=[{element-6066-11e4-a52e-4f735466cecf=f.B7C001BBE6C2F33C574A3A5438A0AE75.d.1A09F6F2E0597E45FF798CAE26448653.e.14873}]}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39195}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.zn...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 85fe87fd88fa, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 20a57fedf1afb9da426f2a59951fa9c5. Exception: org.openqa.selenium.StaleElementReferenceException: stale element reference: stale element not found in the current frame\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [20a57fedf1afb9da426f2a59951fa9c5, executeScript {script=var items = [];var result = {items: null, url: null};var a = arguments[0];while (a != document) {  var child = a;  var i=0; while(child=child.previousElementSibling) i++;  var node = {tag:null,id:null,index:null,classes:[],other:{},innerText:""};  if (a.tagName !== undefined) {    node.tag = a.tagName.toLowerCase();  }  node.id = a.id;  node.index = i;  node.innerText = a.innerText || "";  if (a.hasAttribute("class")) {	  node.classes = a.attributes.class.value.split(' ');  }  for (index = 0; index < a.attributes.length; ++index) {      var attrName = a.attributes[index].name;      if (["id","class"].indexOf(attrName) == -1){		    node.other[attrName] = a.attributes[index].value;      }  };  items.unshift(node);  a = a.parentNode;}result.items = items;result.url = window.location.href;return JSON.stringify(result);, args=[{element-6066-11e4-a52e-4f735466cecf=f.B7C001BBE6C2F33C574A3A5438A0AE75.d.1A09F6F2E0597E45FF798CAE26448653.e.14873}]}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39195}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.zn...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 85fe87fd88fa, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 20a57fedf1afb9da426f2a59951fa9c5 
2025-08-04 03:00:12.834  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.1A09F6F2E0597E45FF798CAE26448653.e.14873"}} 
2025-08-04 03:00:12.854  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:12.930  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fa[contains(text(),'Profiles-ListSet Association')]"\n } 
2025-08-04 03:00:12.988 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:13.006  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.3C655BBBF00F5632C632BE05BDFC2A82.e.35682"}} 
2025-08-04 03:00:13.055  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@title='Exit']"\n } 
2025-08-04 03:00:13.110 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@title='Exit'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:13.120  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.3C655BBBF00F5632C632BE05BDFC2A82.e.35683"}]} 
2025-08-04 03:00:13.142 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //a[contains(text(),'Profiles-ListSet Association')], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:13.173  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:13.222  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.1A09F6F2E0597E45FF798CAE26448653.e.15067"}} 
2025-08-04 03:00:13.242 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:13.258  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.3C655BBBF00F5632C632BE05BDFC2A82.e.35682"}} 
2025-08-04 03:00:13.300  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@title='Exit' or @title ='Quitter']"\n } 
2025-08-04 03:00:13.349  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:13.362 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@title='Exit' or @title ='Quitter'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:13.371  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.3C655BBBF00F5632C632BE05BDFC2A82.e.35683"}]} 
2025-08-04 03:00:13.411 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:13.434  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.1A09F6F2E0597E45FF798CAE26448653.e.15068"}} 
2025-08-04 03:00:13.453  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:13.481  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fa[contains(text(),'Profiles-ListSet Association')]"\n } 
2025-08-04 03:00:13.566 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml# 
2025-08-04 03:00:13.581  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.3C655BBBF00F5632C632BE05BDFC2A82.e.35682"}} 
2025-08-04 03:00:13.639 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //a[contains(text(),'Profiles-ListSet Association')], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:13.725  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.1A09F6F2E0597E45FF798CAE26448653.e.15067"}]} 
2025-08-04 03:00:13.774  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fbutton[.='Yes']"\n } 
2025-08-04 03:00:13.801  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#listSetDetailForm\\:listSet_details\\:tabViewListSet\\:listset_tab_profiles\\:profilePickList",\n   "using": "css selector"\n } 
2025-08-04 03:00:13.863 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listSetDetailForm\:listSet_details\:tabViewListSet\:listset_tab_profiles\:profilePickList, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:13.864 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //button[.='Yes'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml# 
2025-08-04 03:00:13.874  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.1A09F6F2E0597E45FF798CAE26448653.e.15070"}} 
2025-08-04 03:00:13.877  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.3C655BBBF00F5632C632BE05BDFC2A82.e.35662"}]} 
2025-08-04 03:00:13.924  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:13.947  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:14.030 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:14.056  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.0852C5D17286DC92DA5EB3AF34009409.e.11614"}} 
2025-08-04 03:00:14.099  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:14.149 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:14.166  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.1A09F6F2E0597E45FF798CAE26448653.e.15068"}} 
2025-08-04 03:00:14.178  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#zoneForm\\:homepage_business\\:zoneName",\n   "using": "css selector"\n } 
2025-08-04 03:00:14.256  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#listSetDetailForm\\:listSet_details\\:tabViewListSet\\:listset_tab_profiles\\:profilePickList_source_filter",\n   "using": "css selector"\n } 
2025-08-04 03:00:14.282 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #zoneForm\:homepage_business\:zoneName, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:14.311  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.0852C5D17286DC92DA5EB3AF34009409.e.11694"}]} 
2025-08-04 03:00:14.341 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listSetDetailForm\:listSet_details\:tabViewListSet\:listset_tab_profiles\:profilePickList_source_filter, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:14.363  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.1A09F6F2E0597E45FF798CAE26448653.e.14980"}]} 
2025-08-04 03:00:14.497  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:14.568 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:14.577  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.0852C5D17286DC92DA5EB3AF34009409.e.11614"}} 
2025-08-04 03:00:14.587  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:14.613  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#zoneForm\\:homepage_business\\:zoneName",\n   "using": "css selector"\n } 
2025-08-04 03:00:14.630  WARN 1 - [ttp-epoll-4] healenium                        : [Save Elements] Error during save elements: [f.F55F3D0EB16719BC6F8FA239EE46C51F.d.3C655BBBF00F5632C632BE05BDFC2A82.e.35682]. Message: unknown error: unhandled inspector error: {"code":-32000,"message":"Node with given id does not belong to the document"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [41886cb1ceb2386f552b3eac19d01320, executeScript {script=var items = [];var result = {items: null, url: null};var a = arguments[0];while (a != document) {  var child = a;  var i=0; while(child=child.previousElementSibling) i++;  var node = {tag:null,id:null,index:null,classes:[],other:{},innerText:""};  if (a.tagName !== undefined) {    node.tag = a.tagName.toLowerCase();  }  node.id = a.id;  node.index = i;  node.innerText = a.innerText || "";  if (a.hasAttribute("class")) {	  node.classes = a.attributes.class.value.split(' ');  }  for (index = 0; index < a.attributes.length; ++index) {      var attrName = a.attributes[index].name;      if (["id","class"].indexOf(attrName) == -1){		    node.other[attrName] = a.attributes[index].value;      }  };  items.unshift(node);  a = a.parentNode;}result.items = items;result.url = window.location.href;return JSON.stringify(result);, args=[{element-6066-11e4-a52e-4f735466cecf=f.F55F3D0EB16719BC6F8FA239EE46C51F.d.3C655BBBF00F5632C632BE05BDFC2A82.e.35682}]}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:40951}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.zU...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: a5ffb8a0a9f9, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 41886cb1ceb2386f552b3eac19d01320. Exception: org.openqa.selenium.WebDriverException: unknown error: unhandled inspector error: {"code":-32000,"message":"Node with given id does not belong to the document"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [41886cb1ceb2386f552b3eac19d01320, executeScript {script=var items = [];var result = {items: null, url: null};var a = arguments[0];while (a != document) {  var child = a;  var i=0; while(child=child.previousElementSibling) i++;  var node = {tag:null,id:null,index:null,classes:[],other:{},innerText:""};  if (a.tagName !== undefined) {    node.tag = a.tagName.toLowerCase();  }  node.id = a.id;  node.index = i;  node.innerText = a.innerText || "";  if (a.hasAttribute("class")) {	  node.classes = a.attributes.class.value.split(' ');  }  for (index = 0; index < a.attributes.length; ++index) {      var attrName = a.attributes[index].name;      if (["id","class"].indexOf(attrName) == -1){		    node.other[attrName] = a.attributes[index].value;      }  };  items.unshift(node);  a = a.parentNode;}result.items = items;result.url = window.location.href;return JSON.stringify(result);, args=[{element-6066-11e4-a52e-4f735466cecf=f.F55F3D0EB16719BC6F8FA239EE46C51F.d.3C655BBBF00F5632C632BE05BDFC2A82.e.35682}]}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:40951}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.zU...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: a5ffb8a0a9f9, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 41886cb1ceb2386f552b3eac19d01320 
2025-08-04 03:00:14.631  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.3C655BBBF00F5632C632BE05BDFC2A82.e.35682"}} 
2025-08-04 03:00:14.640 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:14.661  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.1A09F6F2E0597E45FF798CAE26448653.e.15068"}} 
2025-08-04 03:00:14.687 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #zoneForm\:homepage_business\:zoneName, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:14.702  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.0852C5D17286DC92DA5EB3AF34009409.e.11694"}]} 
2025-08-04 03:00:14.753  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#listSetDetailForm\\:listSet_details\\:tabViewListSet\\:listset_tab_profiles\\:profilePickList_source_filter",\n   "using": "css selector"\n } 
2025-08-04 03:00:14.825 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listSetDetailForm\:listSet_details\:tabViewListSet\:listset_tab_profiles\:profilePickList_source_filter, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:14.835  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.1A09F6F2E0597E45FF798CAE26448653.e.14980"}]} 
2025-08-04 03:00:14.840  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#pageLoginForm\\:login_business\\:UserName",\n   "using": "css selector"\n } 
2025-08-04 03:00:14.889 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #pageLoginForm\:login_business\:UserName, Command: findElements, URL: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-04 03:00:14.911  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.CE39C35365BFC26034DFDED392B2CCCC.e.35689"}]} 
2025-08-04 03:00:14.980  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:15.082  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:15.100 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:15.110  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.0852C5D17286DC92DA5EB3AF34009409.e.11614"}} 
2025-08-04 03:00:15.162  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#zoneForm\\:homepage_business\\:zoneDispName",\n   "using": "css selector"\n } 
2025-08-04 03:00:15.233 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #zoneForm\:homepage_business\:zoneDispName, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:15.257  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.0852C5D17286DC92DA5EB3AF34009409.e.11696"}]} 
2025-08-04 03:00:15.368  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:15.432 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:15.447  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.0852C5D17286DC92DA5EB3AF34009409.e.11614"}} 
2025-08-04 03:00:15.505  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#zoneForm\\:homepage_business\\:zoneDispName",\n   "using": "css selector"\n } 
2025-08-04 03:00:15.533  WARN 1 - [ttp-epoll-4] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-04 03:00:15.534  WARN 1 - [ttp-epoll-4] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [41886cb1ceb2386f552b3eac19d01320, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:40951}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.zU...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: a5ffb8a0a9f9, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 41886cb1ceb2386f552b3eac19d01320 
2025-08-04 03:00:15.560 DEBUG 1 - [ttp-epoll-4] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-04 03:00:15.570 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #zoneForm\:homepage_business\:zoneDispName, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:15.583  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.0852C5D17286DC92DA5EB3AF34009409.e.11696"}]} 
2025-08-04 03:00:15.608  WARN 1 - [ttp-epoll-4] healenium                        : Trying to heal... 
2025-08-04 03:00:15.611  WARN 1 - [ttp-epoll-4] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-04 03:00:15.617  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fspan[contains(text(),'Back to login')]"\n } 
2025-08-04 03:00:15.656  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[]} 
2025-08-04 03:00:15.691  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:15.745  WARN 1 - [ttp-epoll-4] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-04 03:00:15.746  WARN 1 - [ttp-epoll-4] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [41886cb1ceb2386f552b3eac19d01320, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:40951}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.zU...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: a5ffb8a0a9f9, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 41886cb1ceb2386f552b3eac19d01320 
2025-08-04 03:00:15.786 DEBUG 1 - [ttp-epoll-4] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-04 03:00:15.816  WARN 1 - [ttp-epoll-4] healenium                        : Trying to heal... 
2025-08-04 03:00:15.817  WARN 1 - [ttp-epoll-4] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-04 03:00:15.823  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "css selector",\n   "value": ".fa-street-view"\n } 
2025-08-04 03:00:15.854  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[]} 
2025-08-04 03:00:15.859  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:15.893  WARN 1 - [ttp-epoll-4] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-04 03:00:15.894  WARN 1 - [ttp-epoll-4] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [41886cb1ceb2386f552b3eac19d01320, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:40951}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.zU...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: a5ffb8a0a9f9, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 41886cb1ceb2386f552b3eac19d01320 
2025-08-04 03:00:15.918 DEBUG 1 - [ttp-epoll-4] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-04 03:00:15.937  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:15.967  WARN 1 - [ttp-epoll-4] healenium                        : Trying to heal... 
2025-08-04 03:00:15.969  WARN 1 - [ttp-epoll-4] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-04 03:00:15.974  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#pageLoginForm\\:login_business\\:UserName",\n   "using": "css selector"\n } 
2025-08-04 03:00:16.052 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #pageLoginForm\:login_business\:UserName, Command: findElements, URL: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-04 03:00:16.066  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.809598D3634D0761F634D7409CBE3CDF.e.35713"}]} 
2025-08-04 03:00:16.067 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:16.084  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.0852C5D17286DC92DA5EB3AF34009409.e.11614"}} 
2025-08-04 03:00:16.129  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#zoneForm\\:homepage_business\\:btnSearch",\n   "using": "css selector"\n } 
2025-08-04 03:00:16.178  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:16.200 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #zoneForm\:homepage_business\:btnSearch, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:16.217  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.0852C5D17286DC92DA5EB3AF34009409.e.11701"}]} 
2025-08-04 03:00:16.252  WARN 1 - [ttp-epoll-4] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-04 03:00:16.253  WARN 1 - [ttp-epoll-4] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [41886cb1ceb2386f552b3eac19d01320, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:40951}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.zU...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: a5ffb8a0a9f9, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 41886cb1ceb2386f552b3eac19d01320 
2025-08-04 03:00:16.299 DEBUG 1 - [ttp-epoll-4] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-04 03:00:16.328  WARN 1 - [ttp-epoll-4] healenium                        : Trying to heal... 
2025-08-04 03:00:16.333  WARN 1 - [ttp-epoll-4] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-04 03:00:16.338  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#pageLoginForm\\:login_business\\:Password",\n   "using": "css selector"\n } 
2025-08-04 03:00:16.377  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:16.390 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #pageLoginForm\:login_business\:Password, Command: findElements, URL: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-04 03:00:16.416  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.809598D3634D0761F634D7409CBE3CDF.e.35714"}]} 
2025-08-04 03:00:16.569  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:16.579 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:16.589  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.0852C5D17286DC92DA5EB3AF34009409.e.11614"}} 
2025-08-04 03:00:16.626  WARN 1 - [ttp-epoll-4] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-04 03:00:16.627  WARN 1 - [ttp-epoll-4] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [41886cb1ceb2386f552b3eac19d01320, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:40951}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.zU...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: a5ffb8a0a9f9, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 41886cb1ceb2386f552b3eac19d01320 
2025-08-04 03:00:16.643  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fa[.=\"Zone392489986\"]"\n } 
2025-08-04 03:00:16.643 DEBUG 1 - [ttp-epoll-4] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-04 03:00:16.703  WARN 1 - [ttp-epoll-4] healenium                        : Trying to heal... 
2025-08-04 03:00:16.707  WARN 1 - [ttp-epoll-4] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-04 03:00:16.712 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //a[.="Zone392489986"], Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:16.713  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#pageLoginForm\\:login_business\\:UserName",\n   "using": "css selector"\n } 
2025-08-04 03:00:16.732  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.0852C5D17286DC92DA5EB3AF34009409.e.11708"}]} 
2025-08-04 03:00:16.738  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_admin_HomeAuthorizationProfileManager",\n   "using": "css selector"\n } 
2025-08-04 03:00:16.783 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #pageLoginForm\:login_business\:UserName, Command: findElements, URL: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-04 03:00:16.795  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.809598D3634D0761F634D7409CBE3CDF.e.35713"}]} 
2025-08-04 03:00:16.809 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_admin_HomeAuthorizationProfileManager, Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:16.821  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.0852C5D17286DC92DA5EB3AF34009409.e.11709"}} 
2025-08-04 03:00:16.883  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:16.934 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:16.946  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.0852C5D17286DC92DA5EB3AF34009409.e.11614"}} 
2025-08-04 03:00:16.954  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:17.005  WARN 1 - [ttp-epoll-4] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-04 03:00:17.006  WARN 1 - [ttp-epoll-4] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [41886cb1ceb2386f552b3eac19d01320, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:40951}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.zU...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: a5ffb8a0a9f9, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 41886cb1ceb2386f552b3eac19d01320 
2025-08-04 03:00:17.015  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_admin_HomeAuthorizationProfileManager",\n   "using": "css selector"\n } 
2025-08-04 03:00:17.029 DEBUG 1 - [ttp-epoll-4] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-04 03:00:17.072  WARN 1 - [ttp-epoll-4] healenium                        : Trying to heal... 
2025-08-04 03:00:17.074  WARN 1 - [ttp-epoll-4] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-04 03:00:17.079  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#pageLoginForm\\:login_business\\:UserName",\n   "using": "css selector"\n } 
2025-08-04 03:00:17.086 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_admin_HomeAuthorizationProfileManager, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/ZoneManager/Homepage.xhtml?module_name=ZoneManager&app_name=ADMIN 
2025-08-04 03:00:17.098  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.0852C5D17286DC92DA5EB3AF34009409.e.11709"}]} 
2025-08-04 03:00:17.127 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #pageLoginForm\:login_business\:UserName, Command: findElements, URL: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-04 03:00:17.137  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.809598D3634D0761F634D7409CBE3CDF.e.35713"}]} 
2025-08-04 03:00:17.286  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:17.361  WARN 1 - [ttp-epoll-4] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-04 03:00:17.362  WARN 1 - [ttp-epoll-4] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [41886cb1ceb2386f552b3eac19d01320, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:40951}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.zU...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: a5ffb8a0a9f9, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 41886cb1ceb2386f552b3eac19d01320 
2025-08-04 03:00:17.388 DEBUG 1 - [ttp-epoll-4] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-04 03:00:17.424  WARN 1 - [ttp-epoll-4] healenium                        : Trying to heal... 
2025-08-04 03:00:17.426  WARN 1 - [ttp-epoll-4] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-04 03:00:17.436  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#pageLoginForm\\:login_business\\:Password",\n   "using": "css selector"\n } 
2025-08-04 03:00:17.497 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #pageLoginForm\:login_business\:Password, Command: findElements, URL: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-04 03:00:17.508  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.809598D3634D0761F634D7409CBE3CDF.e.35714"}]} 
2025-08-04 03:00:17.597  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:17.637  WARN 1 - [ttp-epoll-4] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-04 03:00:17.638  WARN 1 - [ttp-epoll-4] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [41886cb1ceb2386f552b3eac19d01320, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:40951}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.zU...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: a5ffb8a0a9f9, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 41886cb1ceb2386f552b3eac19d01320 
2025-08-04 03:00:17.654 DEBUG 1 - [ttp-epoll-4] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-04 03:00:17.687  WARN 1 - [ttp-epoll-4] healenium                        : Trying to heal... 
2025-08-04 03:00:17.689  WARN 1 - [ttp-epoll-4] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-04 03:00:17.695  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#pageLoginForm\\:login_business\\:Password",\n   "using": "css selector"\n } 
2025-08-04 03:00:17.780 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #pageLoginForm\:login_business\:Password, Command: findElements, URL: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-04 03:00:17.791  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.809598D3634D0761F634D7409CBE3CDF.e.35714"}]} 
2025-08-04 03:00:17.934  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:18.035  WARN 1 - [ttp-epoll-4] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-04 03:00:18.036  WARN 1 - [ttp-epoll-4] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [41886cb1ceb2386f552b3eac19d01320, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:40951}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.zU...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: a5ffb8a0a9f9, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 41886cb1ceb2386f552b3eac19d01320 
2025-08-04 03:00:18.069 DEBUG 1 - [ttp-epoll-4] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-04 03:00:18.090  WARN 1 - [ttp-epoll-4] healenium                        : Trying to heal... 
2025-08-04 03:00:18.091  WARN 1 - [ttp-epoll-4] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-04 03:00:18.097  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#pageLoginForm\\:login_business\\:goButton",\n   "using": "css selector"\n } 
2025-08-04 03:00:18.151 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #pageLoginForm\:login_business\:goButton, Command: findElements, URL: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-04 03:00:18.167  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.809598D3634D0761F634D7409CBE3CDF.e.35727"}]} 
2025-08-04 03:00:18.255  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:18.324  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:18.371 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:18.371 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:18.382  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.1A09F6F2E0597E45FF798CAE26448653.e.15068"}} 
2025-08-04 03:00:18.390  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11800"}} 
2025-08-04 03:00:18.444  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fform[@id='topbar-right']\u002ful\u002flabel"\n } 
2025-08-04 03:00:18.445  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@title='Add All']"\n } 
2025-08-04 03:00:18.521 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //form[@id='topbar-right']/ul/label, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:18.530 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@title='Add All'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:18.549  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11741"}]} 
2025-08-04 03:00:18.553  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.1A09F6F2E0597E45FF798CAE26448653.e.15031"}]} 
2025-08-04 03:00:18.618  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:18.703 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:18.744  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11800"}} 
2025-08-04 03:00:18.829  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#authorizationProfileForm\\:homepage_business\\:btnReset",\n   "using": "css selector"\n } 
2025-08-04 03:00:18.945 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #authorizationProfileForm\:homepage_business\:btnReset, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:18.993  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11767"}]} 
2025-08-04 03:00:20.224  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='ui-growl-title']"\n } 
2025-08-04 03:00:21.221 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='ui-growl-title'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml 
2025-08-04 03:00:21.244  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.E1767AE71006A3E2FF97350405B39358.e.35851"}]} 
2025-08-04 03:00:21.258  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:21.350 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml 
2025-08-04 03:00:21.367  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.E1767AE71006A3E2FF97350405B39358.e.35852"}} 
2025-08-04 03:00:21.426  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='ui-growl-title']"\n } 
2025-08-04 03:00:21.495 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='ui-growl-title'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml 
2025-08-04 03:00:21.520  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.E1767AE71006A3E2FF97350405B39358.e.35851"}]} 
2025-08-04 03:00:21.567  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-04 03:00:21.607  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:21.614 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_HomeListManager, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml 
2025-08-04 03:00:21.627  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.E1767AE71006A3E2FF97350405B39358.e.35853"}} 
2025-08-04 03:00:21.661 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:21.675  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.1A09F6F2E0597E45FF798CAE26448653.e.15068"}} 
2025-08-04 03:00:21.728  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:21.731  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#listSetDetailForm\\:listSet_details\\:btnSave",\n   "using": "css selector"\n } 
2025-08-04 03:00:21.791 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listSetDetailForm\:listSet_details\:btnSave, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:21.799 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml 
2025-08-04 03:00:21.809  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.E1767AE71006A3E2FF97350405B39358.e.35852"}} 
2025-08-04 03:00:21.816  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.1A09F6F2E0597E45FF798CAE26448653.e.15047"}]} 
2025-08-04 03:00:21.848  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-04 03:00:21.912 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_HomeListManager, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml 
2025-08-04 03:00:21.926  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.E1767AE71006A3E2FF97350405B39358.e.35853"}]} 
2025-08-04 03:00:22.127  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='ui-growl-message']"\n } 
2025-08-04 03:00:22.328  WARN 1 - [ttp-epoll-3] healenium                        : Failed to find an element using locator By.xpath: //*[@class='ui-growl-message'] 
2025-08-04 03:00:22.329  WARN 1 - [ttp-epoll-3] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='ui-growl-message']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [20a57fedf1afb9da426f2a59951fa9c5, findElement {value=//*[@class='ui-growl-message'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39195}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.zn...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 85fe87fd88fa, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 20a57fedf1afb9da426f2a59951fa9c5 
2025-08-04 03:00:22.370 DEBUG 1 - [ttp-epoll-3] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='ui-growl-message'], Command: findElement, Url: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:22.506  WARN 1 - [ttp-epoll-3] healenium                        : Trying to heal... 
2025-08-04 03:00:23.843  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:23.995 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:24.012  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35949"}} 
2025-08-04 03:00:24.230  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:24.310 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:24.331  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11800"}} 
2025-08-04 03:00:24.381  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#authorizationProfileForm\\:homepage_business\\:name",\n   "using": "css selector"\n } 
2025-08-04 03:00:24.516 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #authorizationProfileForm\:homepage_business\:name, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:24.550  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11801"}]} 
2025-08-04 03:00:24.626  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:24.638  WARN 1 - [ttp-epoll-3] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [20a57fedf1afb9da426f2a59951fa9c5, findElements {value=div#topbar-right:selectedLanguage.select-width-and-height.p-m-0.field.ui-selectonemenu.ui-state-default.ui-widget.ui-corner-all.comboBox > div.ui-helper-hidden-accessible, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39195}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.zn...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 85fe87fd88fa, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 20a57fedf1afb9da426f2a59951fa9c5\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-04 03:00:24.656  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='ui-growl-message']"\n } 
2025-08-04 03:00:24.689 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:24.703  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11800"}} 
2025-08-04 03:00:24.742 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='ui-growl-message'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:24.761  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.1A09F6F2E0597E45FF798CAE26448653.e.15469"}} 
2025-08-04 03:00:24.770  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fform[@id='topbar-right']\u002ful\u002flabel"\n } 
2025-08-04 03:00:24.790  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#authorizationProfileForm\\:homepage_business\\:name",\n   "using": "css selector"\n } 
2025-08-04 03:00:24.824  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:24.843 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //form[@id='topbar-right']/ul/label, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:24.851 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #authorizationProfileForm\:homepage_business\:name, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:24.856  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35887"}]} 
2025-08-04 03:00:24.866  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11801"}]} 
2025-08-04 03:00:24.875 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:24.886  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.1A09F6F2E0597E45FF798CAE26448653.e.15480"}} 
2025-08-04 03:00:24.891  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fa[contains(text(),'List Manager')]"\n } 
2025-08-04 03:00:24.950  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='ui-growl-title']"\n } 
2025-08-04 03:00:24.995 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //a[contains(text(),'List Manager')], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:25.010  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35950"}} 
2025-08-04 03:00:25.027 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='ui-growl-title'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:25.040  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.1A09F6F2E0597E45FF798CAE26448653.e.15481"}]} 
2025-08-04 03:00:25.117  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:25.173 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:25.185  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35949"}} 
2025-08-04 03:00:25.234  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fa[contains(text(),'List Manager')]"\n } 
2025-08-04 03:00:25.252  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:25.327 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //a[contains(text(),'List Manager')], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:25.347  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35950"}]} 
2025-08-04 03:00:25.350 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:25.370  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11800"}} 
2025-08-04 03:00:25.443  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#authorizationProfileForm\\:homepage_business\\:btnSearchAPM",\n   "using": "css selector"\n } 
2025-08-04 03:00:25.561 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #authorizationProfileForm\:homepage_business\:btnSearchAPM, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:25.579  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11768"}]} 
2025-08-04 03:00:25.634  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:Tab_list_manager\\:Sub_black_list_viewer\\:zoneCbx",\n   "using": "css selector"\n } 
2025-08-04 03:00:25.728  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:25.817 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:zoneCbx, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:25.850  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35951"}} 
2025-08-04 03:00:26.033 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:26.048  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11800"}} 
2025-08-04 03:00:26.100  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:26.141 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:26.173  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11800"}} 
2025-08-04 03:00:26.181  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:26.259  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002ftbody[@id='authorizationProfileForm:homepage_business:_tblResults_data']\u002ftr\u002ftd[3]\u002fa\u002fspan[@title=\"Test-Profile-324443088\"]"\n } 
2025-08-04 03:00:26.283 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:26.294  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35949"}} 
2025-08-04 03:00:26.316  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[]} 
2025-08-04 03:00:26.324  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:26.364 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:26.374  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11800"}} 
2025-08-04 03:00:26.380  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fspan[contains(text(),'Reset')]"\n } 
2025-08-04 03:00:26.415  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#authorizationProfileForm\\:homepage_business\\:_tblResults\\:_btnNewUser",\n   "using": "css selector"\n } 
2025-08-04 03:00:26.472 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //span[contains(text(),'Reset')], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:26.490  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35952"}]} 
2025-08-04 03:00:26.546 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #authorizationProfileForm\:homepage_business\:_tblResults\:_btnNewUser, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:26.562  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11825"}]} 
2025-08-04 03:00:26.633  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:26.948  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeScanManager",\n   "using": "css selector"\n } 
2025-08-04 03:00:27.079 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_HomeScanManager, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:27.094  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.82E81AC932C5A8ED745257C6BFA44AB3.e.15584"}} 
2025-08-04 03:00:27.182  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:27.258 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:27.274  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.82E81AC932C5A8ED745257C6BFA44AB3.e.15585"}} 
2025-08-04 03:00:27.323  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeScanManager",\n   "using": "css selector"\n } 
2025-08-04 03:00:27.372 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_HomeScanManager, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:27.384  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.82E81AC932C5A8ED745257C6BFA44AB3.e.15584"}]} 
2025-08-04 03:00:27.688 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:27.711  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11905"}} 
2025-08-04 03:00:27.781  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#authorizationProfileDetailForm\\:detail_business\\:Name",\n   "using": "css selector"\n } 
2025-08-04 03:00:27.872 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #authorizationProfileDetailForm\:detail_business\:Name, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:27.887  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11835"}]} 
2025-08-04 03:00:27.937  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:28.045  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:28.105 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:28.117  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11905"}} 
2025-08-04 03:00:28.189  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#authorizationProfileDetailForm\\:detail_business\\:Name",\n   "using": "css selector"\n } 
2025-08-04 03:00:28.258 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:28.270 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #authorizationProfileDetailForm\:detail_business\:Name, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:28.292  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35949"}} 
2025-08-04 03:00:28.296  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11835"}]} 
2025-08-04 03:00:28.359  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:28.412 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:28.425  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35949"}} 
2025-08-04 03:00:28.495  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:Tab_list_manager\\:Sub_black_list_viewer\\:zoneCbx_label",\n   "using": "css selector"\n } 
2025-08-04 03:00:28.579  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:28.605 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:zoneCbx_label, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:28.632  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35963"}]} 
2025-08-04 03:00:28.682 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:28.693  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11905"}} 
2025-08-04 03:00:28.758  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#authorizationProfileDetailForm\\:detail_business\\:remark",\n   "using": "css selector"\n } 
2025-08-04 03:00:28.831 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #authorizationProfileDetailForm\:detail_business\:remark, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:28.846  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11836"}]} 
2025-08-04 03:00:28.913  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:28.924  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:28.973 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:28.987  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11905"}} 
2025-08-04 03:00:29.040  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#authorizationProfileDetailForm\\:detail_business\\:remark",\n   "using": "css selector"\n } 
2025-08-04 03:00:29.080 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:29.097  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15700"}} 
2025-08-04 03:00:29.110 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #authorizationProfileDetailForm\:detail_business\:remark, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:29.124  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11836"}]} 
2025-08-04 03:00:29.161  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fform[@id='topbar-right']\u002ful\u002flabel"\n } 
2025-08-04 03:00:29.208 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //form[@id='topbar-right']/ul/label, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:29.222  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15637"}]} 
2025-08-04 03:00:29.262  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fa[contains(text(),'File Scan')]"\n } 
2025-08-04 03:00:29.329 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //a[contains(text(),'File Scan')], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:29.345  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15701"}} 
2025-08-04 03:00:29.429  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:29.489 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:29.505  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15700"}} 
2025-08-04 03:00:29.529  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:29.578 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:29.587  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11905"}} 
2025-08-04 03:00:29.592  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fa[contains(text(),'File Scan')]"\n } 
2025-08-04 03:00:29.625  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#authorizationProfileDetailForm\\:detail_business\\:enabledFlag_input",\n   "using": "css selector"\n } 
2025-08-04 03:00:29.652 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //a[contains(text(),'File Scan')], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:29.667  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15701"}]} 
2025-08-04 03:00:29.704 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #authorizationProfileDetailForm\:detail_business\:enabledFlag_input, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:29.720  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11837"}]} 
2025-08-04 03:00:29.762  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:29.822 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:29.835  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11905"}} 
2025-08-04 03:00:29.873  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#authorizationProfileDetailForm\\:detail_business\\:enabledFlag",\n   "using": "css selector"\n } 
2025-08-04 03:00:29.873  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role='option']"\n } 
2025-08-04 03:00:29.963 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #authorizationProfileDetailForm\:detail_business\:enabledFlag, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:30.002  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11908"}]} 
2025-08-04 03:00:30.030 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role='option'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:30.227  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:30.296 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:30.316  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11905"}} 
2025-08-04 03:00:30.368  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#authorizationProfileDetailForm\\:detail_business\\:zoneWritableFlag_input",\n   "using": "css selector"\n } 
2025-08-04 03:00:30.436 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #authorizationProfileDetailForm\:detail_business\:zoneWritableFlag_input, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:30.464  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11839"}]} 
2025-08-04 03:00:30.501  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:30.554 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:30.565  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11905"}} 
2025-08-04 03:00:30.602  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#authorizationProfileDetailForm\\:detail_business\\:zoneCbx",\n   "using": "css selector"\n } 
2025-08-04 03:00:30.658 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #authorizationProfileDetailForm\:detail_business\:zoneCbx, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:30.677  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11909"}]} 
2025-08-04 03:00:31.697  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35969"},{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35970"}]} 
2025-08-04 03:00:31.704  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Common Zone 02']"\n } 
2025-08-04 03:00:31.704  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:Tab_FileScan\\:scanFile_label",\n   "using": "css selector"\n } 
2025-08-04 03:00:31.776 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:Tab_FileScan\:scanFile_label, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:31.796 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='Common Zone 02'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:31.804  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15725"}} 
2025-08-04 03:00:31.817  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35970"}]} 
2025-08-04 03:00:31.823  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:31.873  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role='option']"\n } 
2025-08-04 03:00:31.874 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:31.884  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35949"}} 
2025-08-04 03:00:31.927  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Common Zone 02']"\n } 
2025-08-04 03:00:31.982 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='Common Zone 02'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:31.992  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35970"}]} 
2025-08-04 03:00:32.030  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Common Zone 02']"\n } 
2025-08-04 03:00:32.090 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='Common Zone 02'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:32.102  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35970"}} 
2025-08-04 03:00:32.339  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:32.385 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:32.395  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35949"}} 
2025-08-04 03:00:32.459  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:Tab_list_manager\\:Sub_black_list_viewer\\:privateCbx",\n   "using": "css selector"\n } 
2025-08-04 03:00:32.534 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:privateCbx, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:32.548  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35971"}]} 
2025-08-04 03:00:33.707  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role='option']"\n } 
2025-08-04 03:00:33.849 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role='option'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:33.860  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35969"},{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35970"},{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35972"},{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35973"},{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35974"}]} 
2025-08-04 03:00:33.865  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='No']"\n } 
2025-08-04 03:00:33.913 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='No'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:33.930  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35974"}]} 
2025-08-04 03:00:33.934  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:34.000 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:34.019  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35949"}} 
2025-08-04 03:00:34.065  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='No']"\n } 
2025-08-04 03:00:34.105 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='No'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:34.114  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35974"}]} 
2025-08-04 03:00:34.141  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='No']"\n } 
2025-08-04 03:00:34.208 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='No'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:34.227  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35974"}} 
2025-08-04 03:00:34.437  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:34.555 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:34.573  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35949"}} 
2025-08-04 03:00:34.607  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:Tab_list_manager\\:Sub_black_list_viewer\\:btnBLVSearch",\n   "using": "css selector"\n } 
2025-08-04 03:00:34.651 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:btnBLVSearch, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:34.663  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35975"}]} 
2025-08-04 03:00:34.706  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:Tab_list_manager\\:Sub_black_list_viewer\\:_tblResults\\:_btnNew",\n   "using": "css selector"\n } 
2025-08-04 03:00:34.798 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:34.823  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35976"}} 
2025-08-04 03:00:34.977  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:Tab_list_manager\\:Sub_black_list_viewer\\:_tblResults\\:_btnNew",\n   "using": "css selector"\n } 
2025-08-04 03:00:35.055 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:35.068  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35994"}} 
2025-08-04 03:00:35.107  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:35.203 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:35.225  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35949"}} 
2025-08-04 03:00:35.265  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:35.313 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:35.323  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35949"}} 
2025-08-04 03:00:35.369  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fa[contains(@id,':0:linkId')]"\n } 
2025-08-04 03:00:35.431 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //a[contains(@id,':0:linkId')], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-04 03:00:35.453  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.B53B6305ED5B723CB60955DC8ABF1033.e.35995"}]} 
2025-08-04 03:00:35.986 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role='option'], Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:36.273  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11910"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11911"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11912"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11913"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11914"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11915"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11916"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11917"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11918"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11919"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11920"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11921"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11922"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11923"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11924"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11925"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11926"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11927"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11928"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11929"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11930"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11931"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11932"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11933"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11934"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11935"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11936"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11937"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11938"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11939"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11940"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11941"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11942"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11943"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11944"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11945"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11946"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11947"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11948"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11949"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11950"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11951"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11952"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11953"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11954"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11955"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11956"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11957"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11958"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11959"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11960"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11961"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11962"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11963"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11964"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11965"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11966"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11967"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11968"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11969"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11970"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11971"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11972"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11973"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11974"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11975"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11976"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11977"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11978"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11979"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11980"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11981"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11982"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11983"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11984"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11985"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11986"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11987"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11988"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11989"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11990"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11991"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11992"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11993"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11994"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11995"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11996"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11997"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11998"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11999"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12000"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12001"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12002"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12003"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12004"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12005"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12006"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12007"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12008"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12009"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12010"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12011"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12012"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12013"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12014"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12015"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12016"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12017"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12018"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12019"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12020"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12021"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12022"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12023"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12024"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12025"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12026"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12027"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12028"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12029"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12030"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12031"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12032"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12033"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12034"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12035"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12036"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12037"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12038"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12039"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12040"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12041"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12042"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12043"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12044"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12045"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12046"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12047"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12048"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12049"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12050"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12051"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12052"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12053"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12054"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12055"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12056"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12057"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12058"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12059"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12060"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12061"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12062"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12063"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12064"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12065"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12066"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12067"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12068"},{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12069"}]} 
2025-08-04 03:00:36.282  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Zone (392489986)']"\n } 
2025-08-04 03:00:36.282  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:36.341 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='Zone (392489986)'], Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:36.343 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:36.355  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15700"}} 
2025-08-04 03:00:36.360  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11984"}]} 
2025-08-04 03:00:36.365  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:36.399  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:Tab_FileScan\\:btnResetFileScan",\n   "using": "css selector"\n } 
2025-08-04 03:00:36.438 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:36.455  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11905"}} 
2025-08-04 03:00:36.546 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:Tab_FileScan\:btnResetFileScan, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:36.578  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15726"}]} 
2025-08-04 03:00:36.604  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Zone (392489986)']"\n } 
2025-08-04 03:00:36.684  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:36.694 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='Zone (392489986)'], Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:36.713  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11984"}]} 
2025-08-04 03:00:36.930  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[.='Black List Edition']"\n } 
2025-08-04 03:00:37.036 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[.='Black List Edition'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:37.052  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.2B33DFC9AA3925731E1F14E7E09C939D.e.36099"}} 
2025-08-04 03:00:37.095  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:37.124 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:37.138  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15700"}} 
2025-08-04 03:00:37.153 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:37.171  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.2B33DFC9AA3925731E1F14E7E09C939D.e.36100"}} 
2025-08-04 03:00:37.207  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Zone (392489986)']"\n } 
2025-08-04 03:00:37.233  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[.='Black List Edition']"\n } 
2025-08-04 03:00:37.267  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:37.297 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='Zone (392489986)'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:37.323  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11984"}} 
2025-08-04 03:00:37.359 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[.='Black List Edition'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:37.367 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:37.374  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.2B33DFC9AA3925731E1F14E7E09C939D.e.36099"},{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.2B33DFC9AA3925731E1F14E7E09C939D.e.36101"}]} 
2025-08-04 03:00:37.379  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:37.379  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15700"}} 
2025-08-04 03:00:37.452 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:37.473  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.2B33DFC9AA3925731E1F14E7E09C939D.e.36100"}} 
2025-08-04 03:00:37.521  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fdiv[contains(@id,'listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:Sub_black_list_edition:Sub_black_list_detail_viewer:_tblResults:0:')]"\n } 
2025-08-04 03:00:37.592 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //div[contains(@id,'listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:Sub_black_list_edition:Sub_black_list_detail_viewer:_tblResults:0:')], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:37.610  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.2B33DFC9AA3925731E1F14E7E09C939D.e.36102"}]} 
2025-08-04 03:00:37.615  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:Tab_FileScan\\:scanFile_input",\n   "using": "css selector"\n } 
2025-08-04 03:00:37.711 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:Tab_FileScan\:scanFile_input, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:37.751  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15727"}]} 
2025-08-04 03:00:37.757  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:37.847  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:37.859 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:37.871  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11905"}} 
2025-08-04 03:00:37.902 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:37.923  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.2B33DFC9AA3925731E1F14E7E09C939D.e.36100"}} 
2025-08-04 03:00:37.928  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:37.972  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:Tab_list_manager\\:Sub_black_list_edition\\:Sub_black_list_detail_viewer\\:_tblResults\\:_btnExport",\n   "using": "css selector"\n } 
2025-08-04 03:00:38.013 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:38.035  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15700"}} 
2025-08-04 03:00:38.045  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fdiv[@id=\"authorizationProfileDetailForm:detail_business:applicationPickListId\"]\u002fdiv[1]\u002ful\u002fli[@data-item-label=\"SWS\"]"\n } 
2025-08-04 03:00:38.066 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_edition\:Sub_black_list_detail_viewer\:_tblResults\:_btnExport, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:38.084  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.2B33DFC9AA3925731E1F14E7E09C939D.e.36058"}]} 
2025-08-04 03:00:38.118  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:Tab_FileScan\\:scanFile_input",\n   "using": "css selector"\n } 
2025-08-04 03:00:38.122 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //div[@id="authorizationProfileDetailForm:detail_business:applicationPickListId"]/div[1]/ul/li[@data-item-label="SWS"], Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:38.122  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:38.141  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12070"}]} 
2025-08-04 03:00:38.198 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:38.211  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.2B33DFC9AA3925731E1F14E7E09C939D.e.36100"}} 
2025-08-04 03:00:38.249 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:Tab_FileScan\:scanFile_input, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:38.282  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002ftd[.='xml']\u002f\u002fdiv"\n } 
2025-08-04 03:00:38.296  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15727"}]} 
2025-08-04 03:00:38.500 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //td[.='xml']//div, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:38.519  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.2B33DFC9AA3925731E1F14E7E09C939D.e.36110"},{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.2B33DFC9AA3925731E1F14E7E09C939D.e.36111"},{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.2B33DFC9AA3925731E1F14E7E09C939D.e.36112"}]} 
2025-08-04 03:00:38.566  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:38.607  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:38.684 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:38.687 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:38.698  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11905"}} 
2025-08-04 03:00:38.711  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15700"}} 
2025-08-04 03:00:38.773  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:Tab_FileScan\\:formatCbx_label",\n   "using": "css selector"\n } 
2025-08-04 03:00:38.799  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:38.859 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:38.879  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.2B33DFC9AA3925731E1F14E7E09C939D.e.36100"}} 
2025-08-04 03:00:38.882 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:Tab_FileScan\:formatCbx_label, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:38.945  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15743"}]} 
2025-08-04 03:00:38.952  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:Tab_list_manager\\:sub_black_list_export_viewer\\:_btnExport",\n   "using": "css selector"\n } 
2025-08-04 03:00:38.952  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fdiv[@id=\"authorizationProfileDetailForm:detail_business:applicationPickListId\"]\u002fdiv[2]\u002fdiv\u002fbutton[@title=\"Add\"]"\n } 
2025-08-04 03:00:39.050 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //div[@id="authorizationProfileDetailForm:detail_business:applicationPickListId"]/div[2]/div/button[@title="Add"], Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:39.071  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11880"}]} 
2025-08-04 03:00:39.126 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:sub_black_list_export_viewer\:_btnExport, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:39.137  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.2B33DFC9AA3925731E1F14E7E09C939D.e.36113"}]} 
2025-08-04 03:00:39.822  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:39.867 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:39.879  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11905"}} 
2025-08-04 03:00:39.927  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@id='authorizationProfileDetailForm:detail_business:btnSaveCreate' or @id='authorizationProfileDetailForm:detail_business:btnSaveModify']"\n } 
2025-08-04 03:00:40.022 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@id='authorizationProfileDetailForm:detail_business:btnSaveCreate' or @id='authorizationProfileDetailForm:detail_business:btnSaveModify'], Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:40.042  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11885"}]} 
2025-08-04 03:00:40.164  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:40.265  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role='option']"\n } 
2025-08-04 03:00:41.237 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:41.253  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.11905"}} 
2025-08-04 03:00:41.457  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#authorizationProfileForm\\:homepage_business\\:btnReset",\n   "using": "css selector"\n } 
2025-08-04 03:00:41.562 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #authorizationProfileForm\:homepage_business\:btnReset, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:41.587  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12133"}]} 
2025-08-04 03:00:44.255  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:44.352 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:44.368  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.2B33DFC9AA3925731E1F14E7E09C939D.e.36100"}} 
2025-08-04 03:00:44.436  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:Tab_activity\\:btnASearch",\n   "using": "css selector"\n } 
2025-08-04 03:00:44.512 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_activity\:btnASearch, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:44.543  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.2B33DFC9AA3925731E1F14E7E09C939D.e.36126"}]} 
2025-08-04 03:00:44.594  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:44.685 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:44.703  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.2B33DFC9AA3925731E1F14E7E09C939D.e.36100"}} 
2025-08-04 03:00:44.795  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:44.864 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:44.875  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.2B33DFC9AA3925731E1F14E7E09C939D.e.36100"}} 
2025-08-04 03:00:44.951  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:45.261 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:45.272  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.2B33DFC9AA3925731E1F14E7E09C939D.e.36100"}} 
2025-08-04 03:00:45.528  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fa[contains(text(),'Activity')]"\n } 
2025-08-04 03:00:45.675 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //a[contains(text(),'Activity')], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:45.693  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.2B33DFC9AA3925731E1F14E7E09C939D.e.36139"}} 
2025-08-04 03:00:45.803  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:45.846 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:45.857  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.2B33DFC9AA3925731E1F14E7E09C939D.e.36100"}} 
2025-08-04 03:00:45.894  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fa[contains(text(),'Activity')]"\n } 
2025-08-04 03:00:45.976 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //a[contains(text(),'Activity')], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:45.992  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.2B33DFC9AA3925731E1F14E7E09C939D.e.36139"}]} 
2025-08-04 03:00:46.154  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:Tab_activity\\:searchDateFrom",\n   "using": "css selector"\n } 
2025-08-04 03:00:46.271 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_activity\:searchDateFrom, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:46.294  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.2B33DFC9AA3925731E1F14E7E09C939D.e.36141"}} 
2025-08-04 03:00:47.067 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role='option'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:47.853  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15751"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15752"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15753"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15754"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15755"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15756"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15757"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15758"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15759"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15760"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15761"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15762"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15763"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15764"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15765"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15766"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15767"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15768"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15769"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15770"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15771"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15772"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15773"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15774"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15775"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15776"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15777"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15778"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15779"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15780"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15781"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15782"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15783"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15784"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15785"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15786"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15787"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15788"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15789"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15790"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15791"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15792"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15793"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15794"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15795"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15796"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15797"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15798"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15799"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15800"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15801"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15802"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15803"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15804"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15805"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15806"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15807"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15808"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15809"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15810"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15811"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15812"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15813"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15814"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15815"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15816"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15817"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15818"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15819"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15820"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15821"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15822"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15823"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15824"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15825"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15826"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15827"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15828"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15829"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15830"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15831"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15832"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15833"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15834"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15835"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15836"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15837"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15838"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15839"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15840"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15841"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15842"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15843"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15844"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15845"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15846"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15847"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15848"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15849"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15850"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15851"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15852"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15853"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15854"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15855"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15856"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15857"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15858"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15859"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15860"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15861"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15862"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15863"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15864"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15865"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15866"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15867"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15868"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15869"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15870"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15871"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15872"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15873"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15874"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15875"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15876"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15877"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15878"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15879"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15880"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15881"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15882"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15883"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15884"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15885"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15886"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15887"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15888"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15889"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15890"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15891"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15892"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15893"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15894"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15895"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15896"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15897"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15898"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15899"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15900"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15901"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15902"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15903"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15904"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15905"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15906"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15907"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15908"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15909"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15910"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15911"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15912"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15913"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15914"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15915"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15916"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15917"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15918"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15919"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15920"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15921"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15922"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15923"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15924"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15925"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15926"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15927"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15928"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15929"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15930"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15931"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15932"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15933"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15934"},{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15935"}]} 
2025-08-04 03:00:47.866  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Generic Text']"\n } 
2025-08-04 03:00:47.949 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='Generic Text'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:47.967  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15751"}]} 
2025-08-04 03:00:47.972  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:48.148 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:48.169  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15700"}} 
2025-08-04 03:00:48.233  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Generic Text']"\n } 
2025-08-04 03:00:48.318 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='Generic Text'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:48.330  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15751"}]} 
2025-08-04 03:00:48.361  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Generic Text']"\n } 
2025-08-04 03:00:48.465 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='Generic Text'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:48.480  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15751"}} 
2025-08-04 03:00:48.699  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:48.823 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:48.836  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15700"}} 
2025-08-04 03:00:48.937  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:Tab_FileScan\\:vessels_input",\n   "using": "css selector"\n } 
2025-08-04 03:00:49.045 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:Tab_FileScan\:vessels_input, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:49.072  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15740"}]} 
2025-08-04 03:00:49.107  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:49.155 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:49.171  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15700"}} 
2025-08-04 03:00:49.231  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:Tab_FileScan\\:vessels",\n   "using": "css selector"\n } 
2025-08-04 03:00:49.391 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:Tab_FileScan\:vessels, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:49.423  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15936"}]} 
2025-08-04 03:00:49.627  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:49.688 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:49.705  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15700"}} 
2025-08-04 03:00:49.760  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:Tab_FileScan\\:countries_input",\n   "using": "css selector"\n } 
2025-08-04 03:00:49.854 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:Tab_FileScan\:countries_input, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:49.881  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15741"}]} 
2025-08-04 03:00:49.914  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:49.981 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:50.014  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15700"}} 
2025-08-04 03:00:50.079  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:Tab_FileScan\\:countries",\n   "using": "css selector"\n } 
2025-08-04 03:00:50.160 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:Tab_FileScan\:countries, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:50.192  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15937"}]} 
2025-08-04 03:00:50.381  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:50.458 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:50.485  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15700"}} 
2025-08-04 03:00:50.543  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:Tab_FileScan\\:autoAlert_input",\n   "using": "css selector"\n } 
2025-08-04 03:00:50.640 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:Tab_FileScan\:autoAlert_input, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:50.673  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15742"}]} 
2025-08-04 03:00:50.719  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:50.778 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:50.792  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15700"}} 
2025-08-04 03:00:50.837  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:Tab_FileScan\\:autoAlert",\n   "using": "css selector"\n } 
2025-08-04 03:00:50.935 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:Tab_FileScan\:autoAlert, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:50.982  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15938"}]} 
2025-08-04 03:00:51.181  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:51.295 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:51.313  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15700"}} 
2025-08-04 03:00:51.348  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:Tab_FileScan\\:btnFileScan",\n   "using": "css selector"\n } 
2025-08-04 03:00:51.384  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:51.446 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:Tab_FileScan\:btnFileScan, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:51.451 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:51.478  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.2B33DFC9AA3925731E1F14E7E09C939D.e.36100"}} 
2025-08-04 03:00:51.491  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15939"}]} 
2025-08-04 03:00:51.565  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:Tab_activity\\:btnASearch",\n   "using": "css selector"\n } 
2025-08-04 03:00:51.570  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:51.655 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_activity\:btnASearch, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:51.686  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.2B33DFC9AA3925731E1F14E7E09C939D.e.36142"}]} 
2025-08-04 03:00:51.733 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:51.752  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15700"}} 
2025-08-04 03:00:51.815  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:51.866  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:51.892 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:51.908  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.2B33DFC9AA3925731E1F14E7E09C939D.e.36100"}} 
2025-08-04 03:00:51.997  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:52.072 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:52.084  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.2B33DFC9AA3925731E1F14E7E09C939D.e.36100"}} 
2025-08-04 03:00:52.636 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:52.649  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15700"}} 
2025-08-04 03:00:52.704  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002ftbody[@id='listManagerForm:homepage_business:tabViewListManager:Tab_activity:_tblResults_data']\u002f\u002ftr[1]\u002f\u002ftd[5]"\n } 
2025-08-04 03:00:52.757  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='ui-growl-title']"\n } 
2025-08-04 03:00:52.793 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //tbody[@id='listManagerForm:homepage_business:tabViewListManager:Tab_activity:_tblResults_data']//tr[1]//td[5], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:52.817  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.2B33DFC9AA3925731E1F14E7E09C939D.e.36155"}} 
2025-08-04 03:00:52.827 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='ui-growl-title'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:52.840  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15963"}]} 
2025-08-04 03:00:52.846  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:52.893 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:52.905  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15700"}} 
2025-08-04 03:00:52.979  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='ui-growl-title']"\n } 
2025-08-04 03:00:53.080 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='ui-growl-title'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:53.092  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.2B1E1E128AE3E4AE5E4A7AC073C1844D.e.15963"}]} 
2025-08-04 03:00:53.131  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:53.229 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:53.249  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12174"}} 
2025-08-04 03:00:53.322  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#authorizationProfileForm\\:homepage_business\\:name",\n   "using": "css selector"\n } 
2025-08-04 03:00:53.451 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #authorizationProfileForm\:homepage_business\:name, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:53.509  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12164"}]} 
2025-08-04 03:00:53.665  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:53.764 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:53.790  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12174"}} 
2025-08-04 03:00:53.892  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#authorizationProfileForm\\:homepage_business\\:name",\n   "using": "css selector"\n } 
2025-08-04 03:00:54.019 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #authorizationProfileForm\:homepage_business\:name, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:54.048  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12164"}]} 
2025-08-04 03:00:54.374  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:54.503 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:54.523  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12174"}} 
2025-08-04 03:00:54.537  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:54.583  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#authorizationProfileForm\\:homepage_business\\:btnSearchAPM",\n   "using": "css selector"\n } 
2025-08-04 03:00:54.688 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #authorizationProfileForm\:homepage_business\:btnSearchAPM, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:54.708  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12134"}]} 
2025-08-04 03:00:54.766 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:54.790  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.5D07081325F07268F1F518CD83921928.e.36254"}} 
2025-08-04 03:00:54.868  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@title='Exit']"\n } 
2025-08-04 03:00:54.884  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:54.965 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@title='Exit'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:54.980  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.5D07081325F07268F1F518CD83921928.e.36255"}]} 
2025-08-04 03:00:55.056  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:55.117 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:55.142  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.5D07081325F07268F1F518CD83921928.e.36254"}} 
2025-08-04 03:00:55.311 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:55.344  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12174"}} 
2025-08-04 03:00:55.345  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeScanManager",\n   "using": "css selector"\n } 
2025-08-04 03:00:55.409  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@title='Exit' or @title ='Quitter']"\n } 
2025-08-04 03:00:55.540 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_HomeScanManager, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:55.541 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@title='Exit' or @title ='Quitter'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-04 03:00:55.561  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.337F4A5807C453CD6370B418EAE9E265.e.16073"}} 
2025-08-04 03:00:55.572  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.5D07081325F07268F1F518CD83921928.e.36255"}]} 
2025-08-04 03:00:55.623  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:55.681  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:55.740 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:55.769  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12174"}} 
2025-08-04 03:00:55.774  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:55.791 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:55.805  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.337F4A5807C453CD6370B418EAE9E265.e.16074"}} 
2025-08-04 03:00:55.850  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002ftbody[@id='authorizationProfileForm:homepage_business:_tblResults_data']\u002ftr\u002ftd[3]\u002fa\u002fspan[@title=\"Test-Profile-324443088\"]"\n } 
2025-08-04 03:00:55.863  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeScanManager",\n   "using": "css selector"\n } 
2025-08-04 03:00:55.928 DEBUG 1 - [ttp-epoll-3] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_HomeScanManager, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-04 03:00:55.935 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml# 
2025-08-04 03:00:55.938 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //tbody[@id='authorizationProfileForm:homepage_business:_tblResults_data']/tr/td[3]/a/span[@title="Test-Profile-324443088"], Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:55.947  INFO 1 - [ttp-epoll-3] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.B7C001BBE6C2F33C574A3A5438A0AE75.d.337F4A5807C453CD6370B418EAE9E265.e.16073"}]} 
2025-08-04 03:00:55.955  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.5D07081325F07268F1F518CD83921928.e.36254"}} 
2025-08-04 03:00:55.964  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12189"}]} 
2025-08-04 03:00:55.969  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002ftbody[@id='authorizationProfileForm:homepage_business:_tblResults_data']\u002ftr\u002ftd[3]\u002fa"\n } 
2025-08-04 03:00:56.057  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fbutton[.='Yes']"\n } 
2025-08-04 03:00:56.083 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //tbody[@id='authorizationProfileForm:homepage_business:_tblResults_data']/tr/td[3]/a, Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:56.097  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12190"}} 
2025-08-04 03:00:56.142 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //button[.='Yes'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml# 
2025-08-04 03:00:56.152  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.5D07081325F07268F1F518CD83921928.e.36231"}]} 
2025-08-04 03:00:56.177  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:56.245  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:56.289 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:56.308  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12174"}} 
2025-08-04 03:00:56.376  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002ftbody[@id='authorizationProfileForm:homepage_business:_tblResults_data']\u002ftr\u002ftd[3]\u002fa"\n } 
2025-08-04 03:00:56.505 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //tbody[@id='authorizationProfileForm:homepage_business:_tblResults_data']/tr/td[3]/a, Command: findElements, URL: http://************:8080/AMLUI/Modules/admin/jsp/AuthorizationProfileManager/Homepage.xhtml?module_name=AuthorizationProfileManager&app_name=ADMIN 
2025-08-04 03:00:56.551  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12190"}]} 
2025-08-04 03:00:56.864  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:57.027  WARN 1 - [ttp-epoll-4] healenium                        : [Save Elements] Error during save elements: [f.F55F3D0EB16719BC6F8FA239EE46C51F.d.5D07081325F07268F1F518CD83921928.e.36254]. Message: stale element reference: stale element not found\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [41886cb1ceb2386f552b3eac19d01320, executeScript {script=var items = [];var result = {items: null, url: null};var a = arguments[0];while (a != document) {  var child = a;  var i=0; while(child=child.previousElementSibling) i++;  var node = {tag:null,id:null,index:null,classes:[],other:{},innerText:""};  if (a.tagName !== undefined) {    node.tag = a.tagName.toLowerCase();  }  node.id = a.id;  node.index = i;  node.innerText = a.innerText || "";  if (a.hasAttribute("class")) {	  node.classes = a.attributes.class.value.split(' ');  }  for (index = 0; index < a.attributes.length; ++index) {      var attrName = a.attributes[index].name;      if (["id","class"].indexOf(attrName) == -1){		    node.other[attrName] = a.attributes[index].value;      }  };  items.unshift(node);  a = a.parentNode;}result.items = items;result.url = window.location.href;return JSON.stringify(result);, args=[{element-6066-11e4-a52e-4f735466cecf=f.F55F3D0EB16719BC6F8FA239EE46C51F.d.5D07081325F07268F1F518CD83921928.e.36254}]}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:40951}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.zU...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: a5ffb8a0a9f9, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 41886cb1ceb2386f552b3eac19d01320. Exception: org.openqa.selenium.StaleElementReferenceException: stale element reference: stale element not found\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [41886cb1ceb2386f552b3eac19d01320, executeScript {script=var items = [];var result = {items: null, url: null};var a = arguments[0];while (a != document) {  var child = a;  var i=0; while(child=child.previousElementSibling) i++;  var node = {tag:null,id:null,index:null,classes:[],other:{},innerText:""};  if (a.tagName !== undefined) {    node.tag = a.tagName.toLowerCase();  }  node.id = a.id;  node.index = i;  node.innerText = a.innerText || "";  if (a.hasAttribute("class")) {	  node.classes = a.attributes.class.value.split(' ');  }  for (index = 0; index < a.attributes.length; ++index) {      var attrName = a.attributes[index].name;      if (["id","class"].indexOf(attrName) == -1){		    node.other[attrName] = a.attributes[index].value;      }  };  items.unshift(node);  a = a.parentNode;}result.items = items;result.url = window.location.href;return JSON.stringify(result);, args=[{element-6066-11e4-a52e-4f735466cecf=f.F55F3D0EB16719BC6F8FA239EE46C51F.d.5D07081325F07268F1F518CD83921928.e.36254}]}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:40951}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.zU...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: a5ffb8a0a9f9, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 41886cb1ceb2386f552b3eac19d01320 
2025-08-04 03:00:57.030  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.5D07081325F07268F1F518CD83921928.e.36254"}} 
2025-08-04 03:00:57.100  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#pageLoginForm\\:login_business\\:UserName",\n   "using": "css selector"\n } 
2025-08-04 03:00:57.191 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #pageLoginForm\:login_business\:UserName, Command: findElements, URL: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-04 03:00:57.203  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.F55F3D0EB16719BC6F8FA239EE46C51F.d.8C578F158D48926DDF76724FF4253828.e.36263"}]} 
2025-08-04 03:00:57.373  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:57.991  WARN 1 - [ttp-epoll-4] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-04 03:00:58.001  WARN 1 - [ttp-epoll-4] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [41886cb1ceb2386f552b3eac19d01320, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:40951}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.zU...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: a5ffb8a0a9f9, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 41886cb1ceb2386f552b3eac19d01320 
2025-08-04 03:00:58.073 DEBUG 1 - [ttp-epoll-4] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-04 03:00:58.112  WARN 1 - [ttp-epoll-4] healenium                        : Trying to heal... 
2025-08-04 03:00:58.117  WARN 1 - [ttp-epoll-4] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-04 03:00:58.125  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fspan[contains(text(),'Back to login')]"\n } 
2025-08-04 03:00:58.148  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[]} 
2025-08-04 03:00:58.153  WARN 1 - [ttp-epoll-2] healenium                        : [Save Elements] Error during save elements: [f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12174]. Message: stale element reference: stale element not found in the current frame\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [27abd5f6db4a82d7ebae1cc93ba25ad6, executeScript {script=var items = [];var result = {items: null, url: null};var a = arguments[0];while (a != document) {  var child = a;  var i=0; while(child=child.previousElementSibling) i++;  var node = {tag:null,id:null,index:null,classes:[],other:{},innerText:""};  if (a.tagName !== undefined) {    node.tag = a.tagName.toLowerCase();  }  node.id = a.id;  node.index = i;  node.innerText = a.innerText || "";  if (a.hasAttribute("class")) {	  node.classes = a.attributes.class.value.split(' ');  }  for (index = 0; index < a.attributes.length; ++index) {      var attrName = a.attributes[index].name;      if (["id","class"].indexOf(attrName) == -1){		    node.other[attrName] = a.attributes[index].value;      }  };  items.unshift(node);  a = a.parentNode;}result.items = items;result.url = window.location.href;return JSON.stringify(result);, args=[{element-6066-11e4-a52e-4f735466cecf=f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12174}]}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:42671}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.7V...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: 6f3adf29ecd5, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 27abd5f6db4a82d7ebae1cc93ba25ad6. Exception: org.openqa.selenium.StaleElementReferenceException: stale element reference: stale element not found in the current frame\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [27abd5f6db4a82d7ebae1cc93ba25ad6, executeScript {script=var items = [];var result = {items: null, url: null};var a = arguments[0];while (a != document) {  var child = a;  var i=0; while(child=child.previousElementSibling) i++;  var node = {tag:null,id:null,index:null,classes:[],other:{},innerText:""};  if (a.tagName !== undefined) {    node.tag = a.tagName.toLowerCase();  }  node.id = a.id;  node.index = i;  node.innerText = a.innerText || "";  if (a.hasAttribute("class")) {	  node.classes = a.attributes.class.value.split(' ');  }  for (index = 0; index < a.attributes.length; ++index) {      var attrName = a.attributes[index].name;      if (["id","class"].indexOf(attrName) == -1){		    node.other[attrName] = a.attributes[index].value;      }  };  items.unshift(node);  a = a.parentNode;}result.items = items;result.url = window.location.href;return JSON.stringify(result);, args=[{element-6066-11e4-a52e-4f735466cecf=f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12174}]}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:42671}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.7V...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: 6f3adf29ecd5, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 27abd5f6db4a82d7ebae1cc93ba25ad6 
2025-08-04 03:00:58.157  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.0B52669B11D1F91EFE5020D3E55511E9.d.06A4EABCBFBAB6C4D0C67608EF30AD6B.e.12174"}} 
2025-08-04 03:00:58.200  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:58.244  INFO 1 - [ttp-epoll-3] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-04 03:00:58.263  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fdiv[@id=\"authorizationProfileDetailForm:detail_business:applicationPickListId\"]\u002fdiv[3]\u002ful\u002fli[@data-item-label=\"SWS\"]"\n } 
2025-08-04 03:00:58.284  WARN 1 - [ttp-epoll-4] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-04 03:00:58.289  WARN 1 - [ttp-epoll-4] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [41886cb1ceb2386f552b3eac19d01320, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:40951}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.zU...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: a5ffb8a0a9f9, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: 41886cb1ceb2386f552b3eac19d01320 
2025-08-04 03:00:58.315 DEBUG 1 - [ttp-epoll-4] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
