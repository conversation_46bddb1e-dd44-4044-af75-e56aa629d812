2025-08-03 06:38:33.519  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.F7478109521FD02C9B0758570EC9CE15.e.134270"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.F7478109521FD02C9B0758570EC9CE15.e.134271"}]} 
2025-08-03 06:38:33.555  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='ListName-116653233']"\n } 
2025-08-03 06:38:33.639 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='ListName-116653233'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 06:38:33.663  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.F7478109521FD02C9B0758570EC9CE15.e.134271"}]} 
2025-08-03 06:38:33.670  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:33.750 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 06:38:33.774  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.F7478109521FD02C9B0758570EC9CE15.e.134113"}} 
2025-08-03 06:38:33.843  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='ListName-116653233']"\n } 
2025-08-03 06:38:33.920 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='ListName-116653233'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 06:38:33.946  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.F7478109521FD02C9B0758570EC9CE15.e.134271"}]} 
2025-08-03 06:38:34.021  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='ListName-116653233']"\n } 
2025-08-03 06:38:34.116 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='ListName-116653233'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 06:38:34.145  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.F7478109521FD02C9B0758570EC9CE15.e.134271"}} 
2025-08-03 06:38:34.516  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:34.659 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 06:38:34.688  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.F7478109521FD02C9B0758570EC9CE15.e.134113"}} 
2025-08-03 06:38:34.802  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:tab_new_list_explorer\\:name",\n   "using": "css selector"\n } 
2025-08-03 06:38:34.981 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:tab_new_list_explorer\:name, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 06:38:35.052  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.F7478109521FD02C9B0758570EC9CE15.e.134282"}]} 
2025-08-03 06:38:35.252  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:35.375 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 06:38:35.411  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.F7478109521FD02C9B0758570EC9CE15.e.134113"}} 
2025-08-03 06:38:35.502  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:tab_new_list_explorer\\:name",\n   "using": "css selector"\n } 
2025-08-03 06:38:35.726 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:tab_new_list_explorer\:name, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 06:38:35.796  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.F7478109521FD02C9B0758570EC9CE15.e.134282"}]} 
2025-08-03 06:38:36.436  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:36.526 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 06:38:36.563  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.F7478109521FD02C9B0758570EC9CE15.e.134113"}} 
2025-08-03 06:38:36.619  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:tab_new_list_explorer\\:btnLESearch",\n   "using": "css selector"\n } 
2025-08-03 06:38:36.823 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:tab_new_list_explorer\:btnLESearch, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 06:38:36.896  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.F7478109521FD02C9B0758570EC9CE15.e.134304"}]} 
2025-08-03 06:38:37.887  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:38.015 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:38:38.038  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4BB2FE442149284E91C0DF5043A3DD2C.e.134426"}} 
2025-08-03 06:38:38.085  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:tab_new_list_explorer\\:_tblResults\\:_btnNewEntry",\n   "using": "css selector"\n } 
2025-08-03 06:38:38.216 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:tab_new_list_explorer\:_tblResults\:_btnNewEntry, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:38:38.295  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4BB2FE442149284E91C0DF5043A3DD2C.e.134390"}} 
2025-08-03 06:38:38.381  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:38.490 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:38:38.508  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4BB2FE442149284E91C0DF5043A3DD2C.e.134426"}} 
2025-08-03 06:38:38.565  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:tab_new_list_explorer\\:_tblResults\\:_btnNewEntry",\n   "using": "css selector"\n } 
2025-08-03 06:38:38.691 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:tab_new_list_explorer\:_tblResults\:_btnNewEntry, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:38:38.769  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4BB2FE442149284E91C0DF5043A3DD2C.e.134390"}]} 
2025-08-03 06:38:39.009  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:39.088 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:38:39.104  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4BB2FE442149284E91C0DF5043A3DD2C.e.134426"}} 
2025-08-03 06:38:39.951  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#listExplorerEditorForm\\:viewDetails\\:ListEditorTabs\\:view_details\\:entryEditorTabs\\:listExplorerTypeAndNames\\:name",\n   "using": "css selector"\n } 
2025-08-03 06:38:40.041 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listExplorerEditorForm\:viewDetails\:ListEditorTabs\:view_details\:entryEditorTabs\:listExplorerTypeAndNames\:name, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:38:40.067  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4BB2FE442149284E91C0DF5043A3DD2C.e.134438"}]} 
2025-08-03 06:38:40.171  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:40.228 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:38:40.255  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4BB2FE442149284E91C0DF5043A3DD2C.e.134561"}} 
2025-08-03 06:38:40.311  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#listExplorerEditorForm\\:viewDetails\\:ListEditorTabs\\:view_details\\:entryEditorTabs\\:listExplorerTypeAndNames\\:name",\n   "using": "css selector"\n } 
2025-08-03 06:38:40.369 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listExplorerEditorForm\:viewDetails\:ListEditorTabs\:view_details\:entryEditorTabs\:listExplorerTypeAndNames\:name, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:38:40.397  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4BB2FE442149284E91C0DF5043A3DD2C.e.134438"}]} 
2025-08-03 06:38:40.744  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:40.863 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:38:40.879  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4BB2FE442149284E91C0DF5043A3DD2C.e.134561"}} 
2025-08-03 06:38:40.941  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:41.060 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:38:41.078  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4BB2FE442149284E91C0DF5043A3DD2C.e.134561"}} 
2025-08-03 06:38:41.181  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:41.260 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:38:41.293  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4BB2FE442149284E91C0DF5043A3DD2C.e.134561"}} 
2025-08-03 06:38:41.359  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:41.476 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:38:41.490  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4BB2FE442149284E91C0DF5043A3DD2C.e.134561"}} 
2025-08-03 06:38:41.580  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:41.713 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:38:41.743  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4BB2FE442149284E91C0DF5043A3DD2C.e.134561"}} 
2025-08-03 06:38:41.827  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#listExplorerEditorForm\\:viewDetails\\:ListEditorTabs\\:view_details\\:entryEditorTabs\\:listExplorerTypeAndNames\\:firstNameField",\n   "using": "css selector"\n } 
2025-08-03 06:38:41.896 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listExplorerEditorForm\:viewDetails\:ListEditorTabs\:view_details\:entryEditorTabs\:listExplorerTypeAndNames\:firstNameField, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:38:41.916  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4BB2FE442149284E91C0DF5043A3DD2C.e.134439"}]} 
2025-08-03 06:38:42.077  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:42.119 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:38:42.132  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4BB2FE442149284E91C0DF5043A3DD2C.e.134561"}} 
2025-08-03 06:38:42.181  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#listExplorerEditorForm\\:viewDetails\\:ListEditorTabs\\:view_details\\:entryEditorTabs\\:listExplorerTypeAndNames\\:firstNameField",\n   "using": "css selector"\n } 
2025-08-03 06:38:42.244 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listExplorerEditorForm\:viewDetails\:ListEditorTabs\:view_details\:entryEditorTabs\:listExplorerTypeAndNames\:firstNameField, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:38:42.267  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4BB2FE442149284E91C0DF5043A3DD2C.e.134439"}]} 
2025-08-03 06:38:42.661  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:42.792 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:38:42.839  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4BB2FE442149284E91C0DF5043A3DD2C.e.134561"}} 
2025-08-03 06:38:42.938  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:43.068 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:38:43.090  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4BB2FE442149284E91C0DF5043A3DD2C.e.134561"}} 
2025-08-03 06:38:43.177  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:43.332 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:38:43.347  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4BB2FE442149284E91C0DF5043A3DD2C.e.134561"}} 
2025-08-03 06:38:43.440  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:43.551 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:38:43.567  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4BB2FE442149284E91C0DF5043A3DD2C.e.134561"}} 
2025-08-03 06:38:43.651  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:43.753 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:38:43.786  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4BB2FE442149284E91C0DF5043A3DD2C.e.134561"}} 
2025-08-03 06:38:43.849  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:43.931 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:38:43.947  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4BB2FE442149284E91C0DF5043A3DD2C.e.134561"}} 
2025-08-03 06:38:44.083  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:44.201 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:38:44.220  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4BB2FE442149284E91C0DF5043A3DD2C.e.134561"}} 
2025-08-03 06:38:44.316  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#listExplorerEditorForm\\:viewDetails\\:ListEditorTabs\\:listExplorerDetails\\:btnSave",\n   "using": "css selector"\n } 
2025-08-03 06:38:44.419 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listExplorerEditorForm\:viewDetails\:ListEditorTabs\:listExplorerDetails\:btnSave, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:38:44.450  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4BB2FE442149284E91C0DF5043A3DD2C.e.134586"}]} 
2025-08-03 06:38:44.636  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:44.741 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:38:44.777  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4BB2FE442149284E91C0DF5043A3DD2C.e.134561"}} 
2025-08-03 06:38:44.857  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:44.930 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:38:44.947  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4BB2FE442149284E91C0DF5043A3DD2C.e.134561"}} 
2025-08-03 06:38:45.011  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:45.091 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:38:45.109  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4BB2FE442149284E91C0DF5043A3DD2C.e.134561"}} 
2025-08-03 06:38:45.174  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:45.238 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:38:45.259  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4BB2FE442149284E91C0DF5043A3DD2C.e.134561"}} 
2025-08-03 06:38:46.184  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:46.305 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:38:46.341  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4BB2FE442149284E91C0DF5043A3DD2C.e.134708"}} 
2025-08-03 06:38:46.432  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002ftbody\u002f\u002ftr\u002f\u002ftd[.='EntryName-116653233, EntryFirstName-116653233']"\n } 
2025-08-03 06:38:46.590 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //tbody//tr//td[.='EntryName-116653233, EntryFirstName-116653233'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:38:46.637  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4BB2FE442149284E91C0DF5043A3DD2C.e.134709"}]} 
2025-08-03 06:38:46.643  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeScanManager",\n   "using": "css selector"\n } 
2025-08-03 06:38:46.780 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_HomeScanManager, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:38:46.804  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4BB2FE442149284E91C0DF5043A3DD2C.e.134710"}} 
2025-08-03 06:38:46.932  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:47.059 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:38:47.098  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4BB2FE442149284E91C0DF5043A3DD2C.e.134708"}} 
2025-08-03 06:38:47.161  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeScanManager",\n   "using": "css selector"\n } 
2025-08-03 06:38:47.231 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_HomeScanManager, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:38:47.254  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4BB2FE442149284E91C0DF5043A3DD2C.e.134710"}]} 
2025-08-03 06:38:48.829  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:48.953 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:38:48.988  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134820"}} 
2025-08-03 06:38:49.062  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fform[@id='topbar-right']\u002ful\u002flabel"\n } 
2025-08-03 06:38:49.134 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //form[@id='topbar-right']/ul/label, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:38:49.155  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134758"}]} 
2025-08-03 06:38:49.217  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fa[contains(text(),'Name Checker')]"\n } 
2025-08-03 06:38:49.303 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //a[contains(text(),'Name Checker')], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:38:49.320  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134821"}} 
2025-08-03 06:38:49.395  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:49.462 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:38:49.480  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134820"}} 
2025-08-03 06:38:49.546  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fa[contains(text(),'Name Checker')]"\n } 
2025-08-03 06:38:49.629 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //a[contains(text(),'Name Checker')], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:38:49.643  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134821"}]} 
2025-08-03 06:38:49.859  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:NameChecker_business\\:ncZoneId_label",\n   "using": "css selector"\n } 
2025-08-03 06:38:49.942 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:NameChecker_business\:ncZoneId_label, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:38:49.956  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134767"}} 
2025-08-03 06:38:50.057  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:50.116 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:38:50.133  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134820"}} 
2025-08-03 06:38:50.192  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:NameChecker_business\\:btnReset",\n   "using": "css selector"\n } 
2025-08-03 06:38:50.296 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:NameChecker_business\:btnReset, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:38:50.316  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134795"}]} 
2025-08-03 06:38:50.405  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:50.510 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:38:50.545  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134820"}} 
2025-08-03 06:38:50.679  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:50.780 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:38:50.800  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134820"}} 
2025-08-03 06:38:50.852  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:50.905 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:38:50.918  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134820"}} 
2025-08-03 06:38:51.003  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:NameChecker_business\\:ncZoneId",\n   "using": "css selector"\n } 
2025-08-03 06:38:51.089 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:NameChecker_business\:ncZoneId, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:38:51.114  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134839"}]} 
2025-08-03 06:38:52.387  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role='option']"\n } 
2025-08-03 06:38:52.437 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role='option'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:38:52.456  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134840"}]} 
2025-08-03 06:38:52.462  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Common Zone 05']"\n } 
2025-08-03 06:38:52.577 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='Common Zone 05'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:38:52.592  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134840"}]} 
2025-08-03 06:38:52.598  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:52.658 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:38:52.672  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134820"}} 
2025-08-03 06:38:52.749  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Common Zone 05']"\n } 
2025-08-03 06:38:52.840 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='Common Zone 05'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:38:52.855  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134840"}]} 
2025-08-03 06:38:52.894  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Common Zone 05']"\n } 
2025-08-03 06:38:52.964 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='Common Zone 05'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:38:52.980  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134840"}} 
2025-08-03 06:38:53.295  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:53.368 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:38:53.383  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134820"}} 
2025-08-03 06:38:53.450  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:NameChecker_business\\:rankCbx",\n   "using": "css selector"\n } 
2025-08-03 06:38:53.531 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:NameChecker_business\:rankCbx, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:38:53.550  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134841"}]} 
2025-08-03 06:38:54.833  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role='option']"\n } 
2025-08-03 06:38:57.081 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role='option'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:38:57.150  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134840"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134842"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134843"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134844"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134845"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134846"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134847"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134848"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134849"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134850"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134851"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134852"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134853"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134854"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134855"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134856"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134857"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134858"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134859"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134860"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134861"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134862"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134863"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134864"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134865"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134866"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134867"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134868"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134869"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134870"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134871"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134872"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134873"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134874"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134875"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134876"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134877"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134878"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134879"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134880"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134881"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134882"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134883"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134884"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134885"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134886"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134887"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134888"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134889"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134890"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134891"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134892"}]} 
2025-08-03 06:38:57.165  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='50']"\n } 
2025-08-03 06:38:57.266 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='50'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:38:57.296  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134892"}]} 
2025-08-03 06:38:57.301  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:57.361 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:38:57.383  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134820"}} 
2025-08-03 06:38:57.469  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='50']"\n } 
2025-08-03 06:38:57.589 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='50'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:38:57.602  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134892"}]} 
2025-08-03 06:38:57.642  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='50']"\n } 
2025-08-03 06:38:57.743 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='50'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:38:57.772  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134892"}} 
2025-08-03 06:38:58.096  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:58.178 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:38:58.208  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134820"}} 
2025-08-03 06:38:58.280  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:NameChecker_business\\:name",\n   "using": "css selector"\n } 
2025-08-03 06:38:58.379 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:NameChecker_business\:name, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:38:58.397  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134827"}]} 
2025-08-03 06:38:58.534  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:58.608 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:38:58.620  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134820"}} 
2025-08-03 06:38:58.693  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:NameChecker_business\\:name",\n   "using": "css selector"\n } 
2025-08-03 06:38:58.768 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:NameChecker_business\:name, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:38:58.796  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134827"}]} 
2025-08-03 06:38:59.252  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:59.361 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:38:59.383  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134820"}} 
2025-08-03 06:38:59.456  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:NameChecker_business\\:auto_create_alert_input",\n   "using": "css selector"\n } 
2025-08-03 06:38:59.618 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:NameChecker_business\:auto_create_alert_input, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:38:59.642  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134834"}]} 
2025-08-03 06:38:59.710  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:38:59.816 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:38:59.848  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134820"}} 
2025-08-03 06:38:59.910  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:NameChecker_business\\:countries_input",\n   "using": "css selector"\n } 
2025-08-03 06:38:59.996 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:NameChecker_business\:countries_input, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:00.033  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134833"}]} 
2025-08-03 06:39:00.098  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:00.169 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:00.194  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134820"}} 
2025-08-03 06:39:00.266  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:NameChecker_business\\:countries",\n   "using": "css selector"\n } 
2025-08-03 06:39:00.363 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:NameChecker_business\:countries, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:00.382  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134895"}]} 
2025-08-03 06:39:00.774  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:00.892 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:00.912  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134820"}} 
2025-08-03 06:39:00.987  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:NameChecker_business\\:vessels_input",\n   "using": "css selector"\n } 
2025-08-03 06:39:01.100 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:NameChecker_business\:vessels_input, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:01.119  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134832"}]} 
2025-08-03 06:39:01.174  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:01.251 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:01.266  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134820"}} 
2025-08-03 06:39:01.322  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:NameChecker_business\\:vessels",\n   "using": "css selector"\n } 
2025-08-03 06:39:01.396 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:NameChecker_business\:vessels, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:01.420  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134896"}]} 
2025-08-03 06:39:01.648  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:01.715 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:01.735  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134820"}} 
2025-08-03 06:39:01.807  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:NameChecker_business\\:btnScan",\n   "using": "css selector"\n } 
2025-08-03 06:39:01.884 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:NameChecker_business\:btnScan, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:01.911  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134897"}]} 
2025-08-03 06:39:01.990  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:02.086 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:02.102  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134820"}} 
2025-08-03 06:39:02.189  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:02.257 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:02.286  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134820"}} 
2025-08-03 06:39:02.365  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:02.455 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:02.490  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134820"}} 
2025-08-03 06:39:02.592  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:02.684 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:02.699  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134820"}} 
2025-08-03 06:39:02.771  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:02.857 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:02.875  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134820"}} 
2025-08-03 06:39:02.925  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:03.012 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:03.029  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134820"}} 
2025-08-03 06:39:03.095  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:03.167 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:03.196  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134820"}} 
2025-08-03 06:39:05.273  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='ui-growl-title']"\n } 
2025-08-03 06:39:05.308  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[]} 
2025-08-03 06:39:05.315  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:05.370 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:05.391  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134820"}} 
2025-08-03 06:39:05.445  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:05.510 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:05.528  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134820"}} 
2025-08-03 06:39:05.590  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[contains(@id , 'scanManagerForm:homepage_business:tabViewScanManager:NameChecker_business:detectionItem:0:tagDet')]\u002f\u002fancestor::td[1]"\n } 
2025-08-03 06:39:05.668 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[contains(@id , 'scanManagerForm:homepage_business:tabViewScanManager:NameChecker_business:detectionItem:0:tagDet')]//ancestor::td[1], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:05.685  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134899"}]} 
2025-08-03 06:39:05.750  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:05.823 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:05.841  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134820"}} 
2025-08-03 06:39:05.892  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002ftable\u002ftbody[@id='scanManagerForm:homepage_business:tabViewScanManager:NameChecker_business:detectionItem_data']\u002ftr\u002ftd[count(\u002f\u002fspan[.='Detection ID']\u002fancestor::th\u002fpreceding-sibling::th)+1]"\n } 
2025-08-03 06:39:05.976 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //table/tbody[@id='scanManagerForm:homepage_business:tabViewScanManager:NameChecker_business:detectionItem_data']/tr/td[count(//span[.='Detection ID']/ancestor::th/preceding-sibling::th)+1], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:06.006  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134900"}]} 
2025-08-03 06:39:06.070  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeFilteringDetectionManager",\n   "using": "css selector"\n } 
2025-08-03 06:39:06.139 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_HomeFilteringDetectionManager, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:06.154  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134901"}} 
2025-08-03 06:39:06.277  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:06.348 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:06.360  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134820"}} 
2025-08-03 06:39:06.409  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeFilteringDetectionManager",\n   "using": "css selector"\n } 
2025-08-03 06:39:06.513 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_HomeFilteringDetectionManager, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:06.525  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.17A0818F75ADB543BE6ABE238C457A96.e.134901"}]} 
2025-08-03 06:39:08.227  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:08.345 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:08.367  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134999"}} 
2025-08-03 06:39:08.430  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fform[@id='topbar-right']\u002ful\u002flabel"\n } 
2025-08-03 06:39:08.509 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //form[@id='topbar-right']/ul/label, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:08.539  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134939"}]} 
2025-08-03 06:39:08.606  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:08.664 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:08.677  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134999"}} 
2025-08-03 06:39:08.738  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#auditManagerForm\\:homepage_business\\:detectionSearch\\:btnReset",\n   "using": "css selector"\n } 
2025-08-03 06:39:08.833 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #auditManagerForm\:homepage_business\:detectionSearch\:btnReset, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:08.847  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134968"}]} 
2025-08-03 06:39:09.007  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:09.093 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:09.110  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134999"}} 
2025-08-03 06:39:09.192  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:09.431 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:09.464  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134999"}} 
2025-08-03 06:39:09.606  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:09.659 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:09.670  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134999"}} 
2025-08-03 06:39:09.728  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#auditManagerForm\\:homepage_business\\:detectionSearch\\:col2\\:0\\:detectionSearchFields\\:numberExl_input",\n   "using": "css selector"\n } 
2025-08-03 06:39:09.815 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #auditManagerForm\:homepage_business\:detectionSearch\:col2\:0\:detectionSearchFields\:numberExl_input, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:09.836  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.135007"}]} 
2025-08-03 06:39:09.969  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:10.073 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:10.093  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134999"}} 
2025-08-03 06:39:10.141  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#auditManagerForm\\:homepage_business\\:detectionSearch\\:col2\\:0\\:detectionSearchFields\\:numberExl_input",\n   "using": "css selector"\n } 
2025-08-03 06:39:10.211 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #auditManagerForm\:homepage_business\:detectionSearch\:col2\:0\:detectionSearchFields\:numberExl_input, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:10.230  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.135007"}]} 
2025-08-03 06:39:10.482  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:10.572 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:10.602  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134999"}} 
2025-08-03 06:39:10.661  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#auditManagerForm\\:homepage_business\\:detectionSearch\\:btnSearch",\n   "using": "css selector"\n } 
2025-08-03 06:39:10.761 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #auditManagerForm\:homepage_business\:detectionSearch\:btnSearch, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:10.780  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.135016"}]} 
2025-08-03 06:39:10.850  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:10.942 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:10.964  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134999"}} 
2025-08-03 06:39:11.053  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:11.608 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:11.624  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134999"}} 
2025-08-03 06:39:11.681  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:11.788 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:11.807  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134999"}} 
2025-08-03 06:39:11.872  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[contains(@id , 'auditManagerForm:homepage_business:Detections_list:detectionResultsTable:0:tagDet')]\u002f\u002fancestor::td[1]"\n } 
2025-08-03 06:39:11.980 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[contains(@id , 'auditManagerForm:homepage_business:Detections_list:detectionResultsTable:0:tagDet')]//ancestor::td[1], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:11.999  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.135074"}]} 
2025-08-03 06:39:12.082  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:12.146 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:12.161  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134999"}} 
2025-08-03 06:39:12.226  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[.='7906']"\n } 
2025-08-03 06:39:12.332 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[.='7906'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:12.356  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.135075"}]} 
2025-08-03 06:39:12.465  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:12.617 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:12.640  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134999"}} 
2025-08-03 06:39:12.741  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:12.849 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:12.870  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134999"}} 
2025-08-03 06:39:12.953  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:13.355 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:13.383  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134999"}} 
2025-08-03 06:39:13.460  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:13.551 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:13.564  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134999"}} 
2025-08-03 06:39:13.636  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#auditManagerForm\\:homepage_business\\:Detections_list\\:violation_list\\:btnAddGG",\n   "using": "css selector"\n } 
2025-08-03 06:39:13.720 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #auditManagerForm\:homepage_business\:Detections_list\:violation_list\:btnAddGG, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:13.739  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.135106"}]} 
2025-08-03 06:39:13.808  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:13.882 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:13.900  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134999"}} 
2025-08-03 06:39:13.992  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:14.117 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:14.163  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134999"}} 
2025-08-03 06:39:14.254  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:14.366 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:14.392  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134999"}} 
2025-08-03 06:39:14.460  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:14.570 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:14.591  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134999"}} 
2025-08-03 06:39:14.644  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:14.766 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:14.786  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134999"}} 
2025-08-03 06:39:14.851  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:14.933 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:14.952  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134999"}} 
2025-08-03 06:39:15.026  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:15.105 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:15.119  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134999"}} 
2025-08-03 06:39:15.167  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:15.247 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:15.262  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134999"}} 
2025-08-03 06:39:15.361  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:15.447 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:15.485  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134999"}} 
2025-08-03 06:39:15.889  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:15.992 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:16.012  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134999"}} 
2025-08-03 06:39:16.087  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#auditManagerForm\\:homepage_business\\:Detections_list\\:Good_guy_management\\:btnScanGG",\n   "using": "css selector"\n } 
2025-08-03 06:39:16.168 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #auditManagerForm\:homepage_business\:Detections_list\:Good_guy_management\:btnScanGG, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:16.204  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.135139"}]} 
2025-08-03 06:39:16.270  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:16.380 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:16.399  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134999"}} 
2025-08-03 06:39:16.466  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:16.578 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:16.599  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134999"}} 
2025-08-03 06:39:16.732  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#auditManagerForm\\:homepage_business\\:Detections_list\\:Good_guy_management\\:btnAcceptGG",\n   "using": "css selector"\n } 
2025-08-03 06:39:16.857 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #auditManagerForm\:homepage_business\:Detections_list\:Good_guy_management\:btnAcceptGG, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:16.878  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.135158"}]} 
2025-08-03 06:39:16.920  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:16.988 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:17.013  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134999"}} 
2025-08-03 06:39:17.097  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:17.172 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:17.192  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134999"}} 
2025-08-03 06:39:17.288  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:17.375 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:17.391  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134999"}} 
2025-08-03 06:39:17.485  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:17.726 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:17.754  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134999"}} 
2025-08-03 06:39:17.860  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@id='auditManagerForm:homepage_business:Detections_list:createGGDlg']\u002f\u002f*[contains(@class,'ui-icon-closethick')]"\n } 
2025-08-03 06:39:17.945 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@id='auditManagerForm:homepage_business:Detections_list:createGGDlg']//*[contains(@class,'ui-icon-closethick')], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:17.969  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.135179"}]} 
2025-08-03 06:39:18.019  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:18.089 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:18.105  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134999"}} 
2025-08-03 06:39:18.152  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='ui-growl-title']"\n } 
2025-08-03 06:39:18.246 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='ui-growl-title'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:18.259  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.135180"}]} 
2025-08-03 06:39:18.264  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:18.355 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:18.369  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134999"}} 
2025-08-03 06:39:18.437  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='ui-growl-title']"\n } 
2025-08-03 06:39:18.508 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='ui-growl-title'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:18.533  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.135180"}]} 
2025-08-03 06:39:18.616  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeScanManager",\n   "using": "css selector"\n } 
2025-08-03 06:39:18.676 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_HomeScanManager, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:18.712  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.135181"}} 
2025-08-03 06:39:18.838  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:18.926 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:18.944  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.134999"}} 
2025-08-03 06:39:19.046  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeScanManager",\n   "using": "css selector"\n } 
2025-08-03 06:39:19.142 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_HomeScanManager, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:19.163  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.41D49367B7126BF0F227D66D75A6E04B.e.135181"}]} 
2025-08-03 06:39:20.723  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:20.831 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:20.851  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135291"}} 
2025-08-03 06:39:20.924  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fform[@id='topbar-right']\u002ful\u002flabel"\n } 
2025-08-03 06:39:21.002 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //form[@id='topbar-right']/ul/label, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:21.017  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135228"}]} 
2025-08-03 06:39:21.083  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fa[contains(text(),'Name Checker')]"\n } 
2025-08-03 06:39:21.158 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //a[contains(text(),'Name Checker')], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:21.174  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135292"}} 
2025-08-03 06:39:21.361  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:21.414 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:21.426  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135291"}} 
2025-08-03 06:39:21.491  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fa[contains(text(),'Name Checker')]"\n } 
2025-08-03 06:39:21.581 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //a[contains(text(),'Name Checker')], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:21.598  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135292"}]} 
2025-08-03 06:39:21.798  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:NameChecker_business\\:ncZoneId_label",\n   "using": "css selector"\n } 
2025-08-03 06:39:21.878 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:NameChecker_business\:ncZoneId_label, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:21.911  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135237"}} 
2025-08-03 06:39:22.074  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:22.161 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:22.178  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135291"}} 
2025-08-03 06:39:22.253  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:NameChecker_business\\:btnReset",\n   "using": "css selector"\n } 
2025-08-03 06:39:22.336 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:NameChecker_business\:btnReset, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:22.362  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135265"}]} 
2025-08-03 06:39:22.428  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:22.531 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:22.563  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135291"}} 
2025-08-03 06:39:22.675  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:22.801 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:22.826  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135291"}} 
2025-08-03 06:39:22.934  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:23.047 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:23.079  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135291"}} 
2025-08-03 06:39:23.174  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:NameChecker_business\\:ncZoneId",\n   "using": "css selector"\n } 
2025-08-03 06:39:23.295 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:NameChecker_business\:ncZoneId, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:23.330  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135310"}]} 
2025-08-03 06:39:24.673  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role='option']"\n } 
2025-08-03 06:39:24.767 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role='option'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:24.811  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135311"}]} 
2025-08-03 06:39:24.830  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Common Zone 05']"\n } 
2025-08-03 06:39:24.880 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='Common Zone 05'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:24.892  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135311"}]} 
2025-08-03 06:39:24.897  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:25.075 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:25.086  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135291"}} 
2025-08-03 06:39:25.137  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Common Zone 05']"\n } 
2025-08-03 06:39:25.195 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='Common Zone 05'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:25.223  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135311"}]} 
2025-08-03 06:39:25.274  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Common Zone 05']"\n } 
2025-08-03 06:39:25.355 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='Common Zone 05'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:25.377  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135311"}} 
2025-08-03 06:39:25.619  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:25.668 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:25.682  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135291"}} 
2025-08-03 06:39:25.737  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:NameChecker_business\\:rankCbx",\n   "using": "css selector"\n } 
2025-08-03 06:39:25.819 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:NameChecker_business\:rankCbx, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:25.839  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135312"}]} 
2025-08-03 06:39:27.123  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role='option']"\n } 
2025-08-03 06:39:28.722 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role='option'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:28.759  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135311"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135313"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135314"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135315"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135316"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135317"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135318"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135319"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135320"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135321"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135322"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135323"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135324"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135325"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135326"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135327"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135328"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135329"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135330"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135331"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135332"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135333"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135334"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135335"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135336"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135337"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135338"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135339"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135340"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135341"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135342"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135343"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135344"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135345"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135346"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135347"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135348"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135349"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135350"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135351"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135352"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135353"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135354"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135355"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135356"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135357"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135358"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135359"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135360"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135361"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135362"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135363"}]} 
2025-08-03 06:39:28.771  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='50']"\n } 
2025-08-03 06:39:28.846 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='50'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:28.866  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135363"}]} 
2025-08-03 06:39:28.872  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:28.920 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:28.932  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135291"}} 
2025-08-03 06:39:28.998  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='50']"\n } 
2025-08-03 06:39:29.073 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='50'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:29.085  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135363"}]} 
2025-08-03 06:39:29.117  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='50']"\n } 
2025-08-03 06:39:29.161 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='50'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:29.176  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135363"}} 
2025-08-03 06:39:29.472  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:29.573 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:29.589  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135291"}} 
2025-08-03 06:39:29.652  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:NameChecker_business\\:name",\n   "using": "css selector"\n } 
2025-08-03 06:39:29.721 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:NameChecker_business\:name, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:29.752  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135298"}]} 
2025-08-03 06:39:29.913  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:30.003 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:30.040  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135291"}} 
2025-08-03 06:39:30.106  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:NameChecker_business\\:name",\n   "using": "css selector"\n } 
2025-08-03 06:39:30.205 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:NameChecker_business\:name, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:30.236  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135298"}]} 
2025-08-03 06:39:30.652  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:30.711 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:30.724  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135291"}} 
2025-08-03 06:39:30.811  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:NameChecker_business\\:auto_create_alert_input",\n   "using": "css selector"\n } 
2025-08-03 06:39:30.883 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:NameChecker_business\:auto_create_alert_input, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:30.896  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135305"}]} 
2025-08-03 06:39:30.936  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:31.014 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:31.038  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135291"}} 
2025-08-03 06:39:31.092  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:NameChecker_business\\:auto_create_alert",\n   "using": "css selector"\n } 
2025-08-03 06:39:31.166 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:NameChecker_business\:auto_create_alert, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:31.187  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135366"}]} 
2025-08-03 06:39:31.424  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:31.494 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:31.514  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135291"}} 
2025-08-03 06:39:31.583  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:NameChecker_business\\:countries_input",\n   "using": "css selector"\n } 
2025-08-03 06:39:31.657 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:NameChecker_business\:countries_input, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:31.672  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135304"}]} 
2025-08-03 06:39:31.717  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:31.783 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:31.803  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135291"}} 
2025-08-03 06:39:31.851  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:NameChecker_business\\:vessels_input",\n   "using": "css selector"\n } 
2025-08-03 06:39:31.905 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:NameChecker_business\:vessels_input, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:31.919  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135303"}]} 
2025-08-03 06:39:31.952  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:32.042 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:32.063  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135291"}} 
2025-08-03 06:39:32.118  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:NameChecker_business\\:btnScan",\n   "using": "css selector"\n } 
2025-08-03 06:39:32.206 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:NameChecker_business\:btnScan, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:32.227  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135367"}]} 
2025-08-03 06:39:32.348  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:32.493 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:32.525  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135291"}} 
2025-08-03 06:39:32.618  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:32.724 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:32.739  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135291"}} 
2025-08-03 06:39:32.889  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:33.000 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:33.048  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135291"}} 
2025-08-03 06:39:35.112  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='ui-growl-title']"\n } 
2025-08-03 06:39:35.159  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[]} 
2025-08-03 06:39:35.166  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:35.261 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:35.277  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135291"}} 
2025-08-03 06:39:35.342  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:35.427 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:35.449  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135291"}} 
2025-08-03 06:39:35.526  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[contains(@id , 'scanManagerForm:homepage_business:tabViewScanManager:NameChecker_business:detectionItem:0:tagDet')]\u002f\u002fancestor::td[1]"\n } 
2025-08-03 06:39:35.630 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[contains(@id , 'scanManagerForm:homepage_business:tabViewScanManager:NameChecker_business:detectionItem:0:tagDet')]//ancestor::td[1], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:35.650  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135369"}]} 
2025-08-03 06:39:35.707  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:35.786 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:35.802  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135291"}} 
2025-08-03 06:39:35.853  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002ftable\u002ftbody[@id='scanManagerForm:homepage_business:tabViewScanManager:NameChecker_business:detectionItem_data']\u002ftr\u002ftd[count(\u002f\u002fspan[.='Detection ID']\u002fancestor::th\u002fpreceding-sibling::th)+1]"\n } 
2025-08-03 06:39:35.928 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //table/tbody[@id='scanManagerForm:homepage_business:tabViewScanManager:NameChecker_business:detectionItem_data']/tr/td[count(//span[.='Detection ID']/ancestor::th/preceding-sibling::th)+1], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:35.946  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135370"}]} 
2025-08-03 06:39:36.061  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeFilteringDetectionManager",\n   "using": "css selector"\n } 
2025-08-03 06:39:36.112 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_HomeFilteringDetectionManager, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:36.133  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135371"}} 
2025-08-03 06:39:36.215  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:36.284 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:36.300  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135291"}} 
2025-08-03 06:39:36.356  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeFilteringDetectionManager",\n   "using": "css selector"\n } 
2025-08-03 06:39:36.412 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_HomeFilteringDetectionManager, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-03 06:39:36.431  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.99583AFD1EE4AF02AB298E5750E71C1A.e.135371"}]} 
2025-08-03 06:39:38.015  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:38.133 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:38.155  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.257C8FF3C7474D97C7F763D23872FB1F.e.135469"}} 
2025-08-03 06:39:38.202  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fform[@id='topbar-right']\u002ful\u002flabel"\n } 
2025-08-03 06:39:38.294 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //form[@id='topbar-right']/ul/label, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:38.315  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.257C8FF3C7474D97C7F763D23872FB1F.e.135410"}]} 
2025-08-03 06:39:38.381  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:38.437 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:38.455  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.257C8FF3C7474D97C7F763D23872FB1F.e.135469"}} 
2025-08-03 06:39:38.519  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#auditManagerForm\\:homepage_business\\:detectionSearch\\:btnReset",\n   "using": "css selector"\n } 
2025-08-03 06:39:38.613 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #auditManagerForm\:homepage_business\:detectionSearch\:btnReset, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:38.633  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.257C8FF3C7474D97C7F763D23872FB1F.e.135439"}]} 
2025-08-03 06:39:38.824  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:38.930 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:38.944  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.257C8FF3C7474D97C7F763D23872FB1F.e.135469"}} 
2025-08-03 06:39:39.116  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:39.192 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:39.204  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.257C8FF3C7474D97C7F763D23872FB1F.e.135469"}} 
2025-08-03 06:39:39.265  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#auditManagerForm\\:homepage_business\\:detectionSearch\\:col2\\:0\\:detectionSearchFields\\:numberExl_input",\n   "using": "css selector"\n } 
2025-08-03 06:39:39.346 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #auditManagerForm\:homepage_business\:detectionSearch\:col2\:0\:detectionSearchFields\:numberExl_input, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:39.362  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.257C8FF3C7474D97C7F763D23872FB1F.e.135477"}]} 
2025-08-03 06:39:39.485  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:39.554 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:39.568  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.257C8FF3C7474D97C7F763D23872FB1F.e.135469"}} 
2025-08-03 06:39:39.641  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#auditManagerForm\\:homepage_business\\:detectionSearch\\:col2\\:0\\:detectionSearchFields\\:numberExl_input",\n   "using": "css selector"\n } 
2025-08-03 06:39:39.707 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #auditManagerForm\:homepage_business\:detectionSearch\:col2\:0\:detectionSearchFields\:numberExl_input, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:39.721  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.257C8FF3C7474D97C7F763D23872FB1F.e.135477"}]} 
2025-08-03 06:39:39.948  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:40.032 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:40.052  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.257C8FF3C7474D97C7F763D23872FB1F.e.135469"}} 
2025-08-03 06:39:40.124  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#auditManagerForm\\:homepage_business\\:detectionSearch\\:btnSearch",\n   "using": "css selector"\n } 
2025-08-03 06:39:40.200 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #auditManagerForm\:homepage_business\:detectionSearch\:btnSearch, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:40.217  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.257C8FF3C7474D97C7F763D23872FB1F.e.135486"}]} 
2025-08-03 06:39:40.318  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:40.406 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:40.421  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.257C8FF3C7474D97C7F763D23872FB1F.e.135469"}} 
2025-08-03 06:39:40.499  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:41.148 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:41.172  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.257C8FF3C7474D97C7F763D23872FB1F.e.135469"}} 
2025-08-03 06:39:41.243  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:41.336 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:41.362  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.257C8FF3C7474D97C7F763D23872FB1F.e.135469"}} 
2025-08-03 06:39:41.422  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[contains(@id , 'auditManagerForm:homepage_business:Detections_list:detectionResultsTable:0:tagDet')]\u002f\u002fancestor::td[1]"\n } 
2025-08-03 06:39:41.495 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[contains(@id , 'auditManagerForm:homepage_business:Detections_list:detectionResultsTable:0:tagDet')]//ancestor::td[1], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:41.519  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.257C8FF3C7474D97C7F763D23872FB1F.e.135544"}]} 
2025-08-03 06:39:41.564  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:41.666 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:41.679  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.257C8FF3C7474D97C7F763D23872FB1F.e.135469"}} 
2025-08-03 06:39:41.741  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[contains(@id , 'auditManagerForm:homepage_business:Detections_list:detectionResultsTable:0:tagDet')]\u002f\u002fancestor::td[1]"\n } 
2025-08-03 06:39:41.822 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[contains(@id , 'auditManagerForm:homepage_business:Detections_list:detectionResultsTable:0:tagDet')]//ancestor::td[1], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:41.840  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.257C8FF3C7474D97C7F763D23872FB1F.e.135544"}]} 
2025-08-03 06:39:42.205  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:42.677  WARN 1 - [ttp-epoll-2] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-03 06:39:42.679  WARN 1 - [ttp-epoll-2] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [a4d254052d07b789c26942856a805ce5, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:38909}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.FI...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: cd21dbe29cc6, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: a4d254052d07b789c26942856a805ce5 
2025-08-03 06:39:42.709 DEBUG 1 - [ttp-epoll-2] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:42.822  WARN 1 - [ttp-epoll-2] healenium                        : Trying to heal... 
2025-08-03 06:39:42.936  WARN 1 - [ttp-epoll-2] healenium                        : Using healed locator: Scored(score=1.0, value=By.cssSelector: i.pi-spin.pi.ajax-loader.pi-spinner) 
2025-08-03 06:39:43.284 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Healed Elements] Locator(value=i.pi-spin.pi.ajax-loader.pi-spinner, type=By.cssSelector) 
2025-08-03 06:39:43.422  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4D91F4C29B8B6BE52094BF844F1882D3.e.135667"}} 
2025-08-03 06:39:43.512  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@title='Exit']"\n } 
2025-08-03 06:39:43.648 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@title='Exit'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:43.664  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4D91F4C29B8B6BE52094BF844F1882D3.e.135669"}]} 
2025-08-03 06:39:43.726  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:43.804 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:43.822  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4D91F4C29B8B6BE52094BF844F1882D3.e.135667"}} 
2025-08-03 06:39:43.879  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@title='Exit' or @title ='Quitter']"\n } 
2025-08-03 06:39:43.967 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@title='Exit' or @title ='Quitter'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-03 06:39:43.983  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4D91F4C29B8B6BE52094BF844F1882D3.e.135669"}]} 
2025-08-03 06:39:44.115  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:44.213 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP# 
2025-08-03 06:39:44.241  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4D91F4C29B8B6BE52094BF844F1882D3.e.135667"}} 
2025-08-03 06:39:44.320  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fbutton[.='Yes']"\n } 
2025-08-03 06:39:44.430 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //button[.='Yes'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP# 
2025-08-03 06:39:44.445  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4D91F4C29B8B6BE52094BF844F1882D3.e.135624"}]} 
2025-08-03 06:39:44.502  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:44.875  WARN 1 - [ttp-epoll-2] healenium                        : [Save Elements] Error during save elements: [f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4D91F4C29B8B6BE52094BF844F1882D3.e.135667]. Message: stale element reference: stale element not found\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [a4d254052d07b789c26942856a805ce5, executeScript {args=[{element-6066-11e4-a52e-4f735466cecf=f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4D91F4C29B8B6BE52094BF844F1882D3.e.135667}], script=var items = [];var result = {items: null, url: null};var a = arguments[0];while (a != document) {  var child = a;  var i=0; while(child=child.previousElementSibling) i++;  var node = {tag:null,id:null,index:null,classes:[],other:{},innerText:""};  if (a.tagName !== undefined) {    node.tag = a.tagName.toLowerCase();  }  node.id = a.id;  node.index = i;  node.innerText = a.innerText || "";  if (a.hasAttribute("class")) {	  node.classes = a.attributes.class.value.split(' ');  }  for (index = 0; index < a.attributes.length; ++index) {      var attrName = a.attributes[index].name;      if (["id","class"].indexOf(attrName) == -1){		    node.other[attrName] = a.attributes[index].value;      }  };  items.unshift(node);  a = a.parentNode;}result.items = items;result.url = window.location.href;return JSON.stringify(result);}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:38909}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.FI...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: cd21dbe29cc6, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: a4d254052d07b789c26942856a805ce5. Exception: org.openqa.selenium.StaleElementReferenceException: stale element reference: stale element not found\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [a4d254052d07b789c26942856a805ce5, executeScript {args=[{element-6066-11e4-a52e-4f735466cecf=f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4D91F4C29B8B6BE52094BF844F1882D3.e.135667}], script=var items = [];var result = {items: null, url: null};var a = arguments[0];while (a != document) {  var child = a;  var i=0; while(child=child.previousElementSibling) i++;  var node = {tag:null,id:null,index:null,classes:[],other:{},innerText:""};  if (a.tagName !== undefined) {    node.tag = a.tagName.toLowerCase();  }  node.id = a.id;  node.index = i;  node.innerText = a.innerText || "";  if (a.hasAttribute("class")) {	  node.classes = a.attributes.class.value.split(' ');  }  for (index = 0; index < a.attributes.length; ++index) {      var attrName = a.attributes[index].name;      if (["id","class"].indexOf(attrName) == -1){		    node.other[attrName] = a.attributes[index].value;      }  };  items.unshift(node);  a = a.parentNode;}result.items = items;result.url = window.location.href;return JSON.stringify(result);}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:38909}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.FI...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: cd21dbe29cc6, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: a4d254052d07b789c26942856a805ce5 
2025-08-03 06:39:44.877  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4D91F4C29B8B6BE52094BF844F1882D3.e.135667"}} 
2025-08-03 06:39:44.944  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#pageLoginForm\\:login_business\\:UserName",\n   "using": "css selector"\n } 
2025-08-03 06:39:45.060 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #pageLoginForm\:login_business\:UserName, Command: findElements, URL: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-03 06:39:45.094  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.4B9D17B7EE0834611B3451444A521A91.e.135677"}]} 
2025-08-03 06:39:45.276  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:45.539  WARN 1 - [ttp-epoll-2] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-03 06:39:45.543  WARN 1 - [ttp-epoll-2] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [a4d254052d07b789c26942856a805ce5, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:38909}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.FI...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: cd21dbe29cc6, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: a4d254052d07b789c26942856a805ce5 
2025-08-03 06:39:45.584 DEBUG 1 - [ttp-epoll-2] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-03 06:39:45.689  WARN 1 - [ttp-epoll-2] healenium                        : Trying to heal... 
2025-08-03 06:39:45.691  WARN 1 - [ttp-epoll-2] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-03 06:39:45.698  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fspan[contains(text(),'Back to login')]"\n } 
2025-08-03 06:39:45.751  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[]} 
2025-08-03 06:39:45.856  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:45.917  WARN 1 - [ttp-epoll-2] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-03 06:39:45.920  WARN 1 - [ttp-epoll-2] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [a4d254052d07b789c26942856a805ce5, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:38909}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.FI...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: cd21dbe29cc6, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: a4d254052d07b789c26942856a805ce5 
2025-08-03 06:39:45.962 DEBUG 1 - [ttp-epoll-2] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-03 06:39:46.096  WARN 1 - [ttp-epoll-2] healenium                        : Trying to heal... 
2025-08-03 06:39:46.108  WARN 1 - [ttp-epoll-2] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-03 06:39:46.129  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "css selector",\n   "value": ".fa-street-view"\n } 
2025-08-03 06:39:46.184  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[]} 
2025-08-03 06:39:46.192  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:46.302  WARN 1 - [ttp-epoll-2] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-03 06:39:46.303  WARN 1 - [ttp-epoll-2] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [a4d254052d07b789c26942856a805ce5, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:38909}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.FI...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: cd21dbe29cc6, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: a4d254052d07b789c26942856a805ce5 
2025-08-03 06:39:46.339 DEBUG 1 - [ttp-epoll-2] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-03 06:39:46.394  WARN 1 - [ttp-epoll-2] healenium                        : Trying to heal... 
2025-08-03 06:39:46.396  WARN 1 - [ttp-epoll-2] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-03 06:39:46.402  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#pageLoginForm\\:login_business\\:UserName",\n   "using": "css selector"\n } 
2025-08-03 06:39:46.498 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #pageLoginForm\:login_business\:UserName, Command: findElements, URL: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-03 06:39:46.549  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.AD0ADBCF601EF4DA9AA1D79ABEA3B5AA.e.135700"}]} 
2025-08-03 06:39:46.792  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:46.882  WARN 1 - [ttp-epoll-2] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-03 06:39:46.883  WARN 1 - [ttp-epoll-2] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [a4d254052d07b789c26942856a805ce5, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:38909}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.FI...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: cd21dbe29cc6, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: a4d254052d07b789c26942856a805ce5 
2025-08-03 06:39:46.949 DEBUG 1 - [ttp-epoll-2] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-03 06:39:47.008  WARN 1 - [ttp-epoll-2] healenium                        : Trying to heal... 
2025-08-03 06:39:47.017  WARN 1 - [ttp-epoll-2] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-03 06:39:47.030  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#pageLoginForm\\:login_business\\:Password",\n   "using": "css selector"\n } 
2025-08-03 06:39:47.172 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #pageLoginForm\:login_business\:Password, Command: findElements, URL: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-03 06:39:47.198  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.AD0ADBCF601EF4DA9AA1D79ABEA3B5AA.e.135701"}]} 
2025-08-03 06:39:47.400  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:47.496  WARN 1 - [ttp-epoll-2] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-03 06:39:47.497  WARN 1 - [ttp-epoll-2] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [a4d254052d07b789c26942856a805ce5, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:38909}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.FI...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: cd21dbe29cc6, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: a4d254052d07b789c26942856a805ce5 
2025-08-03 06:39:47.547 DEBUG 1 - [ttp-epoll-2] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-03 06:39:47.624  WARN 1 - [ttp-epoll-2] healenium                        : Trying to heal... 
2025-08-03 06:39:47.633  WARN 1 - [ttp-epoll-2] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-03 06:39:47.647  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#pageLoginForm\\:login_business\\:UserName",\n   "using": "css selector"\n } 
2025-08-03 06:39:47.723 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #pageLoginForm\:login_business\:UserName, Command: findElements, URL: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-03 06:39:47.740  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.AD0ADBCF601EF4DA9AA1D79ABEA3B5AA.e.135700"}]} 
2025-08-03 06:39:47.886  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:47.937  WARN 1 - [ttp-epoll-2] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-03 06:39:47.938  WARN 1 - [ttp-epoll-2] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [a4d254052d07b789c26942856a805ce5, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:38909}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.FI...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: cd21dbe29cc6, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: a4d254052d07b789c26942856a805ce5 
2025-08-03 06:39:47.954 DEBUG 1 - [ttp-epoll-2] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-03 06:39:47.997  WARN 1 - [ttp-epoll-2] healenium                        : Trying to heal... 
2025-08-03 06:39:47.999  WARN 1 - [ttp-epoll-2] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-03 06:39:48.017  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#pageLoginForm\\:login_business\\:UserName",\n   "using": "css selector"\n } 
2025-08-03 06:39:48.108 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #pageLoginForm\:login_business\:UserName, Command: findElements, URL: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-03 06:39:48.169  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.AD0ADBCF601EF4DA9AA1D79ABEA3B5AA.e.135700"}]} 
2025-08-03 06:39:48.368  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:48.413  WARN 1 - [ttp-epoll-2] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-03 06:39:48.414  WARN 1 - [ttp-epoll-2] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [a4d254052d07b789c26942856a805ce5, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:38909}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.FI...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: cd21dbe29cc6, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: a4d254052d07b789c26942856a805ce5 
2025-08-03 06:39:48.445 DEBUG 1 - [ttp-epoll-2] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-03 06:39:48.484  WARN 1 - [ttp-epoll-2] healenium                        : Trying to heal... 
2025-08-03 06:39:48.486  WARN 1 - [ttp-epoll-2] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-03 06:39:48.493  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#pageLoginForm\\:login_business\\:Password",\n   "using": "css selector"\n } 
2025-08-03 06:39:48.572 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #pageLoginForm\:login_business\:Password, Command: findElements, URL: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-03 06:39:48.603  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.AD0ADBCF601EF4DA9AA1D79ABEA3B5AA.e.135701"}]} 
2025-08-03 06:39:48.702  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:48.761  WARN 1 - [ttp-epoll-2] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-03 06:39:48.762  WARN 1 - [ttp-epoll-2] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [a4d254052d07b789c26942856a805ce5, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:38909}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.FI...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: cd21dbe29cc6, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: a4d254052d07b789c26942856a805ce5 
2025-08-03 06:39:48.797 DEBUG 1 - [ttp-epoll-2] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-03 06:39:48.832  WARN 1 - [ttp-epoll-2] healenium                        : Trying to heal... 
2025-08-03 06:39:48.838  WARN 1 - [ttp-epoll-2] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-03 06:39:48.851  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#pageLoginForm\\:login_business\\:Password",\n   "using": "css selector"\n } 
2025-08-03 06:39:48.926 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #pageLoginForm\:login_business\:Password, Command: findElements, URL: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-03 06:39:48.939  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.AD0ADBCF601EF4DA9AA1D79ABEA3B5AA.e.135701"}]} 
2025-08-03 06:39:49.165  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:49.221  WARN 1 - [ttp-epoll-2] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-03 06:39:49.223  WARN 1 - [ttp-epoll-2] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [a4d254052d07b789c26942856a805ce5, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:38909}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.FI...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: cd21dbe29cc6, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: a4d254052d07b789c26942856a805ce5 
2025-08-03 06:39:49.269 DEBUG 1 - [ttp-epoll-2] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-03 06:39:49.330  WARN 1 - [ttp-epoll-2] healenium                        : Trying to heal... 
2025-08-03 06:39:49.334  WARN 1 - [ttp-epoll-2] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-03 06:39:49.340  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#pageLoginForm\\:login_business\\:goButton",\n   "using": "css selector"\n } 
2025-08-03 06:39:49.407 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #pageLoginForm\:login_business\:goButton, Command: findElements, URL: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-03 06:39:49.419  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.AD0ADBCF601EF4DA9AA1D79ABEA3B5AA.e.135714"}]} 
2025-08-03 06:39:51.483  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='ui-growl-title']"\n } 
2025-08-03 06:39:52.506 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='ui-growl-title'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml 
2025-08-03 06:39:52.534  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.8EB07CA93DDBC3FE1166911E85BB50BB.e.135838"}]} 
2025-08-03 06:39:52.540  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:52.638 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml 
2025-08-03 06:39:52.664  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.8EB07CA93DDBC3FE1166911E85BB50BB.e.135839"}} 
2025-08-03 06:39:52.734  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='ui-growl-title']"\n } 
2025-08-03 06:39:52.812 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='ui-growl-title'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml 
2025-08-03 06:39:52.835  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.8EB07CA93DDBC3FE1166911E85BB50BB.e.135838"}]} 
2025-08-03 06:39:52.903  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 06:39:53.000 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_HomeListManager, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml 
2025-08-03 06:39:53.017  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.8EB07CA93DDBC3FE1166911E85BB50BB.e.135840"}} 
2025-08-03 06:39:53.132  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:53.199 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml 
2025-08-03 06:39:53.222  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.8EB07CA93DDBC3FE1166911E85BB50BB.e.135839"}} 
2025-08-03 06:39:53.312  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-03 06:39:53.393 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_HomeListManager, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml 
2025-08-03 06:39:53.407  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.8EB07CA93DDBC3FE1166911E85BB50BB.e.135840"}]} 
2025-08-03 06:39:54.599  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:54.676 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 06:39:54.687  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.76F290251A05F6D43A7A26E9B71F1824.e.135936"}} 
2025-08-03 06:39:54.732  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fform[@id='topbar-right']\u002ful\u002flabel"\n } 
2025-08-03 06:39:54.810 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //form[@id='topbar-right']/ul/label, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 06:39:54.824  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.76F290251A05F6D43A7A26E9B71F1824.e.135875"}]} 
2025-08-03 06:39:54.878  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fa[contains(text(),'List Explorer')]"\n } 
2025-08-03 06:39:54.943 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //a[contains(text(),'List Explorer')], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 06:39:54.962  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.76F290251A05F6D43A7A26E9B71F1824.e.135937"}} 
2025-08-03 06:39:55.087  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:55.175 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 06:39:55.189  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.76F290251A05F6D43A7A26E9B71F1824.e.135936"}} 
2025-08-03 06:39:55.235  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fa[contains(text(),'List Explorer')]"\n } 
2025-08-03 06:39:55.328 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //a[contains(text(),'List Explorer')], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 06:39:55.346  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.76F290251A05F6D43A7A26E9B71F1824.e.135937"}]} 
2025-08-03 06:39:55.696  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:tab_new_list_explorer\\:zoneCbx",\n   "using": "css selector"\n } 
2025-08-03 06:39:55.773  WARN 1 - [ttp-epoll-2] healenium                        : Failed to find an element using locator By.cssSelector: #listManagerForm\:homepage_business\:tabViewListManager\:tab_new_list_explorer\:zoneCbx 
2025-08-03 06:39:55.774  WARN 1 - [ttp-epoll-2] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#listManagerForm\:homepage_business\:tabViewListManager\:tab_new_list_explorer\:zoneCbx"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [a4d254052d07b789c26942856a805ce5, findElement {value=#listManagerForm\:homepage_business\:tabViewListManager\:tab_new_list_explorer\:zoneCbx, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:38909}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.FI...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: cd21dbe29cc6, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: a4d254052d07b789c26942856a805ce5 
2025-08-03 06:39:55.816 DEBUG 1 - [ttp-epoll-2] healenium                        : [Get Reference Elements] Request. Locator: #listManagerForm\:homepage_business\:tabViewListManager\:tab_new_list_explorer\:zoneCbx, Command: findElement, Url: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 06:39:56.184  WARN 1 - [ttp-epoll-2] healenium                        : Trying to heal... 
2025-08-03 06:39:57.344  WARN 1 - [ttp-epoll-2] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [a4d254052d07b789c26942856a805ce5, findElements {value=div#listManagerForm:homepage_business:tabViewListManager:tab_new_list_explorer:zoneCbx, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:38909}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.FI...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: cd21dbe29cc6, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: a4d254052d07b789c26942856a805ce5\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-03 06:39:57.386  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:tab_new_list_explorer\\:zoneCbx",\n   "using": "css selector"\n } 
2025-08-03 06:39:57.581 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:tab_new_list_explorer\:zoneCbx, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 06:39:57.699  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.76F290251A05F6D43A7A26E9B71F1824.e.135976"}} 
2025-08-03 06:39:57.816  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:39:57.903 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 06:39:57.926  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.76F290251A05F6D43A7A26E9B71F1824.e.135936"}} 
2025-08-03 06:39:57.991  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fbutton[.='Reset']"\n } 
2025-08-03 06:39:58.367 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //button[.='Reset'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 06:39:58.548  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.76F290251A05F6D43A7A26E9B71F1824.e.135904"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.76F290251A05F6D43A7A26E9B71F1824.e.135977"}]} 
2025-08-03 06:40:00.831  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:40:00.898 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 06:40:00.921  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.76F290251A05F6D43A7A26E9B71F1824.e.135936"}} 
2025-08-03 06:40:00.965  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:tab_new_list_explorer\\:zoneCbx",\n   "using": "css selector"\n } 
2025-08-03 06:40:01.154 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:tab_new_list_explorer\:zoneCbx, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 06:40:01.230  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.76F290251A05F6D43A7A26E9B71F1824.e.136018"}]} 
2025-08-03 06:40:02.486  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role='option']"\n } 
2025-08-03 06:40:02.623 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role='option'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 06:40:02.660  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.76F290251A05F6D43A7A26E9B71F1824.e.136020"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.76F290251A05F6D43A7A26E9B71F1824.e.136021"}]} 
2025-08-03 06:40:02.675  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Common Zone 02']"\n } 
2025-08-03 06:40:02.790 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='Common Zone 02'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 06:40:02.815  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.76F290251A05F6D43A7A26E9B71F1824.e.136021"}]} 
2025-08-03 06:40:02.832  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:40:02.915 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 06:40:02.937  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.76F290251A05F6D43A7A26E9B71F1824.e.135936"}} 
2025-08-03 06:40:03.024  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Common Zone 02']"\n } 
2025-08-03 06:40:03.122 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='Common Zone 02'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 06:40:03.148  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.76F290251A05F6D43A7A26E9B71F1824.e.136021"}]} 
2025-08-03 06:40:03.179  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Common Zone 02']"\n } 
2025-08-03 06:40:03.306 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='Common Zone 02'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 06:40:03.330  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.76F290251A05F6D43A7A26E9B71F1824.e.136021"}} 
2025-08-03 06:40:03.684  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:40:03.797 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 06:40:03.846  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.76F290251A05F6D43A7A26E9B71F1824.e.135936"}} 
2025-08-03 06:40:04.051  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:tab_new_list_explorer\\:btnLESearch",\n   "using": "css selector"\n } 
2025-08-03 06:40:04.212 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:tab_new_list_explorer\:btnLESearch, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-03 06:40:04.316  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.76F290251A05F6D43A7A26E9B71F1824.e.136058"}]} 
2025-08-03 06:40:05.497  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:40:05.626 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:40:05.666  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.F8D385647A873FDC6B89D96E06B9747B.e.136182"}} 
2025-08-03 06:40:05.740  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:40:05.833 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:40:05.859  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.F8D385647A873FDC6B89D96E06B9747B.e.136182"}} 
2025-08-03 06:40:05.914  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:tab_new_list_explorer\\:_tblResults\\:leExportTypeCbx_label",\n   "using": "css selector"\n } 
2025-08-03 06:40:06.103 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:tab_new_list_explorer\:_tblResults\:leExportTypeCbx_label, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:40:06.188  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.F8D385647A873FDC6B89D96E06B9747B.e.136127"}]} 
2025-08-03 06:40:07.541  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role='option']"\n } 
2025-08-03 06:40:07.684 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role='option'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:40:07.707  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.F8D385647A873FDC6B89D96E06B9747B.e.136184"},{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.F8D385647A873FDC6B89D96E06B9747B.e.136185"}]} 
2025-08-03 06:40:07.712  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='XML']"\n } 
2025-08-03 06:40:07.817 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='XML'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:40:07.839  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.F8D385647A873FDC6B89D96E06B9747B.e.136184"}]} 
2025-08-03 06:40:07.843  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:40:07.914 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:40:07.950  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.F8D385647A873FDC6B89D96E06B9747B.e.136182"}} 
2025-08-03 06:40:07.984  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='XML']"\n } 
2025-08-03 06:40:08.082 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='XML'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:40:08.106  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.F8D385647A873FDC6B89D96E06B9747B.e.136184"}]} 
2025-08-03 06:40:08.152  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='XML']"\n } 
2025-08-03 06:40:08.258 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='XML'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:40:08.293  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.F8D385647A873FDC6B89D96E06B9747B.e.136184"}} 
2025-08-03 06:40:08.568  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:40:08.707 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:40:08.731  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.F8D385647A873FDC6B89D96E06B9747B.e.136182"}} 
2025-08-03 06:40:08.806  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:tab_new_list_explorer\\:_tblResults\\:_btnLEExport",\n   "using": "css selector"\n } 
2025-08-03 06:40:09.033 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:tab_new_list_explorer\:_tblResults\:_btnLEExport, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:40:09.139  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.F8D385647A873FDC6B89D96E06B9747B.e.136151"}]} 
2025-08-03 06:40:40.339  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:40:40.491 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:40:40.523  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.012655B1C9315F5F891B349C83A51CF3.e.136312"}} 
2025-08-03 06:40:40.632  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@title='Exit']"\n } 
2025-08-03 06:40:40.696 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@title='Exit'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:40:40.711  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.012655B1C9315F5F891B349C83A51CF3.e.136313"}]} 
2025-08-03 06:40:40.755  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:40:40.864 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:40:40.879  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.012655B1C9315F5F891B349C83A51CF3.e.136312"}} 
2025-08-03 06:40:40.936  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@title='Exit' or @title ='Quitter']"\n } 
2025-08-03 06:40:41.024 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@title='Exit' or @title ='Quitter'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml 
2025-08-03 06:40:41.056  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.012655B1C9315F5F891B349C83A51CF3.e.136313"}]} 
2025-08-03 06:40:41.175  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:40:41.285 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml# 
2025-08-03 06:40:41.322  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.012655B1C9315F5F891B349C83A51CF3.e.136312"}} 
2025-08-03 06:40:41.400  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fbutton[.='Yes']"\n } 
2025-08-03 06:40:41.482 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.xpath, Locator: //button[.='Yes'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml# 
2025-08-03 06:40:41.509  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.012655B1C9315F5F891B349C83A51CF3.e.136293"}]} 
2025-08-03 06:40:41.561  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:40:41.998  WARN 1 - [ttp-epoll-2] healenium                        : [Save Elements] Error during save elements: [f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.012655B1C9315F5F891B349C83A51CF3.e.136312]. Message: stale element reference: stale element not found\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [a4d254052d07b789c26942856a805ce5, executeScript {args=[{element-6066-11e4-a52e-4f735466cecf=f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.012655B1C9315F5F891B349C83A51CF3.e.136312}], script=var items = [];var result = {items: null, url: null};var a = arguments[0];while (a != document) {  var child = a;  var i=0; while(child=child.previousElementSibling) i++;  var node = {tag:null,id:null,index:null,classes:[],other:{},innerText:""};  if (a.tagName !== undefined) {    node.tag = a.tagName.toLowerCase();  }  node.id = a.id;  node.index = i;  node.innerText = a.innerText || "";  if (a.hasAttribute("class")) {	  node.classes = a.attributes.class.value.split(' ');  }  for (index = 0; index < a.attributes.length; ++index) {      var attrName = a.attributes[index].name;      if (["id","class"].indexOf(attrName) == -1){		    node.other[attrName] = a.attributes[index].value;      }  };  items.unshift(node);  a = a.parentNode;}result.items = items;result.url = window.location.href;return JSON.stringify(result);}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:38909}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.FI...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: cd21dbe29cc6, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: a4d254052d07b789c26942856a805ce5. Exception: org.openqa.selenium.StaleElementReferenceException: stale element reference: stale element not found\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [a4d254052d07b789c26942856a805ce5, executeScript {args=[{element-6066-11e4-a52e-4f735466cecf=f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.012655B1C9315F5F891B349C83A51CF3.e.136312}], script=var items = [];var result = {items: null, url: null};var a = arguments[0];while (a != document) {  var child = a;  var i=0; while(child=child.previousElementSibling) i++;  var node = {tag:null,id:null,index:null,classes:[],other:{},innerText:""};  if (a.tagName !== undefined) {    node.tag = a.tagName.toLowerCase();  }  node.id = a.id;  node.index = i;  node.innerText = a.innerText || "";  if (a.hasAttribute("class")) {	  node.classes = a.attributes.class.value.split(' ');  }  for (index = 0; index < a.attributes.length; ++index) {      var attrName = a.attributes[index].name;      if (["id","class"].indexOf(attrName) == -1){		    node.other[attrName] = a.attributes[index].value;      }  };  items.unshift(node);  a = a.parentNode;}result.items = items;result.url = window.location.href;return JSON.stringify(result);}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:38909}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.FI...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: cd21dbe29cc6, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: a4d254052d07b789c26942856a805ce5 
2025-08-03 06:40:41.999  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.012655B1C9315F5F891B349C83A51CF3.e.136312"}} 
2025-08-03 06:40:42.078  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#pageLoginForm\\:login_business\\:UserName",\n   "using": "css selector"\n } 
2025-08-03 06:40:42.219 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #pageLoginForm\:login_business\:UserName, Command: findElements, URL: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-03 06:40:42.242  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.3816AAADCDCAD35CAE31B2AFF710750A.e.136321"}]} 
2025-08-03 06:40:42.607  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:40:42.673  WARN 1 - [ttp-epoll-2] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-03 06:40:42.673  WARN 1 - [ttp-epoll-2] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [a4d254052d07b789c26942856a805ce5, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:38909}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.FI...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: cd21dbe29cc6, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: a4d254052d07b789c26942856a805ce5 
2025-08-03 06:40:42.691 DEBUG 1 - [ttp-epoll-2] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-03 06:40:42.735  WARN 1 - [ttp-epoll-2] healenium                        : Trying to heal... 
2025-08-03 06:40:42.739  WARN 1 - [ttp-epoll-2] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-03 06:40:42.745  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fspan[contains(text(),'Back to login')]"\n } 
2025-08-03 06:40:42.786  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[]} 
2025-08-03 06:40:42.822  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:40:42.883  WARN 1 - [ttp-epoll-2] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-03 06:40:42.886  WARN 1 - [ttp-epoll-2] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [a4d254052d07b789c26942856a805ce5, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:38909}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.FI...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: cd21dbe29cc6, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: a4d254052d07b789c26942856a805ce5 
2025-08-03 06:40:42.930 DEBUG 1 - [ttp-epoll-2] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-03 06:40:42.982  WARN 1 - [ttp-epoll-2] healenium                        : Trying to heal... 
2025-08-03 06:40:42.986  WARN 1 - [ttp-epoll-2] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-03 06:40:42.995  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "css selector",\n   "value": ".fa-street-view"\n } 
2025-08-03 06:40:43.027  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[]} 
2025-08-03 06:40:43.035  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:40:43.144  WARN 1 - [ttp-epoll-2] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-03 06:40:43.144  WARN 1 - [ttp-epoll-2] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [a4d254052d07b789c26942856a805ce5, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:38909}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.FI...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: cd21dbe29cc6, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: a4d254052d07b789c26942856a805ce5 
2025-08-03 06:40:43.183 DEBUG 1 - [ttp-epoll-2] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-03 06:40:43.247  WARN 1 - [ttp-epoll-2] healenium                        : Trying to heal... 
2025-08-03 06:40:43.253  WARN 1 - [ttp-epoll-2] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-03 06:40:43.267  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#pageLoginForm\\:login_business\\:UserName",\n   "using": "css selector"\n } 
2025-08-03 06:40:43.346 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #pageLoginForm\:login_business\:UserName, Command: findElements, URL: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-03 06:40:43.367  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.25E4F6DCF8F374E23FE8FC7DF16FA844.e.136344"}]} 
2025-08-03 06:40:43.544  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:40:43.646  WARN 1 - [ttp-epoll-2] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-03 06:40:43.647  WARN 1 - [ttp-epoll-2] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [a4d254052d07b789c26942856a805ce5, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:38909}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.FI...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: cd21dbe29cc6, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: a4d254052d07b789c26942856a805ce5 
2025-08-03 06:40:43.677 DEBUG 1 - [ttp-epoll-2] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-03 06:40:43.739  WARN 1 - [ttp-epoll-2] healenium                        : Trying to heal... 
2025-08-03 06:40:43.742  WARN 1 - [ttp-epoll-2] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-03 06:40:43.748  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#pageLoginForm\\:login_business\\:Password",\n   "using": "css selector"\n } 
2025-08-03 06:40:43.867 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #pageLoginForm\:login_business\:Password, Command: findElements, URL: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-03 06:40:43.884  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.25E4F6DCF8F374E23FE8FC7DF16FA844.e.136345"}]} 
2025-08-03 06:40:44.028  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:40:44.091  WARN 1 - [ttp-epoll-2] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-03 06:40:44.092  WARN 1 - [ttp-epoll-2] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [a4d254052d07b789c26942856a805ce5, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:38909}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.FI...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: cd21dbe29cc6, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: a4d254052d07b789c26942856a805ce5 
2025-08-03 06:40:44.116 DEBUG 1 - [ttp-epoll-2] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-03 06:40:44.171  WARN 1 - [ttp-epoll-2] healenium                        : Trying to heal... 
2025-08-03 06:40:44.173  WARN 1 - [ttp-epoll-2] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-03 06:40:44.182  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#pageLoginForm\\:login_business\\:UserName",\n   "using": "css selector"\n } 
2025-08-03 06:40:44.248 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #pageLoginForm\:login_business\:UserName, Command: findElements, URL: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-03 06:40:44.264  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.25E4F6DCF8F374E23FE8FC7DF16FA844.e.136344"}]} 
2025-08-03 06:40:44.368  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:40:44.437  WARN 1 - [ttp-epoll-2] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-03 06:40:44.438  WARN 1 - [ttp-epoll-2] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [a4d254052d07b789c26942856a805ce5, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:38909}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.FI...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: cd21dbe29cc6, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: a4d254052d07b789c26942856a805ce5 
2025-08-03 06:40:44.461 DEBUG 1 - [ttp-epoll-2] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-03 06:40:44.540  WARN 1 - [ttp-epoll-2] healenium                        : Trying to heal... 
2025-08-03 06:40:44.547  WARN 1 - [ttp-epoll-2] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-03 06:40:44.561  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#pageLoginForm\\:login_business\\:UserName",\n   "using": "css selector"\n } 
2025-08-03 06:40:44.646 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #pageLoginForm\:login_business\:UserName, Command: findElements, URL: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-03 06:40:44.681  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.25E4F6DCF8F374E23FE8FC7DF16FA844.e.136344"}]} 
2025-08-03 06:40:44.878  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:40:44.933  WARN 1 - [ttp-epoll-2] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-03 06:40:44.933  WARN 1 - [ttp-epoll-2] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [a4d254052d07b789c26942856a805ce5, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:38909}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.FI...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: cd21dbe29cc6, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: a4d254052d07b789c26942856a805ce5 
2025-08-03 06:40:44.954 DEBUG 1 - [ttp-epoll-2] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-03 06:40:44.998  WARN 1 - [ttp-epoll-2] healenium                        : Trying to heal... 
2025-08-03 06:40:45.001  WARN 1 - [ttp-epoll-2] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-03 06:40:45.021  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#pageLoginForm\\:login_business\\:Password",\n   "using": "css selector"\n } 
2025-08-03 06:40:45.094 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #pageLoginForm\:login_business\:Password, Command: findElements, URL: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-03 06:40:45.106  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.25E4F6DCF8F374E23FE8FC7DF16FA844.e.136345"}]} 
2025-08-03 06:40:45.221  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:40:45.279  WARN 1 - [ttp-epoll-2] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-03 06:40:45.280  WARN 1 - [ttp-epoll-2] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [a4d254052d07b789c26942856a805ce5, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:38909}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.FI...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: cd21dbe29cc6, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: a4d254052d07b789c26942856a805ce5 
2025-08-03 06:40:45.324 DEBUG 1 - [ttp-epoll-2] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-03 06:40:45.374  WARN 1 - [ttp-epoll-2] healenium                        : Trying to heal... 
2025-08-03 06:40:45.378  WARN 1 - [ttp-epoll-2] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-03 06:40:45.388  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#pageLoginForm\\:login_business\\:Password",\n   "using": "css selector"\n } 
2025-08-03 06:40:45.438 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #pageLoginForm\:login_business\:Password, Command: findElements, URL: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-03 06:40:45.451  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.25E4F6DCF8F374E23FE8FC7DF16FA844.e.136345"}]} 
2025-08-03 06:40:45.725  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-03 06:40:45.814  WARN 1 - [ttp-epoll-2] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-03 06:40:45.815  WARN 1 - [ttp-epoll-2] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [a4d254052d07b789c26942856a805ce5, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:38909}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.FI...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: cd21dbe29cc6, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: a4d254052d07b789c26942856a805ce5 
2025-08-03 06:40:45.850 DEBUG 1 - [ttp-epoll-2] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-03 06:40:45.924  WARN 1 - [ttp-epoll-2] healenium                        : Trying to heal... 
2025-08-03 06:40:45.927  WARN 1 - [ttp-epoll-2] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-03 06:40:45.932  INFO 1 - [ttp-epoll-2] healenium                        : Find Element Request: {\n   "value": "#pageLoginForm\\:login_business\\:goButton",\n   "using": "css selector"\n } 
2025-08-03 06:40:46.035 DEBUG 1 - [ttp-epoll-2] healenium                        : [Save Elements] By: By.cssSelector, Locator: #pageLoginForm\:login_business\:goButton, Command: findElements, URL: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-03 06:40:46.051  INFO 1 - [ttp-epoll-2] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.9A4EBD59DF653486AE865B0CCDA5ED6E.d.25E4F6DCF8F374E23FE8FC7DF16FA844.e.136358"}]} 
