2025-08-02 21:02:30.310  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.B6BBB7D6746F3CF851F37B2F205CE613.e.95526"}} 
2025-08-02 21:02:30.391  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fa[contains(text(),'Profiles-ListSet Association')]"\n } 
2025-08-02 21:02:30.572 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //a[contains(text(),'Profiles-ListSet Association')], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:02:30.624  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.B6BBB7D6746F3CF851F37B2F205CE613.e.95630"}} 
2025-08-02 21:02:30.799  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:02:30.879 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:02:30.907  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.B6BBB7D6746F3CF851F37B2F205CE613.e.95526"}} 
2025-08-02 21:02:30.979  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fa[contains(text(),'Profiles-ListSet Association')]"\n } 
2025-08-02 21:02:31.154 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //a[contains(text(),'Profiles-ListSet Association')], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:02:31.233  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.B6BBB7D6746F3CF851F37B2F205CE613.e.95630"}]} 
2025-08-02 21:02:31.298  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#listSetDetailForm\\:listSet_details\\:tabViewListSet\\:listset_tab_profiles\\:profilePickList",\n   "using": "css selector"\n } 
2025-08-02 21:02:31.361 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listSetDetailForm\:listSet_details\:tabViewListSet\:listset_tab_profiles\:profilePickList, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:02:31.375  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.B6BBB7D6746F3CF851F37B2F205CE613.e.95632"}} 
2025-08-02 21:02:31.515  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:02:31.579 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:02:31.600  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.B6BBB7D6746F3CF851F37B2F205CE613.e.95526"}} 
2025-08-02 21:02:31.647  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#listSetDetailForm\\:listSet_details\\:tabViewListSet\\:listset_tab_profiles\\:profilePickList_source_filter",\n   "using": "css selector"\n } 
2025-08-02 21:02:31.760 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listSetDetailForm\:listSet_details\:tabViewListSet\:listset_tab_profiles\:profilePickList_source_filter, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:02:31.779  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.B6BBB7D6746F3CF851F37B2F205CE613.e.95541"}]} 
2025-08-02 21:02:31.910  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:02:31.985 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:02:32.006  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.B6BBB7D6746F3CF851F37B2F205CE613.e.95526"}} 
2025-08-02 21:02:32.057  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#listSetDetailForm\\:listSet_details\\:tabViewListSet\\:listset_tab_profiles\\:profilePickList_source_filter",\n   "using": "css selector"\n } 
2025-08-02 21:02:32.123 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listSetDetailForm\:listSet_details\:tabViewListSet\:listset_tab_profiles\:profilePickList_source_filter, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:02:32.140  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.B6BBB7D6746F3CF851F37B2F205CE613.e.95541"}]} 
2025-08-02 21:02:35.528  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:02:35.593 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:02:35.621  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.B6BBB7D6746F3CF851F37B2F205CE613.e.95526"}} 
2025-08-02 21:02:35.688  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@title='Add All']"\n } 
2025-08-02 21:02:35.761 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@title='Add All'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:02:35.782  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.B6BBB7D6746F3CF851F37B2F205CE613.e.95592"}]} 
2025-08-02 21:02:38.823  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:02:38.903 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:02:38.922  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.B6BBB7D6746F3CF851F37B2F205CE613.e.95526"}} 
2025-08-02 21:02:38.977  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#listSetDetailForm\\:listSet_details\\:btnSave",\n   "using": "css selector"\n } 
2025-08-02 21:02:39.024 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listSetDetailForm\:listSet_details\:btnSave, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:02:39.048  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.B6BBB7D6746F3CF851F37B2F205CE613.e.95608"}]} 
2025-08-02 21:02:39.247  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='ui-growl-message']"\n } 
2025-08-02 21:02:39.360  WARN 1 - [ttp-epoll-4] healenium                        : Failed to find an element using locator By.xpath: //*[@class='ui-growl-message'] 
2025-08-02 21:02:39.361  WARN 1 - [ttp-epoll-4] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='ui-growl-message']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [ee1a60c61db2d2f29d3cafef88c043a1, findElement {value=//*[@class='ui-growl-message'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39131}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.G4...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: fdd5724a7923, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: ee1a60c61db2d2f29d3cafef88c043a1 
2025-08-02 21:02:39.398 DEBUG 1 - [ttp-epoll-4] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='ui-growl-message'], Command: findElement, Url: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:02:39.494  WARN 1 - [ttp-epoll-4] healenium                        : Trying to heal... 
2025-08-02 21:02:41.173  WARN 1 - [ttp-epoll-4] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [ee1a60c61db2d2f29d3cafef88c043a1, findElements {value=div#topbar-right:selectedLanguage.select-width-and-height.p-m-0.field.ui-selectonemenu.ui-state-default.ui-widget.ui-corner-all.comboBox > div.ui-helper-hidden-accessible, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39131}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.G4...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: fdd5724a7923, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: ee1a60c61db2d2f29d3cafef88c043a1\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-02 21:02:41.196  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='ui-growl-message']"\n } 
2025-08-02 21:02:41.256 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='ui-growl-message'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:02:41.280  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.B6BBB7D6746F3CF851F37B2F205CE613.e.96031"}} 
2025-08-02 21:02:41.320  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:02:41.383 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:02:41.400  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.B6BBB7D6746F3CF851F37B2F205CE613.e.96042"}} 
2025-08-02 21:02:41.451  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='ui-growl-title']"\n } 
2025-08-02 21:02:41.507 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='ui-growl-title'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:02:41.527  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.B6BBB7D6746F3CF851F37B2F205CE613.e.96043"}]} 
2025-08-02 21:02:42.278  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeFormatManager",\n   "using": "css selector"\n } 
2025-08-02 21:02:42.387 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_HomeFormatManager, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:02:42.410  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.17D5BBF53C7930DF5BE63EB98B05E367.e.96146"}} 
2025-08-02 21:02:42.517  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:02:42.561 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:02:42.575  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.17D5BBF53C7930DF5BE63EB98B05E367.e.96147"}} 
2025-08-02 21:02:42.642  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeFormatManager",\n   "using": "css selector"\n } 
2025-08-02 21:02:42.733 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_HomeFormatManager, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:02:42.750  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.17D5BBF53C7930DF5BE63EB98B05E367.e.96146"}]} 
2025-08-02 21:02:43.516  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:02:43.581 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:43.605  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:02:43.719  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fform[@id='topbar-right']\u002ful\u002flabel"\n } 
2025-08-02 21:02:43.781 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //form[@id='topbar-right']/ul/label, Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:43.795  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96181"}]} 
2025-08-02 21:02:43.844  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:02:43.915 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:43.940  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:02:43.996  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#FormatManagerForm\\:Homepage_business\\:zoneCbx",\n   "using": "css selector"\n } 
2025-08-02 21:02:44.071 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #FormatManagerForm\:Homepage_business\:zoneCbx, Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:44.110  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96239"}]} 
2025-08-02 21:02:45.448  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role='option']"\n } 
2025-08-02 21:02:45.544 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role='option'], Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:46.826  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96249"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96250"}]} 
2025-08-02 21:02:46.831  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Common Zone 04']"\n } 
2025-08-02 21:02:46.875  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[]} 
2025-08-02 21:02:46.880  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:02:46.950 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:46.963  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:02:47.014  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#FormatManagerForm\\:Homepage_business\\:zoneCbx",\n   "using": "css selector"\n } 
2025-08-02 21:02:47.053 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #FormatManagerForm\:Homepage_business\:zoneCbx, Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:47.065  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96239"}]} 
2025-08-02 21:02:47.271  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:02:47.317 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:47.328  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:02:47.392  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#FormatManagerForm\\:Homepage_business\\:btnSearch",\n   "using": "css selector"\n } 
2025-08-02 21:02:47.479 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #FormatManagerForm\:Homepage_business\:btnSearch, Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:47.493  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96207"}]} 
2025-08-02 21:02:47.530  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:02:47.586 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:47.608  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:02:47.754  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#FormatManagerForm\\:Homepage_business\\:Formats\\:_btnAddFormat",\n   "using": "css selector"\n } 
2025-08-02 21:02:47.848 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #FormatManagerForm\:Homepage_business\:Formats\:_btnAddFormat, Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:47.868  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96270"}]} 
2025-08-02 21:02:47.916  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:02:48.129 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:48.141  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:02:48.211  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#FormatManagerForm\\:Homepage_business\\:FormatDetails\\:ZoneCbx",\n   "using": "css selector"\n } 
2025-08-02 21:02:48.274 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #FormatManagerForm\:Homepage_business\:FormatDetails\:ZoneCbx, Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:48.292  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96320"}]} 
2025-08-02 21:02:49.513  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role='option']"\n } 
2025-08-02 21:02:49.560 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role='option'], Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:49.573  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96323"}]} 
2025-08-02 21:02:49.577  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Common Zone 04']"\n } 
2025-08-02 21:02:49.597  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[]} 
2025-08-02 21:02:49.601  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:02:49.654 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:49.673  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:02:49.728  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#FormatManagerForm\\:Homepage_business\\:FormatDetails\\:ZoneCbx",\n   "using": "css selector"\n } 
2025-08-02 21:02:49.783 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #FormatManagerForm\:Homepage_business\:FormatDetails\:ZoneCbx, Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:49.795  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96320"}]} 
2025-08-02 21:02:50.310  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:02:50.426 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:50.446  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:02:50.501  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#FormatManagerForm\\:Homepage_business\\:FormatDetails\\:format",\n   "using": "css selector"\n } 
2025-08-02 21:02:50.602 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #FormatManagerForm\:Homepage_business\:FormatDetails\:format, Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:50.625  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96293"}]} 
2025-08-02 21:02:50.777  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:02:50.841 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:50.852  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:02:50.901  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#FormatManagerForm\\:Homepage_business\\:FormatDetails\\:format",\n   "using": "css selector"\n } 
2025-08-02 21:02:50.958 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #FormatManagerForm\:Homepage_business\:FormatDetails\:format, Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:50.970  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96293"}]} 
2025-08-02 21:02:51.063  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:02:51.130 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:51.151  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:02:51.206  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#FormatManagerForm\\:Homepage_business\\:FormatDetails\\:format",\n   "using": "css selector"\n } 
2025-08-02 21:02:51.252 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #FormatManagerForm\:Homepage_business\:FormatDetails\:format, Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:51.264  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96293"}]} 
2025-08-02 21:02:51.502  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:02:51.587 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:51.612  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:02:51.668  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#FormatManagerForm\\:Homepage_business\\:FormatDetails\\:FormatTypeCbx_label",\n   "using": "css selector"\n } 
2025-08-02 21:02:51.726 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #FormatManagerForm\:Homepage_business\:FormatDetails\:FormatTypeCbx_label, Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:51.740  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96307"}]} 
2025-08-02 21:02:52.986  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role='option']"\n } 
2025-08-02 21:02:53.108 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role='option'], Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:53.121  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96323"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96326"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96327"}]} 
2025-08-02 21:02:53.125  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Scan']"\n } 
2025-08-02 21:02:53.189 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='Scan'], Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:53.203  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96327"}]} 
2025-08-02 21:02:53.207  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:02:53.247 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:53.261  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:02:53.307  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Scan']"\n } 
2025-08-02 21:02:53.367 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='Scan'], Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:53.378  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96327"}]} 
2025-08-02 21:02:53.499  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Scan']"\n } 
2025-08-02 21:02:53.566 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='Scan'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:53.584  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96327"}} 
2025-08-02 21:02:53.812  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:02:53.997 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:54.009  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:02:54.118  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#FormatManagerForm\\:Homepage_business\\:FormatDetails\\:FieldDelimCbx_label",\n   "using": "css selector"\n } 
2025-08-02 21:02:54.204 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #FormatManagerForm\:Homepage_business\:FormatDetails\:FieldDelimCbx_label, Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:54.226  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96345"}]} 
2025-08-02 21:02:55.482  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role='option']"\n } 
2025-08-02 21:02:55.592 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role='option'], Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:55.607  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96358"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96359"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96360"}]} 
2025-08-02 21:02:55.612  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Separator']"\n } 
2025-08-02 21:02:55.677 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='Separator'], Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:55.695  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96359"}]} 
2025-08-02 21:02:55.700  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:02:55.735 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:55.746  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:02:55.789  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Separator']"\n } 
2025-08-02 21:02:55.873 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='Separator'], Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:55.886  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96359"}]} 
2025-08-02 21:02:55.931  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Separator']"\n } 
2025-08-02 21:02:55.977 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='Separator'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:55.987  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96359"}} 
2025-08-02 21:02:56.248  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:02:56.364 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:56.379  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:02:56.436  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#FormatManagerForm\\:Homepage_business\\:FormatDetails\\:fieldSeparatorValue",\n   "using": "css selector"\n } 
2025-08-02 21:02:56.508 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #FormatManagerForm\:Homepage_business\:FormatDetails\:fieldSeparatorValue, Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:56.527  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96372"}]} 
2025-08-02 21:02:56.644  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:02:56.711 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:56.725  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:02:56.767  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#FormatManagerForm\\:Homepage_business\\:FormatDetails\\:fieldSeparatorValue",\n   "using": "css selector"\n } 
2025-08-02 21:02:56.842 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #FormatManagerForm\:Homepage_business\:FormatDetails\:fieldSeparatorValue, Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:56.862  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96372"}]} 
2025-08-02 21:02:57.024  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:02:57.140 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:57.167  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:02:57.213  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#FormatManagerForm\\:Homepage_business\\:FormatDetails\\:FieldDetails\\:Fields\\:_btnAddField",\n   "using": "css selector"\n } 
2025-08-02 21:02:57.260 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #FormatManagerForm\:Homepage_business\:FormatDetails\:FieldDetails\:Fields\:_btnAddField, Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:57.281  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96392"}]} 
2025-08-02 21:02:57.337  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:02:57.560 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:57.582  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:02:57.644  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#FormatManagerForm\\:Homepage_business\\:FormatDetails\\:FieldDetails\\:fieldLabelInput",\n   "using": "css selector"\n } 
2025-08-02 21:02:57.725 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #FormatManagerForm\:Homepage_business\:FormatDetails\:FieldDetails\:fieldLabelInput, Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:57.746  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96429"}]} 
2025-08-02 21:02:57.858  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:02:57.917 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:57.938  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:02:57.984  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#FormatManagerForm\\:Homepage_business\\:FormatDetails\\:FieldDetails\\:fieldLabelInput",\n   "using": "css selector"\n } 
2025-08-02 21:02:58.057 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #FormatManagerForm\:Homepage_business\:FormatDetails\:FieldDetails\:fieldLabelInput, Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:58.078  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96429"}]} 
2025-08-02 21:02:58.440  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:02:58.664 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:58.684  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:02:58.784  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:02:58.989 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:59.005  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:02:59.147  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:02:59.266 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:59.281  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:02:59.419  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:02:59.580 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:59.607  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:02:59.744  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:02:59.823 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:59.849  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:02:59.911  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#FormatManagerForm\\:Homepage_business\\:FormatDetails\\:FieldDetails\\:fieldTypeInput_label",\n   "using": "css selector"\n } 
2025-08-02 21:02:59.973 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #FormatManagerForm\:Homepage_business\:FormatDetails\:FieldDetails\:fieldTypeInput_label, Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:02:59.990  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96445"}]} 
2025-08-02 21:03:00.056  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:00.144 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:00.156  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:03:00.210  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#FormatManagerForm\\:Homepage_business\\:FormatDetails\\:FieldDetails\\:fieldTypeInput_label",\n   "using": "css selector"\n } 
2025-08-02 21:03:00.268 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #FormatManagerForm\:Homepage_business\:FormatDetails\:FieldDetails\:fieldTypeInput_label, Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:00.284  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96445"}]} 
2025-08-02 21:03:01.571  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role='option']"\n } 
2025-08-02 21:03:02.221 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role='option'], Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:02.253  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96507"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96508"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96509"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96510"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96511"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96512"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96513"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96514"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96515"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96516"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96517"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96518"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96519"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96520"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96521"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96522"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96523"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96524"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96525"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96526"}]} 
2025-08-02 21:03:02.260  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='FIRST_NAME']"\n } 
2025-08-02 21:03:02.345 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='FIRST_NAME'], Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:02.358  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96510"}]} 
2025-08-02 21:03:02.363  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:02.459 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:02.472  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:03:02.515  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='FIRST_NAME']"\n } 
2025-08-02 21:03:02.585 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='FIRST_NAME'], Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:02.604  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96510"}]} 
2025-08-02 21:03:02.657  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='FIRST_NAME']"\n } 
2025-08-02 21:03:02.721 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='FIRST_NAME'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:02.734  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96510"}} 
2025-08-02 21:03:02.975  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:03.071 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:03.095  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:03:03.165  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#FormatManagerForm\\:Homepage_business\\:FormatDetails\\:FieldDetails\\:ScanChkBx_input",\n   "using": "css selector"\n } 
2025-08-02 21:03:03.239 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #FormatManagerForm\:Homepage_business\:FormatDetails\:FieldDetails\:ScanChkBx_input, Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:03.256  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96436"}]} 
2025-08-02 21:03:03.300  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:03.369 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:03.392  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:03:03.448  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#FormatManagerForm\\:Homepage_business\\:FormatDetails\\:FieldDetails\\:ScanChkBx",\n   "using": "css selector"\n } 
2025-08-02 21:03:03.513 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #FormatManagerForm\:Homepage_business\:FormatDetails\:FieldDetails\:ScanChkBx, Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:03.528  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96536"}]} 
2025-08-02 21:03:03.781  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:03.864 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:03.885  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:03:03.949  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#FormatManagerForm\\:Homepage_business\\:FormatDetails\\:FieldDetails\\:ContextChkBx_input",\n   "using": "css selector"\n } 
2025-08-02 21:03:04.018 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #FormatManagerForm\:Homepage_business\:FormatDetails\:FieldDetails\:ContextChkBx_input, Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:04.038  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96437"}]} 
2025-08-02 21:03:04.085  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:04.154 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:04.168  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:03:04.213  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#FormatManagerForm\\:Homepage_business\\:FormatDetails\\:FieldDetails\\:ContextChkBx",\n   "using": "css selector"\n } 
2025-08-02 21:03:04.290 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #FormatManagerForm\:Homepage_business\:FormatDetails\:FieldDetails\:ContextChkBx, Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:04.315  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96538"}]} 
2025-08-02 21:03:07.574  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:07.633 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:07.652  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:03:07.721  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#FormatManagerForm\\:Homepage_business\\:FormatDetails\\:FieldDetails\\:Fields\\:_btnAddField",\n   "using": "css selector"\n } 
2025-08-02 21:03:07.782 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #FormatManagerForm\:Homepage_business\:FormatDetails\:FieldDetails\:Fields\:_btnAddField, Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:07.795  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96540"}]} 
2025-08-02 21:03:07.837  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:08.203 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:08.230  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:03:08.285  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#FormatManagerForm\\:Homepage_business\\:FormatDetails\\:FieldDetails\\:fieldLabelInput",\n   "using": "css selector"\n } 
2025-08-02 21:03:08.355 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #FormatManagerForm\:Homepage_business\:FormatDetails\:FieldDetails\:fieldLabelInput, Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:08.400  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96577"}]} 
2025-08-02 21:03:08.507  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:08.558 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:08.570  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:03:08.622  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#FormatManagerForm\\:Homepage_business\\:FormatDetails\\:FieldDetails\\:fieldLabelInput",\n   "using": "css selector"\n } 
2025-08-02 21:03:08.724 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #FormatManagerForm\:Homepage_business\:FormatDetails\:FieldDetails\:fieldLabelInput, Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:08.744  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96577"}]} 
2025-08-02 21:03:09.078  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:09.322 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:09.336  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:03:09.491  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:09.611 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:09.648  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:03:09.817  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:10.028 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:10.048  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:03:10.187  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:10.358 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:10.371  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:03:10.425  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#FormatManagerForm\\:Homepage_business\\:FormatDetails\\:FieldDetails\\:fieldTypeInput_label",\n   "using": "css selector"\n } 
2025-08-02 21:03:10.513 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #FormatManagerForm\:Homepage_business\:FormatDetails\:FieldDetails\:fieldTypeInput_label, Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:10.528  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96594"}]} 
2025-08-02 21:03:10.564  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:10.650 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:10.662  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:03:10.711  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#FormatManagerForm\\:Homepage_business\\:FormatDetails\\:FieldDetails\\:fieldTypeInput_label",\n   "using": "css selector"\n } 
2025-08-02 21:03:10.777 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #FormatManagerForm\:Homepage_business\:FormatDetails\:FieldDetails\:fieldTypeInput_label, Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:10.797  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96594"}]} 
2025-08-02 21:03:12.050  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role='option']"\n } 
2025-08-02 21:03:12.638 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role='option'], Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:12.672  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96665"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96666"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96667"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96668"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96669"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96670"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96671"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96672"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96673"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96674"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96675"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96676"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96677"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96678"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96679"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96680"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96681"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96682"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96683"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96684"}]} 
2025-08-02 21:03:12.676  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='LAST_NAME']"\n } 
2025-08-02 21:03:12.759 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='LAST_NAME'], Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:12.773  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96667"}]} 
2025-08-02 21:03:12.777  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:12.817 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:12.829  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:03:12.885  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='LAST_NAME']"\n } 
2025-08-02 21:03:12.963 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='LAST_NAME'], Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:12.976  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96667"}]} 
2025-08-02 21:03:13.021  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='LAST_NAME']"\n } 
2025-08-02 21:03:13.079 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='LAST_NAME'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:13.097  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96667"}} 
2025-08-02 21:03:13.334  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:13.526 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:13.545  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:03:13.582  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#FormatManagerForm\\:Homepage_business\\:FormatDetails\\:FieldDetails\\:ScanChkBx_input",\n   "using": "css selector"\n } 
2025-08-02 21:03:13.669 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #FormatManagerForm\:Homepage_business\:FormatDetails\:FieldDetails\:ScanChkBx_input, Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:13.684  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96584"}]} 
2025-08-02 21:03:13.731  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:13.783 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:13.795  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:03:13.832  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#FormatManagerForm\\:Homepage_business\\:FormatDetails\\:FieldDetails\\:ScanChkBx",\n   "using": "css selector"\n } 
2025-08-02 21:03:13.896 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #FormatManagerForm\:Homepage_business\:FormatDetails\:FieldDetails\:ScanChkBx, Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:13.913  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96694"}]} 
2025-08-02 21:03:14.153  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:14.268 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:14.281  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:03:14.329  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#FormatManagerForm\\:Homepage_business\\:FormatDetails\\:FieldDetails\\:ContextChkBx_input",\n   "using": "css selector"\n } 
2025-08-02 21:03:14.403 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #FormatManagerForm\:Homepage_business\:FormatDetails\:FieldDetails\:ContextChkBx_input, Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:14.422  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96585"}]} 
2025-08-02 21:03:14.476  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:14.561 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:14.575  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:03:14.618  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#FormatManagerForm\\:Homepage_business\\:FormatDetails\\:FieldDetails\\:ContextChkBx",\n   "using": "css selector"\n } 
2025-08-02 21:03:14.695 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #FormatManagerForm\:Homepage_business\:FormatDetails\:FieldDetails\:ContextChkBx, Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:14.716  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96696"}]} 
2025-08-02 21:03:17.936  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:18.013 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:18.030  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:03:18.074  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#FormatManagerForm\\:Homepage_business\\:FormatDetails\\:btnSave",\n   "using": "css selector"\n } 
2025-08-02 21:03:18.129 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #FormatManagerForm\:Homepage_business\:FormatDetails\:btnSave, Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:18.150  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96698"}]} 
2025-08-02 21:03:18.320  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:18.390 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:18.409  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:03:18.494  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:18.639 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:18.668  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:03:18.745  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='ui-growl-title']"\n } 
2025-08-02 21:03:18.785  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[]} 
2025-08-02 21:03:18.790  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeScanManager",\n   "using": "css selector"\n } 
2025-08-02 21:03:18.837 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_HomeScanManager, Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:18.852  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96721"}} 
2025-08-02 21:03:18.955  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:19.029 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:19.044  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96248"}} 
2025-08-02 21:03:19.084  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeScanManager",\n   "using": "css selector"\n } 
2025-08-02 21:03:19.160 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_HomeScanManager, Command: findElements, URL: http://************:8080/AMLUI/Modules/webplatform/jsp/FormatManager/Homepage.xhtml?module_name=FormatManager&app_name=SFP 
2025-08-02 21:03:19.174  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.DF32D841D431088DFA5766F05180E5DB.e.96721"}]} 
2025-08-02 21:03:20.628  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:20.766 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:20.794  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96832"}} 
2025-08-02 21:03:20.842  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fform[@id='topbar-right']\u002ful\u002flabel"\n } 
2025-08-02 21:03:20.936 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //form[@id='topbar-right']/ul/label, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:20.947  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96770"}]} 
2025-08-02 21:03:21.012  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fa[contains(text(),'File Scan')]"\n } 
2025-08-02 21:03:21.093 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //a[contains(text(),'File Scan')], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:21.112  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96833"}} 
2025-08-02 21:03:21.216  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:21.258 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:21.269  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96832"}} 
2025-08-02 21:03:21.328  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fa[contains(text(),'File Scan')]"\n } 
2025-08-02 21:03:21.407 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //a[contains(text(),'File Scan')], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:21.421  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96833"}]} 
2025-08-02 21:03:21.638  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:Tab_FileScan\\:scanFile_label",\n   "using": "css selector"\n } 
2025-08-02 21:03:21.703  WARN 1 - [ttp-epoll-4] healenium                        : Failed to find an element using locator By.cssSelector: #scanManagerForm\:homepage_business\:tabViewScanManager\:Tab_FileScan\:scanFile_label 
2025-08-02 21:03:21.704  WARN 1 - [ttp-epoll-4] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#scanManagerForm\:homepage_business\:tabViewScanManager\:Tab_FileScan\:scanFile_label"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [ee1a60c61db2d2f29d3cafef88c043a1, findElement {value=#scanManagerForm\:homepage_business\:tabViewScanManager\:Tab_FileScan\:scanFile_label, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39131}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.G4...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: fdd5724a7923, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: ee1a60c61db2d2f29d3cafef88c043a1 
2025-08-02 21:03:21.739 DEBUG 1 - [ttp-epoll-4] healenium                        : [Get Reference Elements] Request. Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:Tab_FileScan\:scanFile_label, Command: findElement, Url: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:21.955  WARN 1 - [ttp-epoll-4] healenium                        : Trying to heal... 
2025-08-02 21:03:22.159  WARN 1 - [ttp-epoll-4] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [ee1a60c61db2d2f29d3cafef88c043a1, findElements {value=span#scanManagerForm:homepage_business:tabViewScanManager:Tab_FileScan:scanFile_label, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39131}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.G4...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: fdd5724a7923, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: ee1a60c61db2d2f29d3cafef88c043a1\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-02 21:03:22.175  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:Tab_FileScan\\:scanFile_label",\n   "using": "css selector"\n } 
2025-08-02 21:03:22.231 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:Tab_FileScan\:scanFile_label, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:22.253  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96857"}} 
2025-08-02 21:03:22.355  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:22.432 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:22.446  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96832"}} 
2025-08-02 21:03:22.488  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:Tab_FileScan\\:btnResetFileScan",\n   "using": "css selector"\n } 
2025-08-02 21:03:22.581 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:Tab_FileScan\:btnResetFileScan, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:22.612  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96858"}]} 
2025-08-02 21:03:22.694  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:22.823 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:22.859  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96832"}} 
2025-08-02 21:03:22.980  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:23.090 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:23.112  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96832"}} 
2025-08-02 21:03:23.176  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:Tab_FileScan\\:scanFile_input",\n   "using": "css selector"\n } 
2025-08-02 21:03:23.266 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:Tab_FileScan\:scanFile_input, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:23.304  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96865"}]} 
2025-08-02 21:03:23.444  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:23.497 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:23.510  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96832"}} 
2025-08-02 21:03:23.569  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:Tab_FileScan\\:scanFile_input",\n   "using": "css selector"\n } 
2025-08-02 21:03:23.654 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:Tab_FileScan\:scanFile_input, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:23.689  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96865"}]} 
2025-08-02 21:03:23.774  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:23.851 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:23.870  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96832"}} 
2025-08-02 21:03:23.948  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:Tab_FileScan\\:formatCbx_label",\n   "using": "css selector"\n } 
2025-08-02 21:03:24.024 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:Tab_FileScan\:formatCbx_label, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:24.065  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96875"}]} 
2025-08-02 21:03:25.396  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role='option']"\n } 
2025-08-02 21:03:30.677 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role='option'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:31.121  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96883"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96884"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96885"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96886"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96887"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96888"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96889"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96890"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96891"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96892"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96893"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96894"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96895"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96896"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96897"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96898"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96899"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96900"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96901"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96902"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96903"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96904"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96905"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96906"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96907"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96908"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96909"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96910"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96911"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96912"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96913"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96914"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96915"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96916"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96917"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96918"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96919"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96920"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96921"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96922"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96923"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96924"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96925"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96926"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96927"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96928"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96929"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96930"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96931"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96932"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96933"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96934"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96935"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96936"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96937"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96938"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96939"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96940"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96941"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96942"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96943"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96944"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96945"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96946"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96947"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96948"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96949"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96950"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96951"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96952"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96953"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96954"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96955"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96956"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96957"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96958"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96959"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96960"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96961"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96962"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96963"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96964"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96965"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96966"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96967"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96968"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96969"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96970"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96971"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96972"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96973"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96974"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96975"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96976"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96977"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96978"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96979"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96980"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96981"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96982"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96983"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96984"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96985"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96986"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96987"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96988"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96989"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96990"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96991"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96992"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96993"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96994"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96995"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96996"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96997"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96998"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96999"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97000"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97001"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97002"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97003"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97004"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97005"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97006"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97007"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97008"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97009"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97010"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97011"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97012"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97013"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97014"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97015"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97016"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97017"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97018"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97019"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97020"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97021"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97022"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97023"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97024"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97025"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97026"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97027"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97028"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97029"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97030"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97031"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97032"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97033"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97034"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97035"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97036"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97037"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97038"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97039"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97040"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97041"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97042"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97043"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97044"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97045"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97046"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97047"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97048"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97049"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97050"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97051"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97052"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97053"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97054"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97055"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97056"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97057"}]} 
2025-08-02 21:03:31.133  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='format1754157762274']"\n } 
2025-08-02 21:03:31.212 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='format1754157762274'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:31.232  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97057"}]} 
2025-08-02 21:03:31.237  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:31.327 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:31.346  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96832"}} 
2025-08-02 21:03:31.411  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='format1754157762274']"\n } 
2025-08-02 21:03:31.466 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='format1754157762274'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:31.478  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97057"}]} 
2025-08-02 21:03:31.513  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='format1754157762274']"\n } 
2025-08-02 21:03:31.600 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='format1754157762274'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:31.619  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97057"}} 
2025-08-02 21:03:31.796  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:31.908 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:31.936  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96832"}} 
2025-08-02 21:03:31.979  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:Tab_FileScan\\:vessels_input",\n   "using": "css selector"\n } 
2025-08-02 21:03:32.055 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:Tab_FileScan\:vessels_input, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:32.079  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96872"}]} 
2025-08-02 21:03:32.133  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:32.196 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:32.208  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96832"}} 
2025-08-02 21:03:32.250  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:Tab_FileScan\\:vessels",\n   "using": "css selector"\n } 
2025-08-02 21:03:32.331 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:Tab_FileScan\:vessels, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:32.363  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97058"}]} 
2025-08-02 21:03:32.604  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:32.684 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:32.697  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96832"}} 
2025-08-02 21:03:32.746  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:Tab_FileScan\\:countries_input",\n   "using": "css selector"\n } 
2025-08-02 21:03:32.848 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:Tab_FileScan\:countries_input, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:32.885  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96873"}]} 
2025-08-02 21:03:32.944  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:33.008 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:33.020  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96832"}} 
2025-08-02 21:03:33.098  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:Tab_FileScan\\:countries",\n   "using": "css selector"\n } 
2025-08-02 21:03:33.204 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:Tab_FileScan\:countries, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:33.225  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97059"}]} 
2025-08-02 21:03:33.426  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:33.475 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:33.492  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96832"}} 
2025-08-02 21:03:33.551  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:Tab_FileScan\\:autoAlert_input",\n   "using": "css selector"\n } 
2025-08-02 21:03:33.660 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:Tab_FileScan\:autoAlert_input, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:33.693  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96874"}]} 
2025-08-02 21:03:33.722  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:33.783 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:33.800  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96832"}} 
2025-08-02 21:03:33.864  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:Tab_FileScan\\:autoAlert",\n   "using": "css selector"\n } 
2025-08-02 21:03:33.963 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:Tab_FileScan\:autoAlert, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:33.985  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97060"}]} 
2025-08-02 21:03:34.205  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:34.266 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:34.293  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96832"}} 
2025-08-02 21:03:34.352  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:Tab_FileScan\\:btnFileScan",\n   "using": "css selector"\n } 
2025-08-02 21:03:34.472 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:Tab_FileScan\:btnFileScan, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:34.509  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97061"}]} 
2025-08-02 21:03:34.593  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:34.711 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:34.729  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96832"}} 
2025-08-02 21:03:34.835  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:34.939 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:34.962  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96832"}} 
2025-08-02 21:03:35.477  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='ui-growl-title']"\n } 
2025-08-02 21:03:35.591 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='ui-growl-title'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:35.610  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97085"}]} 
2025-08-02 21:03:35.617  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:35.721 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:35.738  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.96832"}} 
2025-08-02 21:03:35.793  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='ui-growl-title']"\n } 
2025-08-02 21:03:35.839 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='ui-growl-title'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:35.854  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.87B0159F34B595695E2005ABF95C2AE4.e.97085"}]} 
2025-08-02 21:03:36.514  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeScanManager",\n   "using": "css selector"\n } 
2025-08-02 21:03:37.137 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_HomeScanManager, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:37.169  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.544BDCDE119E612C787BC49145985B99.e.97089"}} 
2025-08-02 21:03:37.277  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:37.349 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:37.378  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.544BDCDE119E612C787BC49145985B99.e.97196"}} 
2025-08-02 21:03:37.430  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeScanManager",\n   "using": "css selector"\n } 
2025-08-02 21:03:37.495 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_HomeScanManager, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:37.511  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.544BDCDE119E612C787BC49145985B99.e.97089"}]} 
2025-08-02 21:03:38.620  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:38.742 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:38.761  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97306"}} 
2025-08-02 21:03:38.812  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fform[@id='topbar-right']\u002ful\u002flabel"\n } 
2025-08-02 21:03:38.869 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //form[@id='topbar-right']/ul/label, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:38.887  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97243"}]} 
2025-08-02 21:03:38.938  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fa[contains(text(),'Results')]"\n } 
2025-08-02 21:03:39.044 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //a[contains(text(),'Results')], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:39.072  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97307"}} 
2025-08-02 21:03:39.166  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:39.234 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:39.257  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97306"}} 
2025-08-02 21:03:39.305  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fa[contains(text(),'Results')]"\n } 
2025-08-02 21:03:39.375 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //a[contains(text(),'Results')], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:39.402  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97307"}]} 
2025-08-02 21:03:39.615  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:Tab_results\\:statusCbx_label",\n   "using": "css selector"\n } 
2025-08-02 21:03:39.692  WARN 1 - [ttp-epoll-4] healenium                        : Failed to find an element using locator By.cssSelector: #scanManagerForm\:homepage_business\:tabViewScanManager\:Tab_results\:statusCbx_label 
2025-08-02 21:03:39.693  WARN 1 - [ttp-epoll-4] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#scanManagerForm\:homepage_business\:tabViewScanManager\:Tab_results\:statusCbx_label"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [ee1a60c61db2d2f29d3cafef88c043a1, findElement {value=#scanManagerForm\:homepage_business\:tabViewScanManager\:Tab_results\:statusCbx_label, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39131}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.G4...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: fdd5724a7923, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: ee1a60c61db2d2f29d3cafef88c043a1 
2025-08-02 21:03:39.795 DEBUG 1 - [ttp-epoll-4] healenium                        : [Get Reference Elements] Request. Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:Tab_results\:statusCbx_label, Command: findElement, Url: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:39.902  WARN 1 - [ttp-epoll-4] healenium                        : Trying to heal... 
2025-08-02 21:03:40.191  WARN 1 - [ttp-epoll-4] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [ee1a60c61db2d2f29d3cafef88c043a1, findElements {value=label#scanManagerForm:homepage_business:tabViewScanManager:Tab_results:statusCbx_label, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39131}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.G4...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: fdd5724a7923, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: ee1a60c61db2d2f29d3cafef88c043a1\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-02 21:03:40.204  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:Tab_results\\:statusCbx_label",\n   "using": "css selector"\n } 
2025-08-02 21:03:40.260 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:Tab_results\:statusCbx_label, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:40.275  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97314"}} 
2025-08-02 21:03:43.357  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:43.410 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:43.425  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97306"}} 
2025-08-02 21:03:43.495  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#scanManagerForm\\:homepage_business\\:tabViewScanManager\\:Tab_results\\:btnSearch",\n   "using": "css selector"\n } 
2025-08-02 21:03:43.599 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #scanManagerForm\:homepage_business\:tabViewScanManager\:Tab_results\:btnSearch, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:43.615  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97315"}]} 
2025-08-02 21:03:43.661  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:43.724 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:43.741  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97306"}} 
2025-08-02 21:03:43.801  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:43.845 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:43.858  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97306"}} 
2025-08-02 21:03:45.119  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:45.179 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:45.191  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97306"}} 
2025-08-02 21:03:45.247  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@id='scanManagerForm:homepage_business:tabViewScanManager:Tab_results:_tblResults_data']\u002f\u002ftd[count(\u002f\u002f*[@id='scanManagerForm:homepage_business:tabViewScanManager:Tab_results:_tblResults_head']\u002f\u002fspan[.='Status']\u002fpreceding::th[contains(@id,'scanManagerForm:homepage_business:tabViewScanManager:Tab_results:_tblResults')])+1]"\n } 
2025-08-02 21:03:45.818 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@id='scanManagerForm:homepage_business:tabViewScanManager:Tab_results:_tblResults_data']//td[count(//*[@id='scanManagerForm:homepage_business:tabViewScanManager:Tab_results:_tblResults_head']//span[.='Status']/preceding::th[contains(@id,'scanManagerForm:homepage_business:tabViewScanManager:Tab_results:_tblResults')])+1], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:45.882  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97334"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97335"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97336"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97337"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97338"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97339"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97340"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97341"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97342"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97343"}]} 
2025-08-02 21:03:45.927  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:45.979 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:45.997  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97306"}} 
2025-08-02 21:03:46.047  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@id='scanManagerForm:homepage_business:tabViewScanManager:Tab_results:_tblResults_data']\u002f\u002ftd[count(\u002f\u002f*[@id='scanManagerForm:homepage_business:tabViewScanManager:Tab_results:_tblResults_head']\u002f\u002fspan[.='Status']\u002fpreceding::th[contains(@id,'scanManagerForm:homepage_business:tabViewScanManager:Tab_results:_tblResults')])+1]"\n } 
2025-08-02 21:03:46.500 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@id='scanManagerForm:homepage_business:tabViewScanManager:Tab_results:_tblResults_data']//td[count(//*[@id='scanManagerForm:homepage_business:tabViewScanManager:Tab_results:_tblResults_head']//span[.='Status']/preceding::th[contains(@id,'scanManagerForm:homepage_business:tabViewScanManager:Tab_results:_tblResults')])+1], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:46.567  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97334"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97335"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97336"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97337"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97338"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97339"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97340"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97341"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97342"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97343"}]} 
2025-08-02 21:03:46.605  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:46.704 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:46.732  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97306"}} 
2025-08-02 21:03:46.804  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:46.848 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:46.859  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97306"}} 
2025-08-02 21:03:46.932  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002ftable\u002ftbody[@id='scanManagerForm:homepage_business:tabViewScanManager:Tab_results:ScanResultDetails:_tblResultDetails_data']\u002ftr\u002ftd[count(\u002f\u002fspan[.='Detection ID']\u002fancestor::th\u002fpreceding-sibling::th)+1]"\n } 
2025-08-02 21:03:47.027 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //table/tbody[@id='scanManagerForm:homepage_business:tabViewScanManager:Tab_results:ScanResultDetails:_tblResultDetails_data']/tr/td[count(//span[.='Detection ID']/ancestor::th/preceding-sibling::th)+1], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:47.057  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97347"}]} 
2025-08-02 21:03:47.091  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:47.160 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:47.178  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97306"}} 
2025-08-02 21:03:47.222  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "css selector",\n   "value": ".ui-widget .customer-badge"\n } 
2025-08-02 21:03:47.292 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: .ui-widget .customer-badge, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:47.319  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97348"}]} 
2025-08-02 21:03:47.352  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:47.407 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:47.433  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97306"}} 
2025-08-02 21:03:47.548  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@id='scanManagerForm:homepage_business:tabViewScanManager:Tab_results:_tblResults_data']\u002f\u002ftd[count(\u002f\u002f*[@id='scanManagerForm:homepage_business:tabViewScanManager:Tab_results:_tblResults_head']\u002f\u002fspan[.='Status']\u002fpreceding::th[contains(@id,'scanManagerForm:homepage_business:tabViewScanManager:Tab_results:_tblResults')])+1]"\n } 
2025-08-02 21:03:48.131 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@id='scanManagerForm:homepage_business:tabViewScanManager:Tab_results:_tblResults_data']//td[count(//*[@id='scanManagerForm:homepage_business:tabViewScanManager:Tab_results:_tblResults_head']//span[.='Status']/preceding::th[contains(@id,'scanManagerForm:homepage_business:tabViewScanManager:Tab_results:_tblResults')])+1], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:48.209  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97334"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97335"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97336"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97337"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97338"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97339"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97340"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97341"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97342"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97343"}]} 
2025-08-02 21:03:48.278  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:48.335 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:48.351  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97306"}} 
2025-08-02 21:03:48.424  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:48.500 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:48.515  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97306"}} 
2025-08-02 21:03:48.559  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002ftable\u002ftbody[@id='scanManagerForm:homepage_business:tabViewScanManager:Tab_results:ScanResultDetails:_tblResultDetails_data']\u002ftr\u002ftd[count(\u002f\u002fspan[.='Detection ID']\u002fancestor::th\u002fpreceding-sibling::th)+1]"\n } 
2025-08-02 21:03:48.652 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //table/tbody[@id='scanManagerForm:homepage_business:tabViewScanManager:Tab_results:ScanResultDetails:_tblResultDetails_data']/tr/td[count(//span[.='Detection ID']/ancestor::th/preceding-sibling::th)+1], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:48.680  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97351"}]} 
2025-08-02 21:03:48.734  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeFilteringDetectionManager",\n   "using": "css selector"\n } 
2025-08-02 21:03:48.796 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_HomeFilteringDetectionManager, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:48.818  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97352"}} 
2025-08-02 21:03:48.925  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:48.998 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:49.022  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97306"}} 
2025-08-02 21:03:49.059  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeFilteringDetectionManager",\n   "using": "css selector"\n } 
2025-08-02 21:03:49.120 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_HomeFilteringDetectionManager, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml?module_name=ScanManager&app_name=SFP 
2025-08-02 21:03:49.149  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.2A6F628457F8678286B639B97CFB5C5A.e.97352"}]} 
2025-08-02 21:03:50.422  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:50.503 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:50.606  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97449"}} 
2025-08-02 21:03:50.821  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fform[@id='topbar-right']\u002ful\u002flabel"\n } 
2025-08-02 21:03:50.903 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //form[@id='topbar-right']/ul/label, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:50.915  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97390"}]} 
2025-08-02 21:03:50.961  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:51.051 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:51.065  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97449"}} 
2025-08-02 21:03:51.135  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#auditManagerForm\\:homepage_business\\:detectionSearch\\:btnReset",\n   "using": "css selector"\n } 
2025-08-02 21:03:51.209 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #auditManagerForm\:homepage_business\:detectionSearch\:btnReset, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:51.234  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97419"}]} 
2025-08-02 21:03:51.402  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:51.505 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:51.524  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97449"}} 
2025-08-02 21:03:51.601  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:51.674 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:51.686  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97449"}} 
2025-08-02 21:03:51.753  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:51.809 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:51.822  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97449"}} 
2025-08-02 21:03:51.864  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#auditManagerForm\\:homepage_business\\:detectionSearch\\:col2\\:0\\:detectionSearchFields\\:numberExl_input",\n   "using": "css selector"\n } 
2025-08-02 21:03:51.916 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #auditManagerForm\:homepage_business\:detectionSearch\:col2\:0\:detectionSearchFields\:numberExl_input, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:51.928  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97457"}]} 
2025-08-02 21:03:52.057  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:52.109 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:52.119  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97449"}} 
2025-08-02 21:03:52.163  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#auditManagerForm\\:homepage_business\\:detectionSearch\\:col2\\:0\\:detectionSearchFields\\:numberExl_input",\n   "using": "css selector"\n } 
2025-08-02 21:03:52.220 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #auditManagerForm\:homepage_business\:detectionSearch\:col2\:0\:detectionSearchFields\:numberExl_input, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:52.235  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97457"}]} 
2025-08-02 21:03:52.475  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:52.552 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:52.567  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97449"}} 
2025-08-02 21:03:52.613  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#auditManagerForm\\:homepage_business\\:detectionSearch\\:btnSearch",\n   "using": "css selector"\n } 
2025-08-02 21:03:52.665 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #auditManagerForm\:homepage_business\:detectionSearch\:btnSearch, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:52.683  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97466"}]} 
2025-08-02 21:03:52.726  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:52.805 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:52.821  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97449"}} 
2025-08-02 21:03:52.896  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:52.961 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:52.983  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97449"}} 
2025-08-02 21:03:53.372  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:53.424 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:53.434  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97449"}} 
2025-08-02 21:03:53.498  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[contains(@id , 'auditManagerForm:homepage_business:Detections_list:detectionResultsTable:0:tagDet')]\u002f\u002fancestor::td[1]"\n } 
2025-08-02 21:03:53.595 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[contains(@id , 'auditManagerForm:homepage_business:Detections_list:detectionResultsTable:0:tagDet')]//ancestor::td[1], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:53.613  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97524"}]} 
2025-08-02 21:03:53.653  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:53.689 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:53.703  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97449"}} 
2025-08-02 21:03:53.754  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[.='7505']"\n } 
2025-08-02 21:03:53.857 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[.='7505'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:53.869  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97525"}]} 
2025-08-02 21:03:53.928  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:54.014 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:54.033  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97449"}} 
2025-08-02 21:03:54.102  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:54.176 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:54.212  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97449"}} 
2025-08-02 21:03:54.293  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:54.616 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:54.631  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97449"}} 
2025-08-02 21:03:54.705  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:54.864 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:54.888  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97449"}} 
2025-08-02 21:03:54.949  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@id='auditManagerForm:homepage_business:Detections_list:actionBar']\u002f\u002fspan[text() = \"Don't Know\"]"\n } 
2025-08-02 21:03:55.075 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@id='auditManagerForm:homepage_business:Detections_list:actionBar']//span[text() = "Don't Know"], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:55.089  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97556"}]} 
2025-08-02 21:03:55.156  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:55.232 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:55.272  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97449"}} 
2025-08-02 21:03:55.336  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:55.429 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:55.445  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97449"}} 
2025-08-02 21:03:55.512  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:55.676 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:55.724  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97449"}} 
2025-08-02 21:03:55.783  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#auditManagerForm\\:homepage_business\\:Detections_list\\:detectionActionDialog\\:text",\n   "using": "css selector"\n } 
2025-08-02 21:03:55.911 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #auditManagerForm\:homepage_business\:Detections_list\:detectionActionDialog\:text, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:55.930  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97563"}]} 
2025-08-02 21:03:56.014  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#auditManagerForm\\:homepage_business\\:Detections_list\\:detectionActionDialog\\:btnNoteEditorSave",\n   "using": "css selector"\n } 
2025-08-02 21:03:56.140 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #auditManagerForm\:homepage_business\:Detections_list\:detectionActionDialog\:btnNoteEditorSave, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:56.155  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97565"}} 
2025-08-02 21:03:56.225  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:56.287 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:56.299  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97449"}} 
2025-08-02 21:03:56.345  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#auditManagerForm\\:homepage_business\\:Detections_list\\:detectionActionDialog\\:btnNoteEditorSave",\n   "using": "css selector"\n } 
2025-08-02 21:03:56.413 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #auditManagerForm\:homepage_business\:Detections_list\:detectionActionDialog\:btnNoteEditorSave, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:56.432  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97565"}]} 
2025-08-02 21:03:56.701  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:56.784 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:56.799  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97449"}} 
2025-08-02 21:03:56.889  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fbutton[.='OK']"\n } 
2025-08-02 21:03:56.960 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //button[.='OK'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:56.985  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97568"}]} 
2025-08-02 21:03:59.058  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='ui-growl-title']"\n } 
2025-08-02 21:03:59.122 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='ui-growl-title'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:59.136  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97638"}]} 
2025-08-02 21:03:59.140  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:59.193 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:59.220  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97449"}} 
2025-08-02 21:03:59.265  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='ui-growl-title']"\n } 
2025-08-02 21:03:59.335 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='ui-growl-title'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:59.349  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97638"}]} 
2025-08-02 21:03:59.382  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeFilteringDetectionManager",\n   "using": "css selector"\n } 
2025-08-02 21:03:59.433 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_HomeFilteringDetectionManager, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:59.446  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97639"}} 
2025-08-02 21:03:59.550  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:03:59.623 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:59.634  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97449"}} 
2025-08-02 21:03:59.668  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeFilteringDetectionManager",\n   "using": "css selector"\n } 
2025-08-02 21:03:59.707 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_HomeFilteringDetectionManager, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:03:59.727  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.5D1BDEA93E46EF0D6167347B36A4A8BC.e.97639"}]} 
2025-08-02 21:04:00.704  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:00.821 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:04:00.840  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.F23E9603265ECE890E8EBAA8F8EC8D6D.e.97738"}} 
2025-08-02 21:04:00.884  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fform[@id='topbar-right']\u002ful\u002flabel"\n } 
2025-08-02 21:04:00.932 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //form[@id='topbar-right']/ul/label, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:04:00.949  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.F23E9603265ECE890E8EBAA8F8EC8D6D.e.97678"}]} 
2025-08-02 21:04:00.993  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:01.079 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:04:01.097  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.F23E9603265ECE890E8EBAA8F8EC8D6D.e.97738"}} 
2025-08-02 21:04:01.145  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#auditManagerForm\\:homepage_business\\:detectionSearch\\:btnReset",\n   "using": "css selector"\n } 
2025-08-02 21:04:01.198 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #auditManagerForm\:homepage_business\:detectionSearch\:btnReset, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:04:01.218  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.F23E9603265ECE890E8EBAA8F8EC8D6D.e.97707"}]} 
2025-08-02 21:04:01.405  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:01.494 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:04:01.509  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.F23E9603265ECE890E8EBAA8F8EC8D6D.e.97738"}} 
2025-08-02 21:04:01.647  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:01.696 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:04:01.721  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.F23E9603265ECE890E8EBAA8F8EC8D6D.e.97738"}} 
2025-08-02 21:04:01.765  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:01.837 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:04:01.852  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.F23E9603265ECE890E8EBAA8F8EC8D6D.e.97738"}} 
2025-08-02 21:04:01.891  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#auditManagerForm\\:homepage_business\\:detectionSearch\\:col2\\:0\\:detectionSearchFields\\:numberExl_input",\n   "using": "css selector"\n } 
2025-08-02 21:04:01.931 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #auditManagerForm\:homepage_business\:detectionSearch\:col2\:0\:detectionSearchFields\:numberExl_input, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:04:01.952  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.F23E9603265ECE890E8EBAA8F8EC8D6D.e.97746"}]} 
2025-08-02 21:04:02.062  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:02.119 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:04:02.129  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.F23E9603265ECE890E8EBAA8F8EC8D6D.e.97738"}} 
2025-08-02 21:04:02.167  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#auditManagerForm\\:homepage_business\\:detectionSearch\\:col2\\:0\\:detectionSearchFields\\:numberExl_input",\n   "using": "css selector"\n } 
2025-08-02 21:04:02.210 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #auditManagerForm\:homepage_business\:detectionSearch\:col2\:0\:detectionSearchFields\:numberExl_input, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:04:02.225  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.F23E9603265ECE890E8EBAA8F8EC8D6D.e.97746"}]} 
2025-08-02 21:04:02.411  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:02.480 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:04:02.497  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.F23E9603265ECE890E8EBAA8F8EC8D6D.e.97738"}} 
2025-08-02 21:04:02.586  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#auditManagerForm\\:homepage_business\\:detectionSearch\\:btnSearch",\n   "using": "css selector"\n } 
2025-08-02 21:04:02.641 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #auditManagerForm\:homepage_business\:detectionSearch\:btnSearch, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:04:02.663  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.F23E9603265ECE890E8EBAA8F8EC8D6D.e.97755"}]} 
2025-08-02 21:04:02.709  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:02.775 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:04:02.790  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.F23E9603265ECE890E8EBAA8F8EC8D6D.e.97738"}} 
2025-08-02 21:04:02.866  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:03.234 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:04:03.257  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.F23E9603265ECE890E8EBAA8F8EC8D6D.e.97738"}} 
2025-08-02 21:04:03.315  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:03.370 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:04:03.384  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.F23E9603265ECE890E8EBAA8F8EC8D6D.e.97738"}} 
2025-08-02 21:04:03.428  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[contains(@id , 'auditManagerForm:homepage_business:Detections_list:detectionResultsTable:0:tagDet')]\u002f\u002fancestor::td[1]"\n } 
2025-08-02 21:04:03.486 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[contains(@id , 'auditManagerForm:homepage_business:Detections_list:detectionResultsTable:0:tagDet')]//ancestor::td[1], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:04:03.503  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.F23E9603265ECE890E8EBAA8F8EC8D6D.e.97813"}]} 
2025-08-02 21:04:03.558  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:03.624 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:04:03.641  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.F23E9603265ECE890E8EBAA8F8EC8D6D.e.97738"}} 
2025-08-02 21:04:03.679  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[contains(@id , 'auditManagerForm:homepage_business:Detections_list:detectionResultsTable:0:tagDet')]\u002f\u002fancestor::td[1]"\n } 
2025-08-02 21:04:03.751 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[contains(@id , 'auditManagerForm:homepage_business:Detections_list:detectionResultsTable:0:tagDet')]//ancestor::td[1], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:04:03.782  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.F23E9603265ECE890E8EBAA8F8EC8D6D.e.97813"}]} 
2025-08-02 21:04:04.037  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:04.476 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:04:04.494  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.42393018E091FEE925D9EFFB8BC06A8F.e.97907"}} 
2025-08-02 21:04:04.545  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@title='Exit']"\n } 
2025-08-02 21:04:04.623 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@title='Exit'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:04:04.638  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.42393018E091FEE925D9EFFB8BC06A8F.e.97911"}]} 
2025-08-02 21:04:04.678  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:04.736 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:04:04.752  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.42393018E091FEE925D9EFFB8BC06A8F.e.97907"}} 
2025-08-02 21:04:04.817  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@title='Exit' or @title ='Quitter']"\n } 
2025-08-02 21:04:04.895 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@title='Exit' or @title ='Quitter'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP 
2025-08-02 21:04:04.905  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.42393018E091FEE925D9EFFB8BC06A8F.e.97911"}]} 
2025-08-02 21:04:05.007  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:05.078 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP# 
2025-08-02 21:04:05.126  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.42393018E091FEE925D9EFFB8BC06A8F.e.97907"}} 
2025-08-02 21:04:05.221  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fbutton[.='Yes']"\n } 
2025-08-02 21:04:05.339 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //button[.='Yes'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/DetectionManager/Homepage.xhtml?module_name=FilteringDetectionManager&app_name=SFP# 
2025-08-02 21:04:05.432  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.42393018E091FEE925D9EFFB8BC06A8F.e.97893"}]} 
2025-08-02 21:04:05.675  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:06.097  WARN 1 - [ttp-epoll-4] healenium                        : [Save Elements] Error during save elements: [f.38ECE04EB16FEB0403C299A060B7EBBC.d.42393018E091FEE925D9EFFB8BC06A8F.e.97907]. Message: stale element reference: stale element not found\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [ee1a60c61db2d2f29d3cafef88c043a1, executeScript {args=[{element-6066-11e4-a52e-4f735466cecf=f.38ECE04EB16FEB0403C299A060B7EBBC.d.42393018E091FEE925D9EFFB8BC06A8F.e.97907}], script=var items = [];var result = {items: null, url: null};var a = arguments[0];while (a != document) {  var child = a;  var i=0; while(child=child.previousElementSibling) i++;  var node = {tag:null,id:null,index:null,classes:[],other:{},innerText:""};  if (a.tagName !== undefined) {    node.tag = a.tagName.toLowerCase();  }  node.id = a.id;  node.index = i;  node.innerText = a.innerText || "";  if (a.hasAttribute("class")) {	  node.classes = a.attributes.class.value.split(' ');  }  for (index = 0; index < a.attributes.length; ++index) {      var attrName = a.attributes[index].name;      if (["id","class"].indexOf(attrName) == -1){		    node.other[attrName] = a.attributes[index].value;      }  };  items.unshift(node);  a = a.parentNode;}result.items = items;result.url = window.location.href;return JSON.stringify(result);}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39131}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.G4...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: fdd5724a7923, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: ee1a60c61db2d2f29d3cafef88c043a1. Exception: org.openqa.selenium.StaleElementReferenceException: stale element reference: stale element not found\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [ee1a60c61db2d2f29d3cafef88c043a1, executeScript {args=[{element-6066-11e4-a52e-4f735466cecf=f.38ECE04EB16FEB0403C299A060B7EBBC.d.42393018E091FEE925D9EFFB8BC06A8F.e.97907}], script=var items = [];var result = {items: null, url: null};var a = arguments[0];while (a != document) {  var child = a;  var i=0; while(child=child.previousElementSibling) i++;  var node = {tag:null,id:null,index:null,classes:[],other:{},innerText:""};  if (a.tagName !== undefined) {    node.tag = a.tagName.toLowerCase();  }  node.id = a.id;  node.index = i;  node.innerText = a.innerText || "";  if (a.hasAttribute("class")) {	  node.classes = a.attributes.class.value.split(' ');  }  for (index = 0; index < a.attributes.length; ++index) {      var attrName = a.attributes[index].name;      if (["id","class"].indexOf(attrName) == -1){		    node.other[attrName] = a.attributes[index].value;      }  };  items.unshift(node);  a = a.parentNode;}result.items = items;result.url = window.location.href;return JSON.stringify(result);}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39131}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.G4...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: fdd5724a7923, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: ee1a60c61db2d2f29d3cafef88c043a1 
2025-08-02 21:04:06.099  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.42393018E091FEE925D9EFFB8BC06A8F.e.97907"}} 
2025-08-02 21:04:06.173  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#pageLoginForm\\:login_business\\:UserName",\n   "using": "css selector"\n } 
2025-08-02 21:04:06.240 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #pageLoginForm\:login_business\:UserName, Command: findElements, URL: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-02 21:04:06.250  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.FE50B73BCC6A7CC8D1CDBD8F348F755E.e.97919"}]} 
2025-08-02 21:04:06.584  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:06.651  WARN 1 - [ttp-epoll-4] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-02 21:04:06.653  WARN 1 - [ttp-epoll-4] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [ee1a60c61db2d2f29d3cafef88c043a1, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39131}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.G4...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: fdd5724a7923, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: ee1a60c61db2d2f29d3cafef88c043a1 
2025-08-02 21:04:06.681 DEBUG 1 - [ttp-epoll-4] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-02 21:04:06.720  WARN 1 - [ttp-epoll-4] healenium                        : Trying to heal... 
2025-08-02 21:04:06.722  WARN 1 - [ttp-epoll-4] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-02 21:04:06.730  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fspan[contains(text(),'Back to login')]"\n } 
2025-08-02 21:04:06.764  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[]} 
2025-08-02 21:04:06.814  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:06.876  WARN 1 - [ttp-epoll-4] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-02 21:04:06.877  WARN 1 - [ttp-epoll-4] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [ee1a60c61db2d2f29d3cafef88c043a1, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39131}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.G4...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: fdd5724a7923, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: ee1a60c61db2d2f29d3cafef88c043a1 
2025-08-02 21:04:06.897 DEBUG 1 - [ttp-epoll-4] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-02 21:04:06.935  WARN 1 - [ttp-epoll-4] healenium                        : Trying to heal... 
2025-08-02 21:04:06.938  WARN 1 - [ttp-epoll-4] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-02 21:04:06.945  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "css selector",\n   "value": ".fa-street-view"\n } 
2025-08-02 21:04:06.980  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[]} 
2025-08-02 21:04:06.985  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:07.029  WARN 1 - [ttp-epoll-4] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-02 21:04:07.030  WARN 1 - [ttp-epoll-4] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [ee1a60c61db2d2f29d3cafef88c043a1, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39131}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.G4...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: fdd5724a7923, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: ee1a60c61db2d2f29d3cafef88c043a1 
2025-08-02 21:04:07.041 DEBUG 1 - [ttp-epoll-4] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-02 21:04:07.084  WARN 1 - [ttp-epoll-4] healenium                        : Trying to heal... 
2025-08-02 21:04:07.086  WARN 1 - [ttp-epoll-4] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-02 21:04:07.101  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#pageLoginForm\\:login_business\\:UserName",\n   "using": "css selector"\n } 
2025-08-02 21:04:07.166 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #pageLoginForm\:login_business\:UserName, Command: findElements, URL: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-02 21:04:07.176  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.7E4D64F470AE64FDAAE6D23EF1F0D976.e.97941"}]} 
2025-08-02 21:04:07.269  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:07.323  WARN 1 - [ttp-epoll-4] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-02 21:04:07.324  WARN 1 - [ttp-epoll-4] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [ee1a60c61db2d2f29d3cafef88c043a1, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39131}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.G4...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: fdd5724a7923, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: ee1a60c61db2d2f29d3cafef88c043a1 
2025-08-02 21:04:07.342 DEBUG 1 - [ttp-epoll-4] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-02 21:04:07.386  WARN 1 - [ttp-epoll-4] healenium                        : Trying to heal... 
2025-08-02 21:04:07.387  WARN 1 - [ttp-epoll-4] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-02 21:04:07.394  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#pageLoginForm\\:login_business\\:Password",\n   "using": "css selector"\n } 
2025-08-02 21:04:07.462 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #pageLoginForm\:login_business\:Password, Command: findElements, URL: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-02 21:04:07.476  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.7E4D64F470AE64FDAAE6D23EF1F0D976.e.97942"}]} 
2025-08-02 21:04:07.551  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:07.619  WARN 1 - [ttp-epoll-4] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-02 21:04:07.619  WARN 1 - [ttp-epoll-4] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [ee1a60c61db2d2f29d3cafef88c043a1, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39131}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.G4...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: fdd5724a7923, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: ee1a60c61db2d2f29d3cafef88c043a1 
2025-08-02 21:04:07.646 DEBUG 1 - [ttp-epoll-4] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-02 21:04:07.684  WARN 1 - [ttp-epoll-4] healenium                        : Trying to heal... 
2025-08-02 21:04:07.686  WARN 1 - [ttp-epoll-4] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-02 21:04:07.693  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#pageLoginForm\\:login_business\\:UserName",\n   "using": "css selector"\n } 
2025-08-02 21:04:07.751 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #pageLoginForm\:login_business\:UserName, Command: findElements, URL: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-02 21:04:07.761  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.7E4D64F470AE64FDAAE6D23EF1F0D976.e.97941"}]} 
2025-08-02 21:04:07.882  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:07.950  WARN 1 - [ttp-epoll-4] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-02 21:04:07.951  WARN 1 - [ttp-epoll-4] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [ee1a60c61db2d2f29d3cafef88c043a1, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39131}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.G4...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: fdd5724a7923, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: ee1a60c61db2d2f29d3cafef88c043a1 
2025-08-02 21:04:07.976 DEBUG 1 - [ttp-epoll-4] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-02 21:04:08.000  WARN 1 - [ttp-epoll-4] healenium                        : Trying to heal... 
2025-08-02 21:04:08.001  WARN 1 - [ttp-epoll-4] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-02 21:04:08.009  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#pageLoginForm\\:login_business\\:UserName",\n   "using": "css selector"\n } 
2025-08-02 21:04:08.053 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #pageLoginForm\:login_business\:UserName, Command: findElements, URL: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-02 21:04:08.068  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.7E4D64F470AE64FDAAE6D23EF1F0D976.e.97941"}]} 
2025-08-02 21:04:08.225  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:08.267  WARN 1 - [ttp-epoll-4] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-02 21:04:08.268  WARN 1 - [ttp-epoll-4] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [ee1a60c61db2d2f29d3cafef88c043a1, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39131}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.G4...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: fdd5724a7923, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: ee1a60c61db2d2f29d3cafef88c043a1 
2025-08-02 21:04:08.283 DEBUG 1 - [ttp-epoll-4] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-02 21:04:08.320  WARN 1 - [ttp-epoll-4] healenium                        : Trying to heal... 
2025-08-02 21:04:08.323  WARN 1 - [ttp-epoll-4] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-02 21:04:08.330  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#pageLoginForm\\:login_business\\:Password",\n   "using": "css selector"\n } 
2025-08-02 21:04:08.388 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #pageLoginForm\:login_business\:Password, Command: findElements, URL: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-02 21:04:08.403  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.7E4D64F470AE64FDAAE6D23EF1F0D976.e.97942"}]} 
2025-08-02 21:04:08.492  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:08.529  WARN 1 - [ttp-epoll-4] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-02 21:04:08.530  WARN 1 - [ttp-epoll-4] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [ee1a60c61db2d2f29d3cafef88c043a1, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39131}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.G4...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: fdd5724a7923, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: ee1a60c61db2d2f29d3cafef88c043a1 
2025-08-02 21:04:08.556 DEBUG 1 - [ttp-epoll-4] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-02 21:04:08.582  WARN 1 - [ttp-epoll-4] healenium                        : Trying to heal... 
2025-08-02 21:04:08.590  WARN 1 - [ttp-epoll-4] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-02 21:04:08.605  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#pageLoginForm\\:login_business\\:Password",\n   "using": "css selector"\n } 
2025-08-02 21:04:08.688 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #pageLoginForm\:login_business\:Password, Command: findElements, URL: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-02 21:04:08.707  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.7E4D64F470AE64FDAAE6D23EF1F0D976.e.97942"}]} 
2025-08-02 21:04:08.905  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:08.965  WARN 1 - [ttp-epoll-4] healenium                        : Failed to find an element using locator By.xpath: //*[@class='pi pi-spin pi-spinner ajax-loader'] 
2025-08-02 21:04:08.966  WARN 1 - [ttp-epoll-4] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//*[@class='pi pi-spin pi-spinner ajax-loader']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [ee1a60c61db2d2f29d3cafef88c043a1, findElement {value=//*[@class='pi pi-spin pi-spinner ajax-loader'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39131}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.G4...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: fdd5724a7923, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: ee1a60c61db2d2f29d3cafef88c043a1 
2025-08-02 21:04:08.986 DEBUG 1 - [ttp-epoll-4] healenium                        : [Get Reference Elements] Request. Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, Url: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-02 21:04:09.011  WARN 1 - [ttp-epoll-4] healenium                        : Trying to heal... 
2025-08-02 21:04:09.013  WARN 1 - [ttp-epoll-4] healenium                        : New element locators have not been found.\n Score property = 0.6 is bigger than healing's locator score 
2025-08-02 21:04:09.019  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#pageLoginForm\\:login_business\\:goButton",\n   "using": "css selector"\n } 
2025-08-02 21:04:09.098 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #pageLoginForm\:login_business\:goButton, Command: findElements, URL: http://************:8080/AMLUI/SWAF/jsp/login.xhtml 
2025-08-02 21:04:09.111  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.7E4D64F470AE64FDAAE6D23EF1F0D976.e.97955"}]} 
2025-08-02 21:04:11.183  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='ui-growl-title']"\n } 
2025-08-02 21:04:11.469 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='ui-growl-title'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml 
2025-08-02 21:04:11.481  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.E44A2B6BA42E0BB85544C5BE5EA68AB1.e.98078"}]} 
2025-08-02 21:04:11.485  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:11.539 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml 
2025-08-02 21:04:11.554  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.E44A2B6BA42E0BB85544C5BE5EA68AB1.e.98079"}} 
2025-08-02 21:04:11.630  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='ui-growl-title']"\n } 
2025-08-02 21:04:11.696 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='ui-growl-title'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml 
2025-08-02 21:04:11.721  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.E44A2B6BA42E0BB85544C5BE5EA68AB1.e.98078"}]} 
2025-08-02 21:04:12.216  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-02 21:04:12.275 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_HomeListManager, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml 
2025-08-02 21:04:12.292  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.E44A2B6BA42E0BB85544C5BE5EA68AB1.e.98080"}} 
2025-08-02 21:04:12.384  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:12.461 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml 
2025-08-02 21:04:12.473  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.E44A2B6BA42E0BB85544C5BE5EA68AB1.e.98079"}} 
2025-08-02 21:04:12.516  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-02 21:04:12.592 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_HomeListManager, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ScanManager/Homepage.xhtml 
2025-08-02 21:04:12.609  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.E44A2B6BA42E0BB85544C5BE5EA68AB1.e.98080"}]} 
2025-08-02 21:04:13.524  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:13.596 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:13.607  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98176"}} 
2025-08-02 21:04:13.668  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fform[@id='topbar-right']\u002ful\u002flabel"\n } 
2025-08-02 21:04:13.737 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //form[@id='topbar-right']/ul/label, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:13.749  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98115"}]} 
2025-08-02 21:04:13.796  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fa[contains(text(),'List Manager')]"\n } 
2025-08-02 21:04:13.895 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //a[contains(text(),'List Manager')], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:13.909  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98177"}} 
2025-08-02 21:04:14.004  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:14.059 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:14.076  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98176"}} 
2025-08-02 21:04:14.133  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fa[contains(text(),'List Manager')]"\n } 
2025-08-02 21:04:14.203 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //a[contains(text(),'List Manager')], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:14.225  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98177"}]} 
2025-08-02 21:04:14.428  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:Tab_list_manager\\:Sub_black_list_viewer\\:zoneCbx",\n   "using": "css selector"\n } 
2025-08-02 21:04:14.493 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:zoneCbx, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:14.506  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98178"}} 
2025-08-02 21:04:14.608  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:14.694 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:14.706  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98176"}} 
2025-08-02 21:04:14.743  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:Tab_list_manager\\:Sub_black_list_viewer\\:btnBLVSearch",\n   "using": "css selector"\n } 
2025-08-02 21:04:14.804 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:btnBLVSearch, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:14.833  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98145"}]} 
2025-08-02 21:04:14.891  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:Tab_list_manager\\:Sub_black_list_viewer\\:_tblResults\\:_btnNew",\n   "using": "css selector"\n } 
2025-08-02 21:04:14.965 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:14.985  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98146"}} 
2025-08-02 21:04:15.279  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:Tab_list_manager\\:Sub_black_list_viewer\\:_tblResults\\:_btnNew",\n   "using": "css selector"\n } 
2025-08-02 21:04:15.372 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnNew, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:15.390  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98216"}} 
2025-08-02 21:04:15.436  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:15.488 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:15.498  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98176"}} 
2025-08-02 21:04:15.529  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:Tab_list_manager\\:Sub_black_list_viewer\\:_tblResults\\:_btnImport",\n   "using": "css selector"\n } 
2025-08-02 21:04:15.631 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnImport, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:15.648  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98217"}} 
2025-08-02 21:04:15.740  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:15.784 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:15.798  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98176"}} 
2025-08-02 21:04:15.837  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:Tab_list_manager\\:Sub_black_list_viewer\\:_tblResults\\:_btnImport",\n   "using": "css selector"\n } 
2025-08-02 21:04:15.946 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:Sub_black_list_viewer\:_tblResults\:_btnImport, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:15.966  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98217"}]} 
2025-08-02 21:04:16.010  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:16.064 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:16.088  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98176"}} 
2025-08-02 21:04:16.211  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:Tab_list_manager\\:sub_black_list_import_viewer\\:zoneCbx",\n   "using": "css selector"\n } 
2025-08-02 21:04:16.266 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:sub_black_list_import_viewer\:zoneCbx, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:16.279  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98236"}]} 
2025-08-02 21:04:17.526  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role='option']"\n } 
2025-08-02 21:04:17.603 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role='option'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:17.794  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98237"}]} 
2025-08-02 21:04:17.803  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Common Zone 05']"\n } 
2025-08-02 21:04:17.878 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='Common Zone 05'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:17.902  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98237"}]} 
2025-08-02 21:04:17.907  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:17.956 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:17.967  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98176"}} 
2025-08-02 21:04:18.012  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Common Zone 05']"\n } 
2025-08-02 21:04:18.057 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='Common Zone 05'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:18.076  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98237"}]} 
2025-08-02 21:04:18.151  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Common Zone 05']"\n } 
2025-08-02 21:04:18.222 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='Common Zone 05'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:18.234  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98237"}} 
2025-08-02 21:04:18.458  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:18.510 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:18.523  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98176"}} 
2025-08-02 21:04:18.577  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:Tab_list_manager\\:sub_black_list_import_viewer\\:file_input",\n   "using": "css selector"\n } 
2025-08-02 21:04:18.680 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:sub_black_list_import_viewer\:file_input, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:18.699  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98238"}]} 
2025-08-02 21:04:18.778  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:18.828 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:18.841  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98176"}} 
2025-08-02 21:04:18.897  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:Tab_list_manager\\:sub_black_list_import_viewer\\:file_input",\n   "using": "css selector"\n } 
2025-08-02 21:04:18.961 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:sub_black_list_import_viewer\:file_input, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:18.971  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98238"}]} 
2025-08-02 21:04:19.043  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:19.113 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:19.123  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98176"}} 
2025-08-02 21:04:19.168  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:Tab_list_manager\\:sub_black_list_import_viewer\\:lockFlag_input",\n   "using": "css selector"\n } 
2025-08-02 21:04:19.230 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:sub_black_list_import_viewer\:lockFlag_input, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:19.252  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98223"}]} 
2025-08-02 21:04:19.305  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:19.349 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:19.369  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98176"}} 
2025-08-02 21:04:19.426  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:Tab_list_manager\\:sub_black_list_import_viewer\\:lockFlag",\n   "using": "css selector"\n } 
2025-08-02 21:04:19.487 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:sub_black_list_import_viewer\:lockFlag, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:19.497  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98240"}]} 
2025-08-02 21:04:19.730  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:19.770 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:19.785  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98176"}} 
2025-08-02 21:04:19.937  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:Tab_list_manager\\:sub_black_list_import_viewer\\:upgradeFlag_input",\n   "using": "css selector"\n } 
2025-08-02 21:04:20.024 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:sub_black_list_import_viewer\:upgradeFlag_input, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:20.038  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98224"}]} 
2025-08-02 21:04:20.090  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:20.168 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:20.185  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98176"}} 
2025-08-02 21:04:20.249  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:Tab_list_manager\\:sub_black_list_import_viewer\\:_btnImport",\n   "using": "css selector"\n } 
2025-08-02 21:04:20.309 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_list_manager\:sub_black_list_import_viewer\:_btnImport, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:20.488  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98241"}]} 
2025-08-02 21:04:20.642  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fa[contains(text(),'Activity')]"\n } 
2025-08-02 21:04:20.747 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //a[contains(text(),'Activity')], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:20.769  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98242"}} 
2025-08-02 21:04:20.926  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:21.103 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:21.144  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98176"}} 
2025-08-02 21:04:21.208  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fa[contains(text(),'Activity')]"\n } 
2025-08-02 21:04:21.290 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //a[contains(text(),'Activity')], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:21.318  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98255"}]} 
2025-08-02 21:04:21.681  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:Tab_activity\\:searchDateFrom",\n   "using": "css selector"\n } 
2025-08-02 21:04:21.748 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_activity\:searchDateFrom, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:21.765  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98256"}} 
2025-08-02 21:04:26.892  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:26.951 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:26.963  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98176"}} 
2025-08-02 21:04:27.003  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:Tab_activity\\:btnASearch",\n   "using": "css selector"\n } 
2025-08-02 21:04:27.063 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:Tab_activity\:btnASearch, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:27.077  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98257"}]} 
2025-08-02 21:04:27.129  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:27.197 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:27.210  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98176"}} 
2025-08-02 21:04:27.269  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:27.334 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:27.362  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98176"}} 
2025-08-02 21:04:27.457  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002ftbody[@id='listManagerForm:homepage_business:tabViewListManager:Tab_activity:_tblResults_data']\u002f\u002ftr[1]\u002f\u002ftd[5]"\n } 
2025-08-02 21:04:27.537 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //tbody[@id='listManagerForm:homepage_business:tabViewListManager:Tab_activity:_tblResults_data']//tr[1]//td[5], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:27.554  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98270"}} 
2025-08-02 21:04:27.626  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeSWIFTConfiguration",\n   "using": "css selector"\n } 
2025-08-02 21:04:27.701 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_HomeSWIFTConfiguration, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:27.718  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98271"}} 
2025-08-02 21:04:27.790  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:27.854 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:27.866  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98176"}} 
2025-08-02 21:04:27.929  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeSWIFTConfiguration",\n   "using": "css selector"\n } 
2025-08-02 21:04:27.977 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_HomeSWIFTConfiguration, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:27.991  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.9AEF9FAD5E6B07DB4792A3E8932B55AA.e.98271"}]} 
2025-08-02 21:04:28.722  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:28.769 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:28.780  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98357"}} 
2025-08-02 21:04:28.845  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fform[@id='topbar-right']\u002ful\u002flabel"\n } 
2025-08-02 21:04:28.915 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //form[@id='topbar-right']/ul/label, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:28.941  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98301"}]} 
2025-08-02 21:04:28.976  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:29.013 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:29.023  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98357"}} 
2025-08-02 21:04:29.081  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#swiftConfigurationForm\\:homepage_business\\:zoneCbx_label",\n   "using": "css selector"\n } 
2025-08-02 21:04:29.155 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #swiftConfigurationForm\:homepage_business\:zoneCbx_label, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:29.169  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98309"}]} 
2025-08-02 21:04:30.350  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role='option']"\n } 
2025-08-02 21:04:30.421 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role='option'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:30.438  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98358"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98359"}]} 
2025-08-02 21:04:30.445  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Common Zone 05']"\n } 
2025-08-02 21:04:30.493 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='Common Zone 05'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:30.503  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98359"}]} 
2025-08-02 21:04:30.511  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:30.584 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:30.616  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98357"}} 
2025-08-02 21:04:30.669  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Common Zone 05']"\n } 
2025-08-02 21:04:30.721 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='Common Zone 05'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:30.731  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98359"}]} 
2025-08-02 21:04:30.769  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Common Zone 05']"\n } 
2025-08-02 21:04:30.830 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='Common Zone 05'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:30.842  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98359"}} 
2025-08-02 21:04:31.138  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:31.200 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:31.213  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98357"}} 
2025-08-02 21:04:31.261  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#swiftConfigurationForm\\:homepage_business\\:btnSearch",\n   "using": "css selector"\n } 
2025-08-02 21:04:31.328 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #swiftConfigurationForm\:homepage_business\:btnSearch, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:31.340  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98326"}]} 
2025-08-02 21:04:31.391  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:31.494 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:31.503  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98357"}} 
2025-08-02 21:04:31.546  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:31.630 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:31.647  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98357"}} 
2025-08-02 21:04:31.693  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:31.733 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:31.743  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98357"}} 
2025-08-02 21:04:31.787  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#swiftConfigurationForm\\:homepage_business\\:btnSearch",\n   "using": "css selector"\n } 
2025-08-02 21:04:31.851 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #swiftConfigurationForm\:homepage_business\:btnSearch, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:31.866  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98377"}]} 
2025-08-02 21:04:31.916  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:32.011 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:32.021  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98357"}} 
2025-08-02 21:04:32.079  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:32.149 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:32.166  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98357"}} 
2025-08-02 21:04:32.213  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#swiftConfigurationForm\\:homepage_business\\:_tblResults\\:_btnAddConfig",\n   "using": "css selector"\n } 
2025-08-02 21:04:32.264 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #swiftConfigurationForm\:homepage_business\:_tblResults\:_btnAddConfig, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:32.275  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98395"}]} 
2025-08-02 21:04:32.330  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:33.725  WARN 1 - [ttp-epoll-4] healenium                        : [Save Elements] Error during save elements: [f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98357]. Message: stale element reference: stale element not found in the current frame\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [ee1a60c61db2d2f29d3cafef88c043a1, executeScript {args=[{element-6066-11e4-a52e-4f735466cecf=f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98357}], script=var items = [];var result = {items: null, url: null};var a = arguments[0];while (a != document) {  var child = a;  var i=0; while(child=child.previousElementSibling) i++;  var node = {tag:null,id:null,index:null,classes:[],other:{},innerText:""};  if (a.tagName !== undefined) {    node.tag = a.tagName.toLowerCase();  }  node.id = a.id;  node.index = i;  node.innerText = a.innerText || "";  if (a.hasAttribute("class")) {	  node.classes = a.attributes.class.value.split(' ');  }  for (index = 0; index < a.attributes.length; ++index) {      var attrName = a.attributes[index].name;      if (["id","class"].indexOf(attrName) == -1){		    node.other[attrName] = a.attributes[index].value;      }  };  items.unshift(node);  a = a.parentNode;}result.items = items;result.url = window.location.href;return JSON.stringify(result);}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39131}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.G4...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: fdd5724a7923, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: ee1a60c61db2d2f29d3cafef88c043a1. Exception: org.openqa.selenium.StaleElementReferenceException: stale element reference: stale element not found in the current frame\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [ee1a60c61db2d2f29d3cafef88c043a1, executeScript {args=[{element-6066-11e4-a52e-4f735466cecf=f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98357}], script=var items = [];var result = {items: null, url: null};var a = arguments[0];while (a != document) {  var child = a;  var i=0; while(child=child.previousElementSibling) i++;  var node = {tag:null,id:null,index:null,classes:[],other:{},innerText:""};  if (a.tagName !== undefined) {    node.tag = a.tagName.toLowerCase();  }  node.id = a.id;  node.index = i;  node.innerText = a.innerText || "";  if (a.hasAttribute("class")) {	  node.classes = a.attributes.class.value.split(' ');  }  for (index = 0; index < a.attributes.length; ++index) {      var attrName = a.attributes[index].name;      if (["id","class"].indexOf(attrName) == -1){		    node.other[attrName] = a.attributes[index].value;      }  };  items.unshift(node);  a = a.parentNode;}result.items = items;result.url = window.location.href;return JSON.stringify(result);}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39131}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.G4...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: fdd5724a7923, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: ee1a60c61db2d2f29d3cafef88c043a1 
2025-08-02 21:04:33.726  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98357"}} 
2025-08-02 21:04:33.792  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#swiftConfiguratoinDetail\\:detail_business\\:_templateName",\n   "using": "css selector"\n } 
2025-08-02 21:04:33.942 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #swiftConfiguratoinDetail\:detail_business\:_templateName, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:33.987  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98410"}]} 
2025-08-02 21:04:34.092  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:34.182 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:34.199  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98813"}} 
2025-08-02 21:04:34.259  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#swiftConfiguratoinDetail\\:detail_business\\:_templateName",\n   "using": "css selector"\n } 
2025-08-02 21:04:34.387 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #swiftConfiguratoinDetail\:detail_business\:_templateName, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:34.431  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98410"}]} 
2025-08-02 21:04:34.742  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:34.837 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:34.852  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98813"}} 
2025-08-02 21:04:34.933  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#swiftConfiguratoinDetail\\:detail_business\\:_tblResults\\:imgSelect",\n   "using": "css selector"\n } 
2025-08-02 21:04:35.032 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #swiftConfiguratoinDetail\:detail_business\:_tblResults\:imgSelect, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:35.096  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98402"}]} 
2025-08-02 21:04:35.245  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:35.338 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:35.554  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98813"}} 
2025-08-02 21:04:35.721  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@id='swiftConfiguratoinDetail:detail_business:_tblResults:overlaySelect']\u002f\u002fspan[.='Select all']"\n } 
2025-08-02 21:04:35.810 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@id='swiftConfiguratoinDetail:detail_business:_tblResults:overlaySelect']//span[.='Select all'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:35.828  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98815"}]} 
2025-08-02 21:04:35.941  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:36.489 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:36.509  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98813"}} 
2025-08-02 21:04:36.555  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#swiftConfiguratoinDetail\\:detail_business\\:_tblResultsField\\:imgSelect",\n   "using": "css selector"\n } 
2025-08-02 21:04:36.717 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #swiftConfiguratoinDetail\:detail_business\:_tblResultsField\:imgSelect, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:36.766  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98403"}]} 
2025-08-02 21:04:36.898  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:37.010 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:37.027  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98813"}} 
2025-08-02 21:04:37.090  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@id='swiftConfiguratoinDetail:detail_business:_tblResultsField:overlaySelect']\u002f\u002fspan[.='Select all']"\n } 
2025-08-02 21:04:37.188 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@id='swiftConfiguratoinDetail:detail_business:_tblResultsField:overlaySelect']//span[.='Select all'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:37.207  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.99015"}]} 
2025-08-02 21:04:37.277  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:37.661 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:37.693  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98813"}} 
2025-08-02 21:04:37.753  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#swiftConfiguratoinDetail\\:detail_business\\:btnSave",\n   "using": "css selector"\n } 
2025-08-02 21:04:37.871 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #swiftConfiguratoinDetail\:detail_business\:btnSave, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:37.915  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98792"}]} 
2025-08-02 21:04:38.067  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:38.227 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:38.246  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98813"}} 
2025-08-02 21:04:38.301  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:38.413 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:38.446  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98813"}} 
2025-08-02 21:04:38.506  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:38.568 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:38.596  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98813"}} 
2025-08-02 21:04:38.665  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:38.782 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:38.805  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98813"}} 
2025-08-02 21:04:38.877  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:38.975 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:39.001  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98813"}} 
2025-08-02 21:04:39.057  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:39.166 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:39.191  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98813"}} 
2025-08-02 21:04:39.261  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:39.334 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:39.362  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98813"}} 
2025-08-02 21:04:39.440  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:39.541 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:39.580  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98813"}} 
2025-08-02 21:04:39.651  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:39.750 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:39.766  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98813"}} 
2025-08-02 21:04:39.819  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:39.920 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:39.938  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98813"}} 
2025-08-02 21:04:40.018  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:40.160 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:40.189  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98813"}} 
2025-08-02 21:04:40.281  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:40.349 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:40.366  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98813"}} 
2025-08-02 21:04:40.432  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:41.318 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:41.360  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.98813"}} 
2025-08-02 21:04:41.445  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#swiftConfigurationForm\\:homepage_business\\:searchGroup1",\n   "using": "css selector"\n } 
2025-08-02 21:04:41.506 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #swiftConfigurationForm\:homepage_business\:searchGroup1, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:41.519  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.99107"}]} 
2025-08-02 21:04:41.524  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-02 21:04:41.572 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_HomeListManager, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:41.598  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.99108"}} 
2025-08-02 21:04:41.687  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:41.755 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:41.766  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.99109"}} 
2025-08-02 21:04:41.810  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#menuform\\:om_HomeListManager",\n   "using": "css selector"\n } 
2025-08-02 21:04:41.902 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #menuform\:om_HomeListManager, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/SWIFTConfiguration/Homepage.xhtml?module_name=SWIFTConfiguration&app_name=SFP 
2025-08-02 21:04:41.919  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.D700ABE99876379203DF1B0CD68C39D9.e.99108"}]} 
2025-08-02 21:04:42.900  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:42.989 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:43.014  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.AF89FB11AF0BF4AF36775E2C4C666C33.e.99205"}} 
2025-08-02 21:04:43.053  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fform[@id='topbar-right']\u002ful\u002flabel"\n } 
2025-08-02 21:04:43.135 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //form[@id='topbar-right']/ul/label, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:43.162  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.AF89FB11AF0BF4AF36775E2C4C666C33.e.99145"}]} 
2025-08-02 21:04:43.251  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fa[contains(text(),'List Set Manager')]"\n } 
2025-08-02 21:04:43.313 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //a[contains(text(),'List Set Manager')], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:43.333  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.AF89FB11AF0BF4AF36775E2C4C666C33.e.99206"}} 
2025-08-02 21:04:43.420  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:43.492 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:43.506  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.AF89FB11AF0BF4AF36775E2C4C666C33.e.99205"}} 
2025-08-02 21:04:43.546  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fa[contains(text(),'List Set Manager')]"\n } 
2025-08-02 21:04:43.611 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //a[contains(text(),'List Set Manager')], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:43.635  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.AF89FB11AF0BF4AF36775E2C4C666C33.e.99206"}]} 
2025-08-02 21:04:43.846  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:tab_list_set_manager\\:zoneCbx",\n   "using": "css selector"\n } 
2025-08-02 21:04:43.921  WARN 1 - [ttp-epoll-4] healenium                        : Failed to find an element using locator By.cssSelector: #listManagerForm\:homepage_business\:tabViewListManager\:tab_list_set_manager\:zoneCbx 
2025-08-02 21:04:43.922  WARN 1 - [ttp-epoll-4] healenium                        : Reason: no such element: Unable to locate element: {"method":"css selector","selector":"#listManagerForm\:homepage_business\:tabViewListManager\:tab_list_set_manager\:zoneCbx"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [ee1a60c61db2d2f29d3cafef88c043a1, findElement {value=#listManagerForm\:homepage_business\:tabViewListManager\:tab_list_set_manager\:zoneCbx, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39131}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.G4...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: fdd5724a7923, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: ee1a60c61db2d2f29d3cafef88c043a1 
2025-08-02 21:04:44.019 DEBUG 1 - [ttp-epoll-4] healenium                        : [Get Reference Elements] Request. Locator: #listManagerForm\:homepage_business\:tabViewListManager\:tab_list_set_manager\:zoneCbx, Command: findElement, Url: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:44.071  WARN 1 - [ttp-epoll-4] healenium                        : Trying to heal... 
2025-08-02 21:04:44.273  WARN 1 - [ttp-epoll-4] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [ee1a60c61db2d2f29d3cafef88c043a1, findElements {value=div#listManagerForm:homepage_business:tabViewListManager:tab_list_set_manager:zoneCbx, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39131}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.G4...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: fdd5724a7923, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: ee1a60c61db2d2f29d3cafef88c043a1\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-02 21:04:44.287  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:tab_list_set_manager\\:zoneCbx",\n   "using": "css selector"\n } 
2025-08-02 21:04:44.353 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:tab_list_set_manager\:zoneCbx, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:44.382  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.AF89FB11AF0BF4AF36775E2C4C666C33.e.99213"}} 
2025-08-02 21:04:44.476  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:44.538 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:44.547  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.AF89FB11AF0BF4AF36775E2C4C666C33.e.99205"}} 
2025-08-02 21:04:44.579  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:tab_list_set_manager\\:zoneCbx_label",\n   "using": "css selector"\n } 
2025-08-02 21:04:44.645 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:tab_list_set_manager\:zoneCbx_label, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:44.667  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.AF89FB11AF0BF4AF36775E2C4C666C33.e.99211"}]} 
2025-08-02 21:04:45.866  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role='option']"\n } 
2025-08-02 21:04:45.957 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role='option'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:45.979  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.AF89FB11AF0BF4AF36775E2C4C666C33.e.99214"},{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.AF89FB11AF0BF4AF36775E2C4C666C33.e.99215"}]} 
2025-08-02 21:04:45.985  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Common Zone 05']"\n } 
2025-08-02 21:04:46.036 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='Common Zone 05'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:46.049  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.AF89FB11AF0BF4AF36775E2C4C666C33.e.99215"}]} 
2025-08-02 21:04:46.054  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:46.123 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:46.141  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.AF89FB11AF0BF4AF36775E2C4C666C33.e.99205"}} 
2025-08-02 21:04:46.211  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Common Zone 05']"\n } 
2025-08-02 21:04:46.287 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='Common Zone 05'], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:46.299  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.AF89FB11AF0BF4AF36775E2C4C666C33.e.99215"}]} 
2025-08-02 21:04:46.332  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role =  'option' and .='Common Zone 05']"\n } 
2025-08-02 21:04:46.395 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@role =  'option' and .='Common Zone 05'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:46.407  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.AF89FB11AF0BF4AF36775E2C4C666C33.e.99215"}} 
2025-08-02 21:04:46.598  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:46.673 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:46.687  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.AF89FB11AF0BF4AF36775E2C4C666C33.e.99205"}} 
2025-08-02 21:04:46.742  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[contains(@id,'listManagerForm:homepage_business:tabViewListManager:tab_list_set_manager')]\u002f\u002fspan[contains(text(),'Search')]"\n } 
2025-08-02 21:04:46.805 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[contains(@id,'listManagerForm:homepage_business:tabViewListManager:tab_list_set_manager')]//span[contains(text(),'Search')], Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:46.830  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.AF89FB11AF0BF4AF36775E2C4C666C33.e.99216"}]} 
2025-08-02 21:04:46.879  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:tab_list_set_manager\\:_listSetsTbl\\:_btnNew",\n   "using": "css selector"\n } 
2025-08-02 21:04:46.955 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:tab_list_set_manager\:_listSetsTbl\:_btnNew, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:46.974  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.AF89FB11AF0BF4AF36775E2C4C666C33.e.99217"}} 
2025-08-02 21:04:47.133  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:tab_list_set_manager\\:_listSetsTbl\\:_btnNew",\n   "using": "css selector"\n } 
2025-08-02 21:04:47.225 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:tab_list_set_manager\:_listSetsTbl\:_btnNew, Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:47.255  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.AF89FB11AF0BF4AF36775E2C4C666C33.e.99247"}} 
2025-08-02 21:04:47.292  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:47.360 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:47.372  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.AF89FB11AF0BF4AF36775E2C4C666C33.e.99205"}} 
2025-08-02 21:04:47.446  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#listManagerForm\\:homepage_business\\:tabViewListManager\\:tab_list_set_manager\\:_listSetsTbl\\:_btnNew",\n   "using": "css selector"\n } 
2025-08-02 21:04:47.525 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listManagerForm\:homepage_business\:tabViewListManager\:tab_list_set_manager\:_listSetsTbl\:_btnNew, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:47.550  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.AF89FB11AF0BF4AF36775E2C4C666C33.e.99247"}]} 
2025-08-02 21:04:47.611  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fdiv[text()='List Set Details']"\n } 
2025-08-02 21:04:47.688  WARN 1 - [ttp-epoll-4] healenium                        : Failed to find an element using locator By.xpath: //div[text()='List Set Details'] 
2025-08-02 21:04:47.689  WARN 1 - [ttp-epoll-4] healenium                        : Reason: no such element: Unable to locate element: {"method":"xpath","selector":"//div[text()='List Set Details']"}\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [ee1a60c61db2d2f29d3cafef88c043a1, findElement {value=//div[text()='List Set Details'], using=xpath}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39131}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.G4...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: fdd5724a7923, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: ee1a60c61db2d2f29d3cafef88c043a1 
2025-08-02 21:04:47.719 DEBUG 1 - [ttp-epoll-4] healenium                        : [Get Reference Elements] Request. Locator: //div[text()='List Set Details'], Command: findElement, Url: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:48.510  WARN 1 - [ttp-epoll-4] healenium                        : Trying to heal... 
2025-08-02 21:04:49.792  WARN 1 - [ttp-epoll-4] healenium                        : Error during findElement:  org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n   (Session info: MicrosoftEdge=138.0.3351.95)\n For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\n Build info: version: '4.25.0', revision: '8a8aea2337'\n System info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '23.0.2'\n Driver info: org.openqa.selenium.remote.RemoteWebDriver\n Command: [ee1a60c61db2d2f29d3cafef88c043a1, findElements {value=div#listSetDetailForm:listSet_details:j_idt194, using=css selector}]\n Capabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39131}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.G4...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: {}, se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: fdd5724a7923, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\n Session ID: ee1a60c61db2d2f29d3cafef88c043a1\n 	at java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:62)\n 	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:501)\n 	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:485)\n 	at org.openqa.selenium.remote.ErrorCodec.decode(ErrorCodec.java:167)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:138)\n 	at org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:50)\n 	at org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:190)\n 	at com.epam.healenium.healenium_proxy.command.HealeniumCommandExecutor.execute(HealeniumCommandExecutor.java:31)\n 	at org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:545)\n 	at org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\n 	at org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:381)\n 	at org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:375)\n 	at com.epam.healenium.service.HealingService.toLocator(HealingService.java:89)\n 	at com.epam.healenium.service.HealingService.lambda$findNewLocations$0(HealingService.java:68)\n 	at java.base/java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:215)\n 	at java.base/java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1709)\n 	at java.base/java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:570)\n 	at java.base/java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:560)\n 	at java.base/java.util.stream.ReduceOps$ReduceOp.evaluateSequential(ReduceOps.java:921)\n 	at java.base/java.util.stream.AbstractPipeline.evaluate(AbstractPipeline.java:265)\n 	at java.base/java.util.stream.ReferencePipeline.collect(ReferencePipeline.java:727)\n 	at com.epam.healenium.service.HealingService.findNewLocations(HealingService.java:70)\n 	at com.epam.healenium.processor.HealingProcessor.execute(HealingProcessor.java:40)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:42)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.processor.BaseProcessor.process(BaseProcessor.java:50)\n 	at com.epam.healenium.handlers.proxy.BaseHandler.findElement(BaseHandler.java:63)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.findElement(FindElementRequestGatewayFilterFactory.java:62)\n 	at com.epam.healenium.healenium_proxy.filter.FindElementRequestGatewayFilterFactory.lambda$apply$0(FindElementRequestGatewayFilterFactory.java:48)\n 	at reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:132)\n 	at reactor.core.publisher.FluxContextWrite$ContextWriteSubscriber.onNext(FluxContextWrite.java:107)\n 	at reactor.core.publisher.FluxMapFuseable$MapFuseableConditionalSubscriber.onNext(FluxMapFuseable.java:299)\n 	at reactor.core.publisher.FluxFilterFuseable$FilterFuseableConditionalSubscriber.onNext(FluxFilterFuseable.java:337)\n 	at reactor.core.publisher.Operators$BaseFluxToMonoOperator.completePossiblyEmpty(Operators.java:2097)\n 	at reactor.core.publisher.MonoCollect$CollectSubscriber.onComplete(MonoCollect.java:145)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.core.publisher.FluxPeek$PeekSubscriber.onComplete(FluxPeek.java:260)\n 	at reactor.core.publisher.FluxMap$MapSubscriber.onComplete(FluxMap.java:144)\n 	at reactor.netty.channel.FluxReceive.onInboundComplete(FluxReceive.java:413)\n 	at reactor.netty.channel.ChannelOperations.onInboundComplete(ChannelOperations.java:455)\n 	at reactor.netty.http.server.HttpServerOperations.handleLastHttpContent(HttpServerOperations.java:888)\n 	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:797)\n 	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:115)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:321)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\n 	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\n 	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:318)\n 	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\n 	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1357)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\n 	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\n 	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:868)\n 	at io.netty.channel.epoll.AbstractEpollStreamChannel$EpollStreamUnsafe.epollInReady(AbstractEpollStreamChannel.java:799)\n 	at io.netty.channel.epoll.EpollEventLoop.processReady(EpollEventLoop.java:501)\n 	at io.netty.channel.epoll.EpollEventLoop.run(EpollEventLoop.java:399)\n 	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:998)\n 	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\n 	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\n 	at java.base/java.lang.Thread.run(Thread.java:1575)\n 
2025-08-02 21:04:49.806  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002fdiv[text()='List Set Details']"\n } 
2025-08-02 21:04:49.952 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //div[text()='List Set Details'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:50.004  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.AF89FB11AF0BF4AF36775E2C4C666C33.e.99343"}} 
2025-08-02 21:04:50.085  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:50.155 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:50.182  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.AF89FB11AF0BF4AF36775E2C4C666C33.e.99344"}} 
2025-08-02 21:04:50.236  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#listSetDetailForm\\:listSet_details\\:tabViewListSet\\:listset_tab_details\\:name",\n   "using": "css selector"\n } 
2025-08-02 21:04:50.435 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listSetDetailForm\:listSet_details\:tabViewListSet\:listset_tab_details\:name, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:50.596  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.AF89FB11AF0BF4AF36775E2C4C666C33.e.99265"}]} 
2025-08-02 21:04:50.786  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:50.868 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:50.899  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.AF89FB11AF0BF4AF36775E2C4C666C33.e.99344"}} 
2025-08-02 21:04:50.950  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#listSetDetailForm\\:listSet_details\\:tabViewListSet\\:listset_tab_details\\:name",\n   "using": "css selector"\n } 
2025-08-02 21:04:51.100 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listSetDetailForm\:listSet_details\:tabViewListSet\:listset_tab_details\:name, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:51.192  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.AF89FB11AF0BF4AF36775E2C4C666C33.e.99265"}]} 
2025-08-02 21:04:51.478  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@class='pi pi-spin pi-spinner ajax-loader']"\n } 
2025-08-02 21:04:51.551 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.xpath, Locator: //*[@class='pi pi-spin pi-spinner ajax-loader'], Command: findElement, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:51.581  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Response: {"value":{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.AF89FB11AF0BF4AF36775E2C4C666C33.e.99344"}} 
2025-08-02 21:04:51.659  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "value": "#listSetDetailForm\\:listSet_details\\:tabViewListSet\\:listset_tab_details\\:swiftCbx_label",\n   "using": "css selector"\n } 
2025-08-02 21:04:51.819 DEBUG 1 - [ttp-epoll-4] healenium                        : [Save Elements] By: By.cssSelector, Locator: #listSetDetailForm\:listSet_details\:tabViewListSet\:listset_tab_details\:swiftCbx_label, Command: findElements, URL: http://************:8080/AMLUI/Modules/filtering/jsp/ListManager/Homepage.xhtml?module_name=ListManager%3BListSetManager%3BListExplorer%3BGoodGuyExplorer%3BDowJonesSettings%3BWorldCheckSettings%3BSafeTrade&app_name=SFP 
2025-08-02 21:04:51.939  INFO 1 - [ttp-epoll-4] healenium                        : Find Elements Response: {"value":[{"element-6066-11e4-a52e-4f735466cecf":"f.38ECE04EB16FEB0403C299A060B7EBBC.d.AF89FB11AF0BF4AF36775E2C4C666C33.e.99305"}]} 
2025-08-02 21:04:53.259  INFO 1 - [ttp-epoll-4] healenium                        : Find Element Request: {\n   "using": "xpath",\n   "value": "\u002f\u002f*[@role='option']"\n } 
