<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd" >
<suite name="Safe Watch Filtering" parallel="tests" thread-count="4">
    <parameter name="browserType" value="Edge" />
    <parameter name="Application" value="SWF" />
    <parameter name="loginType" value="filtering" />

    <!-- Add retry functionality for IDE execution -->
    <!--<listeners>
        <listener class-name="core.SimpleRetryTransformer" />
    </listeners>-->

    <test name="List Manager Test Cases" enabled="true" >
        <classes>
           <class name="eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC001" />
            <class name="eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC002" />
            <class name="eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC003" />
            <class name="eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC004" />
            <class name="eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC005" />
            <class name="eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC006" />
            <class name="eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC007" />
            <class name="eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC008" />
            <class name="eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC009" />
            <class name="eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC010" />
            <class name="eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC011" />
            <class name="eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC012" />
            <class name="eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC013" />
            <class name="eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC014" />
            <class name="eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC015" />
            <class name="eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC016" />
            <class name="eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC017" />
            <class name="eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC018" />
            <class name="eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC019" />
            <class name="eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC020" />
            <class name="eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC021" />
            <class name="eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC022" />
            <class name="eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC023" />

            <class name="eastnets.screening.regression.listmanager.prefiltertests.PreFilter_TC001" />
            <class name="eastnets.screening.regression.listmanager.prefiltertests.PreFilter_TC002" />
            <class name="eastnets.screening.regression.listmanager.prefiltertests.PreFilter_TC003" />
        </classes>
    </test>
    <test name="Scan Manager Test Cases" enabled="true" >
        <classes>
            <class name="eastnets.screening.regression.scanmanager.ScanManager_TC001" />
             <class name="eastnets.screening.regression.scanmanager.ScanManager_TC002" />
             <class name="eastnets.screening.regression.scanmanager.ScanManager_TC003" />
             <class name="eastnets.screening.regression.scanmanager.ScanManager_TC004" />
             <class name="eastnets.screening.regression.scanmanager.ScanManager_TC005" />
             <class name="eastnets.screening.regression.scanmanager.ScanManager_TC006" />
             <class name="eastnets.screening.regression.scanmanager.ScanManager_TC007" />
             <class name="eastnets.screening.regression.scanmanager.ScanManager_TC008" />
             <class name="eastnets.screening.regression.scanmanager.ScanManager_TC009" />
             <class name="eastnets.screening.regression.scanmanager.ScanManager_TC010" />
             <class name="eastnets.screening.regression.scanmanager.ScanManager_TC011" />
             <class name="eastnets.screening.regression.scanmanager.ScanManager_TC012" />
             <class name="eastnets.screening.regression.scanmanager.ScanManager_TC013" />
             <class name="eastnets.screening.regression.scanmanager.ScanManager_TC014" />
        </classes>
    </test>
    <test name="ISO20022 Configurations Test Cases" enabled="true" >
        <classes>
            <class name="eastnets.screening.regression.iso20022configurations.ISO20022_TC001" />
            <class name="eastnets.screening.regression.iso20022configurations.ISO20022_TC002" />
            <class name="eastnets.screening.regression.iso20022configurations.ISO20022_TC003" />
            <class name="eastnets.screening.regression.iso20022configurations.ISO20022_TC004" />
            <class name="eastnets.screening.regression.iso20022configurations.ISO20022_TC005" />
        </classes>
    </test>
    <test name="Format Manager Test Cases" enabled="true" >
        <classes>
            <class name="eastnets.screening.regression.formatmanager.FormatManager_TC001" />
            <class name="eastnets.screening.regression.formatmanager.FormatManager_TC002" />
        </classes>
    </test>
    <test name="Report Manager Test Cases" enabled="true" >
        <classes>
            <class name="eastnets.screening.regression.reports.Report_TC001" />
            <class name="eastnets.screening.regression.reports.Report_TC002" />
            <class name="eastnets.screening.regression.reports.Report_TC003" />
            <class name="eastnets.screening.regression.reports.Report_TC004" />
            <class name="eastnets.screening.regression.reports.Report_TC005" />
            <class name="eastnets.screening.regression.reports.Report_TC006" />
            <class name="eastnets.screening.regression.reports.Report_TC007" />
            <class name="eastnets.screening.regression.reports.Report_TC008" />
            <class name="eastnets.screening.regression.reports.Report_TC009" />
            <class name="eastnets.screening.regression.reports.Report_TC010" />
            <class name="eastnets.screening.regression.reports.Report_TC011" />
            <class name="eastnets.screening.regression.reports.Report_TC012" />
            <class name="eastnets.screening.regression.reports.Report_TC013" />
            <class name="eastnets.screening.regression.reports.Report_TC014" />
            <class name="eastnets.screening.regression.reports.Report_TC015" />
            <class name="eastnets.screening.regression.reports.Report_TC016" />
            <class name="eastnets.screening.regression.reports.Report_TC017" />
            <class name="eastnets.screening.regression.reports.Report_TC018" />
            <class name="eastnets.screening.regression.reports.Report_TC019" />
        </classes>
    </test>
    <test name="Detection Manager Test Cases" enabled="true" >
        <classes>
            <class name="eastnets.screening.regression.detectionmanager.DetectionManager_TC001" />
            <class name="eastnets.screening.regression.detectionmanager.DetectionManager_TC002" />
            <class name="eastnets.screening.regression.detectionmanager.DetectionManager_TC003" />
            <class name="eastnets.screening.regression.detectionmanager.DetectionManager_TC004" />
            <class name="eastnets.screening.regression.detectionmanager.DetectionManager_TC005" />
            <class name="eastnets.screening.regression.detectionmanager.DetectionManager_TC006" />
            <class name="eastnets.screening.regression.detectionmanager.DetectionManager_TC007" />
            <class name="eastnets.screening.regression.detectionmanager.DetectionManager_TC008" />
            <class name="eastnets.screening.regression.detectionmanager.DetectionManager_TC009" />
            <class name="eastnets.screening.regression.detectionmanager.DetectionManager_TC010" />
            <class name="eastnets.screening.regression.detectionmanager.DetectionManager_TC011" />
            <class name="eastnets.screening.regression.detectionmanager.DetectionManager_TC012" />
            <class name="eastnets.screening.regression.detectionmanager.DetectionManager_TC013" />
            <class name="eastnets.screening.regression.detectionmanager.DetectionManager_TC014" />
            <class name="eastnets.screening.regression.detectionmanager.DetectionManager_TC015" />
            <class name="eastnets.screening.regression.detectionmanager.DetectionManager_TC016" />
            <class name="eastnets.screening.regression.detectionmanager.DetectionManager_TC017" />
            <class name="eastnets.screening.regression.detectionmanager.DetectionManager_TC018" />
            <class name="eastnets.screening.regression.detectionmanager.DetectionManager_TC019" />
            <class name="eastnets.screening.regression.detectionmanager.DetectionManager_TC020" />
            <class name="eastnets.screening.regression.detectionmanager.DetectionManager_TC021" />
            <class name="eastnets.screening.regression.detectionmanager.DetectionManager_TC022" />
            <class name="eastnets.screening.regression.detectionmanager.DetectionManager_TC023" />
            <class name="eastnets.screening.regression.detectionmanager.DetectionManager_TC024" />
            <class name="eastnets.screening.regression.detectionmanager.DetectionManager_TC025" />
            <class name="eastnets.screening.regression.detectionmanager.DetectionManager_TC026" />
            <class name="eastnets.screening.regression.detectionmanager.DetectionManager_TC027" />
            <class name="eastnets.screening.regression.detectionmanager.DetectionManager_TC028" />
        </classes>
    </test>
    <test name="Admin Test Cases" enabled="true" >
            <classes>
                <class name="eastnets.admin.ArchiveViewerTest" />
                <class name="eastnets.admin.AuditManagerTest" />
                <class name="eastnets.admin.AdminTest" />
            </classes>
        </test>



    <!--<test name="Dow Jones Test Cases" enabled="false">
          <classes>
              <class name="eastnets.screening.regression.listmanager.dowjonestests.DowJones_TC001" />
              <class name="eastnets.screening.regression.listmanager.dowjonestests.DowJones_TC002" />
              <class name="eastnets.screening.regression.listmanager.dowjonestests.DowJones_TC003" />
              <class name="eastnets.screening.regression.listmanager.dowjonestests.DowJones_TC004" />
              <class name="eastnets.screening.regression.listmanager.dowjonestests.DowJones_TC005" />
              <class name="eastnets.screening.regression.listmanager.dowjonestests.DowJones_TC006" />
              <class name="eastnets.screening.regression.listmanager.dowjonestests.DowJones_TC007" />
              <class name="eastnets.screening.regression.listmanager.dowjonestests.DowJones_TC008" />
              <class name="eastnets.screening.regression.listmanager.dowjonestests.DowJones_TC009" />
              <class name="eastnets.screening.regression.listmanager.dowjonestests.DowJones_TC010" />
         </classes>
      </test>
      <test name="World Check Test Cases" enabled="false" >
          <classes>
              <class name="eastnets.screening.regression.listmanager.worldchecktest.WorldCheck_TC_94746" />
              <class name="eastnets.screening.regression.listmanager.worldchecktest.WorldCheck_TC_94748" />
              <class name="eastnets.screening.regression.listmanager.worldchecktest.WorldCheck_TC_94752" />
              <class name="eastnets.screening.regression.listmanager.worldchecktest.WorldCheck_TC_94753" />
              <class name="eastnets.screening.regression.listmanager.worldchecktest.WorldCheck_TC_94757" />
              <class name="eastnets.screening.regression.listmanager.worldchecktest.WorldCheck_TC_94832" />
          </classes>
      </test>-->
    <test name="DB Manager Test Cases" enabled="false" >
            <classes>
                <class name="eastnets.screening.regression.dbmanager.DBManager_TC001" />
                <class name="eastnets.screening.regression.dbmanager.DBManager_TC002" />
                <class name="eastnets.screening.regression.dbmanager.DBManager_TC003" />
                <class name="eastnets.screening.regression.dbmanager.DBManager_TC004" />
                <class name="eastnets.screening.regression.dbmanager.DBManager_TC005" />
                <class name="eastnets.screening.regression.dbmanager.DBManager_TC006" />
                <class name="eastnets.screening.regression.dbmanager.DBManager_TC007" />
                <class name="eastnets.screening.regression.dbmanager.DBManager_TC008" />
                <class name="eastnets.screening.regression.dbmanager.DBManager_TC009" />
                <class name="eastnets.screening.regression.dbmanager.DBManager_TC010" />
                <class name="eastnets.screening.regression.dbmanager.DBManager_TC011" />
                <class name="eastnets.screening.regression.dbmanager.DBManager_TC012" />
                <class name="eastnets.screening.regression.dbmanager.DBManager_TC013" />
                <class name="eastnets.screening.regression.dbmanager.DBManager_TC014" />
                <class name="eastnets.screening.regression.dbmanager.DBManager_TC015" />
                <class name="eastnets.screening.regression.dbmanager.DBManager_TC016" />
                <class name="eastnets.screening.regression.dbmanager.DBManager_TC017" />
                <class name="eastnets.screening.regression.dbmanager.DBManager_TC018" />
                <class name="eastnets.screening.regression.dbmanager.DBManager_TC019" />
                <class name="eastnets.screening.regression.dbmanager.DBManager_TC020" />
                <class name="eastnets.screening.regression.dbmanager.DBManager_TC021" />
                <class name="eastnets.screening.regression.dbmanager.DBManager_TC022" />
                <class name="eastnets.screening.regression.dbmanager.DBManager_TC121095" />
                <class name="eastnets.screening.regression.dbmanager.DBManager_TC120130" />
                <class name="eastnets.screening.regression.dbmanager.DBManager_TC121093" />


            </classes>
        </test>
</suite>


