{"uuid": "106768e9-3822-4755-81f3-8b7b1977cfdc", "historyId": "3b61debdc1ef290b8ba123eb27e940bf", "fullName": "eastnets.screening.regression.detectionmanager.DetectionManager_TC028.detectionManager_TC028", "labels": [{"name": "package", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC028"}, {"name": "testClass", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC028"}, {"name": "testMethod", "value": "detectionManager_TC028"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Detection Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC028"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Detection Manager"}], "links": [{"name": "81179", "type": "issue"}], "name": "Verify that user is able to Create alert for any open alert that is created from 'Name Checker' from the 'Alerts Section' WITHOUT adding a comment", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "related to Bug 81179: AIX - Cant creat an alert without inserting comment\n*Verify that user is able to Create alert for any open alert that is created from 'Name Checker' from the 'Alerts Section' WITHOUT adding a comment", "steps": [{"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-887010787', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@357811b8, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-887010787', officialDate='null', entry=[ListEntry{type='null', name='EntryName-887010787', firstName='EntryFirstName-887010787', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-887010787', firstName='EntryFirstName-887010787', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153885396, "stop": 1754153885396}, {"name": "Connect to Database and Check if User Profile = full-right-profile_05 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153885396, "stop": 1754153885396}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153885397, "stop": 1754153885397}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153885450, "stop": 1754153885450}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_05' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153885451, "stop": 1754153885451}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153885453, "stop": 1754153885453}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153885453, "stop": 1754153885453}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153885453, "stop": 1754153885453}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153885478, "stop": 1754153885478}, {"name": "Delete From tListSetProfile where profile_id in (8)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153885478, "stop": 1754153885478}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153885483, "stop": 1754153885483}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153885483, "stop": 1754153885483}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153885484, "stop": 1754153885484}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153885484, "stop": 1754153885484}, {"name": "Search for list by listName = ListName-887010787 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153904563, "stop": 1754153904563}, {"name": "Set zone : Common Zone 05", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153912280, "stop": 1754153912280}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153914149, "stop": 1754153914149}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153914636, "stop": 1754153914636}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153914986, "stop": 1754153914986}, {"name": "Set template name = templateName-887010787", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153915184, "stop": 1754153915184}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153917059, "stop": 1754153917059}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153917645, "stop": 1754153917645}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153918755, "stop": 1754153918755}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153919315, "stop": 1754153919315}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153976795, "stop": 1754153976795}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153976795, "stop": 1754153976795}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754154048436, "stop": 1754154048436}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754154048437, "stop": 1754154048437}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754154048437, "stop": 1754154048437}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754154048469, "stop": 1754154048469}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_05'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754154048469, "stop": 1754154048469}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754154048471, "stop": 1754154048471}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754154048471, "stop": 1754154048471}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754154048471, "stop": 1754154048471}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754154066518, "stop": 1754154066518}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754154074728, "stop": 1754154074728}, {"name": "Status for the scanned name = REP", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754154089060, "stop": 1754154089060}, {"name": "Detection ID for the scanned name = 7482", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754154089373, "stop": 1754154089373}, {"name": "Detection Status = REP", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754154093899, "stop": 1754154093899}, {"name": "Validation message = Please, enter a note before saving!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754154097110, "stop": 1754154097110}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754153885392, "stop": 1754154097111}