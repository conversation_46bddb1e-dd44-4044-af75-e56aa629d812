{"uuid": "*************-4f79-a25d-863169cc4461", "name": "eastnets.admin.AdminTest", "children": ["b4e1f653-5be2-428d-875f-1c9ca5871924", "dc910e06-e490-4b6b-b0a8-cfa5e30dc8d0", "2cd8b2ec-a4fc-4ab0-ae7a-d0184e1a5b36", "7fa0bace-795b-4377-921d-98394e0f8cbc", "9a470456-0315-40a5-9b84-1d1f817490f1", "516df2df-3d62-4a05-8954-8105fbb14da0", "*************-4e39-aa6f-075ce34c4713", "ff2f606a-0734-4ec7-bf4a-7f34bb33cbb7", "2762f497-f4cc-4183-a39c-ef09ca5912b7", "7e01ea59-fcda-448c-86cb-b574d27375b8", "8ad5628c-2166-409b-8bb6-906c54337955", "aa9e3104-4a20-4ca5-aae9-8073eebdb793", "d7facf90-dadc-4e54-a8b5-162c458363c7", "03e61cdc-4eae-475b-b39e-5a27a1822ced", "5b1f99a5-d112-4084-96a5-a4bb9190338f"], "befores": [{"name": "createNewOperator", "status": "passed", "stage": "finished", "description": "", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149977364, "stop": 1754149977364}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='sysadmin'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149977364, "stop": 1754149977364}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149977372, "stop": 1754149977372}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149977372, "stop": 1754149977372}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149977373, "stop": 1754149977373}, {"name": "Login with User Name = sysadmin and Password = manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149977520, "stop": 1754149977520}, {"name": "Operator test data = Operator{id=0, loginName='selenium-user-412049628', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone557699725', displayName='Zone (557699725)', description='Zone created with random number '557699725''}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149981973, "stop": 1754149981973}, {"name": "Group test data = Group{id=0, name='selenium-random-group-787378996', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-505104524', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone557699725', displayName='Zone (557699725)', description='Zone created with random number '557699725''}, profileRights={Administration={Archive Viewer=[], Operator Manager=[ALL], Group Manager=[ALL], Profile Manager=[ALL], Report Manager=[ALL], Zone Manager=[ALL], Audit Manager=[], Event Viewer=[]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-412049628', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone557699725', displayName='Zone (557699725)', description='Zone created with random number '557699725''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149981973, "stop": 1754149981973}, {"name": "Create new zone .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149981973, "stop": 1754149981973}, {"name": "Zone test Data = Zone{id=0, name='Zone557699725', displayName='Zone (557699725)', description='Zone created with random number '557699725''}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149981973, "stop": 1754149981973}, {"name": "Check if zone with name = 'Zone557699725' exists", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149983494, "stop": 1754149983494}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149983716, "stop": 1754149983716}, {"name": "Enter name =Zone557699725", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149989116, "stop": 1754149989116}, {"name": "Enter display Name =Zone (557699725)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149989891, "stop": 1754149989891}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149991705, "stop": 1754149991705}, {"name": "Zone doesn't exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149992238, "stop": 1754149992238}, {"name": "Click add new zone button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149992238, "stop": 1754149992238}, {"name": "Set create new zone data.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149992504, "stop": 1754149992504}, {"name": "Set name = Zone557699725", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149992504, "stop": 1754149992504}, {"name": "Set display name = Zone (557699725)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149993733, "stop": 1754149993733}, {"name": "Set description = Zone created with random number '557699725'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149994519, "stop": 1754149994519}, {"name": "Capture zone id from UI = 134", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149995733, "stop": 1754149995733}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149995733, "stop": 1754149995733}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149996973, "stop": 1754149996973}, {"name": "Enter name =Zone557699725", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150002332, "stop": 1754150002332}, {"name": "Enter display Name =Zone (557699725)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150003116, "stop": 1754150003116}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150003868, "stop": 1754150003868}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150006906, "stop": 1754150006906}, {"name": "Enter name =Zone557699725", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150012251, "stop": 1754150012251}, {"name": "Enter display Name =Zone (557699725)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150015298, "stop": 1754150015298}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150017167, "stop": 1754150017167}, {"name": "Create new profile .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150018018, "stop": 1754150018018}, {"name": "Profile test Data = Profile{id=0, name='Test-Profile-505104524', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone557699725', displayName='Zone (557699725)', description='Zone created with random number '557699725''}, profileRights={Administration={Archive Viewer=[], Operator Manager=[ALL], Group Manager=[ALL], Profile Manager=[ALL], Report Manager=[ALL], Zone Manager=[ALL], Audit Manager=[], Event Viewer=[]}}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150018019, "stop": 1754150018019}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150019376, "stop": 1754150019376}, {"name": "Set name = Test-Profile-505104524 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150024794, "stop": 1754150024794}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150025769, "stop": 1754150025769}, {"name": "Click add new profile button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150026658, "stop": 1754150026658}, {"name": "Set name = Test-Profile-505104524 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150026985, "stop": 1754150026985}, {"name": "Set description = Random profile for Selenium testing ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150029484, "stop": 1754150029484}, {"name": "Check enable checkbox to be true ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150030567, "stop": 1754150030567}, {"name": "Check write right checkbox to be Test-Profile-505104524 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150031494, "stop": 1754150031494}, {"name": "Select zone = Test-Profile-505104524 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150031897, "stop": 1754150031897}, {"name": "Add Administration item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150037913, "stop": 1754150037913}, {"name": "Click on the Administration item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150037914, "stop": 1754150037914}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150038507, "stop": 1754150038507}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150039515, "stop": 1754150039515}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150039841, "stop": 1754150039841}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150039918, "stop": 1754150039918}, {"name": "Set name = Test-Profile-505104524 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150046058, "stop": 1754150046058}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150047219, "stop": 1754150047219}, {"name": "Select profile from result view.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150048347, "stop": 1754150048348}, {"name": "Click on Administration", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150049008, "stop": 1754150049008}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150050326, "stop": 1754150050326}, {"name": "Add Archive Viewer item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150051484, "stop": 1754150051484}, {"name": "Click on the Archive Viewer item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150051484, "stop": 1754150051484}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150051963, "stop": 1754150051963}, {"name": "Add Operator Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150053341, "stop": 1754150053341}, {"name": "Click on the Operator Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150053341, "stop": 1754150053341}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150053763, "stop": 1754150053763}, {"name": "Add Group Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150055262, "stop": 1754150055262}, {"name": "Click on the Group Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150055263, "stop": 1754150055263}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150057325, "stop": 1754150057325}, {"name": "Add Profile Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150058801, "stop": 1754150058801}, {"name": "Click on the Profile Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150058801, "stop": 1754150058801}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150059302, "stop": 1754150059302}, {"name": "Add Report Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150060764, "stop": 1754150060764}, {"name": "Click on the Report Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150060764, "stop": 1754150060764}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150061175, "stop": 1754150061175}, {"name": "Add Zone Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150062642, "stop": 1754150062642}, {"name": "Click on the Zone Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150062642, "stop": 1754150062642}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150063050, "stop": 1754150063050}, {"name": "Add Audit Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150064523, "stop": 1754150064523}, {"name": "Click on the Audit Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150064523, "stop": 1754150064523}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150065079, "stop": 1754150065079}, {"name": "Add Event Viewer item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150066523, "stop": 1754150066523}, {"name": "Click on the Event Viewer item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150066523, "stop": 1754150066523}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150066898, "stop": 1754150066898}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150068517, "stop": 1754150068517}, {"name": "Accept <PERSON><PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150068983, "stop": 1754150068983}, {"name": "Click on Administration", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150070203, "stop": 1754150070203}, {"name": "Select Module Rights.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150071045, "stop": 1754150071045}, {"name": "Processing module 'Operator Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150073046, "stop": 1754150073046}, {"name": "Click on Operator Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150073047, "stop": 1754150073047}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150074056, "stop": 1754150074056}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150074713, "stop": 1754150074713}, {"name": "Processing module 'Group Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150078007, "stop": 1754150078007}, {"name": "<PERSON>lick on Group Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150078007, "stop": 1754150078007}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150079526, "stop": 1754150079526}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150079835, "stop": 1754150079835}, {"name": "Processing module 'Profile Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150083491, "stop": 1754150083491}, {"name": "Click on Profile Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150083491, "stop": 1754150083491}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150084570, "stop": 1754150084570}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150084860, "stop": 1754150084860}, {"name": "Processing module 'Report Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150087568, "stop": 1754150087568}, {"name": "Click on Report Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150087568, "stop": 1754150087568}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150089096, "stop": 1754150089096}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150089426, "stop": 1754150089426}, {"name": "Processing module 'Zone Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150091773, "stop": 1754150091773}, {"name": "Click on Zone Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150091773, "stop": 1754150091773}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150092801, "stop": 1754150092801}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150093133, "stop": 1754150093133}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150096310, "stop": 1754150096310}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150096626, "stop": 1754150096626}, {"name": "Check if profile exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150097748, "stop": 1754150097748}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150097748, "stop": 1754150097748}, {"name": "Set name = Test-Profile-505104524 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150103823, "stop": 1754150103823}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150104821, "stop": 1754150104821}, {"name": "Profile exist = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150105603, "stop": 1754150105603}, {"name": "Create new operator.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150105603, "stop": 1754150105603}, {"name": "Operator test Data = Operator{id=0, loginName='selenium-user-412049628', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone557699725', displayName='Zone (557699725)', description='Zone created with random number '557699725''}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150105603, "stop": 1754150105603}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150107680, "stop": 1754150107680}, {"name": "Set login name = selenium-user-412049628", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150113147, "stop": 1754150113147}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150114025, "stop": 1754150114025}, {"name": "Click Create new operator button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150115033, "stop": 1754150115033}, {"name": "Set login name = selenium-user-412049628", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150115360, "stop": 1754150115360}, {"name": "Set first name = Random", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150117745, "stop": 1754150117745}, {"name": "Set last name = User", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150118937, "stop": 1754150118937}, {"name": "Set password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150119597, "stop": 1754150119597}, {"name": "Set confirm password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150120541, "stop": 1754150120541}, {"name": "Set enable flag = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150121602, "stop": 1754150121602}, {"name": "Set reset flag = false", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150122631, "stop": 1754150122631}, {"name": "Select zone  = Zone (557699725)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150124260, "stop": 1754150124260}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150132446, "stop": 1754150132446}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150132766, "stop": 1754150132766}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150140724, "stop": 1754150140724}, {"name": "Set login name = selenium-user-412049628", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150146181, "stop": 1754150146181}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150147238, "stop": 1754150147238}, {"name": "Create new group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150148360, "stop": 1754150148360}, {"name": "Group test Data = Group{id=0, name='selenium-random-group-787378996', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-505104524', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone557699725', displayName='Zone (557699725)', description='Zone created with random number '557699725''}, profileRights={Administration={Archive Viewer=[], Operator Manager=[ALL], Group Manager=[ALL], Profile Manager=[ALL], Report Manager=[ALL], Zone Manager=[ALL], Audit Manager=[], Event Viewer=[]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-412049628', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone557699725', displayName='Zone (557699725)', description='Zone created with random number '557699725''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150148360, "stop": 1754150148360}], "attachments": [], "parameters": [], "start": 1754149976914, "stop": 1754150192663}], "afters": [], "start": 1754149012846, "stop": 1754153238526}