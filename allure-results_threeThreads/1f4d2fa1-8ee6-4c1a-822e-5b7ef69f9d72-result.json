{"uuid": "1f4d2fa1-8ee6-4c1a-822e-5b7ef69f9d72", "historyId": "f22afc7b7cbc88e8a8f443201b15231e", "fullName": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC021.listManager_TC021", "labels": [{"name": "package", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC021"}, {"name": "testClass", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC021"}, {"name": "testMethod", "value": "listManager_TC021"}, {"name": "parentSuite", "value": "Failed Tests Rerun Suite"}, {"name": "suite", "value": "<PERSON><PERSON> Failed Tests"}, {"name": "subSuite", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC021"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "List Manager"}], "links": [], "name": "Verify List set configuration - Assign clean alert - Assign Auto create alert - Assign clean & External alert", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify List set configuration - Assign clean alert - Assign Auto create alert - Assign clean & External alert", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155265506, "stop": 1754155265506}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-02'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155265506, "stop": 1754155265506}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155265514, "stop": 1754155265514}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155265514, "stop": 1754155265514}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155265514, "stop": 1754155265514}, {"name": "Login with User Name = operator-02 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155265683, "stop": 1754155265683}, {"name": "Create swift-code, black list and list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155271012, "stop": 1754155271012}, {"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-62987039', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@751ea31d, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-62987039', officialDate='null', entry=[ListEntry{type='null', name='EntryName-62987039', firstName='EntryFirstName-62987039', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-62987039', firstName='EntryFirstName-62987039', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155271015, "stop": 1754155271015}, {"name": "Connect to Database and Check if User Profile = full-right-profile_002 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155271015, "stop": 1754155271015}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155271016, "stop": 1754155271016}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155271060, "stop": 1754155271060}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_002' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155271060, "stop": 1754155271060}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155271062, "stop": 1754155271062}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155271062, "stop": 1754155271062}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155271063, "stop": 1754155271063}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155271090, "stop": 1754155271090}, {"name": "Delete From tListSetProfile where profile_id in (5)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155271090, "stop": 1754155271090}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155271094, "stop": 1754155271094}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155271094, "stop": 1754155271094}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155271094, "stop": 1754155271094}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155271094, "stop": 1754155271094}, {"name": "Search for list by listName = ListName-62987039 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155291746, "stop": 1754155291746}, {"name": "Set zone : Common Zone 02", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155300955, "stop": 1754155300955}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155303140, "stop": 1754155303140}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155303754, "stop": 1754155303754}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155304186, "stop": 1754155304186}, {"name": "Set template name = templateName-62987039", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155304455, "stop": 1754155304455}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155306625, "stop": 1754155306625}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155307317, "stop": 1754155307317}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155308643, "stop": 1754155308643}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155309266, "stop": 1754155309266}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155344957, "stop": 1754155344957}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155344957, "stop": 1754155344957}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155409525, "stop": 1754155409525}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155409525, "stop": 1754155409525}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155409525, "stop": 1754155409525}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155409552, "stop": 1754155409552}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_002'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155409552, "stop": 1754155409552}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155409556, "stop": 1754155409556}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155409557, "stop": 1754155409557}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155409557, "stop": 1754155409557}, {"name": "Validation message = Syntax is valid!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155433693, "stop": 1754155433693}, {"name": "Validation message = Syntax is valid!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155433694, "stop": 1754155433694}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155437016, "stop": 1754155437016}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155437016, "stop": 1754155437016}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155464397, "stop": 1754155464397}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155499396, "stop": 1754155499396}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155533715, "stop": 1754155533715}, {"name": "Select list set by name.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155558319, "stop": 1754155558319}, {"name": "Validation message = Good guy successfully created!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155568555, "stop": 1754155568555}, {"name": "Alert message = Good guy successfully created!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155568555, "stop": 1754155568555}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155594372, "stop": 1754155594372}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155594372, "stop": 1754155594372}, {"name": "Status for the scanned name = CLEAN", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155607440, "stop": 1754155607440}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155607440, "stop": 1754155607440}, {"name": "Status for the scanned name = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155619728, "stop": 1754155619728}, {"name": "Investigator for the scanned name = operator-02", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155619965, "stop": 1754155619965}, {"name": "Investigator = operator-02", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155619965, "stop": 1754155619965}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155619965, "stop": 1754155619965}, {"name": "Status for the scanned name = REP", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155636858, "stop": 1754155636858}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155636858, "stop": 1754155636858}, {"name": "Status for the scanned name = EXT", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155650464, "stop": 1754155650464}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155650464, "stop": 1754155650464}, {"name": "Status for the scanned name = ACC", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155663756, "stop": 1754155663756}], "attachments": [], "parameters": [], "start": 1754155265104, "stop": 1754155666025}