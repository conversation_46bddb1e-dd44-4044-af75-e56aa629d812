{"uuid": "5433e4d9-9808-43da-8ded-afc5f0d083ef", "historyId": "a73f6f36796a4d1d5c8fce06a3354cbd", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC009.scanManager_TC009", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC009"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC009"}, {"name": "testMethod", "value": "scanManager_TC009"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC009"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is able to Print an alert report in RTF format", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to Print an alert report in RTF format", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142169632, "stop": 1754142169632}, {"name": "Validation message = File sent to the server for processing with id [2876]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142184518, "stop": 1754142184518}, {"name": "Alert Message = File sent to the server for processing with id [2876]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142184518, "stop": 1754142184518}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142185983, "stop": 1754142185983}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142194704, "stop": 1754142194704}, {"name": "Detection ID = 7229", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142197936, "stop": 1754142197936}, {"name": "Start Exporting Violation With Print Scope all And Document Type RTF", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142197951, "stop": 1754142197951}, {"name": "Click on close dialog button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142218685, "stop": 1754142218688}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "eastnets.screening.entity.Report@5a33cf0a"}], "start": 1754142169379, "stop": 1754142249867}