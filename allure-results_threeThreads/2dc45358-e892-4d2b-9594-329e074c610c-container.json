{"uuid": "2dc45358-e892-4d2b-9594-329e074c610c", "name": "Scan Manager Test Cases", "children": ["5a2f68df-8a38-4f20-93a8-d3a0b5b4c87b", "3fdf4ba7-e685-454c-af77-439642a76465", "6c601574-493c-4be1-9b26-7057c7b67f42", "d9391848-b438-4605-82f9-14b4a2bb138b", "a0f3f9bb-b29e-4c9f-9ce2-9abcdde2baa3", "12a80dcd-4665-4e3b-bce7-34fffd7e23c6", "018087ea-b0f5-4379-8ad5-e241526f589c", "b6d432b5-66a5-4b79-8cef-ac8f8e222a84", "ad6f00b7-dde1-497c-a9a7-b8ccf2178672", "fb7c356c-5ef3-4c53-859d-c6eddcb61556", "9a068318-388d-414c-a609-d1af733783c0", "1e0ce550-16cf-4449-ae21-00e43084cde6", "5433e4d9-9808-43da-8ded-afc5f0d083ef", "6876b946-df28-4a6d-911e-6031c4221a2e", "95015f7b-992d-4a4a-b285-a3cb1c60ba09", "c47474a9-0ce5-4254-9632-de4c48d6f1c1", "136cc686-23cd-433a-84cd-dd185bc92dcb"], "befores": [{"name": "Setting up selenium web driver before each class run", "status": "passed", "stage": "finished", "description": "Setting up selenium web driver before each class run", "steps": [{"name": "Initialize Selenium driver before tests' Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141044567, "stop": 1754141044567}, {"name": "Selenium webDriver was initialized successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141052264, "stop": 1754141052264}], "attachments": [], "parameters": [], "start": 1754141044566, "stop": 1754141052264}], "afters": [{"name": "Quitting selenium driver after each class run", "status": "passed", "stage": "finished", "description": "Quitting selenium driver after each class run", "steps": [{"name": "Closing selenium WebDriver after Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142444760, "stop": 1754142444760}], "attachments": [], "parameters": [], "start": 1754142444760, "stop": 1754142445177}], "start": 1754141044254, "stop": 1754142445178}