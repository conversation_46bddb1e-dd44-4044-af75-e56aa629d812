{"uuid": "fb7c356c-5ef3-4c53-859d-c6eddcb61556", "historyId": "53af5d7e30c68caa1bc374572fd0445f", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC008.scanManager_TC008", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC008"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC008"}, {"name": "testMethod", "value": "scanManager_TC008"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC008"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is able to add an entity as <PERSON> Guy from the results tab for a scanned file with Custom Format type.", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to add an entity as <PERSON> Guy from the results tab for a scanned file with Custom Format type.", "steps": [{"name": "RJE File Content= EntryName1, EntryName1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141944595, "stop": 1754141944595}, {"name": "Start coping data from sample file to another file to be used.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141944595, "stop": 1754141944595}, {"name": "Successfully wrote to the file.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141944599, "stop": 1754141944599}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141981270, "stop": 1754141981270}, {"name": "Validation message = File sent to the server for processing with id [2872]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141997976, "stop": 1754141997976}, {"name": "Alert Message = File sent to the server for processing with id [2872]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141997977, "stop": 1754141997977}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141999219, "stop": 1754141999219}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142007748, "stop": 1754142007748}, {"name": "Detection ID = 7221", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142008825, "stop": 1754142008825}, {"name": "Actual validation Message = Good guy successfully created!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142012658, "stop": 1754142012658}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "FileScan{filePath='/src/test/resources/testDataFiles/fileScanTD/ScanFile.txt', format='Custom Format File', encoding='null', enList=null, result='null', rank='null', detectVessels='true', detectCountries='true', createAlertsAutomatically='false'}"}], "start": 1754141944265, "stop": 1754142013311}