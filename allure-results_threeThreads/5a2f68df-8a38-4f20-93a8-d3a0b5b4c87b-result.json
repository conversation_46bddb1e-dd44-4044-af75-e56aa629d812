{"uuid": "5a2f68df-8a38-4f20-93a8-d3a0b5b4c87b", "historyId": "fb1a7c71534fc71f47eab95b765752f6", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC001.scanManager_TC001", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC001"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC001"}, {"name": "testMethod", "value": "scanManager_TC001"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC001"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is able to scan a swift rje record without creating alerts automatically option checked and take decision.", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to scan a swift rje record without creating alerts automatically option checked and take decision.", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141358515, "stop": 1754141358515}, {"name": "Validation message = File sent to the server for processing with id [2862]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141371301, "stop": 1754141371301}, {"name": "Alert Message = File sent to the server for processing with id [2862]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141371301, "stop": 1754141371301}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141372504, "stop": 1754141372504}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141382332, "stop": 1754141382332}, {"name": "Detection ID = 7161", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141384362, "stop": 1754141384362}, {"name": "Detection Status = REP", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141389631, "stop": 1754141389631}, {"name": "Detection Status = REP", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141396195, "stop": 1754141396195}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "FileScan{filePath='/RJE_Swift.txt', format='SWIFT RJE Records', encoding='null', enList=null, result='null', rank='null', detectVessels='true', detectCountries='true', createAlertsAutomatically='false'}"}], "start": 1754141358514, "stop": 1754141412715}