{"uuid": "018087ea-b0f5-4379-8ad5-e241526f589c", "historyId": "f724d3c9e1639024bef130abe66fe7ec", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC006.scanManager_TC006", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC006"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC006"}, {"name": "testMethod", "value": "scanManager_TC006"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC006"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is not able to add a Good Guy for a detection by clicking accept as shared button for non-shared black list", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is not able to add a Good Guy for a detection by clicking accept as shared button for non-shared black list", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141831723, "stop": 1754141831723}, {"name": "Status for the scanned name = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141845414, "stop": 1754141845414}, {"name": "<PERSON>ose scanned name from result table يا<PERSON><PERSON> عمر", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141845414, "stop": 1754141845414}, {"name": "Actual validation Message = Cannot create a shared Good Guy against the non-shared black list ListName-477782242'.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141852225, "stop": 1754141852225}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754141831717, "stop": 1754141852776}