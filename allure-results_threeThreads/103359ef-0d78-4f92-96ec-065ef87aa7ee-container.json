{"uuid": "103359ef-0d78-4f92-96ec-065ef87aa7ee", "name": "ISO20022 Configurations Test Cases", "children": ["5c1b501a-6182-4f17-a3c1-2976f2e57ec6", "3ac5f213-ee3c-4bd5-9175-50b42243443f", "6648f1c0-0cee-4467-8acf-07eabe79833a", "fa0b11b4-c47c-439e-9232-4a43f28744e2", "a46fff4a-1a05-4cfe-b519-6464a6964137"], "befores": [{"name": "Setting up selenium web driver before each class run", "status": "passed", "stage": "finished", "description": "Setting up selenium web driver before each class run", "steps": [{"name": "Initialize Selenium driver before tests' Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141044266, "stop": 1754141044266}, {"name": "Selenium webDriver was initialized successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141052253, "stop": 1754141052253}], "attachments": [], "parameters": [], "start": 1754141044260, "stop": 1754141052253}], "afters": [{"name": "Quitting selenium driver after each class run", "status": "passed", "stage": "finished", "description": "Quitting selenium driver after each class run", "steps": [{"name": "Closing selenium WebDriver after Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141837465, "stop": 1754141837465}], "attachments": [], "parameters": [], "start": 1754141837464, "stop": 1754141838011}], "start": 1754141044254, "stop": 1754141838012}