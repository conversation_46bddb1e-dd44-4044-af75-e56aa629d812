{"uuid": "d404bf7f-7446-42bd-a63e-40fb7f11627d", "historyId": "f56da0e4008bbe8f23f23952f283da46", "fullName": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC022.listManager_TC022", "labels": [{"name": "package", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC022"}, {"name": "testClass", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC022"}, {"name": "testMethod", "value": "listManager_TC022"}, {"name": "parentSuite", "value": "Failed Tests Rerun Suite"}, {"name": "suite", "value": "<PERSON><PERSON> Failed Tests"}, {"name": "subSuite", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC022"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "List Manager"}], "links": [], "name": "Verify GPI Lables as conditions and Add SERV_TYPE_ID (SLA_ID) and UETR as violation filter and make sure that the detection has External status.", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify GPI Lables as conditions and Add SERV_TYPE_ID (SLA_ID) and UETR as violation filter and make sure that the detection has External status.", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155666383, "stop": 1754155666383}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-02'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155666383, "stop": 1754155666383}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155666389, "stop": 1754155666389}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155666389, "stop": 1754155666389}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155666389, "stop": 1754155666389}, {"name": "Login with User Name = operator-02 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155666557, "stop": 1754155666557}, {"name": "Create swift-code, black list and list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155670620, "stop": 1754155670620}, {"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-535165720', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@5259f1e5, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-535165720', officialDate='null', entry=[ListEntry{type='null', name='EntryName-535165720', firstName='EntryFirstName-535165720', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-535165720', firstName='EntryFirstName-535165720', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155670623, "stop": 1754155670623}, {"name": "Connect to Database and Check if User Profile = full-right-profile_002 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155670623, "stop": 1754155670623}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155670624, "stop": 1754155670624}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155670673, "stop": 1754155670673}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_002' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155670674, "stop": 1754155670674}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155670676, "stop": 1754155670676}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155670676, "stop": 1754155670676}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155670676, "stop": 1754155670676}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155670696, "stop": 1754155670696}, {"name": "Delete From tListSetProfile where profile_id in (5)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155670696, "stop": 1754155670696}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155670701, "stop": 1754155670701}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155670701, "stop": 1754155670701}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155670701, "stop": 1754155670701}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155670701, "stop": 1754155670701}, {"name": "Search for list by listName = ListName-535165720 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155690727, "stop": 1754155690727}, {"name": "Set zone : Common Zone 02", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155699967, "stop": 1754155699967}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155702076, "stop": 1754155702076}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155702604, "stop": 1754155702604}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155703040, "stop": 1754155703040}, {"name": "Set template name = templateName-535165720", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155703261, "stop": 1754155703261}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155705085, "stop": 1754155705085}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155705920, "stop": 1754155705920}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155706973, "stop": 1754155706973}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155707547, "stop": 1754155707547}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155739219, "stop": 1754155739219}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155739219, "stop": 1754155739219}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155796078, "stop": 1754155796078}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155796078, "stop": 1754155796078}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155796078, "stop": 1754155796078}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155796105, "stop": 1754155796105}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_002'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155796105, "stop": 1754155796105}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155796108, "stop": 1754155796108}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155796108, "stop": 1754155796108}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155796109, "stop": 1754155796109}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155811512, "stop": 1754155811512}, {"name": "Validation message = Syntax is valid!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155841457, "stop": 1754155841457}, {"name": "Validation message = Syntax is valid!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155841458, "stop": 1754155841458}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155844195, "stop": 1754155844195}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155844195, "stop": 1754155844195}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155844197, "stop": 1754155844197}, {"name": "Validation message = File sent to the server for processing with id [2943]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155855458, "stop": 1754155855458}, {"name": "Alert Message = File sent to the server for processing with id [2943]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155855458, "stop": 1754155855458}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155855934, "stop": 1754155855934}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155863945, "stop": 1754155863945}, {"name": "Detection ID = 7488", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155864702, "stop": 1754155864702}, {"name": "Status detection = External", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155864903, "stop": 1754155864903}, {"name": "Unmatched roles = SERV_TYPE_ID <> '001'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155866201, "stop": 1754155866201}, {"name": "Validation message = Syntax is valid!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155885591, "stop": 1754155885591}, {"name": "Validation message = Syntax is valid!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155885591, "stop": 1754155885591}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155887627, "stop": 1754155887627}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155887627, "stop": 1754155887627}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155887627, "stop": 1754155887627}, {"name": "Validation message = File sent to the server for processing with id [2944]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155897282, "stop": 1754155897282}, {"name": "Alert Message = File sent to the server for processing with id [2944]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155897282, "stop": 1754155897282}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155898065, "stop": 1754155898065}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155905190, "stop": 1754155905190}, {"name": "Detection ID = 7489", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155906012, "stop": 1754155906012}, {"name": "Status detection = External", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155906205, "stop": 1754155906205}, {"name": "Unmatched roles = UETR <> '8E0FE365-A88E-426B-8484-4FB7FEE92742'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754155907250, "stop": 1754155907250}], "attachments": [], "parameters": [], "start": 1754155666042, "stop": 1754155909191}