{"uuid": "eccec90b-75e8-431b-a56f-a149835b38d9", "historyId": "35f85912395adc0ac69cdfc6a9bd434b", "fullName": "eastnets.screening.regression.formatmanager.FormatManager_TC002.formatManager_TC002", "labels": [{"name": "package", "value": "eastnets.screening.regression.formatmanager.FormatManager_TC002"}, {"name": "testClass", "value": "eastnets.screening.regression.formatmanager.FormatManager_TC002"}, {"name": "testMethod", "value": "formatManager_TC002"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Format Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.formatmanager.FormatManager_TC002"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-3(38)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON><PERSON>"}, {"name": "tag", "value": "Regression"}], "links": [], "name": "verify that the unmatched rule appears in the detection details from the results tab", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Format manager - Additional context variables \n* verify that the additional context variables appear when adding a good guy\n* verify the out of context result based on the violation filter added\n* verify the suspected record selection in drop down list\n* verify that the unmatched rule appears in the detection details from the results tab", "steps": [{"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-220274410', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@1e9e3301, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-220274410', officialDate='null', entry=[ListEntry{type='null', name='EntryName-220274410', firstName='EntryFirstName-220274410', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-220274410', firstName='EntryFirstName-220274410', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142100259, "stop": 1754142100259}, {"name": "Connect to Database and Check if User Profile = full-right-profile_06 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142100260, "stop": 1754142100260}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142100260, "stop": 1754142100260}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142100310, "stop": 1754142100310}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_06' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142100310, "stop": 1754142100310}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142100313, "stop": 1754142100313}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142100313, "stop": 1754142100313}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142100314, "stop": 1754142100314}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142100338, "stop": 1754142100338}, {"name": "Delete From tListSetProfile where profile_id in (9)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142100338, "stop": 1754142100338}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142100346, "stop": 1754142100346}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142100346, "stop": 1754142100346}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142100347, "stop": 1754142100347}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142100347, "stop": 1754142100347}, {"name": "Search for list by listName = ListName-220274410 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142122445, "stop": 1754142122445}, {"name": "Set zone : Common Zone 06", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142134057, "stop": 1754142134057}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142136294, "stop": 1754142136294}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142136801, "stop": 1754142136801}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142137230, "stop": 1754142137230}, {"name": "Set template name = templateName-220274410", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142137420, "stop": 1754142137420}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142139234, "stop": 1754142139234}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142140227, "stop": 1754142140227}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142141409, "stop": 1754142141409}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142142047, "stop": 1754142142047}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142161245, "stop": 1754142161245}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142161245, "stop": 1754142161245}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142228730, "stop": 1754142228730}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142228730, "stop": 1754142228730}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142228731, "stop": 1754142228731}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142228758, "stop": 1754142228758}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_06'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142228758, "stop": 1754142228758}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142228761, "stop": 1754142228761}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142228761, "stop": 1754142228761}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142228761, "stop": 1754142228761}, {"name": "Validation message = Syntax is valid!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142253077, "stop": 1754142253077}, {"name": "Validation message = Syntax is valid!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142253077, "stop": 1754142253077}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142269867, "stop": 1754142269867}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142269867, "stop": 1754142269867}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142288718, "stop": 1754142288718}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142325280, "stop": 1754142325280}, {"name": "Validation message = File sent to the server for processing with id [2880]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142339641, "stop": 1754142339641}, {"name": "Alert Message = File sent to the server for processing with id [2880]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142339641, "stop": 1754142339641}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142340935, "stop": 1754142340935}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142351442, "stop": 1754142351442}, {"name": "Detection ID = 7235", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142352667, "stop": 1754142352667}, {"name": "Actual validation Message = Good guy successfully created!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142360845, "stop": 1754142360845}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142361565, "stop": 1754142361565}, {"name": "Validation message = File sent to the server for processing with id [2881]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142373324, "stop": 1754142373324}, {"name": "Alert Message = File sent to the server for processing with id [2881]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142373324, "stop": 1754142373324}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142374550, "stop": 1754142374550}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142382717, "stop": 1754142382717}, {"name": "Detection ID = 7240", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142384601, "stop": 1754142384601}, {"name": "Status detection = Accepted", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142384851, "stop": 1754142384851}, {"name": "Status detection = External", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142384940, "stop": 1754142384940}, {"name": "Status detection = Reported", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142385082, "stop": 1754142385082}, {"name": "Status detection = Accepted", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142385351, "stop": 1754142385351}, {"name": "Status detection = External", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142385448, "stop": 1754142385448}, {"name": "Status detection = Reported", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142385497, "stop": 1754142385497}, {"name": "Status detection = Accepted", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142385769, "stop": 1754142385769}, {"name": "Status detection = External", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142385870, "stop": 1754142385870}, {"name": "Status detection = Reported", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142385960, "stop": 1754142385960}, {"name": "Unmatched roles = CN <> '146'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142387873, "stop": 1754142387873}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754142100255, "stop": 1754142388169}