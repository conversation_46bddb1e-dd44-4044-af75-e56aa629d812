{"uuid": "6c601574-493c-4be1-9b26-7057c7b67f42", "historyId": "6da7954c335bba2c9b60e14680184611", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC001.scanManager_TC001", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC001"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC001"}, {"name": "testMethod", "value": "scanManager_TC001"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC001"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is able to scan a custom format file without creating alerts option checked and take decision. ", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to scan a custom format file without creating alerts option checked and take decision. ", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141522610, "stop": 1754141522610}, {"name": "Validation message = File sent to the server for processing with id [2864]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141537369, "stop": 1754141537369}, {"name": "Alert Message = File sent to the server for processing with id [2864]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141537369, "stop": 1754141537369}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141539181, "stop": 1754141539181}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141548423, "stop": 1754141548423}, {"name": "Detection ID = 7200", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141550687, "stop": 1754141550687}, {"name": "Detection Status = REP", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141555444, "stop": 1754141555444}, {"name": "Detection Status = REP", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141559245, "stop": 1754141559245}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "FileScan{filePath='/Generic.txt', format='Custom Format File', encoding='null', enList=null, result='null', rank='null', detectVessels='true', detectCountries='true', createAlertsAutomatically='false'}"}], "start": 1754141480236, "stop": 1754141565040}