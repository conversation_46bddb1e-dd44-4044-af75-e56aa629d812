{"uuid": "a0f3f9bb-b29e-4c9f-9ce2-9abcdde2baa3", "historyId": "42eb3c2d0f8cf06d96aec17e5620434", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC004.ScanManager_TC004", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC004"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC004"}, {"name": "testMethod", "value": "ScanManager_TC004"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC004"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Validate that user is able to Print a detection created from 'File Scan' from Result Tab.", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Validate that user is able to Print a detection created from 'File Scan' from Result Tab.", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141670234, "stop": 1754141670234}, {"name": "Validation message = File sent to the server for processing with id [2866]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141684152, "stop": 1754141684152}, {"name": "Alert Message = File sent to the server for processing with id [2866]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141684152, "stop": 1754141684152}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141685447, "stop": 1754141685447}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141695062, "stop": 1754141695062}, {"name": "Detection ID = 7203", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141696498, "stop": 1754141696498}, {"name": "response.getStatus()200", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141730413, "stop": 1754141730413}, {"name": "UTF-8", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141730414, "stop": 1754141730414}, {"name": "1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141730414, "stop": 1754141730414}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754141670207, "stop": 1754141730415}