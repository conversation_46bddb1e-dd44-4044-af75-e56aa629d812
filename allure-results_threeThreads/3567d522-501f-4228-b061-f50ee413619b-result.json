{"uuid": "3567d522-501f-4228-b061-f50ee413619b", "historyId": "d4918ff60c00a9240dd6b8716ee9b63", "fullName": "eastnets.screening.regression.reports.Report_TC010.report_TC010", "labels": [{"name": "package", "value": "eastnets.screening.regression.reports.Report_TC010"}, {"name": "testClass", "value": "eastnets.screening.regression.reports.Report_TC010"}, {"name": "testMethod", "value": "report_TC010"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Report Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.reports.Report_TC010"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-3(38)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "tag", "value": "Reports"}], "links": [], "name": "Verify Detections Grouped By Status", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify Detections Grouped By Status", "steps": [{"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-349722881', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@4381e04f, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-349722881', officialDate='null', entry=[ListEntry{type='null', name='EntryName-349722881', firstName='EntryFirstName-349722881', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-349722881', firstName='EntryFirstName-349722881', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149775190, "stop": 1754149775190}, {"name": "Connect to Database and Check if User Profile = full-right-profile_08 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149775191, "stop": 1754149775191}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149775191, "stop": 1754149775191}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149775241, "stop": 1754149775241}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_08' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149775242, "stop": 1754149775242}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149775244, "stop": 1754149775244}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149775244, "stop": 1754149775244}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149775245, "stop": 1754149775245}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149775270, "stop": 1754149775270}, {"name": "Delete From tListSetProfile where profile_id in (11)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149775270, "stop": 1754149775270}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149775273, "stop": 1754149775273}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149775273, "stop": 1754149775273}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149775273, "stop": 1754149775273}, {"name": "Number of effected rows = 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149775273, "stop": 1754149775273}, {"name": "Search for list by listName = ListName-349722881 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149832925, "stop": 1754149832925}, {"name": "Set zone : Common Zone 08", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149842531, "stop": 1754149842531}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149844676, "stop": 1754149844676}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149845447, "stop": 1754149845447}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149845906, "stop": 1754149845906}, {"name": "Set template name = templateName-349722881", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149846234, "stop": 1754149846234}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149849522, "stop": 1754149849522}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149850704, "stop": 1754149850704}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149852554, "stop": 1754149852554}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149854187, "stop": 1754149854187}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149894877, "stop": 1754149894877}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149894877, "stop": 1754149894877}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150000975, "stop": 1754150000975}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150000975, "stop": 1754150000975}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150000975, "stop": 1754150000975}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150001008, "stop": 1754150001008}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_08'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150001008, "stop": 1754150001008}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150001011, "stop": 1754150001011}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150001011, "stop": 1754150001011}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150001012, "stop": 1754150001012}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150026958, "stop": 1754150026958}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150032749, "stop": 1754150032749}, {"name": "Validation message = File sent to the server for processing with id [2906]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150046042, "stop": 1754150046043}, {"name": "Alert Message = File sent to the server for processing with id [2906]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150046043, "stop": 1754150046043}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150047311, "stop": 1754150047311}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150058937, "stop": 1754150058937}, {"name": "Detection ID = 7286", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150060763, "stop": 1754150060763}, {"name": "Report status is: Done", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150083942, "stop": 1754150083942}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754149775182, "stop": 1754150119789}