{"uuid": "1e0ce550-16cf-4449-ae21-00e43084cde6", "historyId": "bc89b54d9de53b4e72cab190aeb39ace", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC009.scanManager_TC009", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC009"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC009"}, {"name": "testMethod", "value": "scanManager_TC009"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC009"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is able to Print an alert report in Excel format", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to Print an alert report in Excel format", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142092041, "stop": 1754142092041}, {"name": "Validation message = File sent to the server for processing with id [2874]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142107020, "stop": 1754142107020}, {"name": "Alert Message = File sent to the server for processing with id [2874]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142107020, "stop": 1754142107020}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142108141, "stop": 1754142108141}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142116921, "stop": 1754142116921}, {"name": "Detection ID = 7226", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142118903, "stop": 1754142118903}, {"name": "Start Exporting Violation With Print Scope all And Document Type Excel", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142118920, "stop": 1754142118920}, {"name": "Click on close dialog button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142129025, "stop": 1754142129025}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "eastnets.screening.entity.Report@d16cf2c"}], "start": 1754142091186, "stop": 1754142160103}