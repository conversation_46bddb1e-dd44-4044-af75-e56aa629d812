{"uuid": "721dfccb-bf7a-47df-a9e2-ce54da5f173d", "historyId": "474be798a03262fda831063cab3f280d", "fullName": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC018.ListManager_TC018", "labels": [{"name": "package", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC018"}, {"name": "testClass", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC018"}, {"name": "testMethod", "value": "ListManager_TC018"}, {"name": "parentSuite", "value": "Failed Tests Rerun Suite"}, {"name": "suite", "value": "<PERSON><PERSON> Failed Tests"}, {"name": "subSuite", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC018"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "List Manager"}], "links": [], "name": "Verify that user is able to Add, modify, delete and a World Check flow as Sanction type", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to Add a new World Check flow as Sanction type\nVerify that user is able to Modify a World Check flow\nVerify that user is able to Delete a World Check flow\nVerify that user is able to search for a World Check flow by Zone", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754157538261, "stop": 1754157538261}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-02'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754157538261, "stop": 1754157538261}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754157538266, "stop": 1754157538266}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754157538266, "stop": 1754157538266}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754157538267, "stop": 1754157538267}, {"name": "Login with User Name = operator-02 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754157538380, "stop": 1754157538380}, {"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-534634231', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@24f5d269, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-534634231', officialDate='null', entry=[ListEntry{type='null', name='EntryName-534634231', firstName='EntryFirstName-534634231', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-534634231', firstName='EntryFirstName-534634231', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754157542255, "stop": 1754157542255}, {"name": "Create new black list", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754157542255, "stop": 1754157542255}, {"name": "Search for list by listName = ListName-534634231 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754157559507, "stop": 1754157559507}, {"name": "Verify that user is able to Add a new World Check flow as Sanction type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754157564316, "stop": 1754157564316}, {"name": "Verify that user is able to Modify a World Check flow", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754157589984, "stop": 1754157589984}, {"name": "Verify that user is able to Delete a World Check flow", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754157598369, "stop": 1754157598369}], "attachments": [], "parameters": [{"name": "arg0", "value": "Sanction"}], "start": 1754157537964, "stop": 1754157605973}