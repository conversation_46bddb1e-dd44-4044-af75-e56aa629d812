{"uuid": "5b1f99a5-d112-4084-96a5-a4bb9190338f", "historyId": "c1c14de74bb43bbf5f8d10a287df71e", "fullName": "eastnets.admin.AdminTest.checkPermissionWhenAssignSecGroupProfileZone", "labels": [{"name": "package", "value": "eastnets.admin.AdminTest"}, {"name": "testClass", "value": "eastnets.admin.AdminTest"}, {"name": "testMethod", "value": "checkPermissionWhenAssignSecGroupProfileZone"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Admin Test Cases"}, {"name": "subSuite", "value": "eastnets.admin.AdminTest"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-1(36)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}], "links": [], "name": "Verify that the user that is assigned to Group, Profile, and Zone have permission to Block but don't have the permission to Release is able to release when this user is assigned to a second Group, Profile and Zone that have the permission to Release.", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that the user that is assigned to Group, Profile, and Zone have permission to Block but don't have the permission to Release is able to release when this user is assigned to a second Group, Profile and Zone that have the permission to Release.", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152630604, "stop": 1754152630604}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='sysadmin'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152630604, "stop": 1754152630604}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152630613, "stop": 1754152630613}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152630613, "stop": 1754152630613}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152630614, "stop": 1754152630614}, {"name": "Login with User Name = sysadmin and Password = manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152630779, "stop": 1754152630779}, {"name": "Operator test data = Operator{id=0, loginName='selenium-user-521038046', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone977909182', displayName='Zone (977909182)', description='Zone created with random number '977909182''}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152635169, "stop": 1754152635169}, {"name": "Group test data = Group{id=0, name='selenium-random-group-842974152', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-17707791', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone977909182', displayName='Zone (977909182)', description='Zone created with random number '977909182''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-521038046', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone977909182', displayName='Zone (977909182)', description='Zone created with random number '977909182''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152635169, "stop": 1754152635169}, {"name": "Create new zone .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152635169, "stop": 1754152635169}, {"name": "Zone test Data = Zone{id=0, name='Zone977909182', displayName='Zone (977909182)', description='Zone created with random number '977909182''}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152635169, "stop": 1754152635169}, {"name": "Check if zone with name = 'Zone977909182' exists", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152637023, "stop": 1754152637023}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152637310, "stop": 1754152637310}, {"name": "Enter name =Zone977909182", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152642723, "stop": 1754152642723}, {"name": "Enter display Name =Zone (977909182)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152643550, "stop": 1754152643550}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152644315, "stop": 1754152644315}, {"name": "Zone doesn't exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152644957, "stop": 1754152644957}, {"name": "Click add new zone button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152644957, "stop": 1754152644957}, {"name": "Set create new zone data.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152645204, "stop": 1754152645204}, {"name": "Set name = Zone977909182", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152645204, "stop": 1754152645204}, {"name": "Set display name = Zone (977909182)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152646459, "stop": 1754152646459}, {"name": "Set description = Zone created with random number '977909182'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152647113, "stop": 1754152647113}, {"name": "Capture zone id from UI = 139", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152648252, "stop": 1754152648252}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152648252, "stop": 1754152648252}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152649458, "stop": 1754152649458}, {"name": "Enter name =Zone977909182", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152654804, "stop": 1754152654804}, {"name": "Enter display Name =Zone (977909182)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152655727, "stop": 1754152655727}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152656796, "stop": 1754152656796}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152659339, "stop": 1754152659339}, {"name": "Enter name =Zone977909182", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152664665, "stop": 1754152664665}, {"name": "Enter display Name =Zone (977909182)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152665572, "stop": 1754152665572}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152666411, "stop": 1754152666411}, {"name": "Create first profile .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152667242, "stop": 1754152667242}, {"name": "Profile test Data = Profile{id=0, name='Test-Profile-17707791', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone977909182', displayName='Zone (977909182)', description='Zone created with random number '977909182''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152667243, "stop": 1754152667243}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152673008, "stop": 1754152673008}, {"name": "Set name = Test-Profile-17707791 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152678981, "stop": 1754152678981}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152692964, "stop": 1754152692964}, {"name": "Click add new profile button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152694038, "stop": 1754152694038}, {"name": "Set name = Test-Profile-17707791 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152695044, "stop": 1754152695044}, {"name": "Set description = Random profile for Selenium testing ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152697112, "stop": 1754152697112}, {"name": "Check enable checkbox to be true ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152698261, "stop": 1754152698261}, {"name": "Check write right checkbox to be Test-Profile-17707791 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152699110, "stop": 1754152699110}, {"name": "Select zone = Test-Profile-17707791 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152699519, "stop": 1754152699519}, {"name": "Add SWS item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152708259, "stop": 1754152708259}, {"name": "Click on the SWS item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152708259, "stop": 1754152708259}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152708776, "stop": 1754152708776}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152709880, "stop": 1754152709880}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152710192, "stop": 1754152710192}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152710278, "stop": 1754152710278}, {"name": "Set name = Test-Profile-17707791 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152716548, "stop": 1754152716548}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152717424, "stop": 1754152717424}, {"name": "Select profile from result view.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152718153, "stop": 1754152718153}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152718866, "stop": 1754152718866}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152720329, "stop": 1754152720329}, {"name": "Add en.Reporting Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152720869, "stop": 1754152720869}, {"name": "Click on the en.Reporting Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152720869, "stop": 1754152720869}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152721382, "stop": 1754152721382}, {"name": "Add SWIFT Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152722753, "stop": 1754152722753}, {"name": "Click on the SWIFT Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152722753, "stop": 1754152722753}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152723355, "stop": 1754152723355}, {"name": "Add SWP Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152725087, "stop": 1754152725087}, {"name": "Click on the SWP Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152725087, "stop": 1754152725087}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152725935, "stop": 1754152725935}, {"name": "Add Detection Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152727498, "stop": 1754152727498}, {"name": "Click on the Detection Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152727498, "stop": 1754152727498}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152728035, "stop": 1754152728035}, {"name": "Add Scan Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152729416, "stop": 1754152729416}, {"name": "Click on the Scan Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152729416, "stop": 1754152729416}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152729865, "stop": 1754152729865}, {"name": "Add Format Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152731335, "stop": 1754152731335}, {"name": "Click on the Format Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152731335, "stop": 1754152731335}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152731867, "stop": 1754152731867}, {"name": "Add Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152733347, "stop": 1754152733347}, {"name": "Click on the Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152733347, "stop": 1754152733347}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152733853, "stop": 1754152733853}, {"name": "Add Batch List Management item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152735350, "stop": 1754152735350}, {"name": "Click on the Batch List Management item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152735350, "stop": 1754152735350}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152735902, "stop": 1754152735902}, {"name": "Add ListExplorer item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152737388, "stop": 1754152737388}, {"name": "Click on the ListExplorer item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152737388, "stop": 1754152737388}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152737779, "stop": 1754152737779}, {"name": "Add ListSetManager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152739209, "stop": 1754152739209}, {"name": "Click on the ListSetManager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152739209, "stop": 1754152739209}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152739665, "stop": 1754152739665}, {"name": "Add List Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152741118, "stop": 1754152741118}, {"name": "Click on the List Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152741118, "stop": 1754152741118}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152741603, "stop": 1754152741603}, {"name": "Add File Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152742993, "stop": 1754152742993}, {"name": "Click on the File Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152742993, "stop": 1754152742993}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152743756, "stop": 1754152743756}, {"name": "Add Advanced Settings item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152745451, "stop": 1754152745451}, {"name": "Click on the Advanced Settings item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152745451, "stop": 1754152745451}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152745993, "stop": 1754152745993}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152747853, "stop": 1754152747853}, {"name": "Accept <PERSON><PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152748242, "stop": 1754152748242}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152749923, "stop": 1754152749923}, {"name": "Select Module Rights.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152750713, "stop": 1754152750713}, {"name": "Processing module 'SWIFT Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152752714, "stop": 1754152752714}, {"name": "Click on SWIFT Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152752714, "stop": 1754152752714}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152753618, "stop": 1754152753618}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152753923, "stop": 1754152753923}, {"name": "Processing module 'Detection Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152757735, "stop": 1754152757735}, {"name": "Click on Detection Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152757735, "stop": 1754152757735}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152758662, "stop": 1754152758662}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152759049, "stop": 1754152759049}, {"name": "Processing module 'Scan Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152761489, "stop": 1754152761489}, {"name": "Click on Scan Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152761489, "stop": 1754152761489}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152762398, "stop": 1754152762398}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152762706, "stop": 1754152762706}, {"name": "Processing module 'Format Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152765092, "stop": 1754152765092}, {"name": "Click on Format Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152765092, "stop": 1754152765092}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152766077, "stop": 1754152766077}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152766857, "stop": 1754152766857}, {"name": "Processing module 'Name Checker'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152769524, "stop": 1754152769524}, {"name": "Click on Name Checker", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152769524, "stop": 1754152769524}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152770739, "stop": 1754152770739}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152771098, "stop": 1754152771098}, {"name": "Processing module 'ListExplorer'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152774320, "stop": 1754152774320}, {"name": "Click on ListExplorer", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152774320, "stop": 1754152774320}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152775531, "stop": 1754152775531}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152775857, "stop": 1754152775857}, {"name": "Processing module 'ListSetManager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152778107, "stop": 1754152778107}, {"name": "Click on ListSetManager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152778107, "stop": 1754152778107}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152779340, "stop": 1754152779340}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152779553, "stop": 1754152779553}, {"name": "Processing module 'List Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152781921, "stop": 1754152781921}, {"name": "Click on List Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152781921, "stop": 1754152781921}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152782834, "stop": 1754152782834}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152783250, "stop": 1754152783250}, {"name": "Processing module 'File Scanner'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152785782, "stop": 1754152785782}, {"name": "Click on File Scanner", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152785783, "stop": 1754152785783}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152786936, "stop": 1754152786936}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152787377, "stop": 1754152787377}, {"name": "Processing module 'Advanced Settings'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152789885, "stop": 1754152789885}, {"name": "Click on Advanced Settings", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152789885, "stop": 1754152789885}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152791018, "stop": 1754152791018}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152791230, "stop": 1754152791231}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152792445, "stop": 1754152792445}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152792837, "stop": 1754152792837}, {"name": "Check if profile exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152795218, "stop": 1754152795218}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152795218, "stop": 1754152795218}, {"name": "Set name = Test-Profile-17707791 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152801210, "stop": 1754152801210}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152802136, "stop": 1754152802136}, {"name": "Profile exist = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152802983, "stop": 1754152802983}, {"name": "Create new operator.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152802983, "stop": 1754152802983}, {"name": "Operator test Data = Operator{id=0, loginName='selenium-user-521038046', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone977909182', displayName='Zone (977909182)', description='Zone created with random number '977909182''}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152802983, "stop": 1754152802983}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152805500, "stop": 1754152805500}, {"name": "Set login name = selenium-user-521038046", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152810916, "stop": 1754152810916}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152811717, "stop": 1754152811717}, {"name": "Click Create new operator button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152812425, "stop": 1754152812425}, {"name": "Set login name = selenium-user-521038046", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152812741, "stop": 1754152812741}, {"name": "Set first name = Random", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152814331, "stop": 1754152814331}, {"name": "Set last name = User", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152815064, "stop": 1754152815064}, {"name": "Set password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152815775, "stop": 1754152815775}, {"name": "Set confirm password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152816423, "stop": 1754152816423}, {"name": "Set enable flag = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152817179, "stop": 1754152817179}, {"name": "Set reset flag = false", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152818109, "stop": 1754152818109}, {"name": "Select zone  = Zone (977909182)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152819348, "stop": 1754152819348}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152826391, "stop": 1754152826391}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152826828, "stop": 1754152826828}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152833745, "stop": 1754152833745}, {"name": "Set login name = selenium-user-521038046", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152839226, "stop": 1754152839226}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152840280, "stop": 1754152840280}, {"name": "Create first group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152841424, "stop": 1754152841424}, {"name": "Group test Data = Group{id=0, name='selenium-random-group-842974152', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-17707791', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone977909182', displayName='Zone (977909182)', description='Zone created with random number '977909182''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-521038046', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone977909182', displayName='Zone (977909182)', description='Zone created with random number '977909182''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152841424, "stop": 1754152841424}, {"name": "Connect to database to remove Link between profile = Test-Profile-17707791 and Permission Allow to add attachments to detection", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152881421, "stop": 1754152881421}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152881453, "stop": 1754152881453}, {"name": "SELECT P.NAME as PR<PERSON><PERSON><PERSON>_NAME , PR.PROFILE_ID as PROFILE_ID , PR.GRANT_ID as GRANT_ID , PR.GRANT_RIGHT_ID as GRANT_RIGHT_ID , PR.GRANT_TYPE_ID as GRANT_TYPE_ID   FROM tModuleFunctions M   LEFT JOIN tProfileRights PR On PR.GRANT_ID  = M.ID     LEFT Join tProfiles P ON P.id =PR.PROFILE_ID   WHERE DESCRIPTION ='Allow to add attachments to detection' and P.NAME = 'Test-Profile-17707791'  and PR.GRANT_TYPE_ID =3", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152881453, "stop": 1754152881453}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152881463, "stop": 1754152881463}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152881463, "stop": 1754152881463}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152881464, "stop": 1754152881464}, {"name": "Delete function permission 'Allow to add attachments to detection' from DataBase", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152881464, "stop": 1754152881464}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152881494, "stop": 1754152881494}, {"name": "DELETE FROM tProfileRights  WHERE GRANT_ID  ='137' and PROFILE_ID ='159' and GRANT_TYPE_ID =3", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152881494, "stop": 1754152881494}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152881510, "stop": 1754152881510}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152881510, "stop": 1754152881510}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152881511, "stop": 1754152881511}, {"name": "Create second zone .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152881513, "stop": 1754152881513}, {"name": "Zone test Data = Zone{id=0, name='Zone614346306', displayName='Zone (614346306)', description='Zone created with random number '614346306''}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152881513, "stop": 1754152881513}, {"name": "Check if zone with name = 'Zone614346306' exists", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152883468, "stop": 1754152883468}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152883792, "stop": 1754152883792}, {"name": "Enter name =Zone614346306", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152889263, "stop": 1754152889263}, {"name": "Enter display Name =Zone (614346306)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152889998, "stop": 1754152889998}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152890921, "stop": 1754152890921}, {"name": "Zone doesn't exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152891678, "stop": 1754152891678}, {"name": "Click add new zone button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152891678, "stop": 1754152891678}, {"name": "Set create new zone data.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152891997, "stop": 1754152891997}, {"name": "Set name = Zone614346306", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152891997, "stop": 1754152891997}, {"name": "Set display name = Zone (614346306)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152893591, "stop": 1754152893591}, {"name": "Set description = Zone created with random number '614346306'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152894454, "stop": 1754152894454}, {"name": "Capture zone id from UI = 140", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152895719, "stop": 1754152895719}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152895719, "stop": 1754152895719}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152897644, "stop": 1754152897644}, {"name": "Enter name =Zone614346306", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152903387, "stop": 1754152903387}, {"name": "Enter display Name =Zone (614346306)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152904138, "stop": 1754152904138}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152904945, "stop": 1754152904945}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152907743, "stop": 1754152907743}, {"name": "Enter name =Zone614346306", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152913128, "stop": 1754152913128}, {"name": "Enter display Name =Zone (614346306)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152913832, "stop": 1754152913832}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152914640, "stop": 1754152914640}, {"name": "Create second profile .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152915218, "stop": 1754152915218}, {"name": "Profile test Data = Profile{id=0, name='Test-Profile-241715103', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone614346306', displayName='Zone (614346306)', description='Zone created with random number '614346306''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152915218, "stop": 1754152915218}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152916669, "stop": 1754152916669}, {"name": "Set name = Test-Profile-241715103 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152922037, "stop": 1754152922037}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152922701, "stop": 1754152922701}, {"name": "Click add new profile button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152923500, "stop": 1754152923500}, {"name": "Set name = Test-Profile-241715103 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152923789, "stop": 1754152923789}, {"name": "Set description = Random profile for Selenium testing ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152925179, "stop": 1754152925179}, {"name": "Check enable checkbox to be true ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152925863, "stop": 1754152925863}, {"name": "Check write right checkbox to be Test-Profile-241715103 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152926523, "stop": 1754152926523}, {"name": "Select zone = Test-Profile-241715103 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152926867, "stop": 1754152926867}, {"name": "Add SWS item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152932276, "stop": 1754152932276}, {"name": "Click on the SWS item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152932276, "stop": 1754152932276}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152932667, "stop": 1754152932667}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152933577, "stop": 1754152933577}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152933854, "stop": 1754152933854}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152933914, "stop": 1754152933914}, {"name": "Set name = Test-Profile-241715103 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152940003, "stop": 1754152940003}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152940798, "stop": 1754152940798}, {"name": "Select profile from result view.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152941540, "stop": 1754152941540}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152942190, "stop": 1754152942190}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152943455, "stop": 1754152943455}, {"name": "Add en.Reporting Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152943989, "stop": 1754152943989}, {"name": "Click on the en.Reporting Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152943989, "stop": 1754152943989}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152944528, "stop": 1754152944528}, {"name": "Add SWIFT Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152945970, "stop": 1754152945970}, {"name": "Click on the SWIFT Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152945970, "stop": 1754152945970}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152946365, "stop": 1754152946365}, {"name": "Add SWP Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152947845, "stop": 1754152947845}, {"name": "Click on the SWP Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152947845, "stop": 1754152947845}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152948233, "stop": 1754152948233}, {"name": "Add Detection Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152949674, "stop": 1754152949674}, {"name": "Click on the Detection Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152949674, "stop": 1754152949674}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152950047, "stop": 1754152950047}, {"name": "Add Scan Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152951442, "stop": 1754152951442}, {"name": "Click on the Scan Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152951442, "stop": 1754152951442}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152951830, "stop": 1754152951830}, {"name": "Add Format Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152953245, "stop": 1754152953245}, {"name": "Click on the Format Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152953245, "stop": 1754152953245}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152953690, "stop": 1754152953690}, {"name": "Add Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152955088, "stop": 1754152955088}, {"name": "Click on the Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152955088, "stop": 1754152955088}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152955589, "stop": 1754152955589}, {"name": "Add Batch List Management item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152956993, "stop": 1754152956993}, {"name": "Click on the Batch List Management item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152956993, "stop": 1754152956993}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152957391, "stop": 1754152957391}, {"name": "Add ListExplorer item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152958797, "stop": 1754152958797}, {"name": "Click on the ListExplorer item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152958797, "stop": 1754152958797}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152959179, "stop": 1754152959179}, {"name": "Add ListSetManager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152960640, "stop": 1754152960640}, {"name": "Click on the ListSetManager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152960640, "stop": 1754152960640}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152960985, "stop": 1754152960985}, {"name": "Add List Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152962422, "stop": 1754152962422}, {"name": "Click on the List Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152962422, "stop": 1754152962422}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152962808, "stop": 1754152962808}, {"name": "Add File Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152964237, "stop": 1754152964237}, {"name": "Click on the File Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152964237, "stop": 1754152964237}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152964772, "stop": 1754152964772}, {"name": "Add Advanced Settings item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152966204, "stop": 1754152966204}, {"name": "Click on the Advanced Settings item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152966204, "stop": 1754152966204}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152966832, "stop": 1754152966832}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152968233, "stop": 1754152968233}, {"name": "Accept <PERSON><PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152968670, "stop": 1754152968670}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152970299, "stop": 1754152970299}, {"name": "Select Module Rights.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152971095, "stop": 1754152971095}, {"name": "Processing module 'SWIFT Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152973097, "stop": 1754152973097}, {"name": "Click on SWIFT Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152973097, "stop": 1754152973097}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152974060, "stop": 1754152974060}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152974418, "stop": 1754152974418}, {"name": "Processing module 'Detection Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152977768, "stop": 1754152977768}, {"name": "Click on Detection Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152977768, "stop": 1754152977768}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152979032, "stop": 1754152979032}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152979329, "stop": 1754152979329}, {"name": "Processing module 'Scan Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152981822, "stop": 1754152981822}, {"name": "Click on Scan Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152981822, "stop": 1754152981822}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152982835, "stop": 1754152982835}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152983194, "stop": 1754152983194}, {"name": "Processing module 'Format Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152985520, "stop": 1754152985520}, {"name": "Click on Format Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152985520, "stop": 1754152985520}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152986463, "stop": 1754152986463}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152986732, "stop": 1754152986732}, {"name": "Processing module 'Name Checker'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152988899, "stop": 1754152988899}, {"name": "Click on Name Checker", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152988899, "stop": 1754152988899}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152989787, "stop": 1754152989787}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152990045, "stop": 1754152990045}, {"name": "Processing module 'ListExplorer'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152993339, "stop": 1754152993339}, {"name": "Click on ListExplorer", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152993339, "stop": 1754152993339}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152994424, "stop": 1754152994424}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152994959, "stop": 1754152994959}, {"name": "Processing module 'ListSetManager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152997505, "stop": 1754152997505}, {"name": "Click on ListSetManager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152997505, "stop": 1754152997505}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152998696, "stop": 1754152998696}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152998988, "stop": 1754152998988}, {"name": "Processing module 'List Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153001231, "stop": 1754153001231}, {"name": "Click on List Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153001231, "stop": 1754153001231}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153002774, "stop": 1754153002774}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153003243, "stop": 1754153003243}, {"name": "Processing module 'File Scanner'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153005775, "stop": 1754153005775}, {"name": "Click on File Scanner", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153005775, "stop": 1754153005775}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153006681, "stop": 1754153006681}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153006986, "stop": 1754153006986}, {"name": "Processing module 'Advanced Settings'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153009232, "stop": 1754153009232}, {"name": "Click on Advanced Settings", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153009232, "stop": 1754153009232}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153010353, "stop": 1754153010353}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153010646, "stop": 1754153010646}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153011900, "stop": 1754153011900}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153012198, "stop": 1754153012198}, {"name": "Check if profile exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153013867, "stop": 1754153013867}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153013867, "stop": 1754153013867}, {"name": "Set name = Test-Profile-241715103 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153019799, "stop": 1754153019799}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153020512, "stop": 1754153020512}, {"name": "Profile exist = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153021316, "stop": 1754153021316}, {"name": "Create second group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153021316, "stop": 1754153021316}, {"name": "Group test Data = Group{id=0, name='selenium-random-group-434574411', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-241715103', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone614346306', displayName='Zone (614346306)', description='Zone created with random number '614346306''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-521038046', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone977909182', displayName='Zone (977909182)', description='Zone created with random number '977909182''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153021316, "stop": 1754153021316}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153061706, "stop": 1754153061706}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='selenium-user-521038046'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153061706, "stop": 1754153061706}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153061714, "stop": 1754153061714}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153061714, "stop": 1754153061714}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153061714, "stop": 1754153061714}, {"name": "Login with User Name = selenium-user-521038046 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153061826, "stop": 1754153061826}, {"name": "Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-9661495', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@30be17a8, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-9661495', officialDate='null', entry=[ListEntry{type='null', name='EntryName-9661495', firstName='EntryFirstName-9661495', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-9661495', firstName='EntryFirstName-9661495', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153067639, "stop": 1754153067639}, {"name": "Connect to Database and Check if User Profile = Test-Profile-241715103 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153067645, "stop": 1754153067645}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153067645, "stop": 1754153067645}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153067697, "stop": 1754153067697}, {"name": "SELECT ID FROM tProfiles Where NAME ='Test-Profile-241715103' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153067697, "stop": 1754153067697}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153067699, "stop": 1754153067699}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153067699, "stop": 1754153067699}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153067699, "stop": 1754153067699}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153067720, "stop": 1754153067720}, {"name": "Delete From tListSetProfile where profile_id in (160)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153067720, "stop": 1754153067720}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153067722, "stop": 1754153067722}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153067722, "stop": 1754153067722}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153067723, "stop": 1754153067723}, {"name": "Number of effected rows = 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153067723, "stop": 1754153067723}, {"name": "Search for list by listName = ListName-9661495 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153088506, "stop": 1754153088506}, {"name": "Set zone : Zone (614346306)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153097648, "stop": 1754153097648}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153099573, "stop": 1754153099573}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153100314, "stop": 1754153100314}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153100648, "stop": 1754153100648}, {"name": "Set template name = templateName-9661495", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153100820, "stop": 1754153100820}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153102514, "stop": 1754153102514}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153103435, "stop": 1754153103435}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153104496, "stop": 1754153104496}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153105163, "stop": 1754153105163}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153123808, "stop": 1754153123808}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153123808, "stop": 1754153123808}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153174056, "stop": 1754153174056}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153174056, "stop": 1754153174056}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153174057, "stop": 1754153174057}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153174096, "stop": 1754153174096}, {"name": "SELECT ID FROM tProfiles Where NAME ='Test-Profile-241715103'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153174096, "stop": 1754153174096}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153174098, "stop": 1754153174098}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153174099, "stop": 1754153174099}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153174099, "stop": 1754153174099}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153188577, "stop": 1754153188577}, {"name": "RJE File Content= {1:F01PTSABEMMAXXX0000000001}{2:I564ACCLFRP1XXXXN}{3:{108:ENT144037000001}}{4:\r\n:16R:GENL\r\n:20C::CORP//2015033100010560\r\n:20C::SEME//0105601900000002\r\n:23G:EntryName-9661495, EntryFirstName-9661495\r\n:22F::CAEV//DVCA\r\n:22F::CAMV//MAND\r\n:98A::PREP//20150331\r\n:25D::PROC//COMP\r\n:16R:LINK\r\n:13A::LINK//564\r\n:20C::PREV//0105601900000001\r\n:16S:LINK\r\n:16S:GENL\r\n:16R:USECU\r\n:35B:ISIN DE0005190003\r\nBMW Bayerische Motoren Werke AG\r\n:16R:ACCTINFO\r\n:97A::SAFE//000000190003\r\n:93B::ELIG//UNIT/360,\r\n:93B::PEND//UNIT/360,\r\n:93B::PENR//UNIT/0,\r\n:93B::SETT//UNIT/360,\r\n:16S:ACCTINFO\r\n:16S:USECU\r\n:16R:CADETL\r\n:98A::XDTE//20150514\r\n:98A::RDTE//20150515\r\n:22F::DIVI//FINL\r\n:16S:CADETL\r\n:16R:CAOPTN\r\n:13A::CAON//001\r\n:22F::CAOP//CASH\r\n:17B::DFLT//Y\r\n:16R:CASHMOVE\r\n:22H::CRDB//CRED\r\n:98A::PAYD//20150514\r\n:92A::TAXR//26,375\r\n:92F::GRSS//EUR2,9\r\n:16S:CASHMOVE\r\n:16S:CAOPTN\r\n-}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153195654, "stop": 1754153195654}, {"name": "Start coping data from sample file to another file to be used.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153195654, "stop": 1754153195654}, {"name": "Successfully wrote to the file.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153195659, "stop": 1754153195659}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153197431, "stop": 1754153197431}, {"name": "Validation message = File sent to the server for processing with id [2935]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153210717, "stop": 1754153210717}, {"name": "Alert Message = File sent to the server for processing with id [2935]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153210717, "stop": 1754153210717}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153211093, "stop": 1754153211093}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153219341, "stop": 1754153219341}, {"name": "Detection ID = 7440", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153220210, "stop": 1754153220210}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153224841, "stop": 1754153224841}, {"name": "Check if user can Release detection. ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153225833, "stop": 1754153225833}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153229677, "stop": 1754153229677}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "Operator{id=0, loginName='null', firstName='null', lastName='null', password='null', zone=null}"}], "start": 1754152630225, "stop": 1754153237701}