{"uuid": "b6d432b5-66a5-4b79-8cef-ac8f8e222a84", "historyId": "b86d509e9ce355a0fe501fe20e66a1a6", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC007.scanManager_TC007", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC007"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC007"}, {"name": "testMethod", "value": "scanManager_TC007"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC007"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is able to add a Good Guy for a detection by clicking accept as shared button for shared black list", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to add a Good Guy for a detection by clicking accept as shared button for shared black list", "steps": [{"name": "Validation Message = null", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141868665, "stop": 1754141868665}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141868665, "stop": 1754141868665}, {"name": "Status for the scanned name = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141880772, "stop": 1754141880772}, {"name": "<PERSON>ose scanned name from result table يا<PERSON><PERSON> عمر", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141880772, "stop": 1754141880772}, {"name": "Actual validation Message = Good guy successfully created!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141886062, "stop": 1754141886062}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754141862050, "stop": 1754141886618}