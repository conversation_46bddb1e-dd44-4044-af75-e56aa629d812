@echo off
echo ========================================
echo Dynamic TestNG Suite Execution with Auto-Rerun
echo ========================================

REM Set the TestNG XML file to run (can be changed here)
set TESTNG_FILE=regressionWithoutRestartServices.xml

echo.
echo TestNG Suite: %TESTNG_FILE%
echo [1/3] Running initial test suite...
mvn clean test -Dsurefire.suiteXmlFiles=%TESTNG_FILE%

echo.
echo [2/3] Checking for failed tests...
if exist "target\surefire-reports\testng-failed.xml" (
    echo Found failed tests! Rerunning them...
    echo.
    echo [3/3] Rerunning failed tests...
    mvn test -Dsurefire.suiteXmlFiles=target\surefire-reports\testng-failed.xml
    
    echo.
    echo Checking results after rerun...
    if exist "target\surefire-reports\testng-failed.xml" (
        echo Some tests still failed after rerun.
        echo Check target\surefire-reports\testng-failed.xml for details.
    ) else (
        echo All previously failed tests now pass!
    )
) else (
    echo All tests passed on first run! No rerun needed.
)

echo.
echo ========================================
echo Test execution completed!
echo ========================================
pause
