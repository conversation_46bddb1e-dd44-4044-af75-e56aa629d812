<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd" >

<suite name="Safe Watch Filtering"  verbose="5" preserve-order="true" >
    <parameter name="browserType" value="Edge" />
    <parameter name="Application" value="SWF" />

    <test name="Safe Watch Filtering App Smoke Test">
        <parameter name="loginType" value="filtering" />
        <classes>
            <class name="eastnets.screening.sanity.ScreeningCoreTest" />
            <class name="eastnets.screening.sanity.LicenseRTest" />

            <!-- <class name="eastnets.screening.sanity.OperatorRTest" />
             <class name="eastnets.screening.sanity.LicenseRTest" />
             <class name="eastnets.screening.sanity.AdvancedSettingsTest" />
             <class name="eastnets.screening.sanity.AddListSetTest" />
             <class name="eastnets.screening.sanity.AddEntryTest" />
             <class name="eastnets.screening.sanity.AddGoodGuyTest" />
             <class name="eastnets.screening.sanity.AddDowJonesListTest" />
             <class name="eastnets.screening.sanity.FormatManagerTests" />
             <class name="eastnets.screening.sanity.BlockDetectionTest" />
             <class name="eastnets.screening.sanity.ImportNewISO20022SchemaTest" />
             <class name="eastnets.screening.sanity.EventViewerTest" />
             <class name="eastnets.screening.sanity.ExportReportTest" />-->


         </classes>
     </test>

 </suite>