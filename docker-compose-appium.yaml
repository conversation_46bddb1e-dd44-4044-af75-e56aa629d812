version: "3.8"

services:

  postgres-db:
    image: postgres:15.5-alpine
    container_name: postgres-db
    restart: always
    ports:
      - "5432:5432"
    volumes:
      - ./db/sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    environment:
      - POSTGRES_DB=healenium
      - POSTGRES_USER=healenium_user
      - POSTGRES_PASSWORD=YDk2nmNs4s9aCP6K
    networks:
      - healenium

  healenium:
    image: healenium/hlm-backend:3.4.6
    container_name: healenium
    restart: on-failure
    ports:
      - "7878:7878"
    links:
      - postgres-db
    environment:
      - SPRING_POSTGRES_DB=healenium
      - SPRING_POSTGRES_SCHEMA=healenium
      - SPRING_POSTGRES_USER=healenium_user
      - SPRING_POSTGRES_PASSWORD=YDk2nmNs4s9aCP6K
      - SPRING_POSTGRES_DB_HOST=postgres-db
      - KEY_SELECTOR_URL=false
      - COLLECT_METRICS=true
      - FIND_ELEMENTS_AUTO_HEALING=false
      - HLM_LOG_LEVEL=info
    volumes:
      - ./screenshots/:/screenshots
      - ./logs/:/logs
    networks:
      - healenium

  selector-imitator:
    image: healenium/hlm-selector-imitator:1.4
    container_name: selector-imitator
    restart: on-failure
    ports:
      - "8000:8000"
    networks:
      - healenium

  hlm-proxy:
    image: healenium/hlm-proxy:2.1.4
    container_name: hlm-proxy
    restart: on-failure
    ports:
      - "8085:8085"
    environment:
      - RECOVERY_TRIES=1
      - SCORE_CAP=.6
      - HEAL_ENABLED=true
      - SELENIUM_SERVER_URL=http://host.docker.internal:4723/wd/hub
      - HEALENIUM_SERVER_URL=http://localhost:7878
      - HEALENIUM_SERVICE=http://healenium:7878
      - IMITATE_SERVICE=http://selector-imitator:8000
      - HLM_LOG_LEVEL=info
    volumes:
      - ./logs/:/logs
    networks:
      - healenium

networks:
  healenium:
    name: healenium
