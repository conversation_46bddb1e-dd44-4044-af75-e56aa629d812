# Quick TestNG Runner - Complete Guide

## Overview
The `quick-run-testng.bat` script provides fast, command-line execution of any TestNG XML file with automatic failed test rerun functionality.

## Quick TestNG Runner
**File:** `quick-run-testng.bat`

### Features
- ✅ **Fast execution** - Minimal overhead and quick startup
- ✅ **Command-line driven** - Perfect for automation and scripting
- ✅ **Automatic failed test rerun** - Reruns only failed tests after suite completion
- ✅ **Parameter validation** - Checks file existence and provides clear error messages
- ✅ **CI/CD ready** - Designed for integration with build pipelines
- ✅ **Sequential execution** - No individual test retries during main run

### Usage
```bash
quick-run-testng.bat [testng-file.xml]
```

### Examples
```bash
# Run regression tests
quick-run-testng.bat regressionWithoutRestartServices.xml

# Run bug tests
quick-run-testng.bat bugsTestNg-noRestart.xml

# Run approval tests
quick-run-testng.bat approvalTestNg.xml

# Run custom test suite
quick-run-testng.bat myCustomTests.xml
```

### Example Output
```
Running TestNG Suite: regressionWithoutRestartServices.xml

[1/3] Running complete test suite...
[INFO] Scanning for projects...
[INFO] Building Filtering - Automation 0.0.1-SNAPSHOT
[INFO] Running TestSuite
Tests run: 2, Failures: 1, Errors: 0, Skipped: 0
[INFO] BUILD SUCCESS

[2/3] Checking for failed tests...
Found failed tests! Rerunning them...

[3/3] Rerunning failed tests...
[INFO] Running TestSuite
Tests run: 1, Failures: 1, Errors: 0, Skipped: 0
[INFO] BUILD SUCCESS

Some tests still failed. Check: target\surefire-reports\testng-failed.xml

Execution completed for regressionWithoutRestartServices.xml
```

## Execution Flow

The script follows a three-phase execution pattern:

### Phase 1: Complete Suite Execution
```bash
mvn clean test "-DsuiteXmlFile=[testng-file.xml]"
```
- **Clean build**: Removes previous build artifacts
- **Complete suite**: Runs the entire test suite without interruption
- **No individual retries**: Tests run once during this phase
- **Failed suite generation**: Creates `testng-failed.xml` if any tests fail

### Phase 2: Results Analysis
```bash
if exist "target\surefire-reports\testng-failed.xml"
```
- **Automatic detection**: Checks for existence of failed test suite
- **Decision point**: Determines if failed test rerun is needed
- **Status reporting**: Informs user of next action

### Phase 3: Failed Test Rerun (if needed)
```bash
mvn test "-DsuiteXmlFile=target/surefire-reports/testng-failed.xml"
```
- **Selective execution**: Runs ONLY the tests that failed in Phase 1
- **No compilation**: Skips clean and compile phases for faster execution
- **Final status**: Reports whether failed tests now pass or still fail

## Key Features

### File Validation
- ✅ **Parameter check**: Ensures TestNG XML file parameter is provided
- ✅ **File existence**: Verifies the specified file exists in current directory
- ✅ **Clear error messages**: Provides helpful guidance when files are missing
- ✅ **Usage instructions**: Shows correct syntax when parameters are missing

### Results Management
- **Current results**: `target\surefire-reports\`
- **Failed tests**: `target\surefire-reports\testng-failed.xml` (if any)
- **Clear status reporting**: Explicit messages about test outcomes
- **Exit codes**: Proper return codes for CI/CD integration

## Usage Examples

### Basic Usage
```bash
# Run specific TestNG suites
quick-run-testng.bat regressionWithoutRestartServices.xml
quick-run-testng.bat bugsTestNg-noRestart.xml
quick-run-testng.bat approvalTestNg.xml
quick-run-testng.bat myCustomTests.xml
```

### CI/CD Integration
```bash
# Use in build pipeline with environment variable
quick-run-testng.bat %TEST_SUITE_FILE%

# With error handling for CI/CD
quick-run-testng.bat regressionWithoutRestartServices.xml
if errorlevel 1 (
    echo Tests failed permanently - failing build
    exit /b 1
) else (
    echo All tests passed - build successful
    exit /b 0
)
```

### Batch Processing
```bash
# Run multiple suites sequentially
for %%f in (*.xml) do (
    echo.
    echo ========================================
    echo Running test suite: %%f
    echo ========================================
    call quick-run-testng.bat %%f
    if errorlevel 1 (
        echo Suite %%f failed
    ) else (
        echo Suite %%f passed
    )
)
```

### Scheduled Execution
```bash
# Create a scheduled task script
@echo off
cd /d "C:\path\to\your\project"
echo Starting nightly test run at %date% %time%
quick-run-testng.bat regressionWithoutRestartServices.xml > test-results-%date:~-4,4%%date:~-10,2%%date:~-7,2%.log 2>&1
echo Test run completed at %date% %time%
```

## Error Handling

### Common Errors and Solutions

#### No Parameter Provided
```bash
Usage: quick-run-testng.bat [testng-file.xml]
Example: quick-run-testng.bat regressionWithoutRestartServices.xml
```
**Solution**: Always provide the TestNG XML file name as a parameter

#### File Not Found
```bash
ERROR: File 'myTests.xml' not found!
```
**Solutions**:
- Ensure the XML file exists in the current directory
- Check the file name spelling and extension
- Use full path if file is in a different directory

#### Maven Not Found
```bash
'mvn' is not recognized as an internal or external command
```
**Solutions**:
- Install Maven and add to system PATH
- Use IDE's built-in Maven instead
- Run from IDE terminal where Maven is available

#### No Tests Executed
```bash
Tests run: 0, Failures: 0, Errors: 0, Skipped: 0
```
**Solutions**:
- Check TestNG XML syntax and structure
- Verify test classes exist and compile successfully
- Ensure test groups/classes are enabled in XML
- Check Maven surefire plugin configuration

#### Failed Test Rerun Not Working
```bash
[2/3] Checking for failed tests...
All tests passed on first run!
```
*But you know tests failed*

**Solutions**:
- Check if `testng-failed.xml` is generated in `target/surefire-reports/`
- Verify TestNG version supports failed suite generation
- Ensure Maven surefire plugin has proper TestNG configuration

## Best Practices

### For Development
- Use `quick-run-testng.bat` for rapid iterations when you know the file name
- Keep TestNG XML files in the project root for easy access
- Use descriptive file names for different test suites

### For CI/CD
- Use environment variables for TestNG file names: `quick-run-testng.bat %TEST_SUITE%`
- Implement proper error handling with exit codes
- Log output to files for build artifact storage
- Use absolute paths when running from different directories

### For Manual Testing
- Create shortcuts for frequently used test suites
- Use batch processing for running multiple suites
- Monitor execution time and optimize slow tests

## Troubleshooting

### Script Won't Run
**Issue**: Script doesn't start or shows permission errors
**Solutions**:
1. Run Command Prompt as Administrator
2. Check file permissions on the batch file
3. Ensure current directory is the project root
4. Verify Maven is installed and in system PATH

### Tests Not Executing
**Issue**: Maven runs but no tests execute (Tests run: 0)
**Solutions**:
1. Validate TestNG XML syntax and structure
2. Check that test classes exist and compile successfully
3. Verify test groups/classes are enabled in the XML file
4. Ensure Maven surefire plugin configuration is correct
5. Check that test methods have `@Test` annotations

### Failed Test Rerun Not Working
**Issue**: Tests fail but rerun doesn't happen
**Solutions**:
1. Verify `testng-failed.xml` is generated in `target/surefire-reports/`
2. Check Maven surefire plugin version (3.0.0+ recommended)
3. Ensure TestNG version supports failed suite generation (7.0+)
4. Verify `usedefaultlisteners` property is enabled in Maven configuration

### Performance Issues
**Issue**: Tests run very slowly
**Solutions**:
1. Check database connectivity and performance
2. Verify Selenium Grid/Docker containers are running properly
3. Increase timeout values if needed
4. Monitor system resources during test execution

## Advanced Configuration

### Custom Maven Properties
```bash
# Run with custom properties
quick-run-testng.bat myTests.xml
# Add to pom.xml: <suiteXmlFile>${suiteXmlFile}</suiteXmlFile>
```

### Environment-Specific Execution
```bash
# Set environment before running
set TEST_ENV=staging
quick-run-testng.bat regressionWithoutRestartServices.xml
```

### Parallel Execution
Modify your TestNG XML file to enable parallel execution:
```xml
<suite name="TestSuite" parallel="methods" thread-count="3">
```

## Quick Reference

### Command Syntax
```bash
quick-run-testng.bat [testng-file.xml]
```

### Key Features
- ✅ **Fast execution** - Minimal overhead
- ✅ **Automatic rerun** - Failed tests rerun automatically
- ✅ **CI/CD ready** - Proper exit codes and error handling
- ✅ **Sequential phases** - Clear execution flow
- ✅ **Parameter validation** - File existence checking

### File Locations
- **TestNG XML files**: Project root directory
- **Test results**: `target/surefire-reports/`
- **Failed tests**: `target/surefire-reports/testng-failed.xml`
- **Allure results**: `target/allure-results/`

### Exit Codes
- **0**: All tests passed (either first run or after rerun)
- **1**: Some tests failed permanently or execution error

**The script automatically handles failed test rerun without additional configuration.**
