<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd" >

<suite name="Saa message creation"  verbose="5" preserve-order="true" >
    <parameter name="browserType" value="Chrome" />

    <test name="Create Fin message from SAA UI">
        <parameter name="loginType" value="filtering" />
        <parameter name="Application" value="SAA" />
        <classes>

            <class name="eastnets.saa.CheckMessageCreationFromSaaTests" >
                <methods>
                    <include name="loginToSaa" />
                    <include name="checkNavigationToFinMessageCreation" />
                    <include name="createIsoMessageFromUi" />
                </methods>
            </class>

        </classes>
    </test>

    <test name="Create message from SAA by uploading">
        <parameter name="loginType" value="filtering" />
        <parameter name="Application" value="SAA" />
        <classes>
            <class name="eastnets.saa.CheckMessageCreationFromSaaTests" >
                <methods>
                    <include name="loginToSaa"/>
                    <include name="loginToSwf"/>
                    <include name="uploadFinMessageToMessagePortalValidateSwfUi"/>
                    <include name="uploadIsoMessageToMessagePortalValidateSwfUi"/>
                </methods>
            </class>
        </classes>
    </test>
</suite>