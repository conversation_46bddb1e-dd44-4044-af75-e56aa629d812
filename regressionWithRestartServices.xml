<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd" >

<suite name="Safe Watch Filtering" verbose="5" preserve-order="true">
    <parameter name="browserType" value="Chrome"/>
    <parameter name="Application" value="SWF"/>


    <test name="Safe Watch Filtering App Smoke Test">
        <parameter name="loginType" value="filtering"/>


        <classes>

         <!--   <class name="eastnets.screening.AdvancedSettingsTests.EngineTuningTests" />
            <class name="eastnets.screening.AdvancedSettingsTests.DetectBicsTests" />
            <class name="eastnets.screening.AdvancedSettingsTests.PhoneticsTests" />
            <class name="eastnets.screening.RepeatManagerTests" />
            <class name="eastnets.screening.AdvancedSettingsTests.FourEyesTests" />
            <class name="eastnets.screening.SwiftImprovementTests" />
            <class name="eastnets.screening.AdvancedSettingsTests.BusinessLogicTests" />
            <class name="eastnets.screening.AdvancedSettingsTests.ZoneSegregationTest" />


            <class name="eastnets.screening.regression.restartServicesRequired.bugs.Tkt92727Test" />
            <class name="eastnets.screening.regression.restartServicesRequired.bugs.Ticket92216Tests" />
            <class name="eastnets.screening.regression.restartServicesRequired.bugs.Tkt_90148_Test" />
            <class name="eastnets.screening.regression.restartServicesRequired.bugs.Tkt_90550_Test" />
            <class name="eastnets.screening.regression.restartServicesNotRequired.bugs.Tkt_93156_Test" />
            <class name="eastnets.screening.regression.restartServicesRequired.bugs.Tkt_70179_Test" />
            <class name="eastnets.screening.regression.restartServicesRequired.bugs.Tk93629Test" />
            <class name="eastnets.screening.regression.restartServicesRequired.bugs.Tk100758Test" />
            <class name="eastnets.screening.regression.restartServicesRequired.bugs.Tkt89427Test" />
            <class name="eastnets.screening.regression.restartServicesRequired.bugs.Tkt89438Test" />
            <class name="eastnets.screening.regression.restartServicesRequired.bugs.Tkt90359Test" />
            <class name="eastnets.screening.regression.restartServicesRequired.bugs.Tkt92810Test" />
            <class name="eastnets.screening.regression.restartServicesRequired.bugs.Tkt90347Test" />
            <class name="eastnets.screening.regression.restartServicesRequired.bugs.Tkt_80310_Test" />
            <class name="eastnets.screening.regression.restartServicesNotRequired.bugs.Tkt87770Test" />
            <class name="eastnets.screening.regression.restartServicesRequired.bugs.Tk92703Test" />
            <class name="eastnets.screening.regression.restartServicesRequired.bugs.Tkt_91682_Test" />
            <class name="eastnets.screening.regression.restartServicesRequired.bugs.TKt_85312_Test"/>
            <class name="eastnets.screening.regression.restartServicesRequired.bugs.Tkt_89277_Test" />
            <class name="eastnets.screening.regression.restartServicesRequired.bugs.Tkt_89277_Test" />-->

            <class name="eastnets.screening.listmanager.worldchecktest.WorldCheck_TC_94746" />
            <class name="eastnets.screening.listmanager.worldchecktest.WorldCheck_TC_94748" />
            <class name="eastnets.screening.listmanager.worldchecktest.WorldCheck_TC_94752" />
            <class name="eastnets.screening.listmanager.worldchecktest.WorldCheck_TC_94753" />
            <class name="eastnets.screening.listmanager.worldchecktest.WorldCheck_TC_94757" />
            <class name="eastnets.screening.listmanager.worldchecktest.WorldCheck_TC_94832" />

            <class name="eastnets.screening.detectionmanager.DetectionManager_TC029" />
            <class name="eastnets.screening.detectionmanager.DetectionManager_TC030" />
        </classes>
    </test>

</suite>