version: "3.8"

services:
  # ========================================
  # SELENIUM GRID 4 DISTRIBUTED ARCHITECTURE
  # ========================================
  selenium-event-bus:
    image: selenium/event-bus:4.34.0-20250717
    container_name: selenium-event-bus
    ports:
      - "4442:4442"
      - "4443:4443"
      - "5557:5557"
    networks:
      - healenium

  selenium-sessions:
    image: selenium/sessions:4.34.0-20250717
    container_name: selenium-sessions
    ports:
      - "5556:5556"
    depends_on:
      - selenium-event-bus
    environment:
      - SE_EVENT_BUS_HOST=selenium-event-bus
    networks:
      - healenium

  selenium-session-queue:
    image: selenium/session-queue:4.34.0-20250717
    container_name: selenium-session-queue
    ports:
      - "5559:5559"
    networks:
      - healenium

  selenium-distributor:
    image: selenium/distributor:4.34.0-20250717
    container_name: selenium-distributor
    ports:
      - "5553:5553"
    depends_on:
      - selenium-event-bus
      - selenium-sessions
      - selenium-session-queue
    environment:
      - SE_EVENT_BUS_HOST=selenium-event-bus
      - SE_SESSIONS_MAP_HOST=selenium-sessions
      - SE_SESSION_QUEUE_HOST=selenium-session-queue
    networks:
      - healenium

  selenium-router:
    image: selenium/router:4.34.0-20250717
    container_name: selenium-router
    ports:
      - "4444:4444"
    depends_on:
      - selenium-distributor
      - selenium-sessions
      - selenium-session-queue
    environment:
      - SE_DISTRIBUTOR_HOST=selenium-distributor
      - SE_SESSIONS_MAP_HOST=selenium-sessions
      - SE_SESSION_QUEUE_HOST=selenium-session-queue
    networks:
      - healenium

  # ========================================
  # HEALENIUM SERVICES
  # ========================================
  postgres-db:
    image: postgres:15.5-alpine
    container_name: postgres-db
    restart: always
    ports:
      - "5432:5432"
    volumes:
      - ./db/sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    environment:
      - POSTGRES_DB=healenium
      - POSTGRES_USER=healenium_user
      - POSTGRES_PASSWORD=YDk2nmNs4s9aCP6K
    networks:
      - healenium

  healenium:
    image: healenium/hlm-backend:3.4.6
    container_name: healenium
    restart: on-failure
    ports:
      - "7878:7878"
    links:
      - postgres-db
    environment:
      - SPRING_POSTGRES_DB=healenium
      - SPRING_POSTGRES_SCHEMA=healenium
      - SPRING_POSTGRES_USER=healenium_user
      - SPRING_POSTGRES_PASSWORD=YDk2nmNs4s9aCP6K
      - SPRING_POSTGRES_DB_HOST=postgres-db
      - KEY_SELECTOR_URL=false
      - COLLECT_METRICS=true
      - FIND_ELEMENTS_AUTO_HEALING=true
      - HLM_LOG_LEVEL=debug
      - JAVA_OPTS=-Xms512m -Xmx4g
    volumes:
      - ./screenshots/:/screenshots
      - ./logs/:/logs
    networks:
      - healenium

  selector-imitator:
    image: healenium/hlm-selector-imitator:1.4
    container_name: selector-imitator
    restart: on-failure
    ports:
      - "8000:8000"
    networks:
      - healenium

  hlm-proxy:
    image: healenium/hlm-proxy:2.1.4
    container_name: hlm-proxy
    restart: on-failure
    ports:
      - "8085:8085"
    environment:
      - RECOVERY_TRIES=1
      - SCORE_CAP=.6
      - HEAL_ENABLED=true
      - SELENIUM_SERVER_URL=http://selenium-router:4444/wd/hub
      - HEALENIUM_SERVER_URL=http://localhost:7878
      - HEALENIUM_SERVICE=http://healenium:7878
      - IMITATE_SERVICE=http://selector-imitator:8000
      - HLM_LOG_LEVEL=debug
    volumes:
      - ./logs/:/logs
    networks:
      - healenium

  # ========================================
  # BROWSER NODES - MODERN DISTRIBUTED GRID
  # ========================================

  # Chrome Nodes (6 nodes for high parallel execution capacity)
  chrome-1:
    image: selenium/node-chrome:4.34.0-20250717
    platform: linux/amd64
    volumes:
      - ./screenshots:/usr/src/myapp/screenshots
    container_name: node-chrome-1
    shm_size: 2gb
    depends_on:
      - selenium-event-bus
    environment:
      - SE_EVENT_BUS_HOST=selenium-event-bus
      - SE_NODE_MAX_INSTANCES=1
      - SE_NODE_MAX_SESSIONS=1
      - SE_NODE_OVERRIDE_MAX_SESSIONS=true
      - SE_NODE_SESSION_TIMEOUT=700
      - SE_OPTS=--enable-managed-downloads true
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    networks:
      - healenium

  chrome-2:
    image: selenium/node-chrome:4.34.0-20250717
    platform: linux/amd64
    volumes:
      - ./screenshots:/usr/src/myapp/screenshots
    container_name: node-chrome-2
    shm_size: 2gb
    depends_on:
      - selenium-event-bus
    environment:
      - SE_EVENT_BUS_HOST=selenium-event-bus
      - SE_NODE_MAX_INSTANCES=1
      - SE_NODE_MAX_SESSIONS=1
      - SE_NODE_OVERRIDE_MAX_SESSIONS=true
      - SE_NODE_SESSION_TIMEOUT=700
      - SE_OPTS=--enable-managed-downloads true
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    networks:
      - healenium

  chrome-3:
    image: selenium/node-chrome:4.34.0-20250717
    platform: linux/amd64
    volumes:
      - ./screenshots:/usr/src/myapp/screenshots
    container_name: node-chrome-3
    shm_size: 2gb
    depends_on:
      - selenium-event-bus
    environment:
      - SE_EVENT_BUS_HOST=selenium-event-bus
      - SE_NODE_MAX_INSTANCES=1
      - SE_NODE_MAX_SESSIONS=1
      - SE_NODE_OVERRIDE_MAX_SESSIONS=true
      - SE_NODE_SESSION_TIMEOUT=700
      - SE_OPTS=--enable-managed-downloads true
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    networks:
      - healenium

  chrome-4:
    image: selenium/node-chrome:4.34.0-20250717
    platform: linux/amd64
    volumes:
      - ./screenshots:/usr/src/myapp/screenshots
    container_name: node-chrome-4
    shm_size: 2gb
    depends_on:
      - selenium-event-bus
    environment:
      - SE_EVENT_BUS_HOST=selenium-event-bus
      - SE_NODE_MAX_INSTANCES=1
      - SE_NODE_MAX_SESSIONS=1
      - SE_NODE_OVERRIDE_MAX_SESSIONS=true
      - SE_NODE_SESSION_TIMEOUT=700
      - SE_OPTS=--enable-managed-downloads true
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    networks:
      - healenium

  chrome-5:
    image: selenium/node-chrome:4.34.0-20250717
    platform: linux/amd64
    volumes:
      - ./screenshots:/usr/src/myapp/screenshots
    container_name: node-chrome-5
    shm_size: 2gb
    depends_on:
      - selenium-event-bus
    environment:
      - SE_EVENT_BUS_HOST=selenium-event-bus
      - SE_NODE_MAX_INSTANCES=1
      - SE_NODE_MAX_SESSIONS=1
      - SE_NODE_OVERRIDE_MAX_SESSIONS=true
      - SE_NODE_SESSION_TIMEOUT=700
      - SE_OPTS=--enable-managed-downloads true
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    networks:
      - healenium

  chrome-6:
    image: selenium/node-chrome:4.34.0-20250717
    platform: linux/amd64
    volumes:
      - ./screenshots:/usr/src/myapp/screenshots
    container_name: node-chrome-6
    shm_size: 2gb
    depends_on:
      - selenium-event-bus
    environment:
      - SE_EVENT_BUS_HOST=selenium-event-bus
      - SE_NODE_MAX_INSTANCES=1
      - SE_NODE_MAX_SESSIONS=1
      - SE_NODE_OVERRIDE_MAX_SESSIONS=true
      - SE_NODE_SESSION_TIMEOUT=700
      - SE_OPTS=--enable-managed-downloads true
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    networks:
      - healenium

  # Edge Nodes (6 nodes for high parallel execution capacity)
  edge-1:
    image: selenium/node-edge:4.34.0-20250717
    platform: linux/amd64
    volumes:
      - ./screenshots:/usr/src/myapp/screenshots
    container_name: node-edge-1
    shm_size: 2gb
    depends_on:
      - selenium-event-bus
    environment:
      - SE_EVENT_BUS_HOST=selenium-event-bus
      - SE_NODE_MAX_INSTANCES=1
      - SE_NODE_MAX_SESSIONS=1
      - SE_NODE_OVERRIDE_MAX_SESSIONS=true
      - SE_NODE_SESSION_TIMEOUT=700
      - SE_OPTS=--enable-managed-downloads true
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    networks:
      - healenium

  edge-2:
    image: selenium/node-edge:4.34.0-20250717
    platform: linux/amd64
    volumes:
      - ./screenshots:/usr/src/myapp/screenshots
    container_name: node-edge-2
    shm_size: 2gb
    depends_on:
      - selenium-event-bus
    environment:
      - SE_EVENT_BUS_HOST=selenium-event-bus
      - SE_NODE_MAX_INSTANCES=1
      - SE_NODE_MAX_SESSIONS=1
      - SE_NODE_OVERRIDE_MAX_SESSIONS=true
      - SE_NODE_SESSION_TIMEOUT=700
      - SE_OPTS=--enable-managed-downloads true
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    networks:
      - healenium

  edge-3:
    image: selenium/node-edge:4.34.0-20250717
    platform: linux/amd64
    volumes:
      - ./screenshots:/usr/src/myapp/screenshots
    container_name: node-edge-3
    shm_size: 2gb
    depends_on:
      - selenium-event-bus
    environment:
      - SE_EVENT_BUS_HOST=selenium-event-bus
      - SE_NODE_MAX_INSTANCES=1
      - SE_NODE_MAX_SESSIONS=1
      - SE_NODE_OVERRIDE_MAX_SESSIONS=true
      - SE_NODE_SESSION_TIMEOUT=700
      - SE_OPTS=--enable-managed-downloads true
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    networks:
      - healenium

  edge-4:
    image: selenium/node-edge:4.34.0-20250717
    platform: linux/amd64
    volumes:
      - ./screenshots:/usr/src/myapp/screenshots
    container_name: node-edge-4
    shm_size: 2gb
    depends_on:
      - selenium-event-bus
    environment:
      - SE_EVENT_BUS_HOST=selenium-event-bus
      - SE_NODE_MAX_INSTANCES=1
      - SE_NODE_MAX_SESSIONS=1
      - SE_NODE_OVERRIDE_MAX_SESSIONS=true
      - SE_NODE_SESSION_TIMEOUT=700
      - SE_OPTS=--enable-managed-downloads true
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    networks:
      - healenium

  edge-5:
    image: selenium/node-edge:4.34.0-20250717
    platform: linux/amd64
    volumes:
      - ./screenshots:/usr/src/myapp/screenshots
    container_name: node-edge-5
    shm_size: 2gb
    depends_on:
      - selenium-event-bus
    environment:
      - SE_EVENT_BUS_HOST=selenium-event-bus
      - SE_NODE_MAX_INSTANCES=1
      - SE_NODE_MAX_SESSIONS=1
      - SE_NODE_OVERRIDE_MAX_SESSIONS=true
      - SE_NODE_SESSION_TIMEOUT=700
      - SE_OPTS=--enable-managed-downloads true
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    networks:
      - healenium

  edge-6:
    image: selenium/node-edge:4.34.0-20250717
    platform: linux/amd64
    volumes:
      - ./screenshots:/usr/src/myapp/screenshots
    container_name: node-edge-6
    shm_size: 2gb
    depends_on:
      - selenium-event-bus
    environment:
      - SE_EVENT_BUS_HOST=selenium-event-bus
      - SE_NODE_MAX_INSTANCES=1
      - SE_NODE_MAX_SESSIONS=1
      - SE_NODE_OVERRIDE_MAX_SESSIONS=true
      - SE_NODE_SESSION_TIMEOUT=700
      - SE_OPTS=--enable-managed-downloads true
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    networks:
      - healenium

  # Firefox Nodes (6 nodes for high parallel execution capacity)
  firefox-1:
    image: selenium/node-firefox:4.34.0-20250717
    platform: linux/amd64
    volumes:
      - ./screenshots:/usr/src/myapp/screenshots
    container_name: node-firefox-1
    shm_size: 2gb
    depends_on:
      - selenium-event-bus
    environment:
      - SE_EVENT_BUS_HOST=selenium-event-bus
      - SE_NODE_MAX_INSTANCES=1
      - SE_NODE_MAX_SESSIONS=1
      - SE_NODE_OVERRIDE_MAX_SESSIONS=true
      - SE_NODE_SESSION_TIMEOUT=700
      - SE_OPTS=--enable-managed-downloads true
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    networks:
      - healenium

  firefox-2:
    image: selenium/node-firefox:4.34.0-20250717
    platform: linux/amd64
    volumes:
      - ./screenshots:/usr/src/myapp/screenshots
    container_name: node-firefox-2
    shm_size: 2gb
    depends_on:
      - selenium-event-bus
    environment:
      - SE_EVENT_BUS_HOST=selenium-event-bus
      - SE_NODE_MAX_INSTANCES=1
      - SE_NODE_MAX_SESSIONS=1
      - SE_NODE_OVERRIDE_MAX_SESSIONS=true
      - SE_NODE_SESSION_TIMEOUT=700
      - SE_OPTS=--enable-managed-downloads true
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    networks:
      - healenium

  firefox-3:
    image: selenium/node-firefox:4.34.0-20250717
    platform: linux/amd64
    volumes:
      - ./screenshots:/usr/src/myapp/screenshots
    container_name: node-firefox-3
    shm_size: 2gb
    depends_on:
      - selenium-event-bus
    environment:
      - SE_EVENT_BUS_HOST=selenium-event-bus
      - SE_NODE_MAX_INSTANCES=1
      - SE_NODE_MAX_SESSIONS=1
      - SE_NODE_OVERRIDE_MAX_SESSIONS=true
      - SE_NODE_SESSION_TIMEOUT=700
      - SE_OPTS=--enable-managed-downloads true
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    networks:
      - healenium

  firefox-4:
    image: selenium/node-firefox:4.34.0-20250717
    platform: linux/amd64
    volumes:
      - ./screenshots:/usr/src/myapp/screenshots
    container_name: node-firefox-4
    shm_size: 2gb
    depends_on:
      - selenium-event-bus
    environment:
      - SE_EVENT_BUS_HOST=selenium-event-bus
      - SE_NODE_MAX_INSTANCES=1
      - SE_NODE_MAX_SESSIONS=1
      - SE_NODE_OVERRIDE_MAX_SESSIONS=true
      - SE_NODE_SESSION_TIMEOUT=700
      - SE_OPTS=--enable-managed-downloads true
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    networks:
      - healenium

  firefox-5:
    image: selenium/node-firefox:4.34.0-20250717
    platform: linux/amd64
    volumes:
      - ./screenshots:/usr/src/myapp/screenshots
    container_name: node-firefox-5
    shm_size: 2gb
    depends_on:
      - selenium-event-bus
    environment:
      - SE_EVENT_BUS_HOST=selenium-event-bus
      - SE_NODE_MAX_INSTANCES=1
      - SE_NODE_MAX_SESSIONS=1
      - SE_NODE_OVERRIDE_MAX_SESSIONS=true
      - SE_NODE_SESSION_TIMEOUT=700
      - SE_OPTS=--enable-managed-downloads true
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    networks:
      - healenium

  firefox-6:
    image: selenium/node-firefox:4.34.0-20250717
    platform: linux/amd64
    volumes:
      - ./screenshots:/usr/src/myapp/screenshots
    container_name: node-firefox-6
    shm_size: 2gb
    depends_on:
      - selenium-event-bus
    environment:
      - SE_EVENT_BUS_HOST=selenium-event-bus
      - SE_NODE_MAX_INSTANCES=1
      - SE_NODE_MAX_SESSIONS=1
      - SE_NODE_OVERRIDE_MAX_SESSIONS=true
      - SE_NODE_SESSION_TIMEOUT=700
      - SE_OPTS=--enable-managed-downloads true
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    networks:
      - healenium



networks:
  healenium:
    name: healenium
