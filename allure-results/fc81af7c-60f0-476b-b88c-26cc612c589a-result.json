{"uuid": "fc81af7c-60f0-476b-b88c-26cc612c589a", "historyId": "35f85912395adc0ac69cdfc6a9bd434b", "fullName": "eastnets.screening.regression.formatmanager.FormatManager_TC002.formatManager_TC002", "labels": [{"name": "package", "value": "eastnets.screening.regression.formatmanager.FormatManager_TC002"}, {"name": "testClass", "value": "eastnets.screening.regression.formatmanager.FormatManager_TC002"}, {"name": "testMethod", "value": "formatManager_TC002"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Format Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.formatmanager.FormatManager_TC002"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-3(38)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON><PERSON>"}, {"name": "tag", "value": "Regression"}], "links": [], "name": "verify that the unmatched rule appears in the detection details from the results tab", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Format manager - Additional context variables \n* verify that the additional context variables appear when adding a good guy\n* verify the out of context result based on the violation filter added\n* verify the suspected record selection in drop down list\n* verify that the unmatched rule appears in the detection details from the results tab", "steps": [{"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-636653179', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@686f7d6e, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-636653179', officialDate='null', entry=[ListEntry{type='null', name='EntryName-636653179', firstName='EntryFirstName-636653179', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-636653179', firstName='EntryFirstName-636653179', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235647578, "stop": 1754235647578}, {"name": "Connect to Database and Check if User Profile = full-right-profile_06 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235647579, "stop": 1754235647579}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235647579, "stop": 1754235647579}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235647668, "stop": 1754235647668}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_06' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235647668, "stop": 1754235647668}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235647671, "stop": 1754235647671}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235647671, "stop": 1754235647671}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235647672, "stop": 1754235647672}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235647712, "stop": 1754235647712}, {"name": "Delete From tListSetProfile where profile_id in (9)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235647712, "stop": 1754235647712}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235647717, "stop": 1754235647717}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235647717, "stop": 1754235647717}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235647718, "stop": 1754235647718}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235647718, "stop": 1754235647718}, {"name": "Search for list by listName = ListName-636653179 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235671632, "stop": 1754235671632}, {"name": "Set zone : Common Zone 06", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235686336, "stop": 1754235686336}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235688832, "stop": 1754235688832}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235689895, "stop": 1754235689895}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235690513, "stop": 1754235690513}, {"name": "Set template name = templateName-636653179", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235690796, "stop": 1754235690796}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235693691, "stop": 1754235693691}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235694555, "stop": 1754235694555}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235696900, "stop": 1754235696900}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235697595, "stop": 1754235697595}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235716244, "stop": 1754235716244}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235716244, "stop": 1754235716244}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235781558, "stop": 1754235781558}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235781559, "stop": 1754235781559}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235781559, "stop": 1754235781559}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235781586, "stop": 1754235781586}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_06'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235781586, "stop": 1754235781586}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235781589, "stop": 1754235781589}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235781589, "stop": 1754235781589}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235781589, "stop": 1754235781589}, {"name": "Validation message = Syntax is valid!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235805730, "stop": 1754235805730}, {"name": "Validation message = Syntax is valid!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235805731, "stop": 1754235805731}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235809454, "stop": 1754235809454}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235809454, "stop": 1754235809454}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235828928, "stop": 1754235828928}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235877303, "stop": 1754235877303}, {"name": "Validation message = File sent to the server for processing with id [3098]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235892489, "stop": 1754235892489}, {"name": "Alert Message = File sent to the server for processing with id [3098]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235892489, "stop": 1754235892489}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235893631, "stop": 1754235893631}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235902529, "stop": 1754235902529}, {"name": "Detection ID = 7994", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235903785, "stop": 1754235903785}, {"name": "Actual validation Message = Good guy successfully created!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235913504, "stop": 1754235913504}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235914426, "stop": 1754235914426}, {"name": "Validation message = File sent to the server for processing with id [3100]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235930572, "stop": 1754235930572}, {"name": "Alert Message = File sent to the server for processing with id [3100]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235930572, "stop": 1754235930572}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235932373, "stop": 1754235932373}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235941604, "stop": 1754235941604}, {"name": "Detection ID = 7999", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235942719, "stop": 1754235942719}, {"name": "Status detection = Accepted", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235942992, "stop": 1754235942992}, {"name": "Status detection = External", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235943111, "stop": 1754235943111}, {"name": "Status detection = Reported", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235943189, "stop": 1754235943189}, {"name": "Status detection = Accepted", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235943826, "stop": 1754235943826}, {"name": "Status detection = External", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235943964, "stop": 1754235943964}, {"name": "Status detection = Reported", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235944219, "stop": 1754235944219}, {"name": "Status detection = Accepted", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235944560, "stop": 1754235944560}, {"name": "Status detection = External", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235944626, "stop": 1754235944626}, {"name": "Status detection = Reported", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235944714, "stop": 1754235944714}, {"name": "Unmatched roles = CN <> '146'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235946448, "stop": 1754235946448}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754235647570, "stop": 1754235946916}