{"uuid": "b13070bc-b599-494e-aa56-6e29e939a907", "historyId": "c6142a835c74d3e0aa41bd07b46dcd7d", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC005.scanManager_TC005", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC005"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC005"}, {"name": "testMethod", "value": "scanManager_TC005"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC005"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is able to Print an Alert created from 'File Scan' from Result Tab", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to Print an Alert created from 'File Scan' from Result Tab", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235249555, "stop": 1754235249555}, {"name": "Validation message = File sent to the server for processing with id [3087]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235268335, "stop": 1754235268335}, {"name": "Alert Message = File sent to the server for processing with id [3087]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235268335, "stop": 1754235268335}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235268866, "stop": 1754235268866}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235279124, "stop": 1754235279124}, {"name": "Detection ID = 7965", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235280780, "stop": 1754235280780}, {"name": "Start Exporting Violation With Print Scope all And Document Type PDF", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235280816, "stop": 1754235280816}, {"name": "Click on close dialog button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235291210, "stop": 1754235291210}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754235249293, "stop": 1754235322380}