{"uuid": "8e26e141-0513-4ecb-a48f-7a3189fb5897", "name": "Safe Watch Filtering", "children": ["147b6b4c-41f8-43a0-aff9-135c08919f95", "3d1f5726-f9af-420f-bbbf-61044cd6aa87", "1f5ccd75-2423-43da-b68e-15106a484419", "d7739a6e-48ea-4bf6-9ffb-61573abd785d"], "befores": [{"name": "Setting up allure report", "status": "passed", "stage": "finished", "description": "Setting up allure report", "steps": [{"name": "Setting up allure report environment data: Edge", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234470923, "stop": 1754234470925}], "attachments": [], "parameters": [], "start": 1754234470918, "stop": 1754234471032}], "afters": [{"name": "rerunFailedTestCases", "status": "passed", "stage": "finished", "description": "", "steps": [{"name": "========================================", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238475136, "stop": 1754238475136}, {"name": "RERUNNING FAILED TEST CASES", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238475136, "stop": 1754238475136}, {"name": "========================================", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238475136, "stop": 1754238475136}, {"name": "Found 6 failed test(s) to rerun:", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238475136, "stop": 1754238475136}, {"name": "  - eastnets.screening.regression.iso20022configurations.ISO20022_TC002.iso20022_TC0012", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238475136, "stop": 1754238475136}, {"name": "  - eastnets.screening.regression.iso20022configurations.ISO20022_TC003.iso20022_TC003", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238475136, "stop": 1754238475136}, {"name": "  - eastnets.screening.regression.iso20022configurations.ISO20022_TC004.iso20022_TC0012", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238475137, "stop": 1754238475137}, {"name": "  - eastnets.screening.regression.iso20022configurations.ISO20022_TC005.iso20022_TC0012", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238475137, "stop": 1754238475137}, {"name": "  - eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC014.listManager_TC014", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238475137, "stop": 1754238475137}, {"name": "  - eastnets.screening.regression.listmanager.prefiltertests.PreFilter_TC003.PreFilter_TC003", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238475137, "stop": 1754238475137}, {"name": "", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238475137, "stop": 1754238475137}, {"name": "Creating new TestNG suite for failed tests...", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238475137, "stop": 1754238475137}, {"name": "Starting failed test rerun...", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238475138, "stop": 1754238475138}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238475164, "stop": 1754238475164}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238475165, "stop": 1754238475165}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238475165, "stop": 1754238475165}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238475173, "stop": 1754238475173}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238475173, "stop": 1754238475173}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238475173, "stop": 1754238475173}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238475177, "stop": 1754238475177}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238475178, "stop": 1754238475178}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238475178, "stop": 1754238475178}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238475182, "stop": 1754238475182}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238475182, "stop": 1754238475182}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238475182, "stop": 1754238475182}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238475190, "stop": 1754238475190}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238475192, "stop": 1754238475192}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238475193, "stop": 1754238475193}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238475197, "stop": 1754238475197}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238475197, "stop": 1754238475197}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238475197, "stop": 1754238475197}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238475197, "stop": 1754238475197}], "attachments": [], "parameters": [], "start": 1754238475135, "stop": 1754239628820}], "start": 1754234470892, "stop": 1754239628820}