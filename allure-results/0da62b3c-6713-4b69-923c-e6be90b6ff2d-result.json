{"uuid": "0da62b3c-6713-4b69-923c-e6be90b6ff2d", "historyId": "********************************", "fullName": "eastnets.screening.regression.formatmanager.FormatManager_TC001.formatManager_TC001", "labels": [{"name": "package", "value": "eastnets.screening.regression.formatmanager.FormatManager_TC001"}, {"name": "testClass", "value": "eastnets.screening.regression.formatmanager.FormatManager_TC001"}, {"name": "testMethod", "value": "formatManager_TC001"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Format Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.formatmanager.FormatManager_TC001"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-3(38)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON><PERSON>"}, {"name": "tag", "value": "Regression"}], "links": [], "name": "Verify that user is able to Import, Export, Edit and Delete a format of \"Scan\" type", "status": "failed", "statusDetails": {"known": false, "muted": false, "flaky": false, "message": "Test Failed due to an exception occurred in POM's method", "trace": "java.lang.AssertionError: Test Failed due to an exception occurred in POM's method\r\n\tat org.testng.Assert.fail(Assert.java:98)\r\n\tat core.ExceptionHandler.onExceptionRaised(ExceptionHandler.java:13)\r\n\tat eastnets.screening.regression.formatmanager.FormatManager_TC001.formatManager_TC001(FormatManager_TC001.java:110)\r\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:578)\r\n\tat org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:677)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:221)\r\n\tat org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:50)\r\n\tat org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:969)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:194)\r\n\tat org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148)\r\n\tat org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128)\r\n\tat java.base/java.util.ArrayList.forEach(ArrayList.java:1511)\r\n\tat org.testng.TestRunner.privateRun(TestRunner.java:829)\r\n\tat org.testng.TestRunner.run(TestRunner.java:602)\r\n\tat org.testng.SuiteRunner.runTest(SuiteRunner.java:437)\r\n\tat org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:475)\r\n\tat org.testng.internal.thread.ThreadUtil.lambda$execute$0(ThreadUtil.java:58)\r\n\tat java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)\r\n\tat java.base/java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:132)\r\n\tat java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:113)\r\n\tat java.base/java.util.concurrent.AbstractExecutorService.invokeAll(AbstractExecutorService.java:274)\r\n\tat org.testng.internal.thread.ThreadUtil.execute(ThreadUtil.java:64)\r\n\tat org.testng.SuiteRunner.runInParallelTestMode(SuiteRunner.java:458)\r\n\tat org.testng.SuiteRunner.privateRun(SuiteRunner.java:389)\r\n\tat org.testng.SuiteRunner.run(SuiteRunner.java:330)\r\n\tat org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)\r\n\tat org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)\r\n\tat org.testng.TestNG.runSuitesSequentially(TestNG.java:1256)\r\n\tat org.testng.TestNG.runSuitesLocally(TestNG.java:1176)\r\n\tat org.testng.TestNG.runSuites(TestNG.java:1099)\r\n\tat org.testng.TestNG.run(TestNG.java:1067)\r\n\tat com.intellij.rt.testng.IDEARemoteTestNG.run(IDEARemoteTestNG.java:65)\r\n\tat com.intellij.rt.testng.RemoteTestNGStarter.main(RemoteTestNGStarter.java:105)\r\nCaused by: org.openqa.selenium.InvalidArgumentException: invalid argument: File not found : C:\\Users\\<USER>\\IdeaProjects\\SWS_selenium/src/test/resources/uploadsAndDownloads/downloads/ExportFormat_Format_357243246[2025_08_02  16_40].xml\n  (Session info: MicrosoftEdge=138.0.3351.95)\nBuild info: version: '4.13.0', revision: 'ba948ece5b*'\nSystem info: os.name: 'Windows Server 2022', os.arch: 'amd64', os.version: '10.0', java.version: '19.0.2'\nDriver info: org.openqa.selenium.remote.RemoteWebDriver\nCommand: [2eba547d46b37a45ccf0cc055a32b0e8, sendKeysToElement {id=f.B31A31D9E785459638A05508D64FB2B2.d.1A081B9BE379BA83BD0D653D0A6F2038.e.2741, value=[Ljava.lang.CharSequence;@27cc9b20}]\nCapabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:37669}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.MC...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: Proxy(), se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 2468c749b3f1, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\nElement: [[RemoteWebDriver: MicrosoftEdge on linux (2eba547d46b37a45ccf0cc055a32b0e8)] -> id: FormatManagerForm:Homepage_business:Formats:uploadFile_input]\nSession ID: 2eba547d46b37a45ccf0cc055a32b0e8\r\n\tat java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:67)\r\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\r\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:484)\r\n\tat org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.createException(W3CHttpResponseCodec.java:200)\r\n\tat org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:133)\r\n\tat org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:52)\r\n\tat org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:191)\r\n\tat org.openqa.selenium.remote.TracedCommandExecutor.execute(TracedCommandExecutor.java:51)\r\n\tat org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:602)\r\n\tat org.openqa.selenium.remote.RemoteWebElement.execute(RemoteWebElement.java:224)\r\n\tat org.openqa.selenium.remote.RemoteWebElement.sendKeys(RemoteWebElement.java:111)\r\n\tat core.gui.Controls.setTextBoxValue(Controls.java:39)\r\n\tat eastnets.screening.gui.formatManager.FormatManager.setBrowserButton(FormatManager.java:60)\r\n\tat eastnets.screening.control.FormatManagerControl.importFormat(FormatManagerControl.java:71)\r\n\tat eastnets.screening.regression.formatmanager.FormatManager_TC001.formatManager_TC001(FormatManager_TC001.java:107)\r\n\t... 34 more\r\n"}, "stage": "finished", "description": "Verify that user is able to Import, Export, Edit and Delete a format of \"Scan\" type", "steps": [{"name": "response.getStatus()200", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142057328, "stop": 1754142057328}, {"name": "UTF-8", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142057328, "stop": 1754142057328}, {"name": "1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142057329, "stop": 1754142057329}, {"name": "Attempting to save screenshot...", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142093093, "stop": 1754142093093}, {"name": "Screenshot saved at: C:\\Users\\<USER>\\IdeaProjects\\SWS_selenium\\FailedTestsScreenshots\\screenshot_1754142093093.png", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142093109, "stop": 1754142093109}, {"name": "Error occurred While logging in eastnets.screening.regression.formatmanager.FormatManager_TC001$5.formatManager_TC001 ----> invalid argument: File not found : C:\\Users\\<USER>\\IdeaProjects\\SWS_selenium/src/test/resources/uploadsAndDownloads/downloads/ExportFormat_Format_357243246[2025_08_02  16_40].xml\n  (Session info: MicrosoftEdge=138.0.3351.95)\nBuild info: version: '4.13.0', revision: 'ba948ece5b*'\nSystem info: os.name: 'Windows Server 2022', os.arch: 'amd64', os.version: '10.0', java.version: '19.0.2'\nDriver info: org.openqa.selenium.remote.RemoteWebDriver\nCommand: [2eba547d46b37a45ccf0cc055a32b0e8, sendKeysToElement {id=f.B31A31D9E785459638A05508D64FB2B2.d.1A081B9BE379BA83BD0D653D0A6F2038.e.2741, value=[Ljava.lang.CharSequence;@27cc9b20}]\nCapabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:37669}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.MC...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: Proxy(), se:bidiEnabled: false, se:cdp: ws://**********:4444/sessio..., se:cdpVersion: 138.0.3351.95, se:containerName: 2468c749b3f1, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://**********:4444/sessio..., se:vncEnabled: true, se:vncLocalAddress: ws://**********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\nElement: [[RemoteWebDriver: MicrosoftEdge on linux (2eba547d46b37a45ccf0cc055a32b0e8)] -> id: FormatManagerForm:Homepage_business:Formats:uploadFile_input]\nSession ID: 2eba547d46b37a45ccf0cc055a32b0e8", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142093146, "stop": 1754142093146}], "attachments": [{"name": "Screenshots", "source": "926bd09b-00de-42ab-a443-cab76bf6ff47-attachment.png"}], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "Format{testCaseTitle='Verify that user is able to Import, Export, Edit and Delete a format of \"Scan\" type', name='Format_%s', zone='null', type='Scan', fieldDelimiter='Separator', separator=',', entryType='null', xpath='null', fields=[FormatField{name='FIRST_NAME_%s', type='FIRST_NAME', xpath='null', scan=false, addToContext=false}, FormatField{name='LAST_NAME_%s', type='LAST_NAME', xpath='null', scan=false, addToContext=false}]}"}], "start": 1754141991549, "stop": 1754142093147}