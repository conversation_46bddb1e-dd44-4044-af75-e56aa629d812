{"uuid": "b6d3be2b-defe-414e-ad27-56d1b767ef56", "historyId": "c1c14de74bb43bbf5f8d10a287df71e", "fullName": "eastnets.admin.AdminTest.checkPermissionWhenAssignSecGroupProfileZone", "labels": [{"name": "package", "value": "eastnets.admin.AdminTest"}, {"name": "testClass", "value": "eastnets.admin.AdminTest"}, {"name": "testMethod", "value": "checkPermissionWhenAssignSecGroupProfileZone"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Admin Test Cases"}, {"name": "subSuite", "value": "eastnets.admin.AdminTest"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}], "links": [], "name": "Verify that the user that is assigned to Group, Profile, and Zone have permission to Release but don't have the permission to Block is able to block when this user is assigned to a second Group, Profile and Zone that have the permission to Block.", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that the user that is assigned to Group, Profile, and Zone have permission to Release but don't have the permission to Block is able to block when this user is assigned to a second Group, Profile and Zone that have the permission to Block.", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267003326, "stop": 1754267003326}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='sysadmin'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267003326, "stop": 1754267003326}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267003332, "stop": 1754267003332}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267003332, "stop": 1754267003332}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267003333, "stop": 1754267003333}, {"name": "Login with User Name = sysadmin and Password = manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267003476, "stop": 1754267003476}, {"name": "Operator test data = Operator{id=0, loginName='selenium-user-859712249', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone436740045', displayName='Zone (436740045)', description='Zone created with random number '436740045''}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267008372, "stop": 1754267008372}, {"name": "Group test data = Group{id=0, name='selenium-random-group-415281262', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-929395495', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone436740045', displayName='Zone (436740045)', description='Zone created with random number '436740045''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-859712249', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone436740045', displayName='Zone (436740045)', description='Zone created with random number '436740045''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267008372, "stop": 1754267008372}, {"name": "Create new zone .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267008372, "stop": 1754267008372}, {"name": "Zone test Data = Zone{id=0, name='Zone436740045', displayName='Zone (436740045)', description='Zone created with random number '436740045''}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267008372, "stop": 1754267008372}, {"name": "Check if zone with name = 'Zone436740045' exists", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267010373, "stop": 1754267010373}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267010644, "stop": 1754267010644}, {"name": "Enter name =Zone436740045", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267016098, "stop": 1754267016098}, {"name": "Enter display Name =Zone (436740045)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267016948, "stop": 1754267016948}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267017779, "stop": 1754267017779}, {"name": "Zone doesn't exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267018413, "stop": 1754267018413}, {"name": "Click add new zone button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267018413, "stop": 1754267018413}, {"name": "Set create new zone data.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267018699, "stop": 1754267018699}, {"name": "Set name = Zone436740045", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267018699, "stop": 1754267018699}, {"name": "Set display name = Zone (436740045)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267020559, "stop": 1754267020559}, {"name": "Set description = Zone created with random number '436740045'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267021484, "stop": 1754267021484}, {"name": "Capture zone id from UI = 161", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267023144, "stop": 1754267023144}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267023144, "stop": 1754267023144}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267025042, "stop": 1754267025042}, {"name": "Enter name =Zone436740045", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267030496, "stop": 1754267030496}, {"name": "Enter display Name =Zone (436740045)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267031354, "stop": 1754267031354}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267032071, "stop": 1754267032071}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267036460, "stop": 1754267036460}, {"name": "Enter name =Zone436740045", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267041864, "stop": 1754267041864}, {"name": "Enter display Name =Zone (436740045)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267043385, "stop": 1754267043385}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267044686, "stop": 1754267044686}, {"name": "Create first profile .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267045375, "stop": 1754267045375}, {"name": "Profile test Data = Profile{id=0, name='Test-Profile-929395495', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone436740045', displayName='Zone (436740045)', description='Zone created with random number '436740045''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267045375, "stop": 1754267045375}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267096800, "stop": 1754267096800}, {"name": "Set name = Test-Profile-929395495 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267102455, "stop": 1754267102455}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267103388, "stop": 1754267103388}, {"name": "Click add new profile button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267104361, "stop": 1754267104361}, {"name": "Set name = Test-Profile-929395495 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267104698, "stop": 1754267104698}, {"name": "Set description = Random profile for Selenium testing ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267106780, "stop": 1754267106780}, {"name": "Check enable checkbox to be true ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267108220, "stop": 1754267108220}, {"name": "Check write right checkbox to be Test-Profile-929395495 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267108903, "stop": 1754267108903}, {"name": "Select zone = Test-Profile-929395495 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267109583, "stop": 1754267109583}, {"name": "Add SWS item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267117152, "stop": 1754267117152}, {"name": "Click on the SWS item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267117152, "stop": 1754267117152}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267118103, "stop": 1754267118103}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267119083, "stop": 1754267119083}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267119347, "stop": 1754267119347}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267119461, "stop": 1754267119461}, {"name": "Set name = Test-Profile-929395495 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267125859, "stop": 1754267125859}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267145194, "stop": 1754267145194}, {"name": "Select profile from result view.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267146538, "stop": 1754267146538}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267147573, "stop": 1754267147573}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267149214, "stop": 1754267149214}, {"name": "Add en.Reporting Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267150971, "stop": 1754267150971}, {"name": "Click on the en.Reporting Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267150971, "stop": 1754267150971}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267151685, "stop": 1754267151685}, {"name": "Add SWIFT Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267153110, "stop": 1754267153110}, {"name": "Click on the SWIFT Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267153110, "stop": 1754267153110}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267153523, "stop": 1754267153523}, {"name": "Add SWP Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267155018, "stop": 1754267155018}, {"name": "Click on the SWP Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267155018, "stop": 1754267155018}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267155443, "stop": 1754267155443}, {"name": "Add Detection Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267156897, "stop": 1754267156897}, {"name": "Click on the Detection Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267156897, "stop": 1754267156897}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267157367, "stop": 1754267157367}, {"name": "Add Scan Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267158758, "stop": 1754267158758}, {"name": "Click on the Scan Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267158758, "stop": 1754267158758}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267159162, "stop": 1754267159162}, {"name": "Add Format Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267160605, "stop": 1754267160605}, {"name": "Click on the Format Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267160605, "stop": 1754267160605}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267161045, "stop": 1754267161045}, {"name": "Add Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267162457, "stop": 1754267162457}, {"name": "Click on the Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267162457, "stop": 1754267162457}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267162893, "stop": 1754267162893}, {"name": "Add Batch List Management item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267164358, "stop": 1754267164358}, {"name": "Click on the Batch List Management item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267164358, "stop": 1754267164358}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267164844, "stop": 1754267164844}, {"name": "Add ListExplorer item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267166212, "stop": 1754267166212}, {"name": "Click on the ListExplorer item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267166212, "stop": 1754267166212}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267166779, "stop": 1754267166779}, {"name": "Add ListSetManager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267168223, "stop": 1754267168223}, {"name": "Click on the ListSetManager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267168223, "stop": 1754267168223}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267168662, "stop": 1754267168662}, {"name": "Add List Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267170042, "stop": 1754267170045}, {"name": "Click on the List Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267170045, "stop": 1754267170045}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267170716, "stop": 1754267170716}, {"name": "Add File Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267172211, "stop": 1754267172211}, {"name": "Click on the File Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267172211, "stop": 1754267172211}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267172701, "stop": 1754267172701}, {"name": "Add Advanced Settings item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267174166, "stop": 1754267174166}, {"name": "Click on the Advanced Settings item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267174166, "stop": 1754267174166}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267174665, "stop": 1754267174665}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267176135, "stop": 1754267176135}, {"name": "Accept <PERSON><PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267176495, "stop": 1754267176496}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267178605, "stop": 1754267178605}, {"name": "Select Module Rights.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267181963, "stop": 1754267181963}, {"name": "Processing module 'SWIFT Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267183964, "stop": 1754267183964}, {"name": "Click on SWIFT Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267183964, "stop": 1754267183964}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267186660, "stop": 1754267186660}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267186914, "stop": 1754267186914}, {"name": "Processing module 'Detection Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267190187, "stop": 1754267190187}, {"name": "Click on Detection Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267190187, "stop": 1754267190187}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267191022, "stop": 1754267191022}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267191324, "stop": 1754267191324}, {"name": "Processing module 'Scan Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267193674, "stop": 1754267193674}, {"name": "Click on Scan Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267193674, "stop": 1754267193674}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267194614, "stop": 1754267194614}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267195160, "stop": 1754267195160}, {"name": "Processing module 'Format Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267197412, "stop": 1754267197412}, {"name": "Click on Format Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267197412, "stop": 1754267197412}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267198296, "stop": 1754267198296}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267198602, "stop": 1754267198602}, {"name": "Processing module 'Name Checker'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267200903, "stop": 1754267200903}, {"name": "Click on Name Checker", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267200903, "stop": 1754267200903}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267201829, "stop": 1754267201829}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267202248, "stop": 1754267202248}, {"name": "Processing module 'ListExplorer'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267205641, "stop": 1754267205641}, {"name": "Click on ListExplorer", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267205641, "stop": 1754267205641}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267206623, "stop": 1754267206623}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267207175, "stop": 1754267207175}, {"name": "Processing module 'ListSetManager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267209381, "stop": 1754267209381}, {"name": "Click on ListSetManager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267209381, "stop": 1754267209381}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267210372, "stop": 1754267210372}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267210830, "stop": 1754267210830}, {"name": "Processing module 'List Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267213217, "stop": 1754267213217}, {"name": "Click on List Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267213217, "stop": 1754267213217}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267214183, "stop": 1754267214183}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267214627, "stop": 1754267214627}, {"name": "Processing module 'File Scanner'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267217175, "stop": 1754267217175}, {"name": "Click on File Scanner", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267217175, "stop": 1754267217175}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267218467, "stop": 1754267218467}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267218808, "stop": 1754267218808}, {"name": "Processing module 'Advanced Settings'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267221207, "stop": 1754267221207}, {"name": "Click on Advanced Settings", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267221207, "stop": 1754267221207}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267223027, "stop": 1754267223027}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267223365, "stop": 1754267223365}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267224817, "stop": 1754267224817}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267225300, "stop": 1754267225300}, {"name": "Check if profile exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267227188, "stop": 1754267227188}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267227188, "stop": 1754267227188}, {"name": "Set name = Test-Profile-929395495 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267233448, "stop": 1754267233448}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267234242, "stop": 1754267234242}, {"name": "Profile exist = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267234984, "stop": 1754267234984}, {"name": "Create new operator.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267234984, "stop": 1754267234984}, {"name": "Operator test Data = Operator{id=0, loginName='selenium-user-859712249', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone436740045', displayName='Zone (436740045)', description='Zone created with random number '436740045''}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267234984, "stop": 1754267234984}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267237278, "stop": 1754267237278}, {"name": "Set login name = selenium-user-859712249", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267242654, "stop": 1754267242654}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267243683, "stop": 1754267243683}, {"name": "Click Create new operator button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267244607, "stop": 1754267244607}, {"name": "Set login name = selenium-user-859712249", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267244879, "stop": 1754267244879}, {"name": "Set first name = Random", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267246839, "stop": 1754267246839}, {"name": "Set last name = User", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267247678, "stop": 1754267247678}, {"name": "Set password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267248649, "stop": 1754267248649}, {"name": "Set confirm password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267249936, "stop": 1754267249936}, {"name": "Set enable flag = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267250841, "stop": 1754267250841}, {"name": "Set reset flag = false", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267251690, "stop": 1754267251690}, {"name": "Select zone  = Zone (436740045)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267253175, "stop": 1754267253175}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267260046, "stop": 1754267260046}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267260384, "stop": 1754267260384}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267268651, "stop": 1754267268651}, {"name": "Set login name = selenium-user-859712249", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267274151, "stop": 1754267274151}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267275187, "stop": 1754267275187}, {"name": "Create first group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267276264, "stop": 1754267276264}, {"name": "Group test Data = Group{id=0, name='selenium-random-group-415281262', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-929395495', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone436740045', displayName='Zone (436740045)', description='Zone created with random number '436740045''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-859712249', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone436740045', displayName='Zone (436740045)', description='Zone created with random number '436740045''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267276264, "stop": 1754267276264}, {"name": "Connect to database to remove Link between profile = Test-Profile-929395495 and Permission Allow to change detection status to Real Violation(Block detection)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267318753, "stop": 1754267318753}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267318787, "stop": 1754267318787}, {"name": "SELECT P.NAME as PRO<PERSON><PERSON>_NAME , PR.PROFILE_ID as PROFILE_ID , PR.GRANT_ID as GRANT_ID , PR.GRANT_RIGHT_ID as GRANT_RIGHT_ID , PR.GRANT_TYPE_ID as GRANT_TYPE_ID   FROM tModuleFunctions M   LEFT JOIN tProfileRights PR On PR.GRANT_ID  = M.ID     LEFT Join tProfiles P ON P.id =PR.PROFILE_ID   WHERE DESCRIPTION ='Allow to change detection status to Real Violation(Block detection)' and P.NAME = 'Test-Profile-929395495'  and PR.GRANT_TYPE_ID =3", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267318787, "stop": 1754267318787}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267318802, "stop": 1754267318802}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267318802, "stop": 1754267318802}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267318802, "stop": 1754267318802}, {"name": "Delete function permission 'Allow to change detection status to Real Violation(Block detection)' from DataBase", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267318802, "stop": 1754267318802}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267318832, "stop": 1754267318832}, {"name": "DELETE FROM tProfileRights  WHERE GRANT_ID  ='130' and PROFILE_ID ='189' and GRANT_TYPE_ID =3", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267318832, "stop": 1754267318832}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267318852, "stop": 1754267318852}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267318852, "stop": 1754267318852}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267318852, "stop": 1754267318852}, {"name": "Create second zone .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267318853, "stop": 1754267318853}, {"name": "Zone test Data = Zone{id=0, name='Zone377352367', displayName='Zone (377352367)', description='Zone created with random number '377352367''}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267318853, "stop": 1754267318853}, {"name": "Check if zone with name = 'Zone377352367' exists", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267320920, "stop": 1754267320920}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267321156, "stop": 1754267321156}, {"name": "Enter name =Zone377352367", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267326560, "stop": 1754267326560}, {"name": "Enter display Name =Zone (377352367)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267327567, "stop": 1754267327567}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267328658, "stop": 1754267328658}, {"name": "Zone doesn't exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267329332, "stop": 1754267329332}, {"name": "Click add new zone button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267329332, "stop": 1754267329332}, {"name": "Set create new zone data.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267329569, "stop": 1754267329569}, {"name": "Set name = Zone377352367", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267329569, "stop": 1754267329569}, {"name": "Set display name = Zone (377352367)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267331639, "stop": 1754267331639}, {"name": "Set description = Zone created with random number '377352367'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267332445, "stop": 1754267332445}, {"name": "Capture zone id from UI = 162", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267333714, "stop": 1754267333714}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267333714, "stop": 1754267333714}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267335286, "stop": 1754267335286}, {"name": "Enter name =Zone377352367", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267340734, "stop": 1754267340734}, {"name": "Enter display Name =Zone (377352367)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267341736, "stop": 1754267341736}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267342848, "stop": 1754267342848}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267346149, "stop": 1754267346149}, {"name": "Enter name =Zone377352367", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267351530, "stop": 1754267351530}, {"name": "Enter display Name =Zone (377352367)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267352228, "stop": 1754267352228}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267353027, "stop": 1754267353027}, {"name": "Create second profile .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267353623, "stop": 1754267353623}, {"name": "Profile test Data = Profile{id=0, name='Test-Profile-916328070', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone377352367', displayName='Zone (377352367)', description='Zone created with random number '377352367''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267353623, "stop": 1754267353623}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267355326, "stop": 1754267355326}, {"name": "Set name = Test-Profile-916328070 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267360678, "stop": 1754267360678}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267361370, "stop": 1754267361370}, {"name": "Click add new profile button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267362172, "stop": 1754267362172}, {"name": "Set name = Test-Profile-916328070 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267362418, "stop": 1754267362418}, {"name": "Set description = Random profile for Selenium testing ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267364054, "stop": 1754267364054}, {"name": "Check enable checkbox to be true ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267364943, "stop": 1754267364943}, {"name": "Check write right checkbox to be Test-Profile-916328070 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267365606, "stop": 1754267365606}, {"name": "Select zone = Test-Profile-916328070 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267365856, "stop": 1754267365856}, {"name": "Add SWS item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267372128, "stop": 1754267372128}, {"name": "Click on the SWS item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267372128, "stop": 1754267372128}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267372538, "stop": 1754267372538}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267373480, "stop": 1754267373480}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267373745, "stop": 1754267373745}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267373849, "stop": 1754267373849}, {"name": "Set name = Test-Profile-916328070 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267380173, "stop": 1754267380173}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267380831, "stop": 1754267380831}, {"name": "Select profile from result view.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267381583, "stop": 1754267381583}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267382069, "stop": 1754267382069}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267383606, "stop": 1754267383606}, {"name": "Add en.Reporting Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267384575, "stop": 1754267384575}, {"name": "Click on the en.Reporting Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267384575, "stop": 1754267384575}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267385043, "stop": 1754267385043}, {"name": "Add SWIFT Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267386439, "stop": 1754267386439}, {"name": "Click on the SWIFT Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267386439, "stop": 1754267386439}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267386823, "stop": 1754267386823}, {"name": "Add SWP Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267388235, "stop": 1754267388235}, {"name": "Click on the SWP Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267388235, "stop": 1754267388235}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267388691, "stop": 1754267388691}, {"name": "Add Detection Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267390134, "stop": 1754267390134}, {"name": "Click on the Detection Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267390134, "stop": 1754267390134}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267390563, "stop": 1754267390563}, {"name": "Add Scan Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267392174, "stop": 1754267392174}, {"name": "Click on the Scan Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267392174, "stop": 1754267392174}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267393220, "stop": 1754267393220}, {"name": "Add Format Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267394653, "stop": 1754267394653}, {"name": "Click on the Format Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267394653, "stop": 1754267394653}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267395127, "stop": 1754267395127}, {"name": "Add Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267396600, "stop": 1754267396600}, {"name": "Click on the Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267396600, "stop": 1754267396600}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267397780, "stop": 1754267397780}, {"name": "Add Batch List Management item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267399220, "stop": 1754267399220}, {"name": "Click on the Batch List Management item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267399220, "stop": 1754267399220}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267399631, "stop": 1754267399631}, {"name": "Add ListExplorer item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267401092, "stop": 1754267401092}, {"name": "Click on the ListExplorer item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267401092, "stop": 1754267401092}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267401627, "stop": 1754267401627}, {"name": "Add ListSetManager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267403129, "stop": 1754267403129}, {"name": "Click on the ListSetManager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267403129, "stop": 1754267403129}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267403529, "stop": 1754267403529}, {"name": "Add List Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267405022, "stop": 1754267405022}, {"name": "Click on the List Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267405022, "stop": 1754267405022}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267405585, "stop": 1754267405585}, {"name": "Add File Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267407210, "stop": 1754267407210}, {"name": "Click on the File Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267407210, "stop": 1754267407210}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267408091, "stop": 1754267408091}, {"name": "Add Advanced Settings item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267409494, "stop": 1754267409494}, {"name": "Click on the Advanced Settings item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267409494, "stop": 1754267409494}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267410150, "stop": 1754267410150}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267411600, "stop": 1754267411600}, {"name": "Accept <PERSON><PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267412222, "stop": 1754267412222}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267414638, "stop": 1754267414638}, {"name": "Select Module Rights.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267415439, "stop": 1754267415439}, {"name": "Processing module 'SWIFT Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267417440, "stop": 1754267417440}, {"name": "Click on SWIFT Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267417440, "stop": 1754267417440}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267418400, "stop": 1754267418400}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267418753, "stop": 1754267418753}, {"name": "Processing module 'Detection Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267422165, "stop": 1754267422165}, {"name": "Click on Detection Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267422165, "stop": 1754267422165}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267424821, "stop": 1754267424821}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267425336, "stop": 1754267425336}, {"name": "Processing module 'Scan Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267427675, "stop": 1754267427675}, {"name": "Click on Scan Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267427675, "stop": 1754267427675}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267445896, "stop": 1754267445896}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267446235, "stop": 1754267446235}, {"name": "Processing module 'Format Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267448516, "stop": 1754267448516}, {"name": "Click on Format Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267448516, "stop": 1754267448516}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267449379, "stop": 1754267449379}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267449667, "stop": 1754267449667}, {"name": "Processing module 'Name Checker'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267451916, "stop": 1754267451916}, {"name": "Click on Name Checker", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267451916, "stop": 1754267451916}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267452855, "stop": 1754267452855}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267453191, "stop": 1754267453191}, {"name": "Processing module 'ListExplorer'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267456461, "stop": 1754267456461}, {"name": "Click on ListExplorer", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267456461, "stop": 1754267456461}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267457436, "stop": 1754267457436}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267457823, "stop": 1754267457823}, {"name": "Processing module 'ListSetManager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267460350, "stop": 1754267460350}, {"name": "Click on ListSetManager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267460350, "stop": 1754267460350}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267461596, "stop": 1754267461596}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267462724, "stop": 1754267462724}, {"name": "Processing module 'List Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267465242, "stop": 1754267465242}, {"name": "Click on List Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267465242, "stop": 1754267465242}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267466380, "stop": 1754267466380}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267467060, "stop": 1754267467060}, {"name": "Processing module 'File Scanner'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267469472, "stop": 1754267469472}, {"name": "Click on File Scanner", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267469472, "stop": 1754267469472}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267470444, "stop": 1754267470444}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267470774, "stop": 1754267470774}, {"name": "Processing module 'Advanced Settings'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267473229, "stop": 1754267473229}, {"name": "Click on Advanced Settings", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267473229, "stop": 1754267473229}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267474347, "stop": 1754267474347}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267474706, "stop": 1754267474706}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267476026, "stop": 1754267476026}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267476413, "stop": 1754267476413}, {"name": "Check if profile exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267478652, "stop": 1754267478652}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267478652, "stop": 1754267478652}, {"name": "Set name = Test-Profile-916328070 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267484914, "stop": 1754267484914}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267485839, "stop": 1754267485839}, {"name": "Profile exist = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267486744, "stop": 1754267486744}, {"name": "Create second group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267486744, "stop": 1754267486744}, {"name": "Group test Data = Group{id=0, name='selenium-random-group-685731621', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-916328070', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone377352367', displayName='Zone (377352367)', description='Zone created with random number '377352367''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-859712249', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone436740045', displayName='Zone (436740045)', description='Zone created with random number '436740045''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267486744, "stop": 1754267486744}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267538072, "stop": 1754267538072}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='selenium-user-859712249'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267538072, "stop": 1754267538072}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267538078, "stop": 1754267538078}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267538078, "stop": 1754267538078}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267538079, "stop": 1754267538079}, {"name": "Login with User Name = selenium-user-859712249 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267538252, "stop": 1754267538252}, {"name": "Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-905037798', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@6dfa5c25, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-905037798', officialDate='null', entry=[ListEntry{type='null', name='EntryName-905037798', firstName='EntryFirstName-905037798', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-905037798', firstName='EntryFirstName-905037798', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267544318, "stop": 1754267544318}, {"name": "Connect to Database and Check if User Profile = Test-Profile-916328070 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267544323, "stop": 1754267544323}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267544323, "stop": 1754267544323}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267544376, "stop": 1754267544376}, {"name": "SELECT ID FROM tProfiles Where NAME ='Test-Profile-916328070' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267544376, "stop": 1754267544376}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267544379, "stop": 1754267544379}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267544379, "stop": 1754267544379}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267544380, "stop": 1754267544380}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267544395, "stop": 1754267544395}, {"name": "Delete From tListSetProfile where profile_id in (190)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267544395, "stop": 1754267544395}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267544405, "stop": 1754267544405}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267544406, "stop": 1754267544406}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267544406, "stop": 1754267544406}, {"name": "Number of effected rows = 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267544406, "stop": 1754267544406}, {"name": "Search for list by listName = ListName-905037798 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267570695, "stop": 1754267570695}, {"name": "Set zone : Zone (377352367)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267583383, "stop": 1754267583383}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267586896, "stop": 1754267586896}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267588575, "stop": 1754267588575}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267588965, "stop": 1754267588965}, {"name": "Set template name = templateName-905037798", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267589258, "stop": 1754267589258}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267592486, "stop": 1754267592486}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267593252, "stop": 1754267593252}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267594552, "stop": 1754267594552}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267596935, "stop": 1754267596935}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267617509, "stop": 1754267617509}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267617509, "stop": 1754267617509}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267676815, "stop": 1754267676815}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267676815, "stop": 1754267676815}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267676815, "stop": 1754267676815}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267676849, "stop": 1754267676849}, {"name": "SELECT ID FROM tProfiles Where NAME ='Test-Profile-916328070'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267676849, "stop": 1754267676849}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267676851, "stop": 1754267676851}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267676851, "stop": 1754267676851}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267676852, "stop": 1754267676852}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267699399, "stop": 1754267699399}, {"name": "RJE File Content= {1:F01PTSABEMMAXXX0000000001}{2:I564ACCLFRP1XXXXN}{3:{108:ENT144037000001}}{4:\r\n:16R:GENL\r\n:20C::CORP//2015033100010560\r\n:20C::SEME//0105601900000002\r\n:23G:EntryName-905037798, EntryFirstName-905037798\r\n:22F::CAEV//DVCA\r\n:22F::CAMV//MAND\r\n:98A::PREP//20150331\r\n:25D::PROC//COMP\r\n:16R:LINK\r\n:13A::LINK//564\r\n:20C::PREV//0105601900000001\r\n:16S:LINK\r\n:16S:GENL\r\n:16R:USECU\r\n:35B:ISIN DE0005190003\r\nBMW Bayerische Motoren Werke AG\r\n:16R:ACCTINFO\r\n:97A::SAFE//000000190003\r\n:93B::ELIG//UNIT/360,\r\n:93B::PEND//UNIT/360,\r\n:93B::PENR//UNIT/0,\r\n:93B::SETT//UNIT/360,\r\n:16S:ACCTINFO\r\n:16S:USECU\r\n:16R:CADETL\r\n:98A::XDTE//20150514\r\n:98A::RDTE//20150515\r\n:22F::DIVI//FINL\r\n:16S:CADETL\r\n:16R:CAOPTN\r\n:13A::CAON//001\r\n:22F::CAOP//CASH\r\n:17B::DFLT//Y\r\n:16R:CASHMOVE\r\n:22H::CRDB//CRED\r\n:98A::PAYD//20150514\r\n:92A::TAXR//26,375\r\n:92F::GRSS//EUR2,9\r\n:16S:CASHMOVE\r\n:16S:CAOPTN\r\n-}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267707324, "stop": 1754267707324}, {"name": "Start coping data from sample file to another file to be used.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267707324, "stop": 1754267707324}, {"name": "Successfully wrote to the file.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267707329, "stop": 1754267707329}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267709694, "stop": 1754267709694}, {"name": "Validation message = File sent to the server for processing with id [3200]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267725173, "stop": 1754267725173}, {"name": "Alert Message = File sent to the server for processing with id [3200]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267725173, "stop": 1754267725173}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267726208, "stop": 1754267726208}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267735732, "stop": 1754267735732}, {"name": "Detection ID = 8432", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267736671, "stop": 1754267736671}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267742477, "stop": 1754267742477}, {"name": "Check if user can block detection. ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267743674, "stop": 1754267743674}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267749608, "stop": 1754267749608}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "Operator{id=0, loginName='null', firstName='null', lastName='null', password='null', zone=null}"}], "start": 1754267002709, "stop": 1754267760680}