{"uuid": "6f39d861-ef6d-463f-8922-ffaad9d18637", "historyId": "********************************", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC001.scanManager_TC001", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC001"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC001"}, {"name": "testMethod", "value": "scanManager_TC001"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC001"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is able to scan a custom format file with creating alerts option checked and take decision. ", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to scan a custom format file with creating alerts option checked and take decision. ", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172562798, "stop": 1754172562798}, {"name": "Validation message = File sent to the server for processing with id [2972]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172580663, "stop": 1754172580663}, {"name": "Alert Message = File sent to the server for processing with id [2972]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172580663, "stop": 1754172580663}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172582202, "stop": 1754172582202}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172594184, "stop": 1754172594185}, {"name": "Detection ID = 7559", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172595740, "stop": 1754172595740}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172601871, "stop": 1754172601871}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172608096, "stop": 1754172608096}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "FileScan{filePath='/Generic.txt', format='Custom Format File', encoding='null', enList=null, result='null', rank='null', detectVessels='true', detectCountries='true', createAlertsAutomatically='true'}"}], "start": 1754172516106, "stop": 1754172615153}