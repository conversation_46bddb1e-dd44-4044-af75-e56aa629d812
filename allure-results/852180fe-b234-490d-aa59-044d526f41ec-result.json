{"uuid": "852180fe-b234-490d-aa59-044d526f41ec", "historyId": "276bb51d9fe0a45781c719f8d9643b0a", "fullName": "eastnets.screening.regression.reports.Report_TC008.report_TC008", "labels": [{"name": "package", "value": "eastnets.screening.regression.reports.Report_TC008"}, {"name": "testClass", "value": "eastnets.screening.regression.reports.Report_TC008"}, {"name": "testMethod", "value": "report_TC008"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Report Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.reports.Report_TC008"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-3(38)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "tag", "value": "Reports"}], "links": [], "name": "Verify Compare sessions info Report", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify Compare sessions info Report", "steps": [{"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-260517053', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@c733181, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-260517053', officialDate='null', entry=[ListEntry{type='null', name='EntryName-260517053', firstName='EntryFirstName-260517053', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-260517053', firstName='EntryFirstName-260517053', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754147380876, "stop": 1754147380876}, {"name": "Connect to Database and Check if User Profile = full-right-profile_08 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754147380877, "stop": 1754147380877}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754147380877, "stop": 1754147380877}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754147380937, "stop": 1754147380937}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_08' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754147380937, "stop": 1754147380937}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754147380944, "stop": 1754147380944}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754147380944, "stop": 1754147380944}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754147380947, "stop": 1754147380947}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754147380976, "stop": 1754147380976}, {"name": "Delete From tListSetProfile where profile_id in (11)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754147380976, "stop": 1754147380976}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754147380983, "stop": 1754147380983}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754147380983, "stop": 1754147380983}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754147380984, "stop": 1754147380984}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754147380984, "stop": 1754147380984}, {"name": "Search for list by listName = ListName-260517053 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754147662328, "stop": 1754147662328}, {"name": "Set zone : Common Zone 08", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754147676951, "stop": 1754147676951}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754147679145, "stop": 1754147679145}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754147679952, "stop": 1754147679952}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754147680615, "stop": 1754147680615}, {"name": "Set template name = templateName-260517053", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754147680893, "stop": 1754147680893}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754147683301, "stop": 1754147683301}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754147684221, "stop": 1754147684221}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754147685465, "stop": 1754147685465}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754147686386, "stop": 1754147686386}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754147804970, "stop": 1754147804970}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754147804970, "stop": 1754147804970}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754148061086, "stop": 1754148061086}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754148061087, "stop": 1754148061087}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754148061087, "stop": 1754148061087}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754148061114, "stop": 1754148061114}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_08'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754148061114, "stop": 1754148061114}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754148061117, "stop": 1754148061117}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754148061117, "stop": 1754148061117}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754148061118, "stop": 1754148061118}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754148199522, "stop": 1754148199522}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754148472331, "stop": 1754148472331}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754148559533, "stop": 1754148559533}, {"name": "Validation message = File sent to the server for processing with id [2902]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754148619180, "stop": 1754148619180}, {"name": "Alert Message = File sent to the server for processing with id [2902]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754148619180, "stop": 1754148619180}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754148670691, "stop": 1754148670691}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754148799084, "stop": 1754148799084}, {"name": "Detection ID = 7282", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754148800363, "stop": 1754148800363}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754148800773, "stop": 1754148800773}, {"name": "Validation message = File sent to the server for processing with id [2903]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754148839554, "stop": 1754148839554}, {"name": "Alert Message = File sent to the server for processing with id [2903]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754148839554, "stop": 1754148839554}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754148865935, "stop": 1754148865935}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149006961, "stop": 1754149006961}, {"name": "Detection ID = 7283", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149008375, "stop": 1754149008375}, {"name": "Report status is: Done", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149164301, "stop": 1754149164301}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754147380873, "stop": 1754149198154}