{"uuid": "3178b22b-cac5-44e8-91f4-de829e13f156", "historyId": "79a789d30305889a79da2767efd68fec", "fullName": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC006.listManager_TC006", "labels": [{"name": "package", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC006"}, {"name": "testClass", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC006"}, {"name": "testMethod", "value": "listManager_TC006"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "List Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC006"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-1(36)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "List Manager"}], "links": [], "name": "listManager_TC006", "status": "skipped", "statusDetails": {"known": false, "muted": false, "flaky": false, "message": "Cannot invoke \"org.openqa.selenium.remote.RemoteWebDriver.quit()\" because the return value of \"core.BaseTest.getDriver()\" is null", "trace": "java.lang.NullPointerException: Cannot invoke \"org.openqa.selenium.remote.RemoteWebDriver.quit()\" because the return value of \"core.BaseTest.getDriver()\" is null\r\n\tat core.BaseTest.closeDriver(BaseTest.java:218)\r\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:578)\r\n\tat org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)\r\n\tat org.testng.internal.invokers.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:69)\r\n\tat org.testng.internal.invokers.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:361)\r\n\tat org.testng.internal.invokers.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:296)\r\n\tat org.testng.internal.invokers.TestMethodWorker.invokeAfterClassMethods(TestMethodWorker.java:222)\r\n\tat org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:131)\r\n\tat java.base/java.util.ArrayList.forEach(ArrayList.java:1511)\r\n\tat org.testng.TestRunner.privateRun(TestRunner.java:829)\r\n\tat org.testng.TestRunner.run(TestRunner.java:602)\r\n\tat org.testng.SuiteRunner.runTest(SuiteRunner.java:437)\r\n\tat org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:475)\r\n\tat org.testng.internal.thread.ThreadUtil.lambda$execute$0(ThreadUtil.java:58)\r\n\tat java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)\r\n\tat java.base/java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:132)\r\n\tat java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:113)\r\n\tat java.base/java.util.concurrent.AbstractExecutorService.invokeAll(AbstractExecutorService.java:274)\r\n\tat org.testng.internal.thread.ThreadUtil.execute(ThreadUtil.java:64)\r\n\tat org.testng.SuiteRunner.runInParallelTestMode(SuiteRunner.java:458)\r\n\tat org.testng.SuiteRunner.privateRun(SuiteRunner.java:389)\r\n\tat org.testng.SuiteRunner.run(SuiteRunner.java:330)\r\n\tat org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)\r\n\tat org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)\r\n\tat org.testng.TestNG.runSuitesSequentially(TestNG.java:1256)\r\n\tat org.testng.TestNG.runSuitesLocally(TestNG.java:1176)\r\n\tat org.testng.TestNG.runSuites(TestNG.java:1099)\r\n\tat org.testng.TestNG.run(TestNG.java:1067)\r\n\tat com.intellij.rt.testng.IDEARemoteTestNG.run(IDEARemoteTestNG.java:65)\r\n\tat com.intellij.rt.testng.RemoteTestNGStarter.main(RemoteTestNGStarter.java:105)\r\n"}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "EnList{testCaseTitle='Verify that user is able to search for an entity by List Set', listSet=ListSet{zone='null', name='ListSetName-%s', rank='null', owner='null', swiftTemplate=null, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='null', officialDate='null', entry=null, expectedMessage='null'}"}], "start": 1754233583506, "stop": 1754233583506}