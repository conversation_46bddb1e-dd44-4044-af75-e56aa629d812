{"uuid": "3f505c3b-efdb-4072-bbb8-f4ca3e6484fc", "historyId": "cb7f934a0c930919d77eab3430b69264", "fullName": "eastnets.screening.regression.detectionmanager.DetectionManager_TC015.detectionManager_TC015", "labels": [{"name": "package", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC015"}, {"name": "testClass", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC015"}, {"name": "testMethod", "value": "detectionManager_TC015"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Detection Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC015"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-3(38)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Detection Manager"}], "links": [], "name": "detectionManager_TC015", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754242197906, "stop": 1754242197906}