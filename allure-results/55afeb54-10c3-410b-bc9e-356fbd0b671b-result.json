{"uuid": "55afeb54-10c3-410b-bc9e-356fbd0b671b", "historyId": "eb0b1f3a3f4a74896cf6cc77ab5390b1", "fullName": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC010.listManager_TC010", "labels": [{"name": "package", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC010"}, {"name": "testClass", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC010"}, {"name": "testMethod", "value": "listManager_TC010"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "List Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC010"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-1(36)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "List Manager"}], "links": [], "name": "Verify that user is able to Import a non shared Good Guys", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to Import a non shared Good Guys", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173101902, "stop": 1754173101902}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-02'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173101902, "stop": 1754173101902}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173101906, "stop": 1754173101906}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173101906, "stop": 1754173101906}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173101910, "stop": 1754173101910}, {"name": "Login with User Name = operator-02 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173102191, "stop": 1754173102191}, {"name": "Create swift-code, black list and list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109230, "stop": 1754173109230}, {"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-499534452', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@213c6337, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-499534452', officialDate='null', entry=[ListEntry{type='null', name='EntryName-499534452', firstName='EntryFirstName-499534452', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-499534452', firstName='EntryFirstName-499534452', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109237, "stop": 1754173109237}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109237, "stop": 1754173109237}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109260, "stop": 1754173109260}, {"name": "select id from tZones tz where tz.DISPLAY_NAME  = 'Common Zone 02'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109260, "stop": 1754173109260}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109265, "stop": 1754173109266}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109266, "stop": 1754173109266}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109266, "stop": 1754173109266}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109292, "stop": 1754173109292}, {"name": "DELETE  FROM tSetContent   WHERE id IN ( SELECT tsc.id FROM tSetContent tsc  LEFT JOIN tBlackListVersions tblv on tblv.id =tsc.black_list_id  LEFT JOIN tListSets tls on tls.id = tsc.list_set_id  LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id  WHERE tbl.zone_id ='4'  AND tbl.display_name ='UN Democratic People Republic of Korea')", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109292, "stop": 1754173109292}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109317, "stop": 1754173109317}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109317, "stop": 1754173109317}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109317, "stop": 1754173109317}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109317, "stop": 1754173109317}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109340, "stop": 1754173109340}, {"name": "DELETE  FROM tGoodGuys  WHERE id IN ( SELECT tgg.id  FROM tGoodGuys tgg LEFT JOIN tBlackListVersions tblv on tblv.id =tgg.black_list_id LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id WHERE tbl.display_name ='UN Democratic People Republic of Korea')", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109340, "stop": 1754173109340}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109371, "stop": 1754173109371}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109371, "stop": 1754173109371}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109372, "stop": 1754173109372}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109372, "stop": 1754173109372}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109399, "stop": 1754173109399}, {"name": "DELETE FROM tBlackLists WHERE display_name ='UN Democratic People Republic of Korea'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109399, "stop": 1754173109399}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109722, "stop": 1754173109722}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109722, "stop": 1754173109722}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109723, "stop": 1754173109723}, {"name": "Connect to Database and Check if User Profile = full-right-profile_002 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109723, "stop": 1754173109723}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109723, "stop": 1754173109723}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109812, "stop": 1754173109812}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_002' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109812, "stop": 1754173109812}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109815, "stop": 1754173109815}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109815, "stop": 1754173109815}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109815, "stop": 1754173109815}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109846, "stop": 1754173109846}, {"name": "Delete From tListSetProfile where profile_id in (5)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109846, "stop": 1754173109846}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109859, "stop": 1754173109859}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109859, "stop": 1754173109859}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109860, "stop": 1754173109860}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109860, "stop": 1754173109860}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109860, "stop": 1754173109860}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109889, "stop": 1754173109889}, {"name": "select id from tZones tz where tz.DISPLAY_NAME  = 'Common Zone 02'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109889, "stop": 1754173109889}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109898, "stop": 1754173109898}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109898, "stop": 1754173109898}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109898, "stop": 1754173109898}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109926, "stop": 1754173109926}, {"name": "DELETE  FROM tSetContent   WHERE id IN ( SELECT tsc.id FROM tSetContent tsc  LEFT JOIN tBlackListVersions tblv on tblv.id =tsc.black_list_id  LEFT JOIN tListSets tls on tls.id = tsc.list_set_id  LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id  WHERE tbl.zone_id ='4'  AND tbl.display_name ='UN Democratic People Republic of Korea')", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109926, "stop": 1754173109926}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109948, "stop": 1754173109948}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109948, "stop": 1754173109948}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109948, "stop": 1754173109948}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109948, "stop": 1754173109948}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109973, "stop": 1754173109973}, {"name": "DELETE  FROM tGoodGuys  WHERE id IN ( SELECT tgg.id  FROM tGoodGuys tgg LEFT JOIN tBlackListVersions tblv on tblv.id =tgg.black_list_id LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id WHERE tbl.display_name ='UN Democratic People Republic of Korea')", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109973, "stop": 1754173109973}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109992, "stop": 1754173109992}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109992, "stop": 1754173109992}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109994, "stop": 1754173109994}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173109994, "stop": 1754173109994}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173110017, "stop": 1754173110017}, {"name": "DELETE FROM tBlackLists WHERE display_name ='UN Democratic People Republic of Korea'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173110017, "stop": 1754173110017}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173110028, "stop": 1754173110028}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173110028, "stop": 1754173110028}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173110028, "stop": 1754173110028}, {"name": "Set zone : Common Zone 02", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173130937, "stop": 1754173130937}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173133621, "stop": 1754173133622}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173134607, "stop": 1754173134607}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173135312, "stop": 1754173135312}, {"name": "Set template name = templateName-499534452", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173135690, "stop": 1754173135690}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173138592, "stop": 1754173138592}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173140080, "stop": 1754173140080}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173141972, "stop": 1754173141972}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173142730, "stop": 1754173142730}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173187619, "stop": 1754173187619}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173187619, "stop": 1754173187619}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173266243, "stop": 1754173266243}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173266243, "stop": 1754173266243}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173266243, "stop": 1754173266243}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173266260, "stop": 1754173266260}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_002'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173266260, "stop": 1754173266260}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173266264, "stop": 1754173266264}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173266264, "stop": 1754173266264}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173266265, "stop": 1754173266265}, {"name": "Validation message = Good guy successfully created!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173321283, "stop": 1754173321283}, {"name": "Validation message = Good guy successfully created! .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173321283, "stop": 1754173321283}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173341619, "stop": 1754173341619}, {"name": "Validation message = Operation completed successfully. .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173341619, "stop": 1754173341619}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754173101237, "stop": 1754173352863}