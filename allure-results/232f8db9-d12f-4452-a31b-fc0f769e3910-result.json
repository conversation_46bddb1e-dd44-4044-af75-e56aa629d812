{"uuid": "232f8db9-d12f-4452-a31b-fc0f769e3910", "historyId": "2538063769aeb59b4f0f7f07159a6022", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC003.ScanManager_TC003", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC003"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC003"}, {"name": "testMethod", "value": "ScanManager_TC003"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC003"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "ScanManager_TC003", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754231929773, "stop": 1754231929773}