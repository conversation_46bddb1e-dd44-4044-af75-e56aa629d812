{"uuid": "32d6bca3-d167-4b4d-9ccb-27f7c3e54903", "historyId": "42eb3c2d0f8cf06d96aec17e5620434", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC004.ScanManager_TC004", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC004"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC004"}, {"name": "testMethod", "value": "ScanManager_TC004"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC004"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Validate that user is able to Print a detection created from 'File Scan' from Result Tab.", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Validate that user is able to Print a detection created from 'File Scan' from Result Tab.", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172626878, "stop": 1754172626878}, {"name": "Validation message = File sent to the server for processing with id [2974]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172644165, "stop": 1754172644165}, {"name": "Alert Message = File sent to the server for processing with id [2974]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172644165, "stop": 1754172644165}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172646023, "stop": 1754172646023}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172656583, "stop": 1754172656583}, {"name": "Detection ID = 7560", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172658577, "stop": 1754172658578}, {"name": "response.getStatus()200", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172691487, "stop": 1754172691487}, {"name": "UTF-8", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172691487, "stop": 1754172691487}, {"name": "1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172691488, "stop": 1754172691488}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754172626850, "stop": 1754172691488}