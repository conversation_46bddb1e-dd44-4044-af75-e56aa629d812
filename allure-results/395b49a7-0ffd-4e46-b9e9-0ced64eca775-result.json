{"uuid": "395b49a7-0ffd-4e46-b9e9-0ced64eca775", "historyId": "********************************", "fullName": "eastnets.admin.AdminTest.checkOperatorPermissions", "labels": [{"name": "package", "value": "eastnets.admin.AdminTest"}, {"name": "testClass", "value": "eastnets.admin.AdminTest"}, {"name": "testMethod", "value": "checkOperatorPermissions"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Admin Test Cases"}, {"name": "subSuite", "value": "eastnets.admin.AdminTest"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-1(36)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}], "links": [], "name": "Verify that user is not able to Edit a User when 'Allow to modify all operators' permission is not granted.", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is not able to Edit a User when 'Allow to modify all operators' permission is not granted.", "steps": [{"name": "Connect to database to remove Link between profile = Test-Profile-23911606 and Permission Allow to modify all operators and enable some admin functions", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181498907, "stop": 1754181498907}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181498928, "stop": 1754181498928}, {"name": "SELECT P.NAME as PR<PERSON><PERSON><PERSON>_NAME , PR.PROFILE_ID as PRO<PERSON>LE_ID , PR.GRANT_ID as GRANT_ID , PR.GRANT_RIGHT_ID as GRANT_RIGHT_ID , PR.GRANT_TYPE_ID as GRANT_TYPE_ID   FROM tModuleFunctions M   LEFT JOIN tProfileRights PR On PR.GRANT_ID  = M.ID     LEFT Join tProfiles P ON P.id =PR.PROFILE_ID   WHERE DESCRIPTION ='Allow to modify all operators and enable some admin functions' and P.NAME = 'Test-Profile-23911606'  and PR.GRANT_TYPE_ID =3", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181498928, "stop": 1754181498928}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181498941, "stop": 1754181498941}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181498942, "stop": 1754181498942}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181498942, "stop": 1754181498942}, {"name": "Delete function permission 'Allow to modify all operators and enable some admin functions' from DataBase", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181498942, "stop": 1754181498942}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181498961, "stop": 1754181498961}, {"name": "DELETE FROM tProfileRights  WHERE GRANT_ID  ='1' and PROFILE_ID ='161' and GRANT_TYPE_ID =3", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181498962, "stop": 1754181498962}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181498978, "stop": 1754181498978}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181498978, "stop": 1754181498978}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181498978, "stop": 1754181498978}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181499593, "stop": 1754181499593}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='selenium-user-834901972'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181499593, "stop": 1754181499593}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181499606, "stop": 1754181499606}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181499606, "stop": 1754181499606}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181499607, "stop": 1754181499607}, {"name": "Login with User Name = selenium-user-834901972 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181499798, "stop": 1754181499798}, {"name": "Check if user can edit user.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181507488, "stop": 1754181507488}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181513900, "stop": 1754181513900}, {"name": " INSERT INTO tProfileRights (PROFILE_ID, GRANT_ID, GRANT_RIGHT_ID, GRANT_TYPE_ID) VALUES (161, 1, 3, 3);", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181513901, "stop": 1754181513901}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181513907, "stop": 1754181513907}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181513907, "stop": 1754181513907}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181513907, "stop": 1754181513907}, {"name": "Operator logout.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181513907, "stop": 1754181513907}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "Operator{id=0, loginName='null', firstName='null', lastName='null', password='null', zone=null}"}], "start": 1754181498906, "stop": 1754181513908}