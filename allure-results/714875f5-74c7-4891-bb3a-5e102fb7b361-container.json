{"uuid": "714875f5-74c7-4891-bb3a-5e102fb7b361", "name": "Safe Watch Filtering", "children": ["6f6891fc-2e1e-4ca2-9a97-62ec6640a6c0", "3a9ca420-e3a8-4a5b-8ed4-05f0f17685d6", "a50cbb55-e3d2-41e1-854e-bfede515fab0", "4ae1af1e-3a1b-49fd-84c3-2c6071f703f4", "8c1d3531-bf60-4325-b801-52e6665d1617", "bba89440-3416-412d-9fae-76efef50a314", "d7cab24f-ea16-4cff-94bd-6b35c1ec732f", "ff4f0a14-ab55-41f2-a438-7b929bd0cd6c", "646c6b81-5792-475b-88a2-e15581ae3aae", "2ec30a4e-bb2f-4103-ac2f-35c50aa4ee92", "8eb0e1e3-2b4e-4bac-925b-be93401a286c", "ba0d378a-cda0-4584-8626-f08aa391db94", "0fd5cae7-b49f-4ae9-b8ff-d4422fb51b75", "c1aee215-08e4-469b-aaad-36429fcdab28", "7b8e0ce8-6ac0-4783-888b-c08f1931162c", "33ab2eeb-4fb6-42ac-bf5e-c3bf43680e9a", "2e9afa4e-676d-43e7-bc81-87b2df368722", "0e0fe5bd-0214-48f3-9222-1b5b01ae30fb", "aab720fa-024b-446c-9f4d-a1cbe1b0bab1", "efe40e2d-fe80-4b97-8a2c-fe6cf2dcc950", "88831dd1-2cf6-44a9-8995-33d312e16808", "930d510c-0fa8-4106-a539-2a63c20d117d", "848e3f83-c883-4625-a198-2216356daa74", "f9134193-847b-4f67-869b-0c6d8bb5f03a", "c2777e4d-3d1b-4e81-9f12-6085ee2adce8", "f8957337-b85d-49b0-af2a-23ff620d687f", "faf233b5-f340-479b-928e-623c5ef1fe46", "ea7003db-ad0f-4db3-8cc7-ea6e05ad2fd4", "51c7d995-33b8-4769-99c6-b979a7c1b314", "9bd1ea07-239a-4660-b0ae-7e1eb3851e54", "d41f9b92-e28a-4509-a5c7-3bf0167eef78", "93511470-0d5d-441e-bee2-46d5edb9dd4b", "3f4f1acb-04c4-4cac-b96e-6505a261f406", "16231f4d-80b0-4830-a738-347f5309092e", "ab156120-ddf7-4b7d-aeb7-3aef108e15bb", "8e3aa16a-8826-413d-98ef-7786fc19d8c8", "a56a7b78-044a-49c6-9e47-c8d6da33d424", "4e2867b5-314e-4a0d-aed5-af476a3a41c8", "353acb41-354c-447f-8f06-04f84b33eb77", "c0d019f4-44ec-4219-b948-68f5ea5ff482", "97962736-1e48-4f0e-805b-57fff3104bc5", "99113bdc-a242-4b3a-a6fd-9bbf6a19f66d"], "befores": [{"name": "Setting up allure report", "status": "passed", "stage": "finished", "description": "Setting up allure report", "steps": [{"name": "Setting up allure report environment data: Edge", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754233052607, "stop": 1754233052608}], "attachments": [], "parameters": [], "start": 1754233052600, "stop": 1754233052948}], "afters": [{"name": "rerunFailedTestCases", "status": "passed", "stage": "finished", "description": "", "steps": [{"name": "========================================", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754233060648, "stop": 1754233060648}, {"name": "*** ALL TESTS PASSED! ***", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754233060648, "stop": 1754233060648}, {"name": "No failed tests to rerun.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754233060648, "stop": 1754233060648}, {"name": "========================================", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754233060648, "stop": 1754233060648}], "attachments": [], "parameters": [], "start": 1754233060647, "stop": 1754233060648}], "start": 1754233052532, "stop": 1754233060648}