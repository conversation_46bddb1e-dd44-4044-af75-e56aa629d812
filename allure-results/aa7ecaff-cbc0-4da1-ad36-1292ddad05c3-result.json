{"uuid": "aa7ecaff-cbc0-4da1-ad36-1292ddad05c3", "historyId": "fe866d406b480c600b46beede6bac709", "fullName": "eastnets.screening.regression.detectionmanager.DetectionManager_TC008.detectionManager_TC008", "labels": [{"name": "package", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC008"}, {"name": "testClass", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC008"}, {"name": "testMethod", "value": "detectionManager_TC008"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Detection Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC008"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-3(38)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Detection Manager"}], "links": [], "name": "Verify that user is able to perform a Pending action on a detection created from 'Name Checker' from the 'Detections Section'\nVerify that user is able to Assign a detection created from 'Name Checker' to User/Group from the 'Detections Section' ", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to perform a Pending action on a detection created from 'Name Checker' from the 'Detections Section'\nVerify that user is able to Assign a detection created from 'Name Checker' to User/Group from the 'Detections Section' ", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266666750, "stop": 1754266666750}, {"name": "update tConfig set variable_value = 'Yes' where variable_name = 'Change detection status to DontKnow'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266666750, "stop": 1754266666750}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266666755, "stop": 1754266666755}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266666755, "stop": 1754266666755}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266666755, "stop": 1754266666755}, {"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-273369727', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@6761aaca, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-273369727', officialDate='null', entry=[ListEntry{type='null', name='EntryName-273369727', firstName='EntryFirstName-273369727', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-273369727', firstName='EntryFirstName-273369727', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266666758, "stop": 1754266666758}, {"name": "Connect to Database and Check if User Profile = full-right-profile_05 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266666758, "stop": 1754266666758}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266666758, "stop": 1754266666758}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266666814, "stop": 1754266666814}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_05' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266666814, "stop": 1754266666814}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266666817, "stop": 1754266666817}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266666817, "stop": 1754266666817}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266666818, "stop": 1754266666818}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266666847, "stop": 1754266666847}, {"name": "Delete From tListSetProfile where profile_id in (8)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266666847, "stop": 1754266666847}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266666852, "stop": 1754266666852}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266666852, "stop": 1754266666852}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266666853, "stop": 1754266666853}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266666853, "stop": 1754266666853}, {"name": "Search for list by listName = ListName-273369727 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266691474, "stop": 1754266691474}, {"name": "Set zone : Common Zone 05", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266723429, "stop": 1754266723429}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266725904, "stop": 1754266725904}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266726628, "stop": 1754266726628}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266727079, "stop": 1754266727079}, {"name": "Set template name = templateName-273369727", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266727352, "stop": 1754266727352}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266730501, "stop": 1754266730501}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266731412, "stop": 1754266731412}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266732727, "stop": 1754266732727}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266733493, "stop": 1754266733493}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266802090, "stop": 1754266802090}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266802090, "stop": 1754266802090}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266884163, "stop": 1754266884163}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266884164, "stop": 1754266884164}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266884164, "stop": 1754266884164}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266884194, "stop": 1754266884194}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_05'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266884194, "stop": 1754266884194}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266884196, "stop": 1754266884196}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266884196, "stop": 1754266884196}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266884196, "stop": 1754266884196}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266906025, "stop": 1754266906025}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266916389, "stop": 1754266916389}, {"name": "Status for the scanned name = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266937982, "stop": 1754266937982}, {"name": "Detection ID for the scanned name = 8376", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266938199, "stop": 1754266938199}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266961337, "stop": 1754266961337}, {"name": "Validation message = 1 detection(s) successfully modified!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266973164, "stop": 1754266973164}, {"name": "Detection Status = REPINV", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754266978395, "stop": 1754266978395}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754266666723, "stop": 1754266985339}