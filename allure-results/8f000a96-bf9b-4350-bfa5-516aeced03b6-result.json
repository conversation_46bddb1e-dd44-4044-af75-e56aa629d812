{"uuid": "8f000a96-bf9b-4350-bfa5-516aeced03b6", "historyId": "********************************", "fullName": "eastnets.admin.AdminTest.checkOperatorPermissions", "labels": [{"name": "package", "value": "eastnets.admin.AdminTest"}, {"name": "testClass", "value": "eastnets.admin.AdminTest"}, {"name": "testMethod", "value": "checkOperatorPermissions"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Admin Test Cases"}, {"name": "subSuite", "value": "eastnets.admin.AdminTest"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}], "links": [], "name": "Verify that user is not able to Edit a User when 'Allow to modify all operators' permission is not granted.", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is not able to Edit a User when 'Allow to modify all operators' permission is not granted.", "steps": [{"name": "Connect to database to remove Link between profile = Test-Profile-413870425 and Permission Allow to modify all operators and enable some admin functions", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265394077, "stop": 1754265394077}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265394102, "stop": 1754265394102}, {"name": "SELECT P.NAME as PR<PERSON><PERSON><PERSON>_NAME , PR.PROFILE_ID as PRO<PERSON>LE_ID , PR.GRANT_ID as GRANT_ID , PR.GRANT_RIGHT_ID as GRANT_RIGHT_ID , PR.GRANT_TYPE_ID as GRANT_TYPE_ID   FROM tModuleFunctions M   LEFT JOIN tProfileRights PR On PR.GRANT_ID  = M.ID     LEFT Join tProfiles P ON P.id =PR.PROFILE_ID   WHERE DESCRIPTION ='Allow to modify all operators and enable some admin functions' and P.NAME = 'Test-Profile-413870425'  and PR.GRANT_TYPE_ID =3", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265394102, "stop": 1754265394102}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265394117, "stop": 1754265394117}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265394117, "stop": 1754265394117}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265394118, "stop": 1754265394118}, {"name": "Delete function permission 'Allow to modify all operators and enable some admin functions' from DataBase", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265394118, "stop": 1754265394118}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265394147, "stop": 1754265394147}, {"name": "DELETE FROM tProfileRights  WHERE GRANT_ID  ='1' and PROFILE_ID ='184' and GRANT_TYPE_ID =3", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265394148, "stop": 1754265394148}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265394167, "stop": 1754265394167}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265394167, "stop": 1754265394167}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265394168, "stop": 1754265394168}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265395258, "stop": 1754265395258}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='selenium-user-538712605'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265395259, "stop": 1754265395259}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265395265, "stop": 1754265395265}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265395265, "stop": 1754265395265}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265395265, "stop": 1754265395265}, {"name": "Login with User Name = selenium-user-538712605 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265395473, "stop": 1754265395473}, {"name": "Check if user can edit user.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265404383, "stop": 1754265404383}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265411163, "stop": 1754265411163}, {"name": " INSERT INTO tProfileRights (PROFILE_ID, GRANT_ID, GRANT_RIGHT_ID, GRANT_TYPE_ID) VALUES (184, 1, 3, 3);", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265411163, "stop": 1754265411163}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265411168, "stop": 1754265411169}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265411169, "stop": 1754265411169}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265411169, "stop": 1754265411169}, {"name": "Operator logout.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265411169, "stop": 1754265411169}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "Operator{id=0, loginName='null', firstName='null', lastName='null', password='null', zone=null}"}], "start": 1754265394076, "stop": 1754265411169}