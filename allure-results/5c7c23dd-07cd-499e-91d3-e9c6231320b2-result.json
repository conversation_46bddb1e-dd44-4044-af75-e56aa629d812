{"uuid": "5c7c23dd-07cd-499e-91d3-e9c6231320b2", "historyId": "23bc6ffd88953a4bd38eb8690dbe5d0", "fullName": "eastnets.screening.regression.iso20022configurations.ISO20022_TC005.iso20022_TC0012", "labels": [{"name": "package", "value": "eastnets.screening.regression.iso20022configurations.ISO20022_TC005"}, {"name": "testClass", "value": "eastnets.screening.regression.iso20022configurations.ISO20022_TC005"}, {"name": "testMethod", "value": "iso20022_TC0012"}, {"name": "parentSuite", "value": "Failed Tests Rerun Suite"}, {"name": "suite", "value": "<PERSON><PERSON> Failed Tests"}, {"name": "subSuite", "value": "eastnets.screening.regression.iso20022configurations.ISO20022_TC005"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "tag", "value": "ISO20022"}, {"name": "tag", "value": "Regression"}, {"name": "owner", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "links": [], "name": "Verify that the user can import the exported custom ISO20022 XSD", "status": "failed", "statusDetails": {"known": false, "muted": false, "flaky": false, "message": "Test Failed due to an exception occurred in POM's method while adding ISO message expected [New message [pacs.009.001] with version [pacs.009.001.08] added successfully.] but found [This message already exist!\"]", "trace": "java.lang.AssertionError: Test Failed due to an exception occurred in POM's method while adding ISO message expected [New message [pacs.009.001] with version [pacs.009.001.08] added successfully.] but found [This message already exist!\"]\r\n\tat org.testng.Assert.fail(Assert.java:110)\r\n\tat org.testng.Assert.failNotEquals(Assert.java:1413)\r\n\tat org.testng.Assert.assertEqualsImpl(Assert.java:149)\r\n\tat org.testng.Assert.assertEquals(Assert.java:131)\r\n\tat org.testng.Assert.assertEquals(Assert.java:655)\r\n\tat core.ISOTestMethods.addISOMessage(ISOTestMethods.java:83)\r\n\tat eastnets.screening.regression.iso20022configurations.ISO20022_TC005.iso20022_TC0012(ISO20022_TC005.java:100)\r\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:580)\r\n\tat org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:677)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:221)\r\n\tat org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:50)\r\n\tat org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:969)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:194)\r\n\tat org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148)\r\n\tat org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128)\r\n\tat java.base/java.util.ArrayList.forEach(ArrayList.java:1596)\r\n\tat org.testng.TestRunner.privateRun(TestRunner.java:829)\r\n\tat org.testng.TestRunner.run(TestRunner.java:602)\r\n\tat org.testng.SuiteRunner.runTest(SuiteRunner.java:437)\r\n\tat org.testng.SuiteRunner.runSequentially(SuiteRunner.java:431)\r\n\tat org.testng.SuiteRunner.privateRun(SuiteRunner.java:391)\r\n\tat org.testng.SuiteRunner.run(SuiteRunner.java:330)\r\n\tat org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)\r\n\tat org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)\r\n\tat org.testng.TestNG.runSuitesSequentially(TestNG.java:1256)\r\n\tat org.testng.TestNG.runSuitesLocally(TestNG.java:1176)\r\n\tat org.testng.TestNG.runSuites(TestNG.java:1099)\r\n\tat org.testng.TestNG.run(TestNG.java:1067)\r\n\tat core.BaseTest.rerunFailedTestCases(BaseTest.java:380)\r\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:580)\r\n\tat org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)\r\n\tat org.testng.internal.invokers.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:69)\r\n\tat org.testng.internal.invokers.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:361)\r\n\tat org.testng.internal.invokers.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:296)\r\n\tat org.testng.SuiteRunner.privateRun(SuiteRunner.java:404)\r\n\tat org.testng.SuiteRunner.run(SuiteRunner.java:330)\r\n\tat org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)\r\n\tat org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)\r\n\tat org.testng.TestNG.runSuitesSequentially(TestNG.java:1256)\r\n\tat org.testng.TestNG.runSuitesLocally(TestNG.java:1176)\r\n\tat org.testng.TestNG.runSuites(TestNG.java:1099)\r\n\tat org.testng.TestNG.run(TestNG.java:1067)\r\n\tat org.apache.maven.surefire.testng.TestNGExecutor.run(TestNGExecutor.java:308)\r\n\tat org.apache.maven.surefire.testng.TestNGXmlTestSuite.execute(TestNGXmlTestSuite.java:71)\r\n\tat org.apache.maven.surefire.testng.TestNGProvider.invoke(TestNGProvider.java:113)\r\n\tat org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)\r\n\tat org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)\r\n\tat org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)\r\n\tat org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)\r\n"}, "stage": "finished", "description": "Verify that the user can import the exported custom ISO20022 XSD", "steps": [{"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-465889650', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@6de2f769, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-465889650', officialDate='null', entry=[ListEntry{type='null', name='EntryName-465889650', firstName='EntryFirstName-465889650', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-465889650', firstName='EntryFirstName-465889650', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188425175, "stop": 1754188425175}, {"name": "Navigate to By.xpath: //a[contains(text(),'ISO20022 Format Configuration')]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188427187, "stop": 1754188427187}, {"name": "Navigate to By.xpath: //a[contains(text(),'ISO20022 Format Configuration')]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188429222, "stop": 1754188429222}, {"name": "Create New Group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188432994, "stop": 1754188432994}, {"name": "Connect to Database and Check if User Profile = full-right-profile_07 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188440188, "stop": 1754188440188}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188440188, "stop": 1754188440188}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188440232, "stop": 1754188440232}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_07' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188440232, "stop": 1754188440232}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188440234, "stop": 1754188440234}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188440234, "stop": 1754188440234}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188440235, "stop": 1754188440235}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188440249, "stop": 1754188440249}, {"name": "Delete From tListSetProfile where profile_id in (10)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188440249, "stop": 1754188440249}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188440254, "stop": 1754188440254}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188440254, "stop": 1754188440254}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188440254, "stop": 1754188440254}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188440254, "stop": 1754188440254}, {"name": "Search for list by listName = ListName-465889650 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188458852, "stop": 1754188458852}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188483432, "stop": 1754188483432}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188483432, "stop": 1754188483432}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188543344, "stop": 1754188543344}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188543345, "stop": 1754188543345}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188543345, "stop": 1754188543345}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188543366, "stop": 1754188543366}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_07'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188543366, "stop": 1754188543366}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 17***********, "stop": 17***********}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 17***********, "stop": 17***********}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188543369, "stop": 1754188543369}, {"name": "Navigate to By.xpath: //a[contains(text(),'ISO20022 Schema Configuration')]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188545711, "stop": 1754188545711}, {"name": "Navigate to By.xpath: //a[@href='#iso20022:homepage_business:tabViewListManager:tabView:schemaConfiguration']", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188546591, "stop": 1754188546591}, {"name": "Validation message = The schema already exist!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188549586, "stop": 1754188549586}, {"name": "Navigate to By.xpath: //a[contains(text(),'ISO20022 Format Configuration')]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188551293, "stop": 1754188551293}, {"name": "Add new message.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188552117, "stop": 1754188552117}, {"name": "Navigate to By.xpath: //a[contains(text(),'ISO20022 Format Configuration')]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188553433, "stop": 1754188553433}, {"name": "Sort table results by creation to click on the latest created group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188554071, "stop": 1754188554071}, {"name": "Validation message = This message already exist!\"", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188577378, "stop": 1754188577378}, {"name": "Actual result = This message already exist!\"", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188577378, "stop": 1754188577378}], "attachments": [], "parameters": [{"name": "arg0", "value": "ISO20022FormatConfiguration{zone='Zone (867793082)', groupName='Group_Name-439752740', iso20022SchemaConfiguration=ISO20022SchemaConfiguration{schemaName='pacs.009.001', schemaVersion='08', headerSwift='head.001.001.01', expectedResults='Schema version [%s] successfully deleted'}, expectedResults='Successfully imported schema [pacs.009.001] with version [08].'}"}], "start": 1754188425171, "stop": 1754188577379}