{"uuid": "e4127791-**************-9c1c42aac8e3", "name": "eastnets.admin.AdminTest", "children": ["8718b424-3c14-4180-9c48-35708a4a4e33", "cb354bbb-43e7-409b-b315-af9639a75a2c", "c18a9626-82f4-4be1-aa8c-a736601f24ac", "2ff80d61-ad7e-4991-a81e-590d5292c013", "c2ac7c4f-0436-4ad2-8731-f2d42ce77d7b", "395b49a7-0ffd-4e46-b9e9-0ced64eca775", "ec9796c9-7d9a-4814-a88a-7a43a2bab4f2", "887d48f7-cdde-4187-b3e1-b5028bbbdce5", "258b3686-c2fc-4bd3-9ebb-62a8ad9432c9", "576fd2ce-8880-4ed3-a1e6-448614f69e9f", "8ef7c70b-0b3b-4d06-a96a-476e6ad7481a", "d996147d-b39d-46f4-8492-9edac14263ea", "b60df14a-123b-4794-9210-7d215e9c6eb5", "d2c20e48-3093-409d-be71-fac22163aaa9", "aa323ada-1514-4b63-944c-f872afe49830"], "befores": [{"name": "createNewOperator", "status": "passed", "stage": "finished", "description": "", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181192676, "stop": 1754181192676}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='sysadmin'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181192676, "stop": 1754181192676}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181192682, "stop": 1754181192682}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181192682, "stop": 1754181192682}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181192683, "stop": 1754181192683}, {"name": "Login with User Name = sysadmin and Password = manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181192877, "stop": 1754181192877}, {"name": "Operator test data = Operator{id=0, loginName='selenium-user-834901972', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone877517897', displayName='Zone (877517897)', description='Zone created with random number '877517897''}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181199467, "stop": 1754181199467}, {"name": "Group test data = Group{id=0, name='selenium-random-group-653646973', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-23911606', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone877517897', displayName='Zone (877517897)', description='Zone created with random number '877517897''}, profileRights={Administration={Archive Viewer=[], Operator Manager=[ALL], Group Manager=[ALL], Profile Manager=[ALL], Report Manager=[ALL], Zone Manager=[ALL], Audit Manager=[], Event Viewer=[]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-834901972', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone877517897', displayName='Zone (877517897)', description='Zone created with random number '877517897''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181199467, "stop": 1754181199467}, {"name": "Create new zone .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181199467, "stop": 1754181199467}, {"name": "Zone test Data = Zone{id=0, name='Zone877517897', displayName='Zone (877517897)', description='Zone created with random number '877517897''}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181199467, "stop": 1754181199467}, {"name": "Check if zone with name = 'Zone877517897' exists", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181202368, "stop": 1754181202368}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181202669, "stop": 1754181202669}, {"name": "Enter name =Zone877517897", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181208118, "stop": 1754181208118}, {"name": "Enter display Name =Zone (877517897)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181209115, "stop": 1754181209115}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181212244, "stop": 1754181212244}, {"name": "Zone doesn't exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181213062, "stop": 1754181213062}, {"name": "Click add new zone button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181213062, "stop": 1754181213062}, {"name": "Set create new zone data.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181213392, "stop": 1754181213392}, {"name": "Set name = Zone877517897", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181213392, "stop": 1754181213392}, {"name": "Set display name = Zone (877517897)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181215251, "stop": 1754181215251}, {"name": "Set description = Zone created with random number '877517897'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181217565, "stop": 1754181217565}, {"name": "Capture zone id from UI = 141", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181218780, "stop": 1754181218780}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181218780, "stop": 1754181218780}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181220542, "stop": 1754181220542}, {"name": "Enter name =Zone877517897", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181225938, "stop": 1754181225938}, {"name": "Enter display Name =Zone (877517897)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181226781, "stop": 1754181226781}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181227573, "stop": 1754181227573}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181230814, "stop": 1754181230814}, {"name": "Enter name =Zone877517897", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181236255, "stop": 1754181236255}, {"name": "Enter display Name =Zone (877517897)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181237319, "stop": 1754181237319}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181238592, "stop": 1754181238592}, {"name": "Create new profile .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181239396, "stop": 1754181239396}, {"name": "Profile test Data = Profile{id=0, name='Test-Profile-23911606', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone877517897', displayName='Zone (877517897)', description='Zone created with random number '877517897''}, profileRights={Administration={Archive Viewer=[], Operator Manager=[ALL], Group Manager=[ALL], Profile Manager=[ALL], Report Manager=[ALL], Zone Manager=[ALL], Audit Manager=[], Event Viewer=[]}}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181239398, "stop": 1754181239398}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181241355, "stop": 1754181241355}, {"name": "Set name = Test-Profile-23911606 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181246914, "stop": 1754181246914}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181250240, "stop": 1754181250240}, {"name": "Click add new profile button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181251074, "stop": 1754181251074}, {"name": "Set name = Test-Profile-23911606 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181251343, "stop": 1754181251343}, {"name": "Set description = Random profile for Selenium testing ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181253997, "stop": 1754181253997}, {"name": "Check enable checkbox to be true ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181255350, "stop": 1754181255350}, {"name": "Check write right checkbox to be Test-Profile-23911606 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181256329, "stop": 1754181256329}, {"name": "Select zone = Test-Profile-23911606 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181256667, "stop": 1754181256667}, {"name": "Add Administration item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181264353, "stop": 1754181264353}, {"name": "Click on the Administration item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181264353, "stop": 1754181264353}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181264949, "stop": 1754181264949}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181266219, "stop": 1754181266219}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181266609, "stop": 1754181266609}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181266914, "stop": 1754181266914}, {"name": "Set name = Test-Profile-23911606 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181273805, "stop": 1754181273805}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181275120, "stop": 1754181275120}, {"name": "Select profile from result view.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181276010, "stop": 1754181276010}, {"name": "Click on Administration", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181276788, "stop": 1754181276788}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181278224, "stop": 1754181278224}, {"name": "Add Archive Viewer item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181279557, "stop": 1754181279557}, {"name": "Click on the Archive Viewer item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181279557, "stop": 1754181279557}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181280086, "stop": 1754181280086}, {"name": "Add Operator Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181281579, "stop": 1754181281579}, {"name": "Click on the Operator Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181281579, "stop": 1754181281579}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181282127, "stop": 1754181282127}, {"name": "Add Group Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181283588, "stop": 1754181283588}, {"name": "Click on the Group Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181283588, "stop": 1754181283588}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181284366, "stop": 1754181284366}, {"name": "Add Profile Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181285943, "stop": 1754181285943}, {"name": "Click on the Profile Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181285943, "stop": 1754181285943}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181286604, "stop": 1754181286604}, {"name": "Add Report Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181288025, "stop": 1754181288025}, {"name": "Click on the Report Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181288025, "stop": 1754181288025}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181288539, "stop": 1754181288539}, {"name": "Add Zone Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181290030, "stop": 1754181290030}, {"name": "Click on the Zone Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181290030, "stop": 1754181290030}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181290606, "stop": 1754181290606}, {"name": "Add Audit Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181292119, "stop": 1754181292119}, {"name": "Click on the Audit Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181292119, "stop": 1754181292119}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181292887, "stop": 1754181292887}, {"name": "Add Event Viewer item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181294322, "stop": 1754181294322}, {"name": "Click on the Event Viewer item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181294324, "stop": 1754181294324}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181294919, "stop": 1754181294919}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181296395, "stop": 1754181296395}, {"name": "Accept <PERSON><PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181297092, "stop": 1754181297092}, {"name": "Click on Administration", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181298854, "stop": 1754181298854}, {"name": "Select Module Rights.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181299843, "stop": 1754181299843}, {"name": "Processing module 'Operator Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181301844, "stop": 1754181301844}, {"name": "Click on Operator Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181301844, "stop": 1754181301844}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181302784, "stop": 1754181302784}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181303041, "stop": 1754181303041}, {"name": "Processing module 'Group Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181305558, "stop": 1754181305558}, {"name": "<PERSON>lick on Group Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181305558, "stop": 1754181305558}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181306590, "stop": 1754181306590}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181306989, "stop": 1754181306989}, {"name": "Processing module 'Profile Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181309591, "stop": 1754181309591}, {"name": "Click on Profile Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181309591, "stop": 1754181309591}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181311604, "stop": 1754181311604}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181312106, "stop": 1754181312106}, {"name": "Processing module 'Report Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181315117, "stop": 1754181315117}, {"name": "Click on Report Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181315117, "stop": 1754181315117}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181316158, "stop": 1754181316158}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181316525, "stop": 1754181316525}, {"name": "Processing module 'Zone Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181319040, "stop": 1754181319040}, {"name": "Click on Zone Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181319040, "stop": 1754181319040}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181320055, "stop": 1754181320055}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181320432, "stop": 1754181320432}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181324244, "stop": 1754181324244}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181324723, "stop": 1754181324723}, {"name": "Check if profile exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181326070, "stop": 1754181326070}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181326070, "stop": 1754181326070}, {"name": "Set name = Test-Profile-23911606 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181332339, "stop": 1754181332339}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181333836, "stop": 1754181333836}, {"name": "Profile exist = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181335379, "stop": 1754181335379}, {"name": "Create new operator.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181335379, "stop": 1754181335379}, {"name": "Operator test Data = Operator{id=0, loginName='selenium-user-834901972', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone877517897', displayName='Zone (877517897)', description='Zone created with random number '877517897''}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181335380, "stop": 1754181335380}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181339045, "stop": 1754181339045}, {"name": "Set login name = selenium-user-834901972", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181344508, "stop": 1754181344508}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181347003, "stop": 1754181347003}, {"name": "Click Create new operator button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181348126, "stop": 1754181348126}, {"name": "Set login name = selenium-user-834901972", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181348424, "stop": 1754181348424}, {"name": "Set first name = Random", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181349819, "stop": 1754181349819}, {"name": "Set last name = User", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181350608, "stop": 1754181350608}, {"name": "Set password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181351593, "stop": 1754181351593}, {"name": "Set confirm password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181352778, "stop": 1754181352778}, {"name": "Set enable flag = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181353842, "stop": 1754181353842}, {"name": "Set reset flag = false", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181354857, "stop": 1754181354857}, {"name": "Select zone  = Zone (877517897)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181356579, "stop": 1754181356579}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181363897, "stop": 1754181363897}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181364296, "stop": 1754181364296}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181372028, "stop": 1754181372028}, {"name": "Set login name = selenium-user-834901972", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181377379, "stop": 1754181377379}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181378462, "stop": 1754181378462}, {"name": "Create new group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181379520, "stop": 1754181379520}, {"name": "Group test Data = Group{id=0, name='selenium-random-group-653646973', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-23911606', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone877517897', displayName='Zone (877517897)', description='Zone created with random number '877517897''}, profileRights={Administration={Archive Viewer=[], Operator Manager=[ALL], Group Manager=[ALL], Profile Manager=[ALL], Report Manager=[ALL], Zone Manager=[ALL], Audit Manager=[], Event Viewer=[]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-834901972', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone877517897', displayName='Zone (877517897)', description='Zone created with random number '877517897''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181379520, "stop": 1754181379520}], "attachments": [], "parameters": [], "start": 1754181192235, "stop": 1754181430795}], "afters": [], "start": 1754180159087, "stop": 1754184702590}