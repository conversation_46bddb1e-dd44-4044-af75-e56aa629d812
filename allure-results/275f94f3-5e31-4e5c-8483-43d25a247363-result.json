{"uuid": "275f94f3-5e31-4e5c-8483-43d25a247363", "historyId": "6bde445f6a574388fa663d83b2983136", "fullName": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC019.listManager_TC019", "labels": [{"name": "package", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC019"}, {"name": "testClass", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC019"}, {"name": "testMethod", "value": "listManager_TC019"}, {"name": "parentSuite", "value": "Failed Tests Rerun Suite"}, {"name": "suite", "value": "<PERSON><PERSON> Failed Tests"}, {"name": "subSuite", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC019"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "List Manager"}], "links": [], "name": "Verify that Good Guys migration for a Private List is functioning properly", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158291938, "stop": 1754158291938}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-02'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158291938, "stop": 1754158291938}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158291946, "stop": 1754158291946}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158291946, "stop": 1754158291946}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158291946, "stop": 1754158291946}, {"name": "Login with User Name = operator-02 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158292138, "stop": 1754158292138}, {"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-380006475', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@642eea49, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-380006475', officialDate='null', entry=[ListEntry{type='null', name='EntryName-380006475', firstName='EntryFirstName-380006475', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-380006475', firstName='EntryFirstName-380006475', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158296996, "stop": 1754158296996}, {"name": "Create new black list", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158296996, "stop": 1754158296996}, {"name": "Connect to Database and Check if User Profile = full-right-profile_002 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158296996, "stop": 1754158296996}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158296996, "stop": 1754158296996}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158297049, "stop": 1754158297049}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_002' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158297049, "stop": 1754158297049}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158297051, "stop": 1754158297051}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158297051, "stop": 1754158297051}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158297052, "stop": 1754158297052}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158297072, "stop": 1754158297072}, {"name": "Delete From tListSetProfile where profile_id in (5)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158297072, "stop": 1754158297072}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158297078, "stop": 1754158297078}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158297078, "stop": 1754158297078}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158297079, "stop": 1754158297079}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158297079, "stop": 1754158297079}, {"name": "Search for list by listName = ListName-380006475 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158306744, "stop": 1754158306744}, {"name": "Set zone : Common Zone 02", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158314423, "stop": 1754158314423}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158316344, "stop": 1754158316344}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158316878, "stop": 1754158316878}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158317252, "stop": 1754158317252}, {"name": "Set template name = templateName-380006475", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158317456, "stop": 1754158317456}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158319282, "stop": 1754158319282}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158319998, "stop": 1754158319998}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158321040, "stop": 1754158321040}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158321608, "stop": 1754158321608}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158350274, "stop": 1754158350274}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158350274, "stop": 1754158350274}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158407741, "stop": 1754158407741}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158407741, "stop": 1754158407741}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158407742, "stop": 1754158407742}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158407773, "stop": 1754158407773}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_002'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158407773, "stop": 1754158407773}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158407775, "stop": 1754158407775}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158407775, "stop": 1754158407775}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158407775, "stop": 1754158407775}, {"name": "Successfully wrote to the file.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158436826, "stop": 1754158436826}, {"name": "Select version : v1 - 2025/14/02", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158460667, "stop": 1754158460667}, {"name": "Click on migrate checkbox", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158466844, "stop": 1754158466844}, {"name": "Click on Extra AKAs checkbox", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158467286, "stop": 1754158467286}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158469554, "stop": 1754158469554}, {"name": "Select list set by name.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158493668, "stop": 1754158493668}, {"name": "Validation message = Good guy successfully created!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158500111, "stop": 1754158500111}, {"name": "Alert message = Good guy successfully created!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158500111, "stop": 1754158500111}, {"name": "Select version : v1 - 2025/15/02", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158537237, "stop": 1754158537237}, {"name": "Click on migrate checkbox", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158544082, "stop": 1754158544082}, {"name": "Click on Extra AKAs checkbox", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158544892, "stop": 1754158544892}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754158546784, "stop": 1754158546784}], "attachments": [], "parameters": [], "start": 1754158291584, "stop": 1754158574416}