{"uuid": "564e5706-986f-4a1d-b8b5-33db3709face", "historyId": "42eb3c2d0f8cf06d96aec17e5620434", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC004.ScanManager_TC004", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC004"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC004"}, {"name": "testMethod", "value": "ScanManager_TC004"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC004"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Validate that user is able to Print a detection created from 'File Scan' from Result Tab.", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Validate that user is able to Print a detection created from 'File Scan' from Result Tab.", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241587464, "stop": 1754241587464}, {"name": "Validation message = File sent to the server for processing with id [3122]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241606517, "stop": 1754241606517}, {"name": "Alert Message = File sent to the server for processing with id [3122]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241606517, "stop": 1754241606517}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241608143, "stop": 1754241608143}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241618066, "stop": 1754241618066}, {"name": "Detection ID = 8058", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241619698, "stop": 1754241619698}, {"name": "response.getStatus()200", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241651386, "stop": 1754241651386}, {"name": "UTF-8", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241651386, "stop": 1754241651386}, {"name": "1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241651387, "stop": 1754241651387}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754241587420, "stop": 1754241651387}