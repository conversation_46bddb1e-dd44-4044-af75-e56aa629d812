{"uuid": "b737a791-99ae-40db-aeb0-5ffdacbb2b78", "name": "ISO20022 Configurations Test Cases", "children": ["598fc903-df45-40fa-92c5-c03352e5a9f4", "0bc31feb-3944-46b0-94c2-0500ef5ca735", "1286c762-3962-4fcd-8e3a-4f581832745e", "d06751c7-90c3-491d-b320-86fc41a64280", "94999259-c519-4572-96e1-ecf6b65a941a"], "befores": [{"name": "Setting up selenium web driver before each class run", "status": "passed", "stage": "finished", "description": "Setting up selenium web driver before each class run", "steps": [{"name": "Initialize Selenium driver before tests' Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754263191163, "stop": 1754263191163}, {"name": "Selenium webDriver was initialized successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754263204695, "stop": 1754263204695}], "attachments": [], "parameters": [], "start": 1754263191160, "stop": 1754263204695}], "afters": [{"name": "Quitting selenium driver after each class run", "status": "passed", "stage": "finished", "description": "Quitting selenium driver after each class run", "steps": [{"name": "Closing selenium WebDriver after Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264144481, "stop": 1754264144481}], "attachments": [], "parameters": [], "start": 1754264144481, "stop": 1754264144908}], "start": 1754263191155, "stop": 1754264144908}