{"uuid": "3fd14bd0-9498-4c93-9ab7-ed83afb06ade", "historyId": "42eb3c2d0f8cf06d96aec17e5620434", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC004.ScanManager_TC004", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC004"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC004"}, {"name": "testMethod", "value": "ScanManager_TC004"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC004"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Validate that user is able to Print a detection created from 'File Scan' from Result Tab.", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Validate that user is able to Print a detection created from 'File Scan' from Result Tab.", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235173564, "stop": 1754235173564}, {"name": "Validation message = File sent to the server for processing with id [3086]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235190393, "stop": 1754235190393}, {"name": "Alert Message = File sent to the server for processing with id [3086]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235190393, "stop": 1754235190393}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235190992, "stop": 1754235190992}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235201582, "stop": 1754235201582}, {"name": "Detection ID = 7962", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235203632, "stop": 1754235203632}, {"name": "response.getStatus()200", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235241160, "stop": 1754235241160}, {"name": "UTF-8", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235241160, "stop": 1754235241160}, {"name": "1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235241161, "stop": 1754235241161}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754235173533, "stop": 1754235241161}