{"uuid": "09867e31-418a-4803-ba6a-e338331ebce0", "historyId": "67c353d6fb3f0e8e9c67fa93dfcfe1f5", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC009.scanManager_TC009", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC009"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC009"}, {"name": "testMethod", "value": "scanManager_TC009"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC009"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is able to Print an alert report in RTF format", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to Print an alert report in RTF format", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264467104, "stop": 1754264467104}, {"name": "Validation message = File sent to the server for processing with id [3165]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264499970, "stop": 1754264499970}, {"name": "Alert Message = File sent to the server for processing with id [3165]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264499970, "stop": 1754264499970}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264501510, "stop": 1754264501510}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264510376, "stop": 1754264510376}, {"name": "Detection ID = 8302", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264527685, "stop": 1754264527685}, {"name": "Start Exporting Violation With Print Scope all And Document Type RTF", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264527754, "stop": 1754264527754}, {"name": "Click on close dialog button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264536690, "stop": 1754264536690}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "eastnets.screening.entity.Report@6720a72f"}], "start": 1754264466849, "stop": 1754264567555}