{"uuid": "47cc0371-6cdd-42b9-b63a-b0f25e086a8b", "historyId": "3d50e6d3cec97bb291ce44c44119d5f5", "fullName": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC009.listManager_TC009", "labels": [{"name": "package", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC009"}, {"name": "testClass", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC009"}, {"name": "testMethod", "value": "listManager_TC009"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "List Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC009"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-1(36)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "List Manager"}], "links": [], "name": "Verify that user is able to Export Good Guys", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to Export Good Guys", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235591522, "stop": 1754235591522}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-02'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235591523, "stop": 1754235591523}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235591529, "stop": 1754235591529}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235591529, "stop": 1754235591529}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235591529, "stop": 1754235591529}, {"name": "Login with User Name = operator-02 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235591722, "stop": 1754235591722}, {"name": "<PERSON><PERSON> Reset <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235602240, "stop": 1754235602240}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235602904, "stop": 1754235602904}, {"name": "response.getStatus()200", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235636960, "stop": 1754235636960}, {"name": "UTF-8", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235636960, "stop": 1754235636960}, {"name": "3", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235636960, "stop": 1754235636960}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "PDF"}], "start": 1754235590944, "stop": 1754235640267}