{"uuid": "4750cc9b-2a30-4ecc-81f0-2f48ab93328b", "historyId": "c435cb36bbadfd47600d4b83dc1469d3", "fullName": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC016.listManager_TC016", "labels": [{"name": "package", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC016"}, {"name": "testClass", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC016"}, {"name": "testMethod", "value": "listManager_TC016"}, {"name": "parentSuite", "value": "Failed Tests Rerun Suite"}, {"name": "suite", "value": "<PERSON><PERSON> Failed Tests"}, {"name": "subSuite", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC016"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "List Manager"}], "links": [], "name": "Verify that user is able to search for a List from different zone", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to search for a List from different zone", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159003175, "stop": 1754159003175}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='4eyes01'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159003175, "stop": 1754159003175}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159003181, "stop": 1754159003181}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159003182, "stop": 1754159003182}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159003182, "stop": 1754159003182}, {"name": "Login with User Name = 4eyes01 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159003310, "stop": 1754159003310}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007397, "stop": 1754159007397}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007421, "stop": 1754159007421}, {"name": "select id from tZones tz where tz.DISPLAY_NAME  = 'Four Eyes'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007421, "stop": 1754159007421}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007422, "stop": 1754159007422}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007422, "stop": 1754159007422}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007423, "stop": 1754159007423}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007442, "stop": 1754159007442}, {"name": "DELETE  FROM tSetContent   WHERE id IN ( SELECT tsc.id FROM tSetContent tsc  LEFT JOIN tBlackListVersions tblv on tblv.id =tsc.black_list_id  LEFT JOIN tListSets tls on tls.id = tsc.list_set_id  LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id  WHERE tbl.zone_id ='15'  AND tbl.display_name ='UN Democratic Republic of Congo')", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007442, "stop": 1754159007442}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007456, "stop": 1754159007456}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007456, "stop": 1754159007456}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007457, "stop": 1754159007457}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007457, "stop": 1754159007457}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007478, "stop": 1754159007478}, {"name": "DELETE  FROM tGoodGuys  WHERE id IN ( SELECT tgg.id  FROM tGoodGuys tgg LEFT JOIN tBlackListVersions tblv on tblv.id =tgg.black_list_id LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id WHERE tbl.display_name ='UN Democratic Republic of Congo')", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007478, "stop": 1754159007478}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007494, "stop": 1754159007494}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007494, "stop": 1754159007494}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007495, "stop": 1754159007495}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007495, "stop": 1754159007495}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007518, "stop": 1754159007518}, {"name": "DELETE FROM tBlackLists WHERE display_name ='UN Democratic Republic of Congo'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007518, "stop": 1754159007518}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007710, "stop": 1754159007710}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007710, "stop": 1754159007710}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007711, "stop": 1754159007711}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007711, "stop": 1754159007711}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007736, "stop": 1754159007736}, {"name": "select id from tZones tz where tz.DISPLAY_NAME  = 'Four Eyes'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007737, "stop": 1754159007737}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007737, "stop": 1754159007737}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007737, "stop": 1754159007737}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007738, "stop": 1754159007738}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007762, "stop": 1754159007762}, {"name": "DELETE  FROM tSetContent   WHERE id IN ( SELECT tsc.id FROM tSetContent tsc  LEFT JOIN tBlackListVersions tblv on tblv.id =tsc.black_list_id  LEFT JOIN tListSets tls on tls.id = tsc.list_set_id  LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id  WHERE tbl.zone_id ='15'  AND tbl.display_name ='UN Yemen')", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007762, "stop": 1754159007762}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007782, "stop": 1754159007782}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007782, "stop": 1754159007782}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007782, "stop": 1754159007782}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007782, "stop": 1754159007782}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007805, "stop": 1754159007805}, {"name": "DELETE  FROM tGoodGuys  WHERE id IN ( SELECT tgg.id  FROM tGoodGuys tgg LEFT JOIN tBlackListVersions tblv on tblv.id =tgg.black_list_id LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id WHERE tbl.display_name ='UN Yemen')", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007805, "stop": 1754159007805}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007820, "stop": 1754159007820}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007820, "stop": 1754159007820}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007821, "stop": 1754159007821}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007821, "stop": 1754159007821}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007846, "stop": 1754159007846}, {"name": "DELETE FROM tBlackLists WHERE display_name ='UN Yemen'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007846, "stop": 1754159007846}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007937, "stop": 1754159007937}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007937, "stop": 1754159007937}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007937, "stop": 1754159007937}, {"name": "Import 'UN Democratic Republic of Congo' Black List on user linked on 'Four Eyes' zone", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159007937, "stop": 1754159007937}, {"name": "Share 'UN Democratic Republic of Congo' black list", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159021007, "stop": 1754159021007}, {"name": "Validation Message = null", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159025544, "stop": 1754159025544}, {"name": "Import 'UN Yemen' Black List on user linked on 'Four Eyes' zone", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159025544, "stop": 1754159025544}, {"name": "Log out and login with another user linked to 'Four Eyes' zone", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159038060, "stop": 1754159038060}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159040115, "stop": 1754159040115}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='4eyes02'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159040115, "stop": 1754159040115}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159040118, "stop": 1754159040118}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159040118, "stop": 1754159040118}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159040118, "stop": 1754159040118}, {"name": "Login with User Name = 4eyes02 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159040236, "stop": 1754159040236}, {"name": "Verify that user linked with 'Four Eyes' zone have access to 'UN Democratic Republic of Congo' shared black list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159044215, "stop": 1754159044215}, {"name": "Verify that user linked with 'Four Eyes' zone have access to 'UN Yemen' non-shared black list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159050488, "stop": 1754159050488}, {"name": "Log out and login with another user linked to 'Default Zone' zone", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159056914, "stop": 1754159056914}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159059042, "stop": 1754159059042}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='def-operator-1'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159059042, "stop": 1754159059042}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159059049, "stop": 1754159059049}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159059049, "stop": 1754159059049}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159059050, "stop": 1754159059050}, {"name": "Login with User Name = def-operator-1 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159059212, "stop": 1754159059212}, {"name": "Verify that user linked with 'Default Zone' zone have access to 'UN Democratic Republic of Congo' shared black list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159063264, "stop": 1754159063264}, {"name": "Verify that user linked with 'Default Zone' zone not have access to 'UN Yemen' non-shared black list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159069755, "stop": 1754159069755}], "attachments": [], "parameters": [], "start": 1754159002811, "stop": 1754159077531}