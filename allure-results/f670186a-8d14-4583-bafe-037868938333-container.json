{"uuid": "f670186a-8d14-4583-bafe-037868938333", "name": "Admin Test Cases", "children": ["1928b884-bffb-41f9-9c3e-b5213f10e6a9", "1648ef6d-b7b9-46c2-a401-5f95bd4247ed", "8718b424-3c14-4180-9c48-35708a4a4e33", "cb354bbb-43e7-409b-b315-af9639a75a2c", "c18a9626-82f4-4be1-aa8c-a736601f24ac", "2ff80d61-ad7e-4991-a81e-590d5292c013", "c2ac7c4f-0436-4ad2-8731-f2d42ce77d7b", "395b49a7-0ffd-4e46-b9e9-0ced64eca775", "ec9796c9-7d9a-4814-a88a-7a43a2bab4f2", "887d48f7-cdde-4187-b3e1-b5028bbbdce5", "258b3686-c2fc-4bd3-9ebb-62a8ad9432c9", "576fd2ce-8880-4ed3-a1e6-448614f69e9f", "8ef7c70b-0b3b-4d06-a96a-476e6ad7481a", "d996147d-b39d-46f4-8492-9edac14263ea", "b60df14a-123b-4794-9210-7d215e9c6eb5", "d2c20e48-3093-409d-be71-fac22163aaa9", "aa323ada-1514-4b63-944c-f872afe49830"], "befores": [{"name": "Setting up selenium web driver before each class run", "status": "passed", "stage": "finished", "description": "Setting up selenium web driver before each class run", "steps": [{"name": "Initialize Selenium driver before tests' Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180159087, "stop": 1754180159087}, {"name": "Selenium webDriver was initialized successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180165058, "stop": 1754180165058}], "attachments": [], "parameters": [], "start": 1754180159087, "stop": 1754180165058}], "afters": [{"name": "Quitting selenium driver after each class run", "status": "passed", "stage": "finished", "description": "Quitting selenium driver after each class run", "steps": [{"name": "Closing selenium WebDriver after Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184701973, "stop": 1754184701973}], "attachments": [], "parameters": [], "start": 1754184701973, "stop": 1754184702587}], "start": 1754180159087, "stop": 1754184702587}