{"uuid": "d2c20e48-3093-409d-be71-fac22163aaa9", "historyId": "c1c14de74bb43bbf5f8d10a287df71e", "fullName": "eastnets.admin.AdminTest.checkPermissionWhenAssignSecGroupProfileZone", "labels": [{"name": "package", "value": "eastnets.admin.AdminTest"}, {"name": "testClass", "value": "eastnets.admin.AdminTest"}, {"name": "testMethod", "value": "checkPermissionWhenAssignSecGroupProfileZone"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Admin Test Cases"}, {"name": "subSuite", "value": "eastnets.admin.AdminTest"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-1(36)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}], "links": [], "name": "Verify that the user that is assigned to Group, Profile, and Zone have permission to Release but don't have the permission to Block is able to block when this user is assigned to a second Group, Profile and Zone that have the permission to Block.", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that the user that is assigned to Group, Profile, and Zone have permission to Release but don't have the permission to Block is able to block when this user is assigned to a second Group, Profile and Zone that have the permission to Block.", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183089231, "stop": 1754183089231}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='sysadmin'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183089231, "stop": 1754183089231}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183089238, "stop": 1754183089238}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183089238, "stop": 1754183089238}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183089238, "stop": 1754183089238}, {"name": "Login with User Name = sysadmin and Password = manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183089522, "stop": 1754183089522}, {"name": "Operator test data = Operator{id=0, loginName='selenium-user-745514521', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone16673657', displayName='Zone (16673657)', description='Zone created with random number '16673657''}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183095113, "stop": 1754183095113}, {"name": "Group test data = Group{id=0, name='selenium-random-group-227266669', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-923559846', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone16673657', displayName='Zone (16673657)', description='Zone created with random number '16673657''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-745514521', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone16673657', displayName='Zone (16673657)', description='Zone created with random number '16673657''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183095113, "stop": 1754183095113}, {"name": "Create new zone .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183095113, "stop": 1754183095113}, {"name": "Zone test Data = Zone{id=0, name='Zone16673657', displayName='Zone (16673657)', description='Zone created with random number '16673657''}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183095113, "stop": 1754183095113}, {"name": "Check if zone with name = 'Zone16673657' exists", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183096670, "stop": 1754183096670}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183096968, "stop": 1754183096968}, {"name": "Enter name =Zone16673657", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183102382, "stop": 1754183102382}, {"name": "Enter display Name =Zone (16673657)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183103409, "stop": 1754183103409}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183104369, "stop": 1754183104369}, {"name": "Zone doesn't exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183104983, "stop": 1754183104983}, {"name": "Click add new zone button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183104983, "stop": 1754183104983}, {"name": "Set create new zone data.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183105254, "stop": 1754183105254}, {"name": "Set name = Zone16673657", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183105254, "stop": 1754183105254}, {"name": "Set display name = Zone (16673657)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183106831, "stop": 1754183106831}, {"name": "Set description = Zone created with random number '16673657'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183108803, "stop": 1754183108803}, {"name": "Capture zone id from UI = 144", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183110882, "stop": 1754183110882}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183110882, "stop": 1754183110882}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183112316, "stop": 1754183112316}, {"name": "Enter name =Zone16673657", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183117794, "stop": 1754183117794}, {"name": "Enter display Name =Zone (16673657)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183118940, "stop": 1754183118940}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183119904, "stop": 1754183119904}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183123967, "stop": 1754183123967}, {"name": "Enter name =Zone16673657", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183129615, "stop": 1754183129615}, {"name": "Enter display Name =Zone (16673657)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183130399, "stop": 1754183130399}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183131132, "stop": 1754183131132}, {"name": "Create first profile .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183131727, "stop": 1754183131727}, {"name": "Profile test Data = Profile{id=0, name='Test-Profile-923559846', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone16673657', displayName='Zone (16673657)', description='Zone created with random number '16673657''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183131727, "stop": 1754183131727}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183133583, "stop": 1754183133583}, {"name": "Set name = Test-Profile-923559846 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183139043, "stop": 1754183139043}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183140020, "stop": 1754183140020}, {"name": "Click add new profile button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183140924, "stop": 1754183140924}, {"name": "Set name = Test-Profile-923559846 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183141217, "stop": 1754183141217}, {"name": "Set description = Random profile for Selenium testing ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183142902, "stop": 1754183142902}, {"name": "Check enable checkbox to be true ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183144200, "stop": 1754183144200}, {"name": "Check write right checkbox to be Test-Profile-923559846 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183145169, "stop": 1754183145169}, {"name": "Select zone = Test-Profile-923559846 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183145522, "stop": 1754183145522}, {"name": "Add SWS item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183153562, "stop": 1754183153562}, {"name": "Click on the SWS item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183153563, "stop": 1754183153563}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183154316, "stop": 1754183154316}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183156454, "stop": 1754183156454}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183156844, "stop": 1754183156844}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183156942, "stop": 1754183156942}, {"name": "Set name = Test-Profile-923559846 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183163115, "stop": 1754183163115}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183164440, "stop": 1754183164440}, {"name": "Select profile from result view.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183165215, "stop": 1754183165215}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183172409, "stop": 1754183172409}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183174335, "stop": 1754183174335}, {"name": "Add en.Reporting Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183175157, "stop": 1754183175157}, {"name": "Click on the en.Reporting Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183175157, "stop": 1754183175157}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183175874, "stop": 1754183175874}, {"name": "Add SWIFT Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183177412, "stop": 1754183177412}, {"name": "Click on the SWIFT Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183177412, "stop": 1754183177412}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183178521, "stop": 1754183178521}, {"name": "Add SWP Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183180040, "stop": 1754183180040}, {"name": "Click on the SWP Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183180040, "stop": 1754183180040}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183180971, "stop": 1754183180971}, {"name": "Add Detection Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183182449, "stop": 1754183182449}, {"name": "Click on the Detection Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183182451, "stop": 1754183182451}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183183163, "stop": 1754183183163}, {"name": "Add Scan Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183184668, "stop": 1754183184668}, {"name": "Click on the Scan Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183184668, "stop": 1754183184668}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183185225, "stop": 1754183185225}, {"name": "Add Format Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183186698, "stop": 1754183186698}, {"name": "Click on the Format Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183186698, "stop": 1754183186698}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183187240, "stop": 1754183187240}, {"name": "Add Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183188662, "stop": 1754183188662}, {"name": "Click on the Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183188662, "stop": 1754183188662}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183189106, "stop": 1754183189106}, {"name": "Add Batch List Management item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183190715, "stop": 1754183190715}, {"name": "Click on the Batch List Management item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183190715, "stop": 1754183190715}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183191250, "stop": 1754183191250}, {"name": "Add ListExplorer item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183192673, "stop": 1754183192673}, {"name": "Click on the ListExplorer item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183192673, "stop": 1754183192673}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183193626, "stop": 1754183193626}, {"name": "Add ListSetManager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183195138, "stop": 1754183195138}, {"name": "Click on the ListSetManager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183195138, "stop": 1754183195138}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183195750, "stop": 1754183195750}, {"name": "Add List Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183197227, "stop": 1754183197227}, {"name": "Click on the List Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183197227, "stop": 1754183197227}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183198447, "stop": 1754183198447}, {"name": "Add File Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183200477, "stop": 1754183200477}, {"name": "Click on the File Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183200477, "stop": 1754183200477}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183201044, "stop": 1754183201044}, {"name": "Add Advanced Settings item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183202568, "stop": 1754183202568}, {"name": "Click on the Advanced Settings item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183202568, "stop": 1754183202568}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183203217, "stop": 1754183203217}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183204713, "stop": 1754183204713}, {"name": "Accept <PERSON><PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183205271, "stop": 1754183205271}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183207560, "stop": 1754183207560}, {"name": "Select Module Rights.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183208489, "stop": 1754183208489}, {"name": "Processing module 'SWIFT Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183210495, "stop": 1754183210495}, {"name": "Click on SWIFT Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183210496, "stop": 1754183210496}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183211643, "stop": 1754183211643}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183211974, "stop": 1754183211974}, {"name": "Processing module 'Detection Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183215839, "stop": 1754183215839}, {"name": "Click on Detection Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183215840, "stop": 1754183215840}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183216992, "stop": 1754183216992}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183217426, "stop": 1754183217426}, {"name": "Processing module 'Scan Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183220423, "stop": 1754183220423}, {"name": "Click on Scan Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183220423, "stop": 1754183220423}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183222015, "stop": 1754183222015}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183222498, "stop": 1754183222498}, {"name": "Processing module 'Format Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183225158, "stop": 1754183225158}, {"name": "Click on Format Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183225159, "stop": 1754183225159}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183226356, "stop": 1754183226356}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183227069, "stop": 1754183227069}, {"name": "Processing module 'Name Checker'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183229656, "stop": 1754183229656}, {"name": "Click on Name Checker", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183229656, "stop": 1754183229656}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183230664, "stop": 1754183230664}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183230969, "stop": 1754183230969}, {"name": "Processing module 'ListExplorer'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183234518, "stop": 1754183234518}, {"name": "Click on ListExplorer", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183234518, "stop": 1754183234518}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183235534, "stop": 1754183235534}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183235936, "stop": 1754183235936}, {"name": "Processing module 'ListSetManager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183238446, "stop": 1754183238446}, {"name": "Click on ListSetManager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183238446, "stop": 1754183238446}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183239410, "stop": 1754183239410}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183239979, "stop": 1754183239979}, {"name": "Processing module 'List Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183242604, "stop": 1754183242604}, {"name": "Click on List Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183242604, "stop": 1754183242604}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183243583, "stop": 1754183243583}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183243981, "stop": 1754183243981}, {"name": "Processing module 'File Scanner'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183246510, "stop": 1754183246510}, {"name": "Click on File Scanner", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183246510, "stop": 1754183246510}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183247578, "stop": 1754183247578}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183247915, "stop": 1754183247915}, {"name": "Processing module 'Advanced Settings'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183250362, "stop": 1754183250362}, {"name": "Click on Advanced Settings", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183250362, "stop": 1754183250362}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183251405, "stop": 1754183251405}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183251970, "stop": 1754183251970}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183253524, "stop": 1754183253524}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183253868, "stop": 1754183253868}, {"name": "Check if profile exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183256089, "stop": 1754183256089}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183256089, "stop": 1754183256089}, {"name": "Set name = Test-Profile-923559846 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183262057, "stop": 1754183262057}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183263044, "stop": 1754183263044}, {"name": "Profile exist = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183264347, "stop": 1754183264347}, {"name": "Create new operator.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183264347, "stop": 1754183264347}, {"name": "Operator test Data = Operator{id=0, loginName='selenium-user-745514521', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone16673657', displayName='Zone (16673657)', description='Zone created with random number '16673657''}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183264347, "stop": 1754183264347}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183267308, "stop": 1754183267308}, {"name": "Set login name = selenium-user-745514521", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183273003, "stop": 1754183273003}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183274012, "stop": 1754183274012}, {"name": "Click Create new operator button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183275260, "stop": 1754183275260}, {"name": "Set login name = selenium-user-745514521", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183275688, "stop": 1754183275688}, {"name": "Set first name = Random", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183277606, "stop": 1754183277606}, {"name": "Set last name = User", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183278832, "stop": 1754183278832}, {"name": "Set password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183280408, "stop": 1754183280408}, {"name": "Set confirm password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183281806, "stop": 1754183281806}, {"name": "Set enable flag = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183283192, "stop": 1754183283192}, {"name": "Set reset flag = false", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183284004, "stop": 1754183284004}, {"name": "Select zone  = Zone (16673657)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183285998, "stop": 1754183285998}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183347175, "stop": 1754183347175}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183347783, "stop": 1754183347783}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183362126, "stop": 1754183362126}, {"name": "Set login name = selenium-user-745514521", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183367993, "stop": 1754183367993}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183369156, "stop": 1754183369157}, {"name": "Create first group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183370288, "stop": 1754183370288}, {"name": "Group test Data = Group{id=0, name='selenium-random-group-227266669', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-923559846', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone16673657', displayName='Zone (16673657)', description='Zone created with random number '16673657''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-745514521', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone16673657', displayName='Zone (16673657)', description='Zone created with random number '16673657''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183370288, "stop": 1754183370288}, {"name": "Connect to database to remove Link between profile = Test-Profile-923559846 and Permission Allow to change detection status to Real Violation(Block detection)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183442685, "stop": 1754183442685}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183442702, "stop": 1754183442702}, {"name": "SELECT P.NAME as PRO<PERSON><PERSON>_NAME , PR.PROFILE_ID as PROFILE_ID , PR.GRANT_ID as GRANT_ID , PR.GRANT_RIGHT_ID as GRANT_RIGHT_ID , PR.GRANT_TYPE_ID as GRANT_TYPE_ID   FROM tModuleFunctions M   LEFT JOIN tProfileRights PR On PR.GRANT_ID  = M.ID     LEFT Join tProfiles P ON P.id =PR.PROFILE_ID   WHERE DESCRIPTION ='Allow to change detection status to Real Violation(Block detection)' and P.NAME = 'Test-Profile-923559846'  and PR.GRANT_TYPE_ID =3", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183442702, "stop": 1754183442702}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183442716, "stop": 1754183442716}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183442716, "stop": 1754183442716}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183442717, "stop": 1754183442717}, {"name": "Delete function permission 'Allow to change detection status to Real Violation(Block detection)' from DataBase", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183442717, "stop": 1754183442717}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183442736, "stop": 1754183442736}, {"name": "DELETE FROM tProfileRights  WHERE GRANT_ID  ='130' and PROFILE_ID ='166' and GRANT_TYPE_ID =3", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183442736, "stop": 1754183442736}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183442758, "stop": 1754183442758}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183442758, "stop": 1754183442758}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183442759, "stop": 1754183442759}, {"name": "Create second zone .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183442760, "stop": 1754183442760}, {"name": "Zone test Data = Zone{id=0, name='Zone441414001', displayName='Zone (441414001)', description='Zone created with random number '441414001''}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183442760, "stop": 1754183442760}, {"name": "Check if zone with name = 'Zone441414001' exists", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183444276, "stop": 1754183444276}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183444537, "stop": 1754183444537}, {"name": "Enter name =Zone441414001", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183449888, "stop": 1754183449888}, {"name": "Enter display Name =Zone (441414001)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183450790, "stop": 1754183450790}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183451713, "stop": 1754183451713}, {"name": "Zone doesn't exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183452404, "stop": 1754183452404}, {"name": "Click add new zone button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183452404, "stop": 1754183452404}, {"name": "Set create new zone data.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183452814, "stop": 1754183452814}, {"name": "Set name = Zone441414001", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183452814, "stop": 1754183452814}, {"name": "Set display name = Zone (441414001)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183454630, "stop": 1754183454630}, {"name": "Set description = Zone created with random number '441414001'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183455445, "stop": 1754183455445}, {"name": "Capture zone id from UI = 145", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183456677, "stop": 1754183456677}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183456677, "stop": 1754183456677}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183460766, "stop": 1754183460766}, {"name": "Enter name =Zone441414001", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183466264, "stop": 1754183466264}, {"name": "Enter display Name =Zone (441414001)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183467225, "stop": 1754183467225}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183467960, "stop": 1754183467960}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183472014, "stop": 1754183472014}, {"name": "Enter name =Zone441414001", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183477918, "stop": 1754183477918}, {"name": "Enter display Name =Zone (441414001)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183478946, "stop": 1754183478946}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183479948, "stop": 1754183479948}, {"name": "Create second profile .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183480934, "stop": 1754183480934}, {"name": "Profile test Data = Profile{id=0, name='Test-Profile-795632314', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone441414001', displayName='Zone (441414001)', description='Zone created with random number '441414001''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183480934, "stop": 1754183480934}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183482738, "stop": 1754183482738}, {"name": "Set name = Test-Profile-795632314 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183488276, "stop": 1754183488276}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183489426, "stop": 1754183489426}, {"name": "Click add new profile button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183490597, "stop": 1754183490597}, {"name": "Set name = Test-Profile-795632314 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183490904, "stop": 1754183490904}, {"name": "Set description = Random profile for Selenium testing ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183492601, "stop": 1754183492601}, {"name": "Check enable checkbox to be true ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183493750, "stop": 1754183493751}, {"name": "Check write right checkbox to be Test-Profile-795632314 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183494897, "stop": 1754183494897}, {"name": "Select zone = Test-Profile-795632314 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183495597, "stop": 1754183495597}, {"name": "Add SWS item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183504075, "stop": 1754183504075}, {"name": "Click on the SWS item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183504075, "stop": 1754183504075}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183504649, "stop": 1754183504649}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183505772, "stop": 1754183505772}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183506183, "stop": 1754183506183}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183506271, "stop": 1754183506271}, {"name": "Set name = Test-Profile-795632314 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183512895, "stop": 1754183512895}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183514431, "stop": 1754183514431}, {"name": "Select profile from result view.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183515442, "stop": 1754183515442}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183516282, "stop": 1754183516282}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183517686, "stop": 1754183517686}, {"name": "Add en.Reporting Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183518994, "stop": 1754183518994}, {"name": "Click on the en.Reporting Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183518994, "stop": 1754183518994}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183519945, "stop": 1754183519945}, {"name": "Add SWIFT Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183521252, "stop": 1754183521252}, {"name": "Click on the SWIFT Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183521252, "stop": 1754183521252}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183521876, "stop": 1754183521876}, {"name": "Add SWP Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183523308, "stop": 1754183523308}, {"name": "Click on the SWP Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183523308, "stop": 1754183523308}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183524261, "stop": 1754183524261}, {"name": "Add Detection Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183525715, "stop": 1754183525715}, {"name": "Click on the Detection Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183525716, "stop": 1754183525716}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183526560, "stop": 1754183526560}, {"name": "Add Scan Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183528135, "stop": 1754183528135}, {"name": "Click on the Scan Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183528135, "stop": 1754183528135}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183528884, "stop": 1754183528884}, {"name": "Add Format Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183530356, "stop": 1754183530356}, {"name": "Click on the Format Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183530356, "stop": 1754183530356}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183530953, "stop": 1754183530953}, {"name": "Add Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183532382, "stop": 1754183532382}, {"name": "Click on the Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183532382, "stop": 1754183532382}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183533029, "stop": 1754183533029}, {"name": "Add Batch List Management item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183534490, "stop": 1754183534490}, {"name": "Click on the Batch List Management item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183534490, "stop": 1754183534490}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183535179, "stop": 1754183535179}, {"name": "Add ListExplorer item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183536646, "stop": 1754183536646}, {"name": "Click on the ListExplorer item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183536646, "stop": 1754183536646}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183537512, "stop": 1754183537512}, {"name": "Add ListSetManager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183538850, "stop": 1754183538850}, {"name": "Click on the ListSetManager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183538850, "stop": 1754183538850}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183539502, "stop": 1754183539502}, {"name": "Add List Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183541220, "stop": 1754183541220}, {"name": "Click on the List Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183541220, "stop": 1754183541220}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183541788, "stop": 1754183541788}, {"name": "Add File Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183543205, "stop": 1754183543205}, {"name": "Click on the File Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183543205, "stop": 1754183543205}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183543729, "stop": 1754183543729}, {"name": "Add Advanced Settings item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183545197, "stop": 1754183545197}, {"name": "Click on the Advanced Settings item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183545197, "stop": 1754183545197}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183545965, "stop": 1754183545965}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183547458, "stop": 1754183547458}, {"name": "Accept <PERSON><PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183548202, "stop": 1754183548202}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183550706, "stop": 1754183550706}, {"name": "Select Module Rights.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183551635, "stop": 1754183551635}, {"name": "Processing module 'SWIFT Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183553636, "stop": 1754183553636}, {"name": "Click on SWIFT Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183553636, "stop": 1754183553636}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183554636, "stop": 1754183554636}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183554945, "stop": 1754183554945}, {"name": "Processing module 'Detection Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183558239, "stop": 1754183558239}, {"name": "Click on Detection Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183558239, "stop": 1754183558239}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183559680, "stop": 1754183559680}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183560054, "stop": 1754183560054}, {"name": "Processing module 'Scan Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183562726, "stop": 1754183562726}, {"name": "Click on Scan Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183562726, "stop": 1754183562726}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183563986, "stop": 1754183563986}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183565093, "stop": 1754183565093}, {"name": "Processing module 'Format Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183567849, "stop": 1754183567849}, {"name": "Click on Format Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183567849, "stop": 1754183567849}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183569859, "stop": 1754183569859}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183570415, "stop": 1754183570415}, {"name": "Processing module 'Name Checker'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183573223, "stop": 1754183573223}, {"name": "Click on Name Checker", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183573224, "stop": 1754183573224}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183574595, "stop": 1754183574595}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183575132, "stop": 1754183575132}, {"name": "Processing module 'ListExplorer'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183579103, "stop": 1754183579104}, {"name": "Click on ListExplorer", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183579105, "stop": 1754183579105}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183580507, "stop": 1754183580507}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183581427, "stop": 1754183581427}, {"name": "Processing module 'ListSetManager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183584118, "stop": 1754183584118}, {"name": "Click on ListSetManager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183584118, "stop": 1754183584118}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183585369, "stop": 1754183585369}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183585876, "stop": 1754183585876}, {"name": "Processing module 'List Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183589545, "stop": 1754183589545}, {"name": "Click on List Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183589545, "stop": 1754183589545}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183590628, "stop": 1754183590628}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183591010, "stop": 1754183591010}, {"name": "Processing module 'File Scanner'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183593457, "stop": 1754183593457}, {"name": "Click on File Scanner", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183593457, "stop": 1754183593457}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183594578, "stop": 1754183594578}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183594939, "stop": 1754183594939}, {"name": "Processing module 'Advanced Settings'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183597264, "stop": 1754183597264}, {"name": "Click on Advanced Settings", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183597264, "stop": 1754183597264}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183598584, "stop": 1754183598584}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183598942, "stop": 1754183598942}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183600408, "stop": 1754183600408}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183600722, "stop": 1754183600722}, {"name": "Check if profile exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183602313, "stop": 1754183602313}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183602313, "stop": 1754183602313}, {"name": "Set name = Test-Profile-795632314 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183608443, "stop": 1754183608443}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183609876, "stop": 1754183609876}, {"name": "Profile exist = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183611210, "stop": 1754183611210}, {"name": "Create second group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183611210, "stop": 1754183611210}, {"name": "Group test Data = Group{id=0, name='selenium-random-group-800822790', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-795632314', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone441414001', displayName='Zone (441414001)', description='Zone created with random number '441414001''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-745514521', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone16673657', displayName='Zone (16673657)', description='Zone created with random number '16673657''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183611210, "stop": 1754183611210}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183662799, "stop": 1754183662799}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='selenium-user-745514521'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183662800, "stop": 1754183662800}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183662807, "stop": 1754183662807}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183662807, "stop": 1754183662807}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183662808, "stop": 1754183662808}, {"name": "Login with User Name = selenium-user-745514521 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183663019, "stop": 1754183663019}, {"name": "Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-980285233', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@3baa3c0f, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-980285233', officialDate='null', entry=[ListEntry{type='null', name='EntryName-980285233', firstName='EntryFirstName-980285233', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-980285233', firstName='EntryFirstName-980285233', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183670000, "stop": 1754183670000}, {"name": "Connect to Database and Check if User Profile = Test-Profile-795632314 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183670006, "stop": 1754183670006}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183670006, "stop": 1754183670006}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183670057, "stop": 1754183670057}, {"name": "SELECT ID FROM tProfiles Where NAME ='Test-Profile-795632314' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183670057, "stop": 1754183670057}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183670059, "stop": 1754183670059}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183670059, "stop": 1754183670059}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183670059, "stop": 1754183670059}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183670079, "stop": 1754183670079}, {"name": "Delete From tListSetProfile where profile_id in (167)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183670079, "stop": 1754183670079}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183670081, "stop": 1754183670081}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183670081, "stop": 1754183670081}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183670081, "stop": 1754183670081}, {"name": "Number of effected rows = 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183670081, "stop": 1754183670081}, {"name": "Search for list by listName = ListName-980285233 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183693326, "stop": 1754183693326}, {"name": "Set zone : Zone (441414001)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183733865, "stop": 1754183733865}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183739061, "stop": 1754183739061}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183740628, "stop": 1754183740628}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183741372, "stop": 1754183741372}, {"name": "Set template name = templateName-980285233", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183741887, "stop": 1754183741887}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183744609, "stop": 1754183744609}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183745358, "stop": 1754183745358}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183746728, "stop": 1754183746728}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183747419, "stop": 1754183747419}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183768717, "stop": 1754183768717}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183768717, "stop": 1754183768717}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183825398, "stop": 1754183825398}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183825398, "stop": 1754183825398}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183825398, "stop": 1754183825398}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183825423, "stop": 1754183825423}, {"name": "SELECT ID FROM tProfiles Where NAME ='Test-Profile-795632314'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183825423, "stop": 1754183825423}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183825425, "stop": 1754183825425}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183825425, "stop": 1754183825425}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183825425, "stop": 1754183825425}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183850028, "stop": 1754183850028}, {"name": "RJE File Content= {1:F01PTSABEMMAXXX0000000001}{2:I564ACCLFRP1XXXXN}{3:{108:ENT144037000001}}{4:\r\n:16R:GENL\r\n:20C::CORP//2015033100010560\r\n:20C::SEME//0105601900000002\r\n:23G:EntryName-980285233, EntryFirstName-980285233\r\n:22F::CAEV//DVCA\r\n:22F::CAMV//MAND\r\n:98A::PREP//20150331\r\n:25D::PROC//COMP\r\n:16R:LINK\r\n:13A::LINK//564\r\n:20C::PREV//0105601900000001\r\n:16S:LINK\r\n:16S:GENL\r\n:16R:USECU\r\n:35B:ISIN DE0005190003\r\nBMW Bayerische Motoren Werke AG\r\n:16R:ACCTINFO\r\n:97A::SAFE//000000190003\r\n:93B::ELIG//UNIT/360,\r\n:93B::PEND//UNIT/360,\r\n:93B::PENR//UNIT/0,\r\n:93B::SETT//UNIT/360,\r\n:16S:ACCTINFO\r\n:16S:USECU\r\n:16R:CADETL\r\n:98A::XDTE//20150514\r\n:98A::RDTE//20150515\r\n:22F::DIVI//FINL\r\n:16S:CADETL\r\n:16R:CAOPTN\r\n:13A::CAON//001\r\n:22F::CAOP//CASH\r\n:17B::DFLT//Y\r\n:16R:CASHMOVE\r\n:22H::CRDB//CRED\r\n:98A::PAYD//20150514\r\n:92A::TAXR//26,375\r\n:92F::GRSS//EUR2,9\r\n:16S:CASHMOVE\r\n:16S:CAOPTN\r\n-}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183860117, "stop": 1754183860117}, {"name": "Start coping data from sample file to another file to be used.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183860117, "stop": 1754183860117}, {"name": "Successfully wrote to the file.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183860121, "stop": 1754183860121}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183862551, "stop": 1754183862551}, {"name": "Validation message = File sent to the server for processing with id [3032]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183880251, "stop": 1754183880251}, {"name": "Alert Message = File sent to the server for processing with id [3032]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183880251, "stop": 1754183880251}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183881417, "stop": 1754183881417}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183890410, "stop": 1754183890410}, {"name": "Detection ID = 7707", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183891319, "stop": 1754183891319}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183896816, "stop": 1754183896816}, {"name": "Check if user can block detection. ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183898179, "stop": 1754183898179}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183903700, "stop": 1754183903700}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "Operator{id=0, loginName='null', firstName='null', lastName='null', password='null', zone=null}"}], "start": 1754183088689, "stop": 1754183915370}