{"uuid": "b63e4cb8-0fa0-4bb2-b206-af80f2a02942", "name": "Safe Watch Filtering", "children": ["5624452a-bac3-4184-85fd-bc1f16464d7c", "a0de7463-71c6-4102-b9e9-b6a0279df69d", "37b9ab00-2f11-4f68-bcbf-00ff047daaf6", "1f488dff-fa0e-4798-b0d0-c888888a9fe6", "7557da03-4f9d-4735-8ff2-cac4b835e566", "44be8e26-6511-4c27-acb4-c954bb52c184", "70b7bb42-6672-4f25-9361-3f23000a8df7", "f670186a-8d14-4583-bafe-037868938333"], "befores": [{"name": "Setting up allure report", "status": "passed", "stage": "finished", "description": "Setting up allure report", "steps": [{"name": "Setting up allure report environment data: Edge", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754171885597, "stop": 1754171885598}], "attachments": [], "parameters": [], "start": 1754171885591, "stop": 1754171885798}], "afters": [{"name": "rerunFailedTestCases", "status": "passed", "stage": "finished", "description": "", "steps": [{"name": "========================================", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132585, "stop": 1754186132585}, {"name": "RERUNNING FAILED TEST CASES", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132585, "stop": 1754186132585}, {"name": "========================================", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132585, "stop": 1754186132585}, {"name": "Found 29 failed test(s) to rerun:", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132585, "stop": 1754186132585}, {"name": "  - eastnets.screening.regression.iso20022configurations.ISO20022_TC002.iso20022_TC0012", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132585, "stop": 1754186132585}, {"name": "  - eastnets.screening.regression.iso20022configurations.ISO20022_TC003.iso20022_TC003", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132585, "stop": 1754186132585}, {"name": "  - eastnets.screening.regression.iso20022configurations.ISO20022_TC004.iso20022_TC0012", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132585, "stop": 1754186132585}, {"name": "  - eastnets.screening.regression.iso20022configurations.ISO20022_TC005.iso20022_TC0012", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132585, "stop": 1754186132585}, {"name": "  - eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC011.listManager_TC011", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132585, "stop": 1754186132585}, {"name": "  - eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC013.listManager_TC013", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132585, "stop": 1754186132585}, {"name": "  - eastnets.screening.regression.detectionmanager.DetectionManager_TC002.detectionManager_TC002", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132585, "stop": 1754186132585}, {"name": "  - eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC014.listManager_TC014", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132586, "stop": 1754186132586}, {"name": "  - eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC015.listManager_TC015", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132586, "stop": 1754186132586}, {"name": "  - eastnets.screening.regression.detectionmanager.DetectionManager_TC003.detectionManager_TC003", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132586, "stop": 1754186132586}, {"name": "  - eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC016.listManager_TC016", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132586, "stop": 1754186132586}, {"name": "  - eastnets.screening.regression.reports.Report_TC004.report_TC004", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132586, "stop": 1754186132586}, {"name": "  - eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC017.listManager_TC017", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132586, "stop": 1754186132586}, {"name": "  - eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC018.ListManager_TC018", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132586, "stop": 1754186132586}, {"name": "  - eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC019.listManager_TC019", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132586, "stop": 1754186132586}, {"name": "  - eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC020.listManager_TC020", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132586, "stop": 1754186132586}, {"name": "  - eastnets.screening.regression.reports.Report_TC006.report_TC006", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132586, "stop": 1754186132586}, {"name": "  - eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC021.listManager_TC021", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132586, "stop": 1754186132586}, {"name": "  - eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC022.listManager_TC022", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132586, "stop": 1754186132586}, {"name": "  - eastnets.screening.regression.detectionmanager.DetectionManager_TC005.detectionManager_TC005", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132586, "stop": 1754186132586}, {"name": "  - eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC023.listManager_TC023", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132586, "stop": 1754186132586}, {"name": "  - eastnets.screening.regression.listmanager.prefiltertests.PreFilter_TC001.preFilter_TC001", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132586, "stop": 1754186132586}, {"name": "  - eastnets.screening.regression.listmanager.prefiltertests.PreFilter_TC002.PreFilter_TC002", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132586, "stop": 1754186132586}, {"name": "  - eastnets.screening.regression.listmanager.prefiltertests.PreFilter_TC003.PreFilter_TC003", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132586, "stop": 1754186132586}, {"name": "  - eastnets.screening.regression.detectionmanager.DetectionManager_TC007.detectionManager_TC007", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132586, "stop": 1754186132586}, {"name": "  - eastnets.screening.regression.reports.Report_TC011.report_TC011", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132587, "stop": 1754186132587}, {"name": "  - eastnets.screening.regression.reports.Report_TC012.report_TC012", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132587, "stop": 1754186132587}, {"name": "  - eastnets.screening.regression.reports.Report_TC013.report_TC013", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132587, "stop": 1754186132587}, {"name": "  - eastnets.admin.AdminTest.checkPermissionWhenAssignSecGroupAndProfile", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132587, "stop": 1754186132587}, {"name": "", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132587, "stop": 1754186132587}, {"name": "Creating new TestNG suite for failed tests...", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132587, "stop": 1754186132587}, {"name": "Starting failed test rerun...", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132588, "stop": 1754186132588}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132616, "stop": 1754186132616}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132616, "stop": 1754186132616}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132616, "stop": 1754186132616}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132626, "stop": 1754186132626}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132626, "stop": 1754186132626}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132626, "stop": 1754186132626}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132631, "stop": 1754186132631}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132631, "stop": 1754186132631}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132631, "stop": 1754186132631}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132640, "stop": 1754186132640}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132640, "stop": 1754186132640}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132640, "stop": 1754186132640}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132640, "stop": 1754186132640}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132640, "stop": 1754186132640}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132640, "stop": 1754186132640}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132640, "stop": 1754186132640}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132666, "stop": 1754186132666}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132667, "stop": 1754186132667}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132667, "stop": 1754186132667}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132673, "stop": 1754186132673}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132673, "stop": 1754186132673}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132673, "stop": 1754186132673}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132673, "stop": 1754186132673}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132673, "stop": 1754186132673}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132673, "stop": 1754186132673}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132684, "stop": 1754186132684}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132684, "stop": 1754186132684}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132684, "stop": 1754186132684}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132684, "stop": 1754186132684}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132684, "stop": 1754186132684}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132684, "stop": 1754186132684}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132692, "stop": 1754186132692}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132692, "stop": 1754186132692}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132692, "stop": 1754186132692}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132698, "stop": 1754186132698}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132698, "stop": 1754186132698}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132698, "stop": 1754186132698}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132698, "stop": 1754186132698}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132703, "stop": 1754186132703}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132703, "stop": 1754186132703}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132703, "stop": 1754186132703}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132708, "stop": 1754186132708}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132708, "stop": 1754186132708}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132708, "stop": 1754186132708}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132714, "stop": 1754186132714}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132714, "stop": 1754186132714}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132714, "stop": 1754186132714}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132714, "stop": 1754186132714}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132718, "stop": 1754186132718}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132718, "stop": 1754186132718}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132718, "stop": 1754186132718}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132718, "stop": 1754186132718}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132723, "stop": 1754186132723}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132724, "stop": 1754186132724}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132724, "stop": 1754186132724}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132728, "stop": 1754186132728}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132729, "stop": 1754186132729}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132729, "stop": 1754186132729}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132729, "stop": 1754186132729}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132729, "stop": 1754186132729}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132734, "stop": 1754186132734}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132734, "stop": 1754186132734}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132734, "stop": 1754186132734}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132734, "stop": 1754186132734}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132738, "stop": 1754186132738}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132738, "stop": 1754186132738}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132738, "stop": 1754186132738}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132741, "stop": 1754186132741}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132741, "stop": 1754186132741}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132741, "stop": 1754186132741}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132741, "stop": 1754186132741}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132742, "stop": 1754186132742}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132742, "stop": 1754186132742}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132747, "stop": 1754186132747}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132747, "stop": 1754186132747}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132747, "stop": 1754186132747}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132747, "stop": 1754186132747}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132758, "stop": 1754186132758}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132759, "stop": 1754186132759}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132759, "stop": 1754186132759}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132759, "stop": 1754186132759}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132760, "stop": 1754186132760}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132760, "stop": 1754186132760}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132762, "stop": 1754186132762}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132763, "stop": 1754186132763}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132763, "stop": 1754186132763}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132763, "stop": 1754186132763}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132765, "stop": 1754186132765}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132766, "stop": 1754186132766}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132766, "stop": 1754186132766}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132766, "stop": 1754186132766}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132768, "stop": 1754186132768}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132768, "stop": 1754186132768}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132768, "stop": 1754186132768}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132768, "stop": 1754186132768}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132771, "stop": 1754186132771}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132771, "stop": 1754186132771}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132771, "stop": 1754186132771}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132771, "stop": 1754186132771}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132774, "stop": 1754186132774}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132774, "stop": 1754186132774}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132774, "stop": 1754186132774}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132774, "stop": 1754186132774}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132774, "stop": 1754186132774}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132774, "stop": 1754186132774}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132776, "stop": 1754186132776}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132776, "stop": 1754186132776}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132776, "stop": 1754186132776}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132776, "stop": 1754186132776}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132777, "stop": 1754186132777}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132778, "stop": 1754186132778}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132778, "stop": 1754186132778}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132778, "stop": 1754186132778}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132778, "stop": 1754186132778}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132779, "stop": 1754186132779}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132779, "stop": 1754186132779}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132779, "stop": 1754186132779}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132780, "stop": 1754186132780}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132780, "stop": 1754186132780}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132780, "stop": 1754186132780}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132782, "stop": 1754186132782}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132782, "stop": 1754186132782}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186132782, "stop": 1754186132782}], "attachments": [], "parameters": [], "start": 1754186132584, "stop": 1754193745805}], "start": 1754171885557, "stop": 1754193745805}