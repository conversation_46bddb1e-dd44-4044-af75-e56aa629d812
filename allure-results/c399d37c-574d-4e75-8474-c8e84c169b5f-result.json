{"uuid": "c399d37c-574d-4e75-8474-c8e84c169b5f", "historyId": "c0194b8bb8b0033b9601f74d1078c62e", "fullName": "eastnets.screening.regression.formatmanager.FormatManager_TC001.formatManager_TC001", "labels": [{"name": "package", "value": "eastnets.screening.regression.formatmanager.FormatManager_TC001"}, {"name": "testClass", "value": "eastnets.screening.regression.formatmanager.FormatManager_TC001"}, {"name": "testMethod", "value": "formatManager_TC001"}, {"name": "parentSuite", "value": "Failed Tests Rerun Suite"}, {"name": "suite", "value": "<PERSON><PERSON> Failed Tests"}, {"name": "subSuite", "value": "eastnets.screening.regression.formatmanager.FormatManager_TC001"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON><PERSON>"}, {"name": "tag", "value": "Regression"}], "links": [], "name": "Verify that user is able to Import, Export, Edit and Delete a format of \"Scan\" type", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to Import, Export, Edit and Delete a format of \"Scan\" type", "steps": [{"name": "response.getStatus()200", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159247074, "stop": 1754159247074}, {"name": "UTF-8", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159247074, "stop": 1754159247074}, {"name": "1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754159247074, "stop": 1754159247074}], "attachments": [], "parameters": [{"name": "arg0", "value": "Format{testCaseTitle='Verify that user is able to Import, Export, Edit and Delete a format of \"Scan\" type', name='Format_%s', zone='null', type='Scan', fieldDelimiter='Separator', separator=',', entryType='null', xpath='null', fields=[FormatField{name='FIRST_NAME_%s', type='FIRST_NAME', xpath='null', scan=false, addToContext=false}, FormatField{name='LAST_NAME_%s', type='LAST_NAME', xpath='null', scan=false, addToContext=false}]}"}], "start": 1754159185638, "stop": 1754159284033}