{"uuid": "b66aecdf-ef8e-4fb2-808a-eef9a6e600ea", "historyId": "c0c16a88d02b3d5fb122d5bc62f85540", "fullName": "eastnets.screening.regression.iso20022configurations.ISO20022_TC002.iso20022_TC0012", "labels": [{"name": "package", "value": "eastnets.screening.regression.iso20022configurations.ISO20022_TC002"}, {"name": "testClass", "value": "eastnets.screening.regression.iso20022configurations.ISO20022_TC002"}, {"name": "testMethod", "value": "iso20022_TC0012"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "ISO20022 Configurations Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.iso20022configurations.ISO20022_TC002"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-3(38)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "tag", "value": "ISO20022"}, {"name": "tag", "value": "Regression"}, {"name": "owner", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "links": [{"name": "87504", "type": "issue"}], "name": "Tfs 87504: Scan ISO message without xml tag having body fields only while <PERSON><PERSON> is added to config and no Wrapper defined.", "status": "failed", "statusDetails": {"known": false, "muted": false, "flaky": false, "message": "Test Failed due to an exception occurred in POM's method while adding ISO message expected [New message [pacs.009.001] with version [pacs.009.001.08] added successfully.] but found [This message already exist!\"]", "trace": "java.lang.AssertionError: Test Failed due to an exception occurred in POM's method while adding ISO message expected [New message [pacs.009.001] with version [pacs.009.001.08] added successfully.] but found [This message already exist!\"]\r\n\tat org.testng.Assert.fail(Assert.java:110)\r\n\tat org.testng.Assert.failNotEquals(Assert.java:1413)\r\n\tat org.testng.Assert.assertEqualsImpl(Assert.java:149)\r\n\tat org.testng.Assert.assertEquals(Assert.java:131)\r\n\tat org.testng.Assert.assertEquals(Assert.java:655)\r\n\tat core.ISOTestMethods.addISOMessage(ISOTestMethods.java:83)\r\n\tat eastnets.screening.regression.iso20022configurations.ISO20022_TC002.iso20022_TC0012(ISO20022_TC002.java:133)\r\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:578)\r\n\tat org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:677)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:221)\r\n\tat org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:50)\r\n\tat org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:969)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:194)\r\n\tat org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148)\r\n\tat org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128)\r\n\tat java.base/java.util.ArrayList.forEach(ArrayList.java:1511)\r\n\tat org.testng.TestRunner.privateRun(TestRunner.java:829)\r\n\tat org.testng.TestRunner.run(TestRunner.java:602)\r\n\tat org.testng.SuiteRunner.runTest(SuiteRunner.java:437)\r\n\tat org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:475)\r\n\tat org.testng.internal.thread.ThreadUtil.lambda$execute$0(ThreadUtil.java:58)\r\n\tat java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)\r\n\tat java.base/java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:132)\r\n\tat java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:113)\r\n\tat java.base/java.util.concurrent.AbstractExecutorService.invokeAll(AbstractExecutorService.java:274)\r\n\tat org.testng.internal.thread.ThreadUtil.execute(ThreadUtil.java:64)\r\n\tat org.testng.SuiteRunner.runInParallelTestMode(SuiteRunner.java:458)\r\n\tat org.testng.SuiteRunner.privateRun(SuiteRunner.java:389)\r\n\tat org.testng.SuiteRunner.run(SuiteRunner.java:330)\r\n\tat org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)\r\n\tat org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)\r\n\tat org.testng.TestNG.runSuitesSequentially(TestNG.java:1256)\r\n\tat org.testng.TestNG.runSuitesLocally(TestNG.java:1176)\r\n\tat org.testng.TestNG.runSuites(TestNG.java:1099)\r\n\tat org.testng.TestNG.run(TestNG.java:1067)\r\n\tat com.intellij.rt.testng.IDEARemoteTestNG.run(IDEARemoteTestNG.java:65)\r\n\tat com.intellij.rt.testng.RemoteTestNGStarter.main(RemoteTestNGStarter.java:105)\r\n"}, "stage": "finished", "description": "Tfs 87504: Scan ISO message without xml tag having body fields only while <PERSON><PERSON> is added to config and no Wrapper defined.", "steps": [{"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-578092966', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@7fde0b59, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-578092966', officialDate='null', entry=[ListEntry{type='null', name='EntryName-578092966', firstName='EntryFirstName-578092966', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-578092966', firstName='EntryFirstName-578092966', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234514143, "stop": 1754234514143}, {"name": "Search for list by listName = ListName-578092966 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234537101, "stop": 1754234537101}, {"name": "Navigate to By.xpath: //a[contains(text(),'ISO20022 Format Configuration')]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234547899, "stop": 1754234547899}, {"name": "Navigate to By.xpath: //a[contains(text(),'ISO20022 Format Configuration')]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234551876, "stop": 1754234551876}, {"name": "Create New Group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234556718, "stop": 1754234556718}, {"name": "Connect to Database and Check if User Profile = full-right-profile_07 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234567132, "stop": 1754234567132}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234567132, "stop": 1754234567132}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234567209, "stop": 1754234567209}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_07' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234567209, "stop": 1754234567209}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234567213, "stop": 1754234567213}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234567213, "stop": 1754234567213}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234567214, "stop": 1754234567214}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234567245, "stop": 1754234567245}, {"name": "Delete From tListSetProfile where profile_id in (10)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234567245, "stop": 1754234567245}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234567248, "stop": 1754234567248}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234567248, "stop": 1754234567248}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234567249, "stop": 1754234567249}, {"name": "Number of effected rows = 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234567250, "stop": 1754234567250}, {"name": "Black list already exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234573801, "stop": 1754234573801}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234596966, "stop": 1754234596966}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234596966, "stop": 1754234596966}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234658034, "stop": 1754234658034}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234658035, "stop": 1754234658035}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234658035, "stop": 1754234658035}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234658074, "stop": 1754234658074}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_07'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234658074, "stop": 1754234658074}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234658079, "stop": 1754234658079}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234658079, "stop": 1754234658079}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234658080, "stop": 1754234658080}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234676717, "stop": 1754234676717}, {"name": "Test Data = ISO20022FormatConfiguration{zone='Zone (867793082)', groupName='Group_Name-445308043', iso20022SchemaConfiguration=ISO20022SchemaConfiguration{schemaName='pacs.009.001', schemaVersion='08', headerSwift='head.001.001.01', expectedResults='Schema version [%s] successfully deleted'}, expectedResults='Successfully imported schema [pacs.009.001] with version [08].'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234681789, "stop": 1754234681789}, {"name": "Import schema.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234681789, "stop": 1754234681789}, {"name": "Navigate to By.xpath: //a[contains(text(),'ISO20022 Schema Configuration')]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234684087, "stop": 1754234684087}, {"name": "Navigate to By.xpath: //a[@href='#iso20022:homepage_business:tabViewListManager:tabView:schemaConfiguration']", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234685728, "stop": 1754234685728}, {"name": "Check if schema with Name = pacs.009.001", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234686823, "stop": 1754234686823}, {"name": "Delete schema with name = pacs.009.001", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234687303, "stop": 1754234687303}, {"name": "Validation message = Schema version [pacs.009.001.08] successfully deleted.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234694492, "stop": 1754234694492}, {"name": "Schema File Path = C:\\Users\\<USER>\\IdeaProjects\\SWS_selenium/src/test/resources/uploadsAndDownloads/uploads/ISO/pacs.009.001.08.xsd", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234694492, "stop": 1754234694492}, {"name": "Validation message = Successfully imported schema [pacs.009.001] with version [08].", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234698386, "stop": 1754234698386}, {"name": "Import schema.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234698386, "stop": 1754234698386}, {"name": "Navigate to By.xpath: //a[contains(text(),'ISO20022 Schema Configuration')]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234700166, "stop": 1754234700166}, {"name": "Navigate to By.xpath: //a[@href='#iso20022:homepage_business:tabViewListManager:tabView:schemaConfiguration']", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234700816, "stop": 1754234700816}, {"name": "Check if schema with Name = head.001.001", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234701506, "stop": 1754234701506}, {"name": "Delete schema with name = head.001.001", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234701729, "stop": 1754234701729}, {"name": "Validation message = Schema version [head.001.001.01] successfully deleted.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234703780, "stop": 1754234703780}, {"name": "Schema File Path = C:\\Users\\<USER>\\IdeaProjects\\SWS_selenium/src/test/resources/uploadsAndDownloads/uploads/ISO/head.001.001.01.xsd", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234703780, "stop": 1754234703780}, {"name": "Validation message = Successfully imported schema [head.001.001] with version [01].", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234706527, "stop": 1754234706527}, {"name": "Add new message.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234706527, "stop": 1754234706527}, {"name": "Navigate to By.xpath: //a[contains(text(),'ISO20022 Format Configuration')]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234708725, "stop": 1754234708725}, {"name": "Sort table results by creation to click on the latest created group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234712028, "stop": 1754234712028}, {"name": "Validation message = This message already exist!\"", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234749485, "stop": 1754234749485}, {"name": "Actual result = This message already exist!\"", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234749485, "stop": 1754234749485}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "ISO20022FormatDetailsConfiguration{headerField='null', bodyField='CdtTrfTxInf/PmtId/TxId', Xpath='null', bodyFieldType='null', SEPA='null', category='SKIPPED', quickLink='null', fieldLinkWith='null', iso20022FormatConfiguration$=ISO20022FormatConfiguration{zone='null', groupName='Group_Name-%s', iso20022SchemaConfiguration=ISO20022SchemaConfiguration{schemaName='head.001.001', schemaVersion='01', headerSwift='head.001.001.01', expectedResults='Schema version [%s] successfully deleted'}, expectedResults='Successfully imported schema [%s] with version [%s].'}}"}], "start": 1754234514136, "stop": 1754234749494}