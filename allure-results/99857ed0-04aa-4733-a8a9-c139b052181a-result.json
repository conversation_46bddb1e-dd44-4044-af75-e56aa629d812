{"uuid": "99857ed0-04aa-4733-a8a9-c139b052181a", "historyId": "dc11dbd33f2d79b2f543672e1979c449", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC001.scanManager_TC001", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC001"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC001"}, {"name": "testMethod", "value": "scanManager_TC001"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC001"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is able to scan a generic text format file without creating alerts automatically option checked and take decision .", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to scan a generic text format file without creating alerts automatically option checked and take decision .", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754263682240, "stop": 1754263682240}, {"name": "Validation message = File sent to the server for processing with id [3152]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754263698878, "stop": 1754263698878}, {"name": "Alert Message = File sent to the server for processing with id [3152]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754263698878, "stop": 1754263698878}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754263700590, "stop": 1754263700590}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754263710663, "stop": 1754263710663}, {"name": "Detection ID = 8272", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754263711750, "stop": 1754263711750}, {"name": "Detection Status = REP", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754263717584, "stop": 1754263717584}, {"name": "Detection Status = REP", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754263723276, "stop": 1754263723276}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "FileScan{filePath='/Generic.txt', format='Generic Text', encoding='null', enList=null, result='null', rank='null', detectVessels='true', detectCountries='true', createAlertsAutomatically='false'}"}], "start": 1754263682239, "stop": 1754263730520}