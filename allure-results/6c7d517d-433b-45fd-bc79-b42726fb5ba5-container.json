{"uuid": "6c7d517d-433b-45fd-bc79-b42726fb5ba5", "name": "Admin Test Cases", "children": ["0b175f3a-473c-467d-9ef6-8177282e89ff", "d72bcad4-d3c2-4a1a-b9b5-bb8eeb403104", "b4e1f653-5be2-428d-875f-1c9ca5871924", "dc910e06-e490-4b6b-b0a8-cfa5e30dc8d0", "2cd8b2ec-a4fc-4ab0-ae7a-d0184e1a5b36", "7fa0bace-795b-4377-921d-98394e0f8cbc", "9a470456-0315-40a5-9b84-1d1f817490f1", "516df2df-3d62-4a05-8954-8105fbb14da0", "*************-4e39-aa6f-075ce34c4713", "ff2f606a-0734-4ec7-bf4a-7f34bb33cbb7", "2762f497-f4cc-4183-a39c-ef09ca5912b7", "7e01ea59-fcda-448c-86cb-b574d27375b8", "8ad5628c-2166-409b-8bb6-906c54337955", "aa9e3104-4a20-4ca5-aae9-8073eebdb793", "d7facf90-dadc-4e54-a8b5-162c458363c7", "03e61cdc-4eae-475b-b39e-5a27a1822ced", "5b1f99a5-d112-4084-96a5-a4bb9190338f"], "befores": [{"name": "Setting up selenium web driver before each class run", "status": "passed", "stage": "finished", "description": "Setting up selenium web driver before each class run", "steps": [{"name": "Initialize Selenium driver before tests' Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149012847, "stop": 1754149012847}, {"name": "Selenium webDriver was initialized successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149018150, "stop": 1754149018150}], "attachments": [], "parameters": [], "start": 1754149012846, "stop": 1754149018150}], "afters": [{"name": "Quitting selenium driver after each class run", "status": "passed", "stage": "finished", "description": "Quitting selenium driver after each class run", "steps": [{"name": "Closing selenium WebDriver after Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153237905, "stop": 1754153237905}], "attachments": [], "parameters": [], "start": 1754153237905, "stop": 1754153238516}], "start": 1754149012846, "stop": 1754153238516}