{"uuid": "03e61cdc-4eae-475b-b39e-5a27a1822ced", "historyId": "c1c14de74bb43bbf5f8d10a287df71e", "fullName": "eastnets.admin.AdminTest.checkPermissionWhenAssignSecGroupProfileZone", "labels": [{"name": "package", "value": "eastnets.admin.AdminTest"}, {"name": "testClass", "value": "eastnets.admin.AdminTest"}, {"name": "testMethod", "value": "checkPermissionWhenAssignSecGroupProfileZone"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Admin Test Cases"}, {"name": "subSuite", "value": "eastnets.admin.AdminTest"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-1(36)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}], "links": [], "name": "Verify that the user that is assigned to Group, Profile, and Zone have permission to Release but don't have the permission to Block is able to block when this user is assigned to a second Group, Profile and Zone that have the permission to Block.", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that the user that is assigned to Group, Profile, and Zone have permission to Release but don't have the permission to Block is able to block when this user is assigned to a second Group, Profile and Zone that have the permission to Block.", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151904025, "stop": 1754151904025}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='sysadmin'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151904025, "stop": 1754151904025}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151904031, "stop": 1754151904031}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151904031, "stop": 1754151904031}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151904032, "stop": 1754151904032}, {"name": "Login with User Name = sysadmin and Password = manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151904299, "stop": 1754151904299}, {"name": "Operator test data = Operator{id=0, loginName='selenium-user-37575872', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone845099599', displayName='Zone (845099599)', description='Zone created with random number '845099599''}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151909252, "stop": 1754151909252}, {"name": "Group test data = Group{id=0, name='selenium-random-group-877574245', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-385805627', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone845099599', displayName='Zone (845099599)', description='Zone created with random number '845099599''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-37575872', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone845099599', displayName='Zone (845099599)', description='Zone created with random number '845099599''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151909252, "stop": 1754151909252}, {"name": "Create new zone .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151909252, "stop": 1754151909252}, {"name": "Zone test Data = Zone{id=0, name='Zone845099599', displayName='Zone (845099599)', description='Zone created with random number '845099599''}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151909252, "stop": 1754151909252}, {"name": "Check if zone with name = 'Zone845099599' exists", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151911252, "stop": 1754151911252}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151911618, "stop": 1754151911618}, {"name": "Enter name =Zone845099599", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151917054, "stop": 1754151917054}, {"name": "Enter display Name =Zone (845099599)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151918221, "stop": 1754151918221}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151919200, "stop": 1754151919200}, {"name": "Zone doesn't exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151919960, "stop": 1754151919960}, {"name": "Click add new zone button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151919960, "stop": 1754151919960}, {"name": "Set create new zone data.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151920240, "stop": 1754151920240}, {"name": "Set name = Zone845099599", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151920240, "stop": 1754151920240}, {"name": "Set display name = Zone (845099599)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151922371, "stop": 1754151922371}, {"name": "Set description = Zone created with random number '845099599'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151923517, "stop": 1754151923517}, {"name": "Capture zone id from UI = 137", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151925319, "stop": 1754151925319}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151925319, "stop": 1754151925319}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151926777, "stop": 1754151926777}, {"name": "Enter name =Zone845099599", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151932407, "stop": 1754151932407}, {"name": "Enter display Name =Zone (845099599)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151933798, "stop": 1754151933798}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151934698, "stop": 1754151934698}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151937755, "stop": 1754151937755}, {"name": "Enter name =Zone845099599", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151943290, "stop": 1754151943290}, {"name": "Enter display Name =Zone (845099599)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151944111, "stop": 1754151944111}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151945164, "stop": 1754151945164}, {"name": "Create first profile .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151945824, "stop": 1754151945824}, {"name": "Profile test Data = Profile{id=0, name='Test-Profile-385805627', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone845099599', displayName='Zone (845099599)', description='Zone created with random number '845099599''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151945825, "stop": 1754151945825}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151947545, "stop": 1754151947545}, {"name": "Set name = Test-Profile-385805627 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151953169, "stop": 1754151953169}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151953978, "stop": 1754151953978}, {"name": "Click add new profile button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151954802, "stop": 1754151954802}, {"name": "Set name = Test-Profile-385805627 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151955076, "stop": 1754151955076}, {"name": "Set description = Random profile for Selenium testing ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151956759, "stop": 1754151956759}, {"name": "Check enable checkbox to be true ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151959849, "stop": 1754151959849}, {"name": "Check write right checkbox to be Test-Profile-385805627 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151961320, "stop": 1754151961320}, {"name": "Select zone = Test-Profile-385805627 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151961603, "stop": 1754151961603}, {"name": "Add SWS item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151969277, "stop": 1754151969277}, {"name": "Click on the SWS item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151969277, "stop": 1754151969277}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151969895, "stop": 1754151969895}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151972149, "stop": 1754151972149}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151972611, "stop": 1754151972611}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151972719, "stop": 1754151972719}, {"name": "Set name = Test-Profile-385805627 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151978972, "stop": 1754151978972}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151980252, "stop": 1754151980252}, {"name": "Select profile from result view.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151981188, "stop": 1754151981188}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151981885, "stop": 1754151981885}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151983282, "stop": 1754151983282}, {"name": "Add en.Reporting Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151984734, "stop": 1754151984734}, {"name": "Click on the en.Reporting Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151984734, "stop": 1754151984734}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151985204, "stop": 1754151985204}, {"name": "Add SWIFT Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151986596, "stop": 1754151986596}, {"name": "Click on the SWIFT Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151986596, "stop": 1754151986596}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151987155, "stop": 1754151987155}, {"name": "Add SWP Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151988579, "stop": 1754151988579}, {"name": "Click on the SWP Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151988579, "stop": 1754151988579}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151988995, "stop": 1754151988995}, {"name": "Add Detection Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151990431, "stop": 1754151990431}, {"name": "Click on the Detection Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151990431, "stop": 1754151990431}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151990841, "stop": 1754151990841}, {"name": "Add Scan Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151992233, "stop": 1754151992233}, {"name": "Click on the Scan Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151992233, "stop": 1754151992233}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151992628, "stop": 1754151992628}, {"name": "Add Format Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151994032, "stop": 1754151994032}, {"name": "Click on the Format Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151994032, "stop": 1754151994032}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151994604, "stop": 1754151994604}, {"name": "Add Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151996079, "stop": 1754151996079}, {"name": "Click on the Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151996079, "stop": 1754151996080}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151996495, "stop": 1754151996495}, {"name": "Add Batch List Management item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151997962, "stop": 1754151997962}, {"name": "Click on the Batch List Management item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151997962, "stop": 1754151997962}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151998475, "stop": 1754151998475}, {"name": "Add ListExplorer item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151999898, "stop": 1754151999898}, {"name": "Click on the ListExplorer item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754151999898, "stop": 1754151999898}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152000285, "stop": 1754152000285}, {"name": "Add ListSetManager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152001628, "stop": 1754152001628}, {"name": "Click on the ListSetManager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152001628, "stop": 1754152001628}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152002023, "stop": 1754152002023}, {"name": "Add List Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152003642, "stop": 1754152003642}, {"name": "Click on the List Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152003642, "stop": 1754152003642}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152004528, "stop": 1754152004528}, {"name": "Add File Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152005925, "stop": 1754152005925}, {"name": "Click on the File Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152005925, "stop": 1754152005925}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152006514, "stop": 1754152006514}, {"name": "Add Advanced Settings item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152008063, "stop": 1754152008063}, {"name": "Click on the Advanced Settings item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152008063, "stop": 1754152008063}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152008519, "stop": 1754152008519}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152009882, "stop": 1754152009882}, {"name": "Accept <PERSON><PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152010331, "stop": 1754152010331}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152012488, "stop": 1754152012488}, {"name": "Select Module Rights.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152014036, "stop": 1754152014036}, {"name": "Processing module 'SWIFT Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152016037, "stop": 1754152016037}, {"name": "Click on SWIFT Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152016037, "stop": 1754152016037}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152017037, "stop": 1754152017037}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152017355, "stop": 1754152017355}, {"name": "Processing module 'Detection Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152020859, "stop": 1754152020859}, {"name": "Click on Detection Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152020859, "stop": 1754152020859}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152022586, "stop": 1754152022586}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152023240, "stop": 1754152023240}, {"name": "Processing module 'Scan Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152026062, "stop": 1754152026062}, {"name": "Click on Scan Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152026062, "stop": 1754152026062}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152027150, "stop": 1754152027150}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152027503, "stop": 1754152027503}, {"name": "Processing module 'Format Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152029833, "stop": 1754152029833}, {"name": "Click on Format Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152029833, "stop": 1754152029833}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152030866, "stop": 1754152030866}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152031125, "stop": 1754152031125}, {"name": "Processing module 'Name Checker'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152033393, "stop": 1754152033393}, {"name": "Click on Name Checker", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152033393, "stop": 1754152033393}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152034650, "stop": 1754152034650}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152035010, "stop": 1754152035010}, {"name": "Processing module 'ListExplorer'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152038490, "stop": 1754152038490}, {"name": "Click on ListExplorer", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152038490, "stop": 1754152038490}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152039626, "stop": 1754152039626}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152040310, "stop": 1754152040310}, {"name": "Processing module 'ListSetManager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152043474, "stop": 1754152043474}, {"name": "Click on ListSetManager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152043474, "stop": 1754152043474}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152063120, "stop": 1754152063120}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152063707, "stop": 1754152063707}, {"name": "Processing module 'List Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152067324, "stop": 1754152067324}, {"name": "Click on List Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152067324, "stop": 1754152067324}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152068321, "stop": 1754152068321}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152068731, "stop": 1754152068731}, {"name": "Processing module 'File Scanner'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152071405, "stop": 1754152071405}, {"name": "Click on File Scanner", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152071405, "stop": 1754152071405}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152073863, "stop": 1754152073863}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152074298, "stop": 1754152074298}, {"name": "Processing module 'Advanced Settings'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152076740, "stop": 1754152076740}, {"name": "Click on Advanced Settings", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152076741, "stop": 1754152076741}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152077933, "stop": 1754152077933}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152078236, "stop": 1754152078236}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152079678, "stop": 1754152079678}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152080050, "stop": 1754152080050}, {"name": "Check if profile exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152081951, "stop": 1754152081951}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152081951, "stop": 1754152081951}, {"name": "Set name = Test-Profile-385805627 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152087853, "stop": 1754152087853}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152088878, "stop": 1754152088878}, {"name": "Profile exist = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152089858, "stop": 1754152089858}, {"name": "Create new operator.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152089858, "stop": 1754152089858}, {"name": "Operator test Data = Operator{id=0, loginName='selenium-user-37575872', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone845099599', displayName='Zone (845099599)', description='Zone created with random number '845099599''}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152089858, "stop": 1754152089858}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152091877, "stop": 1754152091877}, {"name": "Set login name = selenium-user-37575872", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152097305, "stop": 1754152097305}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152098266, "stop": 1754152098266}, {"name": "Click Create new operator button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152099095, "stop": 1754152099095}, {"name": "Set login name = selenium-user-37575872", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152099370, "stop": 1754152099370}, {"name": "Set first name = Random", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152100948, "stop": 1754152100948}, {"name": "Set last name = User", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152101889, "stop": 1754152101889}, {"name": "Set password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152102829, "stop": 1754152102829}, {"name": "Set confirm password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152103898, "stop": 1754152103898}, {"name": "Set enable flag = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152104994, "stop": 1754152104994}, {"name": "Set reset flag = false", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152111721, "stop": 1754152111721}, {"name": "Select zone  = Zone (845099599)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152112568, "stop": 1754152112568}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152120846, "stop": 1754152120846}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152121153, "stop": 1754152121153}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152129864, "stop": 1754152129864}, {"name": "Set login name = selenium-user-37575872", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152135253, "stop": 1754152135253}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152136235, "stop": 1754152136235}, {"name": "Create first group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152137318, "stop": 1754152137318}, {"name": "Group test Data = Group{id=0, name='selenium-random-group-877574245', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-385805627', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone845099599', displayName='Zone (845099599)', description='Zone created with random number '845099599''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-37575872', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone845099599', displayName='Zone (845099599)', description='Zone created with random number '845099599''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152137318, "stop": 1754152137318}, {"name": "Connect to database to remove Link between profile = Test-Profile-385805627 and Permission Allow to change detection status to Real Violation(Block detection)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152175973, "stop": 1754152175973}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152176005, "stop": 1754152176005}, {"name": "SELECT P.NAME as PRO<PERSON><PERSON>_NAME , PR.PROFILE_ID as PROFILE_ID , PR.GRANT_ID as GRANT_ID , PR.GRANT_RIGHT_ID as GRANT_RIGHT_ID , PR.GRANT_TYPE_ID as GRANT_TYPE_ID   FROM tModuleFunctions M   LEFT JOIN tProfileRights PR On PR.GRANT_ID  = M.ID     LEFT Join tProfiles P ON P.id =PR.PROFILE_ID   WHERE DESCRIPTION ='Allow to change detection status to Real Violation(Block detection)' and P.NAME = 'Test-Profile-385805627'  and PR.GRANT_TYPE_ID =3", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152176005, "stop": 1754152176005}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152176018, "stop": 1754152176018}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152176018, "stop": 1754152176018}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152176019, "stop": 1754152176019}, {"name": "Delete function permission 'Allow to change detection status to Real Violation(Block detection)' from DataBase", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152176019, "stop": 1754152176019}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152176044, "stop": 1754152176044}, {"name": "DELETE FROM tProfileRights  WHERE GRANT_ID  ='130' and PROFILE_ID ='157' and GRANT_TYPE_ID =3", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152176044, "stop": 1754152176044}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152176058, "stop": 1754152176058}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152176058, "stop": 1754152176058}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152176058, "stop": 1754152176058}, {"name": "Create second zone .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152176059, "stop": 1754152176059}, {"name": "Zone test Data = Zone{id=0, name='Zone947484947', displayName='Zone (947484947)', description='Zone created with random number '947484947''}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152176059, "stop": 1754152176059}, {"name": "Check if zone with name = 'Zone947484947' exists", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152177985, "stop": 1754152177985}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152178447, "stop": 1754152178447}, {"name": "Enter name =Zone947484947", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152183930, "stop": 1754152183930}, {"name": "Enter display Name =Zone (947484947)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152184861, "stop": 1754152184861}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152185834, "stop": 1754152185834}, {"name": "Zone doesn't exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152186357, "stop": 1754152186357}, {"name": "Click add new zone button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152186357, "stop": 1754152186357}, {"name": "Set create new zone data.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152186688, "stop": 1754152186688}, {"name": "Set name = Zone947484947", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152186688, "stop": 1754152186688}, {"name": "Set display name = Zone (947484947)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152187958, "stop": 1754152187958}, {"name": "Set description = Zone created with random number '947484947'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152188645, "stop": 1754152188645}, {"name": "Capture zone id from UI = 138", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152189701, "stop": 1754152189701}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152189701, "stop": 1754152189701}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152190773, "stop": 1754152190773}, {"name": "Enter name =Zone947484947", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152196104, "stop": 1754152196104}, {"name": "Enter display Name =Zone (947484947)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152197808, "stop": 1754152197808}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152198645, "stop": 1754152198645}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152202234, "stop": 1754152202234}, {"name": "Enter name =Zone947484947", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152207589, "stop": 1754152207589}, {"name": "Enter display Name =Zone (947484947)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152208299, "stop": 1754152208299}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152209121, "stop": 1754152209121}, {"name": "Create second profile .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152209684, "stop": 1754152209684}, {"name": "Profile test Data = Profile{id=0, name='Test-Profile-571550970', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone947484947', displayName='Zone (947484947)', description='Zone created with random number '947484947''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152209684, "stop": 1754152209684}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152211502, "stop": 1754152211502}, {"name": "Set name = Test-Profile-571550970 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152216945, "stop": 1754152216945}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152217744, "stop": 1754152217744}, {"name": "Click add new profile button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152218709, "stop": 1754152218709}, {"name": "Set name = Test-Profile-571550970 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152218983, "stop": 1754152218983}, {"name": "Set description = Random profile for Selenium testing ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152221321, "stop": 1754152221321}, {"name": "Check enable checkbox to be true ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152222517, "stop": 1754152222517}, {"name": "Check write right checkbox to be Test-Profile-571550970 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152223578, "stop": 1754152223578}, {"name": "Select zone = Test-Profile-571550970 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152223871, "stop": 1754152223871}, {"name": "Add SWS item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152229886, "stop": 1754152229886}, {"name": "Click on the SWS item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152229886, "stop": 1754152229886}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152230825, "stop": 1754152230825}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152231857, "stop": 1754152231857}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152232337, "stop": 1754152232337}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152232452, "stop": 1754152232452}, {"name": "Set name = Test-Profile-571550970 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152238903, "stop": 1754152238903}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152239884, "stop": 1754152239884}, {"name": "Select profile from result view.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152240718, "stop": 1754152240718}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152241644, "stop": 1754152241644}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152243001, "stop": 1754152243001}, {"name": "Add en.Reporting Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152243612, "stop": 1754152243612}, {"name": "Click on the en.Reporting Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152243612, "stop": 1754152243612}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152244200, "stop": 1754152244200}, {"name": "Add SWIFT Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152245587, "stop": 1754152245587}, {"name": "Click on the SWIFT Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152245587, "stop": 1754152245587}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152246066, "stop": 1754152246066}, {"name": "Add SWP Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152247523, "stop": 1754152247523}, {"name": "Click on the SWP Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152247523, "stop": 1754152247523}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152248085, "stop": 1754152248085}, {"name": "Add Detection Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152249540, "stop": 1754152249540}, {"name": "Click on the Detection Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152249540, "stop": 1754152249540}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152250632, "stop": 1754152250632}, {"name": "Add Scan Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152252017, "stop": 1754152252017}, {"name": "Click on the Scan Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152252017, "stop": 1754152252017}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152252582, "stop": 1754152252582}, {"name": "Add Format Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152254038, "stop": 1754152254038}, {"name": "Click on the Format Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152254038, "stop": 1754152254038}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152254781, "stop": 1754152254781}, {"name": "Add Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152256293, "stop": 1754152256293}, {"name": "Click on the Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152256293, "stop": 1754152256293}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152256988, "stop": 1754152256988}, {"name": "Add Batch List Management item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152258497, "stop": 1754152258497}, {"name": "Click on the Batch List Management item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152258497, "stop": 1754152258497}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152259181, "stop": 1754152259181}, {"name": "Add ListExplorer item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152260657, "stop": 1754152260657}, {"name": "Click on the ListExplorer item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152260657, "stop": 1754152260657}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152261233, "stop": 1754152261233}, {"name": "Add ListSetManager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152262680, "stop": 1754152262680}, {"name": "Click on the ListSetManager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152262680, "stop": 1754152262680}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152263293, "stop": 1754152263293}, {"name": "Add List Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152264780, "stop": 1754152264780}, {"name": "Click on the List Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152264780, "stop": 1754152264780}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152266812, "stop": 1754152266812}, {"name": "Add File Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152277756, "stop": 1754152277756}, {"name": "Click on the File Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152277756, "stop": 1754152277756}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152278506, "stop": 1754152278506}, {"name": "Add Advanced Settings item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152279894, "stop": 1754152279894}, {"name": "Click on the Advanced Settings item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152279894, "stop": 1754152279894}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152323880, "stop": 1754152323880}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152325398, "stop": 1754152325398}, {"name": "Accept <PERSON><PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152329612, "stop": 1754152329612}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152332076, "stop": 1754152332076}, {"name": "Select Module Rights.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152333289, "stop": 1754152333289}, {"name": "Processing module 'SWIFT Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152335299, "stop": 1754152335299}, {"name": "Click on SWIFT Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152335299, "stop": 1754152335299}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152336588, "stop": 1754152336588}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152336947, "stop": 1754152336947}, {"name": "Processing module 'Detection Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152341377, "stop": 1754152341377}, {"name": "Click on Detection Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152341378, "stop": 1754152341378}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152342463, "stop": 1754152342463}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152342896, "stop": 1754152342896}, {"name": "Processing module 'Scan Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152345806, "stop": 1754152345806}, {"name": "Click on Scan Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152345806, "stop": 1754152345806}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152347657, "stop": 1754152347657}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152347921, "stop": 1754152347921}, {"name": "Processing module 'Format Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152350088, "stop": 1754152350088}, {"name": "Click on Format Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152350088, "stop": 1754152350088}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152351222, "stop": 1754152351222}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152351471, "stop": 1754152351471}, {"name": "Processing module 'Name Checker'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152353710, "stop": 1754152353710}, {"name": "Click on Name Checker", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152353710, "stop": 1754152353710}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152354862, "stop": 1754152354862}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152355140, "stop": 1754152355140}, {"name": "Processing module 'ListExplorer'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152359234, "stop": 1754152359234}, {"name": "Click on ListExplorer", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152359234, "stop": 1754152359234}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152360206, "stop": 1754152360206}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152360599, "stop": 1754152360599}, {"name": "Processing module 'ListSetManager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152363137, "stop": 1754152363137}, {"name": "Click on ListSetManager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152363137, "stop": 1754152363137}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152364583, "stop": 1754152364583}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152364852, "stop": 1754152364852}, {"name": "Processing module 'List Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152367307, "stop": 1754152367307}, {"name": "Click on List Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152367307, "stop": 1754152367307}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152368266, "stop": 1754152368266}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152368613, "stop": 1754152368613}, {"name": "Processing module 'File Scanner'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152370912, "stop": 1754152370912}, {"name": "Click on File Scanner", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152370912, "stop": 1754152370912}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152372077, "stop": 1754152372077}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152372333, "stop": 1754152372333}, {"name": "Processing module 'Advanced Settings'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152374644, "stop": 1754152374644}, {"name": "Click on Advanced Settings", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152374644, "stop": 1754152374644}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152375839, "stop": 1754152375839}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152376146, "stop": 1754152376146}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152377675, "stop": 1754152377675}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152378137, "stop": 1754152378137}, {"name": "Check if profile exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152379998, "stop": 1754152379998}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152379998, "stop": 1754152379998}, {"name": "Set name = Test-Profile-571550970 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152386989, "stop": 1754152386989}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152388870, "stop": 1754152388870}, {"name": "Profile exist = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152390134, "stop": 1754152390134}, {"name": "Create second group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152390134, "stop": 1754152390134}, {"name": "Group test Data = Group{id=0, name='selenium-random-group-902797603', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-571550970', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone947484947', displayName='Zone (947484947)', description='Zone created with random number '947484947''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-37575872', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone845099599', displayName='Zone (845099599)', description='Zone created with random number '845099599''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152390135, "stop": 1754152390135}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152439832, "stop": 1754152439832}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='selenium-user-37575872'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152439832, "stop": 1754152439832}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152439843, "stop": 1754152439843}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152439843, "stop": 1754152439843}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152439843, "stop": 1754152439843}, {"name": "Login with User Name = selenium-user-37575872 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152439980, "stop": 1754152439980}, {"name": "Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-446565828', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@6aac8b03, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-446565828', officialDate='null', entry=[ListEntry{type='null', name='EntryName-446565828', firstName='EntryFirstName-446565828', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-446565828', firstName='EntryFirstName-446565828', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152446592, "stop": 1754152446592}, {"name": "Connect to Database and Check if User Profile = Test-Profile-571550970 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152446599, "stop": 1754152446599}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152446600, "stop": 1754152446600}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152446661, "stop": 1754152446661}, {"name": "SELECT ID FROM tProfiles Where NAME ='Test-Profile-571550970' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152446661, "stop": 1754152446661}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152446667, "stop": 1754152446667}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152446667, "stop": 1754152446667}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152446668, "stop": 1754152446668}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152446694, "stop": 1754152446694}, {"name": "Delete From tListSetProfile where profile_id in (158)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152446694, "stop": 1754152446694}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152446696, "stop": 1754152446696}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152446696, "stop": 1754152446696}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152446696, "stop": 1754152446696}, {"name": "Number of effected rows = 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152446698, "stop": 1754152446698}, {"name": "Search for list by listName = ListName-446565828 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152469445, "stop": 1754152469445}, {"name": "Set zone : Zone (947484947)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152477809, "stop": 1754152477809}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152481780, "stop": 1754152481780}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152482651, "stop": 1754152482651}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152483001, "stop": 1754152483001}, {"name": "Set template name = templateName-446565828", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152483344, "stop": 1754152483344}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152485669, "stop": 1754152485669}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152486291, "stop": 1754152486291}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152487366, "stop": 1754152487366}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152487892, "stop": 1754152487892}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152505632, "stop": 1754152505632}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152505632, "stop": 1754152505632}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152554144, "stop": 1754152554144}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152554144, "stop": 1754152554144}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152554144, "stop": 1754152554144}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152554178, "stop": 1754152554178}, {"name": "SELECT ID FROM tProfiles Where NAME ='Test-Profile-571550970'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152554178, "stop": 1754152554178}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152554180, "stop": 1754152554180}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152554180, "stop": 1754152554180}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152554181, "stop": 1754152554181}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152570429, "stop": 1754152570429}, {"name": "RJE File Content= {1:F01PTSABEMMAXXX0000000001}{2:I564ACCLFRP1XXXXN}{3:{108:ENT144037000001}}{4:\r\n:16R:GENL\r\n:20C::CORP//2015033100010560\r\n:20C::SEME//0105601900000002\r\n:23G:EntryName-446565828, EntryFirstName-446565828\r\n:22F::CAEV//DVCA\r\n:22F::CAMV//MAND\r\n:98A::PREP//20150331\r\n:25D::PROC//COMP\r\n:16R:LINK\r\n:13A::LINK//564\r\n:20C::PREV//0105601900000001\r\n:16S:LINK\r\n:16S:GENL\r\n:16R:USECU\r\n:35B:ISIN DE0005190003\r\nBMW Bayerische Motoren Werke AG\r\n:16R:ACCTINFO\r\n:97A::SAFE//000000190003\r\n:93B::ELIG//UNIT/360,\r\n:93B::PEND//UNIT/360,\r\n:93B::PENR//UNIT/0,\r\n:93B::SETT//UNIT/360,\r\n:16S:ACCTINFO\r\n:16S:USECU\r\n:16R:CADETL\r\n:98A::XDTE//20150514\r\n:98A::RDTE//20150515\r\n:22F::DIVI//FINL\r\n:16S:CADETL\r\n:16R:CAOPTN\r\n:13A::CAON//001\r\n:22F::CAOP//CASH\r\n:17B::DFLT//Y\r\n:16R:CASHMOVE\r\n:22H::CRDB//CRED\r\n:98A::PAYD//20150514\r\n:92A::TAXR//26,375\r\n:92F::GRSS//EUR2,9\r\n:16S:CASHMOVE\r\n:16S:CAOPTN\r\n-}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152579616, "stop": 1754152579616}, {"name": "Start coping data from sample file to another file to be used.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152579616, "stop": 1754152579616}, {"name": "Successfully wrote to the file.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152579622, "stop": 1754152579622}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152581432, "stop": 1754152581432}, {"name": "Validation message = File sent to the server for processing with id [2929]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152597266, "stop": 1754152597266}, {"name": "Alert Message = File sent to the server for processing with id [2929]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152597266, "stop": 1754152597266}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152598361, "stop": 1754152598361}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152607369, "stop": 1754152607369}, {"name": "Detection ID = 7395", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152608471, "stop": 1754152608471}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152615963, "stop": 1754152615963}, {"name": "Check if user can block detection. ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152617303, "stop": 1754152617303}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754152622163, "stop": 1754152622163}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "Operator{id=0, loginName='null', firstName='null', lastName='null', password='null', zone=null}"}], "start": 1754151903601, "stop": 1754152629978}