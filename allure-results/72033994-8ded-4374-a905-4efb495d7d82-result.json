{"uuid": "72033994-8ded-4374-a905-4efb495d7d82", "historyId": "fb1a7c71534fc71f47eab95b765752f6", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC001.scanManager_TC001", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC001"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC001"}, {"name": "testMethod", "value": "scanManager_TC001"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC001"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is able to scan a swift rje record without creating alerts automatically option checked and take decision.", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to scan a swift rje record without creating alerts automatically option checked and take decision.", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754263598053, "stop": 1754263598053}, {"name": "Validation message = File sent to the server for processing with id [3151]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754263619385, "stop": 1754263619385}, {"name": "Alert Message = File sent to the server for processing with id [3151]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754263619385, "stop": 1754263619385}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754263621331, "stop": 1754263621331}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754263632937, "stop": 1754263632937}, {"name": "Detection ID = 8234", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754263635159, "stop": 1754263635159}, {"name": "Detection Status = REP", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754263658425, "stop": 1754263658425}, {"name": "Detection Status = REP", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754263664052, "stop": 1754263664052}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "FileScan{filePath='/RJE_Swift.txt', format='SWIFT RJE Records', encoding='null', enList=null, result='null', rank='null', detectVessels='true', detectCountries='true', createAlertsAutomatically='false'}"}], "start": 1754263598052, "stop": 1754263671695}