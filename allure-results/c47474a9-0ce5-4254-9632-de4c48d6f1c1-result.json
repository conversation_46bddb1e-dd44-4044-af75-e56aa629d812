{"uuid": "c47474a9-0ce5-4254-9632-de4c48d6f1c1", "historyId": "251b3cae75c8e8bc33d7887a97a3ae5f", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC013.scanManager_TC013", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC013"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC013"}, {"name": "testMethod", "value": "scanManager_TC013"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC013"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that Deleting a related black listed entity is not giving violations on the second scan", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that Deleting a related black listed entity is not giving violations on the second scan", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142360273, "stop": 1754142360273}, {"name": "Status for the scanned name = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142389724, "stop": 1754142389724}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142408186, "stop": 1754142408186}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142408186, "stop": 1754142408186}, {"name": "Status for the scanned name = CLEAN", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754142417824, "stop": 1754142417824}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754142360273, "stop": 1754142417824}