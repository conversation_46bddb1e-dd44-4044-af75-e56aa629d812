{"uuid": "9b1dbb73-721a-415c-8ee2-3c3b6b375307", "historyId": "39fdefff98ede705eb3c9d2af44b2414", "fullName": "eastnets.admin.AuditManagerTest.checkAuditManager", "labels": [{"name": "package", "value": "eastnets.admin.AuditManagerTest"}, {"name": "testClass", "value": "eastnets.admin.AuditManagerTest"}, {"name": "testMethod", "value": "checkAuditManager"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Admin Test Cases"}, {"name": "subSuite", "value": "eastnets.admin.AuditManagerTest"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "tag", "value": "AuditManager"}], "links": [], "name": "Verify that an event recording audit is appearing properly for the removed Groups/Users", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that an event recording audit is appearing properly for the removed Groups/Users", "steps": [{"name": "Operator test data = Operator{id=0, loginName='selenium-user-174189161', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Common_Zone_01', displayName='Common Zone 01', description='Zone for most of the tests'}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242424863, "stop": 1754242424863}, {"name": "Group test data = Group{id=0, name='selenium-random-group-179714839', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='full-right-profile', enabled=true, writeRight=true, description='Profile with full Rights', zone=Zone{id=0, name='Common_Zone_01', displayName='Common Zone 01', description='Zone for most of the tests'}, profileRights={Administration={Archive Viewer=[], Operator Manager=[ALL], Group Manager=[ALL], Profile Manager=[ALL], Report Manager=[ALL], Zone Manager=[ALL], Audit Manager=[], Event Viewer=[]}, RPTSRV={}, SWS={License Manager=[], Name Checker=[ALL], WorldCheckSettings=[ALL], Batch List Management=[], ISO20022 Format Configuration=[ALL], DowJonesSettings=[ALL], ListSetManager=[ALL], MQ Connector=[ALL], DB Scanner=[ALL], Customer Card=[ALL], Good Guys Migration=[], SAA OFCA Station=[ALL], SAA OFCS Detect=[ALL], List Manager=[ALL], SafeTrade=[ALL], en.Reporting Scanner=[ALL], SWIFT Manager=[ALL], SWP Name Checker=[ALL], File Based Archive=[ALL], Detection Manager=[ALL], Approval Configuration=[ALL], DB Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], SAA OFCS Monitor=[ALL], Archive Management=[ALL], Event Viewer=[], ISO20022-Module=[ALL], ListExplorer=[ALL], ISO20022 Schema Configuration=[ALL], MQ Manager=[ALL], Archive Viewer=[ALL], Replay=[ALL], StrippingDetectorManager=[ALL], Report Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL], GoodGuyExplorer=[ALL], SafeWatch API=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-174189161', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Common_Zone_01', displayName='Common Zone 01', description='Zone for most of the tests'}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242424869, "stop": 1754242424869}, {"name": "Create new operator.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242424869, "stop": 1754242424869}, {"name": "Operator test Data = Operator{id=0, loginName='selenium-user-174189161', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Common_Zone_01', displayName='Common Zone 01', description='Zone for most of the tests'}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242424869, "stop": 1754242424869}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242427489, "stop": 1754242427489}, {"name": "Set login name = selenium-user-174189161", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242462898, "stop": 1754242462898}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242464202, "stop": 1754242464202}, {"name": "Click Create new operator button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242465317, "stop": 1754242465317}, {"name": "Set login name = selenium-user-174189161", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242465663, "stop": 1754242465663}, {"name": "Set first name = Random", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242467872, "stop": 1754242467872}, {"name": "Set last name = User", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242469056, "stop": 1754242469056}, {"name": "Set password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242470195, "stop": 1754242470195}, {"name": "Set confirm password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242471248, "stop": 1754242471248}, {"name": "Set enable flag = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242472312, "stop": 1754242472312}, {"name": "Set reset flag = false", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242473617, "stop": 1754242473617}, {"name": "Select zone  = Common Zone 01", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242475516, "stop": 1754242475516}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242486633, "stop": 1754242486633}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242487344, "stop": 1754242487344}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242494773, "stop": 1754242494773}, {"name": "Set login name = selenium-user-174189161", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242500248, "stop": 1754242500248}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242501416, "stop": 1754242501416}, {"name": "Create first group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242504052, "stop": 1754242504052}, {"name": "Group test Data = Group{id=0, name='selenium-random-group-179714839', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='full-right-profile', enabled=true, writeRight=true, description='Profile with full Rights', zone=Zone{id=0, name='Common_Zone_01', displayName='Common Zone 01', description='Zone for most of the tests'}, profileRights={Administration={Archive Viewer=[], Operator Manager=[ALL], Group Manager=[ALL], Profile Manager=[ALL], Report Manager=[ALL], Zone Manager=[ALL], Audit Manager=[], Event Viewer=[]}, RPTSRV={}, SWS={License Manager=[], Name Checker=[ALL], WorldCheckSettings=[ALL], Batch List Management=[], ISO20022 Format Configuration=[ALL], DowJonesSettings=[ALL], ListSetManager=[ALL], MQ Connector=[ALL], DB Scanner=[ALL], Customer Card=[ALL], Good Guys Migration=[], SAA OFCA Station=[ALL], SAA OFCS Detect=[ALL], List Manager=[ALL], SafeTrade=[ALL], en.Reporting Scanner=[ALL], SWIFT Manager=[ALL], SWP Name Checker=[ALL], File Based Archive=[ALL], Detection Manager=[ALL], Approval Configuration=[ALL], DB Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], SAA OFCS Monitor=[ALL], Archive Management=[ALL], Event Viewer=[], ISO20022-Module=[ALL], ListExplorer=[ALL], ISO20022 Schema Configuration=[ALL], MQ Manager=[ALL], Archive Viewer=[ALL], Replay=[ALL], StrippingDetectorManager=[ALL], Report Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL], GoodGuyExplorer=[ALL], SafeWatch API=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-174189161', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Common_Zone_01', displayName='Common Zone 01', description='Zone for most of the tests'}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242504052, "stop": 1754242504052}, {"name": "Remove operator with login name = selenium-user-174189161.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242557944, "stop": 1754242557944}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242559605, "stop": 1754242559605}, {"name": "Set login name = selenium-user-174189161", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242565217, "stop": 1754242565217}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242566563, "stop": 1754242566563}, {"name": "Select operator and click remove button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242567500, "stop": 1754242567500}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242571261, "stop": 1754242571261}, {"name": "Set login name = selenium-user-174189161", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242576871, "stop": 1754242576871}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242577916, "stop": 1754242577916}, {"name": "Remove group with name = selenium-random-group-179714839.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242580270, "stop": 1754242580270}, {"name": "Select group and click remove button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242589820, "stop": 1754242589820}, {"name": "Asserting on data in result view", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242680499, "stop": 1754242680499}, {"name": "<PERSON><PERSON><PERSON> Passed Successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242680502, "stop": 1754242680502}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754242424844, "stop": 1754242680502}