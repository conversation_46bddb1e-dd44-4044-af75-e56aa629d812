{"uuid": "d996147d-b39d-46f4-8492-9edac14263ea", "historyId": "a75a0474176d63c3db078d1abecfe33b", "fullName": "eastnets.admin.AdminTest.checkPermissionWhenAssignSecGroupAndProfile", "labels": [{"name": "package", "value": "eastnets.admin.AdminTest"}, {"name": "testClass", "value": "eastnets.admin.AdminTest"}, {"name": "testMethod", "value": "checkPermissionWhenAssignSecGroupAndProfile"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Admin Test Cases"}, {"name": "subSuite", "value": "eastnets.admin.AdminTest"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-1(36)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}], "links": [], "name": "Verify that the user that is assigned to Group and Profile have permission to Release but don't have the permission to Block is able to block when this user is assigned to a second Group and Profile that have the permission to Block.", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that the user that is assigned to Group and Profile have permission to Release but don't have the permission to Block is able to block when this user is assigned to a second Group and Profile that have the permission to Block.", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181587695, "stop": 1754181587695}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='sysadmin'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181587695, "stop": 1754181587695}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181587704, "stop": 1754181587704}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181587704, "stop": 1754181587704}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181587705, "stop": 1754181587705}, {"name": "Login with User Name = sysadmin and Password = manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181587903, "stop": 1754181587903}, {"name": "Operator test data = Operator{id=0, loginName='selenium-user-632886178', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone402596277', displayName='Zone (402596277)', description='Zone created with random number '402596277''}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181593411, "stop": 1754181593411}, {"name": "Group test data = Group{id=0, name='selenium-random-group-907964702', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-587065061', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone402596277', displayName='Zone (402596277)', description='Zone created with random number '402596277''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-632886178', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone402596277', displayName='Zone (402596277)', description='Zone created with random number '402596277''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181593412, "stop": 1754181593412}, {"name": "Create new zone .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181593412, "stop": 1754181593412}, {"name": "Zone test Data = Zone{id=0, name='Zone402596277', displayName='Zone (402596277)', description='Zone created with random number '402596277''}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181593412, "stop": 1754181593412}, {"name": "Check if zone with name = 'Zone402596277' exists", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181595582, "stop": 1754181595582}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181595969, "stop": 1754181595969}, {"name": "Enter name =Zone402596277", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181601434, "stop": 1754181601434}, {"name": "Enter display Name =Zone (402596277)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181602189, "stop": 1754181602189}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181603320, "stop": 1754181603320}, {"name": "Zone doesn't exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181604189, "stop": 1754181604189}, {"name": "Click add new zone button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181604189, "stop": 1754181604189}, {"name": "Set create new zone data.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181604644, "stop": 1754181604644}, {"name": "Set name = Zone402596277", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181604644, "stop": 1754181604644}, {"name": "Set display name = Zone (402596277)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181606851, "stop": 1754181606851}, {"name": "Set description = Zone created with random number '402596277'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181608150, "stop": 1754181608150}, {"name": "Capture zone id from UI = 142", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181610325, "stop": 1754181610325}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181610325, "stop": 1754181610325}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181612027, "stop": 1754181612027}, {"name": "Enter name =Zone402596277", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181617677, "stop": 1754181617677}, {"name": "Enter display Name =Zone (402596277)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181618539, "stop": 1754181618539}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181619701, "stop": 1754181619701}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181623423, "stop": 1754181623423}, {"name": "Enter name =Zone402596277", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181628810, "stop": 1754181628810}, {"name": "Enter display Name =Zone (402596277)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181629718, "stop": 1754181629718}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181630773, "stop": 1754181630774}, {"name": "Create first profile .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181631589, "stop": 1754181631589}, {"name": "Profile test Data = Profile{id=0, name='Test-Profile-587065061', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone402596277', displayName='Zone (402596277)', description='Zone created with random number '402596277''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181631589, "stop": 1754181631589}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181633258, "stop": 1754181633258}, {"name": "Set name = Test-Profile-587065061 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181638794, "stop": 1754181638794}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181640887, "stop": 1754181640887}, {"name": "Click add new profile button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181641940, "stop": 1754181641940}, {"name": "Set name = Test-Profile-587065061 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181642312, "stop": 1754181642312}, {"name": "Set description = Random profile for Selenium testing ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181644325, "stop": 1754181644325}, {"name": "Check enable checkbox to be true ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181647552, "stop": 1754181647552}, {"name": "Check write right checkbox to be Test-Profile-587065061 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181648429, "stop": 1754181648429}, {"name": "Select zone = Test-Profile-587065061 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181648746, "stop": 1754181648746}, {"name": "Add SWS item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181655962, "stop": 1754181655962}, {"name": "Click on the SWS item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181655963, "stop": 1754181655963}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181656728, "stop": 1754181656728}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181657917, "stop": 1754181657917}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181658319, "stop": 1754181658319}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181658404, "stop": 1754181658404}, {"name": "Set name = Test-Profile-587065061 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181665232, "stop": 1754181665232}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181666641, "stop": 1754181666641}, {"name": "Select profile from result view.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181667577, "stop": 1754181667577}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181668652, "stop": 1754181668652}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181670438, "stop": 1754181670438}, {"name": "Add en.Reporting Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181671436, "stop": 1754181671436}, {"name": "Click on the en.Reporting Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181671436, "stop": 1754181671436}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181672096, "stop": 1754181672096}, {"name": "Add SWIFT Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181673539, "stop": 1754181673539}, {"name": "Click on the SWIFT Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181673539, "stop": 1754181673539}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181674210, "stop": 1754181674210}, {"name": "Add SWP Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181675661, "stop": 1754181675661}, {"name": "Click on the SWP Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181675661, "stop": 1754181675661}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181676443, "stop": 1754181676443}, {"name": "Add Detection Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181730309, "stop": 1754181730309}, {"name": "Click on the Detection Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181730309, "stop": 1754181730309}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181730991, "stop": 1754181730992}, {"name": "Add Scan Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181732475, "stop": 1754181732475}, {"name": "Click on the Scan Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181732475, "stop": 1754181732475}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181738156, "stop": 1754181738156}, {"name": "Add Format Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181739205, "stop": 1754181739205}, {"name": "Click on the Format Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181739205, "stop": 1754181739205}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181739867, "stop": 1754181739867}, {"name": "Add Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181741302, "stop": 1754181741302}, {"name": "Click on the Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181741302, "stop": 1754181741302}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181742098, "stop": 1754181742098}, {"name": "Add Batch List Management item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181743588, "stop": 1754181743588}, {"name": "Click on the Batch List Management item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181743588, "stop": 1754181743588}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181744292, "stop": 1754181744292}, {"name": "Add ListExplorer item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181745815, "stop": 1754181745815}, {"name": "Click on the ListExplorer item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181745815, "stop": 1754181745815}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181746520, "stop": 1754181746520}, {"name": "Add ListSetManager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181747955, "stop": 1754181747955}, {"name": "Click on the ListSetManager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181747955, "stop": 1754181747955}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181748536, "stop": 1754181748536}, {"name": "Add List Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181749997, "stop": 1754181749997}, {"name": "Click on the List Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181749997, "stop": 1754181749997}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181750445, "stop": 1754181750445}, {"name": "Add File Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181751919, "stop": 1754181751919}, {"name": "Click on the File Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181751919, "stop": 1754181751919}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181752491, "stop": 1754181752491}, {"name": "Add Advanced Settings item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181753918, "stop": 1754181753918}, {"name": "Click on the Advanced Settings item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181753918, "stop": 1754181753918}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181754591, "stop": 1754181754591}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181756098, "stop": 1754181756098}, {"name": "Accept <PERSON><PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181756992, "stop": 1754181756993}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181760033, "stop": 1754181760033}, {"name": "Select Module Rights.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181761065, "stop": 1754181761065}, {"name": "Processing module 'SWIFT Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181763066, "stop": 1754181763066}, {"name": "Click on SWIFT Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181763066, "stop": 1754181763066}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181764153, "stop": 1754181764153}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181764547, "stop": 1754181764547}, {"name": "Processing module 'Detection Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181768383, "stop": 1754181768383}, {"name": "Click on Detection Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181768383, "stop": 1754181768383}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181769765, "stop": 1754181769765}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181770748, "stop": 1754181770748}, {"name": "Processing module 'Scan Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181773392, "stop": 1754181773392}, {"name": "Click on Scan Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181773392, "stop": 1754181773392}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181774577, "stop": 1754181774577}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181775534, "stop": 1754181775534}, {"name": "Processing module 'Format Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181778702, "stop": 1754181778702}, {"name": "Click on Format Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181778702, "stop": 1754181778702}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181780972, "stop": 1754181780972}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181781422, "stop": 1754181781422}, {"name": "Processing module 'Name Checker'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181784261, "stop": 1754181784261}, {"name": "Click on Name Checker", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181784261, "stop": 1754181784261}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181785349, "stop": 1754181785349}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181785714, "stop": 1754181785714}, {"name": "Processing module 'ListExplorer'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181789543, "stop": 1754181789543}, {"name": "Click on ListExplorer", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181789543, "stop": 1754181789543}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181790852, "stop": 1754181790852}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181791410, "stop": 1754181791410}, {"name": "Processing module 'ListSetManager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181795023, "stop": 1754181795023}, {"name": "Click on ListSetManager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181795023, "stop": 1754181795023}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181796611, "stop": 1754181796611}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181797030, "stop": 1754181797030}, {"name": "Processing module 'List Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181799435, "stop": 1754181799435}, {"name": "Click on List Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181799435, "stop": 1754181799435}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181800595, "stop": 1754181800595}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181800852, "stop": 1754181800852}, {"name": "Processing module 'File Scanner'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181803182, "stop": 1754181803182}, {"name": "Click on File Scanner", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181803182, "stop": 1754181803182}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181804345, "stop": 1754181804345}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181804633, "stop": 1754181804634}, {"name": "Processing module 'Advanced Settings'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181807098, "stop": 1754181807098}, {"name": "Click on Advanced Settings", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181807098, "stop": 1754181807098}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181808474, "stop": 1754181808474}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181809105, "stop": 1754181809105}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181811211, "stop": 1754181811211}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181811602, "stop": 1754181811602}, {"name": "Check if profile exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181813509, "stop": 1754181813509}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181813509, "stop": 1754181813509}, {"name": "Set name = Test-Profile-587065061 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181819416, "stop": 1754181819416}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181821759, "stop": 1754181821760}, {"name": "Profile exist = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181823019, "stop": 1754181823019}, {"name": "Create new operator.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181823019, "stop": 1754181823019}, {"name": "Operator test Data = Operator{id=0, loginName='selenium-user-632886178', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone402596277', displayName='Zone (402596277)', description='Zone created with random number '402596277''}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181823019, "stop": 1754181823019}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181825764, "stop": 1754181825764}, {"name": "Set login name = selenium-user-632886178", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181831150, "stop": 1754181831150}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181832121, "stop": 1754181832121}, {"name": "Click Create new operator button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181833400, "stop": 1754181833400}, {"name": "Set login name = selenium-user-632886178", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181833769, "stop": 1754181833769}, {"name": "Set first name = Random", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181835867, "stop": 1754181835867}, {"name": "Set last name = User", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181837129, "stop": 1754181837129}, {"name": "Set password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181838714, "stop": 1754181838714}, {"name": "Set confirm password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181840066, "stop": 1754181840066}, {"name": "Set enable flag = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181841106, "stop": 1754181841106}, {"name": "Set reset flag = false", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181842376, "stop": 1754181842376}, {"name": "Select zone  = Zone (402596277)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181844379, "stop": 1754181844379}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181851781, "stop": 1754181851781}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181852230, "stop": 1754181852230}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181861114, "stop": 1754181861114}, {"name": "Set login name = selenium-user-632886178", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181866675, "stop": 1754181866675}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181867552, "stop": 1754181867552}, {"name": "Create first group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181868402, "stop": 1754181868402}, {"name": "Group test Data = Group{id=0, name='selenium-random-group-907964702', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-587065061', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone402596277', displayName='Zone (402596277)', description='Zone created with random number '402596277''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-632886178', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone402596277', displayName='Zone (402596277)', description='Zone created with random number '402596277''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181868402, "stop": 1754181868402}, {"name": "Connect to database to remove Link between profile = Test-Profile-587065061 and Permission Allow to change detection status to Real Violation(Block detection)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181940821, "stop": 1754181940821}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181940849, "stop": 1754181940849}, {"name": "SELECT P.NAME as PRO<PERSON><PERSON>_NAME , PR.PROFILE_ID as PROFILE_ID , PR.GRANT_ID as GRANT_ID , PR.GRANT_RIGHT_ID as GRANT_RIGHT_ID , PR.GRANT_TYPE_ID as GRANT_TYPE_ID   FROM tModuleFunctions M   LEFT JOIN tProfileRights PR On PR.GRANT_ID  = M.ID     LEFT Join tProfiles P ON P.id =PR.PROFILE_ID   WHERE DESCRIPTION ='Allow to change detection status to Real Violation(Block detection)' and P.NAME = 'Test-Profile-587065061'  and PR.GRANT_TYPE_ID =3", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181940849, "stop": 1754181940849}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181940868, "stop": 1754181940868}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181940868, "stop": 1754181940868}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181940868, "stop": 1754181940868}, {"name": "Delete function permission 'Allow to change detection status to Real Violation(Block detection)' from DataBase", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181940868, "stop": 1754181940868}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181940887, "stop": 1754181940887}, {"name": "DELETE FROM tProfileRights  WHERE GRANT_ID  ='130' and PROFILE_ID ='162' and GRANT_TYPE_ID =3", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181940888, "stop": 1754181940888}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181940907, "stop": 1754181940907}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181940907, "stop": 1754181940907}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181940907, "stop": 1754181940907}, {"name": "Create second profile .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181940908, "stop": 1754181940908}, {"name": "Profile test Data = Profile{id=0, name='Test-Profile-372991885', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone402596277', displayName='Zone (402596277)', description='Zone created with random number '402596277''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181940909, "stop": 1754181940909}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181942619, "stop": 1754181942619}, {"name": "Set name = Test-Profile-372991885 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181948199, "stop": 1754181948199}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181949505, "stop": 1754181949505}, {"name": "Click add new profile button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181950555, "stop": 1754181950555}, {"name": "Set name = Test-Profile-372991885 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181950934, "stop": 1754181950934}, {"name": "Set description = Random profile for Selenium testing ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181952964, "stop": 1754181952964}, {"name": "Check enable checkbox to be true ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181954115, "stop": 1754181954115}, {"name": "Check write right checkbox to be Test-Profile-372991885 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181955131, "stop": 1754181955131}, {"name": "Select zone = Test-Profile-372991885 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181955654, "stop": 1754181955654}, {"name": "Add SWS item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181965066, "stop": 1754181965066}, {"name": "Click on the SWS item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181965066, "stop": 1754181965066}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181965724, "stop": 1754181965724}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181966932, "stop": 1754181966932}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181967369, "stop": 1754181967369}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181967452, "stop": 1754181967452}, {"name": "Set name = Test-Profile-372991885 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181974248, "stop": 1754181974248}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181975587, "stop": 1754181975587}, {"name": "Select profile from result view.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181976816, "stop": 1754181976816}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181977727, "stop": 1754181977727}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181979442, "stop": 1754181979442}, {"name": "Add en.Reporting Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181981277, "stop": 1754181981277}, {"name": "Click on the en.Reporting Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181981277, "stop": 1754181981277}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181982149, "stop": 1754181982149}, {"name": "Add SWIFT Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181983529, "stop": 1754181983529}, {"name": "Click on the SWIFT Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181983529, "stop": 1754181983529}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181983997, "stop": 1754181983997}, {"name": "Add SWP Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181985437, "stop": 1754181985437}, {"name": "Click on the SWP Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181985437, "stop": 1754181985437}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181985944, "stop": 1754181985944}, {"name": "Add Detection Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181987403, "stop": 1754181987403}, {"name": "Click on the Detection Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181987404, "stop": 1754181987404}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181988256, "stop": 1754181988256}, {"name": "Add Scan Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181989717, "stop": 1754181989717}, {"name": "Click on the Scan Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181989717, "stop": 1754181989717}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181990264, "stop": 1754181990264}, {"name": "Add Format Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181991792, "stop": 1754181991792}, {"name": "Click on the Format Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181991792, "stop": 1754181991792}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181992367, "stop": 1754181992367}, {"name": "Add Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181993767, "stop": 1754181993767}, {"name": "Click on the Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181993767, "stop": 1754181993767}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181994277, "stop": 1754181994277}, {"name": "Add Batch List Management item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181996239, "stop": 1754181996239}, {"name": "Click on the Batch List Management item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181996239, "stop": 1754181996239}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181996885, "stop": 1754181996885}, {"name": "Add ListExplorer item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181998283, "stop": 1754181998283}, {"name": "Click on the ListExplorer item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181998283, "stop": 1754181998283}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181998831, "stop": 1754181998831}, {"name": "Add ListSetManager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182000255, "stop": 1754182000255}, {"name": "Click on the ListSetManager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182000255, "stop": 1754182000255}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182000896, "stop": 1754182000896}, {"name": "Add List Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182002312, "stop": 1754182002312}, {"name": "Click on the List Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182002312, "stop": 1754182002312}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182002987, "stop": 1754182002987}, {"name": "Add File Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182004505, "stop": 1754182004505}, {"name": "Click on the File Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182004505, "stop": 1754182004505}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182005271, "stop": 1754182005271}, {"name": "Add Advanced Settings item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182008002, "stop": 1754182008002}, {"name": "Click on the Advanced Settings item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182008002, "stop": 1754182008002}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182008624, "stop": 1754182008624}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182010028, "stop": 1754182010028}, {"name": "Accept <PERSON><PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182010735, "stop": 1754182010735}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182013040, "stop": 1754182013040}, {"name": "Select Module Rights.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182013900, "stop": 1754182013900}, {"name": "Processing module 'SWIFT Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182015902, "stop": 1754182015902}, {"name": "Click on SWIFT Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182015902, "stop": 1754182015902}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182017222, "stop": 1754182017222}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182017615, "stop": 1754182017615}, {"name": "Processing module 'Detection Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182021100, "stop": 1754182021100}, {"name": "Click on Detection Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182021100, "stop": 1754182021100}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182022154, "stop": 1754182022154}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182022696, "stop": 1754182022696}, {"name": "Processing module 'Scan Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182025572, "stop": 1754182025572}, {"name": "Click on Scan Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182025576, "stop": 1754182025576}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182026704, "stop": 1754182026704}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182086145, "stop": 1754182086145}, {"name": "Processing module 'Format Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182088723, "stop": 1754182088723}, {"name": "Click on Format Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182088727, "stop": 1754182088727}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182089869, "stop": 1754182089869}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182091544, "stop": 1754182091545}, {"name": "Processing module 'Name Checker'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182094479, "stop": 1754182094479}, {"name": "Click on Name Checker", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182094481, "stop": 1754182094481}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182096785, "stop": 1754182096785}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182097294, "stop": 1754182097294}, {"name": "Processing module 'ListExplorer'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182101930, "stop": 1754182101930}, {"name": "Click on ListExplorer", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182101930, "stop": 1754182101930}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182102987, "stop": 1754182102987}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182103412, "stop": 1754182103412}, {"name": "Processing module 'ListSetManager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182105806, "stop": 1754182105806}, {"name": "Click on ListSetManager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182105806, "stop": 1754182105806}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182107446, "stop": 1754182107446}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182107847, "stop": 1754182107847}, {"name": "Processing module 'List Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182110553, "stop": 1754182110553}, {"name": "Click on List Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182110554, "stop": 1754182110554}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182111845, "stop": 1754182111845}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182112378, "stop": 1754182112378}, {"name": "Processing module 'File Scanner'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182115030, "stop": 1754182115030}, {"name": "Click on File Scanner", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182115032, "stop": 1754182115032}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182116296, "stop": 1754182116296}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182117238, "stop": 1754182117238}, {"name": "Processing module 'Advanced Settings'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182120331, "stop": 1754182120331}, {"name": "Click on Advanced Settings", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182120331, "stop": 1754182120331}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182121878, "stop": 1754182121878}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182122323, "stop": 1754182122323}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182123750, "stop": 1754182123750}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182124110, "stop": 1754182124110}, {"name": "Check if profile exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182126051, "stop": 1754182126051}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182126051, "stop": 1754182126051}, {"name": "Set name = Test-Profile-372991885 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182132260, "stop": 1754182132260}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182133449, "stop": 1754182133449}, {"name": "Profile exist = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182134712, "stop": 1754182134712}, {"name": "Create second group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182134712, "stop": 1754182134712}, {"name": "Group test Data = Group{id=0, name='selenium-random-group-865955671', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-372991885', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone402596277', displayName='Zone (402596277)', description='Zone created with random number '402596277''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-632886178', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone402596277', displayName='Zone (402596277)', description='Zone created with random number '402596277''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182134712, "stop": 1754182134712}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182183143, "stop": 1754182183143}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='selenium-user-632886178'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182183143, "stop": 1754182183143}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182183150, "stop": 1754182183150}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182183150, "stop": 1754182183150}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182183151, "stop": 1754182183151}, {"name": "Login with User Name = selenium-user-632886178 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182183320, "stop": 1754182183320}, {"name": "Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-508377405', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@15482a08, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-508377405', officialDate='null', entry=[ListEntry{type='null', name='EntryName-508377405', firstName='EntryFirstName-508377405', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-508377405', firstName='EntryFirstName-508377405', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182189939, "stop": 1754182189939}, {"name": "Connect to Database and Check if User Profile = Test-Profile-372991885 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182189940, "stop": 1754182189940}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182189941, "stop": 1754182189941}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182189984, "stop": 1754182189984}, {"name": "SELECT ID FROM tProfiles Where NAME ='Test-Profile-372991885' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182189984, "stop": 1754182189984}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182189989, "stop": 1754182189989}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182189990, "stop": 1754182189990}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182189990, "stop": 1754182189990}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182190017, "stop": 1754182190017}, {"name": "Delete From tListSetProfile where profile_id in (163)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182190017, "stop": 1754182190017}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182190019, "stop": 1754182190019}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182190019, "stop": 1754182190019}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182190020, "stop": 1754182190020}, {"name": "Number of effected rows = 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182190020, "stop": 1754182190020}, {"name": "Search for list by listName = ListName-508377405 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182223275, "stop": 1754182223275}, {"name": "Set zone : Zone (402596277)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182236700, "stop": 1754182236700}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182239783, "stop": 1754182239783}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182240691, "stop": 1754182240691}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182241275, "stop": 1754182241275}, {"name": "Set template name = templateName-508377405", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182241556, "stop": 1754182241556}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182244320, "stop": 1754182244320}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182245415, "stop": 1754182245415}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182247022, "stop": 1754182247022}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182247969, "stop": 1754182247969}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182274809, "stop": 1754182274809}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182274809, "stop": 1754182274809}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182359281, "stop": 1754182359281}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182359282, "stop": 1754182359282}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182359282, "stop": 1754182359282}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182359326, "stop": 1754182359326}, {"name": "SELECT ID FROM tProfiles Where NAME ='Test-Profile-372991885'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182359326, "stop": 1754182359326}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182359331, "stop": 1754182359331}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182359331, "stop": 1754182359331}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182359331, "stop": 1754182359331}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182376097, "stop": 1754182376097}, {"name": "RJE File Content= {1:F01PTSABEMMAXXX0000000001}{2:I564ACCLFRP1XXXXN}{3:{108:ENT144037000001}}{4:\r\n:16R:GENL\r\n:20C::CORP//2015033100010560\r\n:20C::SEME//0105601900000002\r\n:23G:EntryName-508377405, EntryFirstName-508377405\r\n:22F::CAEV//DVCA\r\n:22F::CAMV//MAND\r\n:98A::PREP//20150331\r\n:25D::PROC//COMP\r\n:16R:LINK\r\n:13A::LINK//564\r\n:20C::PREV//0105601900000001\r\n:16S:LINK\r\n:16S:GENL\r\n:16R:USECU\r\n:35B:ISIN DE0005190003\r\nBMW Bayerische Motoren Werke AG\r\n:16R:ACCTINFO\r\n:97A::SAFE//000000190003\r\n:93B::ELIG//UNIT/360,\r\n:93B::PEND//UNIT/360,\r\n:93B::PENR//UNIT/0,\r\n:93B::SETT//UNIT/360,\r\n:16S:ACCTINFO\r\n:16S:USECU\r\n:16R:CADETL\r\n:98A::XDTE//20150514\r\n:98A::RDTE//20150515\r\n:22F::DIVI//FINL\r\n:16S:CADETL\r\n:16R:CAOPTN\r\n:13A::CAON//001\r\n:22F::CAOP//CASH\r\n:17B::DFLT//Y\r\n:16R:CASHMOVE\r\n:22H::CRDB//CRED\r\n:98A::PAYD//20150514\r\n:92A::TAXR//26,375\r\n:92F::GRSS//EUR2,9\r\n:16S:CASHMOVE\r\n:16S:CAOPTN\r\n-}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182386073, "stop": 1754182386073}, {"name": "Start coping data from sample file to another file to be used.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182386073, "stop": 1754182386073}, {"name": "Successfully wrote to the file.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182386076, "stop": 1754182386076}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182388007, "stop": 1754182388007}, {"name": "Validation message = File sent to the server for processing with id [3023]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182411135, "stop": 1754182411135}, {"name": "Alert Message = File sent to the server for processing with id [3023]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182411135, "stop": 1754182411135}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182412474, "stop": 1754182412474}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182422218, "stop": 1754182422218}, {"name": "Detection ID = 7657", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182423354, "stop": 1754182423354}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182429175, "stop": 1754182429175}, {"name": "Check if user can block detection. ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182430811, "stop": 1754182430811}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182461630, "stop": 1754182461630}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "Operator{id=0, loginName='null', firstName='null', lastName='null', password='null', zone=null}"}], "start": 1754181587240, "stop": 1754182474728}