{"uuid": "e21f5ef6-b260-4a53-91f8-d64621b274d4", "historyId": "823f26a334db1a4c0b3f993cd20cf2c9", "fullName": "eastnets.admin.AdminTest.checkPermissionWhenAssignSecGroupAndProfile", "labels": [{"name": "package", "value": "eastnets.admin.AdminTest"}, {"name": "testClass", "value": "eastnets.admin.AdminTest"}, {"name": "testMethod", "value": "checkPermissionWhenAssignSecGroupAndProfile"}, {"name": "parentSuite", "value": "Failed Tests Rerun Suite"}, {"name": "suite", "value": "<PERSON><PERSON> Failed Tests"}, {"name": "subSuite", "value": "eastnets.admin.AdminTest"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}], "links": [], "name": "Verify that the user that is assigned to Group and Profile have permission to Release but don't have the permission to Block is able to block when this user is assigned to a second Group and Profile that have the permission to Block.", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that the user that is assigned to Group and Profile have permission to Release but don't have the permission to Block is able to block when this user is assigned to a second Group and Profile that have the permission to Block.", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186879396, "stop": 1754186879396}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='sysadmin'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186879396, "stop": 1754186879396}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186879402, "stop": 1754186879402}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186879402, "stop": 1754186879402}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186879403, "stop": 1754186879403}, {"name": "Login with User Name = sysadmin and Password = manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186879550, "stop": 1754186879550}, {"name": "Operator test data = Operator{id=0, loginName='selenium-user-527944822', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone48603045', displayName='Zone (48603045)', description='Zone created with random number '48603045''}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186884220, "stop": 1754186884220}, {"name": "Group test data = Group{id=0, name='selenium-random-group-253121846', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-232441602', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone48603045', displayName='Zone (48603045)', description='Zone created with random number '48603045''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-527944822', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone48603045', displayName='Zone (48603045)', description='Zone created with random number '48603045''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186884220, "stop": 1754186884220}, {"name": "Create new zone .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186884220, "stop": 1754186884220}, {"name": "Zone test Data = Zone{id=0, name='Zone48603045', displayName='Zone (48603045)', description='Zone created with random number '48603045''}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186884220, "stop": 1754186884220}, {"name": "Check if zone with name = 'Zone48603045' exists", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186886092, "stop": 1754186886092}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186886431, "stop": 1754186886431}, {"name": "Enter name =Zone48603045", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186891762, "stop": 1754186891762}, {"name": "Enter display Name =Zone (48603045)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186892533, "stop": 1754186892533}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186893281, "stop": 1754186893281}, {"name": "Zone doesn't exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186893871, "stop": 1754186893871}, {"name": "Click add new zone button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186893871, "stop": 1754186893871}, {"name": "Set create new zone data.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186894170, "stop": 1754186894170}, {"name": "Set name = Zone48603045", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186894170, "stop": 1754186894170}, {"name": "Set display name = Zone (48603045)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186895582, "stop": 1754186895582}, {"name": "Set description = Zone created with random number '48603045'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186896353, "stop": 1754186896353}, {"name": "Capture zone id from UI = 149", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186897570, "stop": 1754186897570}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186897570, "stop": 1754186897570}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186898763, "stop": 1754186898763}, {"name": "Enter name =Zone48603045", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186904145, "stop": 1754186904145}, {"name": "Enter display Name =Zone (48603045)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186904771, "stop": 1754186904771}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186905417, "stop": 1754186905417}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186907931, "stop": 1754186907931}, {"name": "Enter name =Zone48603045", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186913350, "stop": 1754186913350}, {"name": "Enter display Name =Zone (48603045)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186914082, "stop": 1754186914082}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186914781, "stop": 1754186914781}, {"name": "Create first profile .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186915356, "stop": 1754186915356}, {"name": "Profile test Data = Profile{id=0, name='Test-Profile-232441602', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone48603045', displayName='Zone (48603045)', description='Zone created with random number '48603045''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186915356, "stop": 1754186915356}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186916596, "stop": 1754186916596}, {"name": "Set name = Test-Profile-232441602 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186921986, "stop": 1754186921986}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186922850, "stop": 1754186922850}, {"name": "Click add new profile button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186923620, "stop": 1754186923620}, {"name": "Set name = Test-Profile-232441602 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186923935, "stop": 1754186923935}, {"name": "Set description = Random profile for Selenium testing ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186925491, "stop": 1754186925491}, {"name": "Check enable checkbox to be true ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186926382, "stop": 1754186926382}, {"name": "Check write right checkbox to be Test-Profile-232441602 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186927115, "stop": 1754186927115}, {"name": "Select zone = Test-Profile-232441602 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186927428, "stop": 1754186927428}, {"name": "Add SWS item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186934602, "stop": 1754186934602}, {"name": "Click on the SWS item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186934602, "stop": 1754186934602}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186935227, "stop": 1754186935227}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186936251, "stop": 1754186936251}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186936514, "stop": 1754186936514}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186936572, "stop": 1754186936572}, {"name": "Set name = Test-Profile-232441602 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186942535, "stop": 1754186942535}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186943307, "stop": 1754186943307}, {"name": "Select profile from result view.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186944145, "stop": 1754186944145}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186945029, "stop": 1754186945029}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186946409, "stop": 1754186946409}, {"name": "Add en.Reporting Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186947243, "stop": 1754186947243}, {"name": "Click on the en.Reporting Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186947243, "stop": 1754186947243}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186947790, "stop": 1754186947790}, {"name": "Add SWIFT Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186949289, "stop": 1754186949289}, {"name": "Click on the SWIFT Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186949289, "stop": 1754186949289}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186949770, "stop": 1754186949771}, {"name": "Add SWP Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186951261, "stop": 1754186951261}, {"name": "Click on the SWP Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186951261, "stop": 1754186951261}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186951842, "stop": 1754186951842}, {"name": "Add Detection Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186953369, "stop": 1754186953369}, {"name": "Click on the Detection Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186953369, "stop": 1754186953369}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186953806, "stop": 1754186953806}, {"name": "Add Scan Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186955249, "stop": 1754186955249}, {"name": "Click on the Scan Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186955249, "stop": 1754186955249}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186955846, "stop": 1754186955846}, {"name": "Add Format Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186957292, "stop": 1754186957292}, {"name": "Click on the Format Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186957292, "stop": 1754186957292}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186957985, "stop": 1754186957985}, {"name": "Add Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186959443, "stop": 1754186959443}, {"name": "Click on the Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186959443, "stop": 1754186959443}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186959938, "stop": 1754186959938}, {"name": "Add Batch List Management item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186961376, "stop": 1754186961376}, {"name": "Click on the Batch List Management item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186961376, "stop": 1754186961376}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186961849, "stop": 1754186961849}, {"name": "Add ListExplorer item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186963236, "stop": 1754186963236}, {"name": "Click on the ListExplorer item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186963236, "stop": 1754186963236}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186963790, "stop": 1754186963790}, {"name": "Add ListSetManager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186965189, "stop": 1754186965189}, {"name": "Click on the ListSetManager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186965190, "stop": 1754186965190}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186965580, "stop": 1754186965580}, {"name": "Add List Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186967057, "stop": 1754186967057}, {"name": "Click on the List Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186967057, "stop": 1754186967057}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186967568, "stop": 1754186967568}, {"name": "Add File Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186969009, "stop": 1754186969009}, {"name": "Click on the File Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186969009, "stop": 1754186969009}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186969530, "stop": 1754186969530}, {"name": "Add Advanced Settings item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186970937, "stop": 1754186970937}, {"name": "Click on the Advanced Settings item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186970937, "stop": 1754186970937}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186971319, "stop": 1754186971319}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186972744, "stop": 1754186972744}, {"name": "Accept <PERSON><PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186973160, "stop": 1754186973160}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186975159, "stop": 1754186975159}, {"name": "Select Module Rights.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186976013, "stop": 1754186976013}, {"name": "Processing module 'SWIFT Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186978014, "stop": 1754186978014}, {"name": "Click on SWIFT Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186978014, "stop": 1754186978014}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186978968, "stop": 1754186978968}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186979304, "stop": 1754186979304}, {"name": "Processing module 'Detection Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186982805, "stop": 1754186982805}, {"name": "Click on Detection Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186982805, "stop": 1754186982805}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186983983, "stop": 1754186983983}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186984252, "stop": 1754186984252}, {"name": "Processing module 'Scan Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186986753, "stop": 1754186986753}, {"name": "Click on Scan Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186986753, "stop": 1754186986753}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186987717, "stop": 1754186987717}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186987991, "stop": 1754186987991}, {"name": "Processing module 'Format Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186990298, "stop": 1754186990298}, {"name": "Click on Format Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186990298, "stop": 1754186990298}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186991494, "stop": 1754186991494}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186991789, "stop": 1754186991789}, {"name": "Processing module 'Name Checker'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186993906, "stop": 1754186993906}, {"name": "Click on Name Checker", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186993906, "stop": 1754186993906}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186995008, "stop": 1754186995008}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186995226, "stop": 1754186995226}, {"name": "Processing module 'ListExplorer'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186998428, "stop": 1754186998428}, {"name": "Click on ListExplorer", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186998428, "stop": 1754186998428}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186999623, "stop": 1754186999623}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186999885, "stop": 1754186999885}, {"name": "Processing module 'ListSetManager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187002351, "stop": 1754187002351}, {"name": "Click on ListSetManager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187002351, "stop": 1754187002351}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187003581, "stop": 1754187003581}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187003870, "stop": 1754187003870}, {"name": "Processing module 'List Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187006100, "stop": 1754187006100}, {"name": "Click on List Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187006100, "stop": 1754187006100}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187007038, "stop": 1754187007038}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187007335, "stop": 1754187007335}, {"name": "Processing module 'File Scanner'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187009749, "stop": 1754187009749}, {"name": "Click on File Scanner", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187009749, "stop": 1754187009749}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187010688, "stop": 1754187010688}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187010965, "stop": 1754187010965}, {"name": "Processing module 'Advanced Settings'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187013415, "stop": 1754187013415}, {"name": "Click on Advanced Settings", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187013415, "stop": 1754187013415}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187014465, "stop": 1754187014465}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187014840, "stop": 1754187014840}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187016233, "stop": 1754187016233}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187016665, "stop": 1754187016665}, {"name": "Check if profile exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187018543, "stop": 1754187018543}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187018543, "stop": 1754187018543}, {"name": "Set name = Test-Profile-232441602 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187024605, "stop": 1754187024605}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187025425, "stop": 1754187025425}, {"name": "Profile exist = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187026160, "stop": 1754187026160}, {"name": "Create new operator.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187026160, "stop": 1754187026160}, {"name": "Operator test Data = Operator{id=0, loginName='selenium-user-527944822', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone48603045', displayName='Zone (48603045)', description='Zone created with random number '48603045''}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187026160, "stop": 1754187026160}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187027972, "stop": 1754187027972}, {"name": "Set login name = selenium-user-527944822", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187033318, "stop": 1754187033318}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187034245, "stop": 1754187034245}, {"name": "Click Create new operator button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187035136, "stop": 1754187035136}, {"name": "Set login name = selenium-user-527944822", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187035458, "stop": 1754187035458}, {"name": "Set first name = Random", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187036654, "stop": 1754187036654}, {"name": "Set last name = User", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187037346, "stop": 1754187037346}, {"name": "Set password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187038013, "stop": 1754187038013}, {"name": "Set confirm password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187038702, "stop": 1754187038702}, {"name": "Set enable flag = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187039385, "stop": 1754187039385}, {"name": "Set reset flag = false", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187039989, "stop": 1754187039989}, {"name": "Select zone  = Zone (48603045)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187041371, "stop": 1754187041371}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187048572, "stop": 1754187048572}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187048889, "stop": 1754187048889}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187055996, "stop": 1754187055996}, {"name": "Set login name = selenium-user-527944822", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187061336, "stop": 1754187061336}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187062269, "stop": 1754187062269}, {"name": "Create first group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187063189, "stop": 1754187063189}, {"name": "Group test Data = Group{id=0, name='selenium-random-group-253121846', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-232441602', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone48603045', displayName='Zone (48603045)', description='Zone created with random number '48603045''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-527944822', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone48603045', displayName='Zone (48603045)', description='Zone created with random number '48603045''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187063189, "stop": 1754187063189}, {"name": "Connect to database to remove Link between profile = Test-Profile-232441602 and Permission Allow to change detection status to Real Violation(Block detection)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187101568, "stop": 1754187101568}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187101586, "stop": 1754187101586}, {"name": "SELECT P.NAME as PRO<PERSON><PERSON>_NAME , PR.PROFILE_ID as PROFILE_ID , PR.GRANT_ID as GRANT_ID , PR.GRANT_RIGHT_ID as GRANT_RIGHT_ID , PR.GRANT_TYPE_ID as GRANT_TYPE_ID   FROM tModuleFunctions M   LEFT JOIN tProfileRights PR On PR.GRANT_ID  = M.ID     LEFT Join tProfiles P ON P.id =PR.PROFILE_ID   WHERE DESCRIPTION ='Allow to change detection status to Real Violation(Block detection)' and P.NAME = 'Test-Profile-232441602'  and PR.GRANT_TYPE_ID =3", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187101586, "stop": 1754187101586}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187101595, "stop": 1754187101595}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187101596, "stop": 1754187101596}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187101596, "stop": 1754187101596}, {"name": "Delete function permission 'Allow to change detection status to Real Violation(Block detection)' from DataBase", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187101596, "stop": 1754187101596}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187101611, "stop": 1754187101611}, {"name": "DELETE FROM tProfileRights  WHERE GRANT_ID  ='130' and PROFILE_ID ='171' and GRANT_TYPE_ID =3", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187101611, "stop": 1754187101611}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187101629, "stop": 1754187101629}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187101629, "stop": 1754187101629}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187101629, "stop": 1754187101629}, {"name": "Create second profile .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187101629, "stop": 1754187101629}, {"name": "Profile test Data = Profile{id=0, name='Test-Profile-983779857', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone48603045', displayName='Zone (48603045)', description='Zone created with random number '48603045''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187101631, "stop": 1754187101631}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187102791, "stop": 1754187102791}, {"name": "Set name = Test-Profile-983779857 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187108157, "stop": 1754187108157}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187108803, "stop": 1754187108803}, {"name": "Click add new profile button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187109516, "stop": 1754187109516}, {"name": "Set name = Test-Profile-983779857 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187109842, "stop": 1754187109842}, {"name": "Set description = Random profile for Selenium testing ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187111357, "stop": 1754187111357}, {"name": "Check enable checkbox to be true ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187112344, "stop": 1754187112344}, {"name": "Check write right checkbox to be Test-Profile-983779857 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187113032, "stop": 1754187113032}, {"name": "Select zone = Test-Profile-983779857 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187113287, "stop": 1754187113287}, {"name": "Add SWS item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187120245, "stop": 1754187120245}, {"name": "Click on the SWS item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187120245, "stop": 1754187120245}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187120773, "stop": 1754187120773}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187121724, "stop": 1754187121724}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187122092, "stop": 1754187122092}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187122187, "stop": 1754187122187}, {"name": "Set name = Test-Profile-983779857 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187128277, "stop": 1754187128277}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187129234, "stop": 1754187129234}, {"name": "Select profile from result view.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187129932, "stop": 1754187129932}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187130524, "stop": 1754187130524}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187131713, "stop": 1754187131713}, {"name": "Add en.Reporting Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187132401, "stop": 1754187132401}, {"name": "Click on the en.Reporting Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187132401, "stop": 1754187132401}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187132954, "stop": 1754187132954}, {"name": "Add SWIFT Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187134400, "stop": 1754187134400}, {"name": "Click on the SWIFT Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187134400, "stop": 1754187134400}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187135077, "stop": 1754187135077}, {"name": "Add SWP Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187136516, "stop": 1754187136516}, {"name": "Click on the SWP Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187136516, "stop": 1754187136516}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187136930, "stop": 1754187136930}, {"name": "Add Detection Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187138401, "stop": 1754187138401}, {"name": "Click on the Detection Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187138401, "stop": 1754187138401}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187138792, "stop": 1754187138792}, {"name": "Add Scan Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187140224, "stop": 1754187140224}, {"name": "Click on the Scan Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187140224, "stop": 1754187140224}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187140745, "stop": 1754187140745}, {"name": "Add Format Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187142210, "stop": 1754187142210}, {"name": "Click on the Format Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187142210, "stop": 1754187142210}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187142615, "stop": 1754187142615}, {"name": "Add Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187144051, "stop": 1754187144051}, {"name": "Click on the Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187144051, "stop": 1754187144051}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187144614, "stop": 1754187144614}, {"name": "Add Batch List Management item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187146081, "stop": 1754187146081}, {"name": "Click on the Batch List Management item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187146082, "stop": 1754187146082}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187146689, "stop": 1754187146689}, {"name": "Add ListExplorer item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187148093, "stop": 1754187148093}, {"name": "Click on the ListExplorer item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187148093, "stop": 1754187148093}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187148549, "stop": 1754187148549}, {"name": "Add ListSetManager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187149958, "stop": 1754187149958}, {"name": "Click on the ListSetManager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187149958, "stop": 1754187149958}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187150353, "stop": 1754187150353}, {"name": "Add List Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187151836, "stop": 1754187151836}, {"name": "Click on the List Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187151836, "stop": 1754187151836}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187152496, "stop": 1754187152496}, {"name": "Add File Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187153905, "stop": 1754187153905}, {"name": "Click on the File Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187153905, "stop": 1754187153905}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187154460, "stop": 1754187154460}, {"name": "Add Advanced Settings item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187155979, "stop": 1754187155979}, {"name": "Click on the Advanced Settings item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187155979, "stop": 1754187155979}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187156538, "stop": 1754187156538}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187157981, "stop": 1754187157981}, {"name": "Accept <PERSON><PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187158407, "stop": 1754187158407}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187159988, "stop": 1754187159988}, {"name": "Select Module Rights.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187160759, "stop": 1754187160759}, {"name": "Processing module 'SWIFT Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187162761, "stop": 1754187162761}, {"name": "Click on SWIFT Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187162761, "stop": 1754187162761}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187163696, "stop": 1754187163696}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187164017, "stop": 1754187164017}, {"name": "Processing module 'Detection Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187167296, "stop": 1754187167296}, {"name": "Click on Detection Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187167296, "stop": 1754187167296}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187168344, "stop": 1754187168344}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187168779, "stop": 1754187168779}, {"name": "Processing module 'Scan Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187171192, "stop": 1754187171192}, {"name": "Click on Scan Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187171192, "stop": 1754187171192}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187172095, "stop": 1754187172095}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187172328, "stop": 1754187172328}, {"name": "Processing module 'Format Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187174572, "stop": 1754187174572}, {"name": "Click on Format Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187174572, "stop": 1754187174572}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187175778, "stop": 1754187175778}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187176028, "stop": 1754187176028}, {"name": "Processing module 'Name Checker'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187178328, "stop": 1754187178328}, {"name": "Click on Name Checker", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187178328, "stop": 1754187178328}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187179539, "stop": 1754187179539}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187179901, "stop": 1754187179901}, {"name": "Processing module 'ListExplorer'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187183460, "stop": 1754187183460}, {"name": "Click on ListExplorer", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187183460, "stop": 1754187183460}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187184510, "stop": 1754187184510}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187184791, "stop": 1754187184791}, {"name": "Processing module 'ListSetManager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187187241, "stop": 1754187187241}, {"name": "Click on ListSetManager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187187241, "stop": 1754187187241}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187188195, "stop": 1754187188195}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187188565, "stop": 1754187188565}, {"name": "Processing module 'List Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187190822, "stop": 1754187190822}, {"name": "Click on List Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187190822, "stop": 1754187190822}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187191841, "stop": 1754187191841}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187192179, "stop": 1754187192179}, {"name": "Processing module 'File Scanner'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187194445, "stop": 1754187194445}, {"name": "Click on File Scanner", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187194445, "stop": 1754187194445}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187195477, "stop": 1754187195477}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187195697, "stop": 1754187195697}, {"name": "Processing module 'Advanced Settings'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187198002, "stop": 1754187198002}, {"name": "Click on Advanced Settings", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187198002, "stop": 1754187198002}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187199063, "stop": 1754187199063}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187199322, "stop": 1754187199322}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187200823, "stop": 1754187200823}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187201172, "stop": 1754187201172}, {"name": "Check if profile exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187202847, "stop": 1754187202847}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187202847, "stop": 1754187202847}, {"name": "Set name = Test-Profile-983779857 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187208607, "stop": 1754187208607}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187209555, "stop": 1754187209555}, {"name": "Profile exist = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187210564, "stop": 1754187210564}, {"name": "Create second group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187210564, "stop": 1754187210564}, {"name": "Group test Data = Group{id=0, name='selenium-random-group-815100309', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-983779857', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone48603045', displayName='Zone (48603045)', description='Zone created with random number '48603045''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-527944822', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone48603045', displayName='Zone (48603045)', description='Zone created with random number '48603045''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187210564, "stop": 1754187210564}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187250517, "stop": 1754187250517}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='selenium-user-527944822'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187250517, "stop": 1754187250517}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187250524, "stop": 1754187250524}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187250524, "stop": 1754187250524}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187250524, "stop": 1754187250524}, {"name": "Login with User Name = selenium-user-527944822 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187250717, "stop": 1754187250717}, {"name": "Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-103391370', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@390b4bcc, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-103391370', officialDate='null', entry=[ListEntry{type='null', name='EntryName-103391370', firstName='EntryFirstName-103391370', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-103391370', firstName='EntryFirstName-103391370', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187255810, "stop": 1754187255810}, {"name": "Connect to Database and Check if User Profile = Test-Profile-983779857 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187255820, "stop": 1754187255820}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187255820, "stop": 1754187255820}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187255857, "stop": 1754187255857}, {"name": "SELECT ID FROM tProfiles Where NAME ='Test-Profile-983779857' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187255857, "stop": 1754187255857}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187255859, "stop": 1754187255859}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187255859, "stop": 1754187255859}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187255860, "stop": 1754187255860}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187255879, "stop": 1754187255879}, {"name": "Delete From tListSetProfile where profile_id in (172)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187255879, "stop": 1754187255879}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187255882, "stop": 1754187255882}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187255882, "stop": 1754187255882}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187255882, "stop": 1754187255882}, {"name": "Number of effected rows = 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187255882, "stop": 1754187255882}, {"name": "Search for list by listName = ListName-103391370 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187276690, "stop": 1754187276690}, {"name": "Set zone : Zone (48603045)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187286148, "stop": 1754187286148}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187288283, "stop": 1754187288283}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187288871, "stop": 1754187288871}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187289407, "stop": 1754187289407}, {"name": "Set template name = templateName-103391370", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187289673, "stop": 1754187289673}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187292158, "stop": 1754187292158}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187292818, "stop": 1754187292818}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187293835, "stop": 1754187293835}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187294416, "stop": 1754187294416}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187313424, "stop": 1754187313424}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187313424, "stop": 1754187313424}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187360646, "stop": 1754187360646}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187360646, "stop": 1754187360646}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187360646, "stop": 1754187360646}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187360669, "stop": 1754187360669}, {"name": "SELECT ID FROM tProfiles Where NAME ='Test-Profile-983779857'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187360669, "stop": 1754187360669}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187360671, "stop": 1754187360671}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187360671, "stop": 1754187360671}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187360673, "stop": 1754187360673}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187374854, "stop": 1754187374854}, {"name": "RJE File Content= {1:F01PTSABEMMAXXX0000000001}{2:I564ACCLFRP1XXXXN}{3:{108:ENT144037000001}}{4:\r\n:16R:GENL\r\n:20C::CORP//2015033100010560\r\n:20C::SEME//0105601900000002\r\n:23G:EntryName-103391370, EntryFirstName-103391370\r\n:22F::CAEV//DVCA\r\n:22F::CAMV//MAND\r\n:98A::PREP//20150331\r\n:25D::PROC//COMP\r\n:16R:LINK\r\n:13A::LINK//564\r\n:20C::PREV//0105601900000001\r\n:16S:LINK\r\n:16S:GENL\r\n:16R:USECU\r\n:35B:ISIN DE0005190003\r\nBMW Bayerische Motoren Werke AG\r\n:16R:ACCTINFO\r\n:97A::SAFE//000000190003\r\n:93B::ELIG//UNIT/360,\r\n:93B::PEND//UNIT/360,\r\n:93B::PENR//UNIT/0,\r\n:93B::SETT//UNIT/360,\r\n:16S:ACCTINFO\r\n:16S:USECU\r\n:16R:CADETL\r\n:98A::XDTE//20150514\r\n:98A::RDTE//20150515\r\n:22F::DIVI//FINL\r\n:16S:CADETL\r\n:16R:CAOPTN\r\n:13A::CAON//001\r\n:22F::CAOP//CASH\r\n:17B::DFLT//Y\r\n:16R:CASHMOVE\r\n:22H::CRDB//CRED\r\n:98A::PAYD//20150514\r\n:92A::TAXR//26,375\r\n:92F::GRSS//EUR2,9\r\n:16S:CASHMOVE\r\n:16S:CAOPTN\r\n-}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187382390, "stop": 1754187382390}, {"name": "Start coping data from sample file to another file to be used.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187382390, "stop": 1754187382390}, {"name": "Successfully wrote to the file.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187382394, "stop": 1754187382394}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187383893, "stop": 1754187383893}, {"name": "Validation message = File sent to the server for processing with id [3048]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187398236, "stop": 1754187398236}, {"name": "Alert Message = File sent to the server for processing with id [3048]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187398236, "stop": 1754187398236}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187398702, "stop": 1754187398702}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187407439, "stop": 1754187407439}, {"name": "Detection ID = 7802", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187408457, "stop": 1754187408457}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187412754, "stop": 1754187412754}, {"name": "Check if user can block detection. ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187413935, "stop": 1754187413936}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187418267, "stop": 1754187418267}], "attachments": [], "parameters": [{"name": "arg0", "value": "Operator{id=0, loginName='null', firstName='null', lastName='null', password='null', zone=null}"}], "start": 1754186878921, "stop": 1754187426475}