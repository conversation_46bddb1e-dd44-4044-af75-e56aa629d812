{"uuid": "6bc48f82-b19b-4092-a791-aaf0a6dba2f5", "historyId": "e7e1e777566a51e090385f81ecf16fe", "fullName": "eastnets.screening.regression.detectionmanager.DetectionManager_TC001.detectionManager_TC001", "labels": [{"name": "package", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC001"}, {"name": "testClass", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC001"}, {"name": "testMethod", "value": "detectionManager_TC001"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Detection Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC001"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-3(38)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Detection Manager"}], "links": [], "name": "detectionManager_TC001", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754264720203, "stop": 1754264720203}