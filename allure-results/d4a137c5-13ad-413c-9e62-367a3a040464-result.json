{"uuid": "d4a137c5-13ad-413c-9e62-367a3a040464", "historyId": "5359ee0a5d1b716f67c3ed20b21af8b7", "fullName": "eastnets.screening.regression.detectionmanager.DetectionManager_TC011.detectionManager_TC011", "labels": [{"name": "package", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC011"}, {"name": "testClass", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC011"}, {"name": "testMethod", "value": "detectionManager_TC011"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Detection Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC011"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Detection Manager"}], "links": [], "name": "detectionManager_TC011", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754142445242, "stop": 1754142445242}