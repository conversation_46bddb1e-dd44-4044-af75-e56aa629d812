{"uuid": "d72bcad4-d3c2-4a1a-b9b5-bb8eeb403104", "historyId": "39fdefff98ede705eb3c9d2af44b2414", "fullName": "eastnets.admin.AuditManagerTest.checkAuditManager", "labels": [{"name": "package", "value": "eastnets.admin.AuditManagerTest"}, {"name": "testClass", "value": "eastnets.admin.AuditManagerTest"}, {"name": "testMethod", "value": "checkAuditManager"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Admin Test Cases"}, {"name": "subSuite", "value": "eastnets.admin.AuditManagerTest"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-1(36)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "tag", "value": "AuditManager"}], "links": [], "name": "Verify that an event recording audit is appearing properly for the removed Groups/Users", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that an event recording audit is appearing properly for the removed Groups/Users", "steps": [{"name": "Operator test data = Operator{id=0, loginName='selenium-user-482024280', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Common_Zone_01', displayName='Common Zone 01', description='Zone for most of the tests'}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149199105, "stop": 1754149199105}, {"name": "Group test data = Group{id=0, name='selenium-random-group-467700992', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='full-right-profile', enabled=true, writeRight=true, description='Profile with full Rights', zone=Zone{id=0, name='Common_Zone_01', displayName='Common Zone 01', description='Zone for most of the tests'}, profileRights={Administration={Archive Viewer=[], Operator Manager=[ALL], Group Manager=[ALL], Profile Manager=[ALL], Report Manager=[ALL], Zone Manager=[ALL], Audit Manager=[], Event Viewer=[]}, RPTSRV={}, SWS={License Manager=[], Name Checker=[ALL], WorldCheckSettings=[ALL], Batch List Management=[], ISO20022 Format Configuration=[ALL], DowJonesSettings=[ALL], ListSetManager=[ALL], MQ Connector=[ALL], DB Scanner=[ALL], Customer Card=[ALL], Good Guys Migration=[], SAA OFCA Station=[ALL], SAA OFCS Detect=[ALL], List Manager=[ALL], SafeTrade=[ALL], en.Reporting Scanner=[ALL], SWIFT Manager=[ALL], SWP Name Checker=[ALL], File Based Archive=[ALL], Detection Manager=[ALL], Approval Configuration=[ALL], DB Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], SAA OFCS Monitor=[ALL], Archive Management=[ALL], Event Viewer=[], ISO20022-Module=[ALL], ListExplorer=[ALL], ISO20022 Schema Configuration=[ALL], MQ Manager=[ALL], Archive Viewer=[ALL], Replay=[ALL], StrippingDetectorManager=[ALL], Report Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL], GoodGuyExplorer=[ALL], SafeWatch API=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-482024280', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Common_Zone_01', displayName='Common Zone 01', description='Zone for most of the tests'}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149199112, "stop": 1754149199112}, {"name": "Create new operator.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149199112, "stop": 1754149199112}, {"name": "Operator test Data = Operator{id=0, loginName='selenium-user-482024280', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Common_Zone_01', displayName='Common Zone 01', description='Zone for most of the tests'}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149199112, "stop": 1754149199112}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149350861, "stop": 1754149350861}, {"name": "Set login name = selenium-user-482024280", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149356293, "stop": 1754149356293}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149357151, "stop": 1754149357151}, {"name": "Click Create new operator button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149391214, "stop": 1754149391214}, {"name": "Set login name = selenium-user-482024280", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149391735, "stop": 1754149391735}, {"name": "Set first name = Random", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149393551, "stop": 1754149393551}, {"name": "Set last name = User", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149394399, "stop": 1754149394399}, {"name": "Set password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149394994, "stop": 1754149394994}, {"name": "Set confirm password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149395656, "stop": 1754149395656}, {"name": "Set enable flag = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149396446, "stop": 1754149396446}, {"name": "Set reset flag = false", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149397231, "stop": 1754149397231}, {"name": "Select zone  = Common Zone 01", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149398530, "stop": 1754149398530}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149403931, "stop": 1754149403931}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149404258, "stop": 1754149404258}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149508143, "stop": 1754149508143}, {"name": "Set login name = selenium-user-482024280", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149513586, "stop": 1754149513586}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149514613, "stop": 1754149514613}, {"name": "Create first group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149565416, "stop": 1754149565416}, {"name": "Group test Data = Group{id=0, name='selenium-random-group-467700992', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='full-right-profile', enabled=true, writeRight=true, description='Profile with full Rights', zone=Zone{id=0, name='Common_Zone_01', displayName='Common Zone 01', description='Zone for most of the tests'}, profileRights={Administration={Archive Viewer=[], Operator Manager=[ALL], Group Manager=[ALL], Profile Manager=[ALL], Report Manager=[ALL], Zone Manager=[ALL], Audit Manager=[], Event Viewer=[]}, RPTSRV={}, SWS={License Manager=[], Name Checker=[ALL], WorldCheckSettings=[ALL], Batch List Management=[], ISO20022 Format Configuration=[ALL], DowJonesSettings=[ALL], ListSetManager=[ALL], MQ Connector=[ALL], DB Scanner=[ALL], Customer Card=[ALL], Good Guys Migration=[], SAA OFCA Station=[ALL], SAA OFCS Detect=[ALL], List Manager=[ALL], SafeTrade=[ALL], en.Reporting Scanner=[ALL], SWIFT Manager=[ALL], SWP Name Checker=[ALL], File Based Archive=[ALL], Detection Manager=[ALL], Approval Configuration=[ALL], DB Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], SAA OFCS Monitor=[ALL], Archive Management=[ALL], Event Viewer=[], ISO20022-Module=[ALL], ListExplorer=[ALL], ISO20022 Schema Configuration=[ALL], MQ Manager=[ALL], Archive Viewer=[ALL], Replay=[ALL], StrippingDetectorManager=[ALL], Report Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL], GoodGuyExplorer=[ALL], SafeWatch API=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-482024280', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Common_Zone_01', displayName='Common Zone 01', description='Zone for most of the tests'}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149565416, "stop": 1754149565416}, {"name": "Remove operator with login name = selenium-user-482024280.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149808960, "stop": 1754149808960}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149812773, "stop": 1754149812773}, {"name": "Set login name = selenium-user-482024280", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149818338, "stop": 1754149818338}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149819281, "stop": 1754149819281}, {"name": "Select operator and click remove button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149819822, "stop": 1754149819822}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149821482, "stop": 1754149821482}, {"name": "Set login name = selenium-user-482024280", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149827109, "stop": 1754149827109}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149828078, "stop": 1754149828078}, {"name": "Remove group with name = selenium-random-group-467700992.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149828761, "stop": 1754149828761}, {"name": "Select group and click remove button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149837957, "stop": 1754149837957}, {"name": "Asserting on data in result view", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149973684, "stop": 1754149973684}, {"name": "<PERSON><PERSON><PERSON> Passed Successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754149973687, "stop": 1754149973687}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754149199090, "stop": 1754149973687}