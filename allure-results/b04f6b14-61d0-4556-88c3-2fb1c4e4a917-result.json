{"uuid": "b04f6b14-61d0-4556-88c3-2fb1c4e4a917", "historyId": "dc11dbd33f2d79b2f543672e1979c449", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC001.scanManager_TC001", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC001"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC001"}, {"name": "testMethod", "value": "scanManager_TC001"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC001"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is able to scan a generic text format file without creating alerts automatically option checked and take decision .", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to scan a generic text format file without creating alerts automatically option checked and take decision .", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241295723, "stop": 1754241295723}, {"name": "Validation message = File sent to the server for processing with id [3119]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241315908, "stop": 1754241315908}, {"name": "Alert Message = File sent to the server for processing with id [3119]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241315908, "stop": 1754241315908}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241318087, "stop": 1754241318087}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241328023, "stop": 1754241328023}, {"name": "Detection ID = 8054", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241329484, "stop": 1754241329484}, {"name": "Detection Status = REP", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241350388, "stop": 1754241350388}, {"name": "Detection Status = REP", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241356920, "stop": 1754241356920}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "FileScan{filePath='/Generic.txt', format='Generic Text', encoding='null', enList=null, result='null', rank='null', detectVessels='true', detectCountries='true', createAlertsAutomatically='false'}"}], "start": 1754241295723, "stop": 1754241365240}