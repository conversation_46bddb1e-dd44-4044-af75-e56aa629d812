{"uuid": "aa323ada-1514-4b63-944c-f872afe49830", "historyId": "c1c14de74bb43bbf5f8d10a287df71e", "fullName": "eastnets.admin.AdminTest.checkPermissionWhenAssignSecGroupProfileZone", "labels": [{"name": "package", "value": "eastnets.admin.AdminTest"}, {"name": "testClass", "value": "eastnets.admin.AdminTest"}, {"name": "testMethod", "value": "checkPermissionWhenAssignSecGroupProfileZone"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Admin Test Cases"}, {"name": "subSuite", "value": "eastnets.admin.AdminTest"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-1(36)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}], "links": [], "name": "Verify that the user that is assigned to Group, Profile, and Zone have permission to Block but don't have the permission to Release is able to release when this user is assigned to a second Group, Profile and Zone that have the permission to Release.", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that the user that is assigned to Group, Profile, and Zone have permission to Block but don't have the permission to Release is able to release when this user is assigned to a second Group, Profile and Zone that have the permission to Release.", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183916597, "stop": 1754183916597}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='sysadmin'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183916597, "stop": 1754183916597}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183916606, "stop": 1754183916606}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183916606, "stop": 1754183916606}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183916612, "stop": 1754183916612}, {"name": "Login with User Name = sysadmin and Password = manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183916923, "stop": 1754183916923}, {"name": "Operator test data = Operator{id=0, loginName='selenium-user-991059541', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone187240047', displayName='Zone (187240047)', description='Zone created with random number '187240047''}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183923046, "stop": 1754183923046}, {"name": "Group test data = Group{id=0, name='selenium-random-group-447834888', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-631814308', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone187240047', displayName='Zone (187240047)', description='Zone created with random number '187240047''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-991059541', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone187240047', displayName='Zone (187240047)', description='Zone created with random number '187240047''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183923046, "stop": 1754183923046}, {"name": "Create new zone .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183923046, "stop": 1754183923046}, {"name": "Zone test Data = Zone{id=0, name='Zone187240047', displayName='Zone (187240047)', description='Zone created with random number '187240047''}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183923046, "stop": 1754183923046}, {"name": "Check if zone with name = 'Zone187240047' exists", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183925163, "stop": 1754183925163}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183925414, "stop": 1754183925415}, {"name": "Enter name =Zone187240047", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183930801, "stop": 1754183930801}, {"name": "Enter display Name =Zone (187240047)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183931891, "stop": 1754183931891}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183932871, "stop": 1754183932871}, {"name": "Zone doesn't exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183933476, "stop": 1754183933476}, {"name": "Click add new zone button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183933476, "stop": 1754183933476}, {"name": "Set create new zone data.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183933689, "stop": 1754183933689}, {"name": "Set name = Zone187240047", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183933690, "stop": 1754183933690}, {"name": "Set display name = Zone (187240047)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183935208, "stop": 1754183935208}, {"name": "Set description = Zone created with random number '187240047'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183936051, "stop": 1754183936051}, {"name": "Capture zone id from UI = 146", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183937409, "stop": 1754183937409}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183937409, "stop": 1754183937409}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183938967, "stop": 1754183938967}, {"name": "Enter name =Zone187240047", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183944457, "stop": 1754183944457}, {"name": "Enter display Name =Zone (187240047)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183945309, "stop": 1754183945309}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183946281, "stop": 1754183946281}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183949357, "stop": 1754183949357}, {"name": "Enter name =Zone187240047", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183954766, "stop": 1754183954766}, {"name": "Enter display Name =Zone (187240047)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183955711, "stop": 1754183955711}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183956478, "stop": 1754183956478}, {"name": "Create first profile .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183957167, "stop": 1754183957167}, {"name": "Profile test Data = Profile{id=0, name='Test-Profile-631814308', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone187240047', displayName='Zone (187240047)', description='Zone created with random number '187240047''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183957167, "stop": 1754183957167}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183958753, "stop": 1754183958753}, {"name": "Set name = Test-Profile-631814308 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183964144, "stop": 1754183964144}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183965374, "stop": 1754183965374}, {"name": "Click add new profile button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183966509, "stop": 1754183966509}, {"name": "Set name = Test-Profile-631814308 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183966983, "stop": 1754183966983}, {"name": "Set description = Random profile for Selenium testing ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183968792, "stop": 1754183968792}, {"name": "Check enable checkbox to be true ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183970750, "stop": 1754183970750}, {"name": "Check write right checkbox to be Test-Profile-631814308 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183971826, "stop": 1754183971826}, {"name": "Select zone = Test-Profile-631814308 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183972373, "stop": 1754183972373}, {"name": "Add SWS item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183980664, "stop": 1754183980664}, {"name": "Click on the SWS item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183980664, "stop": 1754183980664}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183981246, "stop": 1754183981246}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183982325, "stop": 1754183982325}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183982663, "stop": 1754183982663}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183982774, "stop": 1754183982774}, {"name": "Set name = Test-Profile-631814308 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183989411, "stop": 1754183989411}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183990793, "stop": 1754183990793}, {"name": "Select profile from result view.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183991853, "stop": 1754183991854}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183993016, "stop": 1754183993016}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183994706, "stop": 1754183994706}, {"name": "Add en.Reporting Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183995604, "stop": 1754183995604}, {"name": "Click on the en.Reporting Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183995604, "stop": 1754183995604}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183996434, "stop": 1754183996434}, {"name": "Add SWIFT Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183997972, "stop": 1754183997972}, {"name": "Click on the SWIFT Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183997972, "stop": 1754183997972}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754183998708, "stop": 1754183998708}, {"name": "Add SWP Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184000104, "stop": 1754184000104}, {"name": "Click on the SWP Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184000104, "stop": 1754184000104}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184001145, "stop": 1754184001145}, {"name": "Add Detection Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184002831, "stop": 1754184002831}, {"name": "Click on the Detection Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184002831, "stop": 1754184002831}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184003743, "stop": 1754184003743}, {"name": "Add Scan Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184005300, "stop": 1754184005300}, {"name": "Click on the Scan Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184005300, "stop": 1754184005300}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184006067, "stop": 1754184006067}, {"name": "Add Format Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184007480, "stop": 1754184007480}, {"name": "Click on the Format Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184007480, "stop": 1754184007480}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184008085, "stop": 1754184008085}, {"name": "Add Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184009581, "stop": 1754184009581}, {"name": "Click on the Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184009581, "stop": 1754184009581}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184064323, "stop": 1754184064323}, {"name": "Add Batch List Management item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184065880, "stop": 1754184065880}, {"name": "Click on the Batch List Management item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184065880, "stop": 1754184065880}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184066519, "stop": 1754184066519}, {"name": "Add ListExplorer item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184068004, "stop": 1754184068004}, {"name": "Click on the ListExplorer item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184068004, "stop": 1754184068004}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184069321, "stop": 1754184069321}, {"name": "Add ListSetManager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184070850, "stop": 1754184070850}, {"name": "Click on the ListSetManager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184070850, "stop": 1754184070850}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184071521, "stop": 1754184071521}, {"name": "Add List Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184072972, "stop": 1754184072972}, {"name": "Click on the List Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184072972, "stop": 1754184072972}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184073624, "stop": 1754184073624}, {"name": "Add File Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184075035, "stop": 1754184075035}, {"name": "Click on the File Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184075035, "stop": 1754184075035}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184075523, "stop": 1754184075523}, {"name": "Add Advanced Settings item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184077029, "stop": 1754184077029}, {"name": "Click on the Advanced Settings item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184077029, "stop": 1754184077029}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184077712, "stop": 1754184077712}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184079213, "stop": 1754184079213}, {"name": "Accept <PERSON><PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184079901, "stop": 1754184079901}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184082082, "stop": 1754184082082}, {"name": "Select Module Rights.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184083036, "stop": 1754184083036}, {"name": "Processing module 'SWIFT Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184085037, "stop": 1754184085037}, {"name": "Click on SWIFT Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184085038, "stop": 1754184085038}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184086123, "stop": 1754184086123}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184086581, "stop": 1754184086581}, {"name": "Processing module 'Detection Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184091531, "stop": 1754184091531}, {"name": "Click on Detection Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184091531, "stop": 1754184091531}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184092661, "stop": 1754184092661}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184093128, "stop": 1754184093128}, {"name": "Processing module 'Scan Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184095921, "stop": 1754184095921}, {"name": "Click on Scan Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184095921, "stop": 1754184095921}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184097059, "stop": 1754184097059}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184097670, "stop": 1754184097670}, {"name": "Processing module 'Format Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184101528, "stop": 1754184101528}, {"name": "Click on Format Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184101528, "stop": 1754184101528}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184102523, "stop": 1754184102523}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184102841, "stop": 1754184102841}, {"name": "Processing module 'Name Checker'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184133749, "stop": 1754184133749}, {"name": "Click on Name Checker", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184133750, "stop": 1754184133750}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184135394, "stop": 1754184135394}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184137068, "stop": 1754184137068}, {"name": "Processing module 'ListExplorer'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184140861, "stop": 1754184140861}, {"name": "Click on ListExplorer", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184140861, "stop": 1754184140861}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184142043, "stop": 1754184142043}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184142543, "stop": 1754184142543}, {"name": "Processing module 'ListSetManager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184145384, "stop": 1754184145384}, {"name": "Click on ListSetManager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184145384, "stop": 1754184145384}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184146320, "stop": 1754184146320}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184146652, "stop": 1754184146652}, {"name": "Processing module 'List Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184149095, "stop": 1754184149095}, {"name": "Click on List Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184149095, "stop": 1754184149095}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184150043, "stop": 1754184150043}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184150422, "stop": 1754184150422}, {"name": "Processing module 'File Scanner'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184152839, "stop": 1754184152839}, {"name": "Click on File Scanner", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184152839, "stop": 1754184152839}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184153930, "stop": 1754184153930}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184154381, "stop": 1754184154381}, {"name": "Processing module 'Advanced Settings'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184157299, "stop": 1754184157299}, {"name": "Click on Advanced Settings", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184157299, "stop": 1754184157299}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184158251, "stop": 1754184158251}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184158674, "stop": 1754184158674}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184160247, "stop": 1754184160247}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184160634, "stop": 1754184160634}, {"name": "Check if profile exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184162576, "stop": 1754184162576}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184162576, "stop": 1754184162576}, {"name": "Set name = Test-Profile-631814308 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184168658, "stop": 1754184168658}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184170077, "stop": 1754184170077}, {"name": "Profile exist = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184171132, "stop": 1754184171132}, {"name": "Create new operator.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184171132, "stop": 1754184171132}, {"name": "Operator test Data = Operator{id=0, loginName='selenium-user-991059541', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone187240047', displayName='Zone (187240047)', description='Zone created with random number '187240047''}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184171132, "stop": 1754184171132}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184174303, "stop": 1754184174303}, {"name": "Set login name = selenium-user-991059541", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184179827, "stop": 1754184179827}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184180922, "stop": 1754184180922}, {"name": "Click Create new operator button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184182050, "stop": 1754184182050}, {"name": "Set login name = selenium-user-991059541", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184182457, "stop": 1754184182457}, {"name": "Set first name = Random", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184184407, "stop": 1754184184407}, {"name": "Set last name = User", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184185655, "stop": 1754184185655}, {"name": "Set password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184186906, "stop": 1754184186906}, {"name": "Set confirm password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184188169, "stop": 1754184188169}, {"name": "Set enable flag = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184189261, "stop": 1754184189261}, {"name": "Set reset flag = false", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184190136, "stop": 1754184190136}, {"name": "Select zone  = Zone (187240047)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184191684, "stop": 1754184191684}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184201079, "stop": 1754184201079}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184201587, "stop": 1754184201587}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184209399, "stop": 1754184209399}, {"name": "Set login name = selenium-user-991059541", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184214859, "stop": 1754184214859}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184215894, "stop": 1754184215894}, {"name": "Create first group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184217159, "stop": 1754184217159}, {"name": "Group test Data = Group{id=0, name='selenium-random-group-447834888', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-631814308', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone187240047', displayName='Zone (187240047)', description='Zone created with random number '187240047''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-991059541', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone187240047', displayName='Zone (187240047)', description='Zone created with random number '187240047''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184217159, "stop": 1754184217159}, {"name": "Connect to database to remove Link between profile = Test-Profile-631814308 and Permission Allow to add attachments to detection", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184261734, "stop": 1754184261734}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184261754, "stop": 1754184261754}, {"name": "SELECT P.NAME as PR<PERSON><PERSON><PERSON>_NAME , PR.PROFILE_ID as PROFILE_ID , PR.GRANT_ID as GRANT_ID , PR.GRANT_RIGHT_ID as GRANT_RIGHT_ID , PR.GRANT_TYPE_ID as GRANT_TYPE_ID   FROM tModuleFunctions M   LEFT JOIN tProfileRights PR On PR.GRANT_ID  = M.ID     LEFT Join tProfiles P ON P.id =PR.PROFILE_ID   WHERE DESCRIPTION ='Allow to add attachments to detection' and P.NAME = 'Test-Profile-631814308'  and PR.GRANT_TYPE_ID =3", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184261754, "stop": 1754184261754}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184261767, "stop": 1754184261767}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184261767, "stop": 1754184261767}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184261767, "stop": 1754184261767}, {"name": "Delete function permission 'Allow to add attachments to detection' from DataBase", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184261767, "stop": 1754184261767}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184261784, "stop": 1754184261784}, {"name": "DELETE FROM tProfileRights  WHERE GRANT_ID  ='137' and PROFILE_ID ='168' and GRANT_TYPE_ID =3", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184261784, "stop": 1754184261784}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184261800, "stop": 1754184261800}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184261800, "stop": 1754184261800}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184261800, "stop": 1754184261800}, {"name": "Create second zone .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184261802, "stop": 1754184261802}, {"name": "Zone test Data = Zone{id=0, name='Zone759176895', displayName='Zone (759176895)', description='Zone created with random number '759176895''}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184261802, "stop": 1754184261802}, {"name": "Check if zone with name = 'Zone759176895' exists", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184263935, "stop": 1754184263935}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184264262, "stop": 1754184264262}, {"name": "Enter name =Zone759176895", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184269838, "stop": 1754184269838}, {"name": "Enter display Name =Zone (759176895)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184270572, "stop": 1754184270572}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184271476, "stop": 1754184271476}, {"name": "Zone doesn't exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184272142, "stop": 1754184272142}, {"name": "Click add new zone button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184272142, "stop": 1754184272142}, {"name": "Set create new zone data.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184272557, "stop": 1754184272557}, {"name": "Set name = Zone759176895", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184272557, "stop": 1754184272557}, {"name": "Set display name = Zone (759176895)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184274272, "stop": 1754184274272}, {"name": "Set description = Zone created with random number '759176895'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184275146, "stop": 1754184275146}, {"name": "Capture zone id from UI = 147", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184276540, "stop": 1754184276540}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184276540, "stop": 1754184276540}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184277962, "stop": 1754184277962}, {"name": "Enter name =Zone759176895", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184283378, "stop": 1754184283378}, {"name": "Enter display Name =Zone (759176895)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184284240, "stop": 1754184284240}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184285243, "stop": 1754184285243}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184287782, "stop": 1754184287782}, {"name": "Enter name =Zone759176895", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184293269, "stop": 1754184293269}, {"name": "Enter display Name =Zone (759176895)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184294139, "stop": 1754184294139}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184295588, "stop": 1754184295588}, {"name": "Create second profile .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184296612, "stop": 1754184296612}, {"name": "Profile test Data = Profile{id=0, name='Test-Profile-303302491', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone759176895', displayName='Zone (759176895)', description='Zone created with random number '759176895''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184296612, "stop": 1754184296612}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184298751, "stop": 1754184298751}, {"name": "Set name = Test-Profile-303302491 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184304438, "stop": 1754184304438}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184305588, "stop": 1754184305588}, {"name": "Click add new profile button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184306614, "stop": 1754184306614}, {"name": "Set name = Test-Profile-303302491 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184306967, "stop": 1754184306967}, {"name": "Set description = Random profile for Selenium testing ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184308999, "stop": 1754184308999}, {"name": "Check enable checkbox to be true ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184310370, "stop": 1754184310370}, {"name": "Check write right checkbox to be Test-Profile-303302491 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184311537, "stop": 1754184311537}, {"name": "Select zone = Test-Profile-303302491 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184312011, "stop": 1754184312011}, {"name": "Add SWS item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184320457, "stop": 1754184320457}, {"name": "Click on the SWS item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184320457, "stop": 1754184320457}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184321076, "stop": 1754184321076}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184322102, "stop": 1754184322102}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184322623, "stop": 1754184322623}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184322880, "stop": 1754184322880}, {"name": "Set name = Test-Profile-303302491 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184329029, "stop": 1754184329029}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184331601, "stop": 1754184331601}, {"name": "Select profile from result view.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184332882, "stop": 1754184332882}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184334528, "stop": 1754184334528}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184335938, "stop": 1754184335938}, {"name": "Add en.Reporting Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184336707, "stop": 1754184336707}, {"name": "Click on the en.Reporting Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184336707, "stop": 1754184336707}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184337526, "stop": 1754184337526}, {"name": "Add SWIFT Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184338969, "stop": 1754184338969}, {"name": "Click on the SWIFT Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184338969, "stop": 1754184338969}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184340047, "stop": 1754184340047}, {"name": "Add SWP Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184341477, "stop": 1754184341477}, {"name": "Click on the SWP Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184341477, "stop": 1754184341477}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184342007, "stop": 1754184342007}, {"name": "Add Detection Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184343497, "stop": 1754184343497}, {"name": "Click on the Detection Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184343497, "stop": 1754184343497}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184344074, "stop": 1754184344074}, {"name": "Add Scan Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184345601, "stop": 1754184345601}, {"name": "Click on the Scan Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184345602, "stop": 1754184345602}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184346388, "stop": 1754184346388}, {"name": "Add Format Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184350432, "stop": 1754184350432}, {"name": "Click on the Format Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184350433, "stop": 1754184350433}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184351018, "stop": 1754184351018}, {"name": "Add Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184352529, "stop": 1754184352529}, {"name": "Click on the Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184352529, "stop": 1754184352529}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184352975, "stop": 1754184352975}, {"name": "Add Batch List Management item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184354492, "stop": 1754184354492}, {"name": "Click on the Batch List Management item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184354492, "stop": 1754184354492}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184355154, "stop": 1754184355154}, {"name": "Add ListExplorer item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184356700, "stop": 1754184356700}, {"name": "Click on the ListExplorer item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184356700, "stop": 1754184356700}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184357546, "stop": 1754184357546}, {"name": "Add ListSetManager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184359087, "stop": 1754184359087}, {"name": "Click on the ListSetManager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184359087, "stop": 1754184359087}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184359762, "stop": 1754184359762}, {"name": "Add List Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184361266, "stop": 1754184361266}, {"name": "Click on the List Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184361267, "stop": 1754184361267}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184361854, "stop": 1754184361854}, {"name": "Add File Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184363407, "stop": 1754184363407}, {"name": "Click on the File Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184363407, "stop": 1754184363407}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184364060, "stop": 1754184364060}, {"name": "Add Advanced Settings item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184365540, "stop": 1754184365540}, {"name": "Click on the Advanced Settings item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184365540, "stop": 1754184365540}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184366215, "stop": 1754184366215}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184367853, "stop": 1754184367853}, {"name": "Accept <PERSON><PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184368601, "stop": 1754184368601}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184370786, "stop": 1754184370786}, {"name": "Select Module Rights.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184371655, "stop": 1754184371655}, {"name": "Processing module 'SWIFT Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184373656, "stop": 1754184373656}, {"name": "Click on SWIFT Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184373656, "stop": 1754184373656}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184374971, "stop": 1754184374971}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184375413, "stop": 1754184375413}, {"name": "Processing module 'Detection Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184379068, "stop": 1754184379068}, {"name": "Click on Detection Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184379068, "stop": 1754184379068}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184380288, "stop": 1754184380288}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184380757, "stop": 1754184380757}, {"name": "Processing module 'Scan Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184383380, "stop": 1754184383380}, {"name": "Click on Scan Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184383380, "stop": 1754184383380}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184384798, "stop": 1754184384798}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184385931, "stop": 1754184385931}, {"name": "Processing module 'Format Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184388429, "stop": 1754184388429}, {"name": "Click on Format Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184388429, "stop": 1754184388429}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184389437, "stop": 1754184389437}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184389770, "stop": 1754184389770}, {"name": "Processing module 'Name Checker'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184392362, "stop": 1754184392362}, {"name": "Click on Name Checker", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184392362, "stop": 1754184392362}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184393954, "stop": 1754184393954}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184394494, "stop": 1754184394494}, {"name": "Processing module 'ListExplorer'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184398556, "stop": 1754184398556}, {"name": "Click on ListExplorer", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184398556, "stop": 1754184398556}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184399822, "stop": 1754184399822}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184400270, "stop": 1754184400270}, {"name": "Processing module 'ListSetManager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184404077, "stop": 1754184404077}, {"name": "Click on ListSetManager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184404077, "stop": 1754184404077}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184405872, "stop": 1754184405872}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184406257, "stop": 1754184406257}, {"name": "Processing module 'List Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184409379, "stop": 1754184409379}, {"name": "Click on List Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184409379, "stop": 1754184409379}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184410577, "stop": 1754184410577}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184410957, "stop": 1754184410957}, {"name": "Processing module 'File Scanner'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184413694, "stop": 1754184413694}, {"name": "Click on File Scanner", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184413694, "stop": 1754184413694}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184417331, "stop": 1754184417331}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184417621, "stop": 1754184417621}, {"name": "Processing module 'Advanced Settings'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184419882, "stop": 1754184419882}, {"name": "Click on Advanced Settings", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184419882, "stop": 1754184419882}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184421155, "stop": 1754184421155}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184421752, "stop": 1754184421752}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184423885, "stop": 1754184423885}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184424298, "stop": 1754184424298}, {"name": "Check if profile exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184426550, "stop": 1754184426550}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184426550, "stop": 1754184426550}, {"name": "Set name = Test-Profile-303302491 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184432803, "stop": 1754184432803}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184434089, "stop": 1754184434089}, {"name": "Profile exist = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184435063, "stop": 1754184435063}, {"name": "Create second group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184435063, "stop": 1754184435063}, {"name": "Group test Data = Group{id=0, name='selenium-random-group-387362909', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-303302491', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone759176895', displayName='Zone (759176895)', description='Zone created with random number '759176895''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-991059541', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone187240047', displayName='Zone (187240047)', description='Zone created with random number '187240047''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184435063, "stop": 1754184435063}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184486733, "stop": 1754184486733}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='selenium-user-991059541'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184486733, "stop": 1754184486733}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184486746, "stop": 1754184486746}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184486746, "stop": 1754184486746}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184486747, "stop": 1754184486747}, {"name": "Login with User Name = selenium-user-991059541 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184486990, "stop": 1754184486990}, {"name": "Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-801279215', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@79b9a1d7, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-801279215', officialDate='null', entry=[ListEntry{type='null', name='EntryName-801279215', firstName='EntryFirstName-801279215', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-801279215', firstName='EntryFirstName-801279215', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184492947, "stop": 1754184492947}, {"name": "Connect to Database and Check if User Profile = Test-Profile-303302491 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184492949, "stop": 1754184492949}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184492949, "stop": 1754184492949}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184492985, "stop": 1754184492985}, {"name": "SELECT ID FROM tProfiles Where NAME ='Test-Profile-303302491' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184492985, "stop": 1754184492985}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184492988, "stop": 1754184492988}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184492988, "stop": 1754184492988}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184492988, "stop": 1754184492988}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184493006, "stop": 1754184493006}, {"name": "Delete From tListSetProfile where profile_id in (169)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184493006, "stop": 1754184493006}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184493009, "stop": 1754184493009}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184493009, "stop": 1754184493009}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184493009, "stop": 1754184493009}, {"name": "Number of effected rows = 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184493009, "stop": 1754184493009}, {"name": "Search for list by listName = ListName-801279215 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184517833, "stop": 1754184517833}, {"name": "Set zone : Zone (759176895)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184528727, "stop": 1754184528727}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184531382, "stop": 1754184531382}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184532517, "stop": 1754184532517}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184533038, "stop": 1754184533038}, {"name": "Set template name = templateName-801279215", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184533300, "stop": 1754184533300}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184535948, "stop": 1754184535948}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184537665, "stop": 1754184537665}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184539339, "stop": 1754184539339}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184540338, "stop": 1754184540338}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184565898, "stop": 1754184565898}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184565898, "stop": 1754184565898}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184621002, "stop": 1754184621002}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184621002, "stop": 1754184621002}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184621002, "stop": 1754184621002}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184621031, "stop": 1754184621031}, {"name": "SELECT ID FROM tProfiles Where NAME ='Test-Profile-303302491'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184621031, "stop": 1754184621031}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184621053, "stop": 1754184621053}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184621053, "stop": 1754184621053}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184621053, "stop": 1754184621053}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184637958, "stop": 1754184637958}, {"name": "RJE File Content= {1:F01PTSABEMMAXXX0000000001}{2:I564ACCLFRP1XXXXN}{3:{108:ENT144037000001}}{4:\r\n:16R:GENL\r\n:20C::CORP//2015033100010560\r\n:20C::SEME//0105601900000002\r\n:23G:EntryName-801279215, EntryFirstName-801279215\r\n:22F::CAEV//DVCA\r\n:22F::CAMV//MAND\r\n:98A::PREP//20150331\r\n:25D::PROC//COMP\r\n:16R:LINK\r\n:13A::LINK//564\r\n:20C::PREV//0105601900000001\r\n:16S:LINK\r\n:16S:GENL\r\n:16R:USECU\r\n:35B:ISIN DE0005190003\r\nBMW Bayerische Motoren Werke AG\r\n:16R:ACCTINFO\r\n:97A::SAFE//000000190003\r\n:93B::ELIG//UNIT/360,\r\n:93B::PEND//UNIT/360,\r\n:93B::PENR//UNIT/0,\r\n:93B::SETT//UNIT/360,\r\n:16S:ACCTINFO\r\n:16S:USECU\r\n:16R:CADETL\r\n:98A::XDTE//20150514\r\n:98A::RDTE//20150515\r\n:22F::DIVI//FINL\r\n:16S:CADETL\r\n:16R:CAOPTN\r\n:13A::CAON//001\r\n:22F::CAOP//CASH\r\n:17B::DFLT//Y\r\n:16R:CASHMOVE\r\n:22H::CRDB//CRED\r\n:98A::PAYD//20150514\r\n:92A::TAXR//26,375\r\n:92F::GRSS//EUR2,9\r\n:16S:CASHMOVE\r\n:16S:CAOPTN\r\n-}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184647237, "stop": 1754184647237}, {"name": "Start coping data from sample file to another file to be used.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184647237, "stop": 1754184647237}, {"name": "Successfully wrote to the file.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184647239, "stop": 1754184647239}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184649553, "stop": 1754184649553}, {"name": "Validation message = File sent to the server for processing with id [3039]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184664575, "stop": 1754184664575}, {"name": "Alert Message = File sent to the server for processing with id [3039]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184664575, "stop": 1754184664575}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184665912, "stop": 1754184665912}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184675435, "stop": 1754184675435}, {"name": "Detection ID = 7753", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184676465, "stop": 1754184676465}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184681854, "stop": 1754184681854}, {"name": "Check if user can Release detection. ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184683669, "stop": 1754184683669}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754184690148, "stop": 1754184690148}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "Operator{id=0, loginName='null', firstName='null', lastName='null', password='null', zone=null}"}], "start": 1754183915665, "stop": 1754184701728}