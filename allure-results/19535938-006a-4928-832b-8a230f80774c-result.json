{"uuid": "19535938-006a-4928-832b-8a230f80774c", "historyId": "9b88bc9c6b0563030d5e65aee334b553", "fullName": "eastnets.screening.regression.detectionmanager.DetectionManager_TC002.detectionManager_TC002", "labels": [{"name": "package", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC002"}, {"name": "testClass", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC002"}, {"name": "testMethod", "value": "detectionManager_TC002"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Detection Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC002"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-3(38)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Detection Manager"}], "links": [], "name": "Verify that user is able to perform a Don't Know action on an alert created from 'Name Checker' from the 'Alerts Section'", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to perform a Don't Know action on an alert created from 'Name Checker' from the 'Alerts Section'", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242209167, "stop": 1754242209167}, {"name": "update tConfig set variable_value = 'Yes' where variable_name = 'Change detection status to DontKnow'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242209168, "stop": 1754242209168}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242209177, "stop": 1754242209177}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242209177, "stop": 1754242209177}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242209177, "stop": 1754242209177}, {"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-881618238', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@25a9dde8, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-881618238', officialDate='null', entry=[ListEntry{type='null', name='EntryName-881618238', firstName='EntryFirstName-881618238', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-881618238', firstName='EntryFirstName-881618238', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242209181, "stop": 1754242209181}, {"name": "Connect to Database and Check if User Profile = full-right-profile_05 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242209181, "stop": 1754242209181}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242209181, "stop": 1754242209181}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242209236, "stop": 1754242209236}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_05' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242209236, "stop": 1754242209236}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242209238, "stop": 1754242209238}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242209238, "stop": 1754242209238}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242209239, "stop": 1754242209239}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242209264, "stop": 1754242209264}, {"name": "Delete From tListSetProfile where profile_id in (8)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242209264, "stop": 1754242209264}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242209268, "stop": 1754242209268}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242209268, "stop": 1754242209268}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242209269, "stop": 1754242209269}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242209269, "stop": 1754242209269}, {"name": "Search for list by listName = ListName-881618238 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242236951, "stop": 1754242236951}, {"name": "Set zone : Common Zone 05", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242267156, "stop": 1754242267156}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242269509, "stop": 1754242269509}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242270351, "stop": 1754242270351}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242271143, "stop": 1754242271143}, {"name": "Set template name = templateName-881618238", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242271985, "stop": 1754242271985}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242274904, "stop": 1754242274904}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242275870, "stop": 1754242275870}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242277487, "stop": 1754242277487}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242278462, "stop": 1754242278462}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242404119, "stop": 1754242404119}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242404119, "stop": 1754242404119}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242498373, "stop": 1754242498373}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242498373, "stop": 1754242498373}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242498374, "stop": 1754242498374}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242498409, "stop": 1754242498409}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_05'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242498409, "stop": 1754242498409}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242498412, "stop": 1754242498412}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242498412, "stop": 1754242498412}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242498413, "stop": 1754242498413}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242521335, "stop": 1754242521335}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242539589, "stop": 1754242539589}, {"name": "Status for the scanned name = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242559708, "stop": 1754242559708}, {"name": "Detection ID for the scanned name = 8093", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242560220, "stop": 1754242560220}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242566262, "stop": 1754242566262}, {"name": "Detection Status = REPDK", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242577606, "stop": 1754242577606}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754242209135, "stop": 1754242577931}