{"uuid": "a163d039-f63f-473e-a76f-a672dd7f27cf", "historyId": "6df301c24ed98d16a8094d58f9dd5749", "fullName": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC022.listManager_TC022", "labels": [{"name": "package", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC022"}, {"name": "testClass", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC022"}, {"name": "testMethod", "value": "listManager_TC022"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "List Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC022"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-1(36)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "List Manager"}], "links": [], "name": "Verify GPI Lables as conditions and Add SERV_TYPE_ID (SLA_ID) and UETR as violation filter and make sure that the detection has External status.", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify GPI Lables as conditions and Add SERV_TYPE_ID (SLA_ID) and UETR as violation filter and make sure that the detection has External status.", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267298786, "stop": 1754267298786}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-02'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267298786, "stop": 1754267298786}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267298792, "stop": 1754267298792}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267298792, "stop": 1754267298792}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267298793, "stop": 1754267298793}, {"name": "Login with User Name = operator-02 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267298947, "stop": 1754267298947}, {"name": "Create swift-code, black list and list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267304993, "stop": 1754267304993}, {"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-130343732', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@7a5a44ca, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-130343732', officialDate='null', entry=[ListEntry{type='null', name='EntryName-130343732', firstName='EntryFirstName-130343732', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-130343732', firstName='EntryFirstName-130343732', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267304998, "stop": 1754267304998}, {"name": "Connect to Database and Check if User Profile = full-right-profile_002 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267304998, "stop": 1754267304998}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267304998, "stop": 1754267304998}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267305062, "stop": 1754267305062}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_002' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267305062, "stop": 1754267305062}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267305065, "stop": 1754267305065}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267305066, "stop": 1754267305066}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267305066, "stop": 1754267305066}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267305095, "stop": 1754267305095}, {"name": "Delete From tListSetProfile where profile_id in (5)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267305095, "stop": 1754267305095}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267305107, "stop": 1754267305107}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267305107, "stop": 1754267305107}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267305107, "stop": 1754267305107}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267305107, "stop": 1754267305107}, {"name": "Search for list by listName = ListName-130343732 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267328528, "stop": 1754267328528}, {"name": "Set zone : Common Zone 02", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267342368, "stop": 1754267342368}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267344674, "stop": 1754267344674}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267345437, "stop": 1754267345437}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267347233, "stop": 1754267347233}, {"name": "Set template name = templateName-130343732", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267347512, "stop": 1754267347512}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267349945, "stop": 1754267349945}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267350664, "stop": 1754267350664}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267405635, "stop": 1754267405635}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267406652, "stop": 1754267406652}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267464049, "stop": 1754267464049}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267464049, "stop": 1754267464049}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267550190, "stop": 1754267550190}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267550190, "stop": 1754267550190}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267550190, "stop": 1754267550190}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267550222, "stop": 1754267550222}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_002'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267550222, "stop": 1754267550222}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267550224, "stop": 1754267550224}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267550224, "stop": 1754267550224}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267550224, "stop": 1754267550224}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267578767, "stop": 1754267578767}, {"name": "Validation message = Syntax is valid!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267618834, "stop": 1754267618834}, {"name": "Validation message = Syntax is valid!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267618834, "stop": 1754267618834}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267623235, "stop": 1754267623235}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267623235, "stop": 1754267623235}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267623239, "stop": 1754267623239}, {"name": "Validation message = File sent to the server for processing with id [3197]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267638313, "stop": 1754267638313}, {"name": "Alert Message = File sent to the server for processing with id [3197]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267638313, "stop": 1754267638313}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267638823, "stop": 1754267638823}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267649061, "stop": 1754267649061}, {"name": "Detection ID = 8426", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267650048, "stop": 1754267650048}, {"name": "Status detection = External", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267650486, "stop": 1754267650486}, {"name": "Unmatched roles = SERV_TYPE_ID <> '001'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267652521, "stop": 1754267652521}, {"name": "Validation message = Syntax is valid!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267678857, "stop": 1754267678857}, {"name": "Validation message = Syntax is valid!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267678857, "stop": 1754267678857}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267681884, "stop": 1754267681884}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267681884, "stop": 1754267681884}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267681884, "stop": 1754267681884}, {"name": "Validation message = File sent to the server for processing with id [3199]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267696399, "stop": 1754267696399}, {"name": "Alert Message = File sent to the server for processing with id [3199]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267696399, "stop": 1754267696399}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267696911, "stop": 1754267696911}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267706088, "stop": 1754267706088}, {"name": "Detection ID = 8427", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267706838, "stop": 1754267706838}, {"name": "Status detection = External", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267707323, "stop": 1754267707323}, {"name": "Unmatched roles = UETR <> '8E0FE365-A88E-426B-8484-4FB7FEE92742'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267708977, "stop": 1754267708977}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754267298200, "stop": 1754267711907}