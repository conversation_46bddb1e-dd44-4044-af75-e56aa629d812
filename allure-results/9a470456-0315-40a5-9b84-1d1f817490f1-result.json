{"uuid": "9a470456-0315-40a5-9b84-1d1f817490f1", "historyId": "4d979c84bd4ce7b37b23b4bbd0bd2896", "fullName": "eastnets.admin.AdminTest.checkProfilePermissions", "labels": [{"name": "package", "value": "eastnets.admin.AdminTest"}, {"name": "testClass", "value": "eastnets.admin.AdminTest"}, {"name": "testMethod", "value": "checkProfilePermissions"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Admin Test Cases"}, {"name": "subSuite", "value": "eastnets.admin.AdminTest"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-1(36)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}], "links": [], "name": "Verify that user is not able to Enable a Profile by cloning when 'Allow to enable profile' permission is not granted", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is not able to Enable a Profile by cloning when 'Allow to enable profile' permission is not granted", "steps": [{"name": "Connect to database to remove Link between profile = Test-Profile-505104524 and Permission Allow to enable profile", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150238904, "stop": 1754150238904}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150238928, "stop": 1754150238928}, {"name": "SELECT P.NAME as PR<PERSON><PERSON><PERSON>_NAME , PR.PROFILE_ID as PRO<PERSON>LE_ID , PR.GRANT_ID as GRANT_ID , PR.GRANT_RIGHT_ID as GRANT_RIGHT_ID , PR.GRANT_TYPE_ID as GRANT_TYPE_ID   FROM tModuleFunctions M   LEFT JOIN tProfileRights PR On PR.GRANT_ID  = M.ID     LEFT Join tProfiles P ON P.id =PR.PROFILE_ID   WHERE DESCRIPTION ='Allow to enable profile' and P.NAME = 'Test-Profile-505104524'  and PR.GRANT_TYPE_ID =3", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150238928, "stop": 1754150238928}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150238945, "stop": 1754150238945}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150238946, "stop": 1754150238946}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150238946, "stop": 1754150238946}, {"name": "Delete function permission 'Allow to enable profile' from DataBase", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150238946, "stop": 1754150238946}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150238972, "stop": 1754150238972}, {"name": "DELETE FROM tProfileRights  WHERE GRANT_ID  ='11' and PROFILE_ID ='152' and GRANT_TYPE_ID =3", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150238972, "stop": 1754150238972}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150238992, "stop": 1754150238992}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150238992, "stop": 1754150238992}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150238993, "stop": 1754150238993}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150239393, "stop": 1754150239393}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='selenium-user-412049628'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150239393, "stop": 1754150239393}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150239400, "stop": 1754150239400}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150239400, "stop": 1754150239400}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150239401, "stop": 1754150239401}, {"name": "Login with User Name = selenium-user-412049628 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150239540, "stop": 1754150239540}, {"name": "Check if user can enable profile.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150266171, "stop": 1754150266171}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150272269, "stop": 1754150272269}, {"name": " INSERT INTO tProfileRights (PROFILE_ID, GRANT_ID, GRANT_RIGHT_ID, GRANT_TYPE_ID) VALUES (152, 11, 3, 3);", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150272269, "stop": 1754150272269}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150272280, "stop": 1754150272280}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150272280, "stop": 1754150272280}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150272284, "stop": 1754150272284}, {"name": "Operator logout.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754150272284, "stop": 1754150272284}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "Profile{id=0, name='selenium-full-right-profile ', enabled=false, writeRight=false, description='null', zone=null, profileRights=null}"}], "start": 1754150238904, "stop": 1754150272285}