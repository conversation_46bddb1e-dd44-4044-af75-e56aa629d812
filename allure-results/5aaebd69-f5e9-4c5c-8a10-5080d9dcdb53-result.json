{"uuid": "5aaebd69-f5e9-4c5c-8a10-5080d9dcdb53", "historyId": "bfe4d6fcc3bc864ba24fc615baaa542c", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC012.scanManager_TC012", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC012"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC012"}, {"name": "testMethod", "value": "scanManager_TC012"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC012"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is able to create an Alert for a scanned Arabic name", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to create an Alert for a scanned Arabic name", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235914696, "stop": 1754235914696}, {"name": "Status for the scanned name = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235930394, "stop": 1754235930394}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754235914696, "stop": 1754235930394}