{"uuid": "d3cc550c-1980-43ec-afa7-f704d3afbb1e", "historyId": "f4615273215bae2c01bb2bdc818f02da", "fullName": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC002.listManager_TC002", "labels": [{"name": "package", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC002"}, {"name": "testClass", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC002"}, {"name": "testMethod", "value": "listManager_TC002"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "List Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC002"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-1(36)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "List Manager"}, {"name": "feature", "value": "Good Guy"}], "links": [], "name": "Verify that user is able to delete a Good Guy", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to delete a Good Guy", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754263623527, "stop": 1754263623527}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-02'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754263623527, "stop": 1754263623527}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754263623536, "stop": 1754263623536}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754263623536, "stop": 1754263623536}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754263623537, "stop": 1754263623537}, {"name": "Login with User Name = operator-02 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754263623899, "stop": 1754263623900}, {"name": "Validation message = Good guy successfully created!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754263671696, "stop": 1754263671696}, {"name": "Validation message = Good guy successfully created! .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754263671696, "stop": 1754263671696}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754263622711, "stop": 1754263679888}