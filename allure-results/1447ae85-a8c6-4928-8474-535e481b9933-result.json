{"uuid": "1447ae85-a8c6-4928-8474-535e481b9933", "historyId": "********************************", "fullName": "eastnets.screening.regression.formatmanager.FormatManager_TC001.formatManager_TC001", "labels": [{"name": "package", "value": "eastnets.screening.regression.formatmanager.FormatManager_TC001"}, {"name": "testClass", "value": "eastnets.screening.regression.formatmanager.FormatManager_TC001"}, {"name": "testMethod", "value": "formatManager_TC001"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Format Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.formatmanager.FormatManager_TC001"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-3(38)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON><PERSON>"}, {"name": "tag", "value": "Regression"}], "links": [], "name": "Verify that user is able to Import, Export, Edit and Delete a format of \"Scan\" type", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to Import, Export, Edit and Delete a format of \"Scan\" type", "steps": [{"name": "response.getStatus()200", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235593762, "stop": 1754235593763}, {"name": "UTF-8", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235593763, "stop": 1754235593763}, {"name": "1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235593763, "stop": 1754235593763}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "Format{testCaseTitle='Verify that user is able to Import, Export, Edit and Delete a format of \"Scan\" type', name='Format_%s', zone='null', type='Scan', fieldDelimiter='Separator', separator=',', entryType='null', xpath='null', fields=[FormatField{name='FIRST_NAME_%s', type='FIRST_NAME', xpath='null', scan=false, addToContext=false}, FormatField{name='LAST_NAME_%s', type='LAST_NAME', xpath='null', scan=false, addToContext=false}]}"}], "start": 1754235513359, "stop": 1754235636493}