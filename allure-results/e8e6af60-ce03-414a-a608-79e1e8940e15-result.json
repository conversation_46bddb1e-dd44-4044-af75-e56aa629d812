{"uuid": "e8e6af60-ce03-414a-a608-79e1e8940e15", "historyId": "c6142a835c74d3e0aa41bd07b46dcd7d", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC005.scanManager_TC005", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC005"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC005"}, {"name": "testMethod", "value": "scanManager_TC005"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC005"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is able to Print an Alert created from 'File Scan' from Result Tab", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to Print an Alert created from 'File Scan' from Result Tab", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172704159, "stop": 1754172704159}, {"name": "Validation message = File sent to the server for processing with id [2975]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172741499, "stop": 1754172741499}, {"name": "Alert Message = File sent to the server for processing with id [2975]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172741500, "stop": 1754172741500}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172744297, "stop": 1754172744297}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172754052, "stop": 1754172754052}, {"name": "Detection ID = 7563", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172755753, "stop": 1754172755754}, {"name": "Start Exporting Violation With Print Scope all And Document Type PDF", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172755769, "stop": 1754172755769}, {"name": "Click on close dialog button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172765628, "stop": 1754172765628}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754172703046, "stop": 1754172797297}