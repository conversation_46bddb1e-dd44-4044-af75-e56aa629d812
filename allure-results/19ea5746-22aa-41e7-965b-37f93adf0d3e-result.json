{"uuid": "19ea5746-22aa-41e7-965b-37f93adf0d3e", "historyId": "35f85912395adc0ac69cdfc6a9bd434b", "fullName": "eastnets.screening.regression.formatmanager.FormatManager_TC002.formatManager_TC002", "labels": [{"name": "package", "value": "eastnets.screening.regression.formatmanager.FormatManager_TC002"}, {"name": "testClass", "value": "eastnets.screening.regression.formatmanager.FormatManager_TC002"}, {"name": "testMethod", "value": "formatManager_TC002"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Format Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.formatmanager.FormatManager_TC002"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-3(38)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON><PERSON>"}, {"name": "tag", "value": "Regression"}], "links": [], "name": "verify that the unmatched rule appears in the detection details from the results tab", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Format manager - Additional context variables \n* verify that the additional context variables appear when adding a good guy\n* verify the out of context result based on the violation filter added\n* verify the suspected record selection in drop down list\n* verify that the unmatched rule appears in the detection details from the results tab", "steps": [{"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-175861227', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@12a7d024, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-175861227', officialDate='null', entry=[ListEntry{type='null', name='EntryName-175861227', firstName='EntryFirstName-175861227', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-175861227', firstName='EntryFirstName-175861227', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264431943, "stop": 1754264431943}, {"name": "Connect to Database and Check if User Profile = full-right-profile_06 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264431944, "stop": 1754264431944}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264431945, "stop": 1754264431945}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264431998, "stop": 1754264431998}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_06' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264431998, "stop": 1754264431998}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264432001, "stop": 1754264432001}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264432001, "stop": 1754264432001}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264432002, "stop": 1754264432002}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264432025, "stop": 1754264432025}, {"name": "Delete From tListSetProfile where profile_id in (9)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264432025, "stop": 1754264432025}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264432029, "stop": 1754264432029}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264432029, "stop": 1754264432029}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264432029, "stop": 1754264432029}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264432029, "stop": 1754264432029}, {"name": "Search for list by listName = ListName-175861227 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264453520, "stop": 1754264453521}, {"name": "Set zone : Common Zone 06", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264464955, "stop": 1754264464955}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264467235, "stop": 1754264467235}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264467852, "stop": 1754264467852}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264468653, "stop": 1754264468653}, {"name": "Set template name = templateName-175861227", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264468982, "stop": 1754264468982}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264471641, "stop": 1754264471641}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264472496, "stop": 1754264472496}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264473913, "stop": 1754264473913}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264474625, "stop": 1754264474625}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264499041, "stop": 1754264499041}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264499041, "stop": 1754264499041}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264555384, "stop": 1754264555384}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264555384, "stop": 1754264555384}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264555384, "stop": 1754264555384}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264555411, "stop": 1754264555411}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_06'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264555411, "stop": 1754264555411}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264555415, "stop": 1754264555415}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264555416, "stop": 1754264555416}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264555416, "stop": 1754264555416}, {"name": "Validation message = Syntax is valid!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264584090, "stop": 1754264584090}, {"name": "Validation message = Syntax is valid!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264584091, "stop": 1754264584091}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264588371, "stop": 1754264588371}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264588371, "stop": 1754264588371}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264605774, "stop": 1754264605774}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264645632, "stop": 1754264645632}, {"name": "Validation message = File sent to the server for processing with id [3169]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264661583, "stop": 1754264661583}, {"name": "Alert Message = File sent to the server for processing with id [3169]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264661583, "stop": 1754264661583}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264663200, "stop": 1754264663200}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264672256, "stop": 1754264672256}, {"name": "Detection ID = 8308", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264673839, "stop": 1754264673839}, {"name": "Actual validation Message = Good guy successfully created!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264683291, "stop": 1754264683291}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264683852, "stop": 1754264683852}, {"name": "Validation message = File sent to the server for processing with id [3170]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264697603, "stop": 1754264697603}, {"name": "Alert Message = File sent to the server for processing with id [3170]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264697603, "stop": 1754264697603}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264699234, "stop": 1754264699234}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264708577, "stop": 1754264708577}, {"name": "Detection ID = 8314", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264709679, "stop": 1754264709679}, {"name": "Status detection = Accepted", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264709900, "stop": 1754264709900}, {"name": "Status detection = External", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264709972, "stop": 1754264709972}, {"name": "Status detection = Reported", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264710038, "stop": 1754264710038}, {"name": "Status detection = Accepted", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264710341, "stop": 1754264710341}, {"name": "Status detection = External", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264712803, "stop": 1754264712803}, {"name": "Status detection = Reported", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264712969, "stop": 1754264712969}, {"name": "Status detection = Accepted", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264713265, "stop": 1754264713265}, {"name": "Status detection = External", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264713413, "stop": 1754264713413}, {"name": "Status detection = Reported", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264713475, "stop": 1754264713475}, {"name": "Unmatched roles = CN <> '146'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264715459, "stop": 1754264715459}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754264431939, "stop": 1754264715824}