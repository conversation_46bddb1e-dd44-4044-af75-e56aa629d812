{"uuid": "b0954889-395a-44d2-be0e-040d203f58f3", "historyId": "597ed8afc9675e588c6db9010bd36f72", "fullName": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC016.listManager_TC016", "labels": [{"name": "package", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC016"}, {"name": "testClass", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC016"}, {"name": "testMethod", "value": "listManager_TC016"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "List Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC016"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-1(36)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "List Manager"}], "links": [], "name": "Verify that user is able to search for a List from different zone", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to search for a List from different zone", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236525061, "stop": 1754236525061}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='4eyes01'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236525061, "stop": 1754236525061}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236525069, "stop": 1754236525069}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236525069, "stop": 1754236525069}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236525070, "stop": 1754236525070}, {"name": "Login with User Name = 4eyes01 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236525218, "stop": 1754236525218}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236529696, "stop": 1754236529696}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236529724, "stop": 1754236529724}, {"name": "select id from tZones tz where tz.DISPLAY_NAME  = 'Four Eyes'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236529724, "stop": 1754236529724}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236529726, "stop": 1754236529726}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236529726, "stop": 1754236529726}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236529727, "stop": 1754236529727}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236529753, "stop": 1754236529753}, {"name": "DELETE  FROM tSetContent   WHERE id IN ( SELECT tsc.id FROM tSetContent tsc  LEFT JOIN tBlackListVersions tblv on tblv.id =tsc.black_list_id  LEFT JOIN tListSets tls on tls.id = tsc.list_set_id  LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id  WHERE tbl.zone_id ='15'  AND tbl.display_name ='UN Democratic Republic of Congo')", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236529753, "stop": 1754236529753}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236529767, "stop": 1754236529767}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236529767, "stop": 1754236529767}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236529768, "stop": 1754236529768}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236529768, "stop": 1754236529768}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236529791, "stop": 1754236529791}, {"name": "DELETE  FROM tGoodGuys  WHERE id IN ( SELECT tgg.id  FROM tGoodGuys tgg LEFT JOIN tBlackListVersions tblv on tblv.id =tgg.black_list_id LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id WHERE tbl.display_name ='UN Democratic Republic of Congo')", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236529791, "stop": 1754236529791}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236529809, "stop": 1754236529809}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236529810, "stop": 1754236529810}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236529810, "stop": 1754236529810}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236529810, "stop": 1754236529810}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236529834, "stop": 1754236529834}, {"name": "DELETE FROM tBlackLists WHERE display_name ='UN Democratic Republic of Congo'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236529834, "stop": 1754236529834}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236529951, "stop": 1754236529951}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236529952, "stop": 1754236529952}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236529952, "stop": 1754236529952}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236529952, "stop": 1754236529952}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236529982, "stop": 1754236529982}, {"name": "select id from tZones tz where tz.DISPLAY_NAME  = 'Four Eyes'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236529982, "stop": 1754236529982}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236529984, "stop": 1754236529984}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236529985, "stop": 1754236529985}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236529985, "stop": 1754236529985}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236530011, "stop": 1754236530011}, {"name": "DELETE  FROM tSetContent   WHERE id IN ( SELECT tsc.id FROM tSetContent tsc  LEFT JOIN tBlackListVersions tblv on tblv.id =tsc.black_list_id  LEFT JOIN tListSets tls on tls.id = tsc.list_set_id  LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id  WHERE tbl.zone_id ='15'  AND tbl.display_name ='UN Yemen')", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236530011, "stop": 1754236530011}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236530027, "stop": 1754236530027}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236530027, "stop": 1754236530027}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236530027, "stop": 1754236530027}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236530027, "stop": 1754236530027}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236530050, "stop": 1754236530050}, {"name": "DELETE  FROM tGoodGuys  WHERE id IN ( SELECT tgg.id  FROM tGoodGuys tgg LEFT JOIN tBlackListVersions tblv on tblv.id =tgg.black_list_id LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id WHERE tbl.display_name ='UN Yemen')", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236530050, "stop": 1754236530050}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236530064, "stop": 1754236530064}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236530064, "stop": 1754236530064}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236530065, "stop": 1754236530065}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236530065, "stop": 1754236530065}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236530088, "stop": 1754236530088}, {"name": "DELETE FROM tBlackLists WHERE display_name ='UN Yemen'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236530088, "stop": 1754236530088}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236530149, "stop": 1754236530149}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236530149, "stop": 1754236530149}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236530150, "stop": 1754236530150}, {"name": "Import 'UN Democratic Republic of Congo' Black List on user linked on 'Four Eyes' zone", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236530150, "stop": 1754236530150}, {"name": "Share 'UN Democratic Republic of Congo' black list", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236544177, "stop": 1754236544177}, {"name": "Validation Message = null", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236549562, "stop": 1754236549562}, {"name": "Import 'UN Yemen' Black List on user linked on 'Four Eyes' zone", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236549562, "stop": 1754236549562}, {"name": "Log out and login with another user linked to 'Four Eyes' zone", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236563133, "stop": 1754236563133}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236565358, "stop": 1754236565358}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='4eyes02'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236565358, "stop": 1754236565358}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236565364, "stop": 1754236565364}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236565365, "stop": 1754236565365}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236565365, "stop": 1754236565365}, {"name": "Login with User Name = 4eyes02 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236565526, "stop": 1754236565526}, {"name": "Verify that user linked with 'Four Eyes' zone have access to 'UN Democratic Republic of Congo' shared black list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236569911, "stop": 1754236569911}, {"name": "Verify that user linked with 'Four Eyes' zone have access to 'UN Yemen' non-shared black list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236576821, "stop": 1754236576821}, {"name": "Log out and login with another user linked to 'Default Zone' zone", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236583326, "stop": 1754236583326}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236585378, "stop": 1754236585378}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='def-operator-1'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236585378, "stop": 1754236585378}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236585385, "stop": 1754236585385}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236585385, "stop": 1754236585385}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236585385, "stop": 1754236585385}, {"name": "Login with User Name = def-operator-1 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236585527, "stop": 1754236585527}, {"name": "Verify that user linked with 'Default Zone' zone have access to 'UN Democratic Republic of Congo' shared black list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236589860, "stop": 1754236589860}, {"name": "Verify that user linked with 'Default Zone' zone not have access to 'UN Yemen' non-shared black list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236597020, "stop": 1754236597020}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754236524682, "stop": 1754236605909}