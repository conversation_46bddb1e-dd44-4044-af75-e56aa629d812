{"uuid": "7d7a1a0a-3ccf-42a4-bbd4-d3e591f489b6", "historyId": "53af5d7e30c68caa1bc374572fd0445f", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC008.scanManager_TC008", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC008"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC008"}, {"name": "testMethod", "value": "scanManager_TC008"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC008"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is able to add an entity as <PERSON> Guy from the results tab for a scanned file with Custom Format type.", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to add an entity as <PERSON> Guy from the results tab for a scanned file with Custom Format type.", "steps": [{"name": "RJE File Content= EntryName1, EntryName1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241901876, "stop": 1754241901876}, {"name": "Start coping data from sample file to another file to be used.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241901876, "stop": 1754241901876}, {"name": "Successfully wrote to the file.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241901887, "stop": 1754241901887}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241943332, "stop": 1754241943332}, {"name": "Validation message = File sent to the server for processing with id [3128]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241956617, "stop": 1754241956617}, {"name": "Alert Message = File sent to the server for processing with id [3128]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241956620, "stop": 1754241956620}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241957057, "stop": 1754241957057}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241966666, "stop": 1754241966666}, {"name": "Detection ID = 8076", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241968133, "stop": 1754241968133}, {"name": "Actual validation Message = Good guy successfully created!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241972264, "stop": 1754241972264}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "FileScan{filePath='/src/test/resources/testDataFiles/fileScanTD/ScanFile.txt', format='Custom Format File', encoding='null', enList=null, result='null', rank='null', detectVessels='true', detectCountries='true', createAlertsAutomatically='false'}"}], "start": 1754241901099, "stop": 1754241972917}