{"uuid": "15db007e-c346-47c3-80a3-e5d5af2d4aee", "historyId": "583875c3ae2abf009aabaef23a400aca", "fullName": "eastnets.screening.regression.detectionmanager.DetectionManager_TC017.detectionManager_TC017", "labels": [{"name": "package", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC017"}, {"name": "testClass", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC017"}, {"name": "testMethod", "value": "detectionManager_TC017"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Detection Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC017"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-3(38)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Detection Manager"}], "links": [], "name": "detectionManager_TC017", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754264720198, "stop": 1754264720198}