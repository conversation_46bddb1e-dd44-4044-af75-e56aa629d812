{"uuid": "c83be710-40bc-407d-8766-514816e86a2f", "historyId": "1b302ab994f3a74eda774af2b989ccad", "fullName": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC011.listManager_TC011", "labels": [{"name": "package", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC011"}, {"name": "testClass", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC011"}, {"name": "testMethod", "value": "listManager_TC011"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "List Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC011"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-1(36)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "List Manager"}], "links": [], "name": "Verify that user is able to Import a shared Good Guys", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to Import a shared Good Guys", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264634471, "stop": 1754264634471}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-02'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264634472, "stop": 1754264634472}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264634482, "stop": 1754264634482}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264634482, "stop": 1754264634482}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264634483, "stop": 1754264634483}, {"name": "Login with User Name = operator-02 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264634627, "stop": 1754264634627}, {"name": "Create swift-code, black list and list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264639581, "stop": 1754264639581}, {"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-314300692', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@2d6dcea1, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-314300692', officialDate='null', entry=[ListEntry{type='null', name='EntryName-314300692', firstName='EntryFirstName-314300692', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-314300692', firstName='EntryFirstName-314300692', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264639586, "stop": 1754264639586}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264639587, "stop": 1754264639587}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264639626, "stop": 1754264639626}, {"name": "select id from tZones tz where tz.DISPLAY_NAME  = 'Common Zone 02'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264639626, "stop": 1754264639626}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264639629, "stop": 1754264639629}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264639629, "stop": 1754264639629}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264639630, "stop": 1754264639630}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264639662, "stop": 1754264639662}, {"name": "DELETE  FROM tSetContent   WHERE id IN ( SELECT tsc.id FROM tSetContent tsc  LEFT JOIN tBlackListVersions tblv on tblv.id =tsc.black_list_id  LEFT JOIN tListSets tls on tls.id = tsc.list_set_id  LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id  WHERE tbl.zone_id ='4'  AND tbl.display_name ='UN Democratic People Republic of Korea')", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264639662, "stop": 1754264639662}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264639683, "stop": 1754264639683}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264639683, "stop": 1754264639683}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264639684, "stop": 1754264639684}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264639684, "stop": 1754264639684}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264639716, "stop": 1754264639716}, {"name": "DELETE  FROM tGoodGuys  WHERE id IN ( SELECT tgg.id  FROM tGoodGuys tgg LEFT JOIN tBlackListVersions tblv on tblv.id =tgg.black_list_id LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id WHERE tbl.display_name ='UN Democratic People Republic of Korea')", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264639718, "stop": 1754264639718}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264639735, "stop": 1754264639735}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264639735, "stop": 1754264639735}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264639736, "stop": 1754264639736}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264639736, "stop": 1754264639736}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264639760, "stop": 1754264639760}, {"name": "DELETE FROM tBlackLists WHERE display_name ='UN Democratic People Republic of Korea'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264639760, "stop": 1754264639760}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264639958, "stop": 1754264639958}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264639958, "stop": 1754264639958}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264639959, "stop": 1754264639959}, {"name": "Connect to Database and Check if User Profile = full-right-profile_002 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264639959, "stop": 1754264639959}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264639960, "stop": 1754264639960}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264640011, "stop": 1754264640011}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_002' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264640011, "stop": 1754264640011}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264640013, "stop": 1754264640013}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264640013, "stop": 1754264640013}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264640015, "stop": 1754264640015}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264640044, "stop": 1754264640044}, {"name": "Delete From tListSetProfile where profile_id in (5)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264640044, "stop": 1754264640044}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264640049, "stop": 1754264640049}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264640049, "stop": 1754264640049}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264640050, "stop": 1754264640050}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264640050, "stop": 1754264640050}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264640050, "stop": 1754264640050}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264640091, "stop": 1754264640091}, {"name": "select id from tZones tz where tz.DISPLAY_NAME  = 'Common Zone 02'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264640091, "stop": 1754264640091}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264640095, "stop": 1754264640095}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264640095, "stop": 1754264640095}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264640096, "stop": 1754264640096}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264640129, "stop": 1754264640129}, {"name": "DELETE  FROM tSetContent   WHERE id IN ( SELECT tsc.id FROM tSetContent tsc  LEFT JOIN tBlackListVersions tblv on tblv.id =tsc.black_list_id  LEFT JOIN tListSets tls on tls.id = tsc.list_set_id  LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id  WHERE tbl.zone_id ='4'  AND tbl.display_name ='UN Democratic People Republic of Korea')", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264640129, "stop": 1754264640129}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264640150, "stop": 1754264640150}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264640150, "stop": 1754264640150}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264640150, "stop": 1754264640150}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264640152, "stop": 1754264640152}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264640177, "stop": 1754264640177}, {"name": "DELETE  FROM tGoodGuys  WHERE id IN ( SELECT tgg.id  FROM tGoodGuys tgg LEFT JOIN tBlackListVersions tblv on tblv.id =tgg.black_list_id LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id WHERE tbl.display_name ='UN Democratic People Republic of Korea')", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264640177, "stop": 1754264640177}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264640194, "stop": 1754264640194}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264640194, "stop": 1754264640194}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264640195, "stop": 1754264640195}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264640195, "stop": 1754264640195}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264640224, "stop": 1754264640224}, {"name": "DELETE FROM tBlackLists WHERE display_name ='UN Democratic People Republic of Korea'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264640224, "stop": 1754264640224}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264640272, "stop": 1754264640272}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264640272, "stop": 1754264640272}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264640272, "stop": 1754264640272}, {"name": "Set zone : Common Zone 02", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264657516, "stop": 1754264657516}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264659739, "stop": 1754264659739}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264660757, "stop": 1754264660757}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264661580, "stop": 1754264661580}, {"name": "Set template name = templateName-314300692", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264661940, "stop": 1754264661940}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264664971, "stop": 1754264664971}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264665903, "stop": 1754264665903}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264667077, "stop": 1754264667077}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264667909, "stop": 1754264667909}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264708119, "stop": 1754264708119}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264708119, "stop": 1754264708119}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264783685, "stop": 1754264783685}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264783685, "stop": 1754264783685}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264783685, "stop": 1754264783685}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264783710, "stop": 1754264783710}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_002'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264783710, "stop": 1754264783710}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264783712, "stop": 1754264783712}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264783712, "stop": 1754264783712}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264783713, "stop": 1754264783713}, {"name": "Share 'UN Democratic People Republic of Korea' black list", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264786917, "stop": 1754264786918}, {"name": "Validation Message = null", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264797539, "stop": 1754264797539}, {"name": "Validation message = Good guy successfully created!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264923145, "stop": 1754264923145}, {"name": "Validation message = Good guy successfully created! .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264923145, "stop": 1754264923145}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264943875, "stop": 1754264943875}, {"name": "Validation message = Operation completed successfully. .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264943875, "stop": 1754264943875}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754264633909, "stop": 1754264955261}