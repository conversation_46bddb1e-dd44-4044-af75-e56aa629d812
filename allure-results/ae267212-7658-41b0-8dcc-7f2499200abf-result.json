{"uuid": "ae267212-7658-41b0-8dcc-7f2499200abf", "historyId": "3d751625f604896615514d81374b99e9", "fullName": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC007.listManager_TC007", "labels": [{"name": "package", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC007"}, {"name": "testClass", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC007"}, {"name": "testMethod", "value": "listManager_TC007"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "List Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC007"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-1(36)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "List Manager"}], "links": [], "name": "Verify that user is able to Lock a Public Black List version", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to Lock a Public Black List version", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141680223, "stop": 1754141680223}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-02'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141680223, "stop": 1754141680223}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141680230, "stop": 1754141680230}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141680230, "stop": 1754141680230}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141680231, "stop": 1754141680231}, {"name": "Login with User Name = operator-02 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141680405, "stop": 1754141680405}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141685888, "stop": 1754141685888}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141685935, "stop": 1754141685935}, {"name": "select id from tZones tz where tz.DISPLAY_NAME  = 'Common Zone 02'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141685935, "stop": 1754141685935}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141685938, "stop": 1754141685939}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141685939, "stop": 1754141685939}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141685939, "stop": 1754141685939}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141685977, "stop": 1754141685977}, {"name": "DELETE  FROM tSetContent   WHERE id IN ( SELECT tsc.id FROM tSetContent tsc  LEFT JOIN tBlackListVersions tblv on tblv.id =tsc.black_list_id  LEFT JOIN tListSets tls on tls.id = tsc.list_set_id  LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id  WHERE tbl.zone_id ='4'  AND tbl.display_name ='UN Democratic Republic of Congo')", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141685977, "stop": 1754141685977}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141686007, "stop": 1754141686007}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141686007, "stop": 1754141686007}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141686007, "stop": 1754141686007}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141686007, "stop": 1754141686007}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141686046, "stop": 1754141686046}, {"name": "DELETE  FROM tGoodGuys  WHERE id IN ( SELECT tgg.id  FROM tGoodGuys tgg LEFT JOIN tBlackListVersions tblv on tblv.id =tgg.black_list_id LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id WHERE tbl.display_name ='UN Democratic Republic of Congo')", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141686047, "stop": 1754141686047}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141686066, "stop": 1754141686066}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141686067, "stop": 1754141686067}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141686067, "stop": 1754141686067}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141686067, "stop": 1754141686067}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141686099, "stop": 1754141686099}, {"name": "DELETE FROM tBlackLists WHERE display_name ='UN Democratic Republic of Congo'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141686099, "stop": 1754141686099}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141686363, "stop": 1754141686363}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141686363, "stop": 1754141686363}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141686363, "stop": 1754141686363}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754141679749, "stop": 1754141799310}