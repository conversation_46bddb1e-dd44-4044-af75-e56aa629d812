{"uuid": "9b353ccf-c85a-453f-a8cd-3d47e5a2a5ac", "historyId": "611287d4ba722d52884fa2cb990e164b", "fullName": "eastnets.screening.regression.detectionmanager.DetectionManager_TC004.detectionManager_TC004", "labels": [{"name": "package", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC004"}, {"name": "testClass", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC004"}, {"name": "testMethod", "value": "detectionManager_TC004"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Detection Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC004"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Detection Manager"}], "links": [], "name": "detectionManager_TC004", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754142445236, "stop": 1754142445237}