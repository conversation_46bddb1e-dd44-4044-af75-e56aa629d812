{"uuid": "6d234a5b-b4b6-42c0-8215-4fab0a9fa562", "historyId": "cb7f934a0c930919d77eab3430b69264", "fullName": "eastnets.screening.regression.detectionmanager.DetectionManager_TC015.detectionManager_TC015", "labels": [{"name": "package", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC015"}, {"name": "testClass", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC015"}, {"name": "testMethod", "value": "detectionManager_TC015"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Detection Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC015"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Detection Manager"}], "links": [], "name": "detectionManager_TC015", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754173489377, "stop": 1754173489377}