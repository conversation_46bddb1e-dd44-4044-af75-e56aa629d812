{"uuid": "cafa9aa7-99e3-4983-8a6b-bd0a29fdcac5", "historyId": "********************************", "fullName": "eastnets.screening.regression.iso20022configurations.ISO20022_TC005.iso20022_TC0012", "labels": [{"name": "package", "value": "eastnets.screening.regression.iso20022configurations.ISO20022_TC005"}, {"name": "testClass", "value": "eastnets.screening.regression.iso20022configurations.ISO20022_TC005"}, {"name": "testMethod", "value": "iso20022_TC0012"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "ISO20022 Configurations Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.iso20022configurations.ISO20022_TC005"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-3(38)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "tag", "value": "ISO20022"}, {"name": "tag", "value": "Regression"}, {"name": "owner", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "links": [], "name": "Verify that the user can import the exported custom ISO20022 XSD", "status": "failed", "statusDetails": {"known": false, "muted": false, "flaky": false, "message": "Test Failed due to an exception occurred in POM's method while adding ISO message expected [New message [pacs.009.001] with version [pacs.009.001.08] added successfully.] but found [This message already exist!\"]", "trace": "java.lang.AssertionError: Test Failed due to an exception occurred in POM's method while adding ISO message expected [New message [pacs.009.001] with version [pacs.009.001.08] added successfully.] but found [This message already exist!\"]\r\n\tat org.testng.Assert.fail(Assert.java:110)\r\n\tat org.testng.Assert.failNotEquals(Assert.java:1413)\r\n\tat org.testng.Assert.assertEqualsImpl(Assert.java:149)\r\n\tat org.testng.Assert.assertEquals(Assert.java:131)\r\n\tat org.testng.Assert.assertEquals(Assert.java:655)\r\n\tat core.ISOTestMethods.addISOMessage(ISOTestMethods.java:83)\r\n\tat eastnets.screening.regression.iso20022configurations.ISO20022_TC005.iso20022_TC0012(ISO20022_TC005.java:100)\r\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:580)\r\n\tat org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:677)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:221)\r\n\tat org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:50)\r\n\tat org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:969)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:194)\r\n\tat org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148)\r\n\tat org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128)\r\n\tat java.base/java.util.ArrayList.forEach(ArrayList.java:1596)\r\n\tat org.testng.TestRunner.privateRun(TestRunner.java:829)\r\n\tat org.testng.TestRunner.run(TestRunner.java:602)\r\n\tat org.testng.SuiteRunner.runTest(SuiteRunner.java:437)\r\n\tat org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:475)\r\n\tat org.testng.internal.thread.ThreadUtil.lambda$execute$0(ThreadUtil.java:58)\r\n\tat java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)\r\n\tat java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)\r\n\tat java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)\r\n\tat java.base/java.lang.Thread.run(Thread.java:1583)\r\n"}, "stage": "finished", "description": "Verify that the user can import the exported custom ISO20022 XSD", "steps": [{"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-394695479', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@2a9d3908, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-394695479', officialDate='null', entry=[ListEntry{type='null', name='EntryName-394695479', firstName='EntryFirstName-394695479', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-394695479', firstName='EntryFirstName-394695479', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172596378, "stop": 1754172596378}, {"name": "Navigate to By.xpath: //a[contains(text(),'ISO20022 Format Configuration')]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172598435, "stop": 1754172598435}, {"name": "Navigate to By.xpath: //a[contains(text(),'ISO20022 Format Configuration')]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172601958, "stop": 1754172601958}, {"name": "Create New Group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172607012, "stop": 1754172607012}, {"name": "Connect to Database and Check if User Profile = full-right-profile_07 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172615268, "stop": 1754172615268}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172615269, "stop": 1754172615269}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172615320, "stop": 1754172615321}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_07' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172615321, "stop": 1754172615321}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172615326, "stop": 1754172615326}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172615326, "stop": 1754172615326}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172615328, "stop": 1754172615328}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172615363, "stop": 1754172615363}, {"name": "Delete From tListSetProfile where profile_id in (10)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172615363, "stop": 1754172615363}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172615368, "stop": 1754172615368}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172615368, "stop": 1754172615368}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172615369, "stop": 1754172615369}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172615369, "stop": 1754172615369}, {"name": "Search for list by listName = ListName-394695479 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172640356, "stop": 1754172640356}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172675849, "stop": 1754172675849}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172675849, "stop": 1754172675849}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172744824, "stop": 1754172744824}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172744824, "stop": 1754172744824}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172744824, "stop": 1754172744824}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172744843, "stop": 1754172744843}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_07'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172744843, "stop": 1754172744843}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172744848, "stop": 1754172744848}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 17***********, "stop": 17***********}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 17***********, "stop": 17***********}, {"name": "Navigate to By.xpath: //a[contains(text(),'ISO20022 Schema Configuration')]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172747824, "stop": 1754172747824}, {"name": "Navigate to By.xpath: //a[@href='#iso20022:homepage_business:tabViewListManager:tabView:schemaConfiguration']", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172748685, "stop": 1754172748685}, {"name": "Validation message = The schema already exist!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172752575, "stop": 1754172752575}, {"name": "Navigate to By.xpath: //a[contains(text(),'ISO20022 Format Configuration')]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172755048, "stop": 1754172755048}, {"name": "Add new message.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172756303, "stop": 1754172756303}, {"name": "Navigate to By.xpath: //a[contains(text(),'ISO20022 Format Configuration')]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172759101, "stop": 1754172759101}, {"name": "Sort table results by creation to click on the latest created group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172760557, "stop": 1754172760557}, {"name": "Validation message = This message already exist!\"", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172785725, "stop": 1754172785725}, {"name": "Actual result = This message already exist!\"", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172785725, "stop": 1754172785725}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "ISO20022FormatConfiguration{zone='Zone (867793082)', groupName='Group_Name-628631568', iso20022SchemaConfiguration=ISO20022SchemaConfiguration{schemaName='pacs.009.001', schemaVersion='08', headerSwift='head.001.001.01', expectedResults='Schema version [%s] successfully deleted'}, expectedResults='Successfully imported schema [pacs.009.001] with version [08].'}"}], "start": 1754172596370, "stop": 1754172785725}