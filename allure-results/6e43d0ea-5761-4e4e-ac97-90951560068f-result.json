{"uuid": "6e43d0ea-5761-4e4e-ac97-90951560068f", "historyId": "c1c14de74bb43bbf5f8d10a287df71e", "fullName": "eastnets.admin.AdminTest.checkPermissionWhenAssignSecGroupProfileZone", "labels": [{"name": "package", "value": "eastnets.admin.AdminTest"}, {"name": "testClass", "value": "eastnets.admin.AdminTest"}, {"name": "testMethod", "value": "checkPermissionWhenAssignSecGroupProfileZone"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Admin Test Cases"}, {"name": "subSuite", "value": "eastnets.admin.AdminTest"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}], "links": [], "name": "Verify that the user that is assigned to Group, Profile, and Zone have permission to Block but don't have the permission to Release is able to release when this user is assigned to a second Group, Profile and Zone that have the permission to Release.", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that the user that is assigned to Group, Profile, and Zone have permission to Block but don't have the permission to Release is able to release when this user is assigned to a second Group, Profile and Zone that have the permission to Release.", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267761526, "stop": 1754267761526}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='sysadmin'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267761526, "stop": 1754267761526}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267761535, "stop": 1754267761535}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267761535, "stop": 1754267761535}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267761535, "stop": 1754267761535}, {"name": "Login with User Name = sysadmin and Password = manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267761704, "stop": 1754267761704}, {"name": "Operator test data = Operator{id=0, loginName='selenium-user-164660462', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone191217542', displayName='Zone (191217542)', description='Zone created with random number '191217542''}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267767063, "stop": 1754267767063}, {"name": "Group test data = Group{id=0, name='selenium-random-group-291352373', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-94441445', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone191217542', displayName='Zone (191217542)', description='Zone created with random number '191217542''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-164660462', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone191217542', displayName='Zone (191217542)', description='Zone created with random number '191217542''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267767064, "stop": 1754267767064}, {"name": "Create new zone .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267767064, "stop": 1754267767064}, {"name": "Zone test Data = Zone{id=0, name='Zone191217542', displayName='Zone (191217542)', description='Zone created with random number '191217542''}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267767064, "stop": 1754267767064}, {"name": "Check if zone with name = 'Zone191217542' exists", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267769106, "stop": 1754267769106}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267769380, "stop": 1754267769380}, {"name": "Enter name =Zone191217542", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267774990, "stop": 1754267774990}, {"name": "Enter display Name =Zone (191217542)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267776075, "stop": 1754267776075}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267776897, "stop": 1754267776897}, {"name": "Zone doesn't exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267777849, "stop": 1754267777849}, {"name": "Click add new zone button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267777849, "stop": 1754267777849}, {"name": "Set create new zone data.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267778133, "stop": 1754267778133}, {"name": "Set name = Zone191217542", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267778133, "stop": 1754267778133}, {"name": "Set display name = Zone (191217542)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267780081, "stop": 1754267780081}, {"name": "Set description = Zone created with random number '191217542'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267802316, "stop": 1754267802316}, {"name": "Capture zone id from UI = 163", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267804115, "stop": 1754267804115}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267804115, "stop": 1754267804115}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267805990, "stop": 1754267805990}, {"name": "Enter name =Zone191217542", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267811467, "stop": 1754267811467}, {"name": "Enter display Name =Zone (191217542)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267812476, "stop": 1754267812476}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267813457, "stop": 1754267813457}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267816719, "stop": 1754267816719}, {"name": "Enter name =Zone191217542", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267822111, "stop": 1754267822111}, {"name": "Enter display Name =Zone (191217542)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267822952, "stop": 1754267822952}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267823968, "stop": 1754267823968}, {"name": "Create first profile .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267824870, "stop": 1754267824870}, {"name": "Profile test Data = Profile{id=0, name='Test-Profile-94441445', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone191217542', displayName='Zone (191217542)', description='Zone created with random number '191217542''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267824871, "stop": 1754267824871}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267827386, "stop": 1754267827386}, {"name": "Set name = Test-Profile-94441445 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267832990, "stop": 1754267832990}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267833858, "stop": 1754267833858}, {"name": "Click add new profile button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267834918, "stop": 1754267834918}, {"name": "Set name = Test-Profile-94441445 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267835244, "stop": 1754267835244}, {"name": "Set description = Random profile for Selenium testing ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267839744, "stop": 1754267839744}, {"name": "Check enable checkbox to be true ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267840666, "stop": 1754267840666}, {"name": "Check write right checkbox to be Test-Profile-94441445 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267841541, "stop": 1754267841541}, {"name": "Select zone = Test-Profile-94441445 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267841925, "stop": 1754267841925}, {"name": "Add SWS item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267855110, "stop": 1754267855110}, {"name": "Click on the SWS item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267855110, "stop": 1754267855110}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267855652, "stop": 1754267855652}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267856621, "stop": 1754267856621}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267856927, "stop": 1754267856927}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267857044, "stop": 1754267857044}, {"name": "Set name = Test-Profile-94441445 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267864029, "stop": 1754267864029}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267864899, "stop": 1754267864899}, {"name": "Select profile from result view.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267865838, "stop": 1754267865838}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267866481, "stop": 1754267866481}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267868083, "stop": 1754267868083}, {"name": "Add en.Reporting Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267869564, "stop": 1754267869564}, {"name": "Click on the en.Reporting Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267869564, "stop": 1754267869564}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267870562, "stop": 1754267870562}, {"name": "Add SWIFT Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267872001, "stop": 1754267872001}, {"name": "Click on the SWIFT Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267872001, "stop": 1754267872001}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267872458, "stop": 1754267872458}, {"name": "Add SWP Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267873956, "stop": 1754267873956}, {"name": "Click on the SWP Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267873957, "stop": 1754267873957}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267874491, "stop": 1754267874491}, {"name": "Add Detection Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267875986, "stop": 1754267875986}, {"name": "Click on the Detection Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267875986, "stop": 1754267875986}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267876736, "stop": 1754267876736}, {"name": "Add Scan Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267878152, "stop": 1754267878152}, {"name": "Click on the Scan Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267878152, "stop": 1754267878152}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267878670, "stop": 1754267878670}, {"name": "Add Format Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267880195, "stop": 1754267880195}, {"name": "Click on the Format Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267880195, "stop": 1754267880195}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267880911, "stop": 1754267880911}, {"name": "Add Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267882446, "stop": 1754267882446}, {"name": "Click on the Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267882446, "stop": 1754267882446}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267882919, "stop": 1754267882919}, {"name": "Add Batch List Management item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267884476, "stop": 1754267884476}, {"name": "Click on the Batch List Management item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267884476, "stop": 1754267884476}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267885125, "stop": 1754267885125}, {"name": "Add ListExplorer item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267886543, "stop": 1754267886543}, {"name": "Click on the ListExplorer item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267886543, "stop": 1754267886543}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267887665, "stop": 1754267887665}, {"name": "Add ListSetManager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267889098, "stop": 1754267889098}, {"name": "Click on the ListSetManager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267889098, "stop": 1754267889098}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267889638, "stop": 1754267889638}, {"name": "Add List Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267891089, "stop": 1754267891089}, {"name": "Click on the List Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267891089, "stop": 1754267891089}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267891742, "stop": 1754267891742}, {"name": "Add File Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267893259, "stop": 1754267893259}, {"name": "Click on the File Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267893259, "stop": 1754267893259}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267893728, "stop": 1754267893728}, {"name": "Add Advanced Settings item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267895140, "stop": 1754267895140}, {"name": "Click on the Advanced Settings item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267895140, "stop": 1754267895140}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267895690, "stop": 1754267895690}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267897174, "stop": 1754267897174}, {"name": "Accept <PERSON><PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267898078, "stop": 1754267898078}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267900472, "stop": 1754267900472}, {"name": "Select Module Rights.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267901366, "stop": 1754267901366}, {"name": "Processing module 'SWIFT Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267903370, "stop": 1754267903370}, {"name": "Click on SWIFT Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267903370, "stop": 1754267903370}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267904476, "stop": 1754267904476}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267904765, "stop": 1754267904765}, {"name": "Processing module 'Detection Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267908084, "stop": 1754267908084}, {"name": "Click on Detection Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267908084, "stop": 1754267908084}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267909511, "stop": 1754267909511}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267909977, "stop": 1754267909977}, {"name": "Processing module 'Scan Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267912603, "stop": 1754267912603}, {"name": "Click on Scan Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267912603, "stop": 1754267912603}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267913610, "stop": 1754267913610}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267914092, "stop": 1754267914092}, {"name": "Processing module 'Format Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267916598, "stop": 1754267916598}, {"name": "Click on Format Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267916598, "stop": 1754267916598}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267917552, "stop": 1754267917552}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267917895, "stop": 1754267917895}, {"name": "Processing module 'Name Checker'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267920209, "stop": 1754267920209}, {"name": "Click on Name Checker", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267920209, "stop": 1754267920209}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267921374, "stop": 1754267921374}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267921767, "stop": 1754267921767}, {"name": "Processing module 'ListExplorer'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267925173, "stop": 1754267925173}, {"name": "Click on ListExplorer", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267925173, "stop": 1754267925173}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267926152, "stop": 1754267926152}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267926661, "stop": 1754267926661}, {"name": "Processing module 'ListSetManager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267929165, "stop": 1754267929165}, {"name": "Click on ListSetManager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267929165, "stop": 1754267929165}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267930123, "stop": 1754267930123}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267930592, "stop": 1754267930592}, {"name": "Processing module 'List Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267933261, "stop": 1754267933261}, {"name": "Click on List Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267933261, "stop": 1754267933261}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267934279, "stop": 1754267934279}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267934856, "stop": 1754267934856}, {"name": "Processing module 'File Scanner'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267938641, "stop": 1754267938641}, {"name": "Click on File Scanner", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267938641, "stop": 1754267938641}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267939647, "stop": 1754267939647}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267939988, "stop": 1754267939988}, {"name": "Processing module 'Advanced Settings'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267942345, "stop": 1754267942345}, {"name": "Click on Advanced Settings", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267942345, "stop": 1754267942345}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267943419, "stop": 1754267943419}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267943855, "stop": 1754267943855}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267945243, "stop": 1754267945243}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267945563, "stop": 1754267945563}, {"name": "Check if profile exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267947439, "stop": 1754267947439}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267947439, "stop": 1754267947439}, {"name": "Set name = Test-Profile-94441445 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267953792, "stop": 1754267953792}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267955246, "stop": 1754267955246}, {"name": "Profile exist = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267956593, "stop": 1754267956593}, {"name": "Create new operator.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267956593, "stop": 1754267956593}, {"name": "Operator test Data = Operator{id=0, loginName='selenium-user-164660462', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone191217542', displayName='Zone (191217542)', description='Zone created with random number '191217542''}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267956593, "stop": 1754267956593}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267959770, "stop": 1754267959770}, {"name": "Set login name = selenium-user-164660462", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267965916, "stop": 1754267965916}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267966942, "stop": 1754267966942}, {"name": "Click Create new operator button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267968064, "stop": 1754267968064}, {"name": "Set login name = selenium-user-164660462", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267968412, "stop": 1754267968412}, {"name": "Set first name = Random", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267970468, "stop": 1754267970468}, {"name": "Set last name = User", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267971760, "stop": 1754267971760}, {"name": "Set password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267972590, "stop": 1754267972590}, {"name": "Set confirm password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267973679, "stop": 1754267973679}, {"name": "Set enable flag = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267974644, "stop": 1754267974644}, {"name": "Set reset flag = false", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267975473, "stop": 1754267975473}, {"name": "Select zone  = Zone (191217542)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267977000, "stop": 1754267977000}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267984502, "stop": 1754267984502}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267984939, "stop": 1754267984939}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267992992, "stop": 1754267992992}, {"name": "Set login name = selenium-user-164660462", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267998362, "stop": 1754267998362}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754267999527, "stop": 1754267999527}, {"name": "Create first group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268001036, "stop": 1754268001036}, {"name": "Group test Data = Group{id=0, name='selenium-random-group-291352373', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-94441445', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone191217542', displayName='Zone (191217542)', description='Zone created with random number '191217542''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-164660462', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone191217542', displayName='Zone (191217542)', description='Zone created with random number '191217542''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268001036, "stop": 1754268001036}, {"name": "Connect to database to remove Link between profile = Test-Profile-94441445 and Permission Allow to add attachments to detection", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268059187, "stop": 1754268059187}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268059220, "stop": 1754268059220}, {"name": "SELECT P.NAME as PR<PERSON><PERSON><PERSON>_NAME , PR.PROFILE_ID as PROFILE_ID , PR.GRANT_ID as GRANT_ID , PR.GRANT_RIGHT_ID as GRANT_RIGHT_ID , PR.GRANT_TYPE_ID as GRANT_TYPE_ID   FROM tModuleFunctions M   LEFT JOIN tProfileRights PR On PR.GRANT_ID  = M.ID     LEFT Join tProfiles P ON P.id =PR.PROFILE_ID   WHERE DESCRIPTION ='Allow to add attachments to detection' and P.NAME = 'Test-Profile-94441445'  and PR.GRANT_TYPE_ID =3", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268059220, "stop": 1754268059220}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268059235, "stop": 1754268059235}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268059236, "stop": 1754268059236}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268059236, "stop": 1754268059236}, {"name": "Delete function permission 'Allow to add attachments to detection' from DataBase", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268059236, "stop": 1754268059236}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268059265, "stop": 1754268059265}, {"name": "DELETE FROM tProfileRights  WHERE GRANT_ID  ='137' and PROFILE_ID ='191' and GRANT_TYPE_ID =3", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268059266, "stop": 1754268059266}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268059286, "stop": 1754268059286}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268059286, "stop": 1754268059286}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268059286, "stop": 1754268059286}, {"name": "Create second zone .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268059287, "stop": 1754268059287}, {"name": "Zone test Data = Zone{id=0, name='Zone195919833', displayName='Zone (195919833)', description='Zone created with random number '195919833''}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268059287, "stop": 1754268059287}, {"name": "Check if zone with name = 'Zone195919833' exists", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268061912, "stop": 1754268061912}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268062456, "stop": 1754268062456}, {"name": "Enter name =Zone195919833", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268071579, "stop": 1754268071579}, {"name": "Enter display Name =Zone (195919833)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268072341, "stop": 1754268072341}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268073248, "stop": 1754268073248}, {"name": "Zone doesn't exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268073878, "stop": 1754268073878}, {"name": "Click add new zone button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268073878, "stop": 1754268073878}, {"name": "Set create new zone data.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268074141, "stop": 1754268074141}, {"name": "Set name = Zone195919833", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268074141, "stop": 1754268074141}, {"name": "Set display name = Zone (195919833)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268075823, "stop": 1754268075823}, {"name": "Set description = Zone created with random number '195919833'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268076542, "stop": 1754268076542}, {"name": "Capture zone id from UI = 164", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268077792, "stop": 1754268077792}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268077792, "stop": 1754268077792}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268079292, "stop": 1754268079292}, {"name": "Enter name =Zone195919833", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268084718, "stop": 1754268084718}, {"name": "Enter display Name =Zone (195919833)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268085621, "stop": 1754268085621}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268086653, "stop": 1754268086653}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268090006, "stop": 1754268090006}, {"name": "Enter name =Zone195919833", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268095425, "stop": 1754268095425}, {"name": "Enter display Name =Zone (195919833)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268097223, "stop": 1754268097223}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268098247, "stop": 1754268098247}, {"name": "Create second profile .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268099163, "stop": 1754268099163}, {"name": "Profile test Data = Profile{id=0, name='Test-Profile-166836193', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone195919833', displayName='Zone (195919833)', description='Zone created with random number '195919833''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268099163, "stop": 1754268099163}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268101293, "stop": 1754268101293}, {"name": "Set name = Test-Profile-166836193 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268106827, "stop": 1754268106827}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268107623, "stop": 1754268107623}, {"name": "Click add new profile button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268108374, "stop": 1754268108374}, {"name": "Set name = Test-Profile-166836193 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268108635, "stop": 1754268108635}, {"name": "Set description = Random profile for Selenium testing ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268110427, "stop": 1754268110427}, {"name": "Check enable checkbox to be true ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268111254, "stop": 1754268111254}, {"name": "Check write right checkbox to be Test-Profile-166836193 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268111983, "stop": 1754268111983}, {"name": "Select zone = Test-Profile-166836193 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268113047, "stop": 1754268113047}, {"name": "Add SWS item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268121518, "stop": 1754268121518}, {"name": "Click on the SWS item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268121518, "stop": 1754268121518}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268122227, "stop": 1754268122227}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268123278, "stop": 1754268123278}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268123562, "stop": 1754268123562}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268123659, "stop": 1754268123659}, {"name": "Set name = Test-Profile-166836193 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268129926, "stop": 1754268129926}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268130766, "stop": 1754268130766}, {"name": "Select profile from result view.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268131914, "stop": 1754268131914}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268132527, "stop": 1754268132527}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268134166, "stop": 1754268134166}, {"name": "Add en.Reporting Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268135829, "stop": 1754268135829}, {"name": "Click on the en.Reporting Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268135829, "stop": 1754268135829}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268136659, "stop": 1754268136659}, {"name": "Add SWIFT Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268137943, "stop": 1754268137943}, {"name": "Click on the SWIFT Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268137943, "stop": 1754268137943}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268138419, "stop": 1754268138419}, {"name": "Add SWP Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268139955, "stop": 1754268139955}, {"name": "Click on the SWP Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268139955, "stop": 1754268139955}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268140389, "stop": 1754268140389}, {"name": "Add Detection Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268141881, "stop": 1754268141881}, {"name": "Click on the Detection Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268141881, "stop": 1754268141881}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268142783, "stop": 1754268142783}, {"name": "Add Scan Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268144243, "stop": 1754268144243}, {"name": "Click on the Scan Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268144243, "stop": 1754268144243}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268144736, "stop": 1754268144736}, {"name": "Add Format Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268146171, "stop": 1754268146171}, {"name": "Click on the Format Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268146171, "stop": 1754268146171}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268146735, "stop": 1754268146735}, {"name": "Add Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268148177, "stop": 1754268148177}, {"name": "Click on the Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268148177, "stop": 1754268148177}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268148729, "stop": 1754268148729}, {"name": "Add Batch List Management item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268150079, "stop": 1754268150079}, {"name": "Click on the Batch List Management item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268150079, "stop": 1754268150079}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268150813, "stop": 1754268150813}, {"name": "Add ListExplorer item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268152249, "stop": 1754268152249}, {"name": "Click on the ListExplorer item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268152249, "stop": 1754268152249}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268152684, "stop": 1754268152684}, {"name": "Add ListSetManager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268154128, "stop": 1754268154128}, {"name": "Click on the ListSetManager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268154128, "stop": 1754268154128}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268154582, "stop": 1754268154582}, {"name": "Add List Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268156042, "stop": 1754268156042}, {"name": "Click on the List Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268156042, "stop": 1754268156042}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268156511, "stop": 1754268156511}, {"name": "Add File Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268158012, "stop": 1754268158012}, {"name": "Click on the File Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268158012, "stop": 1754268158012}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268158744, "stop": 1754268158744}, {"name": "Add Advanced Settings item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268160264, "stop": 1754268160264}, {"name": "Click on the Advanced Settings item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268160264, "stop": 1754268160264}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268160802, "stop": 1754268160802}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268162263, "stop": 1754268162263}, {"name": "Accept <PERSON><PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268162787, "stop": 1754268162787}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268165014, "stop": 1754268165014}, {"name": "Select Module Rights.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268165821, "stop": 1754268165821}, {"name": "Processing module 'SWIFT Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268167826, "stop": 1754268167826}, {"name": "Click on SWIFT Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268167827, "stop": 1754268167827}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268170366, "stop": 1754268170366}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268170638, "stop": 1754268170638}, {"name": "Processing module 'Detection Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268174129, "stop": 1754268174129}, {"name": "Click on Detection Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268174129, "stop": 1754268174129}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268175281, "stop": 1754268175281}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268175566, "stop": 1754268175566}, {"name": "Processing module 'Scan Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268177828, "stop": 1754268177828}, {"name": "Click on Scan Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268177828, "stop": 1754268177828}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268178763, "stop": 1754268178763}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268179067, "stop": 1754268179067}, {"name": "Processing module 'Format Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268181486, "stop": 1754268181486}, {"name": "Click on Format Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268181486, "stop": 1754268181486}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268182793, "stop": 1754268182793}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268183072, "stop": 1754268183072}, {"name": "Processing module 'Name Checker'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268185338, "stop": 1754268185338}, {"name": "Click on Name Checker", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268185338, "stop": 1754268185338}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268186394, "stop": 1754268186394}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268186626, "stop": 1754268186626}, {"name": "Processing module 'ListExplorer'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268189924, "stop": 1754268189924}, {"name": "Click on ListExplorer", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268189924, "stop": 1754268189924}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268190910, "stop": 1754268190910}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268191235, "stop": 1754268191235}, {"name": "Processing module 'ListSetManager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268193622, "stop": 1754268193622}, {"name": "Click on ListSetManager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268193622, "stop": 1754268193622}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268194607, "stop": 1754268194607}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268194941, "stop": 1754268194941}, {"name": "Processing module 'List Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268197236, "stop": 1754268197236}, {"name": "Click on List Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268197236, "stop": 1754268197236}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268203383, "stop": 1754268203383}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268203764, "stop": 1754268203764}, {"name": "Processing module 'File Scanner'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268206201, "stop": 1754268206201}, {"name": "Click on File Scanner", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268206202, "stop": 1754268206202}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268207194, "stop": 1754268207194}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268207848, "stop": 1754268207848}, {"name": "Processing module 'Advanced Settings'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268210331, "stop": 1754268210331}, {"name": "Click on Advanced Settings", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268210331, "stop": 1754268210331}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268211324, "stop": 1754268211324}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268211774, "stop": 1754268211774}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268213444, "stop": 1754268213444}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268213719, "stop": 1754268213719}, {"name": "Check if profile exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268216238, "stop": 1754268216238}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268216238, "stop": 1754268216238}, {"name": "Set name = Test-Profile-166836193 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268222358, "stop": 1754268222358}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268223356, "stop": 1754268223356}, {"name": "Profile exist = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268224264, "stop": 1754268224264}, {"name": "Create second group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268224264, "stop": 1754268224264}, {"name": "Group test Data = Group{id=0, name='selenium-random-group-581531110', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-166836193', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone195919833', displayName='Zone (195919833)', description='Zone created with random number '195919833''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-164660462', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone191217542', displayName='Zone (191217542)', description='Zone created with random number '191217542''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268224265, "stop": 1754268224265}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268271058, "stop": 1754268271058}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='selenium-user-164660462'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268271058, "stop": 1754268271058}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268271065, "stop": 1754268271065}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268271066, "stop": 1754268271066}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268271066, "stop": 1754268271066}, {"name": "Login with User Name = selenium-user-164660462 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268271254, "stop": 1754268271254}, {"name": "Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-996093068', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@f066876, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-996093068', officialDate='null', entry=[ListEntry{type='null', name='EntryName-996093068', firstName='EntryFirstName-996093068', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-996093068', firstName='EntryFirstName-996093068', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268277448, "stop": 1754268277448}, {"name": "Connect to Database and Check if User Profile = Test-Profile-166836193 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268277456, "stop": 1754268277456}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268277457, "stop": 1754268277457}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268277513, "stop": 1754268277513}, {"name": "SELECT ID FROM tProfiles Where NAME ='Test-Profile-166836193' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268277513, "stop": 1754268277513}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268277516, "stop": 1754268277516}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268277516, "stop": 1754268277516}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268277516, "stop": 1754268277516}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268277540, "stop": 1754268277540}, {"name": "Delete From tListSetProfile where profile_id in (192)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268277540, "stop": 1754268277540}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268277542, "stop": 1754268277542}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268277542, "stop": 1754268277542}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268277542, "stop": 1754268277542}, {"name": "Number of effected rows = 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268277542, "stop": 1754268277542}, {"name": "Search for list by listName = ListName-996093068 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268298636, "stop": 1754268298636}, {"name": "Set zone : Zone (195919833)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268311735, "stop": 1754268311735}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268313955, "stop": 1754268313955}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268315293, "stop": 1754268315293}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268315715, "stop": 1754268315715}, {"name": "Set template name = templateName-996093068", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268315991, "stop": 1754268315991}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268318440, "stop": 1754268318440}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268319264, "stop": 1754268319264}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268321022, "stop": 1754268321022}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268321730, "stop": 1754268321730}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268393755, "stop": 1754268393755}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268393755, "stop": 1754268393755}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268469272, "stop": 1754268469273}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268469273, "stop": 1754268469273}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268469273, "stop": 1754268469273}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268469302, "stop": 1754268469302}, {"name": "SELECT ID FROM tProfiles Where NAME ='Test-Profile-166836193'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268469302, "stop": 1754268469302}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268469304, "stop": 1754268469304}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268469304, "stop": 1754268469304}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268469304, "stop": 1754268469304}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268487962, "stop": 1754268487962}, {"name": "RJE File Content= {1:F01PTSABEMMAXXX0000000001}{2:I564ACCLFRP1XXXXN}{3:{108:ENT144037000001}}{4:\r\n:16R:GENL\r\n:20C::CORP//2015033100010560\r\n:20C::SEME//0105601900000002\r\n:23G:EntryName-996093068, EntryFirstName-996093068\r\n:22F::CAEV//DVCA\r\n:22F::CAMV//MAND\r\n:98A::PREP//20150331\r\n:25D::PROC//COMP\r\n:16R:LINK\r\n:13A::LINK//564\r\n:20C::PREV//0105601900000001\r\n:16S:LINK\r\n:16S:GENL\r\n:16R:USECU\r\n:35B:ISIN DE0005190003\r\nBMW Bayerische Motoren Werke AG\r\n:16R:ACCTINFO\r\n:97A::SAFE//000000190003\r\n:93B::ELIG//UNIT/360,\r\n:93B::PEND//UNIT/360,\r\n:93B::PENR//UNIT/0,\r\n:93B::SETT//UNIT/360,\r\n:16S:ACCTINFO\r\n:16S:USECU\r\n:16R:CADETL\r\n:98A::XDTE//20150514\r\n:98A::RDTE//20150515\r\n:22F::DIVI//FINL\r\n:16S:CADETL\r\n:16R:CAOPTN\r\n:13A::CAON//001\r\n:22F::CAOP//CASH\r\n:17B::DFLT//Y\r\n:16R:CASHMOVE\r\n:22H::CRDB//CRED\r\n:98A::PAYD//20150514\r\n:92A::TAXR//26,375\r\n:92F::GRSS//EUR2,9\r\n:16S:CASHMOVE\r\n:16S:CAOPTN\r\n-}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268496359, "stop": 1754268496359}, {"name": "Start coping data from sample file to another file to be used.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268496359, "stop": 1754268496359}, {"name": "Successfully wrote to the file.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268496363, "stop": 1754268496363}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268499241, "stop": 1754268499241}, {"name": "Validation message = File sent to the server for processing with id [3204]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268515993, "stop": 1754268515993}, {"name": "Alert Message = File sent to the server for processing with id [3204]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268515993, "stop": 1754268515993}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268517473, "stop": 1754268517473}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268526041, "stop": 1754268526041}, {"name": "Detection ID = 8476", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268526825, "stop": 1754268526825}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268533303, "stop": 1754268533303}, {"name": "Check if user can Release detection. ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268534475, "stop": 1754268534475}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268539979, "stop": 1754268539979}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "Operator{id=0, loginName='null', firstName='null', lastName='null', password='null', zone=null}"}], "start": 1754267760919, "stop": 1754268549504}