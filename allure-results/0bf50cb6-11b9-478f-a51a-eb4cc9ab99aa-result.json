{"uuid": "0bf50cb6-11b9-478f-a51a-eb4cc9ab99aa", "historyId": "276bb51d9fe0a45781c719f8d9643b0a", "fullName": "eastnets.screening.regression.reports.Report_TC008.report_TC008", "labels": [{"name": "package", "value": "eastnets.screening.regression.reports.Report_TC008"}, {"name": "testClass", "value": "eastnets.screening.regression.reports.Report_TC008"}, {"name": "testMethod", "value": "report_TC008"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Report Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.reports.Report_TC008"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-3(38)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "tag", "value": "Reports"}], "links": [], "name": "Verify Compare sessions info Report", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify Compare sessions info Report", "steps": [{"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-942662082', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@6881bcd4, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-942662082', officialDate='null', entry=[ListEntry{type='null', name='EntryName-942662082', firstName='EntryFirstName-942662082', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-942662082', firstName='EntryFirstName-942662082', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754179360135, "stop": 1754179360135}, {"name": "Connect to Database and Check if User Profile = full-right-profile_08 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754179360136, "stop": 1754179360136}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754179360136, "stop": 1754179360136}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754179360174, "stop": 1754179360174}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_08' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754179360174, "stop": 1754179360174}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754179360177, "stop": 1754179360177}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754179360177, "stop": 1754179360177}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754179360177, "stop": 1754179360177}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754179360192, "stop": 1754179360192}, {"name": "Delete From tListSetProfile where profile_id in (11)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754179360192, "stop": 1754179360192}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754179360197, "stop": 1754179360197}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754179360197, "stop": 1754179360197}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754179360197, "stop": 1754179360197}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754179360197, "stop": 1754179360197}, {"name": "Search for list by listName = ListName-942662082 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754179598960, "stop": 1754179598960}, {"name": "Set zone : Common Zone 08", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754179611945, "stop": 1754179611945}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754179614336, "stop": 1754179614336}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754179615071, "stop": 1754179615071}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754179615684, "stop": 1754179615684}, {"name": "Set template name = templateName-942662082", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754179616040, "stop": 1754179616040}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754179618837, "stop": 1754179618837}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754179620002, "stop": 1754179620002}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754179621754, "stop": 1754179621754}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754179647750, "stop": 1754179647750}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754179820681, "stop": 1754179820681}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754179820681, "stop": 1754179820681}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180020442, "stop": 1754180020442}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180020443, "stop": 1754180020443}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180020443, "stop": 1754180020443}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180020465, "stop": 1754180020465}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_08'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180020465, "stop": 1754180020465}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180020469, "stop": 1754180020469}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180020469, "stop": 1754180020469}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180020469, "stop": 1754180020469}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180150891, "stop": 1754180150891}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180543840, "stop": 1754180543840}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180680061, "stop": 1754180680061}, {"name": "Validation message = File sent to the server for processing with id [3011]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180748030, "stop": 1754180748030}, {"name": "Alert Message = File sent to the server for processing with id [3011]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180748030, "stop": 1754180748030}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180774844, "stop": 1754180774844}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180810471, "stop": 1754180810471}, {"name": "Detection ID = 7635", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180812064, "stop": 1754180812064}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180812418, "stop": 1754180812418}, {"name": "Validation message = File sent to the server for processing with id [3012]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180851804, "stop": 1754180851804}, {"name": "Alert Message = File sent to the server for processing with id [3012]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180851804, "stop": 1754180851804}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180989785, "stop": 1754180989785}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181003440, "stop": 1754181003440}, {"name": "Detection ID = 7636", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181005138, "stop": 1754181005139}, {"name": "Report status is: Done", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181037539, "stop": 1754181037539}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754179360130, "stop": 1754181124863}