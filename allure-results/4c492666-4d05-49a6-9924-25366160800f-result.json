{"uuid": "4c492666-4d05-49a6-9924-25366160800f", "historyId": "823f26a334db1a4c0b3f993cd20cf2c9", "fullName": "eastnets.admin.AdminTest.checkPermissionWhenAssignSecGroupAndProfile", "labels": [{"name": "package", "value": "eastnets.admin.AdminTest"}, {"name": "testClass", "value": "eastnets.admin.AdminTest"}, {"name": "testMethod", "value": "checkPermissionWhenAssignSecGroupAndProfile"}, {"name": "parentSuite", "value": "Failed Tests Rerun Suite"}, {"name": "suite", "value": "<PERSON><PERSON> Failed Tests"}, {"name": "subSuite", "value": "eastnets.admin.AdminTest"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}], "links": [], "name": "Verify that the user that is assigned to Group and Profile have permission to Block but don't have the permission to Release is able to release \nwhen this user is assigned to a second Group and Profile that have the permission to Release.", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that the user that is assigned to Group and Profile have permission to Block but don't have the permission to Release is able to release \nwhen this user is assigned to a second Group and Profile that have the permission to Release.", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187426987, "stop": 1754187426987}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='sysadmin'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187426987, "stop": 1754187426987}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187426994, "stop": 1754187426994}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187426994, "stop": 1754187426994}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187426995, "stop": 1754187426995}, {"name": "Login with User Name = sysadmin and Password = manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187427138, "stop": 1754187427138}, {"name": "Operator test data = Operator{id=0, loginName='selenium-user-120272692', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone618122347', displayName='Zone (618122347)', description='Zone created with random number '618122347''}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187431498, "stop": 1754187431498}, {"name": "Group test data = Group{id=0, name='selenium-random-group-108037930', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-678927387', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone618122347', displayName='Zone (618122347)', description='Zone created with random number '618122347''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-120272692', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone618122347', displayName='Zone (618122347)', description='Zone created with random number '618122347''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187431498, "stop": 1754187431498}, {"name": "Create new zone .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187431498, "stop": 1754187431498}, {"name": "Zone test Data = Zone{id=0, name='Zone618122347', displayName='Zone (618122347)', description='Zone created with random number '618122347''}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187431498, "stop": 1754187431498}, {"name": "Check if zone with name = 'Zone618122347' exists", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187432805, "stop": 1754187432805}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187433025, "stop": 1754187433025}, {"name": "Enter name =Zone618122347", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187438470, "stop": 1754187438470}, {"name": "Enter display Name =Zone (618122347)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187439152, "stop": 1754187439152}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187439760, "stop": 1754187439760}, {"name": "Zone doesn't exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187440300, "stop": 1754187440300}, {"name": "Click add new zone button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187440300, "stop": 1754187440300}, {"name": "Set create new zone data.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187440568, "stop": 1754187440568}, {"name": "Set name = Zone618122347", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187440568, "stop": 1754187440568}, {"name": "Set display name = Zone (618122347)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187441763, "stop": 1754187441763}, {"name": "Set description = Zone created with random number '618122347'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187442621, "stop": 1754187442621}, {"name": "Capture zone id from UI = 150", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187443638, "stop": 1754187443639}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187443639, "stop": 1754187443639}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187444826, "stop": 1754187444826}, {"name": "Enter name =Zone618122347", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187450259, "stop": 1754187450259}, {"name": "Enter display Name =Zone (618122347)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187450878, "stop": 1754187450878}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187451500, "stop": 1754187451500}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187453804, "stop": 1754187453804}, {"name": "Enter name =Zone618122347", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187459129, "stop": 1754187459129}, {"name": "Enter display Name =Zone (618122347)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187459768, "stop": 1754187459768}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187460378, "stop": 1754187460378}, {"name": "Create first profile .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187460920, "stop": 1754187460920}, {"name": "Profile test Data = Profile{id=0, name='Test-Profile-678927387', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone618122347', displayName='Zone (618122347)', description='Zone created with random number '618122347''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187460920, "stop": 1754187460920}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187462147, "stop": 1754187462147}, {"name": "Set name = Test-Profile-678927387 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187467497, "stop": 1754187467497}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187468477, "stop": 1754187468477}, {"name": "Click add new profile button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187469317, "stop": 1754187469317}, {"name": "Set name = Test-Profile-678927387 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187469566, "stop": 1754187469566}, {"name": "Set description = Random profile for Selenium testing ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187471151, "stop": 1754187471151}, {"name": "Check enable checkbox to be true ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187471969, "stop": 1754187471969}, {"name": "Check write right checkbox to be Test-Profile-678927387 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187472701, "stop": 1754187472701}, {"name": "Select zone = Test-Profile-678927387 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187472972, "stop": 1754187472972}, {"name": "Add SWS item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187480225, "stop": 1754187480225}, {"name": "Click on the SWS item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187480225, "stop": 1754187480225}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187480762, "stop": 1754187480762}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187481757, "stop": 1754187481757}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187482133, "stop": 1754187482133}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187482245, "stop": 1754187482245}, {"name": "Set name = Test-Profile-678927387 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187488240, "stop": 1754187488240}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187488968, "stop": 1754187488968}, {"name": "Select profile from result view.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187489977, "stop": 1754187489977}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187490759, "stop": 1754187490759}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187492109, "stop": 1754187492109}, {"name": "Add en.Reporting Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187492600, "stop": 1754187492600}, {"name": "Click on the en.Reporting Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187492600, "stop": 1754187492600}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187493087, "stop": 1754187493087}, {"name": "Add SWIFT Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187494550, "stop": 1754187494550}, {"name": "Click on the SWIFT Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187494550, "stop": 1754187494550}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187495011, "stop": 1754187495011}, {"name": "Add SWP Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187496471, "stop": 1754187496471}, {"name": "Click on the SWP Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187496471, "stop": 1754187496471}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187497049, "stop": 1754187497049}, {"name": "Add Detection Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187498512, "stop": 1754187498512}, {"name": "Click on the Detection Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187498512, "stop": 1754187498512}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187499001, "stop": 1754187499001}, {"name": "Add Scan Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187500469, "stop": 1754187500469}, {"name": "Click on the Scan Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187500469, "stop": 1754187500469}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187501140, "stop": 1754187501140}, {"name": "Add Format Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187502603, "stop": 1754187502603}, {"name": "Click on the Format Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187502603, "stop": 1754187502603}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187503133, "stop": 1754187503133}, {"name": "Add Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187504627, "stop": 1754187504627}, {"name": "Click on the Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187504627, "stop": 1754187504627}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187505094, "stop": 1754187505094}, {"name": "Add Batch List Management item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187506588, "stop": 1754187506588}, {"name": "Click on the Batch List Management item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187506588, "stop": 1754187506588}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187507063, "stop": 1754187507063}, {"name": "Add ListExplorer item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187508524, "stop": 1754187508524}, {"name": "Click on the ListExplorer item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187508524, "stop": 1754187508524}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187508993, "stop": 1754187508993}, {"name": "Add ListSetManager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187510420, "stop": 1754187510420}, {"name": "Click on the ListSetManager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187510420, "stop": 1754187510420}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187510881, "stop": 1754187510881}, {"name": "Add List Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187512324, "stop": 1754187512324}, {"name": "Click on the List Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187512324, "stop": 1754187512324}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187512968, "stop": 1754187512968}, {"name": "Add File Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187514373, "stop": 1754187514373}, {"name": "Click on the File Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187514373, "stop": 1754187514373}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187514926, "stop": 1754187514926}, {"name": "Add Advanced Settings item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187516327, "stop": 1754187516327}, {"name": "Click on the Advanced Settings item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187516327, "stop": 1754187516327}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187516748, "stop": 1754187516748}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187518188, "stop": 1754187518188}, {"name": "Accept <PERSON><PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187518855, "stop": 1754187518855}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187520665, "stop": 1754187520665}, {"name": "Select Module Rights.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187521430, "stop": 1754187521430}, {"name": "Processing module 'SWIFT Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187523431, "stop": 1754187523431}, {"name": "Click on SWIFT Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187523431, "stop": 1754187523431}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187524425, "stop": 1754187524425}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187524818, "stop": 1754187524818}, {"name": "Processing module 'Detection Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187528184, "stop": 1754187528184}, {"name": "Click on Detection Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187528184, "stop": 1754187528184}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187529112, "stop": 1754187529112}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187529520, "stop": 1754187529520}, {"name": "Processing module 'Scan Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187532161, "stop": 1754187532161}, {"name": "Click on Scan Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187532161, "stop": 1754187532161}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187533402, "stop": 1754187533402}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187533681, "stop": 1754187533681}, {"name": "Processing module 'Format Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187536271, "stop": 1754187536271}, {"name": "Click on Format Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187536271, "stop": 1754187536271}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187537425, "stop": 1754187537425}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187537687, "stop": 1754187537687}, {"name": "Processing module 'Name Checker'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187540009, "stop": 1754187540009}, {"name": "Click on Name Checker", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187540009, "stop": 1754187540009}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187540951, "stop": 1754187540951}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187541343, "stop": 1754187541343}, {"name": "Processing module 'ListExplorer'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187544680, "stop": 1754187544680}, {"name": "Click on ListExplorer", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187544680, "stop": 1754187544680}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187545986, "stop": 1754187545986}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187546534, "stop": 1754187546534}, {"name": "Processing module 'ListSetManager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187549006, "stop": 1754187549006}, {"name": "Click on ListSetManager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187549006, "stop": 1754187549006}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187549962, "stop": 1754187549962}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187550248, "stop": 1754187550248}, {"name": "Processing module 'List Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187552448, "stop": 1754187552448}, {"name": "Click on List Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187552448, "stop": 1754187552448}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187553545, "stop": 1754187553545}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187553804, "stop": 1754187553804}, {"name": "Processing module 'File Scanner'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187556315, "stop": 1754187556315}, {"name": "Click on File Scanner", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187556315, "stop": 1754187556315}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187557515, "stop": 1754187557515}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187557745, "stop": 1754187557745}, {"name": "Processing module 'Advanced Settings'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187559955, "stop": 1754187559955}, {"name": "Click on Advanced Settings", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187559955, "stop": 1754187559955}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187560875, "stop": 1754187560875}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187561115, "stop": 1754187561115}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187562304, "stop": 1754187562304}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187562632, "stop": 1754187562632}, {"name": "Check if profile exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187564172, "stop": 1754187564172}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187564172, "stop": 1754187564172}, {"name": "Set name = Test-Profile-678927387 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187569965, "stop": 1754187569965}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187570665, "stop": 1754187570665}, {"name": "Profile exist = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187571390, "stop": 1754187571390}, {"name": "Create new operator.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187571390, "stop": 1754187571390}, {"name": "Operator test Data = Operator{id=0, loginName='selenium-user-120272692', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone618122347', displayName='Zone (618122347)', description='Zone created with random number '618122347''}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187571390, "stop": 1754187571390}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187573268, "stop": 1754187573268}, {"name": "Set login name = selenium-user-120272692", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187578704, "stop": 1754187578704}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187579668, "stop": 1754187579668}, {"name": "Click Create new operator button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187580349, "stop": 1754187580349}, {"name": "Set login name = selenium-user-120272692", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187580599, "stop": 1754187580599}, {"name": "Set first name = Random", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187581786, "stop": 1754187581786}, {"name": "Set last name = User", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187582407, "stop": 1754187582407}, {"name": "Set password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187583088, "stop": 1754187583088}, {"name": "Set confirm password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187583790, "stop": 1754187583790}, {"name": "Set enable flag = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187584618, "stop": 1754187584618}, {"name": "Set reset flag = false", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187585558, "stop": 1754187585558}, {"name": "Select zone  = Zone (618122347)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187586981, "stop": 1754187586981}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187594173, "stop": 1754187594173}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187594485, "stop": 1754187594485}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187601814, "stop": 1754187601814}, {"name": "Set login name = selenium-user-120272692", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187607206, "stop": 1754187607206}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187607941, "stop": 1754187607941}, {"name": "Create first group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187608672, "stop": 1754187608672}, {"name": "Group test Data = Group{id=0, name='selenium-random-group-108037930', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-678927387', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone618122347', displayName='Zone (618122347)', description='Zone created with random number '618122347''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-120272692', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone618122347', displayName='Zone (618122347)', description='Zone created with random number '618122347''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187608672, "stop": 1754187608672}, {"name": "Connect to database to remove Link between profile = Test-Profile-678927387 and Permission Allow to add attachments to detection", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187646053, "stop": 1754187646053}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187646077, "stop": 1754187646077}, {"name": "SELECT P.NAME as PR<PERSON><PERSON><PERSON>_NAME , PR.PROFILE_ID as PROFILE_ID , PR.GRANT_ID as GRANT_ID , PR.GRANT_RIGHT_ID as GRANT_RIGHT_ID , PR.GRANT_TYPE_ID as GRANT_TYPE_ID   FROM tModuleFunctions M   LEFT JOIN tProfileRights PR On PR.GRANT_ID  = M.ID     LEFT Join tProfiles P ON P.id =PR.PROFILE_ID   WHERE DESCRIPTION ='Allow to add attachments to detection' and P.NAME = 'Test-Profile-678927387'  and PR.GRANT_TYPE_ID =3", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187646077, "stop": 1754187646077}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187646087, "stop": 1754187646087}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187646087, "stop": 1754187646087}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187646087, "stop": 1754187646087}, {"name": "Delete function permission 'Allow to add attachments to detection' from DataBase", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187646087, "stop": 1754187646087}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187646103, "stop": 1754187646103}, {"name": "DELETE FROM tProfileRights  WHERE GRANT_ID  ='137' and PROFILE_ID ='173' and GRANT_TYPE_ID =3", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187646103, "stop": 1754187646103}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187646119, "stop": 1754187646119}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187646119, "stop": 1754187646119}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187646119, "stop": 1754187646119}, {"name": "Create second profile .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187646119, "stop": 1754187646119}, {"name": "Profile test Data = Profile{id=0, name='Test-Profile-388116407', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone618122347', displayName='Zone (618122347)', description='Zone created with random number '618122347''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187646120, "stop": 1754187646120}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187647286, "stop": 1754187647286}, {"name": "Set name = Test-Profile-388116407 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187652585, "stop": 1754187652585}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187653545, "stop": 1754187653545}, {"name": "Click add new profile button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187654462, "stop": 1754187654462}, {"name": "Set name = Test-Profile-388116407 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187654793, "stop": 1754187654793}, {"name": "Set description = Random profile for Selenium testing ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187656700, "stop": 1754187656700}, {"name": "Check enable checkbox to be true ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187657485, "stop": 1754187657485}, {"name": "Check write right checkbox to be Test-Profile-388116407 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187658160, "stop": 1754187658160}, {"name": "Select zone = Test-Profile-388116407 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187658390, "stop": 1754187658390}, {"name": "Add SWS item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187665652, "stop": 1754187665652}, {"name": "Click on the SWS item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187665652, "stop": 1754187665652}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187666209, "stop": 1754187666209}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187667267, "stop": 1754187667267}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187667662, "stop": 1754187667662}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187667750, "stop": 1754187667750}, {"name": "Set name = Test-Profile-388116407 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187673856, "stop": 1754187673856}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187674629, "stop": 1754187674629}, {"name": "Select profile from result view.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187675509, "stop": 1754187675509}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187676173, "stop": 1754187676173}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187677671, "stop": 1754187677671}, {"name": "Add en.Reporting Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187679145, "stop": 1754187679145}, {"name": "Click on the en.Reporting Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187679145, "stop": 1754187679145}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187679700, "stop": 1754187679700}, {"name": "Add SWIFT Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187681103, "stop": 1754187681103}, {"name": "Click on the SWIFT Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187681103, "stop": 1754187681103}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187681556, "stop": 1754187681556}, {"name": "Add SWP Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187683026, "stop": 1754187683026}, {"name": "Click on the SWP Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187683026, "stop": 1754187683026}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187683383, "stop": 1754187683383}, {"name": "Add Detection Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187684829, "stop": 1754187684829}, {"name": "Click on the Detection Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187684829, "stop": 1754187684829}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187685327, "stop": 1754187685327}, {"name": "Add Scan Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187686761, "stop": 1754187686761}, {"name": "Click on the Scan Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187686761, "stop": 1754187686761}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187687237, "stop": 1754187687237}, {"name": "Add Format Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187688644, "stop": 1754187688644}, {"name": "Click on the Format Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187688644, "stop": 1754187688644}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187689184, "stop": 1754187689184}, {"name": "Add Name Checker item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187690724, "stop": 1754187690724}, {"name": "Click on the Name Checker item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187690724, "stop": 1754187690724}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187691178, "stop": 1754187691178}, {"name": "Add Batch List Management item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187692578, "stop": 1754187692578}, {"name": "Click on the Batch List Management item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187692578, "stop": 1754187692578}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187692999, "stop": 1754187692999}, {"name": "Add ListExplorer item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187694482, "stop": 1754187694482}, {"name": "Click on the ListExplorer item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187694482, "stop": 1754187694482}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187694870, "stop": 1754187694870}, {"name": "Add ListSetManager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187696298, "stop": 1754187696298}, {"name": "Click on the ListSetManager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187696298, "stop": 1754187696298}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187696634, "stop": 1754187696634}, {"name": "Add List Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187698048, "stop": 1754187698048}, {"name": "Click on the List Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187698048, "stop": 1754187698048}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187698511, "stop": 1754187698511}, {"name": "Add File Scanner item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187700026, "stop": 1754187700026}, {"name": "Click on the File Scanner item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187700026, "stop": 1754187700026}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187700519, "stop": 1754187700519}, {"name": "Add Advanced Settings item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187702007, "stop": 1754187702007}, {"name": "Click on the Advanced Settings item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187702007, "stop": 1754187702007}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187702390, "stop": 1754187702390}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187703845, "stop": 1754187703845}, {"name": "Accept <PERSON><PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187704259, "stop": 1754187704259}, {"name": "Click on SWS", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187705777, "stop": 1754187705777}, {"name": "Select Module Rights.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187706551, "stop": 1754187706551}, {"name": "Processing module 'SWIFT Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187708552, "stop": 1754187708552}, {"name": "Click on SWIFT Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187708552, "stop": 1754187708552}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187709479, "stop": 1754187709479}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187709738, "stop": 1754187709738}, {"name": "Processing module 'Detection Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187713239, "stop": 1754187713239}, {"name": "Click on Detection Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187713239, "stop": 1754187713239}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187714187, "stop": 1754187714187}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187714492, "stop": 1754187714492}, {"name": "Processing module 'Scan Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187716712, "stop": 1754187716712}, {"name": "Click on Scan Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187716712, "stop": 1754187716712}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187717840, "stop": 1754187717840}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187718179, "stop": 1754187718179}, {"name": "Processing module 'Format Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187720488, "stop": 1754187720488}, {"name": "Click on Format Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187720488, "stop": 1754187720488}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187721505, "stop": 1754187721505}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187721844, "stop": 1754187721844}, {"name": "Processing module 'Name Checker'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187724243, "stop": 1754187724243}, {"name": "Click on Name Checker", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187724244, "stop": 1754187724244}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187725332, "stop": 1754187725332}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187725670, "stop": 1754187725670}, {"name": "Processing module 'ListExplorer'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187728914, "stop": 1754187728914}, {"name": "Click on ListExplorer", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187728914, "stop": 1754187728914}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187730045, "stop": 1754187730045}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187730306, "stop": 1754187730306}, {"name": "Processing module 'ListSetManager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187732483, "stop": 1754187732483}, {"name": "Click on ListSetManager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187732483, "stop": 1754187732483}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187733699, "stop": 1754187733699}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187734056, "stop": 1754187734056}, {"name": "Processing module 'List Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187736536, "stop": 1754187736536}, {"name": "Click on List Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187736536, "stop": 1754187736536}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187737488, "stop": 1754187737488}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187737790, "stop": 1754187737790}, {"name": "Processing module 'File Scanner'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187739966, "stop": 1754187739966}, {"name": "Click on File Scanner", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187739966, "stop": 1754187739966}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187741130, "stop": 1754187741130}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187741367, "stop": 1754187741367}, {"name": "Processing module 'Advanced Settings'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187743510, "stop": 1754187743510}, {"name": "Click on Advanced Settings", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187743510, "stop": 1754187743510}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187744523, "stop": 1754187744523}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187744919, "stop": 1754187744919}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187746410, "stop": 1754187746410}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187746727, "stop": 1754187746727}, {"name": "Check if profile exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187748230, "stop": 1754187748230}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187748230, "stop": 1754187748230}, {"name": "Set name = Test-Profile-388116407 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187753983, "stop": 1754187753983}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187754920, "stop": 1754187754920}, {"name": "Profile exist = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187755855, "stop": 1754187755855}, {"name": "Create second group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187755855, "stop": 1754187755855}, {"name": "Group test Data = Group{id=0, name='selenium-random-group-338829603', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-388116407', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone618122347', displayName='Zone (618122347)', description='Zone created with random number '618122347''}, profileRights={SWS={en.Reporting Scanner=[], SWIFT Manager=[ALL], SWP Name Checker=[], Detection Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], Name Checker=[ALL], Batch List Management=[], ListExplorer=[ALL], ListSetManager=[ALL], List Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-120272692', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone618122347', displayName='Zone (618122347)', description='Zone created with random number '618122347''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187755855, "stop": 1754187755855}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187795232, "stop": 1754187795232}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='selenium-user-120272692'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187795232, "stop": 1754187795232}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187795237, "stop": 1754187795237}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187795237, "stop": 1754187795237}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187795238, "stop": 1754187795238}, {"name": "Login with User Name = selenium-user-120272692 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187795370, "stop": 1754187795370}, {"name": "Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-463492080', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@74ddf6f8, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-463492080', officialDate='null', entry=[ListEntry{type='null', name='EntryName-463492080', firstName='EntryFirstName-463492080', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-463492080', firstName='EntryFirstName-463492080', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187800313, "stop": 1754187800313}, {"name": "Connect to Database and Check if User Profile = Test-Profile-388116407 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187800315, "stop": 1754187800315}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187800316, "stop": 1754187800316}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187800352, "stop": 1754187800352}, {"name": "SELECT ID FROM tProfiles Where NAME ='Test-Profile-388116407' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187800352, "stop": 1754187800352}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187800354, "stop": 1754187800354}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187800354, "stop": 1754187800354}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187800355, "stop": 1754187800355}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187800371, "stop": 1754187800371}, {"name": "Delete From tListSetProfile where profile_id in (174)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187800371, "stop": 1754187800371}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187800373, "stop": 1754187800373}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187800373, "stop": 1754187800373}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187800374, "stop": 1754187800374}, {"name": "Number of effected rows = 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187800374, "stop": 1754187800374}, {"name": "Search for list by listName = ListName-463492080 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187821571, "stop": 1754187821571}, {"name": "Set zone : Zone (618122347)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187830218, "stop": 1754187830218}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187832419, "stop": 1754187832419}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187833093, "stop": 1754187833093}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187833539, "stop": 1754187833539}, {"name": "Set template name = templateName-463492080", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187834003, "stop": 1754187834003}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187836123, "stop": 1754187836123}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187836798, "stop": 1754187836798}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187837728, "stop": 1754187837728}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187838353, "stop": 1754187838353}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187856684, "stop": 1754187856684}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187856684, "stop": 1754187856684}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187903012, "stop": 1754187903012}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187903013, "stop": 1754187903013}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187903013, "stop": 1754187903013}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187903035, "stop": 1754187903035}, {"name": "SELECT ID FROM tProfiles Where NAME ='Test-Profile-388116407'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187903036, "stop": 1754187903036}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187903038, "stop": 1754187903038}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187903038, "stop": 1754187903038}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187903038, "stop": 1754187903038}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187917409, "stop": 1754187917409}, {"name": "RJE File Content= {1:F01PTSABEMMAXXX0000000001}{2:I564ACCLFRP1XXXXN}{3:{108:ENT144037000001}}{4:\r\n:16R:GENL\r\n:20C::CORP//2015033100010560\r\n:20C::SEME//0105601900000002\r\n:23G:EntryName-463492080, EntryFirstName-463492080\r\n:22F::CAEV//DVCA\r\n:22F::CAMV//MAND\r\n:98A::PREP//20150331\r\n:25D::PROC//COMP\r\n:16R:LINK\r\n:13A::LINK//564\r\n:20C::PREV//0105601900000001\r\n:16S:LINK\r\n:16S:GENL\r\n:16R:USECU\r\n:35B:ISIN DE0005190003\r\nBMW Bayerische Motoren Werke AG\r\n:16R:ACCTINFO\r\n:97A::SAFE//000000190003\r\n:93B::ELIG//UNIT/360,\r\n:93B::PEND//UNIT/360,\r\n:93B::PENR//UNIT/0,\r\n:93B::SETT//UNIT/360,\r\n:16S:ACCTINFO\r\n:16S:USECU\r\n:16R:CADETL\r\n:98A::XDTE//20150514\r\n:98A::RDTE//20150515\r\n:22F::DIVI//FINL\r\n:16S:CADETL\r\n:16R:CAOPTN\r\n:13A::CAON//001\r\n:22F::CAOP//CASH\r\n:17B::DFLT//Y\r\n:16R:CASHMOVE\r\n:22H::CRDB//CRED\r\n:98A::PAYD//20150514\r\n:92A::TAXR//26,375\r\n:92F::GRSS//EUR2,9\r\n:16S:CASHMOVE\r\n:16S:CAOPTN\r\n-}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187925917, "stop": 1754187925917}, {"name": "Start coping data from sample file to another file to be used.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187925917, "stop": 1754187925917}, {"name": "Successfully wrote to the file.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187925918, "stop": 1754187925918}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187927441, "stop": 1754187927441}, {"name": "Validation message = File sent to the server for processing with id [3049]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187942048, "stop": 1754187942048}, {"name": "Alert Message = File sent to the server for processing with id [3049]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187942048, "stop": 1754187942048}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187943204, "stop": 1754187943204}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187951321, "stop": 1754187951321}, {"name": "Detection ID = 7844", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187952348, "stop": 1754187952348}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187956265, "stop": 1754187956265}, {"name": "Check if user can Release detection. ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187957408, "stop": 1754187957408}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754187961394, "stop": 1754187961394}], "attachments": [], "parameters": [{"name": "arg0", "value": "Operator{id=0, loginName='null', firstName='null', lastName='null', password='null', zone=null}"}], "start": 1754187426641, "stop": 1754187969387}