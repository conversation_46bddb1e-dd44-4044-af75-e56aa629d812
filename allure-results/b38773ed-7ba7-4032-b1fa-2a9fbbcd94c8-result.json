{"uuid": "b38773ed-7ba7-4032-b1fa-2a9fbbcd94c8", "historyId": "e9d44a66fb796025c18dc7707e21fe3c", "fullName": "eastnets.screening.regression.detectionmanager.DetectionManager_TC026.detectionManager_TC026", "labels": [{"name": "package", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC026"}, {"name": "testClass", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC026"}, {"name": "testMethod", "value": "detectionManager_TC026"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Detection Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC026"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Detection Manager"}], "links": [], "name": "Detection Manager - Verify that Detections return from RJE msg without and with GPI labels return when selecting 'both' option from  drop down list\n Verify that the detection was found in detection manager", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that the detection was found in detection manager\n* Filtering - Verify that 'SLA ID' column showing the value for '111'  field in RJE msgs in 'Detections' table\n* Filtering - Verify that 'UETR' column showing the value for '121'  field in RJE msgs in 'Detections' table\n* Filtering - Detection Manager - Verify that only Detections return from RJE msgs without gpi lables return when selecting 'RJE' option from 'Filter By' drop down list\n* Filtering - Detection Manager - Verify that only Detections return from msgs with gpi lables return when selecting 'gpi' option from 'Filter By' drop down list\n* Filtering - Verify that Search working properly when searching for RJE msgs using 'SLA ID' and  'UETR' values\n* Filtering - Detection Manager - Verify that new 'UETR' checkboxe dispalys in 'Column Panel'\n* Filtering - Detection Manager - Verify that new 'SLA ID' checkboxe dispalys in 'Column Panel'", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185152078, "stop": 1754185152078}, {"name": "update tConfig set variable_value = 'Yes' where variable_name = 'Change detection status to DontKnow'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185152078, "stop": 1754185152078}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185152082, "stop": 1754185152082}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185152082, "stop": 1754185152082}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185152083, "stop": 1754185152083}, {"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-178423378', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@225bb51e, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-178423378', officialDate='null', entry=[ListEntry{type='null', name='EntryName-178423378', firstName='EntryFirstName-178423378', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-178423378', firstName='EntryFirstName-178423378', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185152084, "stop": 1754185152084}, {"name": "Connect to Database and Check if User Profile = full-right-profile_05 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185152085, "stop": 1754185152085}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185152085, "stop": 1754185152085}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185152116, "stop": 1754185152116}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_05' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185152116, "stop": 1754185152116}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185152118, "stop": 1754185152118}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185152118, "stop": 1754185152118}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185152119, "stop": 1754185152119}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185152134, "stop": 1754185152134}, {"name": "Delete From tListSetProfile where profile_id in (8)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185152134, "stop": 1754185152134}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185152137, "stop": 1754185152137}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185152137, "stop": 1754185152137}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185152138, "stop": 1754185152138}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185152138, "stop": 1754185152138}, {"name": "Search for list by listName = ListName-178423378 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185173031, "stop": 1754185173031}, {"name": "Set zone : Common Zone 05", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185182757, "stop": 1754185182757}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185185018, "stop": 1754185185018}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185185755, "stop": 1754185185755}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185186310, "stop": 1754185186310}, {"name": "Set template name = templateName-178423378", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185186628, "stop": 1754185186628}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185188899, "stop": 1754185188899}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185189952, "stop": 1754185189952}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185191479, "stop": 1754185191479}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185192086, "stop": 1754185192086}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185277720, "stop": 1754185277720}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185277720, "stop": 1754185277720}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185373007, "stop": 1754185373007}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185373007, "stop": 1754185373007}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185373007, "stop": 1754185373007}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185373036, "stop": 1754185373036}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_05'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185373036, "stop": 1754185373036}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185373039, "stop": 1754185373039}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185373039, "stop": 1754185373039}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185373039, "stop": 1754185373039}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185393931, "stop": 1754185393931}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185399825, "stop": 1754185399825}, {"name": "Validation message = File sent to the server for processing with id [3045]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185420027, "stop": 1754185420027}, {"name": "Alert Message = File sent to the server for processing with id [3045]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185420027, "stop": 1754185420027}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185421231, "stop": 1754185421231}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185430769, "stop": 1754185430769}, {"name": "Detection ID = 7794", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185432273, "stop": 1754185432273}, {"name": "Status detection = Reported", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185432541, "stop": 1754185432541}, {"name": "Detection ID = 7794", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185433861, "stop": 1754185433861}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185433861, "stop": 1754185433861}, {"name": "Validation message = File sent to the server for processing with id [3046]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185453558, "stop": 1754185453558}, {"name": "Alert Message = File sent to the server for processing with id [3046]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185453558, "stop": 1754185453558}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185454561, "stop": 1754185454561}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185463443, "stop": 1754185463443}, {"name": "Detection ID = 7795", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185464957, "stop": 1754185464957}, {"name": "Status detection = Reported", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185465253, "stop": 1754185465253}, {"name": "Detection ID = 7795", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185466370, "stop": 1754185466371}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185487966, "stop": 1754185487966}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754185152061, "stop": 1754185569926}