{"uuid": "faef8b2b-c13b-45bf-9f9e-6bdf5739610e", "historyId": "********************************", "fullName": "eastnets.screening.regression.formatmanager.FormatManager_TC001.formatManager_TC001", "labels": [{"name": "package", "value": "eastnets.screening.regression.formatmanager.FormatManager_TC001"}, {"name": "testClass", "value": "eastnets.screening.regression.formatmanager.FormatManager_TC001"}, {"name": "testMethod", "value": "formatManager_TC001"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Format Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.formatmanager.FormatManager_TC001"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-3(38)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON><PERSON>"}, {"name": "tag", "value": "Regression"}], "links": [], "name": "Verify that user is able to Import, Export, Edit and Delete a format of \"Scan\" type", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to Import, Export, Edit and Delete a format of \"Scan\" type", "steps": [{"name": "response.getStatus()200", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264378253, "stop": 1754264378253}, {"name": "UTF-8", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264378253, "stop": 1754264378253}, {"name": "1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264378253, "stop": 1754264378253}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "Format{testCaseTitle='Verify that user is able to Import, Export, Edit and Delete a format of \"Scan\" type', name='Format_%s', zone='null', type='Scan', fieldDelimiter='Separator', separator=',', entryType='null', xpath='null', fields=[FormatField{name='FIRST_NAME_%s', type='FIRST_NAME', xpath='null', scan=false, addToContext=false}, FormatField{name='LAST_NAME_%s', type='LAST_NAME', xpath='null', scan=false, addToContext=false}]}"}], "start": 1754264305165, "stop": 1754264422415}