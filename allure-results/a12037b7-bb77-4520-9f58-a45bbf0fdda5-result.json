{"uuid": "a12037b7-bb77-4520-9f58-a45bbf0fdda5", "historyId": "251dbfcf5cf2b7d31c13c58d555aa5f0", "fullName": "eastnets.screening.regression.reports.Report_TC011.report_TC011", "labels": [{"name": "package", "value": "eastnets.screening.regression.reports.Report_TC011"}, {"name": "testClass", "value": "eastnets.screening.regression.reports.Report_TC011"}, {"name": "testMethod", "value": "report_TC011"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Report Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.reports.Report_TC011"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-3(38)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "tag", "value": "Reports"}], "links": [], "name": "Verify Good Guys Distribution Report", "status": "failed", "statusDetails": {"known": false, "muted": false, "flaky": false, "message": "Test Failed due to an exception occurred in POM's method expected [Done] but found [Failed]", "trace": "java.lang.AssertionError: Test Failed due to an exception occurred in POM's method expected [Done] but found [Failed]\r\n\tat org.testng.Assert.fail(Assert.java:110)\r\n\tat org.testng.Assert.failNotEquals(Assert.java:1413)\r\n\tat org.testng.Assert.assertEqualsImpl(Assert.java:149)\r\n\tat org.testng.Assert.assertEquals(Assert.java:131)\r\n\tat org.testng.Assert.assertEquals(Assert.java:655)\r\n\tat eastnets.screening.regression.reports.Report_TC011.report_TC011(Report_TC011.java:116)\r\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:580)\r\n\tat org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:677)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:221)\r\n\tat org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:50)\r\n\tat org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:969)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:194)\r\n\tat org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148)\r\n\tat org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128)\r\n\tat java.base/java.util.ArrayList.forEach(ArrayList.java:1596)\r\n\tat org.testng.TestRunner.privateRun(TestRunner.java:829)\r\n\tat org.testng.TestRunner.run(TestRunner.java:602)\r\n\tat org.testng.SuiteRunner.runTest(SuiteRunner.java:437)\r\n\tat org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:475)\r\n\tat org.testng.internal.thread.ThreadUtil.lambda$execute$0(ThreadUtil.java:58)\r\n\tat java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)\r\n\tat java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)\r\n\tat java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)\r\n\tat java.base/java.lang.Thread.run(Thread.java:1583)\r\n"}, "stage": "finished", "description": "Verify Good Guys Distribution Report", "steps": [{"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-936800773', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@33c25c1c, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-936800773', officialDate='null', entry=[ListEntry{type='null', name='EntryName-936800773', firstName='EntryFirstName-936800773', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-936800773', firstName='EntryFirstName-936800773', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181833712, "stop": 1754181833712}, {"name": "Connect to Database and Check if User Profile = full-right-profile_08 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181833712, "stop": 1754181833712}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181833712, "stop": 1754181833712}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181833755, "stop": 1754181833755}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_08' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181833755, "stop": 1754181833755}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181833758, "stop": 1754181833758}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181833758, "stop": 1754181833758}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181833759, "stop": 1754181833759}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181833791, "stop": 1754181833791}, {"name": "Delete From tListSetProfile where profile_id in (11)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181833791, "stop": 1754181833791}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181833797, "stop": 1754181833797}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181833797, "stop": 1754181833797}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181833797, "stop": 1754181833797}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181833797, "stop": 1754181833797}, {"name": "Search for list by listName = ListName-936800773 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181862930, "stop": 1754181862930}, {"name": "Set zone : Common Zone 08", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181873794, "stop": 1754181873794}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181876349, "stop": 1754181876349}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181877123, "stop": 1754181877123}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181877642, "stop": 1754181877642}, {"name": "Set template name = templateName-936800773", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181877985, "stop": 1754181877985}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181880816, "stop": 1754181880816}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181882522, "stop": 1754181882522}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181884119, "stop": 1754181884119}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181884860, "stop": 1754181884860}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181932264, "stop": 1754181932264}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181932264, "stop": 1754181932264}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182006716, "stop": 1754182006716}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182006716, "stop": 1754182006716}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182006716, "stop": 1754182006716}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182006739, "stop": 1754182006739}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_08'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182006740, "stop": 1754182006740}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182006742, "stop": 1754182006742}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182006742, "stop": 1754182006742}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182006742, "stop": 1754182006742}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182088283, "stop": 1754182088283}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182096555, "stop": 1754182096555}, {"name": "Validation message = File sent to the server for processing with id [3020]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182115974, "stop": 1754182115974}, {"name": "Alert Message = File sent to the server for processing with id [3020]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182115974, "stop": 1754182115974}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182118455, "stop": 1754182118455}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182129102, "stop": 1754182129102}, {"name": "Detection ID = 7650", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182130798, "stop": 1754182130798}, {"name": "Actual validation Message = Good guy successfully created!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182135087, "stop": 1754182135087}, {"name": "Report status is: Failed", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182198218, "stop": 1754182198218}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754181833710, "stop": 1754182198578}