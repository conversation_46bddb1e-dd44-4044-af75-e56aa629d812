{"uuid": "60a53659-aae9-4807-ab0c-cd4cfc1e885f", "historyId": "72db89ba58be9b5a9d42cef14f92e1b1", "fullName": "eastnets.screening.regression.detectionmanager.DetectionManager_TC027.detectionManager_TC027", "labels": [{"name": "package", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC027"}, {"name": "testClass", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC027"}, {"name": "testMethod", "value": "detectionManager_TC027"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Detection Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC027"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Detection Manager"}], "links": [], "name": "Filtering  - Verify that columns display properly correct value in 'DetectionManagerReport'", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "* Filtering  - Verify that 'SLA ID' column display properly correct value in 'DetectionManagerReport'\n* Filtering  - Verify that 'UETR' column display properly correct value in 'DetectionManagerReport'\n* Filtering - Verify that 'SLA ID' and 'UETR' display properly with correct values in 'DetectionManagerReport' ", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185578536, "stop": 1754185578536}, {"name": "update tConfig set variable_value = 'Yes' where variable_name = 'Change detection status to DontKnow'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185578536, "stop": 1754185578536}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185578544, "stop": 1754185578544}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185578544, "stop": 1754185578544}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185578544, "stop": 1754185578544}, {"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-648574124', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@57953326, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-648574124', officialDate='null', entry=[ListEntry{type='null', name='EntryName-648574124', firstName='EntryFirstName-648574124', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-648574124', firstName='EntryFirstName-648574124', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185578546, "stop": 1754185578546}, {"name": "Connect to Database and Check if User Profile = full-right-profile_05 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185578548, "stop": 1754185578548}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185578548, "stop": 1754185578548}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185578582, "stop": 1754185578582}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_05' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185578582, "stop": 1754185578582}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185578584, "stop": 1754185578584}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185578584, "stop": 1754185578584}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185578584, "stop": 1754185578584}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185578600, "stop": 1754185578600}, {"name": "Delete From tListSetProfile where profile_id in (8)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185578600, "stop": 1754185578600}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185578605, "stop": 1754185578605}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185578605, "stop": 1754185578605}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185578605, "stop": 1754185578605}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185578605, "stop": 1754185578605}, {"name": "Search for list by listName = ListName-648574124 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185598978, "stop": 1754185598978}, {"name": "Set zone : Common Zone 05", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185608424, "stop": 1754185608424}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185610568, "stop": 1754185610568}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185611218, "stop": 1754185611218}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185611647, "stop": 1754185611647}, {"name": "Set template name = templateName-648574124", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185611992, "stop": 1754185611992}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185614620, "stop": 1754185614620}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185615521, "stop": 1754185615521}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185616805, "stop": 1754185616805}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185617461, "stop": 1754185617461}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185695120, "stop": 1754185695120}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185695120, "stop": 1754185695120}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185774459, "stop": 1754185774459}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185774460, "stop": 1754185774460}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185774460, "stop": 1754185774460}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185774483, "stop": 1754185774483}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_05'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185774485, "stop": 1754185774485}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185774486, "stop": 1754185774486}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185774486, "stop": 1754185774486}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185774486, "stop": 1754185774486}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185793952, "stop": 1754185793952}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185799127, "stop": 1754185799127}, {"name": "Validation message = File sent to the server for processing with id [3047]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185816350, "stop": 1754185816350}, {"name": "Alert Message = File sent to the server for processing with id [3047]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185816350, "stop": 1754185816350}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185816926, "stop": 1754185816926}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185826132, "stop": 1754185826132}, {"name": "Detection ID = 7796", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185827340, "stop": 1754185827340}, {"name": "Status detection = Reported", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185827578, "stop": 1754185827578}, {"name": "Detection ID = 7796", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185828761, "stop": 1754185828761}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185832537, "stop": 1754185832537}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185844119, "stop": 1754185844119}, {"name": "File Content :General Information\r\nDetection Date\r\nApplication\r\nUser Name\r\nHost Name\r\n2025/08/03 04:50:18\r\nFile Scanner\r\noperator-05\r\n10.12.35.193\r\nDetection Ticket\r\nDetection Status\r\n7796\r\nNew\r\nFile Attached 0\r\n:\r\n:\r\n:\r\n:\r\n:\r\n:\r\n:\r\n:\r\nScanned Data\r\nData {1:F01ABNAMXMMXXX0008741881}{2:I103ABNAMXMMXXXN}{3:{108:EN10393\r\n5}{111:001}{121:8E0FE365-A88E-426B-8484-4FB7FEE92742}}{4:\r\n:20:Osama bin laden\r\n:23B:CRED\r\n:32A:180516USD90,\r\n:33B:USD1000,\r\n:50A:ZYGKBEB0\r\n:59A:/8900683465\r\nZYGTBEB0XXX\r\n:71A:BEN\r\n:71F:JOD25,\r\n-}\r\n:\r\nActive Black Lists\r\nList Set Name\r\nList Set Owner\r\nLast Modification Date\r\nListSetName-648574124\r\noperator-05\r\n2025/08/03 04:49:32\r\nActivated On 2025/08/03 04:49:32\r\nName Official Date Last Modified Date\r\nListName-648574124 2025/08/03\r\n00:00:00\r\n2025/08/03\r\n04:46:38\r\n:\r\n:\r\n:\r\n:\r\nViolations Details\r\nEntity reported in violation # 1\r\nOfficial Name : Osama bin laden\r\nMatched text : Osama bin laden\r\nStatus : Reported\r\nRank : 100\r\nDetection Summary\r\nReported Violations\r\nAccepted Violations\r\n1\r\n0\r\nOut Of Context Violations 0\r\nStatus Rank Matched Name Black List NameID\r\nReported 100 Osama bin laden ListName-6485741241\r\n:\r\n:\r\n:\r\n 2Page 1 ofSunday, 3 Aug 2025 4.50 AM\r\nIndividual:Category\r\nListed On : ListName-648574124 (Sun Aug 03 00:00:00 AST 2025)\r\nEntity ID : 111964\r\nPosition : 0 to 14\r\nField : 20 (Line 1)\r\nAlert Ticket ID : 2845\r\nAlert Assigned To : operator-05\r\n[2025/08/03 04:50:18] Alert creation:Alert History\r\n 2Page 2 ofSunday, 3 Aug 2025 4.50 AM\r\n", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754185879520, "stop": 1754185879520}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754185578519, "stop": 1754185879520}