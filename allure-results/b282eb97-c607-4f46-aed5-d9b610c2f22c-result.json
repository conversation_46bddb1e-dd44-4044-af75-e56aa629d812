{"uuid": "b282eb97-c607-4f46-aed5-d9b610c2f22c", "historyId": "700056edc7009a4805c808b7acc8d36f", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC002.scanManager_TC002", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC002"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC002"}, {"name": "testMethod", "value": "scanManager_TC002"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC002"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "scanManager_TC002", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754171066703, "stop": 1754171066703}