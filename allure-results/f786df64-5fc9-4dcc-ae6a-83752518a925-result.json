{"uuid": "f786df64-5fc9-4dcc-ae6a-83752518a925", "historyId": "72db89ba58be9b5a9d42cef14f92e1b1", "fullName": "eastnets.screening.regression.detectionmanager.DetectionManager_TC027.detectionManager_TC027", "labels": [{"name": "package", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC027"}, {"name": "testClass", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC027"}, {"name": "testMethod", "value": "detectionManager_TC027"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Detection Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC027"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Detection Manager"}], "links": [], "name": "Filtering  - Verify that columns display properly correct value in 'DetectionManagerReport'", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "* Filtering  - Verify that 'SLA ID' column display properly correct value in 'DetectionManagerReport'\n* Filtering  - Verify that 'UETR' column display properly correct value in 'DetectionManagerReport'\n* Filtering - Verify that 'SLA ID' and 'UETR' display properly with correct values in 'DetectionManagerReport' ", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153619168, "stop": 1754153619168}, {"name": "update tConfig set variable_value = 'Yes' where variable_name = 'Change detection status to DontKnow'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153619168, "stop": 1754153619168}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153619173, "stop": 1754153619173}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153619174, "stop": 1754153619174}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153619174, "stop": 1754153619174}, {"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-133749928', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@645ea61f, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-133749928', officialDate='null', entry=[ListEntry{type='null', name='EntryName-133749928', firstName='EntryFirstName-133749928', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-133749928', firstName='EntryFirstName-133749928', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153619176, "stop": 1754153619176}, {"name": "Connect to Database and Check if User Profile = full-right-profile_05 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153619176, "stop": 1754153619176}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153619176, "stop": 1754153619176}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153619218, "stop": 1754153619218}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_05' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153619218, "stop": 1754153619218}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153619220, "stop": 1754153619220}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153619220, "stop": 1754153619220}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153619220, "stop": 1754153619220}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153619241, "stop": 1754153619241}, {"name": "Delete From tListSetProfile where profile_id in (8)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153619241, "stop": 1754153619241}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153619246, "stop": 1754153619246}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153619246, "stop": 1754153619246}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153619246, "stop": 1754153619246}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153619246, "stop": 1754153619246}, {"name": "Search for list by listName = ListName-133749928 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153638332, "stop": 1754153638332}, {"name": "Set zone : Common Zone 05", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153646304, "stop": 1754153646304}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153648187, "stop": 1754153648187}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153648691, "stop": 1754153648691}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153649061, "stop": 1754153649061}, {"name": "Set template name = templateName-133749928", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153649257, "stop": 1754153649257}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153651326, "stop": 1754153651326}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153652072, "stop": 1754153652072}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153653107, "stop": 1754153653107}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153653783, "stop": 1754153653783}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153714781, "stop": 1754153714781}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153714781, "stop": 1754153714781}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153782644, "stop": 1754153782644}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153782645, "stop": 1754153782645}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153782645, "stop": 1754153782645}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153782676, "stop": 1754153782676}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_05'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153782676, "stop": 1754153782676}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153782679, "stop": 1754153782679}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153782679, "stop": 1754153782679}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153782679, "stop": 1754153782679}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153800148, "stop": 1754153800148}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153805123, "stop": 1754153805123}, {"name": "Validation message = File sent to the server for processing with id [2939]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153820204, "stop": 1754153820204}, {"name": "Alert Message = File sent to the server for processing with id [2939]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153820204, "stop": 1754153820204}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153820716, "stop": 1754153820716}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153829248, "stop": 1754153829248}, {"name": "Detection ID = 7481", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153830471, "stop": 1754153830471}, {"name": "Status detection = Reported", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153830705, "stop": 1754153830705}, {"name": "Detection ID = 7481", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153831592, "stop": 1754153831592}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153834657, "stop": 1754153834657}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153843958, "stop": 1754153843958}, {"name": "File Content :General Information\r\nDetection Date\r\nApplication\r\nUser Name\r\nHost Name\r\n2025/08/02 19:57:03\r\nFile Scanner\r\noperator-05\r\n10.12.35.193\r\nDetection Ticket\r\nDetection Status\r\n7481\r\nNew\r\nFile Attached 0\r\n:\r\n:\r\n:\r\n:\r\n:\r\n:\r\n:\r\n:\r\nScanned Data\r\nData {1:F01ABNAMXMMXXX0008741881}{2:I103ABNAMXMMXXXN}{3:{108:EN10393\r\n5}{111:001}{121:8E0FE365-A88E-426B-8484-4FB7FEE92742}}{4:\r\n:20:Osama bin laden\r\n:23B:CRED\r\n:32A:180516USD90,\r\n:33B:USD1000,\r\n:50A:ZYGKBEB0\r\n:59A:/8900683465\r\nZYGTBEB0XXX\r\n:71A:BEN\r\n:71F:JOD25,\r\n-}\r\n:\r\nActive Black Lists\r\nList Set Name\r\nList Set Owner\r\nLast Modification Date\r\nListSetName-133749928\r\noperator-05\r\n2025/08/02 19:56:20\r\nActivated On 2025/08/02 19:56:20\r\nName Official Date Last Modified Date\r\nListName-133749928 2025/08/02\r\n00:00:00\r\n2025/08/02\r\n19:53:57\r\n:\r\n:\r\n:\r\n:\r\nViolations Details\r\nEntity reported in violation # 1\r\nOfficial Name : Osama bin laden\r\nMatched text : Osama bin laden\r\nStatus : Reported\r\nRank : 100\r\nDetection Summary\r\nReported Violations\r\nAccepted Violations\r\n1\r\n0\r\nOut Of Context Violations 0\r\nStatus Rank Matched Name Black List NameID\r\nReported 100 Osama bin laden ListName-1337499281\r\n:\r\n:\r\n:\r\n 2Page 1 ofSaturday, 2 Aug 2025 7.57 PM\r\nIndividual:Category\r\nListed On : ListName-133749928 (Sat Aug 02 00:00:00 AST 2025)\r\nEntity ID : 109019\r\nPosition : 0 to 14\r\nField : 20 (Line 1)\r\nAlert Ticket ID : 2749\r\nAlert Assigned To : operator-05\r\n[2025/08/02 19:57:03] Alert creation:Alert History\r\n 2Page 2 ofSaturday, 2 Aug 2025 7.57 PM\r\n", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153878489, "stop": 1754153878489}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754153619146, "stop": 1754153878489}