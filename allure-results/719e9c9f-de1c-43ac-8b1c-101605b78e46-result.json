{"uuid": "719e9c9f-de1c-43ac-8b1c-101605b78e46", "historyId": "18f0b1d13312f83f0cc50f908ba63dea", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC010.scanManager_TC010", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC010"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC010"}, {"name": "testMethod", "value": "scanManager_TC010"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC010"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "scanManager_TC010", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754233083350, "stop": 1754233083350}