{"uuid": "6f6891fc-2e1e-4ca2-9a97-62ec6640a6c0", "name": "Scan Manager Test Cases", "children": ["8c1d3531-bf60-4325-b801-52e6665d1617", "646c6b81-5792-475b-88a2-e15581ae3aae", "fee2f355-3d5a-4e61-86f3-ab91646e82f8", "0704ea8b-2ee7-4f1f-827c-bbb63fb679c0", "c01d04ad-4d17-43a2-ae8a-67b90ab31f90", "743ce66a-72d9-4e34-8167-4f0a6b9bb3e0", "58e47d8c-48fd-40b6-941a-096fd802ec0a", "ba0d378a-cda0-4584-8626-f08aa391db94", "76e0d63d-eb1c-407d-95b6-0177b666f0b9", "c1aee215-08e4-469b-aaad-36429fcdab28", "a45c3920-c6e5-4b20-bba4-198e58254747", "aab720fa-024b-446c-9f4d-a1cbe1b0bab1", "8b7f92d4-fdd9-499e-8f94-4a909bddb607", "930d510c-0fa8-4106-a539-2a63c20d117d", "6f5a5e07-0721-4e6d-ab53-301088eb2b54", "f9134193-847b-4f67-869b-0c6d8bb5f03a", "bda004e4-06be-4f2e-8509-5f72a39c74e8", "f8957337-b85d-49b0-af2a-23ff620d687f", "3dd5faf6-f797-436d-adc6-0dafdc27149f", "faf233b5-f340-479b-928e-623c5ef1fe46", "ae36a372-58f5-4ff9-83d9-bcc4a83e5907", "ea7003db-ad0f-4db3-8cc7-ea6e05ad2fd4", "19e34eca-2e16-4dde-8f81-198213af4061", "51c7d995-33b8-4769-99c6-b979a7c1b314", "9ca61320-f409-4e4e-b001-0cc6dd1d1b96", "9bd1ea07-239a-4660-b0ae-7e1eb3851e54", "1a519423-2310-4f6d-873c-0888b631194a", "d41f9b92-e28a-4509-a5c7-3bf0167eef78", "f6471bdc-2486-4ae9-8fa8-664d8ff3fffb", "93511470-0d5d-441e-bee2-46d5edb9dd4b", "5a9cc339-29e7-4cfc-90cf-f7f0f2eeb158", "16231f4d-80b0-4830-a738-347f5309092e", "ab156120-ddf7-4b7d-aeb7-3aef108e15bb"], "befores": [{"name": "Setting up selenium web driver before each class run", "status": "failed", "statusDetails": {"known": false, "muted": false, "flaky": false, "message": "Error occurred while initializing selenium web driver", "trace": "java.lang.AssertionError: Error occurred while initializing selenium web driver\r\n\tat org.testng.Assert.fail(Assert.java:110)\r\n\tat core.BaseTest.aloadConfiguration(BaseTest.java:155)\r\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:578)\r\n\tat org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)\r\n\tat org.testng.internal.invokers.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:69)\r\n\tat org.testng.internal.invokers.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:361)\r\n\tat org.testng.internal.invokers.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:296)\r\n\tat org.testng.TestRunner.invokeTestConfigurations(TestRunner.java:644)\r\n\tat org.testng.TestRunner.beforeRun(TestRunner.java:633)\r\n\tat org.testng.TestRunner.run(TestRunner.java:595)\r\n\tat org.testng.SuiteRunner.runTest(SuiteRunner.java:437)\r\n\tat org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:475)\r\n\tat org.testng.internal.thread.ThreadUtil.lambda$execute$0(ThreadUtil.java:58)\r\n\tat java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)\r\n\tat java.base/java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:132)\r\n\tat java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:113)\r\n\tat java.base/java.util.concurrent.AbstractExecutorService.invokeAll(AbstractExecutorService.java:274)\r\n\tat org.testng.internal.thread.ThreadUtil.execute(ThreadUtil.java:64)\r\n\tat org.testng.SuiteRunner.runInParallelTestMode(SuiteRunner.java:458)\r\n\tat org.testng.SuiteRunner.privateRun(SuiteRunner.java:389)\r\n\tat org.testng.SuiteRunner.run(SuiteRunner.java:330)\r\n\tat org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)\r\n\tat org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)\r\n\tat org.testng.TestNG.runSuitesSequentially(TestNG.java:1256)\r\n\tat org.testng.TestNG.runSuitesLocally(TestNG.java:1176)\r\n\tat org.testng.TestNG.runSuites(TestNG.java:1099)\r\n\tat org.testng.TestNG.run(TestNG.java:1067)\r\n\tat com.intellij.rt.testng.IDEARemoteTestNG.run(IDEARemoteTestNG.java:65)\r\n\tat com.intellij.rt.testng.RemoteTestNGStarter.main(RemoteTestNGStarter.java:105)\r\n"}, "stage": "finished", "description": "Setting up selenium web driver before each class run", "steps": [{"name": "Initialize Selenium driver before tests' Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754233053901, "stop": 1754233053901}, {"name": "Error occurred while initializing selenium web driver ----> Could not start a new session. Possible causes are invalid address of the remote server or browser start-up failure. \nHost info: host: 'ENAMAUTO001', ip: '************'\nBuild info: version: '4.13.0', revision: 'ba948ece5b*'\nSystem info: os.name: 'Windows Server 2022', os.arch: 'amd64', os.version: '10.0', java.version: '19.0.2'\nDriver info: org.openqa.selenium.remote.RemoteWebDriver\nCommand: [null, newSession {capabilities=[Capabilities {browserName: MicrosoftEdge, ms:edgeOptions: {args: [--remote-allow-origins=*, force-device-scale-factor=0.9, high-dpi-support=0.9], extensions: [], prefs: {nativeEvents: false}}, se:downloadsEnabled: true}]}]\nCapabilities {browserName: MicrosoftEdge, ms:edgeOptions: {args: [--remote-allow-origins=*, force-device-scale-factor=0.9, high-dpi-support=0.9], extensions: [], prefs: {nativeEvents: false}}, se:downloadsEnabled: true}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754233056145, "stop": 1754233056145}], "attachments": [], "parameters": [], "start": 1754233053901, "stop": 1754233056171}], "afters": [{"name": "Quitting selenium driver after each class run", "status": "broken", "statusDetails": {"known": false, "muted": false, "flaky": false, "message": "Cannot invoke \"org.openqa.selenium.remote.RemoteWebDriver.quit()\" because the return value of \"core.BaseTest.getDriver()\" is null", "trace": "java.lang.NullPointerException: Cannot invoke \"org.openqa.selenium.remote.RemoteWebDriver.quit()\" because the return value of \"core.BaseTest.getDriver()\" is null\r\n\tat core.BaseTest.closeDriver(BaseTest.java:218)\r\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:578)\r\n\tat org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)\r\n\tat org.testng.internal.invokers.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:69)\r\n\tat org.testng.internal.invokers.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:361)\r\n\tat org.testng.internal.invokers.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:296)\r\n\tat org.testng.TestRunner.invokeTestConfigurations(TestRunner.java:644)\r\n\tat org.testng.TestRunner.afterRun(TestRunner.java:914)\r\n\tat org.testng.TestRunner.run(TestRunner.java:605)\r\n\tat org.testng.SuiteRunner.runTest(SuiteRunner.java:437)\r\n\tat org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:475)\r\n\tat org.testng.internal.thread.ThreadUtil.lambda$execute$0(ThreadUtil.java:58)\r\n\tat java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)\r\n\tat java.base/java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:132)\r\n\tat java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:113)\r\n\tat java.base/java.util.concurrent.AbstractExecutorService.invokeAll(AbstractExecutorService.java:274)\r\n\tat org.testng.internal.thread.ThreadUtil.execute(ThreadUtil.java:64)\r\n\tat org.testng.SuiteRunner.runInParallelTestMode(SuiteRunner.java:458)\r\n\tat org.testng.SuiteRunner.privateRun(SuiteRunner.java:389)\r\n\tat org.testng.SuiteRunner.run(SuiteRunner.java:330)\r\n\tat org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)\r\n\tat org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)\r\n\tat org.testng.TestNG.runSuitesSequentially(TestNG.java:1256)\r\n\tat org.testng.TestNG.runSuitesLocally(TestNG.java:1176)\r\n\tat org.testng.TestNG.runSuites(TestNG.java:1099)\r\n\tat org.testng.TestNG.run(TestNG.java:1067)\r\n\tat com.intellij.rt.testng.IDEARemoteTestNG.run(IDEARemoteTestNG.java:65)\r\n\tat com.intellij.rt.testng.RemoteTestNGStarter.main(RemoteTestNGStarter.java:105)\r\n"}, "stage": "finished", "description": "Quitting selenium driver after each class run", "steps": [{"name": "Closing selenium WebDriver after Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754233059668, "stop": 1754233059668}], "attachments": [], "parameters": [], "start": 1754233059668, "stop": 1754233059668}], "start": 1754233052990, "stop": 1754233059683}