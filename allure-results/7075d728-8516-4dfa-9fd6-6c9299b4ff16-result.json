{"uuid": "7075d728-8516-4dfa-9fd6-6c9299b4ff16", "historyId": "b206d4ea001656f674cf46c5d1e751b3", "fullName": "eastnets.screening.regression.reports.Report_TC012.report_TC012", "labels": [{"name": "package", "value": "eastnets.screening.regression.reports.Report_TC012"}, {"name": "testClass", "value": "eastnets.screening.regression.reports.Report_TC012"}, {"name": "testMethod", "value": "report_TC012"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Report Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.reports.Report_TC012"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-3(38)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "tag", "value": "Reports"}], "links": [], "name": "Verify List Set Info Report", "status": "failed", "statusDetails": {"known": false, "muted": false, "flaky": false, "message": "Test Failed due to an exception occurred in POM's method expected [Done] but found [Failed]", "trace": "java.lang.AssertionError: Test Failed due to an exception occurred in POM's method expected [Done] but found [Failed]\r\n\tat org.testng.Assert.fail(Assert.java:110)\r\n\tat org.testng.Assert.failNotEquals(Assert.java:1413)\r\n\tat org.testng.Assert.assertEqualsImpl(Assert.java:149)\r\n\tat org.testng.Assert.assertEquals(Assert.java:131)\r\n\tat org.testng.Assert.assertEquals(Assert.java:655)\r\n\tat eastnets.screening.regression.reports.Report_TC012.report_TC012(Report_TC012.java:98)\r\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:580)\r\n\tat org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:677)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:221)\r\n\tat org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:50)\r\n\tat org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:969)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:194)\r\n\tat org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148)\r\n\tat org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128)\r\n\tat java.base/java.util.ArrayList.forEach(ArrayList.java:1596)\r\n\tat org.testng.TestRunner.privateRun(TestRunner.java:829)\r\n\tat org.testng.TestRunner.run(TestRunner.java:602)\r\n\tat org.testng.SuiteRunner.runTest(SuiteRunner.java:437)\r\n\tat org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:475)\r\n\tat org.testng.internal.thread.ThreadUtil.lambda$execute$0(ThreadUtil.java:58)\r\n\tat java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)\r\n\tat java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)\r\n\tat java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)\r\n\tat java.base/java.lang.Thread.run(Thread.java:1583)\r\n"}, "stage": "finished", "description": "Verify List Set Info Report", "steps": [{"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-986513082', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@6f0fe4ac, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-986513082', officialDate='null', entry=[ListEntry{type='null', name='EntryName-986513082', firstName='EntryFirstName-986513082', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-986513082', firstName='EntryFirstName-986513082', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182209874, "stop": 1754182209874}, {"name": "Connect to Database and Check if User Profile = full-right-profile_08 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182209877, "stop": 1754182209877}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182209877, "stop": 1754182209877}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182209939, "stop": 1754182209939}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_08' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182209939, "stop": 1754182209939}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182209943, "stop": 1754182209943}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182209943, "stop": 1754182209943}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182209943, "stop": 1754182209943}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182209965, "stop": 1754182209965}, {"name": "Delete From tListSetProfile where profile_id in (11)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182209965, "stop": 1754182209965}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182209970, "stop": 1754182209970}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182209970, "stop": 1754182209970}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182209971, "stop": 1754182209971}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182209971, "stop": 1754182209971}, {"name": "Search for list by listName = ListName-986513082 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182234934, "stop": 1754182234934}, {"name": "Set zone : Common Zone 08", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182247864, "stop": 1754182247864}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182251240, "stop": 1754182251240}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182252185, "stop": 1754182252185}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182253167, "stop": 1754182253167}, {"name": "Set template name = templateName-986513082", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182253713, "stop": 1754182253713}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182256551, "stop": 1754182256551}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182257508, "stop": 1754182257508}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182259232, "stop": 1754182259232}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182260059, "stop": 1754182260059}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182307896, "stop": 1754182307896}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182307896, "stop": 1754182307896}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182378975, "stop": 1754182378975}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182378975, "stop": 1754182378975}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182378975, "stop": 1754182378976}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182378997, "stop": 1754182378997}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_08'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182378997, "stop": 1754182378997}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182378999, "stop": 1754182378999}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182379001, "stop": 1754182379001}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182379002, "stop": 1754182379002}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182406215, "stop": 1754182406215}, {"name": "Report status is: Failed", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754182471760, "stop": 1754182471760}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754182209863, "stop": 1754182472184}