{"uuid": "5624452a-bac3-4184-85fd-bc1f16464d7c", "name": "ISO20022 Configurations Test Cases", "children": ["2ca18770-c99a-4809-a0e7-8d7cc8bc2004", "605730b7-5e67-4526-b8ea-7703c26c9d05", "8b9974e2-f86f-452b-af88-13bc27a90836", "b841ad01-27d6-43a9-8228-ec2e1067cf70", "cafa9aa7-99e3-4983-8a6b-bd0a29fdcac5"], "befores": [{"name": "Setting up selenium web driver before each class run", "status": "passed", "stage": "finished", "description": "Setting up selenium web driver before each class run", "steps": [{"name": "Initialize Selenium driver before tests' Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754171885843, "stop": 1754171885843}, {"name": "Selenium webDriver was initialized successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754171893997, "stop": 1754171893997}], "attachments": [], "parameters": [], "start": 1754171885831, "stop": 1754171893997}], "afters": [{"name": "Quitting selenium driver after each class run", "status": "passed", "stage": "finished", "description": "Quitting selenium driver after each class run", "steps": [{"name": "Closing selenium WebDriver after Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172787926, "stop": 1754172787926}], "attachments": [], "parameters": [], "start": 1754172787925, "stop": 1754172788287}], "start": 1754171885818, "stop": 1754172788287}