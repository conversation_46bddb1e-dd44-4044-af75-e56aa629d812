{"uuid": "f1ee4251-bd83-4f61-a076-025c9fc03bfb", "historyId": "32c28e30128fe0f7db52204a9ed86b53", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC009.scanManager_TC009", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC009"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC009"}, {"name": "testMethod", "value": "scanManager_TC009"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC009"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is able to Print an alert report in Excel format", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to Print an alert report in Excel format", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242060715, "stop": 1754242060715}, {"name": "Validation message = File sent to the server for processing with id [3130]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242078672, "stop": 1754242078672}, {"name": "Alert Message = File sent to the server for processing with id [3130]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242078672, "stop": 1754242078672}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242080057, "stop": 1754242080057}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242091004, "stop": 1754242091004}, {"name": "Detection ID = 8081", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242092478, "stop": 1754242092478}, {"name": "Start Exporting Violation With Print Scope all And Document Type Excel", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242092491, "stop": 1754242092492}, {"name": "Click on close dialog button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242100454, "stop": 1754242100454}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "eastnets.screening.entity.Report@776113c1"}], "start": 1754242060465, "stop": 1754242131432}