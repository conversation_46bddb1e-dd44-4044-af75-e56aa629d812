{"uuid": "1bc3dc9a-4eb9-4bc0-86e9-4438e9b1b369", "name": "List Manager Test Cases", "children": ["21e793f8-2dc1-4306-945a-efbdcc892d3a", "9a4a7ce2-451a-4f82-8b5a-f973d28c5ce6", "a628dabc-5e9d-4122-a169-bb7d72a36d33", "e1ad9727-95ed-49ea-8823-09989178da35", "4ee5f76d-ac48-4845-8712-e32e233060b5", "d92beb47-a086-4bf6-bfa0-07eadbe31f54", "c7dedd15-3e7d-4098-ac03-44868a70cc8e", "7b0366e0-e659-46bc-a8b1-b927ae9c3a92", "8d181856-e1ed-4d12-89ed-87c049247502", "a26d4d2b-d9f9-4009-bf2b-0b31153c5208", "5321f52f-401d-46bb-91e7-86533434a141", "3c3c6f80-6367-4827-88e3-ffbe3624cadf", "f0497892-57d9-4f1f-8277-d3bc608fba88", "2e5aaca6-21ca-4c45-85df-bb5cfb4747b6", "81ae27c3-aa5e-44be-ac9a-b6fd6e230437", "d37e2a94-ab7e-45fa-8568-ff8428a528d1", "f0e97af5-cc36-43c1-bc49-d741ce270643", "3590d521-fc75-4d7e-a288-78d486a90cf5", "d7380102-8be3-4a7f-9894-6d8de1bd2f04", "353bb38a-1fe8-4067-ae9d-f2ae8fdc506a", "05c61bfe-ff35-439a-9b45-a5a63e79bc4a", "6506b046-4306-4ad0-bca3-427a6355552f", "ad5e03e0-3432-4f19-807b-2c4a277af8c6", "3501134c-25d1-43ef-bbae-abc5e428adc4", "4b8ce799-b97c-4e17-aac7-c49f3e7b357c", "b7f6100e-2603-45cf-a8b8-3038dbd662d5", "48299e66-e447-4a21-a3ef-9f115560cdba", "f469f7d3-52e3-45a3-a9a3-f4f5f346718f", "0f4e414f-ad8c-4dbb-8f7f-d93c4e799d5b", "43f38cb4-6301-4241-b28d-e6486ad9ddf2", "5ec884db-a5ad-4b75-b0d4-c6cf0f7499e3", "859522a2-a743-47ce-bb92-96fa1d400208", "a2cf814f-f0f7-4aa9-b326-2dc76f6902d4"], "befores": [{"name": "Setting up selenium web driver before each class run", "status": "passed", "stage": "finished", "description": "Setting up selenium web driver before each class run", "steps": [{"name": "Initialize Selenium driver before tests' Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754240865481, "stop": 1754240865482}, {"name": "Selenium webDriver was initialized successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754240874524, "stop": 1754240874524}], "attachments": [], "parameters": [], "start": 1754240865477, "stop": 1754240874524}], "afters": [{"name": "Quitting selenium driver after each class run", "status": "broken", "statusDetails": {"known": false, "muted": false, "flaky": false, "message": "Unable to execute request for an existing session: java.util.concurrent.TimeoutException\nBuild info: version: '4.34.0', revision: '2a4c61c498'\nSystem info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '21.0.7'\nDriver info: driver.version: unknown\nBuild info: version: '4.13.0', revision: 'ba948ece5b*'\nSystem info: os.name: 'Windows Server 2022', os.arch: 'amd64', os.version: '10.0', java.version: '19.0.2'\nDriver info: org.openqa.selenium.remote.RemoteWebDriver\nCommand: [3aae284e0e1987b777f1187ed3d82a74, quit {}]\nCapabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:45605}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.m1...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: Proxy(), se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: 84f529874b86, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\nSession ID: 3aae284e0e1987b777f1187ed3d82a74", "trace": "org.openqa.selenium.WebDriverException: Unable to execute request for an existing session: java.util.concurrent.TimeoutException\nBuild info: version: '4.34.0', revision: '2a4c61c498'\nSystem info: os.name: 'Linux', os.arch: 'amd64', os.version: '**********-microsoft-standard-WSL2', java.version: '21.0.7'\nDriver info: driver.version: unknown\nBuild info: version: '4.13.0', revision: 'ba948ece5b*'\nSystem info: os.name: 'Windows Server 2022', os.arch: 'amd64', os.version: '10.0', java.version: '19.0.2'\nDriver info: org.openqa.selenium.remote.RemoteWebDriver\nCommand: [3aae284e0e1987b777f1187ed3d82a74, quit {}]\nCapabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:45605}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.m1...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: Proxy(), se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: 84f529874b86, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\nSession ID: 3aae284e0e1987b777f1187ed3d82a74\r\n\tat java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:67)\r\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\r\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:484)\r\n\tat org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.createException(W3CHttpResponseCodec.java:200)\r\n\tat org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:133)\r\n\tat org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:52)\r\n\tat org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:191)\r\n\tat org.openqa.selenium.remote.TracedCommandExecutor.execute(TracedCommandExecutor.java:51)\r\n\tat org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:602)\r\n\tat org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:675)\r\n\tat org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:679)\r\n\tat org.openqa.selenium.remote.RemoteWebDriver.quit(RemoteWebDriver.java:437)\r\n\tat core.BaseTest.closeDriver(BaseTest.java:218)\r\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:578)\r\n\tat org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)\r\n\tat org.testng.internal.invokers.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:69)\r\n\tat org.testng.internal.invokers.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:361)\r\n\tat org.testng.internal.invokers.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:296)\r\n\tat org.testng.TestRunner.invokeTestConfigurations(TestRunner.java:644)\r\n\tat org.testng.TestRunner.afterRun(TestRunner.java:914)\r\n\tat org.testng.TestRunner.run(TestRunner.java:605)\r\n\tat org.testng.SuiteRunner.runTest(SuiteRunner.java:437)\r\n\tat org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:475)\r\n\tat org.testng.internal.thread.ThreadUtil.lambda$execute$0(ThreadUtil.java:58)\r\n\tat java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)\r\n\tat java.base/java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:132)\r\n\tat java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:113)\r\n\tat java.base/java.util.concurrent.AbstractExecutorService.invokeAll(AbstractExecutorService.java:274)\r\n\tat org.testng.internal.thread.ThreadUtil.execute(ThreadUtil.java:64)\r\n\tat org.testng.SuiteRunner.runInParallelTestMode(SuiteRunner.java:458)\r\n\tat org.testng.SuiteRunner.privateRun(SuiteRunner.java:389)\r\n\tat org.testng.SuiteRunner.run(SuiteRunner.java:330)\r\n\tat org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)\r\n\tat org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)\r\n\tat org.testng.TestNG.runSuitesSequentially(TestNG.java:1256)\r\n\tat org.testng.TestNG.runSuitesLocally(TestNG.java:1176)\r\n\tat org.testng.TestNG.runSuites(TestNG.java:1099)\r\n\tat org.testng.TestNG.run(TestNG.java:1067)\r\n\tat com.intellij.rt.testng.IDEARemoteTestNG.run(IDEARemoteTestNG.java:65)\r\n\tat com.intellij.rt.testng.RemoteTestNGStarter.main(RemoteTestNGStarter.java:105)\r\n"}, "stage": "finished", "description": "Quitting selenium driver after each class run", "steps": [{"name": "Closing selenium WebDriver after Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754249134601, "stop": 1754249134601}], "attachments": [], "parameters": [], "start": 1754249134601, "stop": 1754249314597}], "start": 1754240865466, "stop": 1754249314611}