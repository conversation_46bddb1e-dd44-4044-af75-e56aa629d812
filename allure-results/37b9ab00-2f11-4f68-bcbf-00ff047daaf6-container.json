{"uuid": "37b9ab00-2f11-4f68-bcbf-00ff047daaf6", "name": "List Manager Test Cases", "children": ["81b2dd9d-693b-4d65-8573-4bbe1ef299c8", "7564166e-10a9-429a-8155-a82926a61d72", "ed07786f-4cdc-4cec-a28d-d8f40731cdb4", "86f9810a-89b0-4e01-ab8f-0dd63aebb52f", "5b9d7c87-2611-424d-adf9-95e303b874e8", "d382ed77-7a97-42d0-83de-e7e5439f5dab", "bf6429f3-69b4-4db0-964b-2e419960d3b8", "a5732e51-b178-4013-9f4e-9860abad09ac", "f6df9026-3326-4f01-b0a0-9c891944199e", "bef4df5c-e5c8-48bb-9667-ff13576d04d0", "3b299d06-f52d-4a8e-a97c-022c0e523ba5", "ec2355fc-9ad6-443c-853c-57e50eed052d", "55afeb54-10c3-410b-bc9e-356fbd0b671b", "5e603a3c-d8c5-4fc9-aa10-4297bd67b847", "0a85213e-bebc-4f16-936e-53b069c1dbfe", "cf716d3b-f29e-4615-a7a2-d427df86746b", "419fa1cb-de3a-4881-a0d2-f41318748ad0", "fc93ee2b-9713-4309-b742-9d2347f33874", "84e1a856-ffc9-4a26-9ee5-2468f842e18e", "b992b91d-aed4-47bc-8bc2-2c46e4388f84", "834c0fb2-f42b-4c9e-9a10-28e680efc19d", "d0289e31-697c-4673-b523-3bf5777f31b0", "d2ae9b0f-801f-419a-a537-d691f2f5a33e", "43a3e302-88ad-49d9-acbb-9918876c4e5f", "0edc6ae3-b8ff-4821-a8e1-88301fb0f537", "d235b75c-558a-43b0-8a03-590157eabd2f", "d0eadab4-2215-4ded-97d8-6d3db8aed635", "a688fea0-cbf7-4d3a-9413-8d7f4d6a09a8", "ae91e483-7df5-413c-9cdd-22e31c624c2d", "0037ab1a-d450-42df-b77e-6c12f8b7a790", "d3c2b25d-1c9b-451e-a37f-fb5040c22364", "1d9353cd-47b9-4a48-801e-c5db74e25337", "70b7bb42-6672-4f25-9361-3f23000a8df7"], "befores": [{"name": "Setting up selenium web driver before each class run", "status": "passed", "stage": "finished", "description": "Setting up selenium web driver before each class run", "steps": [{"name": "Initialize Selenium driver before tests' Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754171885843, "stop": 1754171885843}, {"name": "Selenium webDriver was initialized successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754171894122, "stop": 1754171894122}], "attachments": [], "parameters": [], "start": 1754171885830, "stop": 1754171894122}], "afters": [{"name": "Quitting selenium driver after each class run", "status": "broken", "statusDetails": {"known": false, "muted": false, "flaky": false, "message": "java.util.concurrent.TimeoutException\nBuild info: version: '4.13.0', revision: 'ba948ece5b*'\nSystem info: os.name: 'Windows Server 2022', os.arch: 'amd64', os.version: '10.0', java.version: '21.0.6'\nDriver info: org.openqa.selenium.remote.RemoteWebDriver\nCommand: [e474db8883e1ca1e6095d7c0cf54958e, quit {}]\nCapabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39013}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.ne...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: Proxy(), se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: 8d11f51348c6, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\nSession ID: e474db8883e1ca1e6095d7c0cf54958e", "trace": "org.openqa.selenium.TimeoutException: java.util.concurrent.TimeoutException\nBuild info: version: '4.13.0', revision: 'ba948ece5b*'\nSystem info: os.name: 'Windows Server 2022', os.arch: 'amd64', os.version: '10.0', java.version: '21.0.6'\nDriver info: org.openqa.selenium.remote.RemoteWebDriver\nCommand: [e474db8883e1ca1e6095d7c0cf54958e, quit {}]\nCapabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:39013}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.ne...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: Proxy(), se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: 8d11f51348c6, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\nSession ID: e474db8883e1ca1e6095d7c0cf54958e\r\n\tat org.openqa.selenium.remote.http.netty.NettyHttpHandler.makeCall(NettyHttpHandler.java:65)\r\n\tat org.openqa.selenium.remote.http.AddSeleniumUserAgent.lambda$apply$0(AddSeleniumUserAgent.java:42)\r\n\tat org.openqa.selenium.remote.http.Filter.lambda$andFinally$1(Filter.java:55)\r\n\tat org.openqa.selenium.remote.http.netty.NettyHttpHandler.execute(NettyHttpHandler.java:48)\r\n\tat org.openqa.selenium.remote.http.netty.NettyClient.execute(NettyClient.java:96)\r\n\tat org.openqa.selenium.remote.tracing.TracedHttpClient.execute(TracedHttpClient.java:54)\r\n\tat org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:188)\r\n\tat org.openqa.selenium.remote.TracedCommandExecutor.execute(TracedCommandExecutor.java:51)\r\n\tat org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:602)\r\n\tat org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:675)\r\n\tat org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:679)\r\n\tat org.openqa.selenium.remote.RemoteWebDriver.quit(RemoteWebDriver.java:437)\r\n\tat core.BaseTest.closeDriver(BaseTest.java:218)\r\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:580)\r\n\tat org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)\r\n\tat org.testng.internal.invokers.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:69)\r\n\tat org.testng.internal.invokers.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:361)\r\n\tat org.testng.internal.invokers.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:296)\r\n\tat org.testng.TestRunner.invokeTestConfigurations(TestRunner.java:644)\r\n\tat org.testng.TestRunner.afterRun(TestRunner.java:914)\r\n\tat org.testng.TestRunner.run(TestRunner.java:605)\r\n\tat org.testng.SuiteRunner.runTest(SuiteRunner.java:437)\r\n\tat org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:475)\r\n\tat org.testng.internal.thread.ThreadUtil.lambda$execute$0(ThreadUtil.java:58)\r\n\tat java.base/java.util.concurrent.FutureTask.run(FutureTask.java:317)\r\n\tat java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)\r\n\tat java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)\r\n\tat java.base/java.lang.Thread.run(Thread.java:1583)\r\nCaused by: java.util.concurrent.TimeoutException\r\n\tat java.base/java.util.concurrent.CompletableFuture.timedGet(CompletableFuture.java:1960)\r\n\tat java.base/java.util.concurrent.CompletableFuture.get(CompletableFuture.java:2095)\r\n\tat org.asynchttpclient.netty.NettyResponseFuture.get(NettyResponseFuture.java:206)\r\n\tat org.openqa.selenium.remote.http.netty.NettyHttpHandler.makeCall(NettyHttpHandler.java:59)\r\n\t... 28 more\r\n"}, "stage": "finished", "description": "Quitting selenium driver after each class run", "steps": [{"name": "Closing selenium WebDriver after Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754179978908, "stop": 1754179978908}], "attachments": [], "parameters": [], "start": 1754179978908, "stop": 1754180158911}], "start": 1754171885820, "stop": 1754180158918}