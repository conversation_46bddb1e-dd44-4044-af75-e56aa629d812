{"uuid": "e4269595-85f2-4430-ae2b-b43b4be441e2", "name": "Admin Test Cases", "children": ["d92d27f8-9517-47e2-ae83-e47fc077d3f4", "76101bb0-61f1-4fba-a345-0287fb1fb3bd", "8183a506-cee8-4a65-8834-2597cf24b180", "cd3b4e5b-bed6-4507-b4bd-9595e5d501af", "12916da3-2ed2-4b2b-9d15-dae767573bf6", "e8ddcea3-bbdd-4161-aeb3-d09b2cd2b266", "5a06bd2e-240a-45c6-8f10-59de37bdc4f6", "8f000a96-bf9b-4350-bfa5-516aeced03b6", "7514f7d0-8d32-4c2b-ad80-b137e7fe4bef", "0b8f99d6-aad5-472f-b5ae-3c0dc4f16678", "a48e1b27-2ed1-4b25-aaad-55ac016e06dc", "*************-4730-ac9b-833478fdfbd2", "a514aedd-6510-431f-9aad-661c7f2d9359", "5d8d1ac7-8181-4f51-987b-afe3fd4a700b", "d7cb7b8a-25c9-480d-9ac3-408383e2fc3b", "b6d3be2b-defe-414e-ad27-56d1b767ef56", "6e43d0ea-5761-4e4e-ac97-90951560068f"], "befores": [{"name": "Setting up selenium web driver before each class run", "status": "passed", "stage": "finished", "description": "Setting up selenium web driver before each class run", "steps": [{"name": "Initialize Selenium driver before tests' Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264785419, "stop": 1754264785419}, {"name": "Selenium webDriver was initialized successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264791729, "stop": 1754264791729}], "attachments": [], "parameters": [], "start": 1754264785419, "stop": 1754264791730}], "afters": [{"name": "Quitting selenium driver after each class run", "status": "passed", "stage": "finished", "description": "Quitting selenium driver after each class run", "steps": [{"name": "Closing selenium WebDriver after Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754268549666, "stop": 1754268549666}], "attachments": [], "parameters": [], "start": 1754268549666, "stop": 1754268551183}], "start": 1754264785418, "stop": 1754268551183}