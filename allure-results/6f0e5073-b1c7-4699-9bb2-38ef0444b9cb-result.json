{"uuid": "6f0e5073-b1c7-4699-9bb2-38ef0444b9cb", "historyId": "700056edc7009a4805c808b7acc8d36f", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC002.scanManager_TC002", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC002"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC002"}, {"name": "testMethod", "value": "scanManager_TC002"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC002"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "scanManager_TC002", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754271147317, "stop": 1754271147317}