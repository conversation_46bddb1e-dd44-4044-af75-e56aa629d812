{"uuid": "3e1a5231-cd04-4993-bc1e-6ab895f01541", "historyId": "99779744c768e2c3e4baec9a7f47f7c", "fullName": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC018.ListManager_TC018", "labels": [{"name": "package", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC018"}, {"name": "testClass", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC018"}, {"name": "testMethod", "value": "ListManager_TC018"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "List Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC018"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-1(36)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "List Manager"}], "links": [], "name": "Verify that user is able to Add, modify, delete and a World Check flow as PEP type", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to Add a new World Check flow as PEP type\nVerify that user is able to Modify a World Check flow\nVerify that user is able to Delete a World Check flow\nVerify that user is able to search for a World Check flow by Zone", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236630349, "stop": 1754236630349}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-02'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236630349, "stop": 1754236630349}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236630356, "stop": 1754236630356}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236630356, "stop": 1754236630356}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236630357, "stop": 1754236630357}, {"name": "Login with User Name = operator-02 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236630497, "stop": 1754236630497}, {"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-461987', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@5895f463, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-461987', officialDate='null', entry=[ListEntry{type='null', name='EntryName-461987', firstName='EntryFirstName-461987', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-461987', firstName='EntryFirstName-461987', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236634779, "stop": 1754236634779}, {"name": "Create new black list", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236634779, "stop": 1754236634779}, {"name": "Search for list by listName = ListName-461987 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236652007, "stop": 1754236652007}, {"name": "Verify that user is able to Add a new World Check flow as PEP type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236656808, "stop": 1754236656808}, {"name": "Verify that user is able to Modify a World Check flow", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236684504, "stop": 1754236684504}, {"name": "Verify that user is able to Delete a World Check flow", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236692229, "stop": 1754236692229}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "PEP"}], "start": 1754236629942, "stop": 1754236700842}