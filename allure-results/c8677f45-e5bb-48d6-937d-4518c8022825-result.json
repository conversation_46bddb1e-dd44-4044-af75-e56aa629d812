{"uuid": "c8677f45-e5bb-48d6-937d-4518c8022825", "historyId": "c6142a835c74d3e0aa41bd07b46dcd7d", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC005.scanManager_TC005", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC005"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC005"}, {"name": "testMethod", "value": "scanManager_TC005"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC005"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is able to Print an Alert created from 'File Scan' from Result Tab", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to Print an Alert created from 'File Scan' from Result Tab", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241660747, "stop": 1754241660747}, {"name": "Validation message = File sent to the server for processing with id [3124]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241677694, "stop": 1754241677694}, {"name": "Alert Message = File sent to the server for processing with id [3124]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241677694, "stop": 1754241677694}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241678222, "stop": 1754241678222}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241705844, "stop": 1754241705844}, {"name": "Detection ID = 8061", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241707786, "stop": 1754241707786}, {"name": "Start Exporting Violation With Print Scope all And Document Type PDF", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241707804, "stop": 1754241707804}, {"name": "Click on close dialog button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241718835, "stop": 1754241718835}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754241660480, "stop": 1754241750077}