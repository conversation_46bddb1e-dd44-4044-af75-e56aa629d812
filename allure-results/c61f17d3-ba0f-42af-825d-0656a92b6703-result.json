{"uuid": "c61f17d3-ba0f-42af-825d-0656a92b6703", "historyId": "dc11dbd33f2d79b2f543672e1979c449", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC001.scanManager_TC001", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC001"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC001"}, {"name": "testMethod", "value": "scanManager_TC001"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC001"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is able to scan a generic text format file without creating alerts automatically option checked and take decision .", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to scan a generic text format file without creating alerts automatically option checked and take decision .", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234911722, "stop": 1754234911722}, {"name": "Validation message = File sent to the server for processing with id [3082]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234928555, "stop": 1754234928555}, {"name": "Alert Message = File sent to the server for processing with id [3082]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234928555, "stop": 1754234928555}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234929280, "stop": 1754234929280}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234940028, "stop": 1754234940028}, {"name": "Detection ID = 7958", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234941599, "stop": 1754234941599}, {"name": "Detection Status = REP", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234946358, "stop": 1754234946358}, {"name": "Detection Status = REP", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234950824, "stop": 1754234950824}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "FileScan{filePath='/Generic.txt', format='Generic Text', encoding='null', enList=null, result='null', rank='null', detectVessels='true', detectCountries='true', createAlertsAutomatically='false'}"}], "start": 1754234911722, "stop": 1754234958260}