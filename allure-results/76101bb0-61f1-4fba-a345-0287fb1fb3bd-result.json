{"uuid": "76101bb0-61f1-4fba-a345-0287fb1fb3bd", "historyId": "39fdefff98ede705eb3c9d2af44b2414", "fullName": "eastnets.admin.AuditManagerTest.checkAuditManager", "labels": [{"name": "package", "value": "eastnets.admin.AuditManagerTest"}, {"name": "testClass", "value": "eastnets.admin.AuditManagerTest"}, {"name": "testMethod", "value": "checkAuditManager"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Admin Test Cases"}, {"name": "subSuite", "value": "eastnets.admin.AuditManagerTest"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "tag", "value": "AuditManager"}], "links": [], "name": "Verify that an event recording audit is appearing properly for the removed Groups/Users", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that an event recording audit is appearing properly for the removed Groups/Users", "steps": [{"name": "Operator test data = Operator{id=0, loginName='selenium-user-828684466', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Common_Zone_01', displayName='Common Zone 01', description='Zone for most of the tests'}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264818674, "stop": 1754264818674}, {"name": "Group test data = Group{id=0, name='selenium-random-group-510194255', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='full-right-profile', enabled=true, writeRight=true, description='Profile with full Rights', zone=Zone{id=0, name='Common_Zone_01', displayName='Common Zone 01', description='Zone for most of the tests'}, profileRights={Administration={Archive Viewer=[], Operator Manager=[ALL], Group Manager=[ALL], Profile Manager=[ALL], Report Manager=[ALL], Zone Manager=[ALL], Audit Manager=[], Event Viewer=[]}, RPTSRV={}, SWS={License Manager=[], Name Checker=[ALL], WorldCheckSettings=[ALL], Batch List Management=[], ISO20022 Format Configuration=[ALL], DowJonesSettings=[ALL], ListSetManager=[ALL], MQ Connector=[ALL], DB Scanner=[ALL], Customer Card=[ALL], Good Guys Migration=[], SAA OFCA Station=[ALL], SAA OFCS Detect=[ALL], List Manager=[ALL], SafeTrade=[ALL], en.Reporting Scanner=[ALL], SWIFT Manager=[ALL], SWP Name Checker=[ALL], File Based Archive=[ALL], Detection Manager=[ALL], Approval Configuration=[ALL], DB Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], SAA OFCS Monitor=[ALL], Archive Management=[ALL], Event Viewer=[], ISO20022-Module=[ALL], ListExplorer=[ALL], ISO20022 Schema Configuration=[ALL], MQ Manager=[ALL], Archive Viewer=[ALL], Replay=[ALL], StrippingDetectorManager=[ALL], Report Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL], GoodGuyExplorer=[ALL], SafeWatch API=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-828684466', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Common_Zone_01', displayName='Common Zone 01', description='Zone for most of the tests'}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264818683, "stop": 1754264818683}, {"name": "Create new operator.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264818683, "stop": 1754264818683}, {"name": "Operator test Data = Operator{id=0, loginName='selenium-user-828684466', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Common_Zone_01', displayName='Common Zone 01', description='Zone for most of the tests'}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264818683, "stop": 1754264818683}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264821470, "stop": 1754264821470}, {"name": "Set login name = selenium-user-828684466", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264827098, "stop": 1754264827098}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264827883, "stop": 1754264827883}, {"name": "Click Create new operator button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264828628, "stop": 1754264828628}, {"name": "Set login name = selenium-user-828684466", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264828882, "stop": 1754264828882}, {"name": "Set first name = Random", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264830479, "stop": 1754264830479}, {"name": "Set last name = User", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264831173, "stop": 1754264831173}, {"name": "Set password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264831836, "stop": 1754264831836}, {"name": "Set confirm password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264832571, "stop": 1754264832571}, {"name": "Set enable flag = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264833296, "stop": 1754264833296}, {"name": "Set reset flag = false", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264834081, "stop": 1754264834081}, {"name": "Select zone  = Common Zone 01", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264835431, "stop": 1754264835431}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264841909, "stop": 1754264841909}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264842275, "stop": 1754264842275}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264850677, "stop": 1754264850677}, {"name": "Set login name = selenium-user-828684466", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264856979, "stop": 1754264856979}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264859779, "stop": 1754264859779}, {"name": "Create first group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264861646, "stop": 1754264861646}, {"name": "Group test Data = Group{id=0, name='selenium-random-group-510194255', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='full-right-profile', enabled=true, writeRight=true, description='Profile with full Rights', zone=Zone{id=0, name='Common_Zone_01', displayName='Common Zone 01', description='Zone for most of the tests'}, profileRights={Administration={Archive Viewer=[], Operator Manager=[ALL], Group Manager=[ALL], Profile Manager=[ALL], Report Manager=[ALL], Zone Manager=[ALL], Audit Manager=[], Event Viewer=[]}, RPTSRV={}, SWS={License Manager=[], Name Checker=[ALL], WorldCheckSettings=[ALL], Batch List Management=[], ISO20022 Format Configuration=[ALL], DowJonesSettings=[ALL], ListSetManager=[ALL], MQ Connector=[ALL], DB Scanner=[ALL], Customer Card=[ALL], Good Guys Migration=[], SAA OFCA Station=[ALL], SAA OFCS Detect=[ALL], List Manager=[ALL], SafeTrade=[ALL], en.Reporting Scanner=[ALL], SWIFT Manager=[ALL], SWP Name Checker=[ALL], File Based Archive=[ALL], Detection Manager=[ALL], Approval Configuration=[ALL], DB Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], SAA OFCS Monitor=[ALL], Archive Management=[ALL], Event Viewer=[], ISO20022-Module=[ALL], ListExplorer=[ALL], ISO20022 Schema Configuration=[ALL], MQ Manager=[ALL], Archive Viewer=[ALL], Replay=[ALL], StrippingDetectorManager=[ALL], Report Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL], GoodGuyExplorer=[ALL], SafeWatch API=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-828684466', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Common_Zone_01', displayName='Common Zone 01', description='Zone for most of the tests'}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264861646, "stop": 1754264861646}, {"name": "Remove operator with login name = selenium-user-828684466.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264924285, "stop": 1754264924285}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264926120, "stop": 1754264926120}, {"name": "Set login name = selenium-user-828684466", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264931680, "stop": 1754264931680}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264933270, "stop": 1754264933270}, {"name": "Select operator and click remove button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264934826, "stop": 1754264934826}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264936408, "stop": 1754264936408}, {"name": "Set login name = selenium-user-828684466", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264941920, "stop": 1754264941920}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264943170, "stop": 1754264943170}, {"name": "Remove group with name = selenium-random-group-510194255.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264944409, "stop": 1754264944409}, {"name": "Select group and click remove button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264955654, "stop": 1754264955654}, {"name": "Asserting on data in result view", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265024170, "stop": 1754265024170}, {"name": "<PERSON><PERSON><PERSON> Passed Successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265024172, "stop": 1754265024172}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754264818654, "stop": 1754265024172}