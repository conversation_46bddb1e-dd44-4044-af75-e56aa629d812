{"uuid": "2d5e1062-9623-4d5d-b394-19a28fb91ea1", "historyId": "c0c16a88d02b3d5fb122d5bc62f85540", "fullName": "eastnets.screening.regression.iso20022configurations.ISO20022_TC002.iso20022_TC0012", "labels": [{"name": "package", "value": "eastnets.screening.regression.iso20022configurations.ISO20022_TC002"}, {"name": "testClass", "value": "eastnets.screening.regression.iso20022configurations.ISO20022_TC002"}, {"name": "testMethod", "value": "iso20022_TC0012"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "ISO20022 Configurations Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.iso20022configurations.ISO20022_TC002"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-3(38)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "tag", "value": "ISO20022"}, {"name": "tag", "value": "Regression"}, {"name": "owner", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "links": [{"name": "87504", "type": "issue"}], "name": "Tfs 87504: Scan ISO message without xml tag having body fields only while <PERSON><PERSON> is added to config and no Wrapper defined.", "status": "failed", "statusDetails": {"known": false, "muted": false, "flaky": false, "message": "Test Failed due to an exception occurred in POM's method", "trace": "java.lang.AssertionError: Test Failed due to an exception occurred in POM's method\r\n\tat org.testng.Assert.fail(Assert.java:98)\r\n\tat core.ExceptionHandler.onExceptionRaised(ExceptionHandler.java:13)\r\n\tat eastnets.screening.regression.iso20022configurations.ISO20022_TC002.iso20022_TC0012(ISO20022_TC002.java:143)\r\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:578)\r\n\tat org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:677)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:221)\r\n\tat org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:50)\r\n\tat org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:969)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:194)\r\n\tat org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148)\r\n\tat org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128)\r\n\tat java.base/java.util.ArrayList.forEach(ArrayList.java:1511)\r\n\tat org.testng.TestRunner.privateRun(TestRunner.java:829)\r\n\tat org.testng.TestRunner.run(TestRunner.java:602)\r\n\tat org.testng.SuiteRunner.runTest(SuiteRunner.java:437)\r\n\tat org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:475)\r\n\tat org.testng.internal.thread.ThreadUtil.lambda$execute$0(ThreadUtil.java:58)\r\n\tat java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)\r\n\tat java.base/java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:132)\r\n\tat java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:113)\r\n\tat java.base/java.util.concurrent.AbstractExecutorService.invokeAll(AbstractExecutorService.java:274)\r\n\tat org.testng.internal.thread.ThreadUtil.execute(ThreadUtil.java:64)\r\n\tat org.testng.SuiteRunner.runInParallelTestMode(SuiteRunner.java:458)\r\n\tat org.testng.SuiteRunner.privateRun(SuiteRunner.java:389)\r\n\tat org.testng.SuiteRunner.run(SuiteRunner.java:330)\r\n\tat org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)\r\n\tat org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)\r\n\tat org.testng.TestNG.runSuitesSequentially(TestNG.java:1256)\r\n\tat org.testng.TestNG.runSuitesLocally(TestNG.java:1176)\r\n\tat org.testng.TestNG.runSuites(TestNG.java:1099)\r\n\tat org.testng.TestNG.run(TestNG.java:1067)\r\n\tat com.intellij.rt.testng.IDEARemoteTestNG.run(IDEARemoteTestNG.java:65)\r\n\tat com.intellij.rt.testng.RemoteTestNGStarter.main(RemoteTestNGStarter.java:105)\r\nCaused by: org.openqa.selenium.TimeoutException: Expected condition failed: waiting for number of elements found by By.xpath: //form[@id='topbar-right']/ul/label to be more than \"0\". Current number: \"0\" (tried for 120 second(s) with 10 milliseconds interval)\nBuild info: version: '4.13.0', revision: 'ba948ece5b*'\nSystem info: os.name: 'Windows Server 2022', os.arch: 'amd64', os.version: '10.0', java.version: '19.0.2'\nDriver info: driver.version: unknown\r\n\tat org.openqa.selenium.support.ui.FluentWait.timeoutException(FluentWait.java:262)\r\n\tat org.openqa.selenium.support.ui.FluentWait.until(FluentWait.java:230)\r\n\tat core.gui.Controls.getWebElement(Controls.java:29)\r\n\tat core.gui.Controls.getLabelValue(Controls.java:62)\r\n\tat eastnets.common.gui.Navigation.navigate(Navigation.java:65)\r\n\tat eastnets.screening.control.listManager.ListSetControl.navigate_to_listSet(ListSetControl.java:43)\r\n\tat eastnets.screening.control.listManager.ListSetControl.create_list_set(ListSetControl.java:69)\r\n\tat core.CommonTestMethods.createListSet(CommonTestMethods.java:150)\r\n\tat eastnets.screening.regression.iso20022configurations.ISO20022_TC002.iso20022_TC0012(ISO20022_TC002.java:114)\r\n\t... 34 more\r\n"}, "stage": "finished", "description": "Tfs 87504: Scan ISO message without xml tag having body fields only while <PERSON><PERSON> is added to config and no Wrapper defined.", "steps": [{"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-27463502', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@2f038561, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-27463502', officialDate='null', entry=[ListEntry{type='null', name='EntryName-27463502', firstName='EntryFirstName-27463502', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-27463502', firstName='EntryFirstName-27463502', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754232000887, "stop": 1754232000887}, {"name": "Search for list by listName = ListName-27463502 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754232026987, "stop": 1754232026987}, {"name": "Navigate to By.xpath: //a[contains(text(),'ISO20022 Format Configuration')]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754232043432, "stop": 1754232043432}, {"name": "Navigate to By.xpath: //a[contains(text(),'ISO20022 Format Configuration')]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754232047934, "stop": 1754232047934}, {"name": "Create New Group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754232056209, "stop": 1754232056209}, {"name": "Connect to Database and Check if User Profile = full-right-profile_07 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754232067061, "stop": 1754232067061}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754232067065, "stop": 1754232067065}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754232067182, "stop": 1754232067182}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_07' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754232067182, "stop": 1754232067182}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754232067185, "stop": 1754232067185}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754232067186, "stop": 1754232067186}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754232067186, "stop": 1754232067186}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754232067222, "stop": 1754232067222}, {"name": "Delete From tListSetProfile where profile_id in (10)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754232067222, "stop": 1754232067222}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754232067229, "stop": 1754232067229}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754232067229, "stop": 1754232067229}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754232067230, "stop": 1754232067230}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754232067230, "stop": 1754232067230}, {"name": "Black list already exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754232119114, "stop": 1754232119114}, {"name": "Attempting to save screenshot...", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754232247498, "stop": 1754232247498}, {"name": "Screenshot saved at: C:\\Users\\<USER>\\IdeaProjects\\SWS_selenium\\FailedTestsScreenshots\\screenshot_1754232247498.png", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754232247508, "stop": 1754232247508}, {"name": "Error occurred While logging in eastnets.screening.regression.iso20022configurations.ISO20022_TC002$5.iso20022_TC0012 ----> Expected condition failed: waiting for number of elements found by By.xpath: //form[@id='topbar-right']/ul/label to be more than \"0\". Current number: \"0\" (tried for 120 second(s) with 10 milliseconds interval)\nBuild info: version: '4.13.0', revision: 'ba948ece5b*'\nSystem info: os.name: 'Windows Server 2022', os.arch: 'amd64', os.version: '10.0', java.version: '19.0.2'\nDriver info: driver.version: unknown", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754232247522, "stop": 1754232247522}], "attachments": [{"name": "Screenshots", "source": "a51cf024-ac65-4aea-bf0f-e27ebb8c36b1-attachment.png"}], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "ISO20022FormatDetailsConfiguration{headerField='null', bodyField='CdtTrfTxInf/PmtId/TxId', Xpath='null', bodyFieldType='null', SEPA='null', category='SKIPPED', quickLink='null', fieldLinkWith='null', iso20022FormatConfiguration$=ISO20022FormatConfiguration{zone='null', groupName='Group_Name-%s', iso20022SchemaConfiguration=ISO20022SchemaConfiguration{schemaName='head.001.001', schemaVersion='01', headerSwift='head.001.001.01', expectedResults='Schema version [%s] successfully deleted'}, expectedResults='Successfully imported schema [%s] with version [%s].'}}"}], "start": 1754232000872, "stop": 1754232247523}