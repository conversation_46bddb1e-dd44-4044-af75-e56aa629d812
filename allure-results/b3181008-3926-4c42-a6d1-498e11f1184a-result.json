{"uuid": "b3181008-3926-4c42-a6d1-498e11f1184a", "historyId": "f56da0e4008bbe8f23f23952f283da46", "fullName": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC022.listManager_TC022", "labels": [{"name": "package", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC022"}, {"name": "testClass", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC022"}, {"name": "testMethod", "value": "listManager_TC022"}, {"name": "parentSuite", "value": "Failed Tests Rerun Suite"}, {"name": "suite", "value": "<PERSON><PERSON> Failed Tests"}, {"name": "subSuite", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC022"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "List Manager"}], "links": [], "name": "Verify GPI Lables as conditions and Add SERV_TYPE_ID (SLA_ID) and UETR as violation filter and make sure that the detection has External status.", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify GPI Lables as conditions and Add SERV_TYPE_ID (SLA_ID) and UETR as violation filter and make sure that the detection has External status.", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188987068, "stop": 1754188987068}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-02'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188987068, "stop": 1754188987068}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188987075, "stop": 1754188987075}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188987075, "stop": 1754188987075}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188987075, "stop": 1754188987075}, {"name": "Login with User Name = operator-02 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188987280, "stop": 1754188987280}, {"name": "Create swift-code, black list and list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188992700, "stop": 1754188992700}, {"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-93276934', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@5ad5469c, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-93276934', officialDate='null', entry=[ListEntry{type='null', name='EntryName-93276934', firstName='EntryFirstName-93276934', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-93276934', firstName='EntryFirstName-93276934', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188992710, "stop": 1754188992710}, {"name": "Connect to Database and Check if User Profile = full-right-profile_002 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188992710, "stop": 1754188992710}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188992710, "stop": 1754188992710}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188992748, "stop": 1754188992748}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_002' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188992748, "stop": 1754188992748}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188992750, "stop": 1754188992750}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188992750, "stop": 1754188992750}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188992751, "stop": 1754188992751}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188992771, "stop": 1754188992771}, {"name": "Delete From tListSetProfile where profile_id in (5)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188992771, "stop": 1754188992771}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188992775, "stop": 1754188992775}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188992775, "stop": 1754188992775}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188992775, "stop": 1754188992775}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188992775, "stop": 1754188992775}, {"name": "Search for list by listName = ListName-93276934 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189012758, "stop": 1754189012758}, {"name": "Set zone : Common Zone 02", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189021906, "stop": 1754189021906}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189024069, "stop": 1754189024069}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189024705, "stop": 1754189024720}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189025179, "stop": 1754189025179}, {"name": "Set template name = templateName-93276934", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189025424, "stop": 1754189025424}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189027705, "stop": 1754189027705}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189028471, "stop": 1754189028471}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189029654, "stop": 1754189029654}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189030401, "stop": 1754189030401}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189064574, "stop": 1754189064574}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189064574, "stop": 1754189064574}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189128857, "stop": 1754189128857}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189128857, "stop": 1754189128857}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189128857, "stop": 1754189128857}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189128902, "stop": 1754189128902}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_002'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189128902, "stop": 1754189128902}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189128905, "stop": 1754189128905}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189128906, "stop": 1754189128906}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189128906, "stop": 1754189128906}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189146495, "stop": 1754189146495}, {"name": "Validation message = Syntax is valid!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189181214, "stop": 1754189181214}, {"name": "Validation message = Syntax is valid!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189181214, "stop": 1754189181214}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189184425, "stop": 1754189184425}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189184425, "stop": 1754189184425}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189184434, "stop": 1754189184434}, {"name": "Validation message = File sent to the server for processing with id [3053]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189196869, "stop": 1754189196869}, {"name": "Alert Message = File sent to the server for processing with id [3053]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189196869, "stop": 1754189196869}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189198147, "stop": 1754189198147}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189206001, "stop": 1754189206001}, {"name": "Detection ID = 7887", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189206645, "stop": 1754189206645}, {"name": "Status detection = External", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189206881, "stop": 1754189206881}, {"name": "Unmatched roles = SERV_TYPE_ID <> '001'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189208362, "stop": 1754189208362}, {"name": "Validation message = Syntax is valid!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189230917, "stop": 1754189230917}, {"name": "Validation message = Syntax is valid!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189230917, "stop": 1754189230917}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189233818, "stop": 1754189233818}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189233818, "stop": 1754189233818}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189233818, "stop": 1754189233818}, {"name": "Validation message = File sent to the server for processing with id [3054]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189246227, "stop": 1754189246227}, {"name": "Alert Message = File sent to the server for processing with id [3054]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189246227, "stop": 1754189246227}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189247284, "stop": 1754189247284}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189255207, "stop": 1754189255207}, {"name": "Detection ID = 7888", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189256122, "stop": 1754189256122}, {"name": "Status detection = External", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189256375, "stop": 1754189256375}, {"name": "Unmatched roles = UETR <> '8E0FE365-A88E-426B-8484-4FB7FEE92742'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754189257618, "stop": 1754189257618}], "attachments": [], "parameters": [], "start": 1754188986667, "stop": 1754189260250}