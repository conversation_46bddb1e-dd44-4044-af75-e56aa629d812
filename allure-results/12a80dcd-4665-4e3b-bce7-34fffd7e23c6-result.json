{"uuid": "12a80dcd-4665-4e3b-bce7-34fffd7e23c6", "historyId": "c6142a835c74d3e0aa41bd07b46dcd7d", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC005.scanManager_TC005", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC005"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC005"}, {"name": "testMethod", "value": "scanManager_TC005"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC005"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is able to Print an Alert created from 'File Scan' from Result Tab", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to Print an Alert created from 'File Scan' from Result Tab", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141741165, "stop": 1754141741165}, {"name": "Validation message = File sent to the server for processing with id [2868]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141754267, "stop": 1754141754267}, {"name": "Alert Message = File sent to the server for processing with id [2868]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141754267, "stop": 1754141754267}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141755916, "stop": 1754141755916}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141780703, "stop": 1754141780703}, {"name": "Detection ID = 7206", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141781996, "stop": 1754141781996}, {"name": "Start Exporting Violation With Print Scope all And Document Type PDF", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141782054, "stop": 1754141782054}, {"name": "Click on close dialog button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141791307, "stop": 1754141791307}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754141740937, "stop": 1754141822540}