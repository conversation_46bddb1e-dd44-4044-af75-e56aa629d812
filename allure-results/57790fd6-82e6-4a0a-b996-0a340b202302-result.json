{"uuid": "57790fd6-82e6-4a0a-b996-0a340b202302", "historyId": "f724d3c9e1639024bef130abe66fe7ec", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC006.scanManager_TC006", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC006"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC006"}, {"name": "testMethod", "value": "scanManager_TC006"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC006"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is not able to add a Good Guy for a detection by clicking accept as shared button for non-shared black list", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is not able to add a Good Guy for a detection by clicking accept as shared button for non-shared black list", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235332684, "stop": 1754235332684}, {"name": "Status for the scanned name = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235347154, "stop": 1754235347154}, {"name": "<PERSON>ose scanned name from result table يا<PERSON><PERSON> عمر", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235347155, "stop": 1754235347155}, {"name": "Actual validation Message = Cannot create a shared Good Guy against the non-shared black list ListName-548660493'.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235354150, "stop": 1754235354150}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754235332683, "stop": 1754235354813}