{"uuid": "3d1f5726-f9af-420f-bbbf-61044cd6aa87", "name": "ISO20022 Configurations Test Cases", "children": ["879f938e-04d0-4982-abf9-141817c3adb8", "b66aecdf-ef8e-4fb2-808a-eef9a6e600ea", "f463371e-cb70-4847-9dd0-afafed1c2792", "9fc64137-c1c4-4455-a852-55bd6ddbefe8", "2361add3-b6fd-4b6e-8cdd-50525c1c642e"], "befores": [{"name": "Setting up selenium web driver before each class run", "status": "passed", "stage": "finished", "description": "Setting up selenium web driver before each class run", "steps": [{"name": "Initialize Selenium driver before tests' Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234471074, "stop": 1754234471074}, {"name": "Selenium webDriver was initialized successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234480500, "stop": 1754234480500}], "attachments": [], "parameters": [], "start": 1754234471072, "stop": 1754234480500}], "afters": [{"name": "Quitting selenium driver after each class run", "status": "passed", "stage": "finished", "description": "Quitting selenium driver after each class run", "steps": [{"name": "Closing selenium WebDriver after Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235372111, "stop": 1754235372111}], "attachments": [], "parameters": [], "start": 1754235372111, "stop": 1754235372548}], "start": 1754234471061, "stop": 1754235372552}