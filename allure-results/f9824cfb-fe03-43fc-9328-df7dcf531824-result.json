{"uuid": "f9824cfb-fe03-43fc-9328-df7dcf531824", "historyId": "d658099ac92a63bb08eb5fb35a9c7ca0", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC009.scanManager_TC009", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC009"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC009"}, {"name": "testMethod", "value": "scanManager_TC009"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC009"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is able to Print an alert report in Excel format", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to Print an alert report in Excel format", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173104776, "stop": 1754173104776}, {"name": "Validation message = File sent to the server for processing with id [2982]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173124688, "stop": 1754173124688}, {"name": "Alert Message = File sent to the server for processing with id [2982]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173124688, "stop": 1754173124688}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173126053, "stop": 1754173126053}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173136889, "stop": 1754173136889}, {"name": "Detection ID = 7583", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173138653, "stop": 1754173138653}, {"name": "Start Exporting Violation With Print Scope all And Document Type Excel", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173138664, "stop": 1754173138664}, {"name": "Click on close dialog button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173149226, "stop": 1754173149226}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "eastnets.screening.entity.Report@5cec32c2"}], "start": 1754173104420, "stop": 1754173180864}