{"uuid": "1f5ccd75-2423-43da-b68e-15106a484419", "name": "Scan Manager Test Cases", "children": ["72265083-74bc-4fe3-babb-fd98828f4186", "c61f17d3-ba0f-42af-825d-0656a92b6703", "17a66fe4-c80b-4d88-a81c-066b202e5ddd", "35c66496-a303-4a0e-9ac3-88b86aa7063b", "3fd14bd0-9498-4c93-9ab7-ed83afb06ade", "b13070bc-b599-494e-aa56-6e29e939a907", "57790fd6-82e6-4a0a-b996-0a340b202302", "ccf9a49a-b493-4a60-a774-0e694ab66c64", "0ad39102-990c-4a2d-b821-3fba1db5c9ef", "c88ff483-e869-49e4-8e71-07ebf3dcf45b", "fc9ae6cc-b9a9-4b10-95f9-ac1f5016604b", "976e31bd-3fde-4694-af9a-5fb278c29871", "6650e523-ee14-45a2-8fcb-0e103542fc9c", "b3eea0c4-3cea-49eb-87a6-1c8580efb3ae", "5aaebd69-f5e9-4c5c-8a10-5080d9dcdb53", "a3c68278-9062-4267-b489-accbb823e252", "fad0e864-d010-4ab7-9e01-304414653cf4"], "befores": [{"name": "Setting up selenium web driver before each class run", "status": "passed", "stage": "finished", "description": "Setting up selenium web driver before each class run", "steps": [{"name": "Initialize Selenium driver before tests' Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234471354, "stop": 1754234471354}, {"name": "Selenium webDriver was initialized successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754234480441, "stop": 1754234480441}], "attachments": [], "parameters": [], "start": 1754234471354, "stop": 1754234480441}], "afters": [{"name": "Quitting selenium driver after each class run", "status": "passed", "stage": "finished", "description": "Quitting selenium driver after each class run", "steps": [{"name": "Closing selenium WebDriver after Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236016061, "stop": 1754236016061}], "attachments": [], "parameters": [], "start": 1754236016061, "stop": 1754236016704}], "start": 1754234471061, "stop": 1754236016704}