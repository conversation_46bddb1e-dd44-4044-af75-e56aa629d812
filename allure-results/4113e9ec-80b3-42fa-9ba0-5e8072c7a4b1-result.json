{"uuid": "4113e9ec-80b3-42fa-9ba0-5e8072c7a4b1", "historyId": "e9d44a66fb796025c18dc7707e21fe3c", "fullName": "eastnets.screening.regression.detectionmanager.DetectionManager_TC026.detectionManager_TC026", "labels": [{"name": "package", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC026"}, {"name": "testClass", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC026"}, {"name": "testMethod", "value": "detectionManager_TC026"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Detection Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC026"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Detection Manager"}], "links": [], "name": "Detection Manager - Verify that Detections return from RJE msg without and with GPI labels return when selecting 'both' option from  drop down list\n Verify that the detection was found in detection manager", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that the detection was found in detection manager\n* Filtering - Verify that 'SLA ID' column showing the value for '111'  field in RJE msgs in 'Detections' table\n* Filtering - Verify that 'UETR' column showing the value for '121'  field in RJE msgs in 'Detections' table\n* Filtering - Detection Manager - Verify that only Detections return from RJE msgs without gpi lables return when selecting 'RJE' option from 'Filter By' drop down list\n* Filtering - Detection Manager - Verify that only Detections return from msgs with gpi lables return when selecting 'gpi' option from 'Filter By' drop down list\n* Filtering - Verify that Search working properly when searching for RJE msgs using 'SLA ID' and  'UETR' values\n* Filtering - Detection Manager - Verify that new 'UETR' checkboxe dispalys in 'Column Panel'\n* Filtering - Detection Manager - Verify that new 'SLA ID' checkboxe dispalys in 'Column Panel'", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153276301, "stop": 1754153276301}, {"name": "update tConfig set variable_value = 'Yes' where variable_name = 'Change detection status to DontKnow'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153276301, "stop": 1754153276301}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153276305, "stop": 1754153276305}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153276305, "stop": 1754153276305}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153276305, "stop": 1754153276305}, {"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-872682330', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@26a79d07, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-872682330', officialDate='null', entry=[ListEntry{type='null', name='EntryName-872682330', firstName='EntryFirstName-872682330', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-872682330', firstName='EntryFirstName-872682330', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153276307, "stop": 1754153276307}, {"name": "Connect to Database and Check if User Profile = full-right-profile_05 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153276307, "stop": 1754153276307}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153276308, "stop": 1754153276308}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153276348, "stop": 1754153276348}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_05' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153276348, "stop": 1754153276348}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153276350, "stop": 1754153276350}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153276350, "stop": 1754153276350}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153276351, "stop": 1754153276351}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153276370, "stop": 1754153276370}, {"name": "Delete From tListSetProfile where profile_id in (8)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153276371, "stop": 1754153276371}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153276374, "stop": 1754153276374}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153276374, "stop": 1754153276374}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153276375, "stop": 1754153276375}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153276375, "stop": 1754153276375}, {"name": "Search for list by listName = ListName-872682330 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153295987, "stop": 1754153295987}, {"name": "Set zone : Common Zone 05", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153303609, "stop": 1754153303609}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153305651, "stop": 1754153305651}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153306141, "stop": 1754153306141}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153306531, "stop": 1754153306531}, {"name": "Set template name = templateName-872682330", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153306814, "stop": 1754153306814}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153308628, "stop": 1754153308628}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153309345, "stop": 1754153309345}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153310323, "stop": 1754153310323}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153310896, "stop": 1754153310896}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153380884, "stop": 1754153380884}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153380884, "stop": 1754153380884}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153453382, "stop": 1754153453382}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153453382, "stop": 1754153453382}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153453382, "stop": 1754153453382}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153453423, "stop": 1754153453423}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_05'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153453423, "stop": 1754153453423}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153453425, "stop": 1754153453425}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153453425, "stop": 1754153453425}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153453426, "stop": 1754153453426}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153470513, "stop": 1754153470513}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153476212, "stop": 1754153476212}, {"name": "Validation message = File sent to the server for processing with id [2937]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153491255, "stop": 1754153491255}, {"name": "Alert Message = File sent to the server for processing with id [2937]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153491255, "stop": 1754153491255}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153492202, "stop": 1754153492202}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153500749, "stop": 1754153500749}, {"name": "Detection ID = 7479", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153501877, "stop": 1754153501877}, {"name": "Status detection = Reported", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153502084, "stop": 1754153502084}, {"name": "Detection ID = 7479", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153503261, "stop": 1754153503261}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153503261, "stop": 1754153503261}, {"name": "Validation message = File sent to the server for processing with id [2938]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153517649, "stop": 1754153517649}, {"name": "Alert Message = File sent to the server for processing with id [2938]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153517649, "stop": 1754153517649}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153518188, "stop": 1754153518188}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153526967, "stop": 1754153526967}, {"name": "Detection ID = 7480", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153527936, "stop": 1754153527936}, {"name": "Status detection = Reported", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153528142, "stop": 1754153528142}, {"name": "Detection ID = 7480", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153529170, "stop": 1754153529170}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754153546336, "stop": 1754153546336}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754153276280, "stop": 1754153612429}