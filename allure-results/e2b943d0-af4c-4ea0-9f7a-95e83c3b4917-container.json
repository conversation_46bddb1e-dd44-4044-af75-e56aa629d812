{"uuid": "e2b943d0-af4c-4ea0-9f7a-95e83c3b4917", "name": "<PERSON><PERSON> Failed Tests", "children": ["d96f0249-80f3-4391-a36c-5258f91b13dd", "943fd55e-56f2-4d7d-8152-e9dfa9eb8333", "80935536-e760-4148-a834-f0f7262d1980", "201c474a-e707-4e06-9ad2-3da30f1b93f4", "af52a2a4-02a5-4000-be4b-c0747197c42b", "ca18383a-cf21-4dc8-b52d-1434c470a212", "3d77a083-de52-4cd0-8eb6-37767a0cb9f3"], "befores": [{"name": "Setting up selenium web driver before each class run", "status": "passed", "stage": "finished", "description": "Setting up selenium web driver before each class run", "steps": [{"name": "Initialize Selenium driver before tests' Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238475247, "stop": 1754238475247}, {"name": "Selenium webDriver was initialized successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238481077, "stop": 1754238481077}], "attachments": [], "parameters": [], "start": 1754238475247, "stop": 1754238481077}], "afters": [{"name": "Quitting selenium driver after each class run", "status": "passed", "stage": "finished", "description": "Quitting selenium driver after each class run", "steps": [{"name": "Closing selenium WebDriver after Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754239627939, "stop": 1754239627939}], "attachments": [], "parameters": [], "start": 1754239627939, "stop": 1754239628320}], "start": 1754238475246, "stop": 1754239628320}