{"uuid": "5599ac26-0635-436e-a9dc-675f4744f324", "historyId": "4cc1bb711612ab6667e72b070ccbee6c", "fullName": "eastnets.screening.regression.detectionmanager.DetectionManager_TC023.detectionManager_TC023", "labels": [{"name": "package", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC023"}, {"name": "testClass", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC023"}, {"name": "testMethod", "value": "detectionManager_TC023"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Detection Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.detectionmanager.DetectionManager_TC023"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-3(38)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Detection Manager"}], "links": [], "name": "detectionManager_TC023", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754242197932, "stop": 1754242197932}