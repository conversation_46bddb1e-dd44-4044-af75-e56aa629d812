{"uuid": "3a7289d6-a208-4a63-aa8d-48c359e55c16", "historyId": "216e7de66c36f10e5f5e920acf0b404f", "fullName": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC018.ListManager_TC018", "labels": [{"name": "package", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC018"}, {"name": "testClass", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC018"}, {"name": "testMethod", "value": "ListManager_TC018"}, {"name": "parentSuite", "value": "Failed Tests Rerun Suite"}, {"name": "suite", "value": "<PERSON><PERSON> Failed Tests"}, {"name": "subSuite", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC018"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "List Manager"}], "links": [], "name": "Verify that user is able to Add, modify, delete and a World Check flow as PEP type", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to Add a new World Check flow as PEP type\nVerify that user is able to Modify a World Check flow\nVerify that user is able to Delete a World Check flow\nVerify that user is able to search for a World Check flow by Zone", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754157472564, "stop": 1754157472564}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-02'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754157472564, "stop": 1754157472564}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754157472570, "stop": 1754157472570}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754157472570, "stop": 1754157472570}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754157472571, "stop": 1754157472571}, {"name": "Login with User Name = operator-02 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754157472724, "stop": 1754157472724}, {"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-970226488', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@69b4135a, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-970226488', officialDate='null', entry=[ListEntry{type='null', name='EntryName-970226488', firstName='EntryFirstName-970226488', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-970226488', firstName='EntryFirstName-970226488', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754157476713, "stop": 1754157476713}, {"name": "Create new black list", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754157476713, "stop": 1754157476713}, {"name": "Search for list by listName = ListName-970226488 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754157493264, "stop": 1754157493264}, {"name": "Verify that user is able to Add a new World Check flow as PEP type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754157497934, "stop": 1754157497934}, {"name": "Verify that user is able to Modify a World Check flow", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754157523009, "stop": 1754157523009}, {"name": "Verify that user is able to Delete a World Check flow", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754157529523, "stop": 1754157529523}], "attachments": [], "parameters": [{"name": "arg0", "value": "PEP"}], "start": 1754157472193, "stop": 1754157537942}