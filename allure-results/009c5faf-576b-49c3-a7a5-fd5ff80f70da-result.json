{"uuid": "009c5faf-576b-49c3-a7a5-fd5ff80f70da", "historyId": "f22afc7b7cbc88e8a8f443201b15231e", "fullName": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC021.listManager_TC021", "labels": [{"name": "package", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC021"}, {"name": "testClass", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC021"}, {"name": "testMethod", "value": "listManager_TC021"}, {"name": "parentSuite", "value": "Failed Tests Rerun Suite"}, {"name": "suite", "value": "<PERSON><PERSON> Failed Tests"}, {"name": "subSuite", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC021"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "List Manager"}], "links": [], "name": "Verify List set configuration - Assign clean alert - Assign Auto create alert - Assign clean & External alert", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify List set configuration - Assign clean alert - Assign Auto create alert - Assign clean & External alert", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188579867, "stop": 1754188579867}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-02'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188579867, "stop": 1754188579867}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188579876, "stop": 1754188579876}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188579876, "stop": 1754188579876}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188579876, "stop": 1754188579876}, {"name": "Login with User Name = operator-02 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188580045, "stop": 1754188580045}, {"name": "Create swift-code, black list and list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188585355, "stop": 1754188585355}, {"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-278312040', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@4ea6c853, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-278312040', officialDate='null', entry=[ListEntry{type='null', name='EntryName-278312040', firstName='EntryFirstName-278312040', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-278312040', firstName='EntryFirstName-278312040', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188585357, "stop": 1754188585357}, {"name": "Connect to Database and Check if User Profile = full-right-profile_002 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188585357, "stop": 1754188585357}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188585358, "stop": 1754188585358}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188585398, "stop": 1754188585398}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_002' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188585399, "stop": 1754188585399}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188585402, "stop": 1754188585402}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188585402, "stop": 1754188585402}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188585402, "stop": 1754188585402}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188585421, "stop": 1754188585421}, {"name": "Delete From tListSetProfile where profile_id in (5)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188585421, "stop": 1754188585421}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188585426, "stop": 1754188585426}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188585426, "stop": 1754188585426}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188585426, "stop": 1754188585426}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188585426, "stop": 1754188585426}, {"name": "Search for list by listName = ListName-278312040 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188606966, "stop": 1754188606966}, {"name": "Set zone : Common Zone 02", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188616086, "stop": 1754188616086}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188618533, "stop": 1754188618533}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188619141, "stop": 1754188619141}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188619526, "stop": 1754188619526}, {"name": "Set template name = templateName-278312040", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188619803, "stop": 1754188619803}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188621737, "stop": 1754188621737}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188622489, "stop": 1754188622489}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188623907, "stop": 1754188623907}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188624597, "stop": 1754188624597}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188660115, "stop": 1754188660115}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188660115, "stop": 1754188660115}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188726118, "stop": 1754188726118}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188726119, "stop": 1754188726119}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188726119, "stop": 1754188726119}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188726146, "stop": 1754188726146}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_002'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188726146, "stop": 1754188726146}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188726149, "stop": 1754188726149}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188726149, "stop": 1754188726149}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188726150, "stop": 1754188726150}, {"name": "Validation message = Syntax is valid!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188749702, "stop": 1754188749702}, {"name": "Validation message = Syntax is valid!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188749702, "stop": 1754188749702}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188753142, "stop": 1754188753142}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188753143, "stop": 1754188753143}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188780988, "stop": 1754188780988}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188815073, "stop": 1754188815073}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188849878, "stop": 1754188849878}, {"name": "Select list set by name.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188876430, "stop": 1754188876430}, {"name": "Validation message = Good guy successfully created!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188886109, "stop": 1754188886109}, {"name": "Alert message = Good guy successfully created!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188886109, "stop": 1754188886109}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188911136, "stop": 1754188911136}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188911137, "stop": 1754188911137}, {"name": "Status for the scanned name = CLEAN", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188926276, "stop": 1754188926276}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188926276, "stop": 1754188926276}, {"name": "Status for the scanned name = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188940540, "stop": 1754188940540}, {"name": "Investigator for the scanned name = operator-02", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188940763, "stop": 1754188940763}, {"name": "Investigator = operator-02", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188940763, "stop": 1754188940763}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188940763, "stop": 1754188940763}, {"name": "Status for the scanned name = REP", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188955416, "stop": 1754188955416}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188955416, "stop": 1754188955416}, {"name": "Status for the scanned name = EXT", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188969718, "stop": 1754188969718}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188969718, "stop": 1754188969718}, {"name": "Status for the scanned name = ACC", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754188983842, "stop": 1754188983842}], "attachments": [], "parameters": [], "start": 1754188579507, "stop": 1754188986652}