{"uuid": "d03e7436-a780-4f47-9c70-e147f0e7a111", "name": "eastnets.admin.AdminTest", "children": ["8183a506-cee8-4a65-8834-2597cf24b180", "cd3b4e5b-bed6-4507-b4bd-9595e5d501af", "12916da3-2ed2-4b2b-9d15-dae767573bf6", "e8ddcea3-bbdd-4161-aeb3-d09b2cd2b266", "5a06bd2e-240a-45c6-8f10-59de37bdc4f6", "8f000a96-bf9b-4350-bfa5-516aeced03b6", "7514f7d0-8d32-4c2b-ad80-b137e7fe4bef", "0b8f99d6-aad5-472f-b5ae-3c0dc4f16678", "a48e1b27-2ed1-4b25-aaad-55ac016e06dc", "*************-4730-ac9b-833478fdfbd2", "a514aedd-6510-431f-9aad-661c7f2d9359", "5d8d1ac7-8181-4f51-987b-afe3fd4a700b", "d7cb7b8a-25c9-480d-9ac3-408383e2fc3b", "b6d3be2b-defe-414e-ad27-56d1b767ef56", "6e43d0ea-5761-4e4e-ac97-90951560068f"], "befores": [{"name": "createNewOperator", "status": "passed", "stage": "finished", "description": "", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265027204, "stop": 1754265027204}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='sysadmin'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265027204, "stop": 1754265027204}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265027210, "stop": 1754265027210}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265027210, "stop": 1754265027210}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265027210, "stop": 1754265027210}, {"name": "Login with User Name = sysadmin and Password = manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265027329, "stop": 1754265027329}, {"name": "Operator test data = Operator{id=0, loginName='selenium-user-538712605', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone299881526', displayName='Zone (299881526)', description='Zone created with random number '299881526''}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265031453, "stop": 1754265031453}, {"name": "Group test data = Group{id=0, name='selenium-random-group-785085669', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-413870425', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone299881526', displayName='Zone (299881526)', description='Zone created with random number '299881526''}, profileRights={Administration={Archive Viewer=[], Operator Manager=[ALL], Group Manager=[ALL], Profile Manager=[ALL], Report Manager=[ALL], Zone Manager=[ALL], Audit Manager=[], Event Viewer=[]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-538712605', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone299881526', displayName='Zone (299881526)', description='Zone created with random number '299881526''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265031453, "stop": 1754265031453}, {"name": "Create new zone .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265031453, "stop": 1754265031453}, {"name": "Zone test Data = Zone{id=0, name='Zone299881526', displayName='Zone (299881526)', description='Zone created with random number '299881526''}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265031453, "stop": 1754265031453}, {"name": "Check if zone with name = 'Zone299881526' exists", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265033241, "stop": 1754265033241}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265033421, "stop": 1754265033421}, {"name": "Enter name =Zone299881526", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265038771, "stop": 1754265038771}, {"name": "Enter display Name =Zone (299881526)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265039591, "stop": 1754265039591}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265040561, "stop": 1754265040561}, {"name": "Zone doesn't exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265041739, "stop": 1754265041739}, {"name": "Click add new zone button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265041739, "stop": 1754265041739}, {"name": "Set create new zone data.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265041949, "stop": 1754265041949}, {"name": "Set name = Zone299881526", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265041949, "stop": 1754265041949}, {"name": "Set display name = Zone (299881526)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265043385, "stop": 1754265043385}, {"name": "Set description = Zone created with random number '299881526'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265043990, "stop": 1754265043990}, {"name": "Capture zone id from UI = 158", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265045351, "stop": 1754265045351}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265045351, "stop": 1754265045351}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265046929, "stop": 1754265046929}, {"name": "Enter name =Zone299881526", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265052188, "stop": 1754265052188}, {"name": "Enter display Name =Zone (299881526)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265052884, "stop": 1754265052884}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265053575, "stop": 1754265053575}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265056827, "stop": 1754265056827}, {"name": "Enter name =Zone299881526", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265062116, "stop": 1754265062116}, {"name": "Enter display Name =Zone (299881526)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265062900, "stop": 1754265062900}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265063795, "stop": 1754265063795}, {"name": "Create new profile .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265064786, "stop": 1754265064786}, {"name": "Profile test Data = Profile{id=0, name='Test-Profile-413870425', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone299881526', displayName='Zone (299881526)', description='Zone created with random number '299881526''}, profileRights={Administration={Archive Viewer=[], Operator Manager=[ALL], Group Manager=[ALL], Profile Manager=[ALL], Report Manager=[ALL], Zone Manager=[ALL], Audit Manager=[], Event Viewer=[]}}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265064786, "stop": 1754265064786}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265067043, "stop": 1754265067043}, {"name": "Set name = Test-Profile-413870425 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265072759, "stop": 1754265072759}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265073886, "stop": 1754265073886}, {"name": "Click add new profile button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265074867, "stop": 1754265074867}, {"name": "Set name = Test-Profile-413870425 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265075174, "stop": 1754265075174}, {"name": "Set description = Random profile for Selenium testing ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265077083, "stop": 1754265077083}, {"name": "Check enable checkbox to be true ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265077887, "stop": 1754265077887}, {"name": "Check write right checkbox to be Test-Profile-413870425 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265078754, "stop": 1754265078754}, {"name": "Select zone = Test-Profile-413870425 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265079208, "stop": 1754265079208}, {"name": "Add Administration item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265085201, "stop": 1754265085201}, {"name": "Click on the Administration item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265085201, "stop": 1754265085201}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265085682, "stop": 1754265085682}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265086736, "stop": 1754265086736}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265087053, "stop": 1754265087053}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265087159, "stop": 1754265087159}, {"name": "Set name = Test-Profile-413870425 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265094340, "stop": 1754265094340}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265094960, "stop": 1754265094960}, {"name": "Select profile from result view.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265095649, "stop": 1754265095649}, {"name": "Click on Administration", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265096165, "stop": 1754265096165}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265097548, "stop": 1754265097548}, {"name": "Add Archive Viewer item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265098957, "stop": 1754265098957}, {"name": "Click on the Archive Viewer item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265098957, "stop": 1754265098957}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265099360, "stop": 1754265099360}, {"name": "Add Operator Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265100744, "stop": 1754265100744}, {"name": "Click on the Operator Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265100744, "stop": 1754265100744}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265101111, "stop": 1754265101111}, {"name": "Add Group Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265102493, "stop": 1754265102493}, {"name": "Click on the Group Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265102493, "stop": 1754265102493}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265102818, "stop": 1754265102818}, {"name": "Add Profile Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265104307, "stop": 1754265104307}, {"name": "Click on the Profile Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265104307, "stop": 1754265104307}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265104781, "stop": 1754265104781}, {"name": "Add Report Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265106164, "stop": 1754265106164}, {"name": "Click on the Report Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265106164, "stop": 1754265106164}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265106533, "stop": 1754265106533}, {"name": "Add Zone Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265107992, "stop": 1754265107992}, {"name": "Click on the Zone Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265107992, "stop": 1754265107992}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265108351, "stop": 1754265108351}, {"name": "Add Audit Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265109790, "stop": 1754265109790}, {"name": "Click on the Audit Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265109790, "stop": 1754265109790}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265110194, "stop": 1754265110194}, {"name": "Add Event Viewer item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265111678, "stop": 1754265111678}, {"name": "Click on the Event Viewer item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265111678, "stop": 1754265111678}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265112036, "stop": 1754265112036}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265113465, "stop": 1754265113465}, {"name": "Accept <PERSON><PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265114006, "stop": 1754265114006}, {"name": "Click on Administration", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265115182, "stop": 1754265115182}, {"name": "Select Module Rights.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265116021, "stop": 1754265116021}, {"name": "Processing module 'Operator Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265118023, "stop": 1754265118023}, {"name": "Click on Operator Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265118023, "stop": 1754265118023}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265118886, "stop": 1754265118886}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265119216, "stop": 1754265119216}, {"name": "Processing module 'Group Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265121668, "stop": 1754265121668}, {"name": "<PERSON>lick on Group Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265121668, "stop": 1754265121668}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265122536, "stop": 1754265122536}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265122768, "stop": 1754265122768}, {"name": "Processing module 'Profile Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265125157, "stop": 1754265125157}, {"name": "Click on Profile Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265125157, "stop": 1754265125157}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265127228, "stop": 1754265127228}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265127426, "stop": 1754265127426}, {"name": "Processing module 'Report Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265129568, "stop": 1754265129568}, {"name": "Click on Report Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265129568, "stop": 1754265129568}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265182019, "stop": 1754265182019}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265182311, "stop": 1754265182311}, {"name": "Processing module 'Zone Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265185072, "stop": 1754265185072}, {"name": "Click on Zone Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265185072, "stop": 1754265185072}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265186234, "stop": 1754265186234}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265186572, "stop": 1754265186572}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265189998, "stop": 1754265189998}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265190304, "stop": 1754265190304}, {"name": "Check if profile exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265191953, "stop": 1754265191953}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265191953, "stop": 1754265191953}, {"name": "Set name = Test-Profile-413870425 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265198368, "stop": 1754265198368}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265199383, "stop": 1754265199383}, {"name": "Profile exist = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265200435, "stop": 1754265200435}, {"name": "Create new operator.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265200435, "stop": 1754265200435}, {"name": "Operator test Data = Operator{id=0, loginName='selenium-user-538712605', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone299881526', displayName='Zone (299881526)', description='Zone created with random number '299881526''}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265200435, "stop": 1754265200435}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265203401, "stop": 1754265203401}, {"name": "Set login name = selenium-user-538712605", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265208957, "stop": 1754265208957}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265210082, "stop": 1754265210082}, {"name": "Click Create new operator button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265211100, "stop": 1754265211100}, {"name": "Set login name = selenium-user-538712605", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265211424, "stop": 1754265211424}, {"name": "Set first name = Random", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265213565, "stop": 1754265213565}, {"name": "Set last name = User", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265214452, "stop": 1754265214452}, {"name": "Set password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265215545, "stop": 1754265215545}, {"name": "Set confirm password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265216685, "stop": 1754265216686}, {"name": "Set enable flag = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265217849, "stop": 1754265217849}, {"name": "Set reset flag = false", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265218867, "stop": 1754265218867}, {"name": "Select zone  = Zone (299881526)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265220731, "stop": 1754265220731}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265240550, "stop": 1754265240550}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265241174, "stop": 1754265241174}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265249796, "stop": 1754265249796}, {"name": "Set login name = selenium-user-538712605", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265255271, "stop": 1754265255271}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265256530, "stop": 1754265256530}, {"name": "Create new group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265259442, "stop": 1754265259442}, {"name": "Group test Data = Group{id=0, name='selenium-random-group-785085669', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-413870425', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone299881526', displayName='Zone (299881526)', description='Zone created with random number '299881526''}, profileRights={Administration={Archive Viewer=[], Operator Manager=[ALL], Group Manager=[ALL], Profile Manager=[ALL], Report Manager=[ALL], Zone Manager=[ALL], Audit Manager=[], Event Viewer=[]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-538712605', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone299881526', displayName='Zone (299881526)', description='Zone created with random number '299881526''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265259442, "stop": 1754265259442}], "attachments": [], "parameters": [], "start": 1754265026762, "stop": 1754265318943}], "afters": [], "start": 1754264785418, "stop": 1754268551187}