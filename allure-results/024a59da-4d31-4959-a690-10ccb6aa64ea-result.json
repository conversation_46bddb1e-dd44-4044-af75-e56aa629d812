{"uuid": "024a59da-4d31-4959-a690-10ccb6aa64ea", "historyId": "5c9cd828d88371bc9e6717e3c3eb6a2f", "fullName": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC004.listManager_TC004", "labels": [{"name": "package", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC004"}, {"name": "testClass", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC004"}, {"name": "testMethod", "value": "listManager_TC004"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "List Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC004"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-1(36)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "List Manager"}, {"name": "feature", "value": "Good Guy"}], "links": [], "name": "Verify that user is able to Validate a Syntax inside the list set when adding a Violation Filter to it", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to Validate a Syntax inside the list set when adding a Violation Filter to it", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141479850, "stop": 1754141479850}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-02'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141479851, "stop": 1754141479851}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141479857, "stop": 1754141479857}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141479858, "stop": 1754141479858}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141479858, "stop": 1754141479858}, {"name": "Login with User Name = operator-02 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141480055, "stop": 1754141480055}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141504420, "stop": 1754141504420}, {"name": "Validation message = Syntax is valid!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141543921, "stop": 1754141543921}, {"name": "Validation message = Syntax is valid!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141543922, "stop": 1754141543922}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141547099, "stop": 1754141547099}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141547099, "stop": 1754141547099}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141549352, "stop": 1754141549352}, {"name": "Status for the scanned name = CLEAN", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754141566285, "stop": 1754141566285}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754141479242, "stop": 1754141569070}