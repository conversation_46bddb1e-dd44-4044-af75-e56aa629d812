{"uuid": "7438d557-7340-4c07-a9ae-0edad7a30361", "name": "ISO20022 Configurations Test Cases", "children": ["934a7d9e-c53b-4838-815a-359e1e65786d", "bbe1a941-37e4-4cb6-8f66-3941c6237f4a", "4f959e7b-6237-4617-97fc-a2fcceeabea5", "7c4d0f19-87d3-45d7-b97e-ac02fdd1028b", "af290822-d29d-4f21-93e1-6837dffd7f30"], "befores": [{"name": "Setting up selenium web driver before each class run", "status": "passed", "stage": "finished", "description": "Setting up selenium web driver before each class run", "steps": [{"name": "Initialize Selenium driver before tests' Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754240865481, "stop": 1754240865482}, {"name": "Selenium webDriver was initialized successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754240874434, "stop": 1754240874434}], "attachments": [], "parameters": [], "start": 1754240865478, "stop": 1754240874434}], "afters": [{"name": "Quitting selenium driver after each class run", "status": "passed", "stage": "finished", "description": "Quitting selenium driver after each class run", "steps": [{"name": "Closing selenium WebDriver after Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241746212, "stop": 1754241746212}], "attachments": [], "parameters": [], "start": 1754241746211, "stop": 1754241746572}], "start": 1754240865466, "stop": 1754241746572}