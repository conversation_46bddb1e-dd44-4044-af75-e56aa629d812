{"uuid": "8beef31d-222b-417a-8f94-2d2acac7d377", "name": "eastnets.admin.AdminTest", "children": ["e21f5ef6-b260-4a53-91f8-d64621b274d4", "4c492666-4d05-49a6-9924-25366160800f"], "befores": [{"name": "createNewOperator", "status": "passed", "stage": "finished", "description": "", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186681377, "stop": 1754186681377}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='sysadmin'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186681377, "stop": 1754186681377}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186681387, "stop": 1754186681387}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186681387, "stop": 1754186681387}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186681388, "stop": 1754186681388}, {"name": "Login with User Name = sysadmin and Password = manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186681584, "stop": 1754186681584}, {"name": "Operator test data = Operator{id=0, loginName='selenium-user-557228992', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone130689484', displayName='Zone (130689484)', description='Zone created with random number '130689484''}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186688346, "stop": 1754186688346}, {"name": "Group test data = Group{id=0, name='selenium-random-group-355990639', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-649579713', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone130689484', displayName='Zone (130689484)', description='Zone created with random number '130689484''}, profileRights={Administration={Archive Viewer=[], Operator Manager=[ALL], Group Manager=[ALL], Profile Manager=[ALL], Report Manager=[ALL], Zone Manager=[ALL], Audit Manager=[], Event Viewer=[]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-557228992', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone130689484', displayName='Zone (130689484)', description='Zone created with random number '130689484''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186688346, "stop": 1754186688346}, {"name": "Create new zone .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186688346, "stop": 1754186688346}, {"name": "Zone test Data = Zone{id=0, name='Zone130689484', displayName='Zone (130689484)', description='Zone created with random number '130689484''}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186688346, "stop": 1754186688346}, {"name": "Check if zone with name = 'Zone130689484' exists", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186690041, "stop": 1754186690041}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186690349, "stop": 1754186690349}, {"name": "Enter name =Zone130689484", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186695760, "stop": 1754186695760}, {"name": "Enter display Name =Zone (130689484)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186696572, "stop": 1754186696572}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186697252, "stop": 1754186697252}, {"name": "Zone doesn't exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186697707, "stop": 1754186697707}, {"name": "Click add new zone button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186697707, "stop": 1754186697707}, {"name": "Set create new zone data.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186697948, "stop": 1754186697948}, {"name": "Set name = Zone130689484", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186697948, "stop": 1754186697948}, {"name": "Set display name = Zone (130689484)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186699124, "stop": 1754186699124}, {"name": "Set description = Zone created with random number '130689484'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186700001, "stop": 1754186700001}, {"name": "Capture zone id from UI = 148", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186701312, "stop": 1754186701312}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186701312, "stop": 1754186701312}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186702610, "stop": 1754186702610}, {"name": "Enter name =Zone130689484", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186707986, "stop": 1754186707986}, {"name": "Enter display Name =Zone (130689484)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186708586, "stop": 1754186708586}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186709498, "stop": 1754186709498}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186712335, "stop": 1754186712335}, {"name": "Enter name =Zone130689484", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186717754, "stop": 1754186717754}, {"name": "Enter display Name =Zone (130689484)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186718529, "stop": 1754186718529}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186719345, "stop": 1754186719345}, {"name": "Create new profile .", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186720019, "stop": 1754186720019}, {"name": "Profile test Data = Profile{id=0, name='Test-Profile-649579713', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone130689484', displayName='Zone (130689484)', description='Zone created with random number '130689484''}, profileRights={Administration={Archive Viewer=[], Operator Manager=[ALL], Group Manager=[ALL], Profile Manager=[ALL], Report Manager=[ALL], Zone Manager=[ALL], Audit Manager=[], Event Viewer=[]}}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186720019, "stop": 1754186720019}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186721648, "stop": 1754186721648}, {"name": "Set name = Test-Profile-649579713 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186727060, "stop": 1754186727060}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186727800, "stop": 1754186727800}, {"name": "Click add new profile button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186728517, "stop": 1754186728517}, {"name": "Set name = Test-Profile-649579713 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186728875, "stop": 1754186728875}, {"name": "Set description = Random profile for Selenium testing ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186730371, "stop": 1754186730371}, {"name": "Check enable checkbox to be true ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186731204, "stop": 1754186731204}, {"name": "Check write right checkbox to be Test-Profile-649579713 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186732133, "stop": 1754186732133}, {"name": "Select zone = Test-Profile-649579713 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186732505, "stop": 1754186732505}, {"name": "Add Administration item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186739413, "stop": 1754186739413}, {"name": "Click on the Administration item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186739413, "stop": 1754186739413}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186740012, "stop": 1754186740012}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186741021, "stop": 1754186741021}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186741386, "stop": 1754186741386}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186741453, "stop": 1754186741453}, {"name": "Set name = Test-Profile-649579713 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186747621, "stop": 1754186747621}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186748340, "stop": 1754186748340}, {"name": "Select profile from result view.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186749001, "stop": 1754186749001}, {"name": "Click on Administration", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186749649, "stop": 1754186749649}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186751009, "stop": 1754186751009}, {"name": "Add Archive Viewer item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186752153, "stop": 1754186752153}, {"name": "Click on the Archive Viewer item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186752153, "stop": 1754186752153}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186752672, "stop": 1754186752672}, {"name": "Add Operator Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186754101, "stop": 1754186754101}, {"name": "Click on the Operator Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186754101, "stop": 1754186754101}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186754711, "stop": 1754186754711}, {"name": "Add Group Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186756134, "stop": 1754186756134}, {"name": "Click on the Group Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186756134, "stop": 1754186756134}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186756776, "stop": 1754186756776}, {"name": "Add Profile Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186758184, "stop": 1754186758184}, {"name": "Click on the Profile Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186758184, "stop": 1754186758184}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186758633, "stop": 1754186758633}, {"name": "Add Report Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186760054, "stop": 1754186760054}, {"name": "Click on the Report Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186760054, "stop": 1754186760054}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186760594, "stop": 1754186760594}, {"name": "Add Zone Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186762067, "stop": 1754186762067}, {"name": "Click on the Zone Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186762067, "stop": 1754186762067}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186762652, "stop": 1754186762652}, {"name": "Add Audit Manager item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186764138, "stop": 1754186764138}, {"name": "Click on the Audit Manager item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186764138, "stop": 1754186764138}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186764633, "stop": 1754186764633}, {"name": "Add Event Viewer item to selected list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186766068, "stop": 1754186766068}, {"name": "Click on the Event Viewer item", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186766068, "stop": 1754186766068}, {"name": "Click on Add button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186766566, "stop": 1754186766566}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186767997, "stop": 1754186767997}, {"name": "Accept <PERSON><PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186768552, "stop": 1754186768552}, {"name": "Click on Administration", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186769607, "stop": 1754186769607}, {"name": "Select Module Rights.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186770334, "stop": 1754186770334}, {"name": "Processing module 'Operator Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186772334, "stop": 1754186772334}, {"name": "Click on Operator Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186772334, "stop": 1754186772334}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186773278, "stop": 1754186773278}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186773577, "stop": 1754186773577}, {"name": "Processing module 'Group Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186776010, "stop": 1754186776010}, {"name": "<PERSON>lick on Group Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186776010, "stop": 1754186776010}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186776996, "stop": 1754186776996}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186777254, "stop": 1754186777254}, {"name": "Processing module 'Profile Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186779724, "stop": 1754186779724}, {"name": "Click on Profile Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186779724, "stop": 1754186779724}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186780637, "stop": 1754186780637}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186780889, "stop": 1754186780889}, {"name": "Processing module 'Report Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186783116, "stop": 1754186783116}, {"name": "Click on Report Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186783116, "stop": 1754186783116}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186783969, "stop": 1754186783969}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186784305, "stop": 1754186784305}, {"name": "Processing module 'Zone Manager'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186786789, "stop": 1754186786789}, {"name": "Click on Zone Manager", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186786789, "stop": 1754186786789}, {"name": "Remove all selected options if exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186787719, "stop": 1754186787719}, {"name": "Selecting all function for module", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186787948, "stop": 1754186787948}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186791408, "stop": 1754186791408}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186791669, "stop": 1754186791669}, {"name": "Check if profile exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186792623, "stop": 1754186792623}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186792623, "stop": 1754186792623}, {"name": "Set name = Test-Profile-649579713 ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186798446, "stop": 1754186798446}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186799340, "stop": 1754186799340}, {"name": "Profile exist = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186800280, "stop": 1754186800280}, {"name": "Create new operator.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186800280, "stop": 1754186800280}, {"name": "Operator test Data = Operator{id=0, loginName='selenium-user-557228992', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone130689484', displayName='Zone (130689484)', description='Zone created with random number '130689484''}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186800280, "stop": 1754186800280}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186802332, "stop": 1754186802332}, {"name": "Set login name = selenium-user-557228992", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186807661, "stop": 1754186807661}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186808338, "stop": 1754186808338}, {"name": "Click Create new operator button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186808952, "stop": 1754186808952}, {"name": "Set login name = selenium-user-557228992", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186809296, "stop": 1754186809296}, {"name": "Set first name = Random", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186810893, "stop": 1754186810893}, {"name": "Set last name = User", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186811869, "stop": 1754186811869}, {"name": "Set password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186812724, "stop": 1754186812724}, {"name": "Set confirm password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186813415, "stop": 1754186813415}, {"name": "Set enable flag = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186814146, "stop": 1754186814146}, {"name": "Set reset flag = false", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186814969, "stop": 1754186814969}, {"name": "Select zone  = Zone (130689484)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186816627, "stop": 1754186816627}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186824940, "stop": 1754186824940}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186825384, "stop": 1754186825384}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186832187, "stop": 1754186832187}, {"name": "Set login name = selenium-user-557228992", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186837574, "stop": 1754186837574}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186838322, "stop": 1754186838322}, {"name": "Create new group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186838994, "stop": 1754186838994}, {"name": "Group test Data = Group{id=0, name='selenium-random-group-355990639', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='Test-Profile-649579713', enabled=true, writeRight=true, description='Random profile for Selenium testing', zone=Zone{id=0, name='Zone130689484', displayName='Zone (130689484)', description='Zone created with random number '130689484''}, profileRights={Administration={Archive Viewer=[], Operator Manager=[ALL], Group Manager=[ALL], Profile Manager=[ALL], Report Manager=[ALL], Zone Manager=[ALL], Audit Manager=[], Event Viewer=[]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-557228992', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Zone130689484', displayName='Zone (130689484)', description='Zone created with random number '130689484''}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754186838995, "stop": 1754186838995}], "attachments": [], "parameters": [], "start": 1754186680937, "stop": 1754186878913}], "afters": [], "start": 1754186132809, "stop": 1754193744999}