{"uuid": "12916da3-2ed2-4b2b-9d15-dae767573bf6", "historyId": "4d979c84bd4ce7b37b23b4bbd0bd2896", "fullName": "eastnets.admin.AdminTest.checkProfilePermissions", "labels": [{"name": "package", "value": "eastnets.admin.AdminTest"}, {"name": "testClass", "value": "eastnets.admin.AdminTest"}, {"name": "testMethod", "value": "checkProfilePermissions"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Admin Test Cases"}, {"name": "subSuite", "value": "eastnets.admin.AdminTest"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}], "links": [], "name": "Verify that user is not able to Delete a Profile when 'Delete Profile' permission is not granted", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is not able to Delete a Profile when 'Delete Profile' permission is not granted", "steps": [{"name": "Connect to database to remove Link between profile = Test-Profile-413870425 and Permission Allow to delete profile", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265350632, "stop": 1754265350632}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265350666, "stop": 1754265350666}, {"name": "SELECT P.NAME as PR<PERSON><PERSON><PERSON>_NAME , PR.PROFILE_ID as PROFILE_ID , PR.GRANT_ID as GRANT_ID , PR.GRANT_RIGHT_ID as GRANT_RIGHT_ID , PR.GRANT_TYPE_ID as GRANT_TYPE_ID   FROM tModuleFunctions M   LEFT JOIN tProfileRights PR On PR.GRANT_ID  = M.ID     LEFT Join tProfiles P ON P.id =PR.PROFILE_ID   WHERE DESCRIPTION ='Allow to delete profile' and P.NAME = 'Test-Profile-413870425'  and PR.GRANT_TYPE_ID =3", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265350666, "stop": 1754265350666}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265350677, "stop": 1754265350677}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265350677, "stop": 1754265350677}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265350677, "stop": 1754265350677}, {"name": "Delete function permission 'Allow to delete profile' from DataBase", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265350677, "stop": 1754265350677}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265350699, "stop": 1754265350699}, {"name": "DELETE FROM tProfileRights  WHERE GRANT_ID  ='12' and PROFILE_ID ='184' and GRANT_TYPE_ID =3", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265350700, "stop": 1754265350700}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265350714, "stop": 1754265350714}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265350715, "stop": 1754265350715}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265350715, "stop": 1754265350715}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265351477, "stop": 1754265351477}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='selenium-user-538712605'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265351477, "stop": 1754265351477}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265351484, "stop": 1754265351484}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265351484, "stop": 1754265351484}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265351484, "stop": 1754265351484}, {"name": "Login with User Name = selenium-user-538712605 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265351691, "stop": 1754265351691}, {"name": "Check if user can delete profile.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265359723, "stop": 1754265359723}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265363433, "stop": 1754265363433}, {"name": " INSERT INTO tProfileRights (PROFILE_ID, GRANT_ID, GRANT_RIGHT_ID, GRANT_TYPE_ID) VALUES (184, 12, 3, 3);", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265363433, "stop": 1754265363433}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265363439, "stop": 1754265363439}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265363439, "stop": 1754265363439}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265363439, "stop": 1754265363439}, {"name": "Operator logout.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754265363439, "stop": 1754265363439}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "Profile{id=0, name='selenium-full-right-profile ', enabled=false, writeRight=false, description='null', zone=null, profileRights=null}"}], "start": 1754265350632, "stop": 1754265363440}