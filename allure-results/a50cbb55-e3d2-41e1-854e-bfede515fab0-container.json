{"uuid": "a50cbb55-e3d2-41e1-854e-bfede515fab0", "name": "List Manager Test Cases", "children": ["bba89440-3416-412d-9fae-76efef50a314", "9597908d-d922-4e2f-91f8-bc9f25057d24", "8afb461a-7d50-4005-a00f-004c789608c5", "2f9986ed-6b49-4fdf-8019-47629b2a2a6d", "efc6ac11-f122-4a75-b522-7fcb1d3ddbc2", "6e098950-7b41-448c-a8d1-c9dd309ce013", "3614d6bb-032e-45b8-81f7-224939a83d23", "c675a712-2dcf-44b1-b556-540c7fef29c2", "b9ca24ec-93c0-40d9-a85e-e0fb6915ae49", "f87228ce-322f-4dd7-b93d-6277043fd58d", "0cb1c980-a7ee-47cd-9d28-6f1aa0c95e5c", "9c51af7f-6cc8-43fb-bca1-3bed2288de6f", "ab4287ac-fe45-4b0e-89d1-edc0e06558c5", "d1b8e7e9-bbf4-4761-85bb-28fac71116e0", "05b62616-cc5e-4fff-a42e-0e93a2869943", "779d1e31-d8e3-478b-80b0-85f9fbb28fc7", "9f85fc73-80a5-4557-85c8-634e56eaf098", "a559fc83-51a6-4618-b002-29fdb3743e97", "f35a4e86-ce20-45ce-ae90-4e5adb267bb8", "82cbe70d-9738-471c-be13-dbf330c7c6f9", "18223a3e-0310-4bff-b77d-1cbc8ab91500", "4ea2f79b-2721-4f1b-ba37-79309dcd1a0a", "6b53595b-ab2d-4e1b-b258-db613ef8456f", "6770803e-5a0d-4cb6-9336-2a9cf25b680e", "ff359395-9434-4896-8cec-18457e9a3d0e", "f2661bb1-e53e-4f02-ad70-24c4b796450b", "ba0ea863-f197-4c2b-87fd-04a3ef921fad", "5b8066a5-ff14-4aad-8de6-8a7471410f3c", "4fe1e6b9-3f43-4ff3-a0a4-0753844b8f75", "bc3f62da-238e-4593-8e80-655cc43da3a2", "e2e73237-fea2-41f5-9631-e78886a733eb", "e26e3170-312d-4c69-b0c8-cad7e7c602fe", "95359fce-594c-4c5a-aab7-a7e1b6ee2d71", "3f4f1acb-04c4-4cac-b96e-6505a261f406"], "befores": [{"name": "Setting up selenium web driver before each class run", "status": "failed", "statusDetails": {"known": false, "muted": false, "flaky": false, "message": "Error occurred while initializing selenium web driver", "trace": "java.lang.AssertionError: Error occurred while initializing selenium web driver\r\n\tat org.testng.Assert.fail(Assert.java:110)\r\n\tat core.BaseTest.aloadConfiguration(BaseTest.java:155)\r\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:578)\r\n\tat org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)\r\n\tat org.testng.internal.invokers.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:69)\r\n\tat org.testng.internal.invokers.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:361)\r\n\tat org.testng.internal.invokers.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:296)\r\n\tat org.testng.TestRunner.invokeTestConfigurations(TestRunner.java:644)\r\n\tat org.testng.TestRunner.beforeRun(TestRunner.java:633)\r\n\tat org.testng.TestRunner.run(TestRunner.java:595)\r\n\tat org.testng.SuiteRunner.runTest(SuiteRunner.java:437)\r\n\tat org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:475)\r\n\tat org.testng.internal.thread.ThreadUtil.lambda$execute$0(ThreadUtil.java:58)\r\n\tat java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)\r\n\tat java.base/java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:132)\r\n\tat java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:113)\r\n\tat java.base/java.util.concurrent.AbstractExecutorService.invokeAll(AbstractExecutorService.java:274)\r\n\tat org.testng.internal.thread.ThreadUtil.execute(ThreadUtil.java:64)\r\n\tat org.testng.SuiteRunner.runInParallelTestMode(SuiteRunner.java:458)\r\n\tat org.testng.SuiteRunner.privateRun(SuiteRunner.java:389)\r\n\tat org.testng.SuiteRunner.run(SuiteRunner.java:330)\r\n\tat org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)\r\n\tat org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)\r\n\tat org.testng.TestNG.runSuitesSequentially(TestNG.java:1256)\r\n\tat org.testng.TestNG.runSuitesLocally(TestNG.java:1176)\r\n\tat org.testng.TestNG.runSuites(TestNG.java:1099)\r\n\tat org.testng.TestNG.run(TestNG.java:1067)\r\n\tat com.intellij.rt.testng.IDEARemoteTestNG.run(IDEARemoteTestNG.java:65)\r\n\tat com.intellij.rt.testng.RemoteTestNGStarter.main(RemoteTestNGStarter.java:105)\r\n"}, "stage": "finished", "description": "Setting up selenium web driver before each class run", "steps": [{"name": "Initialize Selenium driver before tests' Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754233053051, "stop": 1754233053051}, {"name": "Error occurred while initializing selenium web driver ----> Could not start a new session. Possible causes are invalid address of the remote server or browser start-up failure. \nHost info: host: 'ENAMAUTO001', ip: '************'\nBuild info: version: '4.13.0', revision: 'ba948ece5b*'\nSystem info: os.name: 'Windows Server 2022', os.arch: 'amd64', os.version: '10.0', java.version: '19.0.2'\nDriver info: org.openqa.selenium.remote.RemoteWebDriver\nCommand: [null, newSession {capabilities=[Capabilities {browserName: MicrosoftEdge, ms:edgeOptions: {args: [--remote-allow-origins=*, force-device-scale-factor=0.9, high-dpi-support=0.9], extensions: [], prefs: {nativeEvents: false}}, se:downloadsEnabled: true}]}]\nCapabilities {browserName: MicrosoftEdge, ms:edgeOptions: {args: [--remote-allow-origins=*, force-device-scale-factor=0.9, high-dpi-support=0.9], extensions: [], prefs: {nativeEvents: false}}, se:downloadsEnabled: true}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754233056145, "stop": 1754233056145}], "attachments": [], "parameters": [], "start": 1754233053012, "stop": 1754233056171}], "afters": [{"name": "Quitting selenium driver after each class run", "status": "broken", "statusDetails": {"known": false, "muted": false, "flaky": false, "message": "Cannot invoke \"org.openqa.selenium.remote.RemoteWebDriver.quit()\" because the return value of \"core.BaseTest.getDriver()\" is null", "trace": "java.lang.NullPointerException: Cannot invoke \"org.openqa.selenium.remote.RemoteWebDriver.quit()\" because the return value of \"core.BaseTest.getDriver()\" is null\r\n\tat core.BaseTest.closeDriver(BaseTest.java:218)\r\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:578)\r\n\tat org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)\r\n\tat org.testng.internal.invokers.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:69)\r\n\tat org.testng.internal.invokers.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:361)\r\n\tat org.testng.internal.invokers.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:296)\r\n\tat org.testng.TestRunner.invokeTestConfigurations(TestRunner.java:644)\r\n\tat org.testng.TestRunner.afterRun(TestRunner.java:914)\r\n\tat org.testng.TestRunner.run(TestRunner.java:605)\r\n\tat org.testng.SuiteRunner.runTest(SuiteRunner.java:437)\r\n\tat org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:475)\r\n\tat org.testng.internal.thread.ThreadUtil.lambda$execute$0(ThreadUtil.java:58)\r\n\tat java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)\r\n\tat java.base/java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:132)\r\n\tat java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:113)\r\n\tat java.base/java.util.concurrent.AbstractExecutorService.invokeAll(AbstractExecutorService.java:274)\r\n\tat org.testng.internal.thread.ThreadUtil.execute(ThreadUtil.java:64)\r\n\tat org.testng.SuiteRunner.runInParallelTestMode(SuiteRunner.java:458)\r\n\tat org.testng.SuiteRunner.privateRun(SuiteRunner.java:389)\r\n\tat org.testng.SuiteRunner.run(SuiteRunner.java:330)\r\n\tat org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)\r\n\tat org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)\r\n\tat org.testng.TestNG.runSuitesSequentially(TestNG.java:1256)\r\n\tat org.testng.TestNG.runSuitesLocally(TestNG.java:1176)\r\n\tat org.testng.TestNG.runSuites(TestNG.java:1099)\r\n\tat org.testng.TestNG.run(TestNG.java:1067)\r\n\tat com.intellij.rt.testng.IDEARemoteTestNG.run(IDEARemoteTestNG.java:65)\r\n\tat com.intellij.rt.testng.RemoteTestNGStarter.main(RemoteTestNGStarter.java:105)\r\n"}, "stage": "finished", "description": "Quitting selenium driver after each class run", "steps": [{"name": "Closing selenium WebDriver after Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754233059605, "stop": 1754233059605}], "attachments": [], "parameters": [], "start": 1754233059601, "stop": 1754233059605}], "start": 1754233052994, "stop": 1754233059649}