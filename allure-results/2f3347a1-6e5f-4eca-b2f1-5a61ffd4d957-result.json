{"uuid": "2f3347a1-6e5f-4eca-b2f1-5a61ffd4d957", "historyId": "********************************", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC001.scanManager_TC001", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC001"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC001"}, {"name": "testMethod", "value": "scanManager_TC001"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC001"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is able to scan a custom format file with creating alerts option checked and take decision. ", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to scan a custom format file with creating alerts option checked and take decision. ", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241526223, "stop": 1754241526223}, {"name": "Validation message = File sent to the server for processing with id [3121]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241542707, "stop": 1754241542707}, {"name": "Alert Message = File sent to the server for processing with id [3121]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241542707, "stop": 1754241542707}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241544123, "stop": 1754241544123}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241553601, "stop": 1754241553601}, {"name": "Detection ID = 8057", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241555395, "stop": 1754241555395}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241562056, "stop": 1754241562056}, {"name": "Detection Status = REPNEW", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754241569315, "stop": 1754241569315}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "FileScan{filePath='/Generic.txt', format='Custom Format File', encoding='null', enList=null, result='null', rank='null', detectVessels='true', detectCountries='true', createAlertsAutomatically='true'}"}], "start": 1754241480204, "stop": 1754241576835}