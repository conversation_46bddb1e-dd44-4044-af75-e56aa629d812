{"uuid": "ebfef15a-30e7-4e2c-9fe3-f6b00b5b3773", "historyId": "4c582038480dc78a97ad032de285a0ca", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC009.scanManager_TC009", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC009"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC009"}, {"name": "testMethod", "value": "scanManager_TC009"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC009"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is able to Print an alert report in Excel format", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to Print an alert report in Excel format", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264390281, "stop": 1754264390281}, {"name": "Validation message = File sent to the server for processing with id [3163]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264403858, "stop": 1754264403858}, {"name": "Alert Message = File sent to the server for processing with id [3163]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264403858, "stop": 1754264403858}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264404347, "stop": 1754264404347}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264413412, "stop": 1754264413412}, {"name": "Detection ID = 8299", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264414744, "stop": 1754264414744}, {"name": "Start Exporting Violation With Print Scope all And Document Type Excel", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264414767, "stop": 1754264414767}, {"name": "Click on close dialog button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264424581, "stop": 1754264424581}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "eastnets.screening.entity.Report@7aeadbc6"}], "start": 1754264389957, "stop": 1754264455726}