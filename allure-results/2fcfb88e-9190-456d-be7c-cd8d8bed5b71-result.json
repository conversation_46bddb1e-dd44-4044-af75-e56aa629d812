{"uuid": "2fcfb88e-9190-456d-be7c-cd8d8bed5b71", "historyId": "1177ee5dfc1b30c4119b8bc6014ce962", "fullName": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC017.listManager_TC017", "labels": [{"name": "package", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC017"}, {"name": "testClass", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC017"}, {"name": "testMethod", "value": "listManager_TC017"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "List Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC017"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-1(36)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "List Manager"}], "links": [], "name": "Verify that user is able to Share a Public Black List", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to Share a Public Black List", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236606286, "stop": 1754236606286}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-02'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236606286, "stop": 1754236606286}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236606292, "stop": 1754236606292}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236606292, "stop": 1754236606292}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236606292, "stop": 1754236606292}, {"name": "Login with User Name = operator-02 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236606458, "stop": 1754236606458}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236610919, "stop": 1754236610919}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236610941, "stop": 1754236610941}, {"name": "DELETE FROM tBlackLists WHERE display_name ='UN Democratic Republic of Congo'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236610941, "stop": 1754236610941}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236611009, "stop": 1754236611009}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236611009, "stop": 1754236611009}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236611010, "stop": 1754236611010}, {"name": "Import 'UN Democratic Republic of Congo' Black List", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236611010, "stop": 1754236611010}, {"name": "Share 'UN Democratic Republic of Congo' black list", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236622788, "stop": 1754236622788}, {"name": "Validation Message = null", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754236628168, "stop": 1754236628168}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754236605930, "stop": 1754236629923}