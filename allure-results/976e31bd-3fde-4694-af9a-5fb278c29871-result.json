{"uuid": "976e31bd-3fde-4694-af9a-5fb278c29871", "historyId": "ed4cd7484d0ba2df12fa549b50972560", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC009.scanManager_TC009", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC009"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC009"}, {"name": "testMethod", "value": "scanManager_TC009"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC009"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is able to Print an alert report in Excel format", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to Print an alert report in Excel format", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235643098, "stop": 1754235643098}, {"name": "Validation message = File sent to the server for processing with id [3094]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235660572, "stop": 1754235660572}, {"name": "Alert Message = File sent to the server for processing with id [3094]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235660572, "stop": 1754235660572}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235661869, "stop": 1754235661869}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235672024, "stop": 1754235672024}, {"name": "Detection ID = 7985", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235673955, "stop": 1754235673955}, {"name": "Start Exporting Violation With Print Scope all And Document Type Excel", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235674059, "stop": 1754235674059}, {"name": "Click on close dialog button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235686128, "stop": 1754235686128}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "eastnets.screening.entity.Report@2046ff7d"}], "start": 1754235642114, "stop": 1754235717479}