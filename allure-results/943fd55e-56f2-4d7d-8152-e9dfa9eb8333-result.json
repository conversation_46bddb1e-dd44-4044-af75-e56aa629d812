{"uuid": "943fd55e-56f2-4d7d-8152-e9dfa9eb8333", "historyId": "2c35b1a229eec5d7776686e759bb8d97", "fullName": "eastnets.screening.regression.iso20022configurations.ISO20022_TC003.iso20022_TC003", "labels": [{"name": "package", "value": "eastnets.screening.regression.iso20022configurations.ISO20022_TC003"}, {"name": "testClass", "value": "eastnets.screening.regression.iso20022configurations.ISO20022_TC003"}, {"name": "testMethod", "value": "iso20022_TC003"}, {"name": "parentSuite", "value": "Failed Tests Rerun Suite"}, {"name": "suite", "value": "<PERSON><PERSON> Failed Tests"}, {"name": "subSuite", "value": "eastnets.screening.regression.iso20022configurations.ISO20022_TC003"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "tag", "value": "ISO20022"}, {"name": "tag", "value": "Regression"}, {"name": "owner", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "links": [], "name": "Verify that the user can export original/official ISO20022 XSD", "status": "failed", "statusDetails": {"known": false, "muted": false, "flaky": false, "message": "Test Failed due to an exception occurred in POM's method while adding ISO message expected [New message [pacs.009.001] with version [pacs.009.001.08] added successfully.] but found [This message already exist!\"]", "trace": "java.lang.AssertionError: Test Failed due to an exception occurred in POM's method while adding ISO message expected [New message [pacs.009.001] with version [pacs.009.001.08] added successfully.] but found [This message already exist!\"]\r\n\tat org.testng.Assert.fail(Assert.java:110)\r\n\tat org.testng.Assert.failNotEquals(Assert.java:1413)\r\n\tat org.testng.Assert.assertEqualsImpl(Assert.java:149)\r\n\tat org.testng.Assert.assertEquals(Assert.java:131)\r\n\tat org.testng.Assert.assertEquals(Assert.java:655)\r\n\tat core.ISOTestMethods.addISOMessage(ISOTestMethods.java:83)\r\n\tat eastnets.screening.regression.iso20022configurations.ISO20022_TC003.iso20022_TC003(ISO20022_TC003.java:109)\r\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:578)\r\n\tat org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:677)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:221)\r\n\tat org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:50)\r\n\tat org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:969)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:194)\r\n\tat org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148)\r\n\tat org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128)\r\n\tat java.base/java.util.ArrayList.forEach(ArrayList.java:1511)\r\n\tat org.testng.TestRunner.privateRun(TestRunner.java:829)\r\n\tat org.testng.TestRunner.run(TestRunner.java:602)\r\n\tat org.testng.SuiteRunner.runTest(SuiteRunner.java:437)\r\n\tat org.testng.SuiteRunner.runSequentially(SuiteRunner.java:431)\r\n\tat org.testng.SuiteRunner.privateRun(SuiteRunner.java:391)\r\n\tat org.testng.SuiteRunner.run(SuiteRunner.java:330)\r\n\tat org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)\r\n\tat org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)\r\n\tat org.testng.TestNG.runSuitesSequentially(TestNG.java:1256)\r\n\tat org.testng.TestNG.runSuitesLocally(TestNG.java:1176)\r\n\tat org.testng.TestNG.runSuites(TestNG.java:1099)\r\n\tat org.testng.TestNG.run(TestNG.java:1067)\r\n\tat core.BaseTest.rerunFailedTestCases(BaseTest.java:340)\r\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:578)\r\n\tat org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)\r\n\tat org.testng.internal.invokers.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:69)\r\n\tat org.testng.internal.invokers.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:361)\r\n\tat org.testng.internal.invokers.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:296)\r\n\tat org.testng.SuiteRunner.privateRun(SuiteRunner.java:404)\r\n\tat org.testng.SuiteRunner.run(SuiteRunner.java:330)\r\n\tat org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)\r\n\tat org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)\r\n\tat org.testng.TestNG.runSuitesSequentially(TestNG.java:1256)\r\n\tat org.testng.TestNG.runSuitesLocally(TestNG.java:1176)\r\n\tat org.testng.TestNG.runSuites(TestNG.java:1099)\r\n\tat org.testng.TestNG.run(TestNG.java:1067)\r\n\tat com.intellij.rt.testng.IDEARemoteTestNG.run(IDEARemoteTestNG.java:65)\r\n\tat com.intellij.rt.testng.RemoteTestNGStarter.main(RemoteTestNGStarter.java:105)\r\n"}, "stage": "finished", "description": "Verify that the user can export original/official ISO20022 XSD", "steps": [{"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-265308086', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@5c2ae7d7, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-265308086', officialDate='null', entry=[ListEntry{type='null', name='EntryName-265308086', firstName='EntryFirstName-265308086', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-265308086', firstName='EntryFirstName-265308086', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238654472, "stop": 1754238654472}, {"name": "Navigate to By.xpath: //a[contains(text(),'ISO20022 Format Configuration')]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238655928, "stop": 1754238655928}, {"name": "Navigate to By.xpath: //a[contains(text(),'ISO20022 Format Configuration')]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238658350, "stop": 1754238658350}, {"name": "Create New Group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238662431, "stop": 1754238662431}, {"name": "Connect to Database and Check if User Profile = full-right-profile_07 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238669378, "stop": 1754238669378}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238669378, "stop": 1754238669378}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238669429, "stop": 1754238669429}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_07' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238669429, "stop": 1754238669429}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238669432, "stop": 1754238669432}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238669432, "stop": 1754238669432}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238669432, "stop": 1754238669432}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238669456, "stop": 1754238669456}, {"name": "Delete From tListSetProfile where profile_id in (10)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238669456, "stop": 1754238669456}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238669461, "stop": 1754238669461}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238669461, "stop": 1754238669461}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238669461, "stop": 1754238669461}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238669461, "stop": 1754238669461}, {"name": "Search for list by listName = ListName-265308086 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238687896, "stop": 1754238687896}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238712200, "stop": 1754238712200}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238712200, "stop": 1754238712200}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238770620, "stop": 1754238770620}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238770620, "stop": 1754238770620}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238770620, "stop": 1754238770620}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238770647, "stop": 1754238770647}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_07'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238770648, "stop": 1754238770648}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238770649, "stop": 1754238770649}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 17***********, "stop": 17***********}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 17***********, "stop": 17***********}, {"name": "Navigate to By.xpath: //a[contains(text(),'ISO20022 Schema Configuration')]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238772778, "stop": 1754238772778}, {"name": "Navigate to By.xpath: //a[@href='#iso20022:homepage_business:tabViewListManager:tabView:schemaConfiguration']", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238773531, "stop": 1754238773531}, {"name": "Import schema.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238774084, "stop": 1754238774084}, {"name": "Navigate to By.xpath: //a[contains(text(),'ISO20022 Schema Configuration')]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238775410, "stop": 1754238775410}, {"name": "Navigate to By.xpath: //a[@href='#iso20022:homepage_business:tabViewListManager:tabView:schemaConfiguration']", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238775961, "stop": 1754238775961}, {"name": "Check if schema with Name = pacs.009.001", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238776557, "stop": 1754238776557}, {"name": "Delete schema with name = pacs.009.001", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238776796, "stop": 1754238776796}, {"name": "Validation message = Schema version [pacs.009.001.08] successfully deleted.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238778795, "stop": 1754238778795}, {"name": "Schema File Path = C:\\Users\\<USER>\\IdeaProjects\\SWS_selenium/src/test/resources/uploadsAndDownloads/uploads/ISO/pacs.009.001.08.xsd", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238778796, "stop": 1754238778796}, {"name": "Validation message = Successfully imported schema [pacs.009.001] with version [08].", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238781902, "stop": 1754238781902}, {"name": "Navigate to By.xpath: //a[contains(text(),'ISO20022 Format Configuration')]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238783507, "stop": 1754238783507}, {"name": "Add new message.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238784360, "stop": 1754238784360}, {"name": "Navigate to By.xpath: //a[contains(text(),'ISO20022 Format Configuration')]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238785741, "stop": 1754238785741}, {"name": "Sort table results by creation to click on the latest created group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238786518, "stop": 1754238786518}, {"name": "Validation message = This message already exist!\"", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238809780, "stop": 1754238809780}, {"name": "Actual result = This message already exist!\"", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754238809780, "stop": 1754238809780}], "attachments": [], "parameters": [{"name": "arg0", "value": "ISO20022FormatConfiguration{zone='Zone (867793082)', groupName='Group_Name-785651662', iso20022SchemaConfiguration=ISO20022SchemaConfiguration{schemaName='pacs.009.001', schemaVersion='08', headerSwift='head.001.001.01', expectedResults='Schema version [%s] successfully deleted'}, expectedResults='Successfully imported schema [pacs.009.001] with version [08].'}"}], "start": 1754238654469, "stop": 1754238809781}