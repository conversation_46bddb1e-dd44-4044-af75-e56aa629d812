{"uuid": "f0ad9b5c-d211-42c6-88a2-74dfc6d05fdb", "historyId": "9f77c8a9f5de2b7b7ab557de17e4eee", "fullName": "eastnets.screening.regression.listmanager.prefiltertests.PreFilter_TC001.preFilter_TC001", "labels": [{"name": "package", "value": "eastnets.screening.regression.listmanager.prefiltertests.PreFilter_TC001"}, {"name": "testClass", "value": "eastnets.screening.regression.listmanager.prefiltertests.PreFilter_TC001"}, {"name": "testMethod", "value": "preFilter_TC001"}, {"name": "parentSuite", "value": "Failed Tests Rerun Suite"}, {"name": "suite", "value": "<PERSON><PERSON> Failed Tests"}, {"name": "subSuite", "value": "eastnets.screening.regression.listmanager.prefiltertests.PreFilter_TC001"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>(1)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "List Manager"}, {"name": "feature", "value": "PreFilter"}], "links": [], "name": "Verify that the validation message should be displayed when validating the mentioned prefilter", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that the validation message should be displayed when validating the mentioned prefilter", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193286862, "stop": 1754193286862}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-02'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193286862, "stop": 1754193286862}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193286870, "stop": 1754193286870}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193286870, "stop": 1754193286870}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193286871, "stop": 1754193286871}, {"name": "Login with User Name = operator-02 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193287082, "stop": 1754193287082}, {"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-735996610', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@72b375a0, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-735996610', officialDate='null', entry=[ListEntry{type='null', name='EntryName-735996610', firstName='EntryFirstName-735996610', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-735996610', firstName='EntryFirstName-735996610', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193292648, "stop": 1754193292648}, {"name": "Connect to Database and Check if User Profile = full-right-profile_002 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193292649, "stop": 1754193292649}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193292649, "stop": 1754193292649}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193292679, "stop": 1754193292679}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_002' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193292680, "stop": 1754193292680}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193292682, "stop": 1754193292682}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193292682, "stop": 1754193292682}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193292683, "stop": 1754193292683}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193292697, "stop": 1754193292697}, {"name": "Delete From tListSetProfile where profile_id in (5)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193292697, "stop": 1754193292697}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193292701, "stop": 1754193292701}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193292701, "stop": 1754193292701}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193292701, "stop": 1754193292701}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193292701, "stop": 1754193292701}, {"name": "Search for list by listName = ListName-735996610 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193314009, "stop": 1754193314009}, {"name": "Set zone : Common Zone 02", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193324476, "stop": 1754193324476}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193326720, "stop": 1754193326720}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193327374, "stop": 1754193327374}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193327982, "stop": 1754193327982}, {"name": "Set template name = templateName-735996610", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193328323, "stop": 1754193328323}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193330686, "stop": 1754193330686}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193331648, "stop": 1754193331648}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193333170, "stop": 1754193333170}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193333995, "stop": 1754193333995}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193371374, "stop": 1754193371374}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193371374, "stop": 1754193371374}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193439842, "stop": 1754193439842}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193439842, "stop": 1754193439842}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193439842, "stop": 1754193439842}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193439861, "stop": 1754193439861}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_002'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193439862, "stop": 1754193439862}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193439863, "stop": 1754193439863}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193439864, "stop": 1754193439864}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193439864, "stop": 1754193439864}, {"name": "Validation message = Syntax could not be validated!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193474375, "stop": 1754193474375}, {"name": "Validation message = Syntax could not be validated!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754193474375, "stop": 1754193474375}], "attachments": [], "parameters": [], "start": 1754193286405, "stop": 1754193477696}