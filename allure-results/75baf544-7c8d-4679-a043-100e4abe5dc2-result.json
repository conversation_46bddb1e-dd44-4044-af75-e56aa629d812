{"uuid": "75baf544-7c8d-4679-a043-100e4abe5dc2", "historyId": "c5fd00ca6b576b360e2e1c0537d5a19c", "fullName": "eastnets.screening.regression.reports.Report_TC002.report_TC002", "labels": [{"name": "package", "value": "eastnets.screening.regression.reports.Report_TC002"}, {"name": "testClass", "value": "eastnets.screening.regression.reports.Report_TC002"}, {"name": "testMethod", "value": "report_TC002"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Report Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.reports.Report_TC002"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-3(38)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "tag", "value": "Reports"}], "links": [], "name": "Veri<PERSON>", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Veri<PERSON>", "steps": [{"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-275271375', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@6f6f70d, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-275271375', officialDate='null', entry=[ListEntry{type='null', name='EntryName-275271375', firstName='EntryFirstName-275271375', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-275271375', firstName='EntryFirstName-275271375', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735503, "stop": 1754173735503}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735504, "stop": 1754173735504}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735536, "stop": 1754173735536}, {"name": "select id from tZones tz where tz.DISPLAY_NAME  = 'Common Zone 08'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735536, "stop": 1754173735536}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735538, "stop": 1754173735538}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735538, "stop": 1754173735538}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735539, "stop": 1754173735539}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735566, "stop": 1754173735566}, {"name": "DELETE  FROM tSetContent   WHERE id IN ( SELECT tsc.id FROM tSetContent tsc  LEFT JOIN tBlackListVersions tblv on tblv.id =tsc.black_list_id  LEFT JOIN tListSets tls on tls.id = tsc.list_set_id  LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id  WHERE tbl.zone_id ='10'  AND tbl.display_name ='UN Democratic People Republic of Korea')", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735566, "stop": 1754173735566}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735600, "stop": 1754173735600}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735600, "stop": 1754173735600}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735601, "stop": 1754173735601}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735601, "stop": 1754173735601}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735623, "stop": 1754173735623}, {"name": "DELETE  FROM tGoodGuys  WHERE id IN ( SELECT tgg.id  FROM tGoodGuys tgg LEFT JOIN tBlackListVersions tblv on tblv.id =tgg.black_list_id LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id WHERE tbl.display_name ='UN Democratic People Republic of Korea')", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735623, "stop": 1754173735623}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735639, "stop": 1754173735639}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735639, "stop": 1754173735639}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735640, "stop": 1754173735640}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735640, "stop": 1754173735640}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735659, "stop": 1754173735659}, {"name": "DELETE FROM tBlackLists WHERE display_name ='UN Democratic People Republic of Korea'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735659, "stop": 1754173735659}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735906, "stop": 1754173735906}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735906, "stop": 1754173735906}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735907, "stop": 1754173735907}, {"name": "Connect to Database and Check if User Profile = full-right-profile_08 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735909, "stop": 1754173735909}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735909, "stop": 1754173735909}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735952, "stop": 1754173735952}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_08' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735952, "stop": 1754173735952}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735962, "stop": 1754173735962}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735962, "stop": 1754173735962}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735963, "stop": 1754173735963}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735992, "stop": 1754173735992}, {"name": "Delete From tListSetProfile where profile_id in (11)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735992, "stop": 1754173735992}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735997, "stop": 1754173735997}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735997, "stop": 1754173735997}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735997, "stop": 1754173735997}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735997, "stop": 1754173735997}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173735997, "stop": 1754173735997}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173736022, "stop": 1754173736022}, {"name": "select id from tZones tz where tz.DISPLAY_NAME  = 'Common Zone 08'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173736022, "stop": 1754173736022}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173736024, "stop": 1754173736024}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173736024, "stop": 1754173736024}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173736024, "stop": 1754173736024}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173736042, "stop": 1754173736042}, {"name": "DELETE  FROM tSetContent   WHERE id IN ( SELECT tsc.id FROM tSetContent tsc  LEFT JOIN tBlackListVersions tblv on tblv.id =tsc.black_list_id  LEFT JOIN tListSets tls on tls.id = tsc.list_set_id  LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id  WHERE tbl.zone_id ='10'  AND tbl.display_name ='UN Democratic People Republic of Korea')", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173736042, "stop": 1754173736042}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173736058, "stop": 1754173736058}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173736058, "stop": 1754173736058}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173736058, "stop": 1754173736058}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173736058, "stop": 1754173736058}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173736079, "stop": 1754173736079}, {"name": "DELETE  FROM tGoodGuys  WHERE id IN ( SELECT tgg.id  FROM tGoodGuys tgg LEFT JOIN tBlackListVersions tblv on tblv.id =tgg.black_list_id LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id WHERE tbl.display_name ='UN Democratic People Republic of Korea')", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173736080, "stop": 1754173736080}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173736100, "stop": 1754173736100}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173736100, "stop": 1754173736100}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173736101, "stop": 1754173736101}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173736101, "stop": 1754173736101}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173736144, "stop": 1754173736144}, {"name": "DELETE FROM tBlackLists WHERE display_name ='UN Democratic People Republic of Korea'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173736144, "stop": 1754173736144}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173736153, "stop": 1754173736153}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173736153, "stop": 1754173736153}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173736154, "stop": 1754173736154}, {"name": "Set zone : Common Zone 08", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173768574, "stop": 1754173768574}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173771975, "stop": 1754173771975}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173772938, "stop": 1754173772938}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173773444, "stop": 1754173773444}, {"name": "Set template name = templateName-275271375", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173773798, "stop": 1754173773798}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173776056, "stop": 1754173776056}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173777062, "stop": 1754173777062}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173778558, "stop": 1754173778558}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173779514, "stop": 1754173779514}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173840941, "stop": 1754173840941}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173840941, "stop": 1754173840941}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754174014014, "stop": 1754174014014}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754174014016, "stop": 1754174014016}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754174014016, "stop": 1754174014016}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754174014071, "stop": 1754174014071}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_08'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754174014071, "stop": 1754174014071}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754174014075, "stop": 1754174014075}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754174014075, "stop": 1754174014075}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754174014075, "stop": 1754174014075}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754174015051, "stop": 1754174015051}, {"name": "Validation message = File sent to the server for processing with id [2992]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754174052718, "stop": 1754174052719}, {"name": "Alert Message = File sent to the server for processing with id [2992]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754174052719, "stop": 1754174052719}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754174067697, "stop": 1754174067697}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754174101472, "stop": 1754174101472}, {"name": "Detection ID = 7604", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754174102350, "stop": 1754174102350}, {"name": "Status detection = Reported", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754174102639, "stop": 1754174102639}, {"name": "Detection ID = 7604", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754174103529, "stop": 1754174103529}, {"name": "Report status is: In Progress", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754174150506, "stop": 1754174150506}, {"name": "Report status is: In Progress", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754174157133, "stop": 1754174157133}, {"name": "Report status is: In Progress", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754174163761, "stop": 1754174163761}, {"name": "Report status is: In Progress", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754174170141, "stop": 1754174170141}, {"name": "Report status is: In Progress", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754174176858, "stop": 1754174176858}, {"name": "Report status is: In Progress", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754174183665, "stop": 1754174183665}, {"name": "Report status is: In Progress", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754174190538, "stop": 1754174190538}, {"name": "Report status is: In Progress", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754174197225, "stop": 1754174197225}, {"name": "Report status is: Done", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754174203579, "stop": 1754174203580}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754173735495, "stop": 1754174238803}