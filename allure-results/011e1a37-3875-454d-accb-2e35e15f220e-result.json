{"uuid": "011e1a37-3875-454d-accb-2e35e15f220e", "historyId": "c0c16a88d02b3d5fb122d5bc62f85540", "fullName": "eastnets.screening.regression.iso20022configurations.ISO20022_TC002.iso20022_TC0012", "labels": [{"name": "package", "value": "eastnets.screening.regression.iso20022configurations.ISO20022_TC002"}, {"name": "testClass", "value": "eastnets.screening.regression.iso20022configurations.ISO20022_TC002"}, {"name": "testMethod", "value": "iso20022_TC0012"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "ISO20022 Configurations Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.iso20022configurations.ISO20022_TC002"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-3(38)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "tag", "value": "ISO20022"}, {"name": "tag", "value": "Regression"}, {"name": "owner", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "links": [{"name": "87504", "type": "issue"}], "name": "Tfs 87504: Scan ISO message without xml tag having body fields only while <PERSON><PERSON> is added to config and no Wrapper defined.", "status": "failed", "statusDetails": {"known": false, "muted": false, "flaky": false, "message": "Test Failed due to an exception occurred in POM's method", "trace": "java.lang.AssertionError: Test Failed due to an exception occurred in POM's method\r\n\tat org.testng.Assert.fail(Assert.java:98)\r\n\tat core.ExceptionHandler.onExceptionRaised(ExceptionHandler.java:13)\r\n\tat eastnets.screening.regression.iso20022configurations.ISO20022_TC002.iso20022_TC0012(ISO20022_TC002.java:143)\r\n\tat java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:104)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:578)\r\n\tat org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:677)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:221)\r\n\tat org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:50)\r\n\tat org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:969)\r\n\tat org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:194)\r\n\tat org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148)\r\n\tat org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128)\r\n\tat java.base/java.util.ArrayList.forEach(ArrayList.java:1511)\r\n\tat org.testng.TestRunner.privateRun(TestRunner.java:829)\r\n\tat org.testng.TestRunner.run(TestRunner.java:602)\r\n\tat org.testng.SuiteRunner.runTest(SuiteRunner.java:437)\r\n\tat org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:475)\r\n\tat org.testng.internal.thread.ThreadUtil.lambda$execute$0(ThreadUtil.java:58)\r\n\tat java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:317)\r\n\tat java.base/java.util.concurrent.FutureTask.run(FutureTask.java)\r\n\tat --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)\r\n\tat java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:132)\r\n\tat java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:113)\r\n\tat java.base/java.util.concurrent.AbstractExecutorService.invokeAll(AbstractExecutorService.java:274)\r\n\tat org.testng.internal.thread.ThreadUtil.execute(ThreadUtil.java:64)\r\n\tat org.testng.SuiteRunner.runInParallelTestMode(SuiteRunner.java:458)\r\n\tat org.testng.SuiteRunner.privateRun(SuiteRunner.java:389)\r\n\tat org.testng.SuiteRunner.run(SuiteRunner.java:330)\r\n\tat org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)\r\n\tat org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)\r\n\tat org.testng.TestNG.runSuitesSequentially(TestNG.java:1256)\r\n\tat org.testng.TestNG.runSuitesLocally(TestNG.java:1176)\r\n\tat org.testng.TestNG.runSuites(TestNG.java:1099)\r\n\tat org.testng.TestNG.run(TestNG.java:1067)\r\n\tat com.intellij.rt.testng.IDEARemoteTestNG.run(IDEARemoteTestNG.java:65)\r\n\tat com.intellij.rt.testng.RemoteTestNGStarter.main(RemoteTestNGStarter.java:105)\r\nCaused by: org.openqa.selenium.InvalidSelectorException: invalid selector: An invalid or illegal selector was specified\n  (Session info: MicrosoftEdge=138.0.3351.95)\nFor documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\nBuild info: version: '4.13.0', revision: 'ba948ece5b*'\nSystem info: os.name: 'Windows Server 2022', os.arch: 'amd64', os.version: '10.0', java.version: '19.0.2'\nDriver info: org.openqa.selenium.remote.RemoteWebDriver\nCommand: [c896e746db9eccacfb08bca0bb61444d, findElements {using=xpath, value=//*[@class='ui-growl-title']}]\nCapabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:44701}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.Da...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: Proxy(), se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: 185cd41472be, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\nSession ID: c896e746db9eccacfb08bca0bb61444d\r\n\tat java.base/jdk.internal.reflect.DirectConstructorHandleAccessor.newInstance(DirectConstructorHandleAccessor.java:67)\r\n\tat java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)\r\n\tat java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:484)\r\n\tat org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.createException(W3CHttpResponseCodec.java:200)\r\n\tat org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:133)\r\n\tat org.openqa.selenium.remote.codec.w3c.W3CHttpResponseCodec.decode(W3CHttpResponseCodec.java:52)\r\n\tat org.openqa.selenium.remote.HttpCommandExecutor.execute(HttpCommandExecutor.java:191)\r\n\tat org.openqa.selenium.remote.TracedCommandExecutor.execute(TracedCommandExecutor.java:51)\r\n\tat org.openqa.selenium.remote.RemoteWebDriver.execute(RemoteWebDriver.java:602)\r\n\tat org.openqa.selenium.remote.ElementLocation$ElementFinder$2.findElements(ElementLocation.java:182)\r\n\tat org.openqa.selenium.remote.ElementLocation.findElements(ElementLocation.java:103)\r\n\tat org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:372)\r\n\tat org.openqa.selenium.remote.RemoteWebDriver.findElements(RemoteWebDriver.java:366)\r\n\tat eastnets.common.control.CommonAction.getAlertMessageString(CommonAction.java:69)\r\n\tat eastnets.screening.gui.iso20022Manager.ISO20022SchemaConfigurationManager.deleteSchemaVersion(ISO20022SchemaConfigurationManager.java:67)\r\n\tat eastnets.screening.control.ISO20022Control.deleteSchema(ISO20022Control.java:66)\r\n\tat core.ISOTestMethods.importISOSchema(ISOTestMethods.java:28)\r\n\tat eastnets.screening.regression.iso20022configurations.ISO20022_TC002.iso20022_TC0012(ISO20022_TC002.java:131)\r\n\t... 34 more\r\n"}, "stage": "finished", "description": "Tfs 87504: Scan ISO message without xml tag having body fields only while <PERSON><PERSON> is added to config and no Wrapper defined.", "steps": [{"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-793081574', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@47551c53, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-793081574', officialDate='null', entry=[ListEntry{type='null', name='EntryName-793081574', firstName='EntryFirstName-793081574', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-793081574', firstName='EntryFirstName-793081574', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754261687796, "stop": 1754261687796}, {"name": "Search for list by listName = ListName-793081574 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754261720483, "stop": 1754261720484}, {"name": "Navigate to By.xpath: //a[contains(text(),'ISO20022 Format Configuration')]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754261739360, "stop": 1754261739360}, {"name": "Navigate to By.xpath: //a[contains(text(),'ISO20022 Format Configuration')]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754261746909, "stop": 1754261746909}, {"name": "Create New Group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754261754476, "stop": 1754261754476}, {"name": "Connect to Database and Check if User Profile = full-right-profile_07 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754261834311, "stop": 1754261834311}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754261834312, "stop": 1754261834312}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754261834427, "stop": 1754261834427}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_07' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754261834427, "stop": 1754261834428}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754261834434, "stop": 1754261834434}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754261834434, "stop": 1754261834434}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754261834434, "stop": 1754261834434}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754261834477, "stop": 1754261834477}, {"name": "Delete From tListSetProfile where profile_id in (10)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754261834477, "stop": 1754261834477}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754261834485, "stop": 1754261834485}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754261834485, "stop": 1754261834485}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754261834486, "stop": 1754261834486}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754261834486, "stop": 1754261834486}, {"name": "Black list already exist.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754261845441, "stop": 1754261845441}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754261881739, "stop": 1754261881740}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754261881740, "stop": 1754261881740}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754261980901, "stop": 1754261980901}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754261980901, "stop": 1754261980901}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754261980902, "stop": 1754261980902}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754261980948, "stop": 1754261980948}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_07'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754261980949, "stop": 1754261980949}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754261980953, "stop": 1754261980954}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754261980954, "stop": 1754261980954}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754261980955, "stop": 1754261980955}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754262016869, "stop": 1754262016869}, {"name": "Test Data = ISO20022FormatConfiguration{zone='Zone (867793082)', groupName='Group_Name-671962696', iso20022SchemaConfiguration=ISO20022SchemaConfiguration{schemaName='pacs.009.001', schemaVersion='08', headerSwift='head.001.001.01', expectedResults='Schema version [%s] successfully deleted'}, expectedResults='Successfully imported schema [pacs.009.001] with version [08].'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754262027385, "stop": 1754262027385}, {"name": "Import schema.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754262027385, "stop": 1754262027385}, {"name": "Navigate to By.xpath: //a[contains(text(),'ISO20022 Schema Configuration')]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754262031696, "stop": 1754262031696}, {"name": "Navigate to By.xpath: //a[@href='#iso20022:homepage_business:tabViewListManager:tabView:schemaConfiguration']", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754262033119, "stop": 1754262033119}, {"name": "Check if schema with Name = pacs.009.001", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754262034488, "stop": 1754262034488}, {"name": "Delete schema with name = pacs.009.001", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754262034902, "stop": 1754262034902}, {"name": "Validation message = Schema version [pacs.009.001.08] successfully deleted.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754262040692, "stop": 1754262040692}, {"name": "Schema File Path = C:\\Users\\<USER>\\IdeaProjects\\SWS_selenium/src/test/resources/uploadsAndDownloads/uploads/ISO/pacs.009.001.08.xsd", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754262040693, "stop": 1754262040693}, {"name": "Validation message = Successfully imported schema [pacs.009.001] with version [08].", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754262049099, "stop": 1754262049099}, {"name": "Import schema.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754262049099, "stop": 1754262049099}, {"name": "Navigate to By.xpath: //a[contains(text(),'ISO20022 Schema Configuration')]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754262052324, "stop": 1754262052324}, {"name": "Navigate to By.xpath: //a[@href='#iso20022:homepage_business:tabViewListManager:tabView:schemaConfiguration']", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754262053549, "stop": 1754262053549}, {"name": "Check if schema with Name = head.001.001", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754262054899, "stop": 1754262054899}, {"name": "Delete schema with name = head.001.001", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754262055349, "stop": 1754262055350}, {"name": "Attempting to save screenshot...", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754262107962, "stop": 1754262107962}, {"name": "Screenshot saved at: C:\\Users\\<USER>\\IdeaProjects\\SWS_selenium\\FailedTestsScreenshots\\screenshot_1754262107962.png", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754262107980, "stop": 1754262107980}, {"name": "Error occurred While logging in eastnets.screening.regression.iso20022configurations.ISO20022_TC002$5.iso20022_TC0012 ----> invalid selector: An invalid or illegal selector was specified\n  (Session info: MicrosoftEdge=138.0.3351.95)\nFor documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#invalid-selector-exception\nBuild info: version: '4.13.0', revision: 'ba948ece5b*'\nSystem info: os.name: 'Windows Server 2022', os.arch: 'amd64', os.version: '10.0', java.version: '19.0.2'\nDriver info: org.openqa.selenium.remote.RemoteWebDriver\nCommand: [c896e746db9eccacfb08bca0bb61444d, findElements {using=xpath, value=//*[@class='ui-growl-title']}]\nCapabilities {acceptInsecureCerts: false, browserName: MicrosoftEdge, browserVersion: 138.0.3351.95, fedcm:accounts: true, ms:edgeOptions: {debuggerAddress: localhost:44701}, msedge: {msedgedriverVersion: 138.0.3351.95 (b095f7295f46..., userDataDir: /tmp/.com.microsoft.Edge.Da...}, networkConnectionEnabled: false, pageLoadStrategy: normal, platformName: linux, proxy: Proxy(), se:bidiEnabled: false, se:cdp: ws://***********:4444/sessi..., se:cdpVersion: 138.0.3351.95, se:containerName: 185cd41472be, se:deleteSessionOnUi: true, se:downloadsEnabled: true, se:noVncPort: 7900, se:vnc: ws://***********:4444/sessi..., se:vncEnabled: true, se:vncLocalAddress: ws://***********:7900, setWindowRect: true, strictFileInteractability: false, timeouts: {implicit: 0, pageLoad: 300000, script: 30000}, unhandledPromptBehavior: dismiss and notify, webauthn:extension:credBlob: true, webauthn:extension:largeBlob: true, webauthn:extension:minPinLength: true, webauthn:extension:prf: true, webauthn:virtualAuthenticators: true}\nSession ID: c896e746db9eccacfb08bca0bb61444d", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754262108003, "stop": 1754262108003}], "attachments": [{"name": "Screenshots", "source": "e3bd5f00-73ae-44e1-92e6-83f1c7e2279a-attachment.png"}], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "ISO20022FormatDetailsConfiguration{headerField='null', bodyField='CdtTrfTxInf/PmtId/TxId', Xpath='null', bodyFieldType='null', SEPA='null', category='SKIPPED', quickLink='null', fieldLinkWith='null', iso20022FormatConfiguration$=ISO20022FormatConfiguration{zone='null', groupName='Group_Name-%s', iso20022SchemaConfiguration=ISO20022SchemaConfiguration{schemaName='head.001.001', schemaVersion='01', headerSwift='head.001.001.01', expectedResults='Schema version [%s] successfully deleted'}, expectedResults='Successfully imported schema [%s] with version [%s].'}}"}], "start": 1754261687777, "stop": 1754262108005}