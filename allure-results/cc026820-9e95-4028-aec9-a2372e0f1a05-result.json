{"uuid": "cc026820-9e95-4028-aec9-a2372e0f1a05", "historyId": "35f85912395adc0ac69cdfc6a9bd434b", "fullName": "eastnets.screening.regression.formatmanager.FormatManager_TC002.formatManager_TC002", "labels": [{"name": "package", "value": "eastnets.screening.regression.formatmanager.FormatManager_TC002"}, {"name": "testClass", "value": "eastnets.screening.regression.formatmanager.FormatManager_TC002"}, {"name": "testMethod", "value": "formatManager_TC002"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Format Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.formatmanager.FormatManager_TC002"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-3(38)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON><PERSON>"}, {"name": "tag", "value": "Regression"}], "links": [], "name": "verify that the unmatched rule appears in the detection details from the results tab", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Format manager - Additional context variables \n* verify that the additional context variables appear when adding a good guy\n* verify the out of context result based on the violation filter added\n* verify the suspected record selection in drop down list\n* verify that the unmatched rule appears in the detection details from the results tab", "steps": [{"name": "EnList Test Data = EnList{testCaseTitle='null', listSet=ListSet{zone='null', name='ListSetName-253861669', rank='null', owner='null', swiftTemplate=eastnets.screening.entity.SwiftTemplate@7110ea6b, isoGroup='null', detectCountriesFlag='null', detectVesselsFlag='null', enLists=null}, zoneName='null', privateFlag='null', name='ListName-253861669', officialDate='null', entry=[ListEntry{type='null', name='EntryName-253861669', firstName='EntryFirstName-253861669', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}, ListEntry{type='null', name='EntryName-253861669', firstName='EntryFirstName-253861669', title='null', alias=null, address=null, programs='null', lastOccupation='null', birthDate='16/04/1995', birthCountry='null', residenceCountry='null', nationality='null', externalId='null', internalId='null', gender='null', deceased='null', remarks='null', dataSources='null', relatedTo='null'}], expectedMessage='Operation completed successfully.'}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173080788, "stop": 1754173080788}, {"name": "Connect to Database and Check if User Profile = full-right-profile_06 linked to any list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173080790, "stop": 1754173080790}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173080790, "stop": 1754173080790}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173080824, "stop": 1754173080824}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_06' and deleted= 0", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173080824, "stop": 1754173080824}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173080826, "stop": 1754173080826}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173080827, "stop": 1754173080827}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173080827, "stop": 1754173080827}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173080851, "stop": 1754173080851}, {"name": "Delete From tListSetProfile where profile_id in (9)", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173080851, "stop": 1754173080851}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173080856, "stop": 1754173080856}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173080856, "stop": 1754173080856}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173080857, "stop": 1754173080857}, {"name": "Number of effected rows = 1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173080857, "stop": 1754173080857}, {"name": "Search for list by listName = ListName-253861669 to verify the existence of the list.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173102594, "stop": 1754173102594}, {"name": "Set zone : Common Zone 06", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173116790, "stop": 1754173116790}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173119331, "stop": 1754173119331}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173120301, "stop": 1754173120301}, {"name": "<PERSON>lick Add <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173120917, "stop": 1754173120917}, {"name": "Set template name = templateName-253861669", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173121348, "stop": 1754173121348}, {"name": "Select all message type", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173124590, "stop": 1754173124590}, {"name": "Select all message fields", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173125991, "stop": 1754173125991}, {"name": "Click save button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173127871, "stop": 1754173127871}, {"name": "Check if swift template added successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173129001, "stop": 1754173129001}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173154568, "stop": 1754173154568}, {"name": "<PERSON> black list to list set.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173154568, "stop": 1754173154568}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173213436, "stop": 1754173213436}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173213436, "stop": 1754173213436}, {"name": " *******   Create an instance of ServiceDelegate to access backend DB    ******** ", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173213436, "stop": 1754173213436}, {"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173213456, "stop": 1754173213456}, {"name": "SELECT ID FROM tProfiles Where NAME ='full-right-profile_06'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173213456, "stop": 1754173213456}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173213460, "stop": 1754173213460}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173213460, "stop": 1754173213460}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173213461, "stop": 1754173213461}, {"name": "Validation message = Syntax is valid!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173240460, "stop": 1754173240460}, {"name": "Validation message = Syntax is valid!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173240460, "stop": 1754173240460}, {"name": "Validation message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173244891, "stop": 1754173244891}, {"name": "Validation Message = Operation completed successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173244891, "stop": 1754173244891}, {"name": "Click add new entry button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173265641, "stop": 1754173265641}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173314549, "stop": 1754173314549}, {"name": "Validation message = File sent to the server for processing with id [2985]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173331857, "stop": 1754173331857}, {"name": "Alert Message = File sent to the server for processing with id [2985]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173331857, "stop": 1754173331857}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173333028, "stop": 1754173333028}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173342762, "stop": 1754173342762}, {"name": "Detection ID = 7592", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173343995, "stop": 1754173343995}, {"name": "Actual validation Message = Good guy successfully created!", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173351160, "stop": 1754173351160}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173351950, "stop": 1754173351950}, {"name": "Validation message = File sent to the server for processing with id [2987]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173368806, "stop": 1754173368806}, {"name": "Alert Message = File sent to the server for processing with id [2987]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173368807, "stop": 1754173368807}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173370361, "stop": 1754173370361}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173380107, "stop": 1754173380107}, {"name": "Detection ID = 7596", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173381377, "stop": 1754173381377}, {"name": "Status detection = Accepted", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173381714, "stop": 1754173381714}, {"name": "Status detection = External", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173381815, "stop": 1754173381815}, {"name": "Status detection = Reported", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173381899, "stop": 1754173381899}, {"name": "Status detection = Accepted", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173382337, "stop": 1754173382337}, {"name": "Status detection = External", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173382447, "stop": 1754173382447}, {"name": "Status detection = Reported", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173382531, "stop": 1754173382531}, {"name": "Status detection = Accepted", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173382885, "stop": 1754173382885}, {"name": "Status detection = External", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173382987, "stop": 1754173382987}, {"name": "Status detection = Reported", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173383049, "stop": 1754173383049}, {"name": "Unmatched roles = CN <> '146'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754173385752, "stop": 1754173385752}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754173080784, "stop": 1754173386216}