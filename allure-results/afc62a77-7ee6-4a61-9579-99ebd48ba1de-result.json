{"uuid": "afc62a77-7ee6-4a61-9579-99ebd48ba1de", "historyId": "7b0bea7cb5332dc10bf7eb870f718377", "fullName": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC009.listManager_TC009", "labels": [{"name": "package", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC009"}, {"name": "testClass", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC009"}, {"name": "testMethod", "value": "listManager_TC009"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "List Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC009"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-1(36)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "List Manager"}], "links": [], "name": "Verify that user is able to Export Good Guys", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to Export Good Guys", "steps": [{"name": "*********      Executing DB Query     ************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235482727, "stop": 1754235482727}, {"name": "UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-02'", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235482727, "stop": 1754235482727}, {"name": "************     DB Query executed successfully     ***************", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235482735, "stop": 1754235482735}, {"name": "******     Closing DB Connection      *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235482735, "stop": 1754235482735}, {"name": "******      DB Connection closed successfully     *******", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235482736, "stop": 1754235482736}, {"name": "Login with User Name = operator-02 and Password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235483007, "stop": 1754235483007}, {"name": "<PERSON><PERSON> Reset <PERSON>", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235494579, "stop": 1754235494579}, {"name": "Click Search Button", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235495330, "stop": 1754235495330}, {"name": "response.getStatus()200", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235530027, "stop": 1754235530027}, {"name": "UTF-8", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235530027, "stop": 1754235530027}, {"name": "1", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754235530028, "stop": 1754235530028}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "XML"}], "start": 1754235482135, "stop": 1754235534443}