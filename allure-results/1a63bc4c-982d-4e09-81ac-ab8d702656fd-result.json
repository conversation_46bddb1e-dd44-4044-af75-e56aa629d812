{"uuid": "1a63bc4c-982d-4e09-81ac-ab8d702656fd", "historyId": "dc11dbd33f2d79b2f543672e1979c449", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC001.scanManager_TC001", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC001"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC001"}, {"name": "testMethod", "value": "scanManager_TC001"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC001"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is able to scan a generic text format file without creating alerts automatically option checked and take decision .", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to scan a generic text format file without creating alerts automatically option checked and take decision .", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172345774, "stop": 1754172345774}, {"name": "Validation message = File sent to the server for processing with id [2970]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172361727, "stop": 1754172361727}, {"name": "Alert Message = File sent to the server for processing with id [2970]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172361727, "stop": 1754172361727}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172363440, "stop": 1754172363440}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172374944, "stop": 1754172374944}, {"name": "Detection ID = 7556", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172377002, "stop": 1754172377002}, {"name": "Detection Status = REP", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172383866, "stop": 1754172383866}, {"name": "Detection Status = REP", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754172390881, "stop": 1754172390881}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "FileScan{filePath='/Generic.txt', format='Generic Text', encoding='null', enList=null, result='null', rank='null', detectVessels='true', detectCountries='true', createAlertsAutomatically='false'}"}], "start": 1754172345773, "stop": 1754172399204}