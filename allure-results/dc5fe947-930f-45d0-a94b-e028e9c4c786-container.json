{"uuid": "dc5fe947-930f-45d0-a94b-e028e9c4c786", "name": "Scan Manager Test Cases", "children": ["72033994-8ded-4374-a905-4efb495d7d82", "99857ed0-04aa-4733-a8a9-c139b052181a", "751fe54a-1420-4041-b1ee-b4e5beff7c83", "89ce8e7f-ca81-4267-a8bf-45462265e442", "7516ecad-9bd5-43b6-9e0c-7dda5a3c1d58", "7b937381-dbcf-4cf5-bfd9-c864974fdd1c", "e631851e-a7f9-44f2-8680-acbf0e98264d", "5a788d82-5939-4a01-b9f4-9dac3bfd4f5b", "9bc75fac-5b16-433e-a387-deffd466ec70", "2b5da73d-27dd-454a-9de0-1560eaac4bac", "eb35b5f3-74f1-4c70-b36a-b046b6ca43c0", "ebfef15a-30e7-4e2c-9fe3-f6b00b5b3773", "09867e31-418a-4803-ba6a-e338331ebce0", "6867593c-1904-4b84-932c-1fc9a9e5a6b9", "a9350ffc-459f-4726-bfb0-4f8eb6769197", "887c3efe-7531-4b6b-9cb4-6abc4aacf5c5", "28ea5c7a-5284-4555-af21-62194112c77c"], "befores": [{"name": "Setting up selenium web driver before each class run", "status": "passed", "stage": "finished", "description": "Setting up selenium web driver before each class run", "steps": [{"name": "Initialize Selenium driver before tests' Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754263191384, "stop": 1754263191384}, {"name": "Selenium webDriver was initialized successfully", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754263204829, "stop": 1754263204829}], "attachments": [], "parameters": [], "start": 1754263191384, "stop": 1754263204829}], "afters": [{"name": "Quitting selenium driver after each class run", "status": "passed", "stage": "finished", "description": "Quitting selenium driver after each class run", "steps": [{"name": "Closing selenium WebDriver after Class", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264783638, "stop": 1754264783638}], "attachments": [], "parameters": [], "start": 1754264783638, "stop": 1754264785356}], "start": 1754263191155, "stop": 1754264785356}