{"uuid": "1648ef6d-b7b9-46c2-a401-5f95bd4247ed", "historyId": "39fdefff98ede705eb3c9d2af44b2414", "fullName": "eastnets.admin.AuditManagerTest.checkAuditManager", "labels": [{"name": "package", "value": "eastnets.admin.AuditManagerTest"}, {"name": "testClass", "value": "eastnets.admin.AuditManagerTest"}, {"name": "testMethod", "value": "checkAuditManager"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Admin Test Cases"}, {"name": "subSuite", "value": "eastnets.admin.AuditManagerTest"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-1(36)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "tag", "value": "AuditManager"}], "links": [], "name": "Verify that an event recording audit is appearing properly for the removed Groups/Users", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that an event recording audit is appearing properly for the removed Groups/Users", "steps": [{"name": "Operator test data = Operator{id=0, loginName='selenium-user-491221643', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Common_Zone_01', displayName='Common Zone 01', description='Zone for most of the tests'}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180380471, "stop": 1754180380471}, {"name": "Group test data = Group{id=0, name='selenium-random-group-283808923', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='full-right-profile', enabled=true, writeRight=true, description='Profile with full Rights', zone=Zone{id=0, name='Common_Zone_01', displayName='Common Zone 01', description='Zone for most of the tests'}, profileRights={Administration={Archive Viewer=[], Operator Manager=[ALL], Group Manager=[ALL], Profile Manager=[ALL], Report Manager=[ALL], Zone Manager=[ALL], Audit Manager=[], Event Viewer=[]}, RPTSRV={}, SWS={License Manager=[], Name Checker=[ALL], WorldCheckSettings=[ALL], Batch List Management=[], ISO20022 Format Configuration=[ALL], DowJonesSettings=[ALL], ListSetManager=[ALL], MQ Connector=[ALL], DB Scanner=[ALL], Customer Card=[ALL], Good Guys Migration=[], SAA OFCA Station=[ALL], SAA OFCS Detect=[ALL], List Manager=[ALL], SafeTrade=[ALL], en.Reporting Scanner=[ALL], SWIFT Manager=[ALL], SWP Name Checker=[ALL], File Based Archive=[ALL], Detection Manager=[ALL], Approval Configuration=[ALL], DB Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], SAA OFCS Monitor=[ALL], Archive Management=[ALL], Event Viewer=[], ISO20022-Module=[ALL], ListExplorer=[ALL], ISO20022 Schema Configuration=[ALL], MQ Manager=[ALL], Archive Viewer=[ALL], Replay=[ALL], StrippingDetectorManager=[ALL], Report Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL], GoodGuyExplorer=[ALL], SafeWatch API=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-491221643', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Common_Zone_01', displayName='Common Zone 01', description='Zone for most of the tests'}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180380479, "stop": 1754180380479}, {"name": "Create new operator.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180380479, "stop": 1754180380479}, {"name": "Operator test Data = Operator{id=0, loginName='selenium-user-491221643', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Common_Zone_01', displayName='Common Zone 01', description='Zone for most of the tests'}}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180380479, "stop": 1754180380479}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180546572, "stop": 1754180546572}, {"name": "Set login name = selenium-user-491221643", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180552121, "stop": 1754180552121}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180553268, "stop": 1754180553268}, {"name": "Click Create new operator button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180571152, "stop": 1754180571152}, {"name": "Set login name = selenium-user-491221643", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180571594, "stop": 1754180571594}, {"name": "Set first name = Random", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180574565, "stop": 1754180574565}, {"name": "Set last name = User", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180575829, "stop": 1754180575829}, {"name": "Set password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180577134, "stop": 1754180577134}, {"name": "Set confirm password = hello00", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180578290, "stop": 1754180578290}, {"name": "Set enable flag = true", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180579258, "stop": 1754180579258}, {"name": "Set reset flag = false", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180580275, "stop": 1754180580275}, {"name": "Select zone  = Common Zone 01", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180582108, "stop": 1754180582108}, {"name": "Click save button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180597748, "stop": 1754180597748}, {"name": "Accept alert.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180618942, "stop": 1754180618942}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180748784, "stop": 1754180748784}, {"name": "Set login name = selenium-user-491221643", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180754242, "stop": 1754180754242}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180755437, "stop": 1754180755437}, {"name": "Create first group.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180781343, "stop": 1754180781343}, {"name": "Group test Data = Group{id=0, name='selenium-random-group-283808923', description='Random group for selenium testing', mail='', enabled=true, profile=Profile{id=0, name='full-right-profile', enabled=true, writeRight=true, description='Profile with full Rights', zone=Zone{id=0, name='Common_Zone_01', displayName='Common Zone 01', description='Zone for most of the tests'}, profileRights={Administration={Archive Viewer=[], Operator Manager=[ALL], Group Manager=[ALL], Profile Manager=[ALL], Report Manager=[ALL], Zone Manager=[ALL], Audit Manager=[], Event Viewer=[]}, RPTSRV={}, SWS={License Manager=[], Name Checker=[ALL], WorldCheckSettings=[ALL], Batch List Management=[], ISO20022 Format Configuration=[ALL], DowJonesSettings=[ALL], ListSetManager=[ALL], MQ Connector=[ALL], DB Scanner=[ALL], Customer Card=[ALL], Good Guys Migration=[], SAA OFCA Station=[ALL], SAA OFCS Detect=[ALL], List Manager=[ALL], SafeTrade=[ALL], en.Reporting Scanner=[ALL], SWIFT Manager=[ALL], SWP Name Checker=[ALL], File Based Archive=[ALL], Detection Manager=[ALL], Approval Configuration=[ALL], DB Manager=[ALL], Scan Manager=[ALL], Format Manager=[ALL], SAA OFCS Monitor=[ALL], Archive Management=[ALL], Event Viewer=[], ISO20022-Module=[ALL], ListExplorer=[ALL], ISO20022 Schema Configuration=[ALL], MQ Manager=[ALL], Archive Viewer=[ALL], Replay=[ALL], StrippingDetectorManager=[ALL], Report Manager=[ALL], File Scanner=[ALL], Advanced Settings=[ALL], GoodGuyExplorer=[ALL], SafeWatch API=[ALL]}}}, groupMembers=[Operator{id=0, loginName='selenium-user-491221643', firstName='Random', lastName='User', password='hello00', zone=Zone{id=0, name='Common_Zone_01', displayName='Common Zone 01', description='Zone for most of the tests'}}]}", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180781344, "stop": 1754180781344}, {"name": "Remove operator with login name = selenium-user-491221643.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754180998353, "stop": 1754180998353}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181000371, "stop": 1754181000371}, {"name": "Set login name = selenium-user-491221643", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181005920, "stop": 1754181005920}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181007068, "stop": 1754181007068}, {"name": "Select operator and click remove button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181007973, "stop": 1754181007973}, {"name": "Click reset button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181009822, "stop": 1754181009822}, {"name": "Set login name = selenium-user-491221643", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181015425, "stop": 1754181015425}, {"name": "Click search button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181016565, "stop": 1754181016565}, {"name": "Remove group with name = selenium-random-group-283808923.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181018336, "stop": 1754181018336}, {"name": "Select group and click remove button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181030530, "stop": 1754181030530}, {"name": "Asserting on data in result view", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181189026, "stop": 1754181189026}, {"name": "<PERSON><PERSON><PERSON> Passed Successfully.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754181189029, "stop": 1754181189029}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754180380455, "stop": 1754181189029}