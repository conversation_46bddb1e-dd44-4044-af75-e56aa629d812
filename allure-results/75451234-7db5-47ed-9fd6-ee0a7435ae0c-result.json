{"uuid": "75451234-7db5-47ed-9fd6-ee0a7435ae0c", "historyId": "207b755dbd5befc6904831905709c190", "fullName": "eastnets.screening.regression.iso20022configurations.ISO20022_TC001.iso20022_TC001", "labels": [{"name": "package", "value": "eastnets.screening.regression.iso20022configurations.ISO20022_TC001"}, {"name": "testClass", "value": "eastnets.screening.regression.iso20022configurations.ISO20022_TC001"}, {"name": "testMethod", "value": "iso20022_TC001"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "ISO20022 Configurations Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.iso20022configurations.ISO20022_TC001"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-3(38)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "tag", "value": "ISO20022"}, {"name": "tag", "value": "Regression"}, {"name": "owner", "value": "<PERSON><PERSON><PERSON><PERSON>"}], "links": [], "name": "Invalidate Import new schema with invalid file from 'ISO2022 Schema Configuration' module tab.", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Invalidate Import new schema with invalid file from 'ISO2022 Schema Configuration' module tab.", "steps": [{"name": "Schema File Path = C:\\Users\\<USER>\\IdeaProjects\\SWS_selenium/src/test/resources/uploadsAndDownloads/uploads/ISO/WithoutExtension", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754171085292, "stop": 1754171085292}, {"name": "Navigate to By.xpath: //a[contains(text(),'ISO20022 Schema Configuration')]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754171087416, "stop": 1754171087416}, {"name": "Navigate to By.xpath: //a[@href='#iso20022:homepage_business:tabViewListManager:tabView:schemaConfiguration']", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754171088167, "stop": 1754171088167}, {"name": "Validation message = Invalid File Type.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754171090636, "stop": 1754171090636}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754171085282, "stop": 1754171090639}