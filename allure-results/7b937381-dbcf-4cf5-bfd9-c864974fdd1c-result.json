{"uuid": "7b937381-dbcf-4cf5-bfd9-c864974fdd1c", "historyId": "c6142a835c74d3e0aa41bd07b46dcd7d", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC005.scanManager_TC005", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC005"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC005"}, {"name": "testMethod", "value": "scanManager_TC005"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC005"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is able to Print an Alert created from 'File Scan' from Result Tab", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to Print an Alert created from 'File Scan' from Result Tab", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264021219, "stop": 1754264021219}, {"name": "Validation message = File sent to the server for processing with id [3157]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264036222, "stop": 1754264036222}, {"name": "Alert Message = File sent to the server for processing with id [3157]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264036222, "stop": 1754264036222}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264037815, "stop": 1754264037815}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264060075, "stop": 1754264060075}, {"name": "Detection ID = 8279", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264061095, "stop": 1754264061095}, {"name": "Start Exporting Violation With Print Scope all And Document Type PDF", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264061110, "stop": 1754264061110}, {"name": "Click on close dialog button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754264071912, "stop": 1754264071912}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}], "start": 1754264020969, "stop": 1754264103293}