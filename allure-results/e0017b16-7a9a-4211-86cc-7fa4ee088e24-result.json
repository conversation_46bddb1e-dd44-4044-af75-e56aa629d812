{"uuid": "e0017b16-7a9a-4211-86cc-7fa4ee088e24", "historyId": "1ba2b43be4633ac1c4445402e7e990cb", "fullName": "eastnets.screening.regression.scanmanager.ScanManager_TC009.scanManager_TC009", "labels": [{"name": "package", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC009"}, {"name": "testClass", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC009"}, {"name": "testMethod", "value": "scanManager_TC009"}, {"name": "parentSuite", "value": "Safe Watch Filtering"}, {"name": "suite", "value": "Scan Manager Test Cases"}, {"name": "subSuite", "value": "eastnets.screening.regression.scanmanager.ScanManager_TC009"}, {"name": "host", "value": "ENAMAuto001"}, {"name": "thread", "value": "<EMAIL>-tests-2(37)"}, {"name": "framework", "value": "testng"}, {"name": "language", "value": "java"}, {"name": "owner", "value": "<PERSON>"}, {"name": "tag", "value": "Regression"}, {"name": "feature", "value": "Scan Manager"}], "links": [], "name": "Verify that user is able to Print an alert report in RTF format", "status": "passed", "statusDetails": {"known": false, "muted": false, "flaky": false}, "stage": "finished", "description": "Verify that user is able to Print an alert report in RTF format", "steps": [{"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242140189, "stop": 1754242140189}, {"name": "Validation message = File sent to the server for processing with id [3132]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242157946, "stop": 1754242157946}, {"name": "Alert Message = File sent to the server for processing with id [3132]", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242157946, "stop": 1754242157946}, {"name": "Navigate to Name <PERSON><PERSON>.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242159034, "stop": 1754242159034}, {"name": "Scan status = SUCCEEDED", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242168243, "stop": 1754242168243}, {"name": "Detection ID = 8084", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242169504, "stop": 1754242169504}, {"name": "Start Exporting Violation With Print Scope all And Document Type RTF", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242169518, "stop": 1754242169518}, {"name": "Click on close dialog button.", "status": "passed", "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1754242177787, "stop": 1754242177787}], "attachments": [], "parameters": [{"name": "loginType", "value": "filtering"}, {"name": "Application", "value": "SWF"}, {"name": "browserType", "value": "Edge"}, {"name": "arg0", "value": "eastnets.screening.entity.Report@3d1340f8"}], "start": 1754242139934, "stop": 1754242208977}