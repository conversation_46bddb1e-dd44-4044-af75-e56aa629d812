package core;

import core.constants.screening.GeneralConstants;
import core.util.Property;
import eastnets.screening.control.ISO20022Control;
import eastnets.screening.entity.ISO20022FormatConfiguration;
import eastnets.screening.entity.ISO20022FormatDetailsConfiguration;
import eastnets.screening.entity.ISO20022SchemaConfiguration;
import eastnets.screening.gui.iso20022Manager.ISO20022FormatConfigurationEditor;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;

public class ISOTestMethods {

    // Instance variables for page objects and controls
    private ISO20022Control iso20022Control = new ISO20022Control();
    private ISO20022FormatConfigurationEditor iso20022FormatConfigurationEditor = new ISO20022FormatConfigurationEditor();
    private Property property = new Property();
    @Step("Import ISO Schema")
    public void importISOSchema(RemoteWebDriver driver, ISO20022FormatConfiguration formatConfiguration) throws Exception {
        Allure.step("Import schema.");
        String schemaNameAndVersion = formatConfiguration.getIso20022SchemaConfiguration().getSchemaName()
                + "."
                + formatConfiguration.getIso20022SchemaConfiguration().getSchemaVersion();

        iso20022Control.deleteSchema(driver, formatConfiguration.getIso20022SchemaConfiguration().getSchemaName());


        String filePath = System.getProperty("user.dir")
                + property.fromFile(GeneralConstants.GENERAL_CONFIG_FILE_NAME).getProperty(GeneralConstants.DEFAULT_UPLOAD_PATH)
                + "/ISO/" + schemaNameAndVersion + ".xsd";
        Allure.step("Schema File Path = " + filePath);

        Assert.assertEquals(iso20022Control.importSchema(driver, filePath)
                , formatConfiguration.getExpectedResults()
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while import schema");
    }

    @Step("Import ISO Message")
    public void importISOMessage(RemoteWebDriver driver, ISO20022SchemaConfiguration schemaConfiguration, String filePath) throws Exception {
        Allure.step("Import schema.");
        String schemaNameAndVersion = schemaConfiguration.getSchemaName()
                + "."
                + schemaConfiguration.getSchemaVersion();
        if (filePath == null)
            filePath = System.getProperty("user.dir")
                    + property.fromFile(GeneralConstants.GENERAL_CONFIG_FILE_NAME).getProperty(GeneralConstants.DEFAULT_UPLOAD_PATH)
                    + "/ISO/" + schemaNameAndVersion + ".xsd";
        Allure.step("Schema File Path = " + filePath);

        Assert.assertEquals(iso20022Control.importConfigurations(driver, filePath)
                , "Message(s) [" + schemaNameAndVersion + "], successfully imported."
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while import schema");
    }

    public void exportISOSchema(RemoteWebDriver driver, ISO20022FormatConfiguration formatConfiguration, String prefix, String hub) throws Exception {
        Allure.step("Export schema.");
        Assert.assertTrue(iso20022Control.exportISOMessage(driver, formatConfiguration.getIso20022SchemaConfiguration(), prefix, hub)
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while import schema");
    }

    public void mergeISOSchema(RemoteWebDriver driver, ISO20022FormatConfiguration formatConfiguration, String fileName) throws Exception {
        Allure.step("Merge schema.");
        iso20022FormatConfigurationEditor.checkMessageNameCheckbox(driver, formatConfiguration.getIso20022SchemaConfiguration().getSchemaName());
        formatConfiguration.setExpectedResults("New field(s) created successfully.");
        Assert.assertEquals(iso20022Control.mergeISOMessage(driver, fileName)
                , formatConfiguration.getExpectedResults()
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while merging schema");
    }

    @Step("Add ISO Message")
    public void addISOMessage(RemoteWebDriver driver, ISO20022FormatConfiguration ISO20022FormatConfiguration, String SwiftHeader) throws Exception {
        Allure.step("Add new message.");
        iso20022Control.navigateToISO20022FormatManager(driver);
        iso20022Control.clickConfigure(driver);
        String schemaName = ISO20022FormatConfiguration.getIso20022SchemaConfiguration().getSchemaName();
        String schemaVersion = ISO20022FormatConfiguration.getIso20022SchemaConfiguration().getSchemaVersion();
        String schemaNameAndVersion = schemaName + "." + schemaVersion;

        ISO20022FormatConfiguration.setExpectedResults(String.format("New message [%s] with version [%s] added successfully.", schemaName, schemaNameAndVersion));
        Assert.assertEquals(iso20022Control.createNewMessage(driver, ISO20022FormatConfiguration.getIso20022SchemaConfiguration(), SwiftHeader)
                , ISO20022FormatConfiguration.getExpectedResults()
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while adding ISO message");
    }

    @Step("Add Body Field")
    public void addBodyField(RemoteWebDriver driver, ISO20022FormatDetailsConfiguration ISO20022FormatDetailsConfiguration) throws Exception {
        Allure.step("Add new body field.");
        iso20022FormatConfigurationEditor.checkMessageNameCheckbox(driver, ISO20022FormatDetailsConfiguration.getIso20022FormatConfiguration().getIso20022SchemaConfiguration().getSchemaName());
        iso20022FormatConfigurationEditor.clickDetailsButton(driver);
        Assert.assertEquals(iso20022Control.addBodyField(driver, ISO20022FormatDetailsConfiguration)
                , "New field successfully added!"
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while adding ISO message");

    }

    @Step("Remove ISO20022 Message")
    public void removeISO20022Message(RemoteWebDriver driver, ISO20022SchemaConfiguration ISO20022SchemaConfiguration) throws Exception {
        Allure.step("Remove ISO20022 Message.");
        iso20022FormatConfigurationEditor.checkMessageNameCheckbox(driver, ISO20022SchemaConfiguration.getSchemaName());
        String messageNameAndVersion = ISO20022SchemaConfiguration.getSchemaName() + "." + ISO20022SchemaConfiguration.getSchemaVersion();
        Assert.assertEquals(iso20022FormatConfigurationEditor.clickRemoveButton(driver)
                , "Message(s) [" + messageNameAndVersion + "] successfully deleted."
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while removing ISO message");
    }
}