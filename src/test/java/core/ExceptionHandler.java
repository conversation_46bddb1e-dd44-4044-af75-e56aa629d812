package core;

import core.constants.screening.GeneralConstants;
import core.util.Log;
import io.qameta.allure.Step;
import org.testng.Assert;

public class ExceptionHandler extends BaseTest {
    @Step("On exception raised")
    public static void onExceptionRaised(Exception e, String className, String methodName) {
        lifecycle.addAttachment("Screenshots", "", "png", saveScreenshotPNG());
        new Log().error("Error occurred While logging in " + className + "." + methodName, e);
        Assert.fail(GeneralConstants.POM_EXCEPTION_ERR_MSG, e);
    }
}
