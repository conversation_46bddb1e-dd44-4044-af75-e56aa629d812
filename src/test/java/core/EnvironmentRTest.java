package core;

import eastnets.common.control.ScreeningPropertiesChecker;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.sql.SQLException;

public class EnvironmentRTest extends BaseTest {
    private static final String EASTNETS_CONFIG_HOME = "EASTNETS_CONFIG_HOME";
    private static final String EASTNETS_SWFNG_HOME = "EASTNETS_SWFNG_HOME";

    @Test
    public void testConnection() {
        try {
            Assert.assertTrue(scdbSqlConnection.isValid(10));
            System.out.println("SCDB Connections is valid");
            Assert.assertTrue(safeWatchDBSqlConnection.isValid(10));
            System.out.println("SafeWatch DBConnections is valid");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testDatabaseVersion() {
        try {
            Assert.assertTrue(new ScreeningPropertiesChecker().checkTestVersion(scdbSqlConnection), "SCDB has not at the correct version");
            Assert.assertTrue(new ScreeningPropertiesChecker().checkTestVersion(safeWatchDBSqlConnection), "SafeWatchDB has not the correct version");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testScreeningEnvironmentVariable() {
        Assert.assertTrue(new ScreeningPropertiesChecker().checkIfEnvironmentVariableExist(EASTNETS_CONFIG_HOME), "EASTNETS_CONFIG_HOME not defined");
        Assert.assertTrue(new ScreeningPropertiesChecker().checkIfEnvironmentVariableExist(EASTNETS_SWFNG_HOME), "EASTNETS_SWFNG_HOME not defined");
        Assert.assertTrue(new ScreeningPropertiesChecker().checkEastNetsConfigHomeContent(EASTNETS_CONFIG_HOME), "EASTNETS_CONFIG_HOME does not contains the mandatory files or folders");
    }

    @Test
    public void testScreeningExternalToolPath() {
        try {
            Assert.assertTrue(new ScreeningPropertiesChecker().verifyScdbExternalToolPaths(scdbSqlConnection));
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }
}
