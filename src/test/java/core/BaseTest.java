package core;

import com.google.common.collect.ImmutableMap;
import core.constants.screening.GeneralConstants;
import core.database.DatabaseDriver;
import core.util.Log;
import core.util.Property;
import core.util.Wait;
import eastnets.common.control.Application;
import io.github.bonigarcia.wdm.WebDriverManager;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Attachment;
import org.apache.commons.io.FileUtils;
import org.openqa.selenium.OutputType;
import org.openqa.selenium.TakesScreenshot;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.edge.EdgeOptions;
import org.openqa.selenium.firefox.FirefoxOptions;
import org.openqa.selenium.firefox.FirefoxProfile;
import org.openqa.selenium.remote.LocalFileDetector;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.*;
import org.testng.ITestResult;
import org.testng.TestNG;
import org.testng.xml.XmlSuite;
import org.testng.xml.XmlTest;
import org.testng.xml.XmlClass;
import org.testng.xml.XmlInclude;

import java.io.File;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.sql.Connection;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.List;
import java.util.ArrayList;
import java.util.Arrays;

import static com.github.automatedowl.tools.AllureEnvironmentWriter.allureEnvironmentWriter;

public class BaseTest {

    public static AllureLifecycle lifecycle;
    //Initialize instances of properties files to be used in all tests
    public static String userDirectoryPath = System.getProperty("user.dir");
    public static Properties screeningGeneralConfigsProps = new Property().fromFile(GeneralConstants.GENERAL_CONFIG_FILE_NAME);
    public static Properties saaGeneralConfigsProps = new Property().fromFile(core.constants.saa.GeneralConstants.GENERAL_CONFIG_FILE_NAME);
    public static Properties screeningTestDataConfigsProps = new Property().fromFile(GeneralConstants.SCREENING_TEST_DATA_CONFIG_FILE_PATH);
    public static Properties dockerConfigProps = new Property().fromFile(GeneralConstants.DOCKER_CONFIG_FILE);
    public static Properties saaTestDataConfigsProps = new Property().fromFile(core.constants.saa.GeneralConstants.TEST_DATA_CONFIG_FILE_NAME);

    // Browser's default download path config from properties file
    public static String browserDefaultDownloadPath = System.getProperty("user.dir") + screeningGeneralConfigsProps.getProperty(GeneralConstants.DEFAULT_DOWNLOAD_PATH).trim();
    public static String screenshotPath = System.getProperty("user.dir") + screeningGeneralConfigsProps.getProperty(GeneralConstants.SCREENSHOT_FAILD_TESTS_PATH).trim();
    public static String dockerServerIp = dockerConfigProps.getProperty(GeneralConstants.DOCKER_SERVER_IP).trim();

    @BeforeSuite(description = "Setting up allure report")
    @Parameters("browserType")
    public void setAllureEnvironment(@Optional("Chrome") String browserType) {
        try {
            new Log().info("Setting up allure report environment data: " + browserType);
            allureEnvironmentWriter(
                    ImmutableMap.<String, String>builder()
                            .put("Browser Type", browserType)
                            .put("OS Version", System.getProperty("os.name"))
                            .put("Java Version", System.getProperty("java.version"))
                            .put("SQL Server.Version", "SQL Server 2019")
                            .put("DB Connection String", screeningGeneralConfigsProps.getProperty(GeneralConstants.DB_URL))
                            .put("SWF_URL", screeningGeneralConfigsProps.getProperty(GeneralConstants.SCREENING_LOGIN_URL)).build(), System.getProperty("user.dir") + "/allure-results/");
        } catch (Exception e) {
            new Log().error("Error occurred while setting up allure report before ReportManger_TC01 on browser: " + browserType, e);
        }
    }


    @Attachment(value = "Screenshot", type = "image/png")
    public static byte[] saveScreenshotPNG() {

        try {
            File screenshot = ((TakesScreenshot) getDriver()).getScreenshotAs(OutputType.FILE);
            new Log().info("Attempting to save screenshot...");
            File screenshotLocation = new File(screenshotPath + "/screenshot_" + System.currentTimeMillis() + ".png");
            FileUtils.copyFile(screenshot, screenshotLocation);
            new Log().info("Screenshot saved at: " + screenshotLocation.getAbsolutePath());
            return FileUtils.readFileToByteArray(screenshotLocation);
        } catch (IOException e) {
            new Log().error("Error taking screenshot", e);
            return new byte[0];
        }
    }


    @BeforeSuite(description = "Deleting all previously downloaded files before running suite", enabled = false)
    public void clearDownloadedFiles() {
        try {
            FileUtils.cleanDirectory(new File(browserDefaultDownloadPath));
        } catch (IOException e) {
            new Log().error("Error occurred while deleting download files before test " + new Object() {
            }
                    .getClass()
                    .getName() + "." + new Object() {
            }
                    .getClass()
                    .getEnclosingMethod()
                    .getName(), e);
        }
    }
    private static ThreadLocal<RemoteWebDriver> threadLocalDriver = new ThreadLocal<>();

    @Parameters({"Application", "browserType"})
    @BeforeTest(description = "Setting up selenium web driver before each class run", alwaysRun = true)
    public void aloadConfiguration(@Optional("SWF") String application, @Optional("Edge") String browserType) {
        try {
            Allure.step("Initialize Selenium driver before tests' Class");
            RemoteWebDriver driver = null;
            // initialize selenium driver that is set as a config in Testng.xml
            switch (browserType) {
                case ("Chrome"):
                    WebDriverManager.chromedriver().setup();
                    driver =new RemoteWebDriver(new URL(dockerServerIp), setChromeOption());
                    break;
                case ("Firefox"):
                    driver = new RemoteWebDriver(new URL(dockerServerIp), setFireFoxOption());
                    break;
                case ("Edge"):
                    driver = new RemoteWebDriver(new URL(dockerServerIp), setEdgeOptions());
                    break;
            }

            threadLocalDriver.set(driver);
            driver.setFileDetector(new LocalFileDetector());
            driver.manage().window().maximize();
            lifecycle = Allure.getLifecycle();
          //  driver.manage().timeouts().implicitlyWait(Duration.ofSeconds(30));
           // driver.manage().timeouts().pageLoadTimeout(Duration.ofSeconds(50));
            switch (application) {
                case ("SWF"):
                    driver.navigate().to(screeningGeneralConfigsProps.getProperty(GeneralConstants.SCREENING_LOGIN_URL));
                    break;
                case ("SAA"):
                    driver.navigate().to(saaGeneralConfigsProps.getProperty(core.constants.saa.GeneralConstants.SCREENING_LOGIN_URL));
                    break;
            }

            Allure.step("Selenium webDriver was initialized successfully");
        } catch (Exception e) {
            new Log().error("Error occurred while initializing selenium web driver", e);
            Assert.fail("Error occurred while initializing selenium web driver");
        }

    }


    private static ChromeOptions setChromeOption() {
        ChromeOptions options = new ChromeOptions();
        options.addArguments("--start-maximized");
        options.addArguments("--disable-web-security");
        //options.addArguments("--no-proxy-server");

        Map<String, Object> prefs = new HashMap<>();
        prefs.put("credentials_enable_service", false);
        prefs.put("profile.password_manager_enabled", false);
        prefs.put("download.extensions_to_open", "xml");
        prefs.put("download.prompt_for_download", false);
        prefs.put("profile.default.content_settings.popups", 0);

        options.addArguments("force-device-scale-factor=0.9");
        options.setExperimentalOption("prefs", prefs);
        options.setCapability("se:downloadsEnabled", true);
        options.addArguments("--safebrowsing-disable-download-protection");
        options.addArguments("--safebrowsing-disable-extension-blacklist");
        options.addArguments("--lang=en");
       /* options.addArguments("--no-sandbox");
        options.addArguments("--disable-dev-shm-usage");*/
        options.addArguments(String.format("--unsafely-treat-insecure-origin-as-secure=%s",
                screeningGeneralConfigsProps.getProperty(GeneralConstants.SCREENING_LOGIN_URL)));


        return options;
    }

    private static FirefoxOptions setFireFoxOption() {
        FirefoxProfile profile = new FirefoxProfile();
        profile.setPreference("browser.download.folderList", 2);
        profile.setPreference("browser.helperApps.neverAsk.saveToDisk",
                "text/csv,application/java-archive, application/x-msexcel,application/excel," +
                        "application/vnd.openxmlformats-officedocument.wordprocessingml.document,application/x-excel," +
                        "application/vnd.ms-excel,image/png,image/jpeg,text/html,text/plain,application/msword,application/xml," +
                        "application/vnd.microsoft.portable-executable");
        profile.setPreference("layout.css.devPixelsPerPx", "0.9");
        FirefoxOptions option = new FirefoxOptions();
        option.setCapability("se:downloadsEnabled", true);
        option.setProfile(profile);
        return option;
    }

    private static EdgeOptions setEdgeOptions() {
        EdgeOptions options = new EdgeOptions();
        HashMap<String, Object> edgePrefs = new HashMap<>();
        edgePrefs.put("nativeEvents", false);
        options.setCapability("se:downloadsEnabled", true);
        options.addArguments("force-device-scale-factor=0.9");
        options.addArguments("high-dpi-support=0.9");
        options.setExperimentalOption("prefs", edgePrefs);
        return options;
    }

    @AfterTest(description = "Quitting selenium driver after each class run", alwaysRun = true)
    public void closeDriver()  {
        Allure.step("Closing selenium WebDriver after Class");
        getDriver().quit();
    }

    protected Connection scdbSqlConnection = null;
    protected Connection safeWatchDBSqlConnection = null;

    @BeforeTest(enabled = false)
    public void beforeTest() {
        try {
            scdbSqlConnection = new DatabaseDriver().getConnection(Application.ADMIN);
            safeWatchDBSqlConnection = new DatabaseDriver().getConnection(Application.SFP);
        } catch (Exception e) {
            new Log().error("Error occurred while initializing database connections", e);
        }
    }

    @AfterTest(enabled = false)
    public void afterTest() {
        try {
            if (scdbSqlConnection != null) scdbSqlConnection.close();
            if (safeWatchDBSqlConnection != null) safeWatchDBSqlConnection.close();
        } catch (Exception e) {
            new Log().error("Error occurred while closing database connections", e);
        }
    }

    public static RemoteWebDriver getDriver() {
        return threadLocalDriver.get();
    }

    // Track failed tests for rerun
    private static List<String> failedTestMethods = new ArrayList<>();
    private static boolean hasRerunExecuted = false;

    /**
     * Collect failed test methods during execution and handle Allure reporting for data providers
     */
  /*  @AfterMethod(alwaysRun = false)
    public void trackFailedTests(ITestResult result) {
        if (result.getStatus() == ITestResult.FAILURE && !hasRerunExecuted) {
            String testMethod = result.getTestClass().getName() + "." + result.getMethod().getMethodName();
            if (!failedTestMethods.contains(testMethod)) {
                failedTestMethods.add(testMethod);
                new Log().info("Failed test tracked: " + testMethod);
            }
        }
    }*/


    /**
     * @AfterSuite method to rerun failed test cases after whole suite finishes
     */
   /* @AfterSuite(alwaysRun = false)
    public void rerunFailedTestCases() {
        // Prevent multiple executions
        if (hasRerunExecuted) {
            return;
        }

        if (failedTestMethods.isEmpty()) {
            new Log().info("========================================");
            new Log().info("*** ALL TESTS PASSED! ***");
            new Log().info("No failed tests to rerun.");
            new Log().info("========================================");
            return;
        }

        hasRerunExecuted = true;

        new Log().info("========================================");
        new Log().info("RERUNNING FAILED TEST CASES");
        new Log().info("========================================");
        new Log().info("Found " + failedTestMethods.size() + " failed test(s) to rerun:");

        for (String failedTest : failedTestMethods) {
            new Log().info("  - " + failedTest);
        }

        new Log().info("");
        new Log().info("Creating new TestNG suite for failed tests...");

        try {
            // Create TestNG instance for rerun
            TestNG testNG = new TestNG();

            // Create XML suite programmatically
            XmlSuite suite = new XmlSuite();
            suite.setName("Failed Tests Rerun Suite");

            XmlTest test = new XmlTest(suite);
            test.setName("Rerun Failed Tests");

            // Add failed test classes and methods
            Map<String, List<String>> classMethodMap = new HashMap<>();

            for (String failedTest : failedTestMethods) {
                String className = failedTest.substring(0, failedTest.lastIndexOf('.'));
                String methodName = failedTest.substring(failedTest.lastIndexOf('.') + 1);

                classMethodMap.computeIfAbsent(className, k -> new ArrayList<>()).add(methodName);
            }

            List<XmlClass> classes = new ArrayList<>();
            for (Map.Entry<String, List<String>> entry : classMethodMap.entrySet()) {
                XmlClass xmlClass = new XmlClass(entry.getKey());

                // Add specific methods to include
                List<XmlInclude> includes = new ArrayList<>();
                for (String methodName : entry.getValue()) {
                    includes.add(new XmlInclude(methodName));
                }
                xmlClass.setIncludedMethods(includes);

                classes.add(xmlClass);
            }

            test.setXmlClasses(classes);
            testNG.setXmlSuites(Arrays.asList(suite));

            new Log().info("Starting failed test rerun...");

            // Run the failed tests
            testNG.run();

            new Log().info("========================================");
            new Log().info("FAILED TEST RERUN COMPLETED");
            new Log().info("========================================");

        } catch (Exception e) {
            new Log().error("Error during failed test rerun: " + e.getMessage(), e);
        } finally {
            // Clear the failed tests list
            failedTestMethods.clear();
        }
    }*/

    }

