/*
package core;

import org.openqa.selenium.remote.RemoteWebDriver;

public class DriverManager {

    private static final ThreadLocal<RemoteWebDriver> threadLocal = new ThreadLocal<>();

    public static RemoteWebDriver getDriver() {
        return threadLocal.get();
    }

    public static void setDriver(RemoteWebDriver driver) {
        threadLocal.set(driver);
    }

    public static void closeDriver() {
        if (getDriver() != null) {
            try {
                getDriver().close();
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                getDriver().quit();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        threadLocal.remove();
    }
}
*/
