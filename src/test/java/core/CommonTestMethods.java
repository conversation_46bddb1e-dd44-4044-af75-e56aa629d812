package core;

import com.fasterxml.jackson.databind.ObjectMapper;
import core.constants.screening.GeneralConstants;
import core.util.ProcessManager;
import core.util.Property;
import core.util.Randomizer;
import core.util.Wait;
import eastnets.admin.backendServices.AdminServicesDelegate;
import eastnets.admin.control.OperatorControl;
import eastnets.admin.entity.Common;
import eastnets.admin.entity.Operator;
import eastnets.admin.entity.Profile;
import eastnets.admin.entity.Zone;
import eastnets.admin.gui.OperatorManager;
import eastnets.common.gui.Navigation;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.dbManager.DBFlowConfigControl;
import eastnets.screening.control.FormatManagerControl;
import eastnets.screening.control.ISO20022Control;
import eastnets.screening.control.SwiftManagerControl;
import eastnets.screening.control.listManager.*;
import eastnets.screening.control.scanManger.DBScanControl;
import eastnets.screening.control.scanManger.FileScanControl;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.*;
import eastnets.screening.gui.listManager.listSet.ListSetEditor;
import eastnets.screening.gui.scanManager.scanName.NameScanManager;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;

import java.io.File;
import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

import static core.BaseTest.*;

public class CommonTestMethods {

    // Instance variables for page objects and controls
    private OperatorControl operatorControl;
    private OperatorManager operatorManager;
    private BlackListControl blackListControl;
    private SwiftManagerControl swiftManagerControl;
    private ListSetControl listSetControl;
    private ListExplorerControl listExplorerControl;
    private NameScanControl nameScanControl;
    private NameScanManager nameScanManager;
    private GoodGuyControl goodGuyControl;
    private FileScanControl fileScanControl;
    private ResultManagerControl resultManagerControl;
    private DBFlowConfigControl dbFlowConfigControl;
    private DBScanControl dbScanControl;
    private ISO20022Control iso20022Control;
    private FormatManagerControl formatManagerControl;
    private ActivityControl activityControl;
    private ProcessManager processManager;
    private Wait wait;
    private core.util.Randomizer randomizer;

    // Instance variables for service delegates
    private AdminServicesDelegate adminServicesDelegate;
    private ScreeningServicesDelegate screeningServicesDelegate;

    public static Properties testDataConfigsProps = new Property().fromFile(GeneralConstants.SCREENING_TEST_DATA_CONFIG_FILE_PATH);

    /**
     * Constructor to initialize all instance variables for thread-safe parallel execution
     */
    public CommonTestMethods() {
        // Initialize page objects and controls
        this.operatorControl = new OperatorControl();
        this.operatorManager = new OperatorManager();
        this.blackListControl = new BlackListControl();
        this.swiftManagerControl = new SwiftManagerControl();
        this.listSetControl = new ListSetControl();
        this.listExplorerControl = new ListExplorerControl();
        this.nameScanControl = new NameScanControl();
        this.nameScanManager = new NameScanManager();
        this.goodGuyControl = new GoodGuyControl();
        this.fileScanControl = new FileScanControl();
        this.resultManagerControl = new ResultManagerControl();
        this.dbFlowConfigControl = new DBFlowConfigControl();
        this.dbScanControl = new DBScanControl();
        this.iso20022Control = new ISO20022Control();
        this.formatManagerControl = new FormatManagerControl();
        this.activityControl = new ActivityControl();
        this.processManager = new ProcessManager();
        this.wait = new Wait();
        this.randomizer = new core.util.Randomizer();

        // Initialize service delegates
        this.adminServicesDelegate = new AdminServicesDelegate();
        this.screeningServicesDelegate = new ScreeningServicesDelegate();
    }

    @Step("Create operator")
    public Operator createOperator(RemoteWebDriver driver) throws Exception {

        Profile profile = Common.PROFILE.FULL_RIGHT_001.getProfile();
        Operator operator = Operator.getRandom(profile.getZone());
        Allure.step("Operator test data = " + operator.toString());
        Allure.step("Create new operator.");
        Allure.step("Operator test Data = " + operator);
        Assert.assertTrue(operatorControl.createOperator(driver, operator), String.format("Can't create operator %s", operator.getLoginName()));
        return operator;
    }

    @Step("Delete operator")
    public void deleteOperator(RemoteWebDriver driver, Operator operator) throws Exception {

        Allure.step(String.format("Remove operator with login name = %s.", operator.getLoginName()));
        Navigation.OPERATOR.navigate(driver);
        Assert.assertFalse(operatorManager.removeOperator(driver, operator), String.format("Can't remove operator %s", operator.getLoginName()));

    }

    @Step("Remove function access")
    public List<Profile> removeFunctionAccess(Profile profile, String permissionName) throws SQLException {
        Allure.step(String.format("Connect to database to remove Link between profile = %s and Permission %s"
                , profile.getName(), permissionName));
        List<Profile> profileFromDB = adminServicesDelegate.getProfileRight(profile.getName(), permissionName);
        Assert.assertTrue(profileFromDB.size() > 0, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        Allure.step(String.format("Delete function permission '%s' from DataBase", permissionName));
        Assert.assertTrue(adminServicesDelegate.removeProfileRightLink(
                String.valueOf(profileFromDB.get(0).getGrantId()),
                String.valueOf(profileFromDB.get(0).getId())) > 0, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
        return profileFromDB;
    }

    @Step("Create list set")
    public void createListSet(RemoteWebDriver driver, EnList list, String profileName) throws Exception {
        Allure.step("Connect to Database and Check if User Profile = " + profileName + " linked to any list set.");
        Allure.step("Number of effected rows = " + screeningServicesDelegate.deleteProfileFromListSet(profileName));

        Assert.assertTrue(blackListControl.create_Black_List(driver, list)
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new black list.");
        SwiftTemplate swiftTemplate = list.getListSet().getSwiftTemplate();
        if (swiftTemplate != null)
            Assert.assertTrue(swiftManagerControl.CreateSwiftTemplate(driver, swiftTemplate, list.getZoneName())
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while create new swift template.");

        Assert.assertTrue(listSetControl.create_list_set(driver, list).contains(list.getExpectedMessage())
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new list set.");



        Allure.step("Link black list to list set.");
        Assert.assertTrue(listSetControl.link_black_list_to_list_set(driver, list).contains(list.getExpectedMessage())
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while add black list to listSet");


        Assert.assertTrue(listSetControl.select_profiles(driver, profileName).contains(list.getExpectedMessage())
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while select profiles in Profiles-ListSet Association.");

        Assert.assertTrue(screeningServicesDelegate.checkIfListSetLinkedToProfile(profileName)
                , GeneralConstants.DB_ERROR_MSG + " while check if listSet linked to profile.");
        driver.get(driver.getCurrentUrl());
    }

    @Step("Create list set")
    public void createListSetOnly(RemoteWebDriver driver, EnList list, String profileName) throws Exception {
        Allure.step("Connect to Database and Check if User Profile = " + profileName + " linked to any list set.");
        Allure.step("Number of effected rows = " + screeningServicesDelegate.deleteProfileFromListSet(profileName));

        Assert.assertTrue(listSetControl.create_list_set(driver, list).contains(list.getExpectedMessage())
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new list set.");

        Allure.step("Link black list to list set.");
        Assert.assertTrue(listSetControl.link_black_list_to_list_set(driver, list).contains(list.getExpectedMessage())
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while add black list to listSet");







        Assert.assertTrue(listSetControl.select_profiles(driver, profileName).contains(list.getExpectedMessage())
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while select profiles in Profiles-ListSet Association.");

        Assert.assertTrue(screeningServicesDelegate.checkIfListSetLinkedToProfile(profileName)
                , GeneralConstants.DB_ERROR_MSG + " while check if listSet linked to profile.");
        driver.get(driver.getCurrentUrl());
    }

    @Step("import black list ")
    public void importBList(RemoteWebDriver driver, EnList list, String profileName, String filePath) throws Exception {

        deleteBlackListFromDB(list);
        Assert.assertEquals(blackListControl.import_list(driver, list.getZoneName(), filePath, false, false)
                , "SUCCEEDED"
                , GeneralConstants.POM_EXCEPTION_ERR_MSG);


    }

    @Step("import black list ")
    public void importBList(RemoteWebDriver driver, EnList list, String filePath, boolean lock_flag, boolean upgrade_flag) throws Exception {

        deleteBlackListFromDB(list);
        Assert.assertEquals(blackListControl.import_list(driver, list.getZoneName(), filePath, lock_flag, upgrade_flag)
                , "SUCCEEDED"
                , GeneralConstants.POM_EXCEPTION_ERR_MSG);


    }

    @Step("import black list and list set and link them together")
    public void importBListAndLinkToNewListSet(RemoteWebDriver driver, EnList list, String profileName, String filePath) throws Exception {
        Allure.step("Connect to Database and Check if User Profile = " + profileName + " linked to any list set.");
        Allure.step("Number of effected rows = " + screeningServicesDelegate.deleteProfileFromListSet(profileName));

        screeningServicesDelegate.deleteLinkBtwBlackListAndListSet(list.getZoneName(), list.getName());
        screeningServicesDelegate.deleteLinkBtwBlackListAndGoodGuy(list.getZoneName(), list.getName());
        screeningServicesDelegate.deleteBlackList(list.getName());

        Assert.assertEquals(blackListControl.import_list(driver, list.getZoneName(), filePath, false, false)
                , "SUCCEEDED"
                , GeneralConstants.POM_EXCEPTION_ERR_MSG);


        Assert.assertTrue(swiftManagerControl.CreateSwiftTemplate(driver, list.getListSet().getSwiftTemplate(), list.getZoneName())
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while create new swift template.");

        Assert.assertTrue(listSetControl.create_list_set(driver, list).contains(list.getExpectedMessage())
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new list set.");

        Allure.step("Link black list to list set.");
        Assert.assertTrue(listSetControl.link_black_list_to_list_set(driver, list).contains(list.getExpectedMessage())
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while add black list to listSet");

        Assert.assertTrue(listSetControl.select_profiles(driver, profileName).contains(list.getExpectedMessage())
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while select profiles in Profiles-ListSet Association.");

        Assert.assertTrue(screeningServicesDelegate.checkIfListSetLinkedToProfile(profileName)
                , GeneralConstants.DB_ERROR_MSG + " while check if listSet linked to profile.");
        driver.get(driver.getCurrentUrl());
    }

    @Step("import black list ,create list set with iso group and link them together")
    public void importBListAndLinkToNewListSetWithIsoGroup(RemoteWebDriver driver, EnList list, String profileName, String filePathForBlackList, String filePathForISOConfig) throws Exception {
        Allure.step("Connect to Database and Check if User Profile = " + profileName + " linked to any list set.");
        Allure.step("Number of effected rows = " + screeningServicesDelegate.deleteProfileFromListSet(profileName));

        screeningServicesDelegate.deleteLinkBtwBlackListAndListSet(list.getZoneName(), list.getName());
        screeningServicesDelegate.deleteLinkBtwBlackListAndGoodGuy(list.getZoneName(), list.getName());
        screeningServicesDelegate.deleteBlackList(list.getName());

        Assert.assertEquals(blackListControl.import_list(driver, list.getZoneName(), filePathForBlackList, false, false)
                , "SUCCEEDED"
                , GeneralConstants.POM_EXCEPTION_ERR_MSG);


        ObjectMapper mapper = new ObjectMapper();
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("isoConfiguration");
        ISO20022FormatConfiguration formatConfiguration = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath)
                , ISO20022FormatConfiguration.class);
        formatConfiguration.setZone(list.getZoneName());
        formatConfiguration.setGroupName(String.format(formatConfiguration.getGroupName(), randomizer.getInt()));

        Assert.assertEquals(iso20022Control.createGroup(driver, formatConfiguration)
                , String.format("New group [%s] successfully added", formatConfiguration.getGroupName())
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while create new group.");

        iso20022Control.clickConfigure(driver);

        String actualResult = iso20022Control.importConfigurations(driver, filePathForISOConfig);
        Assert.assertTrue(actualResult.contains(" successfully imported")
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while create new group.");


        list.getListSet().setIsoGroup(formatConfiguration.getGroupName());
        list.getListSet().setSwiftTemplate(null);

        Assert.assertTrue(listSetControl.create_list_set(driver, list).contains(list.getExpectedMessage())
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new list set.");

        Allure.step("Link black list to list set.");
        Assert.assertTrue(listSetControl.link_black_list_to_list_set(driver, list).contains(list.getExpectedMessage())
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while add black list to listSet");

        Assert.assertTrue(listSetControl.select_profiles(driver, profileName).contains(list.getExpectedMessage())
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while select profiles in Profiles-ListSet Association.");

        Assert.assertTrue(screeningServicesDelegate.checkIfListSetLinkedToProfile(profileName)
                , GeneralConstants.DB_ERROR_MSG + " while check if listSet linked to profile.");
        driver.get(driver.getCurrentUrl());
    }

    @Step("create list set and link it with previously created/imported black list")
    public void linkImportedListToNewListSet(RemoteWebDriver driver, ArrayList<EnList> list, String profileName) throws Exception {
        Allure.step("Connect to Database and Check if User Profile = " + profileName + " linked to any list set.");
        Allure.step("Number of effected rows = " + screeningServicesDelegate.deleteProfileFromListSet(profileName));

        Assert.assertTrue(swiftManagerControl.CreateSwiftTemplate(driver, list.get(0).getListSet().getSwiftTemplate(), list.get(0).getZoneName())
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while create new swift template.");

        Assert.assertTrue(listSetControl.create_list_set(driver, list.get(0)).contains(list.get(0).getExpectedMessage())
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new list set.");

        for (int i = 0; i < list.size(); i++) {
            Allure.step("Link black list to list set.");
            Assert.assertTrue(listSetControl.link_black_list_to_list_set(driver, list.get(i)).contains(list.get(i).getExpectedMessage())
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while add black list to listSet");
        }



        Assert.assertTrue(listSetControl.select_profiles(driver, profileName).contains(list.get(0).getExpectedMessage())
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while select profiles in Profiles-ListSet Association.");

        Assert.assertTrue(screeningServicesDelegate.checkIfListSetLinkedToProfile(profileName)
                , GeneralConstants.DB_ERROR_MSG + " while check if listSet linked to profile.");
    }

    @Step("Create new entry with type and names")
    public void createNewEntryWithTypeAndNames(RemoteWebDriver driver, EnList list) throws Exception {
        listExplorerControl.search(driver, list);

        Assert.assertTrue(listExplorerControl.create_entry_with_type_and_names(driver, list.getEntry().get(0))
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while Creating new Entry." + list.getEntry().get(0).toString());

    }

    @Step("Create new entry with details info")
    public void createNewEntryWithDetailsInfo(RemoteWebDriver driver, EnList list, ListEntry entey) throws Exception {
        listExplorerControl.search_by_zone_and_blackList(driver, list);

        Assert.assertTrue(listExplorerControl.create_entry_with_details_info(driver, entey)
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while Creating new Entry." + entey.toString());

    }

    @Step("Create Good Guy")
    public void createGoodGuy(RemoteWebDriver driver, EnList list) throws Exception {

        String name = list.getEntry().get(0).getName() + ", " + list.getEntry().get(0).getFirstName();
        nameScanControl.scan_Name(driver, name, null, null, true, true, true, false);
        nameScanManager.chooseScannedNameFromResultTable(driver, name);

        Assert.assertEquals(nameScanControl.add_detection_as_GG(driver, null), "Good guy successfully created!"
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while add Detection as Good Guy.");
        Assert.assertTrue(goodGuyControl.verify_gg_exist(driver, name)
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while check Good Guy with Accepted Text = " + name + " exist");

    }

    @Step("Scan file and get detection ID")
    public String scanFileAndGetDetectionID(RemoteWebDriver driver, FileScan fileScan) throws Exception {


        Assert.assertEquals(fileScanControl.scan_file(driver, fileScan).split("\\[")[0], "File sent to the server for processing with id ", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
        Assert.assertEquals(resultManagerControl.check_scanned_file_result(driver), "SUCCEEDED", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
        String detectionID = resultManagerControl.get_detection_id(driver);
        Assert.assertNotNull(detectionID, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
        Assert.assertNotEquals(detectionID, "n/a", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        return detectionID;
    }

    @Step("Scan file")
    public void scanFile(RemoteWebDriver driver, FileScan fileScan) throws Exception {

        Navigation.SCAN_MANAGER.navigate(driver);
        Assert.assertEquals(fileScanControl.scan_file(driver, fileScan).split("\\[")[0], "File sent to the server for processing with id ", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
        Assert.assertEquals(resultManagerControl.check_scanned_file_result(driver), "SUCCEEDED", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

    }

    @Step("DB Scan")
    public String dbScan(RemoteWebDriver driver, String zone, String filePath) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("dbScan");
        DBConfigurations db = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), DBConfigurations.class);
        db.setName(String.format(db.getName(), randomizer.getInt()));
        db.setZoneName(zone);

        db.setHost(screeningGeneralConfigsProps.getProperty(GeneralConstants.REMOTE_SERVER_IP));
        db.setPort(screeningGeneralConfigsProps.getProperty(GeneralConstants.DB_PORT));
        db.setUserName(screeningGeneralConfigsProps.getProperty(GeneralConstants.DB_NAME));
        screeningServicesDelegate.CreateDBScanTable();
        screeningServicesDelegate.insertDataInDBScanTable(filePath);


        dbFlowConfigControl.create_new_db_configurations(driver, db);
        dbScanControl.db_Scan(driver, db);
        Assert.assertEquals(resultManagerControl.check_scanned_file_result(driver), "SUCCEEDED", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        String detectionID = resultManagerControl.get_detection_id(driver);
        Assert.assertNotNull(detectionID, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        return detectionID;
    }

    @Step("Delete black list from DB")
    public void deleteBlackListFromDB(EnList list) throws SQLException, IOException {
        screeningServicesDelegate.deleteLinkBtwBlackListAndListSet(list.getZoneName(), list.getName());
        screeningServicesDelegate.deleteLinkBtwBlackListAndGoodGuy(list.getZoneName(), list.getName());
        screeningServicesDelegate.deleteBlackList(list.getName());
    }

    @Step("Get enList data")
    public EnList getEnListData() throws IOException {
        int random = randomizer.getInt();
        ObjectMapper mapper = new ObjectMapper();
        String csvDataFilePath = testDataConfigsProps.getProperty("addNewEntry");
        EnList list = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), EnList.class);
        list.setName(String.format(list.getName(), random));
        for (int i = 0; i < list.getEntry().size(); i++) {
            list.getEntry().get(i).setName(String.format(list.getEntry().get(i).getName(), random));
            list.getEntry().get(i).setFirstName(String.format(list.getEntry().get(i).getFirstName(), random));
        }
        list.getListSet().setName(String.format(list.getListSet().getName(), random));
        list.getListSet().getSwiftTemplate().setTemplateName(String.format(list.getListSet().getSwiftTemplate().getTemplateName(), random));
        Allure.step("EnList Test Data = " + list.toString());
        return list;
    }

    @Step("Get format data")
    public Format getFormatData() throws IOException {
        int random = randomizer.getInt();
        ObjectMapper mapper = new ObjectMapper();
        String csvDataFilePath = testDataConfigsProps.getProperty("addNewFormat");
        Format format = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), Format.class);
        format.setName(String.format(format.getName(), random));
        for (int i = 0; i < format.getFields().size(); i++) {
            format.getFields().get(i).setName(String.format(format.getFields().get(i).getName(), random));
        }
        Allure.step("Format Test Data = " + format.toString());
        return format;
    }

    @Step("Get file scan data")
    public FileScan getFileScanData(String filePath) throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("blockDetection");
        FileScan fileScan = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), FileScan.class);
        fileScan.setFilePath(filePath);
        return fileScan;
    }

    @Step("Get file scan data")
    public FileScan getFileScanData(String filePath, String format, String result, String rank) throws IOException {
        FileScan fileScan = new FileScan();
        fileScan.setFilePath(filePath);
        fileScan.setFormat(format);
        fileScan.setRank(rank);
        fileScan.setResult(result);
        fileScan.setDetectCountries(true);
        fileScan.setDetectVessels(true);
        fileScan.setCreateAlertsAutomatically(true);
        return fileScan;
    }



    @Step("Import entries")
    public void addListSetAndimportEntries(RemoteWebDriver driver, EnList enList, Profile profile, Zone zone, String filePath, Format format, String isoGroup) throws Exception {

        enList.setZoneName(zone.getDisplayName());
        enList.getListSet().setIsoGroup(isoGroup);
        ArrayList<EnList> lists = new ArrayList<>();
        lists.add(enList);


        Assert.assertTrue(blackListControl.create_Black_List(driver, enList)
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new black list.");

        Allure.step("Start Link Imported List to List Set.");
        linkImportedListToNewListSet(driver, lists, profile.getName());

        importEntries(driver,enList, zone, filePath, format);

    }

    public void importEntries(RemoteWebDriver driver,EnList enList, Zone zone, String filePath, Format format) throws Exception {
        format.setName("Format-" + randomizer.getInt());
        format.setZone(zone.getDisplayName());

        Assert.assertNull(formatManagerControl.AddNewFormat(driver, format), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG + " while creating new format.");

        Assert.assertEquals(listExplorerControl.import_entries(driver, zone.getDisplayName(), enList.getName(), filePath, format.getName(), "UTF8", false, false, false, true, false)
                , "Operation completed successfully."
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while importing entries.");
        Assert.assertEquals(activityControl.get_status(driver)
                , "SUCCEEDED"
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while getting import list status.");
    }


    @Step("Stop all scan services")
    public void stopAllScanServices() throws Exception {
        ProcessBuilder builder = new ProcessBuilder("cmd.exe", "/C", GeneralConstants.STOP_SCAN_SERVICE_COMMAND);
        processManager.executeCommandAndReturnPID(builder, "started");
    }

    @Step("Stop Arch")
    public void stoArch() throws Exception {
        ProcessBuilder builder = new ProcessBuilder("cmd.exe", "/C", GeneralConstants.STOP_ALL_COMMAND);
        processManager.executeCommandOnLocalMachineWithAssert(builder);
    }

    @Step("Stop all services")
    public void stopAllServices() throws Exception {
        ProcessBuilder builder = new ProcessBuilder("cmd.exe", "/C", GeneralConstants.STOP_ALL_SERVICES_COMMAND);
        processManager.executeCommandOnLocalMachineWithAssert(builder);
    }

    @Step("Stop sws server")
    public void stopSwsServer() throws Exception {
        ProcessBuilder builder = new ProcessBuilder("cmd.exe", "/C", GeneralConstants.STOP_SWS_SERVER_COMMAND);
        processManager.executeCommandOnLocalMachineWithAssert(builder);
    }

    @Step("Start segregate scan services")
    public void startSegregateScanServices() throws Exception {
        ProcessBuilder builder = new ProcessBuilder("cmd.exe", "/C", GeneralConstants.START_SEGREGATE_SCAN_SERVICE_COMMAND);
        processManager.executeCommandOnLocalMachineWithAssert(builder);
        wait.time(Wait.ONE_SECOND * 20);
    }

    @Step("Start default scan services")
    public void startDefaultScanServices() throws Exception {
        ProcessBuilder builder = new ProcessBuilder("cmd.exe", "/C", GeneralConstants.START_DEFAULT_SCAN_SERVICE_COMMAND);
        processManager.executeCommandAndReturnPID(builder, "start");
    }

    @Step("Stop Application Manager")
    public void stopApplicationManager() throws Exception {
        ProcessBuilder builder = new ProcessBuilder("cmd.exe", "/C", GeneralConstants.STOP_APPLICATION_MANAGER_COMMAND);
        processManager.executeCommandOnLocalMachineWithAssert(builder);
    }

    @Step("Start Application Manager")
    public void startApplicationManager() throws Exception {
        ProcessBuilder builder = new ProcessBuilder("cmd.exe", "/C", GeneralConstants.START_APPLICATION_MANAGER_COMMAND);
        processManager.executeCommandOnLocalMachineWithAssert(builder);
    }

    @Step("Start SWS Server")
    public void startSwsServer() throws Exception {
        ProcessBuilder builder = new ProcessBuilder("cmd.exe", "/C", GeneralConstants.START_SWS_SERVER_COMMAND);
        processManager.executeCommandOnLocalMachineWithAssert(builder);
    }

    @Step("Start Report Server")
    public void startReportServer() throws Exception {
        ProcessBuilder builder = new ProcessBuilder("cmd.exe", "/C", GeneralConstants.START_REPORT_SERVER_COMMAND);
        processManager.executeCommandOnLocalMachineWithAssert(builder);
    }

    @Step("Start ZooKeeper")
    public void startZooKeeper() throws Exception {
        ProcessBuilder builder = new ProcessBuilder("cmd.exe", "/C", GeneralConstants.START_ZOO_KEEPER_COMMAND);
        processManager.executeCommandOnLocalMachineWithAssert(builder);
    }

    @Step("Start Kafka")
    public void startKafka() throws Exception {
        ProcessBuilder builder = new ProcessBuilder("cmd.exe", "/C", GeneralConstants.START_KAFKA_COMMAND);
        processManager.executeCommandOnLocalMachineWithAssert(builder);
    }

    @Step("Start Services")
    public void startServices() throws Exception {
        ProcessBuilder builder = new ProcessBuilder("cmd.exe", "/C", GeneralConstants.START_SERVICES_COMMAND);
        processManager.executeCommandOnLocalMachineWithAssert(builder);
    }

    @Step("Start tomcat")
    public void startTomcat() throws Exception {
        ProcessBuilder builder = new ProcessBuilder("cmd.exe", "/C", GeneralConstants.START_TOMCAT_COMMAND);
        processManager.executeCommandOnLocalMachineWithAssert(builder);
    }

    @Step("stop tomcat")
    public void stopTomcat() throws Exception {
        ProcessBuilder builder = new ProcessBuilder("cmd.exe", "/C", GeneralConstants.STOP_TOMCAT_COMMAND);
        processManager.executeCommandOnLocalMachineWithAssert(builder);
    }

    @Step("Restart Services")
    public void restartNewArch() throws Exception {
        stopAllServices();
        stopSwsServer();
        wait.time(Wait.ONE_SECOND * 5);
        startSwsServer();
        startServices();
        wait.time(Wait.ONE_SECOND * 60);
    }

    @Step("Restart Application manager")
    public void restartAppManager() throws Exception {
        stopApplicationManager();
        startApplicationManager();
    }

    public void restartTomcat() throws Exception {
        stopTomcat();
        wait.time(Wait.ONE_SECOND * 10);
        startTomcat();
        wait.time(Wait.ONE_SECOND * 20);

    }


}