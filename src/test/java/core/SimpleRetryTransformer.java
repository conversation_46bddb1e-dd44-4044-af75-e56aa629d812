package core;

import org.testng.IAnnotationTransformer;
import org.testng.annotations.ITestAnnotation;

import java.lang.reflect.Constructor;
import java.lang.reflect.Method;

/**
 * Simple annotation transformer to automatically add retry to all tests
 * This works in IDE execution
 */
public class SimpleRetryTransformer implements IAnnotationTransformer {

    public SimpleRetryTransformer() {
        System.out.println("=== SimpleRetryTransformer LOADED ===");
    }

    @Override
    public void transform(ITestAnnotation annotation, Class testClass, Constructor testConstructor, Method testMethod) {
        System.out.println("=== Transform called for: " +
            (testMethod != null ? testMethod.getName() : "unknown") + " ===");

        // Only add retry analyzer if one isn't already set
/*
        if (annotation.getRetryAnalyzerClass() == null) {
*/
            annotation.setRetryAnalyzer(SimpleRetryAnalyzer.class);
            System.out.println("Added retry analyzer to: " +
                (testMethod != null ? testMethod.getName() : "unknown method"));
       /* } else {
            System.out.println("Retry analyzer already exists for: " +
                (testMethod != null ? testMethod.getName() : "unknown method"));
        }*/
    }
}
