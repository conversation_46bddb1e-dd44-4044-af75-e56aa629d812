package core;

import org.testng.IRetryAnalyzer;
import org.testng.ITestResult;

/**
 * Simple retry analyzer for IDE execution
 * Just add retryAnalyzer = SimpleRetryAnalyzer.class to your @Test annotation
 */
public class SimpleRetryAnalyzer implements IRetryAnalyzer {
    
    private int retryCount = 0;
    private static final int maxRetryCount = 1; // Retry # times
    
    @Override
    public boolean retry(ITestResult result) {
        if (retryCount < maxRetryCount) {
            retryCount++;
            System.out.println("Retrying test: " + result.getMethod().getMethodName() + 
                             " - Attempt " + retryCount + "/" + maxRetryCount);
            
            // Optional: Add delay between retries
            try {
                Thread.sleep(2000); // 2 second delay
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            
            return true;
        }
        return false;
    }
}
