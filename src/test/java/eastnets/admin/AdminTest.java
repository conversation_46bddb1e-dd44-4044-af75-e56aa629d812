package eastnets.admin;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paypal.selion.platform.dataprovider.DataProviderFactory;
import com.paypal.selion.platform.dataprovider.DataResource;
import com.paypal.selion.platform.dataprovider.SeLionDataProvider;
import com.paypal.selion.platform.dataprovider.impl.InputStreamResource;
import core.BaseTest;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import core.util.Property;
import core.util.Randomizer;
import eastnets.admin.backendServices.AdminServicesDelegate;
import eastnets.admin.boundary.ProfileResource;
import eastnets.admin.boundary.ZoneResource;
import eastnets.admin.control.GroupControl;
import eastnets.admin.control.OperatorControl;
import eastnets.admin.control.ProfileControl;
import eastnets.admin.entity.Group;
import eastnets.admin.entity.Operator;
import eastnets.admin.entity.Profile;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.common.gui.Navigation;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.control.SwiftManagerControl;
import eastnets.screening.control.listManager.BlackListControl;
import eastnets.screening.control.listManager.ListExplorerControl;
import eastnets.screening.control.listManager.ListSetControl;
import eastnets.screening.control.scanManger.FileScanControl;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FileScan;
import eastnets.screening.gui.listManager.listSet.ListSetEditor;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.sql.SQLException;
import java.util.Collections;
import java.util.List;


public class AdminTest extends BaseTest {

    Group globalGroup;
    Operator globalOperator;
    Profile globalProfile;

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final ZoneResource zoneResource = new ZoneResource();
    private final ProfileResource profileResource = new ProfileResource();
    private final OperatorControl operatorControl = new OperatorControl();
    private final GroupControl groupControl = new GroupControl();
    private final ProfileControl profileControl = new ProfileControl();
    private final CommonAction commonAction = new CommonAction();
    private final AdminServicesDelegate adminServicesDelegate = new AdminServicesDelegate();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final BlackListControl blackListControl = new BlackListControl();
    private final SwiftManagerControl swiftManagerControl = new SwiftManagerControl();
    private final ListSetControl listSetControl = new ListSetControl();
    private final ListExplorerControl listExplorerControl = new ListExplorerControl();
    private final FileScanControl fileScanControl = new FileScanControl();
    private final ResultManagerControl resultManagerControl = new ResultManagerControl();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();
    private final Property property = new Property();
    private final Randomizer randomizer = new Randomizer();

    private RemoteWebDriver driver;



    @BeforeClass()
    public void createNewOperator() {
        try {
            driver = getDriver();
            loginPage.login(driver
                    , screeningGeneralConfigsProps.getProperty(GeneralConstants.VALID_ADMIN_MAIL)
                    , screeningGeneralConfigsProps.getProperty(GeneralConstants.VALID_ADMIN_PASSWORD)

            );

            globalProfile = Profile.getRandom("full-admin-right");
            globalGroup = Group.getRandom(globalProfile, Collections.emptyList());
            globalOperator = Operator.getRandom(globalGroup.getProfile().getZone());

            Allure.step("Operator test data = " + globalOperator.toString());

            globalGroup.setGroupMembers(Collections.singletonList(globalOperator));
            Allure.step("Group test data = " + globalGroup.toString());
            Allure.step("Create new zone .");
            Allure.step("Zone test Data = " + globalGroup.getProfile().getZone());
            Assert.assertTrue(zoneResource.createZone(driver, globalGroup.getProfile().getZone()), String.format("Can't create group %s", globalGroup.getProfile().getZone().getDisplayName()));
            Allure.step("Create new profile .");
            Allure.step("Profile test Data = " + globalProfile);
            Assert.assertTrue(profileResource.createProfile(driver, globalProfile), String.format("Can't create profile %s", globalProfile.getName()));

            Allure.step("Create new operator.");
            Allure.step("Operator test Data = " + globalOperator);
            Assert.assertTrue(operatorControl.createOperator(driver, globalOperator), String.format("Can't create operator %s", globalOperator.getLoginName()));

            Allure.step("Create new group.");
            Allure.step("Group test Data = " + globalGroup);
            Assert.assertTrue(groupControl.createGroup(driver, globalGroup), String.format("Can't create group %s", globalGroup.getName()));

            commonAction.logout(driver);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test( priority = 0, enabled = true, dataProvider = "profileTestData")
    @Owner("Sara Abdellatif")
    public void checkProfilePermissions(Profile profile) {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription(profile.getTestCaseName()));
            lifecycle.updateTestCase(testResult -> testResult.setName(profile.getTestCaseName()));

            SoftAssert softAssert = new SoftAssert();

            globalOperator.setProfileName(globalProfile.getName());
            globalOperator.setPermissionName(profile.getPermissionName());

            List<Profile> profileFromDB = removeFunctionAccess(globalOperator);

            try {
                loginPage.login(driver, globalOperator.getLoginName(), globalOperator.getPassword());
                softAssert.assertTrue(profileControl.checkPermission(driver, profile), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
                driver.navigate().refresh();
                commonAction.logout(driver);
            } catch (Exception e) {
                Assert.assertTrue(adminServicesDelegate.updateProfileRightLink(profileFromDB) > 0, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
                ExceptionHandler.onExceptionRaised(e
                        , new Object() {
                        }.getClass().getName()
                        , new Object() {
                        }.getClass().getEnclosingMethod().getName());
            }

            Assert.assertTrue(adminServicesDelegate.updateProfileRightLink(profileFromDB) > 0, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Allure.step("Operator logout.");
            softAssert.assertAll();

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test( priority = 1, enabled = true, dataProvider = "operatorTestData")
    @Owner("Sara Abdellatif")
    public void checkOperatorPermissions(Operator operator) {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription(operator.getTestCaseTitle()));
            lifecycle.updateTestCase(testResult -> testResult.setName(operator.getTestCaseTitle()));

            SoftAssert softAssert = new SoftAssert();

            operator.setProfileName(globalProfile.getName());
            List<Profile> profileFromDB = removeFunctionAccess(operator);

            try {
                loginPage.login(driver, globalOperator.getLoginName(), globalOperator.getPassword());
                softAssert.assertTrue(operatorControl.checkPermission(driver, operator.getFunctionToCheck(), globalOperator), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
                driver.navigate().refresh();
                commonAction.logout(driver);
            } catch (Exception e) {
                Assert.assertTrue(adminServicesDelegate.updateProfileRightLink(profileFromDB) > 0, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
                ExceptionHandler.onExceptionRaised(e
                        , new Object() {
                        }.getClass().getName()
                        , new Object() {
                        }.getClass().getEnclosingMethod().getName());
            }

            Assert.assertTrue(adminServicesDelegate.updateProfileRightLink(profileFromDB) > 0, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Allure.step("Operator logout.");
            softAssert.assertAll();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test( priority = 2, enabled = true,
            dataProvider = "OpWithSecondProfileGroupTestData")
    @Owner("Sara Abdellatif")
    public void checkPermissionWhenAssignSecGroupAndProfile(Operator operatorDM) {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription(operatorDM.getTestCaseTitle()));
            lifecycle.updateTestCase(testResult -> testResult.setName(operatorDM.getTestCaseTitle()));

            loginPage.login(driver
                    , screeningGeneralConfigsProps.getProperty(GeneralConstants.VALID_ADMIN_MAIL)
                    , screeningGeneralConfigsProps.getProperty(GeneralConstants.VALID_ADMIN_PASSWORD)

            );

            Group group = createOperator(driver);

            Operator operator = group.getGroupMembers().get(0);

            operator.setPermissionName(operatorDM.getPermissionName());
            operator.setProfileName(group.getProfile().getName());

            removeFunctionAccess(operator);

            Allure.step("Create second profile .");
            Profile secondProfile = Profile.getRandom("Test-Profile-SWS");
            secondProfile.setZone(group.getProfile().getZone());
            Allure.step("Profile test Data = " + secondProfile);
            Assert.assertTrue(profileResource.createProfile(driver, secondProfile), String.format("Can't create profile %s", secondProfile.getName()));


            Group secondGroup = Group.getRandom(secondProfile, Collections.emptyList());
            secondGroup.setGroupMembers(Collections.singletonList(operator));
            Allure.step("Create second group.");
            Allure.step("Group test Data = " + secondGroup);
            Assert.assertTrue(groupControl.createGroup(driver, secondGroup), String.format("Can't create group %s", secondGroup.getName()));

            commonAction.logout(driver);
            loginPage.login(driver, operator.getLoginName(), operator.getPassword());


            EnList enList = prepareTestData();
            property.updateKeyValue(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME, "profile.name", secondProfile.getName());
            enList.getListSet().setOwner(secondGroup.getName());
            enList.setZoneName(group.getProfile().getZone().getDisplayName());
            createListSet(driver, enList, secondProfile.getName());
            createNewEntry(driver, enList);

            scanFile(driver, enList, operatorDM);
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            driver.navigate().refresh();
            commonAction.logout(driver);
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test( priority = 3, enabled = true,
            dataProvider = "OpWithSecondProfileGroupZoneTestData")
    @Owner("Sara Abdellatif")
    public void checkPermissionWhenAssignSecGroupProfileZone(Operator operatorDM) {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription(operatorDM.getTestCaseTitle()));
            lifecycle.updateTestCase(testResult -> testResult.setName(operatorDM.getTestCaseTitle()));

            loginPage.login(driver
                    , screeningGeneralConfigsProps.getProperty(GeneralConstants.VALID_ADMIN_MAIL)
                    , screeningGeneralConfigsProps.getProperty(GeneralConstants.VALID_ADMIN_PASSWORD)

            );

            Group group = createOperator(driver);
            Operator operator = group.getGroupMembers().get(0);

            operator.setPermissionName(operatorDM.getPermissionName());
            operator.setProfileName(group.getProfile().getName());

            removeFunctionAccess(operator);

            Profile secondProfile = Profile.getRandom("Test-Profile-SWS");
            Group secondGroup = Group.getRandom(secondProfile, Collections.emptyList());
            secondGroup.setGroupMembers(Collections.singletonList(operator));
            Allure.step("Create second zone .");
            Allure.step("Zone test Data = " + secondGroup.getProfile().getZone());
            Assert.assertTrue(zoneResource.createZone(driver, secondGroup.getProfile().getZone()), String.format("Can't create group %s", secondGroup.getProfile().getZone().getDisplayName()));

            Allure.step("Create second profile .");
            Allure.step("Profile test Data = " + secondProfile);
            Assert.assertTrue(profileResource.createProfile(driver, secondProfile), String.format("Can't create profile %s", secondProfile.getName()));

            Allure.step("Create second group.");
            Allure.step("Group test Data = " + secondGroup);
            Assert.assertTrue(groupControl.createGroup(driver, secondGroup), String.format("Can't create group %s", secondGroup.getName()));

            commonAction.logout(driver);
            loginPage.login(driver, operator.getLoginName(), operator.getPassword());


            EnList enList = prepareTestData();
            property.updateKeyValue(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME, "profile.name", secondProfile.getName());
            enList.getListSet().setOwner(secondGroup.getName());
            enList.setZoneName(secondGroup.getProfile().getZone().getDisplayName());
            createListSet(driver, enList, secondProfile.getName());
            createNewEntry(driver, enList);

            scanFile(driver, enList, operatorDM);
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            driver.navigate().refresh();
            commonAction.logout(driver);
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Step("Create operator")
    public Group createOperator(RemoteWebDriver driver) throws Exception {

        Profile profile = Profile.getRandom("Test-Profile-SWS");
        Group group = Group.getRandom(profile, Collections.emptyList());

        Operator operator = Operator.getRandom(group.getProfile().getZone());
        Allure.step("Operator test data = " + operator.toString());

        group.setGroupMembers(Collections.singletonList(operator));
        Allure.step("Group test data = " + group.toString());

        Allure.step("Create new zone .");
        Allure.step("Zone test Data = " + group.getProfile().getZone());
        Assert.assertTrue(zoneResource.createZone(driver, group.getProfile().getZone()), String.format("Can't create group %s", group.getProfile().getZone().getDisplayName()));

        Allure.step("Create first profile .");
        Allure.step("Profile test Data = " + group.getProfile());
        Assert.assertTrue(profileResource.createProfile(driver, group.getProfile()), String.format("Can't create profile %s", group.getProfile().getName()));

        Allure.step("Create new operator.");
        Allure.step("Operator test Data = " + operator);
        Assert.assertTrue(operatorControl.createOperator(driver, operator), String.format("Can't create operator %s", operator.getLoginName()));

        Allure.step("Create first group.");
        Allure.step("Group test Data = " + group);
        Assert.assertTrue(groupControl.createGroup(driver, group), String.format("Can't create group %s", group.getName()));
        return group;
    }

    @Step("Remove function access")
    public List<Profile> removeFunctionAccess(Operator operator) throws SQLException {
        Allure.step(String.format("Connect to database to remove Link between profile = %s and Permission %s"
                , operator.getProfileName(), operator.getPermissionName()));
        List<Profile> profileFromDB = adminServicesDelegate.getProfileRight(operator.getProfileName(), operator.getPermissionName());
        Assert.assertTrue(profileFromDB.size() > 0, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        Allure.step(String.format("Delete function permission '%s' from DataBase", operator.getPermissionName()));
        Assert.assertTrue(adminServicesDelegate.removeProfileRightLink(
                String.valueOf(profileFromDB.get(0).getGrantId()),
                String.valueOf(profileFromDB.get(0).getId())) > 0, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
        return profileFromDB;
    }

    @Step("Create list set")
    public void createListSet(RemoteWebDriver driver, EnList list, String profileName) throws Exception {
        Allure.step("Connect to Database and Check if User Profile = " + profileName + " linked to any list set.");
        Allure.step("Number of effected rows = " + screeningServicesDelegate.deleteProfileFromListSet(profileName));

        Assert.assertTrue(blackListControl.create_Black_List(driver, list)
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new black list.");

        Assert.assertTrue(swiftManagerControl.CreateSwiftTemplate(driver, list.getListSet().getSwiftTemplate(), list.getZoneName())
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while create new swift template.");

        Assert.assertTrue(listSetControl.create_list_set(driver, list).contains(list.getExpectedMessage())
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new list set.");

        Allure.step("Link black list to list set.");
        Assert.assertTrue(listSetControl.link_black_list_to_list_set(driver, list).contains(list.getExpectedMessage())
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while add black list to listSet");

        Assert.assertTrue(listSetControl.select_profiles(driver, profileName).contains(list.getExpectedMessage())
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while select profiles in Profiles-ListSet Association.");

        Assert.assertTrue(screeningServicesDelegate.checkIfListSetLinkedToProfile(profileName)
                , GeneralConstants.DB_ERROR_MSG + " while check if listSet linked to profile.");
    }

    @Step("Create new entry")
    public void createNewEntry(RemoteWebDriver driver, EnList list) throws Exception {
        listExplorerControl.search(driver, list);

        Assert.assertTrue(listExplorerControl.create_entry_with_type_and_names(driver, list.getEntry().get(0))
                , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while Creating new Entry." + list.getEntry().get(0).toString());

    }

    @Step("Scan file")
    public void scanFile(RemoteWebDriver driver, EnList enList, Operator operator) throws Exception {
        String rjeSampleFile = System.getProperty("user.dir") + property.fromFile(GeneralConstants.SCREENING_TEST_DATA_CONFIG_FILE_PATH).getProperty("RJESwiftSampleFile");
        String rjeNewFile = System.getProperty("user.dir") + property.fromFile(GeneralConstants.SCREENING_TEST_DATA_CONFIG_FILE_PATH).getProperty("RJESwiftFile");
        String entryName = enList.getEntry().get(0).getName() + ", " + enList.getEntry().get(0).getFirstName();
        fileScanControl.edit_rje_file(rjeSampleFile, rjeNewFile, entryName);

        ObjectMapper mapper = new ObjectMapper();
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("blockDetection");
        FileScan fileScan = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), FileScan.class);

        fileScan.setEnList(enList);
        fileScan.setFilePath(GeneralConstants.RJE_SWIFT_FILE_PATH);
        fileScan.setFormat("Payment");

        Navigation.SCAN_MANAGER.navigate(driver);
        Assert.assertEquals(fileScanControl.scan_file(driver, fileScan).split("\\[")[0], "File sent to the server for processing with id ", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        Assert.assertEquals(resultManagerControl.check_scanned_file_result(driver), "SUCCEEDED", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        String detectionID = resultManagerControl.get_detection_id(driver);
        Assert.assertNotNull(detectionID, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        Assert.assertEquals(detectionManagerControl.search_by_id(driver, detectionID), "REPNEW", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        Assert.assertEquals(detectionManagerControl.check_permission(driver, operator.getFunctionToCheck(), detectionID, "Test Message"), "1 detection(s) successfully modified!", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


    }


    @DataProvider(name = "profileTestData")
    public Object[][] prepareProfileTestData() throws IOException {
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("checkProfilePermissions");
        DataResource resource =
                new InputStreamResource(new FileInputStream(System.getProperty("user.dir") + csvDataFilePath),
                        Profile.class, "json");
        SeLionDataProvider dataProvider =
                DataProviderFactory.getDataProvider(resource);
        return dataProvider.getAllData();

    }

    @DataProvider(name = "operatorTestData")
    public Object[][] prepareOperatorTestData() throws IOException {
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("checkOperatorPermissions");
        DataResource resource =
                new InputStreamResource(new FileInputStream(System.getProperty("user.dir") + csvDataFilePath),
                        Operator.class, "json");
        SeLionDataProvider dataProvider =
                DataProviderFactory.getDataProvider(resource);
        return dataProvider.getAllData();

    }

    @DataProvider(name = "OpWithSecondProfileGroupTestData")
    public Object[][] prepareOpWithSecondProfileGroupTestData() throws IOException {
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("checkPermissionWhenAssignSecGroupAndProfile");
        DataResource resource =
                new InputStreamResource(new FileInputStream(System.getProperty("user.dir") + csvDataFilePath),
                        Operator.class, "json");
        SeLionDataProvider dataProvider =
                DataProviderFactory.getDataProvider(resource);
        return dataProvider.getAllData();

    }

    @DataProvider(name = "OpWithSecondProfileGroupZoneTestData")
    public Object[][] prepareOpWithSecondProfileGroupZoneTestData() throws IOException {
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("checkPermissionWhenAssignSecGroupProfileZone");
        DataResource resource =
                new InputStreamResource(new FileInputStream(System.getProperty("user.dir") + csvDataFilePath),
                        Operator.class, "json");
        SeLionDataProvider dataProvider =
                DataProviderFactory.getDataProvider(resource);
        return dataProvider.getAllData();

    }

    public EnList prepareTestData() {
        int random = randomizer.getInt();
        ObjectMapper mapper = new ObjectMapper();
        EnList list = null;
        try {
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("addNewEntry");
            list = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), EnList.class);
            list.setName(String.format(list.getName(), random));
            for (int i = 0; i < list.getEntry().size(); i++) {
                list.getEntry().get(i).setName(String.format(list.getEntry().get(i).getName(), random));
                list.getEntry().get(i).setFirstName(String.format(list.getEntry().get(i).getFirstName(), random));
            }
            list.getListSet().setName(String.format(list.getListSet().getName(), random));
            list.getListSet().getSwiftTemplate().setTemplateName(String.format(list.getListSet().getSwiftTemplate().getTemplateName(), random));

            System.out.println(list);
        } catch (IOException e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
        Allure.step("Test Data = " + list.toString());
        return list;
    }

    @AfterMethod()
    public void logout() {
        try {
            if (commonAction.isUserLogged(driver))
                commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

}
