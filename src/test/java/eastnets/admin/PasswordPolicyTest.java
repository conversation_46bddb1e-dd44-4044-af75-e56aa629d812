package eastnets.admin;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paypal.selion.platform.dataprovider.DataProviderFactory;
import com.paypal.selion.platform.dataprovider.DataResource;
import com.paypal.selion.platform.dataprovider.SeLionDataProvider;
import com.paypal.selion.platform.dataprovider.impl.InputStreamResource;
import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import core.gui.Controls;
import eastnets.admin.control.OperatorControl;
import eastnets.admin.control.UserProfileControl;
import eastnets.admin.entity.*;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import org.testng.Assert;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeGroups;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Collections;

public class PasswordPolicyTest extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final OperatorControl operatorControl = new OperatorControl();
    private final UserProfileControl userProfileControl = new UserProfileControl();
    private final CommonAction commonAction = new CommonAction();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final Controls controls = new Controls();

    private RemoteWebDriver driver;

    static Operator operator;
    static PasswordPolicy passwordPolicy;
    String password = screeningGeneralConfigsProps.getProperty(GeneralConstants.VALID_ADMIN_PASSWORD);
    String email = screeningGeneralConfigsProps.getProperty(GeneralConstants.VALID_ADMIN_MAIL);

    @BeforeClass()
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeGroups(groups = "changePolicy", description = "Pre-Condition : Change Password Policy")
    public void changePasswordPolicy() {
        try {
            screeningServicesDelegate.resetOperatorPasswordByManager(email);
            screeningServicesDelegate.resetPasswordPolicyData();
            loginPage.login(driver, email, password);

            Profile profile = Common.PROFILE.ADMIN_FULL_RIGHT.getProfile();
            Group group = Common.GROUP.FULL_RIGHT_GROUP_01.get();

            operator = Operator.getRandom(group.getProfile().getZone());
            Allure.step("Operator test data = " + operator.toString());

            group.setGroupMembers(Collections.singletonList(operator));
            Allure.step("Group test data = " + group.toString());

            Allure.step("Create new operator.");
            Allure.step("Operator test Data = " + operator);
            Assert.assertTrue(operatorControl.createOperator(driver, operator), String.format("Can't create operator %s", operator.getLoginName()));

            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("changePasswordPolicy");
            passwordPolicy = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), PasswordPolicy.class);
            operatorControl.changePasswordPolicy(driver, passwordPolicy);
            commonAction.logout(driver);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @Test(priority = 0, enabled = true, dataProvider = "changePasswordTestData", groups = "changePolicy")
    @Owner("Sara Abdellatif")
    public void changePassword(PasswordPolicy passwordPolicy) {
        try {
            AllureLifecycle currentLifecycle = Allure.getLifecycle();
            currentLifecycle.updateTestCase(testResult -> testResult.setDescription(passwordPolicy.getTestCaseTitle()));
            currentLifecycle.updateTestCase(testResult -> testResult.setName(passwordPolicy.getTestCaseTitle()));

            controls.clearBrowserCache(driver);
            driver.navigate().refresh();
            loginPage.clickChangePasswordLinkText(driver);
            String expectedResult = reformatExpectedResults(passwordPolicy.getExpectedMessage());
            Allure.step(String.format("Expected results : %s", expectedResult));
            Assert.assertEquals(loginPage.changePassword(driver, operator, passwordPolicy.getNewPassword())
                    , expectedResult
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            screeningServicesDelegate.resetOperatorPasswordByManager(email);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 1, enabled = true, dataProvider = "PasswordPolicyTestData", groups = "regression")
    @Owner("Tarek Allam")
    public void passwordPolicy(PasswordPolicy passwordPolicy) {
        try {
            AllureLifecycle currentLifecycle = Allure.getLifecycle();
            currentLifecycle.updateTestCase(testResult -> testResult.setDescription(passwordPolicy.getTestCaseTitle()));
            currentLifecycle.updateTestCase(testResult -> testResult.setName(passwordPolicy.getTestCaseTitle()));

            screeningServicesDelegate.resetPasswordPolicyData();
            screeningServicesDelegate.resetOperatorPasswordByManager(email);

            CommonTestMethods commonTestMethods = new CommonTestMethods();
            commonTestMethods.restartTomcat();

            loginPage.login(driver, email, password);

            operatorControl.changePasswordPolicy(driver, passwordPolicy);

            Assert.assertEquals(userProfileControl.Change_Password(driver, password, passwordPolicy.getNewPassword())
                    , passwordPolicy.getExpectedMessage(), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

     /*       if (passwordPolicy.getExpectedMessage().contains("The paswword has been successfully changed"))
            {
                password = (passwordPolicy.getNewPassword());
            }*/

            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 2, enabled = true, dataProvider = "PasswordPolicyListTD", groups = "regression")
    @Owner("Tarek Allam")
    public void passwordPolicyList(PasswordPolicy passwordPolicy) {
        try {
            AllureLifecycle currentLifecycle = Allure.getLifecycle();
            currentLifecycle.updateTestCase(testResult -> testResult.setDescription(passwordPolicy.getTestCaseTitle()));
            currentLifecycle.updateTestCase(testResult -> testResult.setName(passwordPolicy.getTestCaseTitle()));

            //CommonTestMethods.restartTomcat();
            screeningServicesDelegate.resetPasswordPolicyData();
            screeningServicesDelegate.resetOperatorPasswordByManager(email);
            loginPage.login(driver, email, password);

            operatorControl.changePasswordPolicy(driver, passwordPolicy);
            for (int i = 0; i < passwordPolicy.getNewPasswordList().length; i++) {

                Assert.assertEquals(userProfileControl.Change_Password(driver, password, passwordPolicy.getNewPasswordList()[i])
                        , passwordPolicy.getExpectedMessage(), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

                screeningServicesDelegate.resetOperatorPasswordByManager(email);
            }

            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @AfterMethod(description = "Post-Condition : Reset Password Policy data.")
    public void resetPasswordPolicy() {
        try {
            screeningServicesDelegate.resetPasswordPolicyData();
            screeningServicesDelegate.resetOperatorPasswordByManager(email);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    public static String reformatExpectedResults(String message) {
        if (message.contains("#USER_NAME#"))
            message = message.replace("#USER_NAME#", operator.getLoginName());
        if (message.contains("#HISTORY_COUNT#"))
            message = message.replace("#HISTORY_COUNT#", passwordPolicy.getHistoryCount());
        if (message.contains("#UPPER#"))
            message = message.replace("#UPPER#", passwordPolicy.getMinimumUpperCaseCount());
        if (message.contains("#LOWER#"))
            message = message.replace("#LOWER#", passwordPolicy.getMinimumLowerCaseCount());
        if (message.contains("#DIGITS#"))
            message = message.replace("#DIGITS#", passwordPolicy.getMinimumDigitsCount());
        if (message.contains("#SPECIAL_CHARACTERS#"))
            message = message.replace("#SPECIAL_CHARACTERS#", passwordPolicy.getMinimumSpecialCharactersCount());
        if (message.contains("#LENGTH#"))
            message = message.replace("#LENGTH#", passwordPolicy.getMinimumLength());
        return message;
    }

    @DataProvider(name = "changePasswordTestData")
    public Object[][] prepareOpWithSecondProfileGroupTestData() throws IOException {
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("changePassword");
        DataResource resource =
                new InputStreamResource(new FileInputStream(System.getProperty("user.dir") + csvDataFilePath),
                        PasswordPolicy.class, "json");
        SeLionDataProvider dataProvider =
                DataProviderFactory.getDataProvider(resource);
        return dataProvider.getAllData();

    }

    @DataProvider(name = "PasswordPolicyTestData")
    public Object[][] preparePasswordPolicyTestData() throws IOException {
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("passwordPolicy");
        DataResource resource =
                new InputStreamResource(new FileInputStream(System.getProperty("user.dir") + csvDataFilePath),
                        PasswordPolicy.class, "json");
        SeLionDataProvider dataProvider =
                DataProviderFactory.getDataProvider(resource);
        return dataProvider.getAllData();
    }

    @DataProvider(name = "PasswordPolicyListTD")
    public Object[][] preparePasswordPolicyListTD() throws IOException {
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("passwordPolicyList");
        DataResource resource =
                new InputStreamResource(new FileInputStream(System.getProperty("user.dir") + csvDataFilePath),
                        PasswordPolicy.class, "json");
        SeLionDataProvider dataProvider =
                DataProviderFactory.getDataProvider(resource);
        return dataProvider.getAllData();
    }
}