package eastnets.admin;

import core.BaseTest;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.control.ArchiveViewerControl;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;


public class ArchiveViewerTest extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final ArchiveViewerControl archiveViewerControl = new ArchiveViewerControl();
    private final CommonAction commonAction = new CommonAction();
    private RemoteWebDriver driver;

    @BeforeClass()
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod()
    public void login() {
        try {
            loginPage.login(driver
                    , screeningGeneralConfigsProps.getProperty(GeneralConstants.VALID_ADMIN_MAIL)
                    , screeningGeneralConfigsProps.getProperty(GeneralConstants.VALID_ADMIN_PASSWORD)
            );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test()
    @Owner("Sara Abdellatif")
    @Tag("ArchiveViewer")
    @Tag("Regression")
    public void browseArchiveFile() {
        try {
            AllureLifecycle currentLifecycle = Allure.getLifecycle();
            currentLifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that Archive Event Details are appearing properly"));
            currentLifecycle.updateTestCase(testResult -> testResult.setName("Verify that Archive Event Details are appearing properly"));

            Assert.assertTrue(archiveViewerControl.browseArchiveFile(driver, GeneralConstants.ARCHIVE_FILE_PATH), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Allure.step("Assertion Passed Successfully.");

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod()
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

}
