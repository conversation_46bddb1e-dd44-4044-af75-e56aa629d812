package eastnets.admin;

import com.paypal.selion.platform.dataprovider.DataProviderFactory;
import com.paypal.selion.platform.dataprovider.DataResource;
import com.paypal.selion.platform.dataprovider.SeLionDataProvider;
import com.paypal.selion.platform.dataprovider.impl.InputStreamResource;
import core.BaseTest;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.control.AuditControl;
import eastnets.admin.control.GroupControl;
import eastnets.admin.control.OperatorControl;
import eastnets.admin.entity.*;
import eastnets.admin.gui.GroupManager;
import eastnets.admin.gui.OperatorManager;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.common.gui.Navigation;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import io.qameta.allure.Step;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class AuditManagerTest extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final AuditControl auditControl = new AuditControl();
    private final OperatorControl operatorControl = new OperatorControl();
    private final GroupControl groupControl = new GroupControl();
    private final CommonAction commonAction = new CommonAction();
    private final OperatorManager operatorManager = new OperatorManager();
    private final GroupManager groupManager = new GroupManager();

    private RemoteWebDriver driver;

    @BeforeClass()
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod()
    public void login() {
        try {
            loginPage.login(driver
                    , screeningGeneralConfigsProps.getProperty(GeneralConstants.VALID_ADMIN_MAIL)
                    , screeningGeneralConfigsProps.getProperty(GeneralConstants.VALID_ADMIN_PASSWORD)
            );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test()
    @Owner("Sara Abdellatif")
    @Tag("AuditManager")
    @Tag("Regression")
    public void checkAuditManager() {
        try {
            AllureLifecycle currentLifecycle = Allure.getLifecycle();
            currentLifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that an event recording audit is appearing properly for the removed Groups/Users"));
            currentLifecycle.updateTestCase(testResult -> testResult.setName("Verify that an event recording audit is appearing properly for the removed Groups/Users"));

            SoftAssert softAssert = new SoftAssert();

            Group group = createOperatorAndGroup(driver);
            deleteOperatorAndGroup(driver, group);

            auditControl.navigateToAuditManager(driver);
            List<Audit> frontendResults = auditControl.searchAuditManager(driver);

            List<Audit> expectedResults = auditTestData();

            Allure.step("Asserting on data in result view");
            for (int i = 0; i < 4; i++) {
                expectedResults.get(i).setReturnedItem(expectedResults.get(i).getReturnedItem().replace("%OperatorName%", group.getGroupMembers().get(0).getLoginName()));
                expectedResults.get(i).setReturnedItem(expectedResults.get(i).getReturnedItem().replace("%GroupName%", group.getName()));
                softAssert.assertEquals(frontendResults.get(i).getAction(), expectedResults.get(i).getAction(), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG + " - record number " + i);
                softAssert.assertEquals(frontendResults.get(i).getModifiedField(), expectedResults.get(i).getModifiedField(), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG + " - record number " + i);
                softAssert.assertEquals(frontendResults.get(i).getDescription(), expectedResults.get(i).getDescription(), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG + " - record number " + i);
                softAssert.assertEquals(frontendResults.get(i).getReturnedItem(), expectedResults.get(i).getReturnedItem(), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG + " - record number " + i);
            }
            softAssert.assertAll();
            Allure.step("Assertion Passed Successfully.");

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod()
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Step("Create operator and group.")
    public Group createOperatorAndGroup(RemoteWebDriver driver) throws Exception {

        Profile profile = Common.PROFILE.FULL_RIGHT_001.getProfile();
        Group group = Group.getRandom(profile, Collections.emptyList());

        Operator operator = Operator.getRandom(group.getProfile().getZone());
        Allure.step("Operator test data = " + operator.toString());

        group.setGroupMembers(Collections.singletonList(operator));
        Allure.step("Group test data = " + group.toString());

        Allure.step("Create new operator.");
        Allure.step("Operator test Data = " + operator);
        Assert.assertTrue(operatorControl.createOperator(driver, operator), String.format("Can't create operator %s", operator.getLoginName()));

        Allure.step("Create first group.");
        Allure.step("Group test Data = " + group);
        Assert.assertTrue(groupControl.createGroup(driver, group), String.format("Can't create group %s", group.getName()));
        return group;
    }

    @Step("Delete operator and group.")
    public void deleteOperatorAndGroup(RemoteWebDriver driver, Group group) throws Exception {

        Allure.step(String.format("Remove operator with login name = %s.", group.getGroupMembers().get(0).getLoginName()));
        Navigation.OPERATOR.navigate(driver);
        Assert.assertFalse(operatorManager.removeOperator(driver, group.getGroupMembers().get(0)), String.format("Can't remove operator %s", group.getGroupMembers().get(0).getLoginName()));

        Allure.step(String.format("Remove group with name = %s.", group.getName()));
        Navigation.GROUP.navigate(driver);
        Assert.assertFalse(groupManager.removeGroup(driver, group), String.format("Can't remove group %s", group.getName()));
    }

    @Step("Get audit test data.")
    public List<Audit> auditTestData() throws IOException {
        List<Audit> audits = new ArrayList<>();
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("checkAuditManager");
        DataResource resource =
                new InputStreamResource(new FileInputStream(System.getProperty("user.dir") + csvDataFilePath),
                        Audit.class, "json");
        SeLionDataProvider dataProvider =
                DataProviderFactory.getDataProvider(resource);
        Object[][] objects = dataProvider.getAllData();
        for (int i = 0; i < objects.length; i++) {
            audits.add((Audit) objects[i][0]);
        }
        return audits;
    }

}
