package eastnets.admin;

import com.paypal.selion.platform.dataprovider.DataProviderFactory;
import com.paypal.selion.platform.dataprovider.DataResource;
import com.paypal.selion.platform.dataprovider.SeLionDataProvider;
import com.paypal.selion.platform.dataprovider.impl.InputStreamResource;
import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import core.util.TextFilesHandler;
import core.util.Wait;
import eastnets.admin.boundary.ProfileResource;
import eastnets.admin.control.ReportControl;
import eastnets.admin.entity.*;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.common.gui.Navigation;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import io.qameta.allure.Step;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;

import java.io.FileInputStream;
import java.io.IOException;

public class ReportsTest extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final ReportControl reportControl = new ReportControl();
    private final CommonAction commonAction = new CommonAction();
    private final ProfileResource profileResource = new ProfileResource();
    private final TextFilesHandler textFilesHandler = new TextFilesHandler();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final Wait wait = new Wait();

    private RemoteWebDriver driver;

    public Operator operator;
    public Profile profile1;
    public Profile profile2;
    public String permissionName;

    @BeforeClass()
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(dataProvider = "reportTestData")
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Tag("Admin Reports")
    public void exportReport(Report report) {

        try {

            AllureLifecycle currentLifecycle = Allure.getLifecycle();
            Report finalReport = report;
            currentLifecycle.updateTestCase(testResult -> testResult.setDescription(finalReport.getTestCaseTitle()));
            Report finalReport1 = report;
            currentLifecycle.updateTestCase(testResult -> testResult.setName(finalReport1.getTestCaseTitle()));

            SoftAssert softAssert = new SoftAssert();
            clearDownloadedFiles();
            report = performAction(driver, report);

            loginPage.login(driver
                    , screeningGeneralConfigsProps.getProperty(GeneralConstants.VALID_ADMIN_MAIL)
                    , screeningGeneralConfigsProps.getProperty(GeneralConstants.VALID_ADMIN_PASSWORD)

            );
            Navigation.REPORT.navigate(driver);

            Assert.assertTrue(reportControl.createNewReport(driver, report), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(reportControl.getReportStatus(driver), "Done", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertTrue(reportControl.exportReport(driver, report.getTargetFileName(), dockerServerIp), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            String pdfContent = textFilesHandler.get_pdf_file_content(driver, report.getTargetFileName() + ".pdf", browserDefaultDownloadPath, dockerServerIp);
            String[] validationPatterns = report.getValidationString().split("\\|");
            for (int i = 0; i < validationPatterns.length; i++) {
                softAssert.assertTrue(pdfContent.contains(validationPatterns[i]), String.format("Validation String '%s' not exist in downloaded pdf", validationPatterns[i]));
            }

            commonAction.logout(driver);
            softAssert.assertAll();

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @Step("Perform action.")
    public Report performAction(RemoteWebDriver driver, Report report) throws Exception {
        if (report.getAction() != null) {
            switch (report.getAction()) {
                case "Login_Logout":
                    operator = Common.OPERATOR.FULL_RIGHT_3.getOperator();
                    loginPage.login(driver
                            , operator.getLoginName()
                            , operator.getPassword()

                    );
                    commonAction.isUserLogged(driver);
                    commonAction.logout(driver);
                    report.setUserName(operator.getLoginName());
                    report.setValidationString(report.getValidationString().replaceAll("#UserName#", report.getUserName()));
                    break;

                case "deleteOperator":
                    loginPage.login(driver
                            , screeningGeneralConfigsProps.getProperty(GeneralConstants.VALID_ADMIN_MAIL)
                            , screeningGeneralConfigsProps.getProperty(GeneralConstants.VALID_ADMIN_PASSWORD)

                    );
                    operator = commonTestMethods.createOperator(driver);
                    commonTestMethods.deleteOperator(driver, operator);
                    commonAction.logout(driver);
                    String loginName = operator.getLoginName().split("\\-")[2];
                    report.setValidationString(report.getValidationString().replaceAll("#DeletedOperatorName#", loginName));
                    break;

                case "InvalidLogin":
                    operator = Common.OPERATOR.FULL_RIGHT_1.getOperator();
                    loginPage.login(driver
                            , operator.getLoginName()
                            , operator.getPassword() + 11

                    );
                    wait.time(Wait.ONE_SECOND);
                    report.setUserName(operator.getLoginName());
                    report.setValidationString(report.getValidationString().replaceAll("#UserName#", report.getUserName()));
                    break;

                case "removeAccess":
                    profile1 = Profile.getRandom("ProfileReport");
                    profile2 = Profile.getRandom("ProfileReport");
                    permissionName = "Allow to add attachments to detection";
                    loginPage.login(driver
                            , screeningGeneralConfigsProps.getProperty(GeneralConstants.VALID_ADMIN_MAIL)
                            , screeningGeneralConfigsProps.getProperty(GeneralConstants.VALID_ADMIN_PASSWORD)

                    );
                    Allure.step("Create profile1 .");
                    Allure.step("Profile test Data = " + profile1);
                    Assert.assertTrue(profileResource.createProfileAndZoneIfNotExist(driver, profile1), String.format("Can't create profile %s", profile1.getName()));

                    Allure.step("Create profile2 .");
                    Allure.step("Profile test Data = " + profile2);
                    Assert.assertTrue(profileResource.createProfileAndZoneIfNotExist(driver, profile2), String.format("Can't create profile %s", profile2.getName()));

                    commonTestMethods.removeFunctionAccess(profile1, permissionName);
                    report.setProfile1(profile1.getName());
                    report.setProfile2(profile2.getName());
                    commonAction.logout(driver);
                    report.setValidationString(report.getValidationString().replace("#Profile1#", report.getProfile1()));
                    report.setValidationString(report.getValidationString().replace("#Profile2#", report.getProfile2()));
                    break;

                case "AddGoodGuy":
                    Group group = Common.GROUP.FULL_RIGHT_GROUP_03.get();
                    operator = Common.OPERATOR.FULL_RIGHT_3.getOperator();
                    loginPage.login(driver
                            , operator.getLoginName()
                            , operator.getPassword()
                    );
                    EnList enList = commonTestMethods.getEnListData();
                    enList.getListSet().setOwner(group.getName());
                    enList.setZoneName(group.getProfile().getZone().getDisplayName());
                    commonTestMethods.createListSet(driver, enList, group.getProfile().getName());
                    commonTestMethods.createNewEntryWithTypeAndNames(driver, enList);
                    commonTestMethods.createGoodGuy(driver, enList);
                    commonAction.logout(driver);
                    report.setUserName(operator.getLoginName());
                    String entryName = enList.getEntry().get(0).getName() + ", " + enList.getEntry().get(0).getFirstName();
                    report.setValidationString(report.getValidationString().replace("#EntryName#", entryName));
                    break;
            }
        }
        return report;
    }

    @DataProvider(name = "reportTestData")
    public Object[][] reportTestData() throws IOException {
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("exportReport");
        DataResource resource =
                new InputStreamResource(new FileInputStream(System.getProperty("user.dir") + csvDataFilePath),
                        Report.class, "json");
        SeLionDataProvider dataProvider =
                DataProviderFactory.getDataProvider(resource);
        return dataProvider.getAllData();

    }

}
