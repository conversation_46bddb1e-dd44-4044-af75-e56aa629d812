package eastnets.screening;

import com.fasterxml.jackson.databind.ObjectMapper;
import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.AdvancedSettingsControls.BusinessLogicControl;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FileScan;
import eastnets.screening.entity.GeneralSettings;
import eastnets.screening.gui.scanManager.resultManager.ResultManager;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.io.File;


public class SwiftImprovementTests extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final BusinessLogicControl businessLogicControl = new BusinessLogicControl();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final ResultManager resultManager = new ResultManager();

    private RemoteWebDriver driver;

    GeneralSettings generalSettings;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod()
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_7.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_7.getOperator().getPassword()
                   );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeClass()
    public void updateBusinessLogicOptions() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_7.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_7.getOperator().getPassword()
                   );

            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("generalSettings");
            generalSettings = mapper.readValue(new File(System.getProperty("user.dir")
                    + csvDataFilePath), GeneralSettings.class);


            Assert.assertEquals(businessLogicControl.setGeneralSettingsDetails(driver, generalSettings)
                    , "Operation completed successfully! The new configuration will take place after 10 minutes maximum."
                    , "Failed to set general settings details.");

            commonTestMethods.restartNewArch();

            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @Test(priority = 0, enabled = true, groups = {"regression"})
    @Owner("Sarah Abdelatif")
    public void SwiftImprovement_01() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription(" Verify that scanning a SWIFT message with field 50F is giving a violation for Line 1 when the black list is under Individual category"));
            lifecycle.updateTestCase(testResult -> testResult.setName("SwiftImprovement_01"));


            EnList enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.COMMON_TEST_ZONE_007.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, enlist, Common.PROFILE.FULL_RIGHT_007.getProfile().getName());


            enlist.getEntry().get(0).setName("Samer Al Askari");
            enlist.getEntry().get(0).setGender("Male");
            enlist.getEntry().get(0).setFirstName(null);

            enlist.getEntry().get(1).setName("The Group");
            enlist.getEntry().get(1).setType("Group");
            enlist.getEntry().get(1).setFirstName(null);

            commonTestMethods.createNewEntryWithDetailsInfo(driver, enlist, enlist.getEntry().get(0));
            commonTestMethods.createNewEntryWithDetailsInfo(driver, enlist, enlist.getEntry().get(1));


            FileScan fileScan = commonTestMethods.getFileScanData(GeneralConstants.SWIFT_01_SCAN_FILE_PATH);
            String detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            String highlightedText = "1/Samer Al Askari";
            String data = "Samer Al Askari";
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, data)
                    , highlightedText
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            fileScan.setFilePath(GeneralConstants.SWIFT_02_SCAN_FILE_PATH);
            detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            highlightedText = "1/The Group";
            data = "The Group";
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, data)
                    , highlightedText
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 1, enabled = true, groups = {"regression"})
    @Owner("Sarah Abdelatif")
    public void SwiftImprovement_02() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("* Verify that scanning a SWIFT message with field 50F is NOT giving a violation for Line 1 when the black list is under Country category\n" +
                    "* Verify that scanning a SWIFT message with field 59F is NOT giving a violation for Line 1 when the black list is under Country category"));
            lifecycle.updateTestCase(testResult -> testResult.setName("SwiftImprovement_02"));


            EnList enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.COMMON_TEST_ZONE_007.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, enlist, Common.PROFILE.FULL_RIGHT_007.getProfile().getName());


            enlist.getEntry().get(0).setName("Jordan");
            enlist.getEntry().get(0).setType("Country");
            enlist.getEntry().get(0).setFirstName(null);

            enlist.getEntry().get(1).setName("Korea");
            enlist.getEntry().get(1).setType("Country");
            enlist.getEntry().get(1).setFirstName(null);

            commonTestMethods.createNewEntryWithDetailsInfo(driver, enlist, enlist.getEntry().get(0));
            commonTestMethods.createNewEntryWithDetailsInfo(driver, enlist, enlist.getEntry().get(1));


            FileScan fileScan = commonTestMethods.getFileScanData(GeneralConstants.SWIFT_03_SCAN_FILE_PATH);
            fileScan.setResult("All records");
            String detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            detectionManagerControl.search_by_id(driver, detectionId);
            Assert.assertEquals(detectionManagerControl.get_first_listed_detection_status(driver), "CLEAN", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 2, enabled = true, groups = {"regression"})
    @Owner("Sarah Abdelatif")
    public void SwiftImprovement_03() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("* Verify that scanning a SWIFT message with field 50F is highlighting the violation for all Line 1 when the black list is under Individual category\n" +
                    "* Verify that scanning a SWIFT message with field 50F is highlighting the violation for all Line 1 when the black list is under Groups category"));
            lifecycle.updateTestCase(testResult -> testResult.setName("SwiftImprovement_03"));


            EnList enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.COMMON_TEST_ZONE_007.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, enlist, Common.PROFILE.FULL_RIGHT_007.getProfile().getName());


            enlist.getEntry().get(0).setName("Samer Al Askari");
            enlist.getEntry().get(0).setFirstName(null);

            enlist.getEntry().get(1).setName("Hamad al Shammari");
            enlist.getEntry().get(1).setType("Group");
            enlist.getEntry().get(1).setFirstName(null);

            commonTestMethods.createNewEntryWithDetailsInfo(driver, enlist, enlist.getEntry().get(0));
            commonTestMethods.createNewEntryWithDetailsInfo(driver, enlist, enlist.getEntry().get(1));


            FileScan fileScan = commonTestMethods.getFileScanData(GeneralConstants.SWIFT_04_SCAN_FILE_PATH);
            fileScan.setRank("50");
            String detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            String highlightedText = "1/Samer al Askari\n" +
                    "1/Bosh notir\n" +
                    "1/Kafyar Hammoud";
            String data = "Samer Al Askari";
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, data)
                    , highlightedText
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            fileScan.setFilePath(GeneralConstants.SWIFT_05_SCAN_FILE_PATH);
            detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            highlightedText = "1/Hamad al Shammari\n" +
                    "1/Bosh notir\n" +
                    "1/Kafyar Hammoud";
            data = "Hamad al Shammari";
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, data)
                    , highlightedText
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 3, enabled = true, groups = {"regression"})
    @Owner("Sarah Abdelatif")
    public void SwiftImprovement_04() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("* Verify that scanning a SWIFT message with field 50F is giving a violation for Line 2 when the black list is under Countries category\n" +
                    "* Verify that scanning a SWIFT message with field 50F is giving a violation for Line 3 when the black list is under Countries category\n" +
                    "* Verify that scanning a SWIFT message with field 50F is giving a violation for Line 5 when the black list is under Countries category"));
            lifecycle.updateTestCase(testResult -> testResult.setName("SwiftImprovement_04"));


            EnList enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.COMMON_TEST_ZONE_007.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, enlist, Common.PROFILE.FULL_RIGHT_007.getProfile().getName());


            enlist.getEntry().get(0).setName("Oman");
            enlist.getEntry().get(0).setFirstName(null);
            enlist.getEntry().get(0).setType("Country");
            commonTestMethods.createNewEntryWithDetailsInfo(driver, enlist, enlist.getEntry().get(0));

            enlist.getEntry().get(0).setName("Cairo");
            commonTestMethods.createNewEntryWithDetailsInfo(driver, enlist, enlist.getEntry().get(0));

            enlist.getEntry().get(0).setName("Korea");
            commonTestMethods.createNewEntryWithDetailsInfo(driver, enlist, enlist.getEntry().get(0));

            FileScan fileScan = commonTestMethods.getFileScanData(GeneralConstants.SWIFT_06_SCAN_FILE_PATH);
            fileScan.setRank("50");
            String detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            String highlightedText = "2/Oman";
            String data = "Oman";
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, data)
                    , highlightedText
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            highlightedText = "3/Cairo";
            data = "Cairo";
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, data)
                    , highlightedText
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            highlightedText = "5/Korea";
            data = "Korea";
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, data)
                    , highlightedText
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 4, enabled = true, groups = {"regression"})
    @Owner("Sarah Abdelatif")
    public void SwiftImprovement_05() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("* Verify that scanning a SWIFT message with field 50F is giving a violation for Line 6 when the black list is under Individual category\n" +
                    "* Verify that scanning a SWIFT message with field 50F is giving a violation for Line 6 when the black list is under Groups category\n" +
                    "* Verify that scanning a SWIFT message with field 50F is giving a violation for Line 6 when the black list is under Countries category"));
            lifecycle.updateTestCase(testResult -> testResult.setName("SwiftImprovement_05"));


            EnList enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.COMMON_TEST_ZONE_007.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, enlist, Common.PROFILE.FULL_RIGHT_007.getProfile().getName());


            enlist.getEntry().get(0).setName("NASSER");
            enlist.getEntry().get(0).setFirstName(null);
            enlist.getEntry().get(0).setBirthDate(null);
            commonTestMethods.createNewEntryWithDetailsInfo(driver, enlist, enlist.getEntry().get(0));

            enlist.getEntry().get(0).setName("ben laden");
            enlist.getEntry().get(0).setType("Group");
            commonTestMethods.createNewEntryWithDetailsInfo(driver, enlist, enlist.getEntry().get(0));

            enlist.getEntry().get(0).setName("FRANCE");
            enlist.getEntry().get(0).setType("Country");
            commonTestMethods.createNewEntryWithDetailsInfo(driver, enlist, enlist.getEntry().get(0));

            FileScan fileScan = commonTestMethods.getFileScanData(GeneralConstants.SWIFT_07_SCAN_FILE_PATH);
            fileScan.setRank("50");
            String detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            String highlightedText = "6/NASSER";
            String data = "NASSER";
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, data)
                    , highlightedText
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


            fileScan.setFilePath(GeneralConstants.SWIFT_08_SCAN_FILE_PATH);
            detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            highlightedText = "6/GROUP";
            data = "GROUP";
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, data)
                    , highlightedText
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            fileScan.setFilePath(GeneralConstants.SWIFT_09_SCAN_FILE_PATH);
            detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            highlightedText = "6/FRANCE";
            data = "FRANCE";
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, data)
                    , highlightedText
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 5, enabled = true, groups = {"regression"})
    @Owner("Sarah Abdelatif")
    public void SwiftImprovement_06() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("* Verify that scanning a SWIFT message with field 50F is giving a violation for Line 7 when the black list is under Vessels category\n" +
                    "* Verify that scanning a SWIFT message with field 50F is giving a violation for Line 7 when the black list is under Unknown category\n" +
                    "* Verify that scanning a SWIFT message with field 50F is giving a violation for Line 8 when the black list is under Individual category\n" +
                    "* Verify that scanning a SWIFT message with field 50F is giving a violation for Line 8 when the black list is under Groups category"));
            lifecycle.updateTestCase(testResult -> testResult.setName("SwiftImprovement_06"));


            EnList enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.COMMON_TEST_ZONE_007.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, enlist, Common.PROFILE.FULL_RIGHT_007.getProfile().getName());


            enlist.getEntry().get(0).setName("GOMA");
            enlist.getEntry().get(0).setType("Vessel");
            enlist.getEntry().get(0).setFirstName(null);
            commonTestMethods.createNewEntryWithDetailsInfo(driver, enlist, enlist.getEntry().get(0));

            enlist.getEntry().get(0).setName("NAPILOO");
            enlist.getEntry().get(0).setType("Unknown");
            commonTestMethods.createNewEntryWithDetailsInfo(driver, enlist, enlist.getEntry().get(0));

            enlist.getEntry().get(0).setName("NASSER");
            enlist.getEntry().get(0).setType("Individual");
            commonTestMethods.createNewEntryWithDetailsInfo(driver, enlist, enlist.getEntry().get(0));

            enlist.getEntry().get(0).setName("GROUP");
            enlist.getEntry().get(0).setType("Group");
            commonTestMethods.createNewEntryWithDetailsInfo(driver, enlist, enlist.getEntry().get(0));

            FileScan fileScan = commonTestMethods.getFileScanData(GeneralConstants.SWIFT_10_SCAN_FILE_PATH);
            fileScan.setRank("50");
            String detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            String highlightedText = "7/GOMA";
            String data = "GOMA";
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, data)
                    , highlightedText
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


            fileScan.setFilePath(GeneralConstants.SWIFT_11_SCAN_FILE_PATH);
            detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            highlightedText = "7/NAPILOO";
            data = "NAPILOO";
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, data)
                    , highlightedText
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            fileScan.setFilePath(GeneralConstants.SWIFT_12_SCAN_FILE_PATH);
            detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            highlightedText = "8/NASSER";
            data = "NASSER";
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, data)
                    , highlightedText
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            fileScan.setFilePath(GeneralConstants.SWIFT_13_SCAN_FILE_PATH);
            detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            highlightedText = "8/GROUP";
            data = "GROUP";
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, data)
                    , highlightedText
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 6, enabled = true, groups = {"regression"})
    @Owner("Sarah Abdelatif")
    public void SwiftImprovement_07() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("* Verify that scanning a SWIFT message with field 59F is giving a violation for Line 1 when the black list is under Individual category\n" +
                    "* Verify that scanning a SWIFT message with field 59F is giving a violation for Line 1 when the black list is under Groups category"));
            lifecycle.updateTestCase(testResult -> testResult.setName("SwiftImprovement_07"));

            EnList enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.COMMON_TEST_ZONE_007.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, enlist, Common.PROFILE.FULL_RIGHT_007.getProfile().getName());

            enlist.getEntry().get(0).setFirstName(null);
            enlist.getEntry().get(0).setName("Samerani");
            enlist.getEntry().get(0).setType("Individual");
            commonTestMethods.createNewEntryWithDetailsInfo(driver, enlist, enlist.getEntry().get(0));

            enlist.getEntry().get(0).setName("Group");
            enlist.getEntry().get(0).setType("Group");
            commonTestMethods.createNewEntryWithDetailsInfo(driver, enlist, enlist.getEntry().get(0));


            FileScan fileScan = commonTestMethods.getFileScanData(GeneralConstants.SWIFT_14_SCAN_FILE_PATH);
            fileScan.setRank("50");
            String detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            String highlightedText = "1/Samerani";
            String data = "Samerani";
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, data)
                    , highlightedText
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


            fileScan.setFilePath(GeneralConstants.SWIFT_15_SCAN_FILE_PATH);
            detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            highlightedText = "1/Group";
            data = "Group";
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, data)
                    , highlightedText
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 7, enabled = true, groups = {"regression"})
    @Owner("Sarah Abdelatif")
    public void SwiftImprovement_08() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("* Verify that scanning a SWIFT message with field 59F is highlighting the violation for all Line 1 when the black list is under Individual category\\n\" +\n" +
                    "                    \"* Verify that scanning a SWIFT message with field 59F is highlighting the violation for all Line 1 when the black list is under Vessels category\\n\" +\n" +
                    "                    \"* Verify that scanning a SWIFT message with field 59F is highlighting the violation for all Line 1 when the black list is under Unknown category"));
            lifecycle.updateTestCase(testResult -> testResult.setName("SwiftImprovement_08"));

            EnList enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.COMMON_TEST_ZONE_007.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, enlist, Common.PROFILE.FULL_RIGHT_007.getProfile().getName());


            enlist.getEntry().get(0).setName("UAZ");
            enlist.getEntry().get(0).setType("Vessel");
            enlist.getEntry().get(0).setFirstName(null);
            commonTestMethods.createNewEntryWithDetailsInfo(driver, enlist, enlist.getEntry().get(0));

            enlist.getEntry().get(0).setName("IDK");
            enlist.getEntry().get(0).setType("Unknown");
            commonTestMethods.createNewEntryWithDetailsInfo(driver, enlist, enlist.getEntry().get(0));

            enlist.getEntry().get(0).setName("himaran");
            enlist.getEntry().get(0).setType("Individual");
            commonTestMethods.createNewEntryWithDetailsInfo(driver, enlist, enlist.getEntry().get(0));


            FileScan fileScan = commonTestMethods.getFileScanData(GeneralConstants.SWIFT_16_SCAN_FILE_PATH);
            fileScan.setRank("50");
            String detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            String highlightedText = "1/himaran\n1/na\n1/na";
            String data = "himaran";
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, data)
                    , highlightedText
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


            fileScan.setFilePath(GeneralConstants.SWIFT_17_SCAN_FILE_PATH);
            detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            highlightedText = "1/UAZ\n1/na\n1/na";
            data = "UAZ";
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, data)
                    , highlightedText
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            fileScan.setFilePath(GeneralConstants.SWIFT_18_SCAN_FILE_PATH);
            detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            highlightedText = "1/IDK\n1/na\n1/na";
            data = "IDK";
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, data)
                    , highlightedText
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 8, enabled = true, groups = {"regression"})
    @Owner("Sarah Abdelatif")
    public void SwiftImprovement_09() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("* Verify that ISO2 and ISO3 country codes are detected as a violation when added to Field 50F line 3\n" +
                    "* Verify that ISO2 and ISO3 country codes are detected as a violation when added to Field 50F line 7\n" +
                    "* Verify that ISO2 and ISO3 country codes are detected as a violation when added to Field 50F line 6"));
            lifecycle.updateTestCase(testResult -> testResult.setName("SwiftImprovement_09"));


            EnList enlist = commonTestMethods.getEnListData();
            String filePath = GeneralConstants.OFC_FILE_PATH + "/AllCountries.ofc";
            enlist.setZoneName(Common.ZONE.COMMON_TEST_ZONE_007.getZone().getDisplayName());
            enlist.setName("AllCountries");

            screeningServicesDelegate.deleteLinkBtwBlackListAndListSet(enlist.getZoneName(), enlist.getName());
            screeningServicesDelegate.deleteLinkBtwBlackListAndGoodGuy(enlist.getZoneName(), enlist.getName());
            screeningServicesDelegate.deleteBlackList(enlist.getName());

            commonTestMethods.importBListAndLinkToNewListSet(driver, enlist, Common.PROFILE.FULL_RIGHT_007.getProfile().getName(), filePath);


            FileScan fileScan = commonTestMethods.getFileScanData(GeneralConstants.SWIFT_19_SCAN_FILE_PATH);
            fileScan.setRank("50");
            String detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            String highlightedText = "3/EG";
            String data = "EGYPT";
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, data)
                    , highlightedText
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


            highlightedText = "6/JOR";
            data = "JORDAN";
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, data)
                    , highlightedText
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            highlightedText = "7/AFG";
            data = "AFGHANISTAN";
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, data)
                    , highlightedText
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 10, enabled = true, groups = {"regression"})
    @Owner("Sarah Abdelatif")
    public void SwiftImprovement_10() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Check rank when Enable improved interpretation of SWIFT Fields for Individual Entity"));
            lifecycle.updateTestCase(testResult -> testResult.setName("SwiftImprovement_10"));

            EnList enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.COMMON_TEST_ZONE_007.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, enlist, Common.PROFILE.FULL_RIGHT_007.getProfile().getName());


            enlist.getEntry().get(0).setName("Kevin Bontemps");
            enlist.getEntry().get(0).setType("Individual");
            enlist.getEntry().get(0).setFirstName(null);

            commonTestMethods.createNewEntryWithDetailsInfo(driver, enlist, enlist.getEntry().get(0));

            FileScan fileScan = commonTestMethods.getFileScanData(GeneralConstants.SWIFT_20_SCAN_FILE_PATH);
            fileScan.setRank("68");
            fileScan.setResult("All records");
            commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            resultManager.clickOnResult(driver);
            Assert.assertEquals(resultManager.getDetectionRank(driver), "68", "Detection Rank is not correct");


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 11, enabled = true, groups = {"regression"})
    @Owner("Sarah Abdelatif")
    public void SwiftImprovement_11() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Check rank when Enable improved interpretation of SWIFT Fields for Group Entity"));
            lifecycle.updateTestCase(testResult -> testResult.setName("SwiftImprovement_11"));

            EnList enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.COMMON_TEST_ZONE_007.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, enlist, Common.PROFILE.FULL_RIGHT_007.getProfile().getName());


            enlist.getEntry().get(0).setName("Kevin Bontemps");
            enlist.getEntry().get(0).setType("Group");
            enlist.getEntry().get(0).setFirstName(null);

            commonTestMethods.createNewEntryWithDetailsInfo(driver, enlist, enlist.getEntry().get(0));


            FileScan fileScan = commonTestMethods.getFileScanData(GeneralConstants.SWIFT_20_SCAN_FILE_PATH);
            fileScan.setRank("68");
            fileScan.setResult("All records");
            commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            resultManager.clickOnResult(driver);
            Assert.assertEquals(resultManager.getDetectionRank(driver), "78", "Detection Rank is not correct");


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 12, enabled = true, groups = {"regression"})
    @Owner("Sarah Abdelatif")
    public void SwiftImprovement_12() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Check rank when Disbale improved interpretation of SWIFT Fields for Individual Entity"));
            lifecycle.updateTestCase(testResult -> testResult.setName("SwiftImprovement_11"));
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_7.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_7.getOperator().getPassword()
                    );
            generalSettings.setEnableImprovedInterpretationOfSWIFTFields(false);
            Assert.assertEquals(businessLogicControl.setGeneralSettingsDetails(driver, generalSettings)
                    , "Operation completed successfully! The new configuration will take place after 10 minutes maximum."
                    , "Failed to set general settings details.");

            commonTestMethods.restartNewArch();

            EnList enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.COMMON_TEST_ZONE_007.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, enlist, Common.PROFILE.FULL_RIGHT_007.getProfile().getName());


            enlist.getEntry().get(0).setName("Kevin Bontemps");
            enlist.getEntry().get(0).setType("Individual");
            enlist.getEntry().get(0).setFirstName(null);

            commonTestMethods.createNewEntryWithDetailsInfo(driver, enlist, enlist.getEntry().get(0));
            FileScan fileScan = commonTestMethods.getFileScanData(GeneralConstants.SWIFT_20_SCAN_FILE_PATH);
            fileScan.setRank("68");
            fileScan.setResult("All records");
            String detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            resultManager.clickOnResult(driver);
            Assert.assertEquals(resultManager.getDetectionRank(driver), "100", "Detection Rank is not correct");


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 13, enabled = true, groups = {"regression"})
    @Owner("Sarah Abdelatif")
    public void SwiftImprovement_13() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Check rank when Disbale improved interpretation of SWIFT Fields for Individual Entity"));
            lifecycle.updateTestCase(testResult -> testResult.setName("SwiftImprovement_11"));
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_7.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_7.getOperator().getPassword()
                  );
            generalSettings.setEnableImprovedInterpretationOfSWIFTFields(false);
            Assert.assertEquals(businessLogicControl.setGeneralSettingsDetails(driver, generalSettings)
                    , "Operation completed successfully! The new configuration will take place after 10 minutes maximum."
                    , "Failed to set general settings details.");

            commonTestMethods.restartNewArch();

            EnList enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.COMMON_TEST_ZONE_007.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, enlist, Common.PROFILE.FULL_RIGHT_007.getProfile().getName());


            enlist.getEntry().get(0).setName("Kevin Bontemps");
            enlist.getEntry().get(0).setType("Group");
            enlist.getEntry().get(0).setFirstName(null);

            commonTestMethods.createNewEntryWithDetailsInfo(driver, enlist, enlist.getEntry().get(0));
            FileScan fileScan = commonTestMethods.getFileScanData(GeneralConstants.SWIFT_20_SCAN_FILE_PATH);
            fileScan.setRank("68");
            fileScan.setResult("All records");
            String detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            resultManager.clickOnResult(driver);
            Assert.assertEquals(resultManager.getDetectionRank(driver), "100", "Detection Rank is not correct");


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @AfterMethod()
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
