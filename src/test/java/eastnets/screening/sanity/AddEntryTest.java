package eastnets.screening.sanity;

import com.fasterxml.jackson.databind.ObjectMapper;
import core.BaseTest;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.common.gui.Navigation;
import eastnets.screening.control.listManager.ListExplorerControl;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import java.io.File;
import java.io.IOException;

public class AddEntryTest extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final ListExplorerControl listExplorerControl = new ListExplorerControl();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();
    private final core.util.Property property = new core.util.Property();

    private RemoteWebDriver driver;

    private final int random = randomizer.getInt();

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeClass(alwaysRun = true, dependsOnMethods = "initializeDriver")
    public void navigateToListManagerPage() {
        try {
            loginPage.login(driver
                    , property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("user.name")
                    , property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("user.password")
                    );
            Navigation.LIST_MANAGER.navigate(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @Test(priority = 01, dataProvider = "testData", enabled = true, groups = {"sanity"})
    @Owner("Sara Abdellatif")
    public void addNewEntry(EnList list) {

        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Validate Add new Entry To List."));
            lifecycle.updateTestCase(testResult -> testResult.setName("Validate Add new Entry To List."));

            driver.navigate().refresh();
            listExplorerControl.search(driver, list);

            for (int i = 0; i < list.getEntry().size(); i++) {
                Assert.assertTrue(listExplorerControl.create_entry_with_type_and_names(driver, list.getEntry().get(i))
                        , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while Creating new Entry." + list.getEntry().get(i).toString());
            }

            String name = list.getEntry().get(0).getName() + ", " + list.getEntry().get(0).getFirstName();
            property.updateKeyValue(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME, "first.entry.name", name);
            name = list.getEntry().get(1).getName() + ", " + list.getEntry().get(1).getFirstName();
            property.updateKeyValue(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME, "second.entry.name", name);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @AfterClass(alwaysRun = true)
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @DataProvider(name = "testData")
    public Object[][] prepareTestData() {
        Object[][] testDP = new Object[1][1];
        ObjectMapper mapper = new ObjectMapper();
        EnList list = null;
        try {
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("addNewEntry");
            list = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), EnList.class);
            list.setName(String.format(list.getName(), random));
            for (int i = 0; i < list.getEntry().size(); i++) {
                list.getEntry().get(i).setName(String.format(list.getEntry().get(i).getName(), random));
                list.getEntry().get(i).setFirstName(String.format(list.getEntry().get(i).getFirstName(), random));
            }
            list.getListSet().setName(String.format(list.getListSet().getName(), random));
            list.getListSet().getSwiftTemplate().setTemplateName(String.format(list.getListSet().getSwiftTemplate().getTemplateName(), random));
            list.getListSet().setOwner(property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("user.group"));
            list.setZoneName(property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("zone.display.name"));
            list.getListSet().setZone(property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("zone.display.name"));
            list.setName(property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("blackList.name"));


            System.out.println(list);
            testDP[0][0] = list;
        } catch (IOException e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
        Allure.step("Test Data = " + list.toString());
        return testDP;
    }

}
