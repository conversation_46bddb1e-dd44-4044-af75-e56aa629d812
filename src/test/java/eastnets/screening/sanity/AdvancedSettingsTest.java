package eastnets.screening.sanity;

import core.BaseTest;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.common.gui.LoginPage;
import eastnets.common.gui.Navigation;
import eastnets.screening.gui.advancedSettings.AdvancedSettingNavigation;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;


public class AdvancedSettingsTest extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final core.util.Property property = new core.util.Property();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void navigateToAdvancedSettingsPage() {
        try {
            loginPage.login(driver
                    , property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("user.name")
                    , property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("user.password")
                   );
            Navigation.ADVANCED_SETTINGS.navigate(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 0, enabled = true)
    @Owner("Sara Abdellatif")
    public void CheckAdvancedSettingsNavigation() {

        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Check Advanced Settings Tab Navigation"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Check Advanced Settings Tab Navigation"));

            SoftAssert softAssert = new SoftAssert();

            Navigation.ADVANCED_SETTINGS.navigate(driver);

            Allure.step("Check 'Mail' Tab Navigation");
            softAssert.assertEquals(AdvancedSettingNavigation.MAIL.navigate(driver), GeneralConstants.SUCCESS,
                    GeneralConstants.POM_EXCEPTION_ERR_MSG + "While navigate to 'Mail' Tab");

            Allure.step("Check 'System Status' Tab Navigation");
            softAssert.assertEquals(AdvancedSettingNavigation.SYSTEM_STATUS.navigate(driver), GeneralConstants.SUCCESS,
                    GeneralConstants.POM_EXCEPTION_ERR_MSG + "While navigate to 'System Status' Tab");

            Allure.step("Check 'Business Logic' Tab Navigation");
            softAssert.assertEquals(AdvancedSettingNavigation.BUSINESS_LOGIC.navigate(driver), GeneralConstants.SUCCESS,
                    GeneralConstants.POM_EXCEPTION_ERR_MSG + "While navigate to 'Business Logic' Tab");

            Allure.step("Check 'Engine Tuning' Tab Navigation");
            softAssert.assertEquals(AdvancedSettingNavigation.ENGINE_TUNING.navigate(driver), GeneralConstants.SUCCESS,
                    GeneralConstants.POM_EXCEPTION_ERR_MSG + "While navigate to 'Engine Tuning' Tab");

            Allure.step("Check 'Good Guys Category' Tab Navigation");
            softAssert.assertEquals(AdvancedSettingNavigation.GOOD_GUYS_CATEGORIES.navigate(driver), GeneralConstants.SUCCESS,
                    GeneralConstants.POM_EXCEPTION_ERR_MSG + "While navigate to 'Good Guys Category' Tab");

            Allure.step("Check '4 Eyes Settings' Tab Navigation");
            softAssert.assertEquals(AdvancedSettingNavigation.FOUR_EYES_SETTING.navigate(driver), GeneralConstants.SUCCESS,
                    GeneralConstants.POM_EXCEPTION_ERR_MSG + "While navigate to '4 Eyes Settings' Tab");

            Allure.step("Check 'Investigator' Tab Navigation");
            softAssert.assertEquals(AdvancedSettingNavigation.INVESTIGATORS.navigate(driver), GeneralConstants.SUCCESS,
                    GeneralConstants.POM_EXCEPTION_ERR_MSG + "While navigate to 'Investigator' Tab");

            Allure.step("Check 'Detection Reason Code' Tab Navigation");
            softAssert.assertEquals(AdvancedSettingNavigation.DETECTION_REASON_CODE.navigate(driver), GeneralConstants.SUCCESS,
                    GeneralConstants.POM_EXCEPTION_ERR_MSG + "While navigate to 'Detection Reason Code' Tab");

            Allure.step("Check 'Safe Trade' Tab Navigation");
            softAssert.assertEquals(AdvancedSettingNavigation.SAFE_TRADE.navigate(driver), GeneralConstants.SUCCESS,
                    GeneralConstants.POM_EXCEPTION_ERR_MSG + "While navigate to 'Safe Trade' Tab");

            softAssert.assertAll();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

}
