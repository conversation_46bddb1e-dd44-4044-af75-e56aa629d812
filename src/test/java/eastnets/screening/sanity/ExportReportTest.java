package eastnets.screening.sanity;

import core.BaseTest;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.control.ReportControl;
import eastnets.admin.entity.Report;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.common.gui.Navigation;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

public class ExportReportTest extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final ReportControl reportControl = new ReportControl();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 0, enabled = true)
    @Owner("Sara Abdellatif")
    public void exportDailyUserActivityReport() {

        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Validate create new 'DailyUserActivity' report and export it."));
            lifecycle.updateTestCase(testResult -> testResult.setName("Validate create new 'DailyUserActivity' report and export it."));

            loginPage.login(driver
                    , saaGeneralConfigsProps.getProperty(GeneralConstants.VALID_ADMIN_MAIL)
                    , saaGeneralConfigsProps.getProperty(GeneralConstants.VALID_ADMIN_PASSWORD)
            );
            Navigation.REPORT.navigate(driver);

            Report report = new Report("DailyUserActivity", "User-Name" + randomizer.getInt(), "Comment-" + randomizer.getInt());
            String fileName = "rptUserActivity";
            Assert.assertTrue(reportControl.createNewReport(driver, report), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(reportControl.getReportStatus(driver), "Done", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertTrue(reportControl.exportReport(driver, fileName, dockerServerIp), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
