package eastnets.screening.sanity;

import com.fasterxml.jackson.databind.ObjectMapper;
import core.BaseTest;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.common.gui.LoginPage;
import eastnets.common.gui.Navigation;
import eastnets.screening.control.FormatManagerControl;
import eastnets.screening.entity.Format;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import java.io.File;
import java.io.IOException;

public class FormatManagerTests extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final FormatManagerControl formatManagerControl = new FormatManagerControl();
    private final core.util.Property property = new core.util.Property();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();

    private RemoteWebDriver driver;

    private final int random = randomizer.getInt();

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeClass(alwaysRun = true, dependsOnMethods = "initializeDriver")
    public void navigateToListManagerPage() {
        try {
            loginPage.login(driver
                    , property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("user.name")
                    , property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("user.password")
                    );
            Navigation.FORMAT_MANAGER.navigate(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(description = "Validate create new format", priority = 0, dataProvider = "addFormatTestData", enabled = true, groups = {"sanity"})
    @Owner("Sara Abdellatif")
    public void createFormat(Format format) {

        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Validate create new format with type = " + format.getType()));
            lifecycle.updateTestCase(testResult -> testResult.setName("Validate create new format with type = " + format.getType()));
            formatManagerControl.AddNewFormat(driver, format);
            Assert.assertTrue(formatManagerControl.checkFormatExist(driver, format), GeneralConstants.POM_EXCEPTION_ERR_MSG);


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }

    }

    @DataProvider(name = "addFormatTestData")
    public Object[][] prepareTestData() {
        Object[][] addFormatDP = new Object[1][1];
        ObjectMapper mapper = new ObjectMapper();
        Format format = null;
        try {
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("addNewFormat");
            format = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), Format.class);

            format.setName(String.format(format.getName(), random));
            for (int i = 0; i < format.getFields().size(); i++) {
                format.getFields().get(i).setName(String.format(format.getFields().get(i).getName(), random));
            }

            addFormatDP[0][0] = format;
        } catch (IOException e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
        Allure.step("Test Data = " + format.toString());
        return addFormatDP;
    }
}
