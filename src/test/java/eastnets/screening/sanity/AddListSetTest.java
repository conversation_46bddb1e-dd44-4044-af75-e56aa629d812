package eastnets.screening.sanity;

import com.fasterxml.jackson.databind.ObjectMapper;
import core.BaseTest;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.common.gui.Navigation;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.SwiftManagerControl;
import eastnets.screening.control.listManager.BlackListControl;
import eastnets.screening.control.listManager.ListSetControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.gui.listManager.listSet.ListSetEditor;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import java.io.File;
import java.io.IOException;

public class AddListSetTest extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final BlackListControl blackListControl = new BlackListControl();
    private final SwiftManagerControl swiftManagerControl = new SwiftManagerControl();
    private final ListSetControl listSetControl = new ListSetControl();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();
    private final core.util.Property property = new core.util.Property();

    private RemoteWebDriver driver;

    private final int random = randomizer.getInt();

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeClass(alwaysRun = true, dependsOnMethods = "initializeDriver")
    public void navigateToListManagerPage() {
        try {
            loginPage.login(driver
                    , property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("user.name")
                    , property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("user.password")
                    );
            Navigation.LIST_MANAGER.navigate(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 0, dataProvider = "testData", enabled = true, groups = {"sanity"})
    @Owner("Sara Abdellatif")
    public void createListSet(EnList list) {

        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Validate create new List-Set with added Black list and associated profile."));
            lifecycle.updateTestCase(testResult -> testResult.setName("Validate create new List-Set with added Black list and associated profile."));

            String userProfile = property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("profile.name");
            Allure.step("Connect to Database and Check if User Profile = " + userProfile + " linked to any list set.");
            Allure.step("Number of effected rows = " + screeningServicesDelegate.deleteProfileFromListSet(userProfile));

            Assert.assertTrue(blackListControl.create_Black_List(driver, list)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new black list.");

            Assert.assertTrue(swiftManagerControl.CreateSwiftTemplate(driver, list.getListSet().getSwiftTemplate(), list.getZoneName())
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while create new swift template.");

            Assert.assertTrue(listSetControl.create_list_set(driver, list).contains(list.getExpectedMessage())
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new list set.");

            Allure.step("Link black list to list set.");
            Assert.assertTrue(listSetControl.link_black_list_to_list_set(driver, list).contains(list.getExpectedMessage())
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while add black list to listSet");

            Allure.step("Navigate to profile list set association tab.");
            ListSetEditor.ListSetEditorNavigation.PROFILES_LIST_SET_ASSOCIATION.navigate(driver);


            Assert.assertTrue(listSetControl.select_profiles(driver, userProfile).contains(list.getExpectedMessage())
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while select profiles in Profiles-ListSet Association.");

            Assert.assertTrue(screeningServicesDelegate.checkIfListSetLinkedToProfile(userProfile)
                    , GeneralConstants.DB_ERROR_MSG + " while check if listSet linked to profile.");

            property.updateKeyValue(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME, "blackList.name", list.getName());

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }

    }


    @AfterClass(alwaysRun = true)
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @DataProvider(name = "testData")
    public Object[][] prepareTestData() {
        Object[][] testDP = new Object[1][1];
        ObjectMapper mapper = new ObjectMapper();
        EnList list = null;
        try {
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("addNewEntry");
            list = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), EnList.class);
            list.setName(String.format(list.getName(), random));
            for (int i = 0; i < list.getEntry().size(); i++) {
                list.getEntry().get(i).setName(String.format(list.getEntry().get(i).getName(), random));
                list.getEntry().get(i).setFirstName(String.format(list.getEntry().get(i).getFirstName(), random));
            }
            list.getListSet().setName(String.format(list.getListSet().getName(), random));
            list.getListSet().getSwiftTemplate().setTemplateName(String.format(list.getListSet().getSwiftTemplate().getTemplateName(), random));
            list.getListSet().setOwner(property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("user.group"));
            list.setZoneName(property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("zone.display.name"));
            list.getListSet().setZone(property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("zone.display.name"));


            System.out.println(list);
            testDP[0][0] = list;
        } catch (IOException e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
        Allure.step("Test Data = " + list.toString());
        return testDP;
    }
}
