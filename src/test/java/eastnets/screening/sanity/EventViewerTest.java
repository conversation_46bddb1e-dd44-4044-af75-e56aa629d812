package eastnets.screening.sanity;

import core.BaseTest;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.control.EventViewerControl;
import eastnets.admin.entity.EventViewer;
import eastnets.common.gui.LoginPage;
import eastnets.common.gui.Navigation;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;

import java.util.List;

public class EventViewerTest extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final EventViewerControl eventViewerControl = new EventViewerControl();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 0, enabled = true)
    @Owner("Sara Abdellatif")
    public void CheckSearchEventViewer() {

        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Validate Search event viewer functionality"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Validate Search event viewer functionality"));

            SoftAssert softAssert = new SoftAssert();

            Allure.step("Login with invalid credentials.");
            String expectedResults = loginPage.login(driver
                    , screeningGeneralConfigsProps.getProperty(GeneralConstants.VALID_ADMIN_MAIL)
                    , String.valueOf(randomizer.getInt())
            );

            Assert.assertEquals(expectedResults, "Invalid username/password", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Allure.step("Login with valid credentials.");
            loginPage.login(driver
                    , screeningGeneralConfigsProps.getProperty(GeneralConstants.VALID_ADMIN_MAIL)
                    , screeningGeneralConfigsProps.getProperty(GeneralConstants.VALID_ADMIN_PASSWORD)
            );
            //LoginPage.switchToAdmin(driver);

            Allure.step("Navigate to event viewer.");
            Navigation.EVENT_VIEWER.navigate(driver);

            Allure.step("Search event viewer.");

            List<EventViewer> frontendDM = eventViewerControl.searchEventViewer(driver);

            Assert.assertTrue(frontendDM.size() != 0, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            softAssert.assertEquals(frontendDM.get(0).getSeverity(), "Information", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            softAssert.assertEquals(frontendDM.get(0).getMessage(),
                    String.format("User [%s] successfully logged in", screeningGeneralConfigsProps.getProperty(GeneralConstants.VALID_ADMIN_MAIL)),
                    GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            softAssert.assertEquals(frontendDM.get(1).getSeverity(), "Error", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            softAssert.assertEquals(frontendDM.get(1).getMessage(),
                    String.format("Bad password given for user [%s]", screeningGeneralConfigsProps.getProperty(GeneralConstants.VALID_ADMIN_MAIL)),
                    GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            softAssert.assertAll();


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
