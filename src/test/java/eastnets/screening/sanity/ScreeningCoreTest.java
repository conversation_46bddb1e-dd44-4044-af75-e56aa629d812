package eastnets.screening.sanity;

import core.BaseTest;
import core.ExceptionHandler;
import eastnets.common.control.Application;
import eastnets.common.control.CommonOperatorControl;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import io.qameta.allure.*;
import io.qameta.allure.testng.AllureTestNg;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Listeners;
import org.testng.annotations.Test;

@Listeners({AllureTestNg.class})
@Feature("Admin Functionality")
public class ScreeningCoreTest extends BaseTest {

    // Instance variables for page objects and controls
    private final CommonOperatorControl commonOperatorControl = new CommonOperatorControl();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final core.database.DatabaseDriver databaseDriver = new core.database.DatabaseDriver();
    private final core.util.Log log = new core.util.Log();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test()
    @Description("Pre-Requisite : Create Operators to be used in Regression Testing.")
    @Severity(SeverityLevel.CRITICAL)
    @Epic("Operator Manager")
    @Story("Create Test Operators With All Details.")
    @Step("Starting test to create test operators with all details.")
    @Owner("Sara Abdellatif")
    public void createOperators() {
        try {

            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("verify that test operator created with all details (zone,profile and group)"));
            lifecycle.updateTestCase(testResult -> testResult.setName("verify that test operator created with all details (zone,profile and group)"));

            scdbSqlConnection = databaseDriver.getConnection(Application.ADMIN);
            screeningServicesDelegate.resetPasswordPolicyData();
            commonOperatorControl.prepareOperatorEnvironment(driver, scdbSqlConnection);

            log.info("Verify that test operator created with all details (zone,profile and group) succeeded");

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod
    public void afterTest() {
        try {
            scdbSqlConnection.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
