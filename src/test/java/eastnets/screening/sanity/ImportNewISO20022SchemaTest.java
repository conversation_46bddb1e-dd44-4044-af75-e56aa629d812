package eastnets.screening.sanity;

import com.fasterxml.jackson.databind.ObjectMapper;
import core.BaseTest;
import core.ExceptionHandler;
import core.ISOTestMethods;
import core.constants.screening.GeneralConstants;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.common.gui.Navigation;
import eastnets.screening.control.ISO20022Control;
import eastnets.screening.entity.ISO20022FormatConfiguration;
import eastnets.screening.gui.iso20022Manager.ISONavigation;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import java.io.File;
import java.io.IOException;

public class ImportNewISO20022SchemaTest extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final ISO20022Control iso20022Control = new ISO20022Control();
    private final ISOTestMethods isoTestMethods = new ISOTestMethods();
    private final core.util.Property property = new core.util.Property();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod
    public void navigateToListManagerPage() {
        try {
            loginPage.login(driver
                    , property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("user.name")
                    , property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("user.password")
                   );
            Navigation.ISO_20022_MANAGER.navigate(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @Test(priority = 0, dataProvider = "isoTestData", enabled = true, groups = {"sanity"})
    @Owner("Sara Abdellatif")
    public void importNewISOSchema(ISO20022FormatConfiguration formatConfiguration) {

        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Validate Import new schema from 'ISO2022 Schema Configuration' module tab."));
            lifecycle.updateTestCase(testResult -> testResult.setName("Validate Import new schema from 'ISO2022 Schema Configuration' module tab."));
            isoTestMethods.importISOSchema(driver, formatConfiguration);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @Test(priority = 1, dataProvider = "isoTestData", enabled = true, groups = {"sanity"})
    @Owner("Sara Abdellatif")
    public void addNewISOFormat(ISO20022FormatConfiguration formatConfiguration) {

        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Validate Add new format from 'ISO2022 Format Configuration' tab."));
            lifecycle.updateTestCase(testResult -> testResult.setName("Validate Add new format from 'ISO2022 Format Configuration' tab."));

            String schemaNameAndVersion = formatConfiguration.getIso20022SchemaConfiguration().getSchemaName()
                    + "."
                    + formatConfiguration.getIso20022SchemaConfiguration().getSchemaVersion();

            Assert.assertTrue(ISONavigation.FORMAT_CONFIGURATION.navigate(driver), " while navigate to ISO20022 Format manager.");

            Assert.assertEquals(iso20022Control.createGroup(driver, formatConfiguration)
                    , String.format("New group [%s] successfully added", formatConfiguration.getGroupName())
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while create new group.");

            iso20022Control.clickConfigure(driver);

            Assert.assertEquals(iso20022Control.createNewMessage(driver, formatConfiguration.getIso20022SchemaConfiguration(), null)
                    , String.format("New message [%s] with version [%s] successfully Added",
                            formatConfiguration.getIso20022SchemaConfiguration().getSchemaName(), schemaNameAndVersion)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while create new message.");

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }

    }


    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @DataProvider(name = "isoTestData")
    public Object[][] prepareTestData() {
        Object[][] isoConfDP = new Object[1][1];
        ObjectMapper mapper = new ObjectMapper();
        ISO20022FormatConfiguration iso20022FormatConfiguration = new ISO20022FormatConfiguration();
        try {
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("isoConfiguration");
            iso20022FormatConfiguration = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), ISO20022FormatConfiguration.class);

            int random = randomizer.getInt();
            iso20022FormatConfiguration.setGroupName(String.format(iso20022FormatConfiguration.getGroupName(), random));
            iso20022FormatConfiguration.setExpectedResults(String.format(iso20022FormatConfiguration.getExpectedResults(),
                    iso20022FormatConfiguration.getIso20022SchemaConfiguration().getSchemaName(),
                    iso20022FormatConfiguration.getIso20022SchemaConfiguration().getSchemaVersion()));
            iso20022FormatConfiguration.setZone(property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("zone.display.name"));

            isoConfDP[0][0] = iso20022FormatConfiguration;
        } catch (IOException e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
        Allure.step("Test Data = " + iso20022FormatConfiguration.toString());
        return isoConfDP;
    }
}
