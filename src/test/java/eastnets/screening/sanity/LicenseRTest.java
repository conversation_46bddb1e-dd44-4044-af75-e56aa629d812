package eastnets.screening.sanity;

import core.BaseTest;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import core.util.DirectoryUtil;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.LicenseControl;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

public class LicenseRTest extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final LicenseControl licenseControl = new LicenseControl();

    private RemoteWebDriver driver;

    private static final String LICENSE_PATH = DirectoryUtil.DATAFILES_RESOURCE + "/License/";
    private static final String FULL_LICENSE = DirectoryUtil.getAbsolutePath(LICENSE_PATH + "AllConnectorNoExpiryQ1million.xml");

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(description = "Validate import licence", priority = 0, enabled = true)
    @Owner("Sara Abdellatif")
    public void testImportLicence() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Validate import licence"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Validate import licence"));

            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getPassword()
            );
            licenseControl.importLicense(driver, FULL_LICENSE);
            String actualResult = commonAction.getAlertMessageString(driver);
            Allure.step("Actual result = " + actualResult);
            Allure.step("Expected result = License imported successfully");
            Assert.assertTrue(actualResult.equalsIgnoreCase("License imported successfully")
                    || actualResult.equalsIgnoreCase("The license to import already exist"), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
