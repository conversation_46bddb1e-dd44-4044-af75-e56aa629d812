package eastnets.screening.sanity;

import core.BaseTest;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.common.gui.Navigation;
import eastnets.screening.control.listManager.GoodGuyControl;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.gui.scanManager.scanName.NameScanManager;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

public class AddGoodGuyTest extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final GoodGuyControl goodGuyControl = new GoodGuyControl();
    private final NameScanControl nameScanControl = new NameScanControl();
    private final NameScanManager nameScanManager = new NameScanManager();
    private final core.util.Property property = new core.util.Property();

    private RemoteWebDriver driver;


    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeClass(alwaysRun = true, dependsOnMethods = "initializeDriver")
    public void navigateToListManagerPage() {
        try {
            loginPage.login(driver
                    , property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("user.name")
                    , property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("user.password")
                   );
            Navigation.LIST_MANAGER.navigate(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(enabled = true)
    @Owner("Sara Abdellatif")
    public void createGoodGuy() {

        try {

            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Navigate to 'Good Guys Explorer' and create one good guy."));
            lifecycle.updateTestCase(testResult -> testResult.setName("Navigate to 'Good Guys Explorer' and create one good guy."));


            String name = property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("first.entry.name");


            nameScanControl.scan_Name(driver
                    , name
                    , null
                    , null
                    , true
                    , true
                    , true
                    , false);

            nameScanManager.chooseScannedNameFromResultTable(driver, name);

            Assert.assertEquals(nameScanControl.add_detection_as_GG(driver, null), "Good guy successfully created!"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while add Detection as Good Guy.");


            Assert.assertTrue(goodGuyControl.verify_gg_exist(driver, name)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while check Good Guy with Accepted Text = " + name + " exist");

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterClass(alwaysRun = true)
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

}
