package eastnets.screening.sanity;

import com.fasterxml.jackson.databind.ObjectMapper;
import core.BaseTest;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import core.util.Log;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.control.scanManger.FileScanControl;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.FileScan;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import java.io.File;
import java.io.IOException;

public class BlockDetectionTest extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();
    private final FileScanControl fileScanControl = new FileScanControl();
    private final ResultManagerControl resultManagerControl = new ResultManagerControl();
    private final core.util.Property property = new core.util.Property();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeClass(dependsOnMethods = "initializeDriver")
    public void login() {
        try {
            loginPage.login(driver
                    , property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("user.name")
                    , property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("user.password")
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @Test(priority = 0, dataProvider = "testData", enabled = true, groups = {"sanity"})
    @Owner("Sarah Abdellatif")
    public void blockDetection(FileScan fileScan) {

        try {

            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Validate scan file then block detection from 'Detection Manager'."));
            lifecycle.updateTestCase(testResult -> testResult.setName("Validate scan file then block detection from 'Detection Manager'."));

            String rjeSampleFile = System.getProperty("user.dir") + property.fromFile(GeneralConstants.SCREENING_TEST_DATA_CONFIG_FILE_PATH).getProperty("RJESwiftSampleFile");
            String rjeNewFile = System.getProperty("user.dir") + property.fromFile(GeneralConstants.SCREENING_TEST_DATA_CONFIG_FILE_PATH).getProperty("RJESwiftFile");
            String entryName = property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("second.entry.name");
            fileScanControl.edit_rje_file(rjeSampleFile, rjeNewFile, entryName);

            fileScan.setFilePath(System.getProperty("user.dir") + fileScan.getFilePath());
            Assert.assertEquals(fileScanControl.scan_file(driver, fileScan).split("\\[")[0], "File sent to the server for processing with id ", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Assert.assertEquals(resultManagerControl.check_scanned_file_result(driver), "SUCCEEDED", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            String detectionID = resultManagerControl.get_detection_id(driver);
            Assert.assertNotNull(detectionID, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Assert.assertEquals(detectionManagerControl.search_by_id(driver, detectionID), "REPNEW", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.block_detection(driver, detectionID, "Test Massage"), "1 detection(s) successfully modified!", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterClass(alwaysRun = true)
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @DataProvider(name = "testData")
    public Object[][] prepareTestData() {
        Object[][] testDP = new Object[1][1];
        ObjectMapper mapper = new ObjectMapper();
        FileScan fileScan = new FileScan();
        try {
            String csvDataFilePath = userDirectoryPath + screeningTestDataConfigsProps.getProperty("blockDetection");
            fileScan = mapper.readValue(new File(csvDataFilePath), FileScan.class);
            testDP[0][0] = fileScan;
        } catch (IOException e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
        new Log().info("Test Data = " + fileScan.toString());
        return testDP;
    }
}
