package eastnets.screening.sanity;

import com.fasterxml.jackson.databind.ObjectMapper;
import core.BaseTest;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.common.gui.Navigation;
import eastnets.screening.control.listManager.DowJonesControl;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import java.io.File;
import java.io.IOException;

public class AddDowJonesListTest extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final DowJonesControl dowJonesControl = new DowJonesControl();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();
    private final core.util.Property property = new core.util.Property();

    private RemoteWebDriver driver;

    private final int random = randomizer.getInt();

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeClass(alwaysRun = true, dependsOnMethods = "initializeDriver")
    public void navigateToListManagerPage() {
        try {
            loginPage.login(driver
                    , property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("user.name")
                    , property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("user.password")
            );
            Navigation.LIST_MANAGER.navigate(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 3, dataProvider = "testData", enabled = true, groups = {"sanity"})
    @Owner("Sara Abdellatif")
    public void createDowJones(EnList list) {

        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Validate create dow jones."));
            lifecycle.updateTestCase(testResult -> testResult.setName("Validate create dow jones."));
            String listName = "PFA2_201407242200_D";


            String userName = property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("user.name");
            String password = property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("user.password");
            String zoneID = property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("zone.id");

            Allure.step("1- Create New Dow-Jones list.");
            Assert.assertTrue(dowJonesControl.create_dj_list(driver, list, "PEP", false), GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new Dow-Jones list.");
            Allure.step("###  Dow Jones List Created Successfully  ###");

            Allure.step("2- Enable Dow-Jones list.");
            Assert.assertEquals(dowJonesControl.enable_dj_list(driver), list.getExpectedMessage(), GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new Dow-Jones list.");
            Allure.step("###  Dow Jones List Enabled Successfully  ###");

            Allure.step("3- Connect to server and create DJLogin File and DJ File.");
            Assert.assertTrue(dowJonesControl.create_dj_files(list, true, false, userName, password, zoneID, listName, false).contains("INFO  - Bye bye!"), GeneralConstants.POM_EXCEPTION_ERR_MSG);

            Allure.step("4- Edit Down-jones list and select countries");
            dowJonesControl.select_countries(driver, list);
            Allure.step("###  DJ list Edited Successfully And Countries Assigned To It  ###");

            Allure.step("4- Connect to server and edit DJ file with 'loadReferentialOnly=false' and run it using cmd to load entries.");
            Assert.assertTrue(dowJonesControl.load_dj_list_load_referential_only(list, false, false, listName, false).contains("Process successfully finished!"), GeneralConstants.POM_EXCEPTION_ERR_MSG);
            Allure.step("###  DJ list Loaded Successfully  ###");
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterClass(alwaysRun = true)
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @DataProvider(name = "testData")
    public Object[][] prepareTestData() {
        Object[][] testDP = new Object[1][1];
        ObjectMapper mapper = new ObjectMapper();
        EnList list = null;
        try {
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("addNewEntry");
            list = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), EnList.class);
            list.setName(String.format(list.getName(), random));
            for (int i = 0; i < list.getEntry().size(); i++) {
                list.getEntry().get(i).setName(String.format(list.getEntry().get(i).getName(), random));
                list.getEntry().get(i).setFirstName(String.format(list.getEntry().get(i).getFirstName(), random));
            }
            list.getListSet().setName(String.format(list.getListSet().getName(), random));
            list.getListSet().getSwiftTemplate().setTemplateName(String.format(list.getListSet().getSwiftTemplate().getTemplateName(), random));
            list.getListSet().setOwner(property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("user.group"));
            list.setZoneName(property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("zone.display.name"));
            list.getListSet().setZone(property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("zone.display.name"));


            System.out.println(list);
            testDP[0][0] = list;
        } catch (IOException e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
        Allure.step("Test Data = " + list.toString());
        return testDP;
    }
}
