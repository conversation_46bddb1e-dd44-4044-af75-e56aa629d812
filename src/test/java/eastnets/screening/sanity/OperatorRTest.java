package eastnets.screening.sanity;

import com.opencsv.exceptions.CsvValidationException;
import core.BaseTest;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.boundary.ProfileResource;
import eastnets.admin.boundary.ZoneResource;
import eastnets.admin.control.GroupControl;
import eastnets.admin.control.OperatorControl;
import eastnets.admin.entity.Group;
import eastnets.admin.entity.Operator;
import eastnets.admin.entity.Profile;
import eastnets.common.gui.LoginPage;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.IOException;
import java.util.Collections;

public class OperatorRTest extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final GroupControl groupControl = new GroupControl();
    private final OperatorControl operatorControl = new OperatorControl();
    private final ZoneResource zoneResource = new ZoneResource();
    private final ProfileResource profileResource = new ProfileResource();
    private final core.util.Property property = new core.util.Property();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeClass(alwaysRun = true, dependsOnMethods = "initializeDriver")
    public void loginAsAdmin() {
        try {
            loginPage.login(driver
                    , screeningGeneralConfigsProps.getProperty(GeneralConstants.VALID_ADMIN_MAIL)
                    , screeningGeneralConfigsProps.getProperty(GeneralConstants.VALID_ADMIN_PASSWORD)
                   );

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 0, enabled = true, groups = "sanity")
    @Owner("Sara Abdellatif")
    public void testCreateGroupWithOperator() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Validate create new zone , profile , operator and group."));
            lifecycle.updateTestCase(testResult -> testResult.setName("Validate create new zone , profile , operator and group."));


            Group group = getRandomGroup();
            Operator operator = Operator.getRandom(group.getProfile().getZone());
            Allure.step("Operator test data = " + operator.toString());

            group.setGroupMembers(Collections.singletonList(operator));
            Allure.step("Group test data = " + group.toString());

            // Update SMOKE_TEST_CONFIG_FILE_NAME file data to be used in smoke test.
            property.updateKeyValue(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME, "user.name", operator.getLoginName());
            property.updateKeyValue(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME, "user.password", operator.getPassword());
            property.updateKeyValue(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME, "user.group", group.getName());

            Allure.step("Create new zone .");
            Allure.step("Zone test Data = " + group.getProfile().getZone());
            Assert.assertTrue(zoneResource.createZone(driver, group.getProfile().getZone()), String.format("Can't create group %s", group.getProfile().getZone().getDisplayName()));


            Allure.step("Create new profile .");
            Allure.step("Profile test Data = " + group.getProfile());
            Assert.assertTrue(profileResource.createProfileAndZoneIfNotExist(driver, group.getProfile()), String.format("Can't create profile %s", group.getProfile().getName()));

            Allure.step("Create new operator.");
            Allure.step("Operator test Data = " + operator);
            Assert.assertTrue(operatorControl.createOperator(driver, operator), String.format("Can't create operator %s", operator.getLoginName()));

            Allure.step("Create new group.");
            Allure.step("Group test Data = " + group);
            Assert.assertTrue(groupControl.createGroup(driver, group), String.format("Can't create group %s", group.getName()));

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Step("Get random group")
    public Group getRandomGroup() throws CsvValidationException, IOException {
        Profile profile = Profile.getRandom("Test-Profile");

        property.updateKeyValue(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME, "zone.display.name", profile.getZone().getDisplayName());
        property.updateKeyValue(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME, "zone.name", profile.getZone().getName());
        property.updateKeyValue(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME, "profile.name", profile.getName());

        return Group.getRandom(profile, Collections.emptyList());
    }
}
