package eastnets.screening.ApprovalTests.General_TCs;

import core.BaseTest;
import core.ExceptionHandler;
import core.util.Wait;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.common.gui.Navigation;
import eastnets.screening.control.ApprovalControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.gui.approvalConfiguration.WorkflowApprovals;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;

public class TC92095 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final ApprovalControl approvalControl = new ApprovalControl();
    private final WorkflowApprovals workflowApprovals = new WorkflowApprovals();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();
    private final Wait wait = new Wait();

    private RemoteWebDriver driver;

    EnList list;
    String entityName;
    String entityFirstName;
    String entityDisplayedName;
    SoftAssert softassert = new SoftAssert();
    String updatedEntityDisplayedName;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeClass(description = "Before Class", dependsOnMethods = "initializeDriver")
    public void beforeClass() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver, Common.OPERATOR.APPROVAL_RIGHT.getOperator().getLoginName(),
                    Common.OPERATOR.APPROVAL_RIGHT.getOperator().getPassword());

            String groupName = "Group" + randomizer.getInt() + " Approvers";
            approvalControl.clickApprovalModuleIcon(driver);
            Assert.assertEquals(approvalControl.createGroup(driver, groupName, Common.ZONE.APPROVAL_ZONE.getZone().getDisplayName(),
                            Common.OPERATOR.APPROVAL_RIGHT_1.getOperator().getLoginName()), "Group " + "[" + groupName + "] successfully added",
                    "Group is not added");
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(description = "Verify that only COMPLETED workflows return when searching after selecting COMPLETED from Status list in Search panel")
    public void searchByCompletedStatus_TC92095() {
        try {
            Allure.step("Verify that only COMPLETED workflows return when searching after selecting COMPLETED from Status list in Search panel");
            loginPage.login(driver, Common.OPERATOR.APPROVAL_RIGHT_1.getOperator().getLoginName(),
                    Common.OPERATOR.APPROVAL_RIGHT_1.getOperator().getPassword());
            Navigation.APPROVAL.navigate(driver);
            workflowApprovals.clickWorkflowApprovalsTab(driver);
            workflowApprovals.chooseFromDate(driver, "2024/03/01");
            approvalControl.filterRequestsByStatus(driver, "COMPLETED");
            wait.time(Wait.ONE_SECOND * 7);
            Assert.assertTrue(approvalControl.checkStatusForAllRequests(driver, "COMPLETED"), "Not all requests are Completed");
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod
    @Step("after method")
    public void afterMethod() {
        Allure.step("log out after each test case");
        commonAction.logout(driver);
    }
}
