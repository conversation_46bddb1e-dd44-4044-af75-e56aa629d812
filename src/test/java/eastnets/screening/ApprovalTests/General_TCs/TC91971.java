package eastnets.screening.ApprovalTests.General_TCs;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import core.util.Wait;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.ApprovalControl;
import eastnets.screening.control.listManager.BlackListControl;
import eastnets.screening.control.listManager.ListExplorerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.gui.approvalConfiguration.WorkflowApprovals;
import eastnets.screening.gui.listManager.listExplorer.ListExplorerManager;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;

public class TC91971 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final ApprovalControl approvalControl = new ApprovalControl();
    private final BlackListControl blackListControl = new BlackListControl();
    private final ListExplorerControl listExplorerControl = new ListExplorerControl();
    private final ListExplorerManager listExplorerManager = new ListExplorerManager();
    private final WorkflowApprovals workflowApprovals = new WorkflowApprovals();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();
    private final Wait wait = new Wait();

    private RemoteWebDriver driver;

    EnList list;
    String entityName;
    String entityFirstName;
    String entityDisplayedName;
    SoftAssert softassert = new SoftAssert();
    String updatedEntityDisplayedName;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeClass(description = "Before Class", dependsOnMethods = "initializeDriver")
    public void beforeClass() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver, Common.OPERATOR.APPROVAL_RIGHT.getOperator().getLoginName(),
                    Common.OPERATOR.APPROVAL_RIGHT.getOperator().getPassword());

            String groupName = "Group" + randomizer.getInt() + " Approvers";
            approvalControl.clickApprovalModuleIcon(driver);
            Assert.assertEquals(approvalControl.createGroup(driver, groupName, Common.ZONE.APPROVAL_ZONE.getZone().getDisplayName(),
                            Common.OPERATOR.APPROVAL_RIGHT_1.getOperator().getLoginName()), "Group " + "[" + groupName + "] successfully added",
                    "Group is not added");
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(description = "Verify that reject for amendment displayed only for Add/ Update actions ")
    public void rejectFroAmendmentUpdateAndAdd_TC91971() {
        try {
            Allure.step("Verify that reject for amendment displayed only for Add/ Update actions ");
            /// Add Entity
            driver.navigate().refresh();
            loginPage.login(driver, Common.OPERATOR.APPROVAL_RIGHT.getOperator().getLoginName(),
                    Common.OPERATOR.APPROVAL_RIGHT.getOperator().getPassword());

            list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.APPROVAL_ZONE.getZone().getDisplayName());
            entityName = list.getEntry().get(0).getName();
            entityFirstName = list.getEntry().get(0).getFirstName();
            entityDisplayedName = entityName + ", " + entityFirstName;
            updatedEntityDisplayedName = entityName + "update" + ", " + entityFirstName;
            commonTestMethods.createListSet(driver, list, Common.PROFILE.APPROVAL_RIGHT.getProfile().getName()); //create list
            commonTestMethods.createNewEntryWithTypeAndNames(driver, list); //add entities
            commonAction.logout(driver);
            loginPage.login(driver, Common.OPERATOR.APPROVAL_RIGHT_1.getOperator().getLoginName()
                    , Common.OPERATOR.APPROVAL_RIGHT_1.getOperator().getPassword());
            wait.time(Wait.ONE_SECOND * 5);
            Assert.assertEquals(approvalControl.getRequestStatus(driver, WorkflowApprovals.TEMPLATE_NAME.ADD_BLACKLIST_ENTRY), "IN_PROGRESS", "Request status not as expected");
            workflowApprovals.clickCheckBox(driver, WorkflowApprovals.TEMPLATE_NAME.ADD_BLACKLIST_ENTRY);
            workflowApprovals.clickReject(driver);
            Assert.assertTrue(workflowApprovals.chooseRejectionReason(driver, "Reject for Amendment"), "Reject for Amendment not available for Add request");
            workflowApprovals.clickApprove(driver);
            commonAction.logout(driver);
            /// Edit Entry
            loginPage.login(driver, Common.OPERATOR.APPROVAL_RIGHT.getOperator().getLoginName(),
                    Common.OPERATOR.APPROVAL_RIGHT.getOperator().getPassword());
            listExplorerControl.search(driver, list);
            listExplorerControl.edit_entry(driver, Common.ZONE.APPROVAL_ZONE.getZone().getDisplayName(), list.getEntry().get(0));
            commonAction.logout(driver);
            loginPage.login(driver, Common.OPERATOR.APPROVAL_RIGHT_1.getOperator().getLoginName()
                    , Common.OPERATOR.APPROVAL_RIGHT_1.getOperator().getPassword());
            wait.time(Wait.ONE_SECOND * 5);
            Assert.assertEquals(approvalControl.getRequestStatus(driver, WorkflowApprovals.TEMPLATE_NAME.UPDATE_BLACKLIST_ENTRY), "IN_PROGRESS", "Request status not as expected");
            workflowApprovals.clickCheckBox(driver, WorkflowApprovals.TEMPLATE_NAME.UPDATE_BLACKLIST_ENTRY);
            workflowApprovals.clickReject(driver);
            Assert.assertTrue(workflowApprovals.chooseRejectionReason(driver, "Reject for Amendment"), "Reject for Amendment not available for Update request");
            commonAction.logout(driver);
            //Disable Entity
            loginPage.login(driver, Common.OPERATOR.APPROVAL_RIGHT.getOperator().getLoginName(),
                    Common.OPERATOR.APPROVAL_RIGHT.getOperator().getPassword());
            approvalControl.deleteBlacklistBeforeImport(driver, Common.ZONE.APPROVAL_ZONE.getZone().getDisplayName(), GeneralConstants.IRAN_LIST_NAME);
            Assert.assertEquals(blackListControl.import_list(driver, Common.ZONE.APPROVAL_ZONE.getZone().getDisplayName(), GeneralConstants.IRAN_LIST_FILE_PATH, true, false)
                    , "SUCCEEDED"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            approvalControl.disableFirstEntity(driver, Common.ZONE.APPROVAL_ZONE.getZone().getDisplayName(), GeneralConstants.IRAN_LIST_NAME);
            Assert.assertFalse(listExplorerManager.is_disabled(driver, "7th of Tir"), "Entity is disabled without approve");
            commonAction.logout(driver);
            loginPage.login(driver, Common.OPERATOR.APPROVAL_RIGHT_1.getOperator().getLoginName()
                    , Common.OPERATOR.APPROVAL_RIGHT_1.getOperator().getPassword());
            Assert.assertEquals(approvalControl.getRequestStatus(driver, WorkflowApprovals.TEMPLATE_NAME.DISABLE_BLACKLIST_ENTRY), "IN_PROGRESS", "Request status not as expected");
            workflowApprovals.clickCheckBox(driver, WorkflowApprovals.TEMPLATE_NAME.DISABLE_BLACKLIST_ENTRY);
            workflowApprovals.clickReject(driver);
            Assert.assertFalse(workflowApprovals.chooseRejectionReason(driver, "Reject for Amendment"), "Reject for Amendment is available for Disable request");
            workflowApprovals.clickApprove(driver);
            commonAction.logout(driver);
            //Enable Entity
            loginPage.login(driver, Common.OPERATOR.APPROVAL_RIGHT.getOperator().getLoginName()
                    , Common.OPERATOR.APPROVAL_RIGHT.getOperator().getPassword());
            listExplorerControl.navigate(driver);
            listExplorerControl.search(driver, Common.ZONE.APPROVAL_ZONE.getZone().getDisplayName(), GeneralConstants.IRAN_LIST_NAME, "");
            approvalControl.enableFirstEntity(driver);
            Assert.assertFalse(listExplorerManager.is_disabled(driver, "7th of Tir"), "Entity is not enabled before approve");
            commonAction.logout(driver);
            loginPage.login(driver, Common.OPERATOR.APPROVAL_RIGHT_1.getOperator().getLoginName()
                    , Common.OPERATOR.APPROVAL_RIGHT_1.getOperator().getPassword());
            Assert.assertEquals(approvalControl.getRequestStatus(driver, WorkflowApprovals.TEMPLATE_NAME.ENABLE_BLACKLIST_ENTRY), "IN_PROGRESS", "Request status not as expected");
            workflowApprovals.clickCheckBox(driver, WorkflowApprovals.TEMPLATE_NAME.ENABLE_BLACKLIST_ENTRY);
            workflowApprovals.clickReject(driver);
            Assert.assertFalse(workflowApprovals.chooseRejectionReason(driver, "Reject for Amendment"), "Reject for Amendment is available for Enable request");
            workflowApprovals.clickApprove(driver);
            commonAction.logout(driver);
            /// Delete Entity
            loginPage.login(driver, Common.OPERATOR.APPROVAL_RIGHT.getOperator().getLoginName(),
                    Common.OPERATOR.APPROVAL_RIGHT.getOperator().getPassword());
            list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.APPROVAL_ZONE.getZone().getDisplayName());
            entityName = list.getEntry().get(0).getName();
            entityFirstName = list.getEntry().get(0).getFirstName();
            entityDisplayedName = entityName + ", " + entityFirstName;
            commonTestMethods.createListSet(driver, list, Common.PROFILE.APPROVAL_RIGHT.getProfile().getName()); //create list
            commonTestMethods.createNewEntryWithTypeAndNames(driver, list); //add entities
            commonAction.logout(driver);
            loginPage.login(driver, Common.OPERATOR.APPROVAL_RIGHT_1.getOperator().getLoginName()
                    , Common.OPERATOR.APPROVAL_RIGHT_1.getOperator().getPassword());
            approvalControl.getRequestStatus(driver, WorkflowApprovals.TEMPLATE_NAME.ADD_BLACKLIST_ENTRY);
            workflowApprovals.clickCheckBox(driver, WorkflowApprovals.TEMPLATE_NAME.ADD_BLACKLIST_ENTRY);
            workflowApprovals.clickApprove(driver);
            commonAction.logout(driver);
            loginPage.login(driver, Common.OPERATOR.APPROVAL_RIGHT.getOperator().getLoginName()
                    , Common.OPERATOR.APPROVAL_RIGHT.getOperator().getPassword());
            String verifyMessage = listExplorerControl.delete_entry(driver, Common.ZONE.APPROVAL_ZONE.getZone().getDisplayName(), list.getName(), entityDisplayedName);
            commonAction.logout(driver);
            loginPage.login(driver, Common.OPERATOR.APPROVAL_RIGHT_1.getOperator().getLoginName()
                    , Common.OPERATOR.APPROVAL_RIGHT_1.getOperator().getPassword());
            String requestStatus = approvalControl.getRequestStatus(driver, WorkflowApprovals.TEMPLATE_NAME.DELETE_BLACKLIST_ENTRY);
            Assert.assertEquals(requestStatus, "IN_PROGRESS", "Request status not as expected");
            workflowApprovals.clickCheckBox(driver, WorkflowApprovals.TEMPLATE_NAME.DELETE_BLACKLIST_ENTRY);
            workflowApprovals.clickReject(driver);
            Assert.assertFalse(workflowApprovals.chooseRejectionReason(driver, "Reject for Amendment"), "Reject for Amendment is available for Delete request");
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod
    @Step("after method")
    public void afterMethod() {
        Allure.step("log out after each test case");
        commonAction.logout(driver);
    }
}
