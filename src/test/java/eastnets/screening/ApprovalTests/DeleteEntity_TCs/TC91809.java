package eastnets.screening.ApprovalTests.DeleteEntity_TCs;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.ApprovalControl;
import eastnets.screening.control.listManager.ListExplorerControl;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.gui.approvalConfiguration.ConfigurationsManager;
import eastnets.screening.gui.approvalConfiguration.WorkflowApprovals;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;

public class TC91809 extends BaseTest{

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final ApprovalControl approvalControl = new ApprovalControl();
    private final ListExplorerControl listExplorerControl = new ListExplorerControl();
    private final NameScanControl nameScanControl = new NameScanControl();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();

    private RemoteWebDriver driver;

    EnList list;
    String entityName;
    String entityFirstName;
    String entityDisplayedName;
    SoftAssert softassert = new SoftAssert();

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeClass(description = "Before Class", dependsOnMethods = "initializeDriver")
    public void beforeClass()  {
        try{
        loginPage.login(driver, Common.OPERATOR.APPROVAL_RIGHT.getOperator().getLoginName(),
                Common.OPERATOR.APPROVAL_RIGHT.getOperator().getPassword());
        String groupName = "Group" + randomizer.getInt()+" Approvers";
        approvalControl.clickApprovalModuleIcon(driver);
        Assert.assertEquals(approvalControl.createGroup(driver, groupName,Common.ZONE.APPROVAL_ZONE.getZone().getDisplayName(),
                        Common.OPERATOR.APPROVAL_RIGHT_1.getOperator().getLoginName()),"Group "+ "["+groupName +"] successfully added",
                "Group is not added");
        Assert.assertEquals(approvalControl.addPermission(driver,groupName, ConfigurationsManager.PERMISSIONS.DELETE_ENTRY,
                Common.ZONE.APPROVAL_ZONE.getZone().getDisplayName()), "Step [Approve] successfully updated" , "Permission not added successfully");
        Assert.assertEquals(approvalControl.addPermission(driver,groupName, ConfigurationsManager.PERMISSIONS.ADD_ENTRY,
                Common.ZONE.APPROVAL_ZONE.getZone().getDisplayName()), "Step [Approve] successfully updated" , "Permission not added successfully");
        commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
    /**
     * This test is for Ticket 91809
     * Steps:
     * 1- Create a list set with random name.
     * 2- Create a list and add new entry.
     * 3- Check Request is created.
     * 4- Log out then login with the approver user.
     * 5- Navigate to the workflow and check request status.
     * 6- Approve the request then check request status.
     * 7- Log out then login with the Creator user.
     * 8- Navigate to the list and check the entry is added successfully.
     * 9- Delete the entry.
     * 10- Log out then login with the approver user.
     * 11- Navigate to the workflow and check request status.
     * 12- Reject the Delete request.
     * 13- Navigate to the list and check entry exists.
     * 14- Go to Scan Manager and scan entry.
     * 15- Check status is REP.
     */
        @Test( description ="Verify scan with the deleted entity in case Workflow is rejected" )
        public void scanViolationRejectedEntity_TC91809() {
        try{
        Allure.step("Verify scan with the deleted entity in case Workflow is rejected");
        loginPage.login(driver, Common.OPERATOR.APPROVAL_RIGHT.getOperator().getLoginName(),
                Common.OPERATOR.APPROVAL_RIGHT.getOperator().getPassword());
        list = commonTestMethods.getEnListData();
        list.setZoneName(Common.ZONE.APPROVAL_ZONE.getZone().getDisplayName());
        commonTestMethods.createListSet(driver,list,Common.PROFILE.APPROVAL_RIGHT.getProfile().getName()); //create list
        commonTestMethods.createNewEntryWithTypeAndNames(driver,list); //add entities
        entityName = list.getEntry().get(0).getName();
        entityFirstName = list.getEntry().get(0).getFirstName();
        entityDisplayedName = entityName + ", " + entityFirstName;
        commonAction.logout(driver);
        loginPage.login(driver, Common.OPERATOR.APPROVAL_RIGHT_1.getOperator().getLoginName()
                ,Common.OPERATOR.APPROVAL_RIGHT_1.getOperator().getPassword());
        Assert.assertEquals(approvalControl.getRequestStatus(driver, WorkflowApprovals.TEMPLATE_NAME.ADD_BLACKLIST_ENTRY), "IN_PROGRESS", "Request status not as expected");
        Assert.assertEquals(approvalControl.approveRequest(driver, WorkflowApprovals.TEMPLATE_NAME.ADD_BLACKLIST_ENTRY),"Operation completed successfully.", "Success message is not correct.");
        Assert.assertEquals(approvalControl.getRequestStatus(driver, WorkflowApprovals.TEMPLATE_NAME.ADD_BLACKLIST_ENTRY), "COMPLETED", "Request status not as expected");
        commonAction.logout(driver);
        loginPage.login(driver, Common.OPERATOR.APPROVAL_RIGHT.getOperator().getLoginName(),
                Common.OPERATOR.APPROVAL_RIGHT.getOperator().getPassword());
            listExplorerControl.delete_entry(driver,Common.ZONE.APPROVAL_ZONE.getZone().getDisplayName(),list.getName(),entityDisplayedName);
        Allure.step("Verify Create new request success message");
        Assert.assertTrue(approvalControl.verifyRequestCreated(driver), "Request is not created");
        commonAction.logout(driver);
        loginPage.login(driver, Common.OPERATOR.APPROVAL_RIGHT_1.getOperator().getLoginName()
                ,Common.OPERATOR.APPROVAL_RIGHT_1.getOperator().getPassword());
        Assert.assertEquals(approvalControl.getRequestStatus(driver, WorkflowApprovals.TEMPLATE_NAME.DELETE_BLACKLIST_ENTRY), "IN_PROGRESS", "Request status not as expected");
        Assert.assertEquals(approvalControl.rejectRequest(driver, WorkflowApprovals.TEMPLATE_NAME.DELETE_BLACKLIST_ENTRY,"Reject")
                ,"Operation completed successfully.", "Success message is not correct.");
        Assert.assertEquals(approvalControl.getRequestStatus(driver, WorkflowApprovals.TEMPLATE_NAME.DELETE_BLACKLIST_ENTRY), "REJECTED", "Request status not as expected");
        Assert.assertTrue(listExplorerControl.is_entry_exist(driver, list.getZoneName(), list.getName(), entityDisplayedName), "Entity does not exist");

            String status = nameScanControl.scan_Name(driver
                ,entityDisplayedName
                ,Common.ZONE.APPROVAL_ZONE.getZone().getDisplayName()
                ,"75"
                ,false
                ,false
                ,false
                ,false);
        Assert.assertEquals(status, "REP", "Status for the scan is not correct");
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
    @AfterMethod(groups = {"regression"})
    @Step("after method")
    public void afterMethod(){
        Allure.step("log out after each test case");
        commonAction.logout(driver);
    }
}
