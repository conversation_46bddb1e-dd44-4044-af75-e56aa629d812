package eastnets.screening.ApprovalTests.Notifications_TCs;

import core.BaseTest;
import core.ExceptionHandler;
import core.util.Wait;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.common.gui.Navigation;
import eastnets.screening.control.NotificationsControl;
import eastnets.screening.gui.Notifications;
import eastnets.screening.gui.advancedSettings.AdvancedSettingNavigation;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;

public class TC92082 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final NotificationsControl notificationsControl = new NotificationsControl();
    private final Wait wait = new Wait();

    private RemoteWebDriver driver;

    SoftAssert softassert = new SoftAssert();

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(description = "Verify that notification pop up displayed with proper data when clicking on notification record")
    public void verifyNotificationPopUp_TC92082() {
        try {
            loginPage.login(driver, Common.OPERATOR.APPROVAL_RIGHT.getOperator().getLoginName(),
                    Common.OPERATOR.APPROVAL_RIGHT.getOperator().getPassword());
            Navigation.ADVANCED_SETTINGS.navigate(driver);
            AdvancedSettingNavigation.NOTIFICATIONS.navigate(driver);
            wait.time(Wait.ONE_SECOND * 5);
            notificationsControl.clickFirstNotification(driver);
            softassert.assertTrue(notificationsControl.elementExists(driver, Notifications.POPUP_ID_LOCATOR), "ID does not exist");
            softassert.assertTrue(notificationsControl.elementExists(driver, Notifications.POPUP_ID_TEXTBOX_LOCATOR), "ID Textbox does not exist");
            softassert.assertTrue(notificationsControl.elementHasText(driver, Notifications.POPUP_ID_TEXTBOX_LOCATOR), "ID Textbox is empty");
            softassert.assertTrue(notificationsControl.elementExists(driver, Notifications.POPUP_MODULE_LOCATOR));
            softassert.assertTrue(notificationsControl.elementExists(driver, Notifications.POPUP_MODULE_TEXTBOX_LOCATOR));
            softassert.assertTrue(notificationsControl.elementHasText(driver, Notifications.POPUP_MODULE_TEXTBOX_LOCATOR), "Module Textbox is empty");
            softassert.assertTrue(notificationsControl.elementExists(driver, Notifications.POPUP_TYPE_LOCATOR));
            softassert.assertTrue(notificationsControl.elementExists(driver, Notifications.POPUP_TYPE_TEXTBOX_LOCATOR));
            softassert.assertTrue(notificationsControl.elementHasText(driver, Notifications.POPUP_TYPE_TEXTBOX_LOCATOR), "Type Textbox is empty");
            softassert.assertTrue(notificationsControl.elementExists(driver, Notifications.POPUP_CREATED_BY_LOCATOR));
            softassert.assertTrue(notificationsControl.elementExists(driver, Notifications.POPUP_CREATED_BY_TEXTBOX_LOCATOR));
            softassert.assertTrue(notificationsControl.elementHasText(driver, Notifications.POPUP_CREATED_BY_TEXTBOX_LOCATOR), "Created By Textbox is empty");
            softassert.assertTrue(notificationsControl.elementExists(driver, Notifications.POPUP_CREATION_DATE_LOCATOR));
            softassert.assertTrue(notificationsControl.elementExists(driver, Notifications.POPUP_CREATION_DATE_TEXTBOX_LOCATOR));
            softassert.assertTrue(notificationsControl.elementHasText(driver, Notifications.POPUP_CREATION_DATE_TEXTBOX_LOCATOR), "Creation Date Textbox is empty");
            softassert.assertTrue(notificationsControl.elementExists(driver, Notifications.POPUP_NOTIFICATION_MESSAGE_LOCATOR));
            softassert.assertTrue(notificationsControl.elementExists(driver, Notifications.POPUP_NOTIFICATION_MESSAGE_TEXTBOX_LOCATOR));
            softassert.assertTrue(notificationsControl.elementHasText(driver, Notifications.POPUP_NOTIFICATION_MESSAGE_TEXTBOX_LOCATOR), "Notification Message Textbox is empty");
            softassert.assertTrue(notificationsControl.elementExists(driver, Notifications.POPUP_CANCEL_BUTTON_LOCATOR));
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod
    @Step("after method")
    public void afterMethod() {
        Allure.step("log out after each test case");
        commonAction.logout(driver);
    }
}
