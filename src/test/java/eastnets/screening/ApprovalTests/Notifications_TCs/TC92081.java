package eastnets.screening.ApprovalTests.Notifications_TCs;

import core.BaseTest;
import core.ExceptionHandler;
import core.util.Wait;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.common.gui.Navigation;
import eastnets.screening.control.NotificationsControl;
import eastnets.screening.gui.Notifications;
import eastnets.screening.gui.advancedSettings.AdvancedSettingNavigation;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;

public class TC92081 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final NotificationsControl notificationsControl = new NotificationsControl();
    private final Wait wait = new Wait();

    private RemoteWebDriver driver;

    SoftAssert softassert = new SoftAssert();

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(description = "Verify that notification page contains proper details")
    public void verifyNotificationPageElements_TC92081() {
        try {
            loginPage.login(driver, Common.OPERATOR.APPROVAL_RIGHT.getOperator().getLoginName(),
                    Common.OPERATOR.APPROVAL_RIGHT.getOperator().getPassword());
            Navigation.ADVANCED_SETTINGS.navigate(driver);
            AdvancedSettingNavigation.NOTIFICATIONS.navigate(driver);
            wait.time(Wait.ONE_SECOND * 5);
            softassert.assertTrue(notificationsControl.elementExists(driver, Notifications.ID_LOCATOR), "ID does not exist");
            softassert.assertTrue(notificationsControl.elementExists(driver, Notifications.NOTIFICATION_MESSAGE_LOCATOR), "Notification message does not exist");
            softassert.assertTrue(notificationsControl.elementExists(driver, Notifications.MODULE_LOCATOR), "Module does not exist");
            softassert.assertTrue(notificationsControl.elementExists(driver, Notifications.CREATION_DATE_LOCATOR), "Creation Date does not exist");
            softassert.assertTrue(notificationsControl.elementExists(driver, Notifications.ID_TEXTBOX_LOCATOR), "ID Textbox does not exist");
            softassert.assertTrue(notificationsControl.elementExists(driver, Notifications.MODULE_DDL_LOCATOR), "Module DDL does not exist");
            softassert.assertTrue(notificationsControl.elementExists(driver, Notifications.RESET_BUTTON), "Reset button does not exist");
            softassert.assertAll();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod
    @Step("after method")
    public void afterMethod() {
        Allure.step("log out after each test case");
        commonAction.logout(driver);
    }
}
