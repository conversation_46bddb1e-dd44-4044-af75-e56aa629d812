package eastnets.screening.ApprovalTests.AddEntity_TCs;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.util.Wait;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.ApprovalControl;
import eastnets.screening.control.listManager.ListExplorerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.gui.approvalConfiguration.ConfigurationsManager;
import eastnets.screening.gui.approvalConfiguration.WorkflowApprovals;
import eastnets.screening.gui.listManager.listExplorer.ListExplorerManager;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;

public class TC91922 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final ApprovalControl approvalControl = new ApprovalControl();
    private final ListExplorerControl listExplorerControl = new ListExplorerControl();
    private final ListExplorerManager listExplorerManager = new ListExplorerManager();
    private final WorkflowApprovals workflowApprovals = new WorkflowApprovals();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();
    private final Wait wait = new Wait();

    private RemoteWebDriver driver;

    EnList list;
    String entityName;
    String entityFirstName;
    String entityDisplayedName;
    SoftAssert softassert = new SoftAssert();

    @BeforeClass(description = "Initialize Driver", groups = {"regression"})
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeClass(description = "Before Class", groups = {"regression"}, dependsOnMethods = "initializeDriver")
    public void beforeClass() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver, Common.OPERATOR.APPROVAL_RIGHT.getOperator().getLoginName(),
                    Common.OPERATOR.APPROVAL_RIGHT.getOperator().getPassword());
            String groupName = "Group" + randomizer.getInt() + " Approvers";
            approvalControl.clickApprovalModuleIcon(driver);
            Assert.assertEquals(approvalControl.createGroup(driver, groupName, Common.ZONE.APPROVAL_ZONE.getZone().getDisplayName(),
                            Common.OPERATOR.APPROVAL_RIGHT_1.getOperator().getLoginName()), "Group " + "[" + groupName + "] successfully added",
                    "Group is not added");
            Assert.assertEquals(approvalControl.addPermission(driver, groupName, ConfigurationsManager.PERMISSIONS.ADD_ENTRY,
                    Common.ZONE.APPROVAL_ZONE.getZone().getDisplayName()), "Step [Approve] successfully updated", "Permission not added successfully");
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
    /**
     * This test is for Ticket 91922
     * Steps:
     * 1- Create a list set with random name.
     * 2- Create a list and add new entry.
     * 3- Check Request is created.
     * 4- Log out then login with the approver user.
     * 5- Navigate to the workflow and check request status.
     * 6- Reject the request for amendment then check request status.
     * 7- Log out then login with the Creator user.
     * 8- Navigate to the list search for the entry.
     * 9- Select the entry then check Resubmit button is enabled.
     * 10- Click on Resubmit and modify the entry.
     * 11- Log out then login with the approver user.
     * 12- Go to Workflow then approve the request.
     * 13- Log out then Log in with the creator user.
     * 14- Navigate the list and check Entry exists.
     */

    @Test(groups = {"regression"}, description = "Verify that “Reject for amendment” add entity workflow is working fine when Configuration is enabled")
    public void rejectForAmendmentEnabled_TC91922() {
        try {
            Allure.step("Verify that “Reject for amendment” add entity workflow is working fine when Configuration is enabled");
            driver.navigate().refresh();
            loginPage.login(driver, Common.OPERATOR.APPROVAL_RIGHT.getOperator().getLoginName(),
                    Common.OPERATOR.APPROVAL_RIGHT.getOperator().getPassword());
            list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.APPROVAL_ZONE.getZone().getDisplayName());
            entityName = list.getEntry().get(0).getName();
            entityFirstName = list.getEntry().get(0).getFirstName();
            entityDisplayedName = entityName + ", " + entityFirstName;
            commonTestMethods.createListSet(driver, list, Common.PROFILE.APPROVAL_RIGHT.getProfile().getName()); //create list
            commonTestMethods.createNewEntryWithTypeAndNames(driver, list); //add entities
            Assert.assertTrue(approvalControl.verifyRequestCreated(driver), "Request is not created");
            commonAction.logout(driver);
            driver.navigate().refresh();
            loginPage.login(driver, Common.OPERATOR.APPROVAL_RIGHT_1.getOperator().getLoginName()
                    , Common.OPERATOR.APPROVAL_RIGHT_1.getOperator().getPassword());

            Assert.assertEquals(approvalControl.getRequestStatus(driver, WorkflowApprovals.TEMPLATE_NAME.ADD_BLACKLIST_ENTRY), "IN_PROGRESS", "Request status not as expected");
            Assert.assertEquals(approvalControl.rejectRequest(driver, WorkflowApprovals.TEMPLATE_NAME.ADD_BLACKLIST_ENTRY, "Reject for Amendment")
                    , "Operation completed successfully.", "Success message is not correct.");
            Assert.assertEquals(approvalControl.getRequestStatus(driver, WorkflowApprovals.TEMPLATE_NAME.ADD_BLACKLIST_ENTRY), "REJECTED", "Request status not as expected");
            commonAction.logout(driver);
            driver.navigate().refresh();
            loginPage.login(driver, Common.OPERATOR.APPROVAL_RIGHT.getOperator().getLoginName()
                    , Common.OPERATOR.APPROVAL_RIGHT.getOperator().getPassword());

            approvalControl.selectRequest(driver, WorkflowApprovals.TEMPLATE_NAME.ADD_BLACKLIST_ENTRY);
            Assert.assertTrue(workflowApprovals.isResubmitButtonDisplayed(driver), "Resubmit button is not displayed");
            workflowApprovals.clickResubmitButton(driver);
//        ListExplorerEditor.clickSaveButton(driver);
            approvalControl.updateEntryForResubmit(driver, list);
            Assert.assertTrue(approvalControl.verifyRequestCreated(driver), "Request is not created");
            Assert.assertTrue(listExplorerControl.is_entry_exist(driver, list.getZoneName(), list.getName(), "Test, Test"), "Entity does not exist");
            Assert.assertFalse(listExplorerManager.is_disabled(driver, "Test, Test"), "Entity is not disabled");
            commonAction.logout(driver);
            driver.navigate().refresh();
            loginPage.login(driver, Common.OPERATOR.APPROVAL_RIGHT_1.getOperator().getLoginName()
                    , Common.OPERATOR.APPROVAL_RIGHT_1.getOperator().getPassword());

            Assert.assertEquals(approvalControl.getRequestStatus(driver, WorkflowApprovals.TEMPLATE_NAME.ADD_BLACKLIST_ENTRY), "IN_PROGRESS", "Request status not as expected");
            Assert.assertEquals(approvalControl.approveRequest(driver, WorkflowApprovals.TEMPLATE_NAME.ADD_BLACKLIST_ENTRY), "Operation completed successfully.", "Success message is not correct.");
            Assert.assertEquals(approvalControl.getRequestStatus(driver, WorkflowApprovals.TEMPLATE_NAME.ADD_BLACKLIST_ENTRY), "COMPLETED", "Request status not as expected");
            commonAction.logout(driver);
            driver.navigate().refresh();
            loginPage.login(driver, Common.OPERATOR.APPROVAL_RIGHT.getOperator().getLoginName()
                    , Common.OPERATOR.APPROVAL_RIGHT.getOperator().getPassword());
            Assert.assertTrue(listExplorerControl.is_entry_exist(driver, list.getZoneName(), list.getName(), "Test, Test"), "Entity does not exist");
            Assert.assertFalse(listExplorerManager.is_disabled(driver, "Test, Test"), "Entity is disabled");
            wait.time(Wait.ONE_SECOND * 5);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(groups = {"regression"})
    @Step("after method")
    public void afterMethod() {
        Allure.step("log out after each test case");
        commonAction.logout(driver);
    }
}
