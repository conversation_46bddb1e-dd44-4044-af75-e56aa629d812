package eastnets.screening.ApprovalTests.DisableEntity_TCs;

import core.BaseTest;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.ApprovalControl;
import eastnets.screening.control.listManager.BlackListControl;
import eastnets.screening.control.listManager.ListExplorerControl;
import eastnets.screening.gui.approvalConfiguration.ConfigurationsManager;
import eastnets.screening.gui.approvalConfiguration.WorkflowApprovals;
import eastnets.screening.gui.listManager.listExplorer.ListExplorerManager;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

public class TC91845 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final ApprovalControl approvalControl = new ApprovalControl();
    private final BlackListControl blackListControl = new BlackListControl();
    private final ListExplorerControl listExplorerControl = new ListExplorerControl();
    private final ListExplorerManager listExplorerManager = new ListExplorerManager();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeClass(description = "Before Class", dependsOnMethods = "initializeDriver")
    public void beforeClass()  {
        try{
        driver.navigate().refresh();
        loginPage.login(driver, Common.OPERATOR.APPROVAL_RIGHT.getOperator().getLoginName(),
                Common.OPERATOR.APPROVAL_RIGHT.getOperator().getPassword());
        String groupName = "Group" + randomizer.getInt()+" Approvers";
        approvalControl.clickApprovalModuleIcon(driver);
        Assert.assertEquals(approvalControl.createGroup(driver, groupName,Common.ZONE.APPROVAL_ZONE.getZone().getDisplayName(),
                        Common.OPERATOR.APPROVAL_RIGHT_1.getOperator().getLoginName()),"Group "+ "["+groupName +"] successfully added",
                "Group is not added");
        Assert.assertEquals(approvalControl.addPermission(driver,groupName, ConfigurationsManager.PERMISSIONS.DISABLE_ENTRY,
                Common.ZONE.APPROVAL_ZONE.getZone().getDisplayName()), "Step [Approve] successfully updated" , "Permission not added successfully");
        commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
    /**
     * This Test is for ticket 91845
     * Steps:
     * 1- Delete private list before import.
     * 2- Import the private list.
     * 3- Navigate to List Manager and disable first entry.
     * 4- Verify Request created.
     * 5- Log out then login with the approver user.
     * 6- Go to Workflow then reject the disable request.
     * 7- Log out then login with Creator user.
     * 8- Navigate to List then check first entry is enabled.
     */
    @Test(description = "Verify that “Reject” disable entity workflow is working fine when Configuration is enabled")
    public void rejectDisableEntityEnabled_TC91845()  {
        try{
        Allure.step("Verify that “Reject” disable entity workflow is working fine when Configuration is enabled");
        driver.navigate().refresh();
        loginPage.login(driver, Common.OPERATOR.APPROVAL_RIGHT.getOperator().getLoginName(),
                Common.OPERATOR.APPROVAL_RIGHT.getOperator().getPassword());
        approvalControl.deleteBlacklistBeforeImport(driver,Common.ZONE.APPROVAL_ZONE.getZone().getDisplayName(), GeneralConstants.IRAN_LIST_NAME);
        Assert.assertEquals(blackListControl.import_list(driver,Common.ZONE.APPROVAL_ZONE.getZone().getDisplayName(), GeneralConstants.IRAN_LIST_FILE_PATH, true, false)
                , "SUCCEEDED"
                , GeneralConstants.POM_EXCEPTION_ERR_MSG);
        approvalControl.disableFirstEntity(driver,Common.ZONE.APPROVAL_ZONE.getZone().getDisplayName(),GeneralConstants.IRAN_LIST_NAME);
        Assert.assertTrue(approvalControl.verifyRequestCreated(driver), "Request is not created");

        Assert.assertFalse(listExplorerManager.is_disabled(driver,"7th of Tir"),"Entity is disabled without approve");
        commonAction.logout(driver);
        driver.navigate().refresh();
        loginPage.login(driver, Common.OPERATOR.APPROVAL_RIGHT_1.getOperator().getLoginName()
                ,Common.OPERATOR.APPROVAL_RIGHT_1.getOperator().getPassword());

        Assert.assertEquals(approvalControl.getRequestStatus(driver, WorkflowApprovals.TEMPLATE_NAME.DISABLE_BLACKLIST_ENTRY), "IN_PROGRESS", "Request status not as expected");
        Assert.assertEquals(approvalControl.rejectRequest(driver,WorkflowApprovals.TEMPLATE_NAME.DISABLE_BLACKLIST_ENTRY,"Reject"),"Operation completed successfully.", "Success message is not correct.");
        Assert.assertEquals(approvalControl.getRequestStatus(driver, WorkflowApprovals.TEMPLATE_NAME.DISABLE_BLACKLIST_ENTRY), "REJECTED", "Request status not as expected");
        commonAction.logout(driver);
        driver.navigate().refresh();
        loginPage.login(driver, Common.OPERATOR.APPROVAL_RIGHT.getOperator().getLoginName()
                ,Common.OPERATOR.APPROVAL_RIGHT.getOperator().getPassword());
            Assert.assertTrue(listExplorerControl.is_entry_exist(driver, Common.ZONE.APPROVAL_ZONE.getZone().getDisplayName()
                    , GeneralConstants.IRAN_LIST_NAME, ""), "Entity does not exist");
            Assert.assertFalse(listExplorerManager.is_disabled(driver,"7th of Tir"),"Entity is disabled while request is rejected");
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
        }
    @AfterMethod(groups = {"regression"})
    @Step("after method")
    public void afterMethod() {
        Allure.step("log out after each test case");
        commonAction.logout(driver);
    }
}
