package eastnets.screening.ApprovalTests.DisableEntity_TCs;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.ApprovalControl;
import eastnets.screening.control.listManager.ListExplorerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.gui.approvalConfiguration.ConfigurationsManager;
import eastnets.screening.gui.listManager.listExplorer.ListExplorerManager;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;

public class TC91864 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final ApprovalControl approvalControl = new ApprovalControl();
    private final ListExplorerControl listExplorerControl = new ListExplorerControl();
    private final ListExplorerManager listExplorerManager = new ListExplorerManager();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();

    private RemoteWebDriver driver;

    EnList list;
    String entityName;
    String entityFirstName;
    String entityDisplayedName;
    SoftAssert softassert = new SoftAssert();

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeClass(description = "Before Class", dependsOnMethods = "initializeDriver")
    public void beforeClass()  {
        try{
        driver.navigate().refresh();
        loginPage.login(driver, Common.OPERATOR.APPROVAL_RIGHT.getOperator().getLoginName(),
                Common.OPERATOR.APPROVAL_RIGHT.getOperator().getPassword());
        String groupName = "Group" + randomizer.getInt()+" Approvers";
        approvalControl.clickApprovalModuleIcon(driver);
        Assert.assertEquals(approvalControl.createGroup(driver, groupName,Common.ZONE.APPROVAL_ZONE.getZone().getDisplayName(),
                        Common.OPERATOR.APPROVAL_RIGHT_1.getOperator().getLoginName()),"Group "+ "["+groupName +"] successfully added",
                "Group is not added");
        Assert.assertEquals(approvalControl.addPermission(driver,groupName, ConfigurationsManager.PERMISSIONS.DISABLE_ENTRY,
                Common.ZONE.APPROVAL_ZONE.getZone().getDisplayName()), "Step [Approve] successfully updated" , "Permission not added successfully");
        commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
    /**
     * This Test is for ticket 91864
     * Steps:
     * 1- Create a list set.
     * 2- Create a list and add a new entry.
     * 3- Navigate to List Manager and disable first entry.
     * 4- Verify alert message appears that disable is available for private lists only.
     */
    @Test(description = "Verify that disable feature available for public black list only when approval configuration is enabled")
    public void verifyDisableForPublicLists_TC91864() {
        try{
        Allure.step("Verify that disable feature available for public black list only when approval configuration is enabled");
        driver.navigate().refresh();
        loginPage.login(driver, Common.OPERATOR.APPROVAL_RIGHT.getOperator().getLoginName(),
                Common.OPERATOR.APPROVAL_RIGHT.getOperator().getPassword());
        list = commonTestMethods.getEnListData();
        list.setZoneName(Common.ZONE.APPROVAL_ZONE.getZone().getDisplayName());
        entityName = list.getEntry().get(0).getName();
        entityFirstName = list.getEntry().get(0).getFirstName();
        entityDisplayedName = entityName + ", " + entityFirstName;
        commonTestMethods.createListSet(driver,list,Common.PROFILE.APPROVAL_RIGHT.getProfile().getName()); //create list
        commonTestMethods.createNewEntryWithTypeAndNames(driver,list); //add entities
            Assert.assertTrue(listExplorerControl.is_entry_exist(driver, list.getZoneName()
                    , list.getName(), entityDisplayedName), "Entity does not exist");
        listExplorerManager.click_check_box(driver);
        listExplorerManager.click_disable_button(driver);
        Assert.assertEquals(approvalControl.getEnablePublicListMessage(driver),"This feature is available for Public Black List only!", "Error in alert message for deleing from public list");
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
    @AfterMethod
    @Step("after method")
    public void afterMethod() {
        Allure.step("log out after each test case");
        commonAction.logout(driver);
    }
}
