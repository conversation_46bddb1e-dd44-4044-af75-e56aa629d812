package eastnets.screening;

import com.paypal.selion.platform.dataprovider.DataProviderFactory;
import com.paypal.selion.platform.dataprovider.DataResource;
import com.paypal.selion.platform.dataprovider.SeLionDataProvider;
import com.paypal.selion.platform.dataprovider.impl.InputStreamResource;
import core.BaseTest;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.gui.LoginPage;
import eastnets.common.gui.Navigation;
import eastnets.screening.control.AdvancedSettingsControls.AdvancedSettingsControl;
import eastnets.screening.control.AdvancedSettingsControls.BusinessLogicControl;
import eastnets.screening.control.AdvancedSettingsControls.FatfSettingsControl;
import eastnets.screening.control.scanManger.FileScanControl;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FatfSettings;
import eastnets.screening.entity.FileScan;
import eastnets.screening.entity.ListSet;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;

import java.io.FileInputStream;
import java.io.IOException;


public class Fatf16Tests extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final AdvancedSettingsControl advancedSettingsControl = new AdvancedSettingsControl();
    private final BusinessLogicControl businessLogicControl = new BusinessLogicControl();
    private final FatfSettingsControl fatfSettingsControl = new FatfSettingsControl();
    private final FileScanControl fileScanControl = new FileScanControl();
    private final ResultManagerControl resultManagerControl = new ResultManagerControl();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeClass( dependsOnMethods = "initializeDriver")
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_4.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_4.getOperator().getPassword()
            );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(dataProvider = "updateFatfSettings", enabled = true, groups = "regression")
    @Owner("Tarek Shams")
    public void updateFatfSettingsTest(FatfSettings fatfSettings) {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription(fatfSettings.getTestCaseTitle()));
            lifecycle.updateTestCase(testResult -> testResult.setName(fatfSettings.getTestCaseTitle()));

            Navigation.ADVANCED_SETTINGS.navigate(driver);
            Assert.assertTrue(advancedSettingsControl.navigateToBusinessLogic(driver).equalsIgnoreCase(GeneralConstants.SUCCESS));
            Assert.assertTrue(businessLogicControl.navigateToFatfSettings(driver).equalsIgnoreCase(GeneralConstants.SUCCESS));
            fatfSettingsControl.selectZone(driver, fatfSettings.getZones().get(0).getName());
            fatfSettingsControl.enableFatfRecommendation(driver, fatfSettings.getEnableFatfRecommendation());
            fatfSettingsControl.setBlackList(driver, fatfSettings.getBlackLists());
            fatfSettingsControl.selectFieldMinimumLineSize(driver, fatfSettings.getFieldMinimumLineSize());
            fatfSettingsControl.selectRank(driver, fatfSettings.getRank());
            fatfSettingsControl.insertEuropeanCodes(driver, fatfSettings.getEuropeanCodes());
            fatfSettingsControl.enableCheckOfUniqueTransactionRN(driver, fatfSettings.getEnableCheckOfUniqueTransactionReferenceNumber());
            fatfSettingsControl.informationAvailabilityCheck(driver, fatfSettings);
            fatfSettingsControl.insertCurrencies(driver, fatfSettings.getCurrencies());
            fatfSettingsControl.clickSave(driver, fatfSettings.getAction());
            //setting the data into file scan entity
            FileScan fileScan = new FileScan();
            fileScan.setFilePath(GeneralConstants.FATF_MESSAGES_DIRECTORY + fatfSettings.getFileName());
            fileScan.setFormat(fatfSettings.getFormat());
            EnList enList = new EnList();
            enList.setZoneName(fatfSettings.getZones().get(0).getName());
            ListSet listSet = new ListSet();
            listSet.setName("");
            enList.setListSet(listSet);
            fileScan.setEnList(enList);
            fileScan.setEncoding(fatfSettings.getEncoding());
            fileScan.setRank(fatfSettings.getRank());
            fileScan.setDetectVessels(fatfSettings.isDetectVessels());
            fileScan.setDetectCountries(fatfSettings.isDetectCountries());
            fileScan.setCreateAlertsAutomatically(fatfSettings.isCreateAlertsAutomatically());
            fileScan.setResult(fatfSettings.getResults());
            SoftAssert softAssert = new SoftAssert();
            softAssert.assertTrue(fileScanControl.scan_file(driver, fileScan).contains("File sent to the server for processing with id"), "Error sending file to server");
            softAssert.assertTrue(resultManagerControl.check_scanned_file_result(driver).equalsIgnoreCase("SUCCEEDED"), "File scan failed");
            softAssert.assertAll();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test()
    @Owner("Tarek Shams")
    public void loginTest() {

        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("fatfSettings.getTestCaseTitle()"));
            lifecycle.updateTestCase(testResult -> testResult.setName("fatfSettings.getTestCaseTitle()"));

            //Navigation.ADVANCED_SETTINGS.navigate(driver);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @DataProvider(name = "updateFatfSettings")
    public Object[][] updateFatfSettings() throws IOException {
        String dataFilePath = userDirectoryPath + screeningTestDataConfigsProps.getProperty("updateFatfSettings");
        DataResource resource =
                new InputStreamResource(new FileInputStream(dataFilePath),
                        FatfSettings.class, "json");
        SeLionDataProvider dataProvider =
                DataProviderFactory.getDataProvider(resource);
        return dataProvider.getAllData();
    }
}
