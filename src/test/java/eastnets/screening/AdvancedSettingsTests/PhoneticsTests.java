package eastnets.screening.AdvancedSettingsTests;

import com.fasterxml.jackson.databind.ObjectMapper;
import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import core.util.CSVHandler;
import core.util.Log;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.AdvancedSettingsControls.EngineTuningControl;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.EngineTuning;
import eastnets.screening.entity.Format;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import io.qameta.allure.Step;
import org.testng.Assert;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.io.File;
import java.io.IOException;
import java.util.List;


public class PhoneticsTests extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final EngineTuningControl engineTuningControl = new EngineTuningControl();
    private final NameScanControl nameScanControl = new NameScanControl();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final Log log = new Log();
    private final CSVHandler csvHandler = new CSVHandler();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();

    private RemoteWebDriver driver;

    @BeforeClass(alwaysRun = true)
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    @Step("Login")
    public void login() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver,
                    Common.OPERATOR.FULL_RIGHT_4.getOperator().getLoginName(),
                    Common.OPERATOR.FULL_RIGHT_4.getOperator().getPassword());
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 0, enabled = true, groups = {"regression"})
    @Owner("Sarah Abdelatif")
    public void Regression_Scan_Arabic_Phonetics() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify Scan Arabic Phonetics"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Regression_Scan_Arabic_Phonetics"));

            screeningServicesDelegate.excuteQueryAdmin(GeneralConstants.DB_QUERY[14]);
            screeningServicesDelegate.excuteQueryAdmin(GeneralConstants.DB_QUERY[12]);
            screeningServicesDelegate.excuteQueryAdmin(GeneralConstants.DB_QUERY[10]);

            EngineTuning engineTuning = engineTuningsTestData();
            String actualResult = engineTuningControl.engineSettingsIgnoreMatches(driver, engineTuning);
            Assert.assertTrue(actualResult.contains(" Note that some of the changed parameters require the SafeWatch Screening solution to be restarted.")
                            || actualResult.contains("Nothing has been changed.")
                    , "Failed to add engine setting.");

            commonTestMethods.restartNewArch();


            EnList enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.COMMON_TEST_ZONE_004.getZone().getDisplayName());

            Format format = eastnets.screening.entity.Common.FORMAT.IMPORT_ENTRY_NAMES_TYPE.get();
            format.setName("Format-" + randomizer.getInt());
            format.setZone(Common.ZONE.COMMON_TEST_ZONE_004.name());

            String filePath = System.getProperty("user.dir") + GeneralConstants.ARABIC_NAMES_ENTRY_LIST_FILE_PATH;
            commonTestMethods.addListSetAndimportEntries(driver,enlist,Common.PROFILE.FULL_RIGHT_004.getProfile(), Common.ZONE.COMMON_TEST_ZONE_004.getZone(),
                    filePath, format, null);


            filePath = System.getProperty("user.dir") + GeneralConstants.SCAN_ARABIC_NAMES_RESULTS_FILE_PATH;
            List<List<String>> data = csvHandler.readFromFile(filePath);
            for (int i = 1; i < data.size(); i++) {
                String entryName = data.get(i).get(4);
                log.info(entryName);
                nameScanControl.scan_Name(driver
                        , entryName
                        , Common.ZONE.COMMON_TEST_ZONE_004.getZone().getDisplayName()
                        , "50"
                        , true
                        , true
                        , true
                        , false);
                String rank = nameScanControl.get_detection_rank(driver);
                if (rank == null || rank.isEmpty())
                    rank = "no match";
                csvHandler.updateCSV(filePath,
                        rank, i, 5);
            }

            screeningServicesDelegate.excuteQueryAdmin(GeneralConstants.DB_QUERY[11]);
            commonTestMethods.restartNewArch();

            for (int i = 1; i < data.size(); i++) {
                String entryName = data.get(i).get(4);
                log.info(entryName);
                nameScanControl.scan_Name(driver
                        , entryName
                        , Common.ZONE.COMMON_TEST_ZONE_004.getZone().getDisplayName()
                        , "50"
                        , true
                        , true
                        , true
                        , false);
                String rank = nameScanControl.get_detection_rank(driver);
                if (rank == null || rank.isEmpty())
                    rank = "no match";
                csvHandler.updateCSV(filePath,
                        rank, i, 6);
            }

            screeningServicesDelegate.excuteQueryAdmin(GeneralConstants.DB_QUERY[12]);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 1, enabled = true, groups = {"regression"})
    @Owner("Sarah Abdelatif")
    public void Regression_Scan_Russian_Phonetics() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify Scan Russian Phonetics"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Regression_Scan_Russian_Phonetics"));

            screeningServicesDelegate.excuteQueryAdmin(GeneralConstants.DB_QUERY[14]);
            screeningServicesDelegate.excuteQueryAdmin(GeneralConstants.DB_QUERY[12]);
            screeningServicesDelegate.excuteQueryAdmin(GeneralConstants.DB_QUERY[10]);

            EngineTuning engineTuning = engineTuningsTestData();
            String actualResult = engineTuningControl.engineSettingsIgnoreMatches(driver, engineTuning);
            Assert.assertTrue(actualResult.contains(" Note that some of the changed parameters require the SafeWatch Screening solution to be restarted.")
                            || actualResult.contains("Nothing has been changed.")
                    , "Failed to add engine setting.");

            commonTestMethods.restartNewArch();


            EnList enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.COMMON_TEST_ZONE_004.getZone().getDisplayName());

            Format format = eastnets.screening.entity.Common.FORMAT.IMPORT_ENTRY_NAMES_TYPE.get();
            format.setName("Format-" + randomizer.getInt());
            format.setZone(Common.ZONE.COMMON_TEST_ZONE_004.name());

            String filePath = System.getProperty("user.dir") + GeneralConstants.RUSSIAN_NAMES_ENTRY_LIST_FILE_PATH;
            commonTestMethods.addListSetAndimportEntries(driver, enlist, Common.PROFILE.FULL_RIGHT_004.getProfile(), Common.ZONE.COMMON_TEST_ZONE_004.getZone(),
                    filePath, format, null);


            filePath = System.getProperty("user.dir") + GeneralConstants.SCAN_RUSSIAN_NAMES_RESULTS_FILE_PATH;
            List<List<String>> data = csvHandler.readFromFile(filePath);
            for (int i = 1; i < data.size(); i++) {
                String entryName = data.get(i).get(4);
                log.info(entryName);
                String massage = nameScanControl.scan_Name(driver
                        , entryName
                        , Common.ZONE.COMMON_TEST_ZONE_004.getZone().getDisplayName()
                        , "50"
                        , true
                        , true
                        , true
                        , false);
                String rank;
                if (massage.contains("No detection found!"))
                    rank = "no match";
                else {
                    rank = nameScanControl.get_detection_rank(driver);
                    if (rank == null || rank.isEmpty())
                        rank = "no match";
                }
                csvHandler.updateCSV(filePath,
                        rank, i, 5);
            }

            screeningServicesDelegate.excuteQueryAdmin(GeneralConstants.DB_QUERY[13]);
            commonTestMethods.restartNewArch();

            for (int i = 1; i < data.size(); i++) {
                String entryName = data.get(i).get(4);
                log.info(entryName);
                String massage = nameScanControl.scan_Name(driver
                        , entryName
                        , Common.ZONE.COMMON_TEST_ZONE_004.getZone().getDisplayName()
                        , "50"
                        , true
                        , true
                        , true
                        , false);
                String rank;
                if (massage.contains("No detection found!"))
                    rank = "no match";
                else {
                    rank = nameScanControl.get_detection_rank(driver);
                    if (rank == null || rank.isEmpty())
                        rank = "no match";
                }
                csvHandler.updateCSV(filePath,
                        rank, i, 6);
            }

            screeningServicesDelegate.excuteQueryAdmin(GeneralConstants.DB_QUERY[14]);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @AfterMethod(alwaysRun = true)
    @Step("Logout")
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    public EngineTuning engineTuningsTestData() throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("engineTuningsTD");
        EngineTuning[] engineTuning = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), EngineTuning[].class);
        return engineTuning[0];

    }
}
