package eastnets.screening.AdvancedSettingsTests;

import com.paypal.selion.platform.dataprovider.DataProviderFactory;
import com.paypal.selion.platform.dataprovider.DataResource;
import com.paypal.selion.platform.dataprovider.SeLionDataProvider;
import com.paypal.selion.platform.dataprovider.impl.InputStreamResource;
import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import core.util.CSVHandler;
import core.util.Log;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.common.gui.Navigation;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.AdvancedSettingsControls.AdvancedSettingsControl;
import eastnets.screening.control.AdvancedSettingsControls.EngineTuningControl;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.EngineTuning;
import eastnets.screening.entity.Format;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import io.qameta.allure.Step;
import org.testng.Assert;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.List;


public class EngineTuningTests extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final AdvancedSettingsControl advancedSettingsControl = new AdvancedSettingsControl();
    private final EngineTuningControl engineTuningControl = new EngineTuningControl();
    private final NameScanControl nameScanControl = new NameScanControl();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final Log log = new Log();

    private RemoteWebDriver driver;
    private final CSVHandler csvHandler = new CSVHandler();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();

    @BeforeClass()
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod()
    @Step("Login")
    public void login() {
        try {

            loginPage.login(driver,
                    Common.OPERATOR.FULL_RIGHT_2.getOperator().getLoginName(),
                    Common.OPERATOR.FULL_RIGHT_2.getOperator().getPassword());
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 1, enabled = true, dataProvider = "engineSettingsRegressionTestData", groups = {"regression"})
    @Owner("Sarah Abdelatif")
    public void scanIgnoreMatches(EngineTuning engineTunings) {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription(engineTunings.getTestCaseTitle()));
            lifecycle.updateTestCase(testResult -> testResult.setName(engineTunings.getTestCaseTitle()));

            SoftAssert softAssert = new SoftAssert();
            String filePath = System.getProperty("user.dir") + GeneralConstants.IGNORE_MATCHES_FILE_PATH;
            EnList enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.COMMON_TEST_ZONE_010.getZone().getDisplayName());
            enlist.setName("IgnoreMatches");

            screeningServicesDelegate.deleteLinkBtwBlackListAndListSet(enlist.getZoneName(), enlist.getName());
            screeningServicesDelegate.deleteLinkBtwBlackListAndGoodGuy(enlist.getZoneName(), enlist.getName());
            screeningServicesDelegate.deleteBlackList(enlist.getName());

            commonTestMethods.importBListAndLinkToNewListSet(driver, enlist, Common.PROFILE.FULL_RIGHT_010.getProfile().getName(), filePath);


            Navigation.ADVANCED_SETTINGS.navigate(driver);
            advancedSettingsControl.navigateToEngineTuning(driver);
            String actualResult = engineTuningControl.engineSettingsIgnoreMatches(driver, engineTunings);
            Assert.assertTrue(actualResult.contains(" Note that some of the changed parameters require the SafeWatch Screening solution to be restarted.")
                            || actualResult.contains("Nothing has been changed.")
                    , "Failed to add engine setting.");

            commonTestMethods.restartNewArch();

            Navigation.SCAN_MANAGER.navigate(driver);

            for (int i = 0; i < engineTunings.getScannedNameList().length; i++) {
                String detectionStatus = nameScanControl.scan_Name(driver
                        , engineTunings.getScannedNameList()[i]
                        , Common.ZONE.DEFAULT_ZONE.getZone().getDisplayName()
                        , null
                        , true
                        , true
                        , true
                        , false);
                softAssert.assertEquals(detectionStatus
                        , engineTunings.getDetectionStatusList()[i]
                        , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
                log.info("Scanned Name :" + engineTunings.getScannedNameList()[i]);
                log.info("Detection Status  :" + detectionStatus);

            }
            engineTuningControl.engineSettingsIgnoreMatches(driver, engineTunings);
            softAssert.assertAll();

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 2, enabled = true, dataProvider = "engineTuningsTestData", groups = {"regression"})
    @Owner("Sarah Abdelatif")
    public void scanIslamicNames(EngineTuning engineTunings) {

        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            engineTunings.setTestCaseTitle("Regression_Scan_IgnoreMatches_04");
            engineTunings.setScriptName("Scan comparision - Islamic Names");
            lifecycle.updateTestCase(testResult -> testResult.setDescription(engineTunings.getTestCaseTitle()));
            lifecycle.updateTestCase(testResult -> testResult.setName(engineTunings.getTestCaseTitle()));

            EnList enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.COMMON_TEST_ZONE_010.getZone().getDisplayName());

            Format format = eastnets.screening.entity.Common.FORMAT.IMPORT_ENTRY_NAMES_TYPE.get();
            format.setName("Format-" + randomizer.getInt());
            format.setZone(Common.ZONE.COMMON_TEST_ZONE_010.name());

            String filePath = System.getProperty("user.dir") + GeneralConstants.ISLAMIC_NAMES_ENTRY_LIST_FILE_PATH;
            commonTestMethods.addListSetAndimportEntries(driver, enlist, Common.PROFILE.FULL_RIGHT_010.getProfile(), Common.ZONE.COMMON_TEST_ZONE_010.getZone(),
                    filePath, format, null);

            //connection_Pool
            screeningServicesDelegate.updateConfiguration("ENHANCED_GLUEDWORDS");
            screeningServicesDelegate.updateConfiguration("ARABIC_PHONETICS_ON");
            screeningServicesDelegate.updateConfiguration("RUSSIAN_PHONETICS_ON");

            engineTunings.setDetectSwiftBic("Always");
            advancedSettingsControl.navigateToEngineTuning(driver);
            String actualResult = engineTuningControl.engineSettings(driver, engineTunings);
            Assert.assertTrue(actualResult.contains(" Note that some of the changed parameters require the SafeWatch Screening solution to be restarted.")
                            || actualResult.contains("Nothing has been changed.")
                    , "Failed to add engine setting.");

            engineTunings.setShowSettings("Islamic Names");
            engineTunings.setEnableIslamicNames(true);
            engineTuningControl.enableIslamicNames(driver, engineTunings);
            commonTestMethods.restartNewArch();

            filePath = System.getProperty("user.dir") + GeneralConstants.ISLAMIC_NAMES_FILE_PATH;
            List<List<String>> data = csvHandler.readFromFile(filePath);
            for (int i = 1; i < data.size(); i++) {
                String entryName = data.get(i).get(4);
                log.info(entryName);
                nameScanControl.scan_Name(driver
                        , entryName
                        , Common.ZONE.COMMON_TEST_ZONE_010.getZone().getDisplayName()
                        , "50"
                        , true
                        , true
                        , true
                        , false);
                String rank = nameScanControl.get_detection_rank(driver);
                if (rank == null || rank.isEmpty())
                    rank = "no match";
                csvHandler.updateCSV(filePath,
                        rank, i, 5);
            }

            engineTunings.setEnableIslamicNames(false);
            engineTuningControl.enableIslamicNames(driver, engineTunings);
            commonTestMethods.restartNewArch();

            for (int i = 1; i < data.size(); i++) {
                String entryName = data.get(i).get(4);
                log.info(entryName);
                nameScanControl.scan_Name(driver
                        , entryName
                        , Common.ZONE.COMMON_TEST_ZONE_010.getZone().getDisplayName()
                        , "50"
                        , true
                        , true
                        , true
                        , false);
                String rank = nameScanControl.get_detection_rank(driver);
                if (rank == null || rank.isEmpty())
                    rank = "no match";
                csvHandler.updateCSV(filePath,
                        rank, i, 6);
            }
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 3, enabled = true, dataProvider = "engineTuningsTestData", groups = {"regression"})
    @Owner("Sarah Abdelatif")
    public void scanNeutralWords(EngineTuning engineTunings) {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            engineTunings.setTestCaseTitle("Regression_Scan_IgnoreMatches_05");
            engineTunings.setScriptName("Scan comparision - Neutral Words");
            lifecycle.updateTestCase(testResult -> testResult.setDescription(engineTunings.getTestCaseTitle()));
            lifecycle.updateTestCase(testResult -> testResult.setName(engineTunings.getTestCaseTitle()));

            EnList enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.COMMON_TEST_ZONE_010.getZone().getDisplayName());

            Format format = eastnets.screening.entity.Common.FORMAT.IMPORT_ENTRY_NAMES_TYPE.get();
            format.setName("Format-" + randomizer.getInt());
            format.setZone(Common.ZONE.COMMON_TEST_ZONE_010.name());

            String filePath = System.getProperty("user.dir") + GeneralConstants.NEUTRAL_WORDS_ENTRY_LIST_FILE_PATH;
            commonTestMethods.addListSetAndimportEntries(driver,enlist, Common.PROFILE.FULL_RIGHT_010.getProfile(), Common.ZONE.COMMON_TEST_ZONE_010.getZone(),
                    filePath, format, null);

            //connection_Pool
            screeningServicesDelegate.updateConfiguration("ENHANCED_GLUEDWORDS");
            screeningServicesDelegate.updateConfiguration("ARABIC_PHONETICS_ON");
            screeningServicesDelegate.updateConfiguration("RUSSIAN_PHONETICS_ON");

            engineTunings.setDetectSwiftBic("Always");
            advancedSettingsControl.navigateToEngineTuning(driver);
            String actualResult = engineTuningControl.engineSettings(driver, engineTunings);
            Assert.assertTrue(actualResult.contains(" Note that some of the changed parameters require the SafeWatch Screening solution to be restarted.")
                            || actualResult.contains("Nothing has been changed.")
                    , "Failed to add engine setting.");

            commonTestMethods.restartNewArch();

            filePath = System.getProperty("user.dir") + GeneralConstants.NEUTRAL_WORDS_FILE_PATH;
            List<List<String>> data = csvHandler.readFromFile(filePath);
            for (int i = 1; i < data.size(); i++) {
                String entryName = data.get(i).get(4);
                log.info(entryName);
                nameScanControl.scan_Name(driver
                        , entryName
                        , Common.ZONE.COMMON_TEST_ZONE_010.getZone().getDisplayName()
                        , null
                        , true
                        , true
                        , true
                        , false);
                String rank = nameScanControl.get_detection_rank(driver);
                if (rank == null || rank.isEmpty())
                    rank = "no match";
                csvHandler.updateCSV(filePath,
                        rank, i, 6);
            }

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 4, enabled = true, dataProvider = "engineTuningsTestData", groups = {"regression"})
    @Owner("Sarah Abdelatif")
    public void scanSynonyms(EngineTuning engineTunings) {

        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            engineTunings.setTestCaseTitle("Regression_Scan_IgnoreMatches_06");
            engineTunings.setScriptName("Scan comparision - Synonyms");
            lifecycle.updateTestCase(testResult -> testResult.setDescription(engineTunings.getTestCaseTitle()));
            lifecycle.updateTestCase(testResult -> testResult.setName(engineTunings.getTestCaseTitle()));

            EnList enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.COMMON_TEST_ZONE_010.getZone().getDisplayName());

            Format format = eastnets.screening.entity.Common.FORMAT.IMPORT_ENTRY_NAMES_TYPE.get();
            format.setName("Format-" + randomizer.getInt());
            format.setZone(Common.ZONE.COMMON_TEST_ZONE_010.name());

            String filePath = System.getProperty("user.dir") + GeneralConstants.SYNONYMS_ENTRY_LIST_FILE_PATH;
            commonTestMethods.addListSetAndimportEntries(driver,enlist, Common.PROFILE.FULL_RIGHT_010.getProfile(), Common.ZONE.COMMON_TEST_ZONE_010.getZone(),
                    filePath, format, null);

            //connection_Pool
            screeningServicesDelegate.updateConfiguration("ENHANCED_GLUEDWORDS");
            screeningServicesDelegate.updateConfiguration("ARABIC_PHONETICS_ON");
            screeningServicesDelegate.updateConfiguration("RUSSIAN_PHONETICS_ON");


            engineTunings.setDetectSwiftBic("Always");
            advancedSettingsControl.navigateToEngineTuning(driver);
            String actualResult = engineTuningControl.engineSettings(driver, engineTunings);
            Assert.assertTrue(actualResult.contains(" Note that some of the changed parameters require the SafeWatch Screening solution to be restarted.")
                            || actualResult.contains("Nothing has been changed.")
                    , "Failed to add engine setting.");

            commonTestMethods.restartNewArch();
            filePath = System.getProperty("user.dir") + GeneralConstants.SYNONYMS_IGNORE_MATCHES_FILE_PATH;
            List<List<String>> data = csvHandler.readFromFile(filePath);
            for (int i = 1; i < data.size(); i++) {
                String entryName = data.get(i).get(4);
                log.info(entryName);
                nameScanControl.scan_Name(driver
                        , entryName
                        , Common.ZONE.COMMON_TEST_ZONE_010.getZone().getDisplayName()
                        , null
                        , true
                        , true
                        , true
                        , false);
                String rank = nameScanControl.get_detection_rank(driver);
                if (rank == null || rank.isEmpty())
                    rank = "no match";
                csvHandler.updateCSV(filePath,
                        rank, i, 6);

            }

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @AfterMethod()
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @DataProvider(name = "engineSettingsRegressionTestData")
    public Object[][] engineSettingsRegressionTestData() throws IOException {
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("engineTuningsRegressionSettings");
        DataResource resource =
                new InputStreamResource(new FileInputStream(System.getProperty("user.dir") + csvDataFilePath),
                        EngineTuning.class, "json");
        SeLionDataProvider dataProvider =
                DataProviderFactory.getDataProvider(resource);
        return dataProvider.getAllData();
    }

    @DataProvider(name = "engineTuningsTestData")
    public Object[][] engineTuningsTestData() throws IOException {
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("engineTuningsTD");
        DataResource resource =
                new InputStreamResource(new FileInputStream(System.getProperty("user.dir") + csvDataFilePath),
                        EngineTuning.class, "json");
        SeLionDataProvider dataProvider =
                DataProviderFactory.getDataProvider(resource);
        return dataProvider.getAllData();
    }
}
