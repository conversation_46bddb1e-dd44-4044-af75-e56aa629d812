package eastnets.screening.AdvancedSettingsTests;

import com.paypal.selion.platform.dataprovider.DataProviderFactory;
import com.paypal.selion.platform.dataprovider.DataResource;
import com.paypal.selion.platform.dataprovider.SeLionDataProvider;
import com.paypal.selion.platform.dataprovider.impl.InputStreamResource;
import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.common.gui.Navigation;
import eastnets.screening.control.AdvancedSettingsControls.AdvancedSettingsControl;
import eastnets.screening.control.AdvancedSettingsControls.FourEyesControl;
import eastnets.screening.control.AdvancedSettingsControls.InvestigatorsControl;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FourEyes;
import eastnets.screening.gui.advancedSettings.FourEyesSettings.FourEyesSettingsManager;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import org.testng.Assert;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.annotations.*;

import java.io.FileInputStream;
import java.io.IOException;

public class FourEyesTests extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final FourEyesControl fourEyesControl = new FourEyesControl();
    private final InvestigatorsControl investigatorsControl = new InvestigatorsControl();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();
    private final NameScanControl nameScanControl = new NameScanControl();
    private final AdvancedSettingsControl advancedSettingsControl = new AdvancedSettingsControl();
    private final FourEyesSettingsManager fourEyesSettingsManager = new FourEyesSettingsManager();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();

    private RemoteWebDriver driver;

    String checkerName = Common.OPERATOR.FOUR_EYES_02.getOperator().getLoginName();

    @BeforeClass()
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeClass( dependsOnMethods = "initializeDriver")
    public void beforeClass() {
        try {
            commonTestMethods.restartTomcat();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod()
    public void login() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver,
                    Common.OPERATOR.FOUR_EYES_01.getOperator().getLoginName(),
                    Common.OPERATOR.FOUR_EYES_01.getOperator().getPassword());
            investigatorsControl.assignInvestigatorRoleChecker(driver
                    , Common.OPERATOR.FOUR_EYES_01.getOperator().getLoginName()
                    , false);


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 0, enabled = true, dataProvider = "createFourEyesTestData", groups = {"regression"})
    @Owner("Sunil Kumar")
    public void lastInputPrevailsModeValidationByRelease(FourEyes fourEyes) {
        try {

            AllureLifecycle lifecycle = Allure.getLifecycle();
            fourEyes.setTestCaseTitle("* Verify that Last Input Prevails mode is functioning properly according to checker action when perform a Release action");
            lifecycle.updateTestCase(testResult -> testResult.setDescription(fourEyes.getTestCaseTitle()));
            lifecycle.updateTestCase(testResult -> testResult.setName(fourEyes.getTestCaseTitle()));

            EnList enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.FOUR_EYES_ZONE.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, enlist, Common.PROFILE.FOUR_EYES_PROFILE.getProfile().getName());
            fourEyes.setScannedName("Bahmanyar, Bahmanyar Morteza");
            enlist.getEntry().get(0).setName(fourEyes.getScannedName());
            commonTestMethods.createNewEntryWithTypeAndNames(driver, enlist);

            fourEyesControl.enableFourEyes(driver);
            fourEyesControl.enableDecisionModeLastInputPrevails(driver);

            investigatorsControl.assignInvestigatorRoleChecker(driver, checkerName, true);

            Assert.assertEquals(nameScanControl.scan_Name(driver
                            , fourEyes.getScannedName()
                            , enlist.getZoneName()
                            , "75", true
                            , true
                            , true
                            , false)
                    , "REPNEW"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            //takeDecisionAsRelease
            String detectionID = nameScanControl.get_detection_id(driver);
            String expectedResults = "1 detection(s) successfully modified!";
            Assert.assertNotNull(detectionID, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.search_by_id(driver, detectionID), "REPNEW", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.release_detection_4_eyes(driver, detectionID, "Test Message", checkerName), expectedResults, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            commonAction.logout(driver);
            driver.navigate().refresh();
            loginPage.login(driver,
                    checkerName,
                    Common.OPERATOR.FULL_RIGHT_2.getOperator().getPassword());

            Assert.assertNotNull(detectionID, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.search_by_id(driver, detectionID), "REPTR", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.release_detection_4_eyes(driver, detectionID, "Test Message", ""), expectedResults, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.get_first_listed_detection_status(driver), "REPFP", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 1, enabled = true, dataProvider = "createFourEyesTestData", groups = {"regression"})
    @Owner("Sunil Kumar")
    public void lastInputPrevailsModeValidationByBlock(FourEyes fourEyes) {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            fourEyes.setTestCaseTitle("Verify that Last Input Prevails mode is functioning properly according to checker action when perform a Block action");
            lifecycle.updateTestCase(testResult -> testResult.setDescription(fourEyes.getTestCaseTitle()));
            lifecycle.updateTestCase(testResult -> testResult.setName(fourEyes.getTestCaseTitle()));


            EnList enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.FOUR_EYES_ZONE.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, enlist, Common.PROFILE.FOUR_EYES_PROFILE.getProfile().getName());
            enlist.getEntry().get(0).setName(fourEyes.getScannedName());
            commonTestMethods.createNewEntryWithTypeAndNames(driver, enlist);

            fourEyesControl.enableFourEyes(driver);
            fourEyesControl.enableDecisionModeLastInputPrevails(driver);
            investigatorsControl.assignInvestigatorRoleChecker(driver, checkerName, true);

            Assert.assertEquals(nameScanControl.scan_Name(driver
                            , fourEyes.getScannedName()
                            , enlist.getZoneName()
                            , "75"
                            , true
                            , true
                            , true
                            , false)
                    , "REPNEW"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            //takeDecisionAsBlock
            String detectionID = nameScanControl.get_detection_id(driver);
            String expectedResults = "1 detection(s) successfully modified!";
            Assert.assertNotNull(detectionID, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.search_by_id(driver, detectionID), "REPNEW", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.block_detection_4_eyes(driver, detectionID, "Test Message", checkerName), expectedResults, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            commonAction.logout(driver);
            driver.navigate().refresh();
            loginPage.login(driver,
                    checkerName,
                    Common.OPERATOR.FOUR_EYES_02.getOperator().getPassword());

            Assert.assertNotNull(detectionID, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.search_by_id(driver, detectionID), "REPTB", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.block_detection(driver, detectionID, "Test Message"), expectedResults, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.get_first_listed_detection_status(driver), "REPRV", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }

    }


    @Test(priority = 2, enabled = true, dataProvider = "createFourEyesTestData", groups = {"regression"})
    //Testcase no 5 from sahi is migrated to Test case no 3
    @Owner("Sunil Kumar")
    public void pessimisticModeMakerTakeReleaseCheckerTakeBlockDecision(FourEyes fourEyes) {

        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            fourEyes.setTestCaseTitle("Verify that no error message that appears when Checker takes a Block action different than the Maker action on the Pessimistic Mode");
            lifecycle.updateTestCase(testResult -> testResult.setDescription(fourEyes.getTestCaseTitle()));
            lifecycle.updateTestCase(testResult -> testResult.setName(fourEyes.getTestCaseTitle()));

            EnList enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.FOUR_EYES_ZONE.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, enlist, Common.PROFILE.FOUR_EYES_PROFILE.getProfile().getName());
            fourEyes.setScannedName("AL QUBAYSI, Munir");
            enlist.getEntry().get(0).setName(fourEyes.getScannedName());
            commonTestMethods.createNewEntryWithTypeAndNames(driver, enlist);

            fourEyesControl.enableFourEyes(driver);
            fourEyesControl.enableDecisionModePessimistic(driver);
            Assert.assertEquals(investigatorsControl.assignInvestigatorRoleChecker(driver, checkerName, true), "Investigator Updated Successfully.", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Assert.assertEquals(nameScanControl.scan_Name(driver
                            , fourEyes.getScannedName()
                            , enlist.getZoneName()
                            , "75"
                            , true
                            , true
                            , true
                            , false)
                    , "REPNEW"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            //takeDecisionAsRelease
            String detectionID = nameScanControl.get_detection_id(driver);
            String expectedResults = "1 detection(s) successfully modified!";
            Assert.assertNotNull(detectionID, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.search_by_id(driver, detectionID), "REPNEW", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.release_detection_4_eyes(driver, detectionID, "Test Message", checkerName), expectedResults, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            commonAction.logout(driver);
            driver.navigate().refresh();
            loginPage.login(driver,
                    checkerName,
                    Common.OPERATOR.FOUR_EYES_02.getOperator().getPassword());

            Assert.assertNotNull(detectionID, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            fourEyes.setStatus("REPTR");
            Assert.assertEquals(detectionManagerControl.search_by_id(driver, detectionID), fourEyes.getStatus(), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            fourEyes.setStatus("REPRV");
            expectedResults = "1 detection(s) successfully modified!";
            Assert.assertEquals(detectionManagerControl.block_detection(driver, detectionID, "Test Message"), expectedResults, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.get_first_listed_detection_status(driver), fourEyes.getStatus(), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 3, enabled = true, dataProvider = "createFourEyesTestData", groups = {"regression"})
    //Testcase no 3 from sahi is migrated to Test case no 4
    /* ScriptName: Regression_4Eyes_03
     * Verify that "Pessimistic" mode is functioning properly when Checker and Maker are taking a Block action
     */
    @Owner("Sunil Kumar")
    public void pessimisticModeCheckerMakerTakeBlockDecision(FourEyes fourEyes) {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            fourEyes.setTestCaseTitle("Verify that Pessimistic mode is functioning properly when Checker and Maker are taking a Block action");
            lifecycle.updateTestCase(testResult -> testResult.setDescription(fourEyes.getTestCaseTitle()));
            lifecycle.updateTestCase(testResult -> testResult.setName(fourEyes.getTestCaseTitle()));

            EnList enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.FOUR_EYES_ZONE.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, enlist, Common.PROFILE.FOUR_EYES_PROFILE.getProfile().getName());
            fourEyes.setScannedName("HAMUDAT, Maki Mustafa");
            enlist.getEntry().get(0).setName(fourEyes.getScannedName());
            commonTestMethods.createNewEntryWithTypeAndNames(driver, enlist);
            //FoursEyes setting configurations
            fourEyesControl.enableFourEyes(driver);
            fourEyesControl.enableDecisionModePessimistic(driver);
            investigatorsControl.assignInvestigatorRoleChecker(driver, checkerName, true);
            Assert.assertEquals(nameScanControl.scan_Name(driver
                            , fourEyes.getScannedName()
                            , enlist.getZoneName()
                            , "75"
                            , true
                            , true
                            , true
                            , false)
                    , "REPNEW"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            //takeDecisionAsBlock
            String detectionID = nameScanControl.get_detection_id(driver);
            String expectedResults = "1 detection(s) successfully modified!";
            Assert.assertNotNull(detectionID, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.search_by_id(driver, detectionID), "REPNEW", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.block_detection_4_eyes(driver, detectionID, "Test Message", checkerName), expectedResults, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            commonAction.logout(driver);

            driver.navigate().refresh();
            loginPage.login(driver,
                    checkerName,
                    Common.OPERATOR.FOUR_EYES_02.getOperator().getPassword());

            Assert.assertNotNull(detectionID, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            fourEyes.setStatus("REPTB");
            Assert.assertEquals(detectionManagerControl.search_by_id(driver, detectionID), fourEyes.getStatus(), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            fourEyes.setStatus("REPRV");
            expectedResults = "1 detection(s) successfully modified!";
            Assert.assertEquals(detectionManagerControl.block_detection(driver, detectionID, "Test Message"), expectedResults, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.get_first_listed_detection_status(driver), fourEyes.getStatus(), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 4, enabled = true, dataProvider = "createFourEyesTestData", groups = {"regression"})
    @Owner("Sunil Kumar")
    public void errorMessageAfterDecision(FourEyes fourEyes) {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            fourEyes.setTestCaseTitle("Verify the error message that appears when Checker takes a Release action different than the Maker action on the Pessimistic Mode");
            lifecycle.updateTestCase(testResult -> testResult.setDescription(fourEyes.getTestCaseTitle()));
            lifecycle.updateTestCase(testResult -> testResult.setName(fourEyes.getTestCaseTitle()));


            EnList enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.FOUR_EYES_ZONE.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, enlist, Common.PROFILE.FOUR_EYES_PROFILE.getProfile().getName());
            fourEyes.setScannedName("AL-AWADI, Qaid Hussein");
            enlist.getEntry().get(0).setName(fourEyes.getScannedName());
            commonTestMethods.createNewEntryWithTypeAndNames(driver, enlist);

            //FoursEyes setting configurations
            Navigation.ADVANCED_SETTINGS.navigate(driver);
            advancedSettingsControl.navigateToFourEyesSettings(driver);
            fourEyesSettingsManager.enableFourEyes(driver);
            fourEyesSettingsManager.enableDecisionModePessimistic(driver);
            investigatorsControl.assignInvestigatorRoleChecker(driver, checkerName, true);
            Assert.assertEquals(nameScanControl.scan_Name(driver
                            , fourEyes.getScannedName()
                            , enlist.getZoneName()
                            , "75"
                            , true
                            , true
                            , true
                            , false)
                    , "REPNEW"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            //takeDecisionAsBlock
            String detectionID = nameScanControl.get_detection_id(driver);
            String expectedResults = "1 detection(s) successfully modified!";
            Assert.assertNotNull(detectionID, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.search_by_id(driver, detectionID), "REPNEW", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.block_detection_4_eyes(driver, detectionID, "Test Message", checkerName), expectedResults, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            commonAction.logout(driver);
            driver.navigate().refresh();
            loginPage.login(driver,
                    checkerName,
                    Common.OPERATOR.FOUR_EYES_02.getOperator().getPassword());

            Assert.assertNotNull(detectionID, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            fourEyes.setStatus("REPTB");
            Assert.assertEquals(detectionManagerControl.search_by_id(driver, detectionID), fourEyes.getStatus(), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            expectedResults = "Being in pessimistic mode, this detection will not be released because it was previously blocked by the first investigator.";
            detectionManagerControl.pessimistic_user_releases_detection(driver, detectionID);
            Assert.assertTrue(true, expectedResults);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 5, enabled = true, dataProvider = "createFourEyesTestData", groups = {"regression"})
    @Owner("Sunil Kumar")
    public void FoursColumnInDetectionManager(FourEyes fourEyes) {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            fourEyes.setTestCaseTitle("Verify that selecting Yes option from '4eyes detections' drop down list is functioning properly showing only the 4 eyes detections");
            lifecycle.updateTestCase(testResult -> testResult.setDescription(fourEyes.getTestCaseTitle()));
            lifecycle.updateTestCase(testResult -> testResult.setName(fourEyes.getTestCaseTitle()));


            EnList enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.FOUR_EYES_ZONE.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, enlist, Common.PROFILE.FOUR_EYES_PROFILE.getProfile().getName());
            EnList list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.FOUR_EYES_ZONE.getZone().getDisplayName());
            fourEyes.setScannedName("AL-AWADI, Qaid Hussein");
            enlist.getEntry().get(0).setName(fourEyes.getScannedName());
            commonTestMethods.createNewEntryWithTypeAndNames(driver, enlist);
            Navigation.ADVANCED_SETTINGS.navigate(driver);
            advancedSettingsControl.navigateToFourEyesSettings(driver);
            fourEyesSettingsManager.disableFourEyes(driver);

            Assert.assertEquals(nameScanControl.scan_Name(driver
                            , fourEyes.getScannedName()
                            , enlist.getZoneName()
                            , "75"
                            , true
                            , true
                            , true
                            , false)
                    , "REPNEW"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            String detectionID = nameScanControl.get_detection_id(driver);
            //Selecting 4 eyes column
            detectionManagerControl.Customize_search_columns(driver, "4 Eyes");
            detectionManagerControl.select_four_eyes_option(driver, "No");
            detectionManagerControl.showGPITableField(driver, "4-Eyes");
            detectionManagerControl.select_four_eyes_option(driver, "No");
            Assert.assertEquals(detectionManagerControl.get_field_value_by_column_number(driver, detectionID, 24)
                    , "No"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            Navigation.ADVANCED_SETTINGS.navigate(driver);
            advancedSettingsControl.navigateToFourEyesSettings(driver);
            fourEyesSettingsManager.enableFourEyes(driver);
            Assert.assertEquals(nameScanControl.scan_Name(driver
                            , fourEyes.getScannedName()
                            , enlist.getZoneName()
                            , "75"
                            , true
                            , true
                            , true
                            , false)
                    , "REPNEW"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            detectionID = nameScanControl.get_detection_id(driver);
            detectionManagerControl.select_four_eyes_option(driver, "Yes");

            Assert.assertEquals(detectionManagerControl.get_field_value_by_column_number(driver, detectionID, 24)
                    , "Yes"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            Navigation.ADVANCED_SETTINGS.navigate(driver);
            advancedSettingsControl.navigateToFourEyesSettings(driver);
            fourEyesSettingsManager.disableFourEyes(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 6, enabled = true, dataProvider = "createFourEyesTestData", groups = {"regression"})
    @Owner("Sunil Kumar")
    public void TransferDecision(FourEyes fourEyes) {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            fourEyes.setTestCaseTitle("Checker 2 is taking an action went through Maker 1 from two different levels by transferring the action from Maker 1 - Checker 1 - Maker 2 - Checker 2");
            lifecycle.updateTestCase(testResult -> testResult.setDescription(fourEyes.getTestCaseTitle()));
            lifecycle.updateTestCase(testResult -> testResult.setName(fourEyes.getTestCaseTitle()));


            EnList enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.FOUR_EYES_ZONE.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, enlist, Common.PROFILE.FOUR_EYES_PROFILE.getProfile().getName());
            fourEyes.setScannedName("AL-AWADI, Qaid Hussein");
            enlist.getEntry().get(0).setName(fourEyes.getScannedName());
            enlist.getEntry().get(0).setFirstName(null);

            commonTestMethods.createNewEntryWithTypeAndNames(driver, enlist);

            Navigation.ADVANCED_SETTINGS.navigate(driver);
            advancedSettingsControl.navigateToFourEyesSettings(driver);
            investigatorsControl.assignInvestigatorRoleChecker(driver, "operator-01", true);
            investigatorsControl.assignInvestigatorRoleChecker(driver, "operator-05", true);
            advancedSettingsControl.navigateToFourEyesSettings(driver);
            fourEyesSettingsManager.enableFourEyes(driver);
            fourEyesSettingsManager.enableDecisionModePessimistic(driver);

            Assert.assertEquals(nameScanControl.scan_Name(driver
                            , fourEyes.getScannedName()
                            , enlist.getZoneName()
                            , "75"
                            , true
                            , true
                            , true
                            , false)
                    , "REPNEW"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            String detectionID = nameScanControl.get_detection_id(driver);
            System.out.println(detectionID);
            String expectedResults = "1 detection(s) successfully modified!";
            Assert.assertNotNull(detectionID, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.search_by_id(driver, detectionID), "REPNEW", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.release_detection_4_eyes(driver, detectionID, "Test Message", Common.OPERATOR.FULL_RIGHT_1.getOperator().getLoginName()), expectedResults, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            commonAction.logout(driver);
            driver.navigate().refresh();
            loginPage.login(driver,
                    Common.OPERATOR.FULL_RIGHT_1.getOperator().getLoginName(),
                    Common.OPERATOR.FULL_RIGHT_1.getOperator().getPassword());

            detectionManagerControl.assignDetection(driver, detectionID, Common.OPERATOR.FULL_RIGHT_3.getOperator().getLoginName(), "Assign detection to another operator checker : operator-03");

            commonAction.logout(driver);
            driver.navigate().refresh();
            loginPage.login(driver,
                    Common.OPERATOR.FULL_RIGHT_3.getOperator().getLoginName(),
                    Common.OPERATOR.FULL_RIGHT_3.getOperator().getPassword());
            Assert.assertEquals(detectionManagerControl.search_by_id(driver, detectionID), "REPINV", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.release_detection_4_eyes(driver, detectionID, "Test Message", Common.OPERATOR.FULL_RIGHT_5.getOperator().getLoginName()), expectedResults, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            commonAction.logout(driver);
            driver.navigate().refresh();
            loginPage.login(driver,
                    Common.OPERATOR.FULL_RIGHT_5.getOperator().getLoginName(),
                    Common.OPERATOR.FULL_RIGHT_5.getOperator().getPassword());
            Assert.assertEquals(detectionManagerControl.search_by_id(driver, detectionID), "REPTR", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.release_detection_4_eyes(driver, detectionID, "Test Message", ""), expectedResults, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 7, enabled = true, dataProvider = "createFourEyesTestData", groups = {"regression"})
    @Owner("Sunil Kumar")
    public void checkerTakesDecisionFromDifferentMakersGroup(FourEyes fourEyes) {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            fourEyes.setTestCaseTitle("Verify that checker is able to take a Block action on a detection from Maker from two different groups");
            lifecycle.updateTestCase(testResult -> testResult.setDescription(fourEyes.getTestCaseTitle()));
            lifecycle.updateTestCase(testResult -> testResult.setName(fourEyes.getTestCaseTitle()));


            EnList enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.FOUR_EYES_ZONE.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, enlist, Common.PROFILE.FOUR_EYES_PROFILE.getProfile().getName());
            EnList list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.FOUR_EYES_ZONE.getZone().getDisplayName());
            fourEyes.setScannedName("AL-AWADI, Qaid Hussein");
            enlist.getEntry().get(0).setName(fourEyes.getScannedName());
            commonTestMethods.createNewEntryWithTypeAndNames(driver, enlist);
            String checker = Common.OPERATOR.FULL_RIGHT_8.getOperator().getLoginName();

            fourEyesControl.enableFourEyes(driver);
            fourEyesControl.enableDecisionModePessimistic(driver);
            investigatorsControl.assignInvestigatorRoleChecker(driver, checker, true);

            Assert.assertEquals(nameScanControl.scan_Name(driver
                            , fourEyes.getScannedName()
                            , enlist.getZoneName()
                            , "75"
                            , true
                            , true
                            , true
                            , false)
                    , "REPNEW"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            String detectionID = nameScanControl.get_detection_id(driver);
            System.out.println(detectionID);
            String expectedResults = "1 detection(s) successfully modified!";
            Assert.assertEquals(detectionManagerControl.block_detection_4_eyes(driver, detectionID, "Test Message", checker), expectedResults, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            //2nd scan
            Assert.assertEquals(nameScanControl.scan_Name(driver
                            , fourEyes.getScannedName()
                            , enlist.getZoneName()
                            , "75"
                            , true
                            , false
                            , false
                            , true)
                    , "REPNEW"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            String detectionID2 = nameScanControl.get_detection_id(driver);
            Assert.assertNotNull(detectionID2, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.release_detection_4_eyes(driver, detectionID2, "Test Message", checker), expectedResults, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            commonAction.logout(driver);
            driver.navigate().refresh();
            loginPage.login(driver,
                    checker,
                    Common.OPERATOR.FULL_RIGHT_8.getOperator().getPassword());
            detectionManagerControl.search_by_id(driver, detectionID);
            detectionManagerControl.block_detection(driver, detectionID, "Test Message");
            detectionManagerControl.search_by_id(driver, detectionID2);
            detectionManagerControl.release_detection_4_eyes(driver, detectionID2, "Test Message", "");
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod()
    public void logout() {
        try {
            driver.navigate().refresh();
            fourEyesControl.disableFourEyes(driver);
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @DataProvider(name = "createFourEyesTestData")
    public Object[][] createFourEyesTestData() throws IOException {
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("createFourEyesConfig");
        DataResource resource =
                new InputStreamResource(new FileInputStream(System.getProperty("user.dir") + csvDataFilePath),
                        FourEyes.class, "json");
        SeLionDataProvider dataProvider =
                DataProviderFactory.getDataProvider(resource);
        return dataProvider.getAllData();

    }
}
