package eastnets.screening.AdvancedSettingsTests;

import com.paypal.selion.platform.dataprovider.DataProviderFactory;
import com.paypal.selion.platform.dataprovider.DataResource;
import com.paypal.selion.platform.dataprovider.SeLionDataProvider;
import com.paypal.selion.platform.dataprovider.impl.InputStreamResource;
import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.AdvancedSettingsControls.BusinessLogicControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.GeneralSettings;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import java.io.FileInputStream;
import java.io.IOException;

public class BusinessLogicTests extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final BusinessLogicControl businessLogicControl = new BusinessLogicControl();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();

    private RemoteWebDriver driver;

    @BeforeClass()
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod()
    public void login() {
        try {
            loginPage.login(driver,
                    Common.OPERATOR.DeFAULT_ZONE_OPERATOR_1.getOperator().getLoginName(),
                    Common.OPERATOR.DeFAULT_ZONE_OPERATOR_1.getOperator().getPassword());
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 0, enabled = true, dataProvider = "createBusinessLogicData", groups = {"regression"})
    @Owner("Sunil Kumar")
    public void maxNumberOfCharacters(GeneralSettings generalSettings) throws IOException {
        try {
            AllureLifecycle currentLifecycle = Allure.getLifecycle();
            currentLifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that the Maximum number of characters displayed in the detection viewer's data columns text box is functioning properly when the value is set to Default "));
            currentLifecycle.updateTestCase(testResult -> testResult.setName("Verify that the Maximum number of characters displayed in the detection viewer's data columns text box is functioning properly when the value is set to Default "));


            EnList enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.DEFAULT_ZONE.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, enlist, Common.PROFILE.DEFAULT_PROFILE.getProfile().getName());
            String addEntity = "HAMUDAT, Maki Mustafa";
            enlist.getEntry().get(0).setName(addEntity);
            businessLogicControl.alertsDetectionsCommentMode(driver, generalSettings);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }

    }

    @Test(priority = 1, enabled = true, dataProvider = "createBusinessLogicData", groups = {"regression"})
    @Owner("Sunil Kumar")
    public void NumberoflastnotesSettings(GeneralSettings generalSettings) {
        try {
            AllureLifecycle currentLifecycle = Allure.getLifecycle();
            currentLifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that the Number of last notes option is functioning properly"));
            currentLifecycle.updateTestCase(testResult -> testResult.setName("Verify that the Number of last notes option is functioning properly"));

            EnList enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.DEFAULT_ZONE.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, enlist, Common.PROFILE.DEFAULT_PROFILE.getProfile().getName());
            String addEntity = "HAMUDAT, Maki Mustafa";
            enlist.getEntry().get(0).setName(addEntity);
            businessLogicControl.alertsDetectionsCommentMode(driver, generalSettings);


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }

    }

    @AfterMethod()
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @DataProvider(name = "createBusinessLogicData")
    public Object[][] createBusinessLogicData() throws IOException {
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("businessLogicAppearanceSettings");
        DataResource resource =
                new InputStreamResource(new FileInputStream(System.getProperty("user.dir") + csvDataFilePath),
                        GeneralSettings.class, "json");
        SeLionDataProvider dataProvider =
                DataProviderFactory.getDataProvider(resource);
        return dataProvider.getAllData();


    }

}
