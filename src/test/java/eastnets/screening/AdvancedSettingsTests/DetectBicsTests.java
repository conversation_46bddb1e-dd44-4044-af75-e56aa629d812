package eastnets.screening.AdvancedSettingsTests;

import com.fasterxml.jackson.databind.ObjectMapper;
import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import core.util.Randomizer;
import core.util.TextFilesHandler;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.AdvancedSettingsControls.EngineTuningControl;
import eastnets.screening.control.AdvancedSettingsControls.FourEyesControl;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.control.FormatManagerControl;
import eastnets.screening.control.listManager.BlackListControl;
import eastnets.screening.control.listManager.ListExplorerControl;
import eastnets.screening.control.listManager.ListSetControl;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.EngineTuning;
import eastnets.screening.entity.FileScan;
import eastnets.screening.entity.Format;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import io.qameta.allure.Step;
import org.testng.Assert;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeGroups;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.io.File;
import java.io.IOException;

public class DetectBicsTests extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final EngineTuningControl engineTuningControl = new EngineTuningControl();
    private final FourEyesControl fourEyesControl = new FourEyesControl();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();
    private final FormatManagerControl formatManagerControl = new FormatManagerControl();
    private final BlackListControl blackListControl = new BlackListControl();
    private final ListSetControl listSetControl = new ListSetControl();
    private final ListExplorerControl listExplorerControl = new ListExplorerControl();
    private final ResultManagerControl resultManagerControl = new ResultManagerControl();
    private final TextFilesHandler textFilesHandler = new TextFilesHandler();
    private final Randomizer randomizer = new Randomizer();

    // Instance variable for CommonTestMethods
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();

    private RemoteWebDriver driver;

    EngineTuning engineTuning = new EngineTuning();
    EnList enlist = new EnList();

    @BeforeClass()
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod()
    @Step("Login")
    public void login() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver,
                    Common.OPERATOR.FULL_RIGHT_3.getOperator().getLoginName(),
                    Common.OPERATOR.FULL_RIGHT_3.getOperator().getPassword());
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeGroups( value = {"detect_Bics=Always"})
    @Step("Regression_DetectBics_01_Pre_Condations")
    public void Regression_DetectBics_01_Pre_Condations() {
        try {

            loginPage.login(driver,
                    Common.OPERATOR.FULL_RIGHT_3.getOperator().getLoginName(),
                    Common.OPERATOR.FULL_RIGHT_3.getOperator().getPassword());

            screeningServicesDelegate.deleteBics();
            screeningServicesDelegate.excuteQuerSFP(GeneralConstants.DB_QUERY[5]);
            screeningServicesDelegate.excuteQuerSFP(GeneralConstants.DB_QUERY[6]);

            String filePath = GeneralConstants.OFC_FILE_PATH + "/AllCountries.ofc";
            enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.COMMON_TEST_ZONE_003.getZone().getDisplayName());
            enlist.setName("AllCountries");

            screeningServicesDelegate.deleteLinkBtwBlackListAndListSet(enlist.getZoneName(), enlist.getName());
            screeningServicesDelegate.deleteLinkBtwBlackListAndGoodGuy(enlist.getZoneName(), enlist.getName());
            screeningServicesDelegate.deleteBlackList(enlist.getName());

            commonTestMethods.importBListAndLinkToNewListSet(driver, enlist, Common.PROFILE.FULL_RIGHT_003.getProfile().getName(), filePath);

            fourEyesControl.disableFourEyes(driver);

            engineTuning = engineTuningsTestData();
            String actualResult = engineTuningControl.engineSettingsIgnoreMatches(driver, engineTuning);
            Assert.assertTrue(actualResult.contains(" Note that some of the changed parameters require the SafeWatch Screening solution to be restarted.")
                            || actualResult.contains("Nothing has been changed.")
                    , "Failed to add engine setting.");

            commonTestMethods.restartNewArch();

            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod()
    @Step("Cleanup")
    public void cleanup() {
        try {
            // Cleanup is handled by BaseTest lifecycle management
            // No additional cleanup needed for instance variables
        } finally {
            // Ensure proper cleanup in case of exceptions
        }
    }

    @BeforeGroups( value = {"detect_Bics=Never"})
    @Step("Regression_DetectBics_02_Pre_Condations")
    public void Regression_DetectBics_02_Pre_Condations() {
        try {
            loginPage.login(driver,
                    Common.OPERATOR.FULL_RIGHT_3.getOperator().getLoginName(),
                    Common.OPERATOR.FULL_RIGHT_3.getOperator().getPassword());

            screeningServicesDelegate.deleteBics();
            screeningServicesDelegate.excuteQuerSFP(GeneralConstants.DB_QUERY[5]);
            screeningServicesDelegate.excuteQuerSFP(GeneralConstants.DB_QUERY[6]);

            String filePath = GeneralConstants.OFC_FILE_PATH + "/AllCountries.ofc";
            enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.COMMON_TEST_ZONE_003.getZone().getDisplayName());
            enlist.setName("AllCountries");

            screeningServicesDelegate.deleteLinkBtwBlackListAndListSet(enlist.getZoneName(), enlist.getName());
            screeningServicesDelegate.deleteLinkBtwBlackListAndGoodGuy(enlist.getZoneName(), enlist.getName());
            screeningServicesDelegate.deleteBlackList(enlist.getName());

            commonTestMethods.importBListAndLinkToNewListSet(driver, enlist, Common.PROFILE.FULL_RIGHT_003.getProfile().getName(), filePath);

            fourEyesControl.disableFourEyes(driver);

            engineTuning = engineTuningsTestData();
            engineTuning.setDetectSwiftBic("Never");
            String actualResult = engineTuningControl.engineSettingsIgnoreMatches(driver, engineTuning);
            Assert.assertTrue(actualResult.contains(" Note that some of the changed parameters require the SafeWatch Screening solution to be restarted.")
                            || actualResult.contains("Nothing has been changed.")
                    , "Failed to add engine setting.");

            commonTestMethods.restartNewArch();

            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeGroups( value = {"detect_Bics=SWIFT Format Only"})
    @Step("Regression_DetectBics_02_Pre_Condations")
    public void Regression_DetectBics_03_Pre_Condations() {
        try {

            loginPage.login(driver,
                    Common.OPERATOR.FULL_RIGHT_3.getOperator().getLoginName(),
                    Common.OPERATOR.FULL_RIGHT_3.getOperator().getPassword());

            screeningServicesDelegate.deleteBics();
            screeningServicesDelegate.excuteQuerSFP(GeneralConstants.DB_QUERY[5]);
            screeningServicesDelegate.excuteQuerSFP(GeneralConstants.DB_QUERY[6]);

            String filePath = GeneralConstants.OFC_FILE_PATH + "/AllCountries.ofc";
            enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.COMMON_TEST_ZONE_003.getZone().getDisplayName());
            enlist.setName("AllCountries");

            screeningServicesDelegate.deleteLinkBtwBlackListAndListSet(enlist.getZoneName(), enlist.getName());
            screeningServicesDelegate.deleteLinkBtwBlackListAndGoodGuy(enlist.getZoneName(), enlist.getName());
            screeningServicesDelegate.deleteBlackList(enlist.getName());

            commonTestMethods.importBListAndLinkToNewListSet(driver, enlist, Common.PROFILE.FULL_RIGHT_003.getProfile().getName(), filePath);

            fourEyesControl.disableFourEyes(driver);

            engineTuning = engineTuningsTestData();
            engineTuning.setDetectSwiftBic("SWIFT Format Only");
            String actualResult = engineTuningControl.engineSettingsIgnoreMatches(driver, engineTuning);
            Assert.assertTrue(actualResult.contains(" Note that some of the changed parameters require the SafeWatch Screening solution to be restarted.")
                            || actualResult.contains("Nothing has been changed.")
                    , "Failed to add engine setting.");

            commonTestMethods.restartNewArch();

            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 0, enabled = true, groups = {"regression", "detect_Bics=Always"})
    @Owner("Sarah Abdelatif")
    public void Regression_DetectBics_01_RJE() {
        try {
            AllureLifecycle currentLifecycle = Allure.getLifecycle();
            currentLifecycle.updateTestCase(testResult -> testResult.setDescription("Verify returned matches when Detect_Bics=Always with Swift RJE scanned format"));
            currentLifecycle.updateTestCase(testResult -> testResult.setName("Regression_DetectBics_01"));

            var Highlighted_Text = "ANDPFRP1";


            Allure.step("Verify returned matches when Detect_Bics=Always with Swift RJE scanned format");
            String filePath = GeneralConstants.RJE_BICS_FILE_PATH;
            FileScan fileScan = commonTestMethods.getFileScanData(filePath);
            String detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, "FRANCE")
                    , Highlighted_Text
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 1, enabled = true, groups = {"regression", "detect_Bics=Always"})
    @Owner("Sarah Abdelatif")
    public void Regression_DetectBics_01_Generic_Txt() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify returned matches when Detect_Bics=Always with Generic Txt scanned format"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Regression_DetectBics_01"));

            var Highlighted_Text = "ANDPFRP1";

            String filePath = GeneralConstants.FILE_SCANNER_FILE_PATH;
            textFilesHandler.writeToFile(filePath, Highlighted_Text + "XXX");
            FileScan fileScan = commonTestMethods.getFileScanData(filePath);
            fileScan.setFormat("Generic Text");
            fileScan.setResult("All records");
            commonTestMethods.scanFile(driver, fileScan);
            Assert.assertEquals(resultManagerControl.get_detection_status(driver)
                    , "Clean"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 2, enabled = true, groups = {"regression", "detect_Bics=Always"})
    @Owner("Sarah Abdelatif")
    public void Regression_DetectBics_01_XML_Custom_format() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify returned matches when Detect_Bics=Always with XML Custom format"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Regression_DetectBics_01"));

            var Highlighted_Text = "ANDPFRP1";


            Format format = eastnets.screening.entity.Common.FORMAT.SCAN_XML_WITH_BIC_FORMAT.get();
            format.setName("Format-" + randomizer.getInt());
            format.setZone(Common.ZONE.COMMON_TEST_ZONE_003.getZone().getDisplayName());
            Assert.assertNull(formatManagerControl.AddNewFormat(driver, format), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG + " while creating new format.");
            String filePath = GeneralConstants.XML_SCANNER_FILE_PATH;
            FileScan fileScan = commonTestMethods.getFileScanData(filePath);
            fileScan.setFormat(format.getName());
            String detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, "FRANCE")
                    , Highlighted_Text
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 3, enabled = true, groups = {"regression", "detect_Bics=Always"})
    @Owner("Sarah Abdelatif")
    public void Regression_DetectBics_01_XML_Custom_format_Not_Bic() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify returned matches when Detect_Bics=Always with XML Custom format when defined field is not BIC Code"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Regression_DetectBics_01"));

            var Highlighted_Text = "ANDPFRP1";


            Format format2 = eastnets.screening.entity.Common.FORMAT.SCAN_XML_WITH_FULLNAME_FORMAT.get();
            format2.setName("Format-" + randomizer.getInt());
            format2.setZone(Common.ZONE.COMMON_TEST_ZONE_003.getZone().getDisplayName());
            Assert.assertNull(formatManagerControl.AddNewFormat(driver, format2), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG + " while creating new format.");
            String filePath = GeneralConstants.XML_SCANNER_FILE_PATH;
            FileScan fileScan = commonTestMethods.getFileScanData(filePath);
            fileScan.setFormat(format2.getName());
            fileScan.setResult("All records");
            commonTestMethods.scanFile(driver, fileScan);
            Assert.assertEquals(resultManagerControl.get_detection_status(driver)
                    , "Clean"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 4, enabled = true, groups = {"regression", "detect_Bics=Always"})
    @Owner("Sarah Abdelatif")
    public void Regression_DetectBics_01_CSV_Custom_format() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify returned matches when Detect_Bics=Always with CSV Custom format"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Regression_DetectBics_01"));

            var Highlighted_Text = "ANDPFRP1";

            Format bicsCsvFormat = eastnets.screening.entity.Common.FORMAT.SCAN_CSV_WITH_BIC_FORMAT.get();
            bicsCsvFormat.setName("Format-" + randomizer.getInt());
            bicsCsvFormat.setZone(Common.ZONE.COMMON_TEST_ZONE_003.getZone().getDisplayName());
            Assert.assertNull(formatManagerControl.AddNewFormat(driver, bicsCsvFormat), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG + " while creating new format.");
            String filePath = GeneralConstants.FILE_SCANNER_FILE_PATH;
            FileScan fileScan = commonTestMethods.getFileScanData(filePath);
            fileScan.setFormat(bicsCsvFormat.getName());
            String detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, "FRANCE")
                    , Highlighted_Text
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 5, enabled = true, groups = {"regression", "detect_Bics=Always"})
    @Owner("Sarah Abdelatif")
    public void Regression_DetectBics_01_CSV_Custom_format_Not_Bic() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify returned matches when Detect_Bics=Always with CSV Custom format when defined field is not BIC Code"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Regression_DetectBics_01"));

            var Highlighted_Text = "ANDPFRP1";

            Format FullNameCsvFormat = eastnets.screening.entity.Common.FORMAT.SCAN_CSV_WITH_FULLNAME_FORMAT.get();
            FullNameCsvFormat.setName("Format-" + randomizer.getInt());
            FullNameCsvFormat.setZone(Common.ZONE.COMMON_TEST_ZONE_003.getZone().getDisplayName());
            Assert.assertNull(formatManagerControl.AddNewFormat(driver, FullNameCsvFormat), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG + " while creating new format.");
            String filePath = GeneralConstants.FILE_SCANNER_FILE_PATH;
            FileScan fileScan = commonTestMethods.getFileScanData(filePath);
            fileScan.setFormat(FullNameCsvFormat.getName());
            fileScan.setResult("All records");
            commonTestMethods.scanFile(driver, fileScan);
            Assert.assertEquals(resultManagerControl.get_detection_status(driver)
                    , "Clean"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 6, enabled = true, groups = {"regression", "detect_Bics=Never"})
    @Owner("Sarah Abdelatif")
    public void Regression_DetectBics_02_RJE() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify returned matches when Detect_Bics=NEVER with Swift RJE scanned format"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Regression_DetectBics_02"));

            var Highlighted_Text = "ANDPFRP1";
            var Highlighted_Text_1 = "ALEIGB22";
            var Data = "FRANCE";
            var Data_1 = "UNITED KINGDOM";

            Allure.step("Verify returned matches when Detect_Bics=NEVER with Swift RJE scanned format");
            String filePath = GeneralConstants.RJE_BICS_FILE_PATH;
            FileScan fileScan = commonTestMethods.getFileScanData(filePath);
            String detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, Data)
                    , Highlighted_Text
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, Data_1)
                    , Highlighted_Text_1
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 7, enabled = true, groups = {"regression", "detect_Bics=Never"})
    @Owner("Sarah Abdelatif")
    public void Regression_DetectBics_02_Generic_Txt() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify returned matches when Detect_Bics=NEVER with Generic Txt scanned format"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Regression_DetectBics_02"));

            var Highlighted_Text = "ANDPFRP1";
            var Data = "FRANCE";

            String filePath = GeneralConstants.FILE_SCANNER_FILE_PATH;
            FileScan fileScan = commonTestMethods.getFileScanData(filePath);
            textFilesHandler.writeToFile(filePath, Highlighted_Text + "XXX");
            fileScan.setFilePath(filePath);
            fileScan.setFormat("Generic Text");
            fileScan.setResult("All records");
            String detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            Assert.assertFalse(detectionManagerControl.verify_violation_exist(driver, detectionId, Data)
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 8, enabled = true, groups = {"regression", "detect_Bics=Never"})
    @Owner("Sarah Abdelatif")
    public void Regression_DetectBics_02_XML_Format() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify returned matches when Detect_Bics=NEVER with XML Custom format"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Regression_DetectBics_02"));

            var Highlighted_Text = "ANDPFRP1";
            var Data = "FRANCE";

            Format format = eastnets.screening.entity.Common.FORMAT.SCAN_XML_WITH_BIC_FORMAT.get();
            format.setName("Format-" + randomizer.getInt());
            format.setZone(Common.ZONE.COMMON_TEST_ZONE_003.getZone().getDisplayName());
            Assert.assertNull(formatManagerControl.AddNewFormat(driver, format), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG + " while creating new format.");
            String filePath = GeneralConstants.XML_SCANNER_FILE_PATH;
            FileScan fileScan = commonTestMethods.getFileScanData(filePath);
            fileScan.setFilePath(filePath);
            fileScan.setFormat(format.getName());
            String detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, Data)
                    , Highlighted_Text
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 9, enabled = true, groups = {"regression", "detect_Bics=Never"})
    @Owner("Sarah Abdelatif")
    public void Regression_DetectBics_02_XML_Format_Undefined_BIC() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify no matches returned when Detect_Bics=NEVER with XML Custom format with undefined BIC field"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Regression_DetectBics_02"));
            var Highlighted_Text = "ANDPFRP1";
            var Data = "FRANCE";


            Format format = eastnets.screening.entity.Common.FORMAT.SCAN_XML_WITH_FULLNAME_FORMAT.get();
            format.setName("Format-" + randomizer.getInt());
            format.setZone(Common.ZONE.COMMON_TEST_ZONE_003.getZone().getDisplayName());
            Assert.assertNull(formatManagerControl.AddNewFormat(driver, format), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG + " while creating new format.");
            String filePath = GeneralConstants.XML_SCANNER_FILE_PATH;
            FileScan fileScan = commonTestMethods.getFileScanData(filePath);
            fileScan.setFilePath(filePath);
            fileScan.setFormat(format.getName());
            fileScan.setResult("All records");

            String detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            Assert.assertFalse(detectionManagerControl.verify_violation_exist(driver, detectionId, Data)
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 10, enabled = true, groups = {"regression", "detect_Bics=Never"})
    @Owner("Sarah Abdelatif")
    public void Regression_DetectBics_02_CSV_Format() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify returned matches when Detect_Bics=NEVER with CSV Custom format"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Regression_DetectBics_02"));

            var Highlighted_Text = "ANDPFRP1";
            var data = "FRANCE";

            Format bicsCsvFormat = eastnets.screening.entity.Common.FORMAT.SCAN_CSV_WITH_BIC_FORMAT.get();
            bicsCsvFormat.setName("Format-" + randomizer.getInt());
            bicsCsvFormat.setZone(Common.ZONE.COMMON_TEST_ZONE_003.getZone().getDisplayName());
            Assert.assertNull(formatManagerControl.AddNewFormat(driver, bicsCsvFormat), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG + " while creating new format.");
            String filePath = GeneralConstants.FILE_SCANNER_FILE_PATH;
            FileScan fileScan = commonTestMethods.getFileScanData(filePath);
            fileScan.setFilePath(filePath);
            fileScan.setFormat(bicsCsvFormat.getName());
            String detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, data)
                    , Highlighted_Text
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 11, enabled = true, groups = {"regression", "detect_Bics=Never"})
    @Owner("Sarah Abdelatif")
    public void Regression_DetectBics_02_CSV_Format_Undefined_BIC() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify no matches returned when Detect_Bics=NEVER with CSV Custom format with undefined BIC field"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Regression_DetectBics_02"));

            var Highlighted_Text = "ANDPFRP1";
            var Data = "FRANCE";

            Format FullNameCsvFormat = eastnets.screening.entity.Common.FORMAT.SCAN_CSV_WITH_FULLNAME_FORMAT.get();
            FullNameCsvFormat.setName("Format-" + randomizer.getInt());
            FullNameCsvFormat.setZone(Common.ZONE.COMMON_TEST_ZONE_003.getZone().getDisplayName());
            Assert.assertNull(formatManagerControl.AddNewFormat(driver, FullNameCsvFormat), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG + " while creating new format.");
            String filePath = GeneralConstants.FILE_SCANNER_FILE_PATH;
            FileScan fileScan = commonTestMethods.getFileScanData(filePath);
            fileScan.setFormat(FullNameCsvFormat.getName());
            fileScan.setResult("All records");

            String detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            Assert.assertFalse(detectionManagerControl.verify_violation_exist(driver, detectionId, Data)
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 12, enabled = true, groups = {"regression", "detect_Bics=SWIFT Format Only"})
    @Owner("Sarah Abdelatif")
    public void Regression_DetectBics_03_RJE() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify returned matches when Detect_Bics=SWIFT Format Only with Swift RJE scanned format"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Regression_DetectBics_03"));

            var Highlighted_Text = "ANDPFRP1";
            var Highlighted_Text_1 = "ALEIGB22";
            var Data = "FRANCE";
            var Data_1 = "UNITED KINGDOM";

            Allure.step("Verify returned matches when Detect_Bics=NEVER with Swift RJE scanned format");
            String filePath = GeneralConstants.RJE_BICS_FILE_PATH;
            FileScan fileScan = commonTestMethods.getFileScanData(filePath);
            String detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, Data)
                    , Highlighted_Text
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, Data_1)
                    , Highlighted_Text_1
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 13, enabled = true, groups = {"regression", "detect_Bics=SWIFT Format Only"})
    @Owner("Sarah Abdelatif")
    public void Regression_DetectBics_03_Generic_Txt() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify returned matches when Detect_Bics=SWIFT Format Only with Generic Txt scanned format"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Regression_DetectBics_03"));

            var Highlighted_Text = "ANDPFRP1";
            var Data = "FRANCE";

            String filePath = GeneralConstants.FILE_SCANNER_FILE_PATH;
            FileScan fileScan = commonTestMethods.getFileScanData(filePath);
            textFilesHandler.writeToFile(filePath, Highlighted_Text + "XXX");
            fileScan.setFilePath(filePath);
            fileScan.setFormat("Generic Text");
            fileScan.setResult("All records");
            String detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            Assert.assertFalse(detectionManagerControl.verify_violation_exist(driver, detectionId, Data)
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 14, enabled = true, groups = {"regression", "detect_Bics=SWIFT Format Only"})
    @Owner("Sarah Abdelatif")
    public void Regression_DetectBics_03_CSV_Format() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify returned matches when Detect_Bics=SWIFT Format Only with CSV Custom format"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Regression_DetectBics_03"));

            var Highlighted_Text = "ANDPFRP1";
            var data = "FRANCE";

            Format bicsCsvFormat = eastnets.screening.entity.Common.FORMAT.SCAN_CSV_WITH_BIC_FORMAT.get();
            bicsCsvFormat.setName("Format-" + randomizer.getInt());
            bicsCsvFormat.setZone(Common.ZONE.COMMON_TEST_ZONE_003.getZone().getDisplayName());
            Assert.assertNull(formatManagerControl.AddNewFormat(driver, bicsCsvFormat), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG + " while creating new format.");
            String filePath = GeneralConstants.FILE_SCANNER_FILE_PATH;
            textFilesHandler.writeToFile(filePath, Highlighted_Text + "XXX");
            FileScan fileScan = commonTestMethods.getFileScanData(filePath);
            fileScan.setFilePath(filePath);
            fileScan.setFormat(bicsCsvFormat.getName());
            String detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, data)
                    , Highlighted_Text
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 15, enabled = true, groups = {"regression", "detect_Bics=SWIFT Format Only"})
    @Owner("Sarah Abdelatif")
    public void Regression_DetectBics_03_CSV_Format_Undefined_BIC() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify no matches returned when Detect_Bics=SWIFT Format Only with CSV Custom format with undefined BIC field"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Regression_DetectBics_03"));

            var Highlighted_Text = "ANDPFRP1";
            var Data = "FRANCE";

            Format FullNameCsvFormat = eastnets.screening.entity.Common.FORMAT.SCAN_CSV_WITH_FULLNAME_FORMAT.get();
            FullNameCsvFormat.setName("Format-" + randomizer.getInt());
            FullNameCsvFormat.setZone(Common.ZONE.COMMON_TEST_ZONE_003.getZone().getDisplayName());
            Assert.assertNull(formatManagerControl.AddNewFormat(driver, FullNameCsvFormat), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG + " while creating new format.");
            String filePath = GeneralConstants.FILE_SCANNER_FILE_PATH;
            FileScan fileScan = commonTestMethods.getFileScanData(filePath);
            fileScan.setFormat(FullNameCsvFormat.getName());
            fileScan.setResult("All records");

            String detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            Assert.assertFalse(detectionManagerControl.verify_violation_exist(driver, detectionId, Data)
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 16, enabled = true, groups = {"regression", "detect_Bics=SWIFT Format Only"})
    @Owner("Sarah Abdelatif")
    public void Regression_DetectBics_03_XML_Format() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify returned matches when Detect_Bics=SWIFT Format Only with XML Custom format"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Regression_DetectBics_03"));

            var Highlighted_Text = "ANDPFRP1";
            var Data = "FRANCE";

            Format format = eastnets.screening.entity.Common.FORMAT.SCAN_XML_WITH_BIC_FORMAT.get();
            format.setName("Format-" + randomizer.getInt());
            format.setZone(Common.ZONE.COMMON_TEST_ZONE_003.getZone().getDisplayName());
            Assert.assertNull(formatManagerControl.AddNewFormat(driver, format), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG + " while creating new format.");
            String filePath = GeneralConstants.XML_SCANNER_FILE_PATH;
            FileScan fileScan = commonTestMethods.getFileScanData(filePath);
            fileScan.setFilePath(filePath);
            fileScan.setFormat(format.getName());
            String detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, Data)
                    , Highlighted_Text
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 17, enabled = true, groups = {"regression", "detect_Bics=SWIFT Format Only"})
    @Owner("Sarah Abdelatif")
    public void Regression_DetectBics_03_XML_Format_Undefined_BIC() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify no matches returned when Detect_Bics=SWIFT Format Only with XML Custom format with undefined BIC field"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Regression_DetectBics_03"));

            var Highlighted_Text = "ANDPFRP1";
            var Data = "FRANCE";

            Format format = eastnets.screening.entity.Common.FORMAT.SCAN_XML_WITH_FULLNAME_FORMAT.get();
            format.setName("Format-" + randomizer.getInt());
            format.setZone(Common.ZONE.COMMON_TEST_ZONE_003.getZone().getDisplayName());
            Assert.assertNull(formatManagerControl.AddNewFormat(driver, format), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG + " while creating new format.");
            String filePath = GeneralConstants.XML_SCANNER_FILE_PATH;
            FileScan fileScan = commonTestMethods.getFileScanData(filePath);
            fileScan.setFilePath(filePath);
            fileScan.setFormat(format.getName());
            fileScan.setResult("All records");

            String detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            Assert.assertFalse(detectionManagerControl.verify_violation_exist(driver, detectionId, Data)
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 18, enabled = true, groups = {"regression"})
    @Owner("Sarah Abdelatif")
    public void Regression_DetectBics_04() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify # of returned violations when scanning a BIC having same value in City and Country\""));
            lifecycle.updateTestCase(testResult -> testResult.setName("Regression_DetectBics_04"));

            String highlightedText = "PETASGS1";
            String data = "SINGAPORE";

            screeningServicesDelegate.deleteBics();
            screeningServicesDelegate.excuteQuerSFP(GeneralConstants.DB_QUERY[15]);

            String filePath = GeneralConstants.OFC_FILE_PATH + "/AllCountries.ofc";
            enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.COMMON_TEST_ZONE_003.getZone().getDisplayName());
            enlist.setName("AllCountries");

            screeningServicesDelegate.deleteLinkBtwBlackListAndListSet(enlist.getZoneName(), enlist.getName());
            screeningServicesDelegate.deleteLinkBtwBlackListAndGoodGuy(enlist.getZoneName(), enlist.getName());
            screeningServicesDelegate.deleteBlackList(enlist.getName());

            commonTestMethods.importBListAndLinkToNewListSet(driver, enlist, Common.PROFILE.FULL_RIGHT_003.getProfile().getName(), filePath);

            fourEyesControl.disableFourEyes(driver);

            engineTuning = engineTuningsTestData();
            engineTuning.setDetectSwiftBic("Always");
            String actualResult = engineTuningControl.engineSettingsIgnoreMatches(driver, engineTuning);
            Assert.assertTrue(actualResult.contains(" Note that some of the changed parameters require the SafeWatch Screening solution to be restarted.")
                            || actualResult.contains("Nothing has been changed.")
                    , "Failed to add engine setting.");

            commonTestMethods.restartNewArch();

            filePath = GeneralConstants.RJE_SINGAPORE_SCANNER_FILE_PATH;
            FileScan fileScan = commonTestMethods.getFileScanData(filePath);
            fileScan.setFilePath(filePath);
            String detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, data)
                    , highlightedText
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 19, enabled = true, groups = {"regression"})
    @Owner("Sarah Abdelatif")
    public void Regression_DetectBics_05() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that BIC is scanned once as BIC and once as text"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Regression_DetectBics_05"));


            screeningServicesDelegate.deleteBics();
            screeningServicesDelegate.excuteQuerSFP(GeneralConstants.DB_QUERY[5]);
            screeningServicesDelegate.excuteQuerSFP(GeneralConstants.DB_QUERY[6]);


            String filePath = GeneralConstants.OFC_FILE_PATH + "/AllCountries.ofc";
            enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.COMMON_TEST_ZONE_003.getZone().getDisplayName());
            enlist.setName("AllCountries");

            EnList enlist2 = commonTestMethods.getEnListData();
            enlist2.setListSet(enlist.getListSet());
            enlist2.setZoneName(Common.ZONE.COMMON_TEST_ZONE_003.getZone().getDisplayName());


            screeningServicesDelegate.deleteLinkBtwBlackListAndListSet(enlist.getZoneName(), enlist.getName());
            screeningServicesDelegate.deleteLinkBtwBlackListAndGoodGuy(enlist.getZoneName(), enlist.getName());
            screeningServicesDelegate.deleteBlackList(enlist.getName());

            commonTestMethods.importBListAndLinkToNewListSet(driver, enlist, Common.PROFILE.FULL_RIGHT_003.getProfile().getName(), filePath);

            Assert.assertTrue(blackListControl.create_Black_List(driver, enlist2)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new black list.");

            Assert.assertTrue(listSetControl.link_black_list_to_exist_listSet(driver, enlist2).contains(enlist2.getExpectedMessage())
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while add black list to listSet");

            enlist2.getEntry().get(0).setType("Group");
            enlist2.getEntry().get(0).setName("ANDPFRP1XXX");
            enlist2.getEntry().get(0).setFirstName(null);
            listExplorerControl.search(driver, enlist2);
            Assert.assertTrue(listExplorerControl.create_entry_with_type_and_names(driver, enlist2.getEntry().get(0))
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while Creating new Entry." + enlist2.getEntry().get(0).toString());


            enlist2.getEntry().get(1).setType("Group");
            enlist2.getEntry().get(1).setName("ALEIGB22CAF");
            enlist2.getEntry().get(1).setFirstName(null);
            listExplorerControl.search(driver, enlist2);
            Assert.assertTrue(listExplorerControl.create_entry_with_type_and_names(driver, enlist2.getEntry().get(1))
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while Creating new Entry." + enlist2.getEntry().get(1).toString());


            fourEyesControl.disableFourEyes(driver);

            engineTuning = engineTuningsTestData();
            engineTuning.setDetectSwiftBic("Always");
            String actualResult = engineTuningControl.engineSettingsIgnoreMatches(driver, engineTuning);
            Assert.assertTrue(actualResult.contains(" Note that some of the changed parameters require the SafeWatch Screening solution to be restarted.")
                            || actualResult.contains("Nothing has been changed.")
                    , "Failed to add engine setting.");

            commonTestMethods.restartNewArch();

            filePath = GeneralConstants.RJE_BICS_IN_BODY_SCANNER_FILE_PATH;
            FileScan fileScan = commonTestMethods.getFileScanData(filePath);
            fileScan.setFilePath(filePath);
            String detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            String highlightedText = "ANDPFRP1";
            String data = "FRANCE";
            String data1 = "ALEIGB22CAF";
            String highlightedText1 = "ALEIGB22CAF";

            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, data)
                    , highlightedText
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, data1)
                    , highlightedText1
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


            highlightedText = "ALEIGB22";
            data = "UNITED KINGDOM";
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, data)
                    , highlightedText
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            highlightedText = "ANDPFRP1";
            data = "ANDPFRP1XXX";
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, data)
                    , highlightedText
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 20, enabled = true, groups = {"regression"})
    @Owner("Sarah Abdelatif")
    public void Regression_DetectBics_06() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that the Sender's BIC in SWIFT RJE Message is not scanned"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Regression_DetectBics_06"));


            screeningServicesDelegate.deleteBics();
            screeningServicesDelegate.excuteQuerSFP(GeneralConstants.DB_QUERY[5]);
            screeningServicesDelegate.excuteQuerSFP(GeneralConstants.DB_QUERY[17]);


            String filePath = GeneralConstants.OFC_FILE_PATH + "/AllCountries.ofc";
            enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.COMMON_TEST_ZONE_003.getZone().getDisplayName());
            enlist.setName("AllCountries");

            EnList enlist2 = commonTestMethods.getEnListData();
            enlist2.setListSet(enlist.getListSet());
            enlist2.setZoneName(Common.ZONE.COMMON_TEST_ZONE_003.getZone().getDisplayName());


            screeningServicesDelegate.deleteLinkBtwBlackListAndListSet(enlist.getZoneName(), enlist.getName());
            screeningServicesDelegate.deleteLinkBtwBlackListAndGoodGuy(enlist.getZoneName(), enlist.getName());
            screeningServicesDelegate.deleteBlackList(enlist.getName());

            commonTestMethods.importBListAndLinkToNewListSet(driver, enlist, Common.PROFILE.FULL_RIGHT_003.getProfile().getName(), filePath);

            Assert.assertTrue(blackListControl.create_Black_List(driver, enlist2)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new black list.");

            Assert.assertTrue(listSetControl.link_black_list_to_exist_listSet(driver, enlist2).contains(enlist2.getExpectedMessage())
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while add black list to listSet");

            enlist2.getEntry().get(0).setType("Group");
            enlist2.getEntry().get(0).setName("ABNAMXMMXXX");
            enlist2.getEntry().get(0).setFirstName(null);
            listExplorerControl.search(driver, enlist2);
            Assert.assertTrue(listExplorerControl.create_entry_with_type_and_names(driver, enlist2.getEntry().get(0))
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while Creating new Entry." + enlist2.getEntry().get(0).toString());


            fourEyesControl.disableFourEyes(driver);

            engineTuning = engineTuningsTestData();
            engineTuning.setDetectSwiftBic("Always");
            String actualResult = engineTuningControl.engineSettingsIgnoreMatches(driver, engineTuning);
            Assert.assertTrue(actualResult.contains(" Note that some of the changed parameters require the SafeWatch Screening solution to be restarted.")
                            || actualResult.contains("Nothing has been changed.")
                    , "Failed to add engine setting.");

            commonTestMethods.restartNewArch();

            filePath = GeneralConstants.RJE_BICS_FILE_PATH;
            FileScan fileScan = commonTestMethods.getFileScanData(filePath);
            fileScan.setRank("50");
            fileScan.setFilePath(filePath);
            String detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            String data = "ABNAMXMM";

            Assert.assertFalse(detectionManagerControl.verify_violation_exist(driver, detectionId, data)
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            data = "ARUBA";
            Assert.assertFalse(detectionManagerControl.verify_violation_exist(driver, detectionId, data)
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 21, enabled = true, groups = {"regression"})
    @Owner("Sarah Abdelatif")
    public void Regression_DetectBics_07() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that no matches return when Sender BIC is not licensed"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Regression_DetectBics_07"));


            screeningServicesDelegate.deleteBics();
            screeningServicesDelegate.excuteQuerSFP(GeneralConstants.DB_QUERY[5]);


            String filePath = GeneralConstants.OFC_FILE_PATH + "/AllCountries.ofc";
            enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.COMMON_TEST_ZONE_003.getZone().getDisplayName());
            enlist.setName("AllCountries");

            EnList enlist2 = commonTestMethods.getEnListData();
            enlist2.setListSet(enlist.getListSet());
            enlist2.setZoneName(Common.ZONE.COMMON_TEST_ZONE_003.getZone().getDisplayName());


            screeningServicesDelegate.deleteLinkBtwBlackListAndListSet(enlist.getZoneName(), enlist.getName());
            screeningServicesDelegate.deleteLinkBtwBlackListAndGoodGuy(enlist.getZoneName(), enlist.getName());
            screeningServicesDelegate.deleteBlackList(enlist.getName());

            commonTestMethods.importBListAndLinkToNewListSet(driver, enlist, Common.PROFILE.FULL_RIGHT_003.getProfile().getName(), filePath);

            engineTuning = engineTuningsTestData();
            engineTuning.setDetectSwiftBic("Always");
            String actualResult = engineTuningControl.engineSettingsIgnoreMatches(driver, engineTuning);
            Assert.assertTrue(actualResult.contains(" Note that some of the changed parameters require the SafeWatch Screening solution to be restarted.")
                            || actualResult.contains("Nothing has been changed.")
                    , "Failed to add engine setting.");

            commonTestMethods.restartNewArch();

            filePath = GeneralConstants.RJE_BICS_NO_LICENSE_SCANNER_FILE_PATH;
            FileScan fileScan = commonTestMethods.getFileScanData(filePath);
            fileScan.setFilePath(filePath);
            fileScan.setResult("All records");
            String detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            String data = "FRANCE";

            Assert.assertFalse(detectionManagerControl.verify_violation_exist(driver, detectionId, data)
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod()
    public void afterMethod() {
        try {
            screeningServicesDelegate.deleteBics();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    public EngineTuning engineTuningsTestData() throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("engineTuningsTD");
        EngineTuning[] engineTuning = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), EngineTuning[].class);
        return engineTuning[0];

    }
}
