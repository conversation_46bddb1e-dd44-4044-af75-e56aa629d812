package eastnets.screening.AdvancedSettingsTests;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paypal.selion.platform.dataprovider.DataProviderFactory;
import com.paypal.selion.platform.dataprovider.DataResource;
import com.paypal.selion.platform.dataprovider.SeLionDataProvider;
import com.paypal.selion.platform.dataprovider.impl.InputStreamResource;
import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import core.util.Log;
import core.util.TextFilesHandler;
import eastnets.admin.entity.Common;
import eastnets.admin.entity.Profile;
import eastnets.admin.entity.Zone;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.common.gui.Navigation;
import eastnets.screening.control.AdvancedSettingsControls.AdvancedSettingsControl;
import eastnets.screening.control.AdvancedSettingsControls.BusinessLogicControl;
import eastnets.screening.control.AdvancedSettingsControls.EngineTuningControl;
import eastnets.screening.control.AdvancedSettingsControls.FourEyesControl;
import eastnets.screening.control.FormatManagerControl;
import eastnets.screening.control.scanManger.FileScanControl;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.*;
import eastnets.screening.gui.advancedSettings.EngineTuningManager;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.*;
import org.testng.asserts.SoftAssert;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class ZoneSegregationTest extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final AdvancedSettingsControl advancedSettingsControl = new AdvancedSettingsControl();
    private final BusinessLogicControl businessLogicControl = new BusinessLogicControl();
    private final EngineTuningControl engineTuningControl = new EngineTuningControl();
    private final FourEyesControl fourEyesControl = new FourEyesControl();
    private final FormatManagerControl formatManagerControl = new FormatManagerControl();
    private final FileScanControl fileScanControl = new FileScanControl();
    private final NameScanControl nameScanControl = new NameScanControl();
    private final ResultManagerControl resultManagerControl = new ResultManagerControl();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final Log log = new Log();
    private final TextFilesHandler textFilesHandler = new TextFilesHandler();
    private final EngineTuningManager engineTuningManager = new EngineTuningManager();

    private RemoteWebDriver driver;

    @BeforeClass(alwaysRun = true)
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeClass(alwaysRun = true, dependsOnMethods = "initializeDriver")
    public void login() {
        try {

            loginPage.login(driver
                    , Common.OPERATOR.DeFAULT_ZONE_OPERATOR_1.getOperator().getLoginName()
                    , Common.OPERATOR.DeFAULT_ZONE_OPERATOR_1.getOperator().getPassword()
                   );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeGroups(value = "importListDefaultZone", alwaysRun = true)
    public void importList() {
        try {

            Profile profile = Common.PROFILE.FULL_RIGHT_001.getProfile();
            Zone commanZone = Common.ZONE.COMMON_TEST_ZONE_001.getZone();
            String filePathIndividual = System.getProperty("user.dir") + GeneralConstants.IMPORT_ENTRY_INDIVIDUAL_LIST_FILE_PATH;
            String filePathGroup = System.getProperty("user.dir") + GeneralConstants.IMPORT_ENTRY_GROUP_LIST_FILE_PATH;

            Format formatIndividual = eastnets.screening.entity.Common.FORMAT.IMPORT_ENTRY_INDIVIDUAL_FORMAT_2.get();
            Format formatGroup = eastnets.screening.entity.Common.FORMAT.IMPORT_ENTRY_GROUP_FORMAT.get();

            EnList enList = commonTestMethods.getEnListData();
            Allure.step(String.format("Start add entries on '%s' zone.", commanZone.getDisplayName()));
            commonTestMethods.addListSetAndimportEntries(driver, enList, profile, commanZone, filePathIndividual, formatIndividual, null);
            commonAction.logout(driver);
            loginPage.login(driver
                    , Common.OPERATOR.DeFAULT_ZONE_OPERATOR_1.getOperator().getLoginName()
                    , Common.OPERATOR.DeFAULT_ZONE_OPERATOR_1.getOperator().getPassword()
                    );
            commonTestMethods.importEntries(driver, enList, commanZone, filePathGroup, formatGroup);

            profile = Common.PROFILE.DEFAULT_PROFILE.getProfile();
            commanZone = Common.ZONE.DEFAULT_ZONE.getZone();
            Allure.step(String.format("Start add entries on '%s' zone.", commanZone.getDisplayName()));
            commonTestMethods.addListSetAndimportEntries(driver, enList, profile, commanZone, filePathIndividual, formatIndividual, null);
            commonAction.logout(driver);
            loginPage.login(driver
                    , Common.OPERATOR.DeFAULT_ZONE_OPERATOR_1.getOperator().getLoginName()
                    , Common.OPERATOR.DeFAULT_ZONE_OPERATOR_1.getOperator().getPassword()
            );
            commonTestMethods.importEntries(driver, enList, commanZone, filePathGroup, formatGroup);


            EngineTuning engineTuning = new EngineTuning();
            engineTuning = engineTuning.setAllToTrueOrFalse("Engine Settings", Common.ZONE.DEFAULT_ZONE.getZone().getDisplayName(), true);

            Navigation.ADVANCED_SETTINGS.navigate(driver);
            advancedSettingsControl.navigateToEngineTuning(driver);

            String actualResult = engineTuningControl.addEngineSetting(driver, engineTuning);
            Assert.assertTrue(actualResult.contains("Please keep in mind that changing the engine settings may drastically change the scanning results." +
                            " Note that some of the changed parameters require the SafeWatch Screening solution to be restarted.")
                            || actualResult.contains("Nothing has been changed.")
                    , "Failed to add engine setting.");


            fourEyesControl.disableFourEyes(driver);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 0, enabled = true, groups = {"regression", "importListDefaultZone"})
    @Owner("Sara Abdellatif")
    public void checkAuditTrail() {

        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that 'Enable audit trail for all scan requests " +
                    "'option can be checked when select segregate zone."));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that 'Enable audit trail for all scan requests 'option can be checked when select segregate zone."));

            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("checkAuditTrail");
            GeneralSettings generalSettings = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), GeneralSettings.class);

            Assert.assertEquals(businessLogicControl.setGeneralSettingsDetails(driver, generalSettings)
                    , "Operation completed successfully! The new configuration will take place after 10 minutes maximum."
                    , "Failed to set general settings details.");

            commonTestMethods.restartNewArch();
            commonTestMethods.startSegregateScanServices();

            Navigation.SCAN_MANAGER.navigate(driver);

            String scanName = "ABOU ABDELJALIL";

            nameScanControl.scan_Name(driver
                    , scanName
                    , Common.ZONE.DEFAULT_ZONE.getZone().getDisplayName()
                    , null
                    , true
                    , true
                    , true
                    , false);


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 1, enabled = true, groups = {"regression", "importListDefaultZone"}, dataProvider = "generalSettingsTestData")
    @Owner("Sara Abdellatif")
    public void checkArabicPhonetics(GeneralSettings[] generalSettings) {


        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription(generalSettings[0].getTestCaseTitle()));
            lifecycle.updateTestCase(testResult -> testResult.setName(generalSettings[0].getTestCaseTitle()));

            SoftAssert softAssert = new SoftAssert();
            Assert.assertEquals(businessLogicControl.setGeneralSettingsDetails(driver, generalSettings[0]),
                    "Operation completed successfully! The new configuration will take place after 10 minutes maximum."
                    , "Failed to set general settings details.");
            driver.navigate().refresh();
            Assert.assertEquals(businessLogicControl.setGeneralSettingsDetails(driver, generalSettings[1])
                    , "Operation completed successfully! The new configuration will take place after 10 minutes maximum."
                    , "Failed to set general settings details.");


            commonTestMethods.restartNewArch();
            commonTestMethods.startSegregateScanServices();

            Navigation.SCAN_MANAGER.navigate(driver);

            softAssert.assertEquals(nameScanControl.scan_Name(driver
                            , generalSettings[0].getScannedName()
                            , Common.ZONE.DEFAULT_ZONE.getZone().getDisplayName()
                            , "50"
                            , true
                            , true
                            , true
                            , false)
                    , generalSettings[0].getDetectionStatus()
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            softAssert.assertEquals(nameScanControl.get_detection_rank(driver)
                    , generalSettings[0].getRank()
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            softAssert.assertEquals(nameScanControl.scan_Name(driver
                            , generalSettings[1].getScannedName()
                            , Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName()
                            , "50"
                            , true
                            , true
                            , true
                            , false)
                    , generalSettings[1].getDetectionStatus()
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            softAssert.assertEquals(nameScanControl.get_detection_rank(driver), generalSettings[1].getRank()
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            softAssert.assertAll();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 2, enabled = true, groups = {"regression", "importListDefaultZone"})
    @Owner("Sara Abdellatif")
    public void verifyScanNeutralWords() {
        try {

            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("verifyScanNeutralWords");
            EngineTuning[] engineTuning = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), EngineTuning[].class);
            SoftAssert softAssert = new SoftAssert();

            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription(engineTuning[0].getTestCaseTitle()));
            lifecycle.updateTestCase(testResult -> testResult.setName(engineTuning[0].getTestCaseTitle()));

            Navigation.ADVANCED_SETTINGS.navigate(driver);
            advancedSettingsControl.navigateToEngineTuning(driver);

            if (engineTuning[1].getZone().contains("Common"))
                engineTuning[1].setZone(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());

            if (!engineTuningManager.verifyIfExist(driver, engineTuning[0])) {
                engineTuningControl.addNewNeutralWord(driver, engineTuning[0]);
            } else {
                Allure.step("The word " + engineTuning[0].getWord() + " already exist.");
            }

            if (engineTuningManager.verifyIfExist(driver, engineTuning[1])) {
                engineTuningManager.delete(driver, engineTuning[1].getWord());
            } else {
                Allure.step("The word " + engineTuning[1].getWord() + " already does not exist.");
            }

            commonTestMethods.restartNewArch();
            commonTestMethods.startSegregateScanServices();

            Navigation.SCAN_MANAGER.navigate(driver);

            softAssert.assertEquals(nameScanControl.scan_Name(driver
                            , engineTuning[0].getScannedName()
                            , Common.ZONE.DEFAULT_ZONE.getZone().getDisplayName()
                            , "50"
                            , true
                            , true
                            , true
                            , false)
                    , engineTuning[0].getDetectionStatus()
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            softAssert.assertEquals(nameScanControl.get_detection_rank(driver)
                    , engineTuning[0].getRank()
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            softAssert.assertEquals(nameScanControl.scan_Name(driver, engineTuning[1].getScannedName()
                            , Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName()
                            , "50"
                            , true
                            , true
                            , true
                            , false)
                    , engineTuning[1].getDetectionStatus()
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            softAssert.assertEquals(nameScanControl.get_detection_rank(driver)
                    , engineTuning[1].getRank()
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            softAssert.assertAll();

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }


    }

    @Test(priority = 3, enabled = true, groups = {"regression", "importListDefaultZone"})
    @Owner("Sara Abdellatif")
    public void verifyScanSynonymWords() {
        try {

            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify the scan result when add Synonym on default config zone while not add it on the segregate zone"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify the scan result when add Synonym on default config zone while not add it on the segregate zone"));

            SoftAssert softAssert = new SoftAssert();

            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("verifyScanSynonymWords");
            EngineTuning[] engineTuning = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), EngineTuning[].class);
            if (engineTuning[0].getZone().contains("Common"))
                engineTuning[0].setZone(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());

            Navigation.ADVANCED_SETTINGS.navigate(driver);
            advancedSettingsControl.navigateToEngineTuning(driver);


            if (!engineTuningManager.verifyIfExist(driver, engineTuning[0])) {
                engineTuningControl.addNewSynonym(driver, engineTuning[0]);
            } else {
                Allure.step("The word " + engineTuning[0].getWord() + " already exist.");
            }

            if (engineTuningManager.verifyIfExist(driver, engineTuning[1])) {
                engineTuningManager.delete(driver, engineTuning[1].getWord());
            } else {
                Allure.step("The word " + engineTuning[1].getWord() + " already does not exist.");
            }

            commonTestMethods.restartNewArch();
            commonTestMethods.startSegregateScanServices();

            Navigation.SCAN_MANAGER.navigate(driver);

            softAssert.assertEquals(nameScanControl.scan_Name(driver
                            , engineTuning[0].getScannedName()
                            , engineTuning[0].getZone()
                            , "50"
                            , true
                            , true
                            , true
                            , false)
                    , engineTuning[0].getDetectionStatus()
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            softAssert.assertEquals(nameScanControl.get_detection_rank(driver)
                    , engineTuning[0].getRank()
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            softAssert.assertEquals(nameScanControl.scan_Name(driver
                            , engineTuning[1].getScannedName()
                            , engineTuning[1].getZone()
                            , "50"
                            , true
                            , true
                            , true
                            , false)
                    , engineTuning[1].getDetectionStatus()
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            softAssert.assertEquals(nameScanControl.get_detection_rank(driver)
                    , engineTuning[1].getRank()
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            softAssert.assertAll();

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }


    }

    @Test(priority = 4, enabled = true, groups = {"regression", "importListDefaultZone"})
    @Owner("Sara Abdellatif")
    public void verifyGluedWordsSymSpell() {

        try {

            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("verifyGluedWordsSymSpell");
            GeneralSettings[] generalSettings = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), GeneralSettings[].class);

            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription(generalSettings[0].getTestCaseTitle()));
            lifecycle.updateTestCase(testResult -> testResult.setName(generalSettings[0].getTestCaseTitle()));

            String formatName1 = importFormat(driver, generalSettings[0].getZone());
            String formatName2 = importFormat(driver, Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());


            Navigation.ADVANCED_SETTINGS.navigate(driver);
            advancedSettingsControl.navigateToBusinessLogic(driver);

            Assert.assertEquals(businessLogicControl.setGeneralSettingsDetails(driver, generalSettings[0])
                    , "Operation completed successfully! The new configuration will take place after 10 minutes maximum."
                    , "Failed to add engine setting.");
            driver.navigate().refresh();
            Assert.assertEquals(businessLogicControl.setGeneralSettingsDetails(driver, generalSettings[1])
                    , "Operation completed successfully! The new configuration will take place after 10 minutes maximum."
                    , "Failed to add engine setting.");


            commonTestMethods.restartNewArch();
            commonTestMethods.startSegregateScanServices();


            FileScan fileScan = new FileScan();
            EnList enList = new EnList();
            enList.setZoneName(generalSettings[0].getZone());
            fileScan.setEnList(enList);
            fileScan.setFilePath(GeneralConstants.FILE_SCAN_PATH);
            fileScan.setFormat(formatName1);
            fileScan.setRank("50");

            Navigation.SCAN_MANAGER.navigate(driver);
            Assert.assertEquals(fileScanControl.scan_file(driver, fileScan).split("\\[")[0], "File sent to the server for processing with id ", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Assert.assertEquals(resultManagerControl.check_scanned_file_result(driver), "SUCCEEDED", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            String detectionID = resultManagerControl.get_detection_id(driver);
            Assert.assertNotNull(detectionID, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Assert.assertEquals(resultManagerControl.get_detection_rank(driver)
                    , generalSettings[0].getRank()
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            fileScan.setFormat(formatName2);
            enList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            fileScan.setEnList(enList);

            Navigation.SCAN_MANAGER.navigate(driver);
            Assert.assertEquals(fileScanControl.scan_file(driver, fileScan).split("\\[")[0], "File sent to the server for processing with id ", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Assert.assertEquals(resultManagerControl.check_scanned_file_result(driver), "SUCCEEDED", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            detectionID = resultManagerControl.get_detection_id(driver);
            Assert.assertEquals(detectionID, "There are no results to be displayed.", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 5, enabled = true, groups = {"regression", "importListDefaultZone"}, dataProvider = "engineSettingsTestData")
    @Owner("Sara Abdellatif")
    public void checkEngineSettings(EngineTuning[] engineTunings) {

        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription(engineTunings[0].getTestCaseTitle()));
            lifecycle.updateTestCase(testResult -> testResult.setName(engineTunings[0].getTestCaseTitle()));

            if (engineTunings[1].getZone().contains("Common"))
                engineTunings[1].setZone(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            Navigation.ADVANCED_SETTINGS.navigate(driver);
            advancedSettingsControl.navigateToEngineTuning(driver);

            String actualResult = engineTuningControl.addEngineSetting(driver, engineTunings[0]);
            Assert.assertTrue(actualResult.contains("Please keep in mind that changing the engine settings may drastically change the scanning results." +
                            " Note that some of the changed parameters require the SafeWatch Screening solution to be restarted.")
                            || actualResult.contains("Nothing has been changed.")
                    , "Failed to add engine setting.");
            driver.navigate().refresh();

            actualResult = engineTuningControl.addEngineSetting(driver, engineTunings[1]);
            Assert.assertTrue(actualResult.contains("Please keep in mind that changing the engine settings may drastically change the scanning results." +
                            " Note that some of the changed parameters require the SafeWatch Screening solution to be restarted.")
                            || actualResult.contains("Nothing has been changed.")
                    , "Failed to add engine setting.");


            commonTestMethods.restartNewArch();
            commonTestMethods.startSegregateScanServices();


            Navigation.SCAN_MANAGER.navigate(driver);

            Assert.assertEquals(nameScanControl.scan_Name(driver
                            , engineTunings[0].getScannedName()
                            , Common.ZONE.DEFAULT_ZONE.getZone().getDisplayName()
                            , "50"
                            , true
                            , true
                            , true
                            , false)
                    , engineTunings[0].getDetectionStatus()
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(nameScanControl.get_detection_rank(driver)
                    , engineTunings[0].getRank()
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Assert.assertEquals(nameScanControl.scan_Name(driver
                            , engineTunings[1].getScannedName()
                            , Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName()
                            , "50"
                            , true
                            , true
                            , true
                            , false)
                    , engineTunings[1].getDetectionStatus()
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            if (engineTunings[1].getDetectionStatus().contains("REP")) {
                Assert.assertTrue(Integer.parseInt(nameScanControl.get_detection_rank(driver)) < Integer.parseInt(engineTunings[1].getRank())
                        , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            }
            //No detection found!
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 6, enabled = true, groups = {"regression", "importListDefaultZone"})
    public void verifyIslamicFullNames() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that new 'Ignore Islamic Full Names' option can be enabled when select segregate zone"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that new 'Ignore Islamic Full Names' option can be enabled when select segregate zone"));
            SoftAssert softAssert = new SoftAssert();

            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("verifyScanSynonymWords");
            EngineTuning[] engineTuning = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), EngineTuning[].class);
            if (engineTuning[0].getZone().contains("Common"))
                engineTuning[0].setZone(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());

            Navigation.ADVANCED_SETTINGS.navigate(driver);
            advancedSettingsControl.navigateToEngineTuning(driver);

            if (!engineTuningManager.verifyIfExist(driver, engineTuning[0])) {
                engineTuningControl.addNewSynonym(driver, engineTuning[0]);
            } else {
                log.info("The word " + engineTuning[0].getWord() + " already exist.");
            }

            if (engineTuningManager.verifyIfExist(driver, engineTuning[1])) {
                engineTuningManager.delete(driver, engineTuning[1].getWord());
            } else {
                log.info("The word " + engineTuning[1].getWord() + " already does not exist.");
            }

            commonTestMethods.restartNewArch();
            commonTestMethods.startSegregateScanServices();

            Navigation.SCAN_MANAGER.navigate(driver);

            softAssert.assertEquals(nameScanControl.scan_Name(driver
                            , engineTuning[0].getScannedName()
                            , engineTuning[0].getZone()
                            , "50"
                            , true
                            , true
                            , true
                            , false)
                    , engineTuning[0].getDetectionStatus()
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            softAssert.assertEquals(nameScanControl.get_detection_rank(driver)
                    , engineTuning[0].getRank()
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            softAssert.assertEquals(nameScanControl.scan_Name(driver
                            , engineTuning[1].getScannedName()
                            , engineTuning[1].getZone(), "50"
                            , true
                            , true
                            , true
                            , false)
                    , engineTuning[1].getDetectionStatus()
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            softAssert.assertEquals(nameScanControl.get_detection_rank(driver)
                    , engineTuning[1].getRank()
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            softAssert.assertAll();

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }


    }


    @AfterClass(alwaysRun = true)
    public void updateBusinessLogicOptions() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.DeFAULT_ZONE_OPERATOR_1.getOperator().getLoginName()
                    , Common.OPERATOR.DeFAULT_ZONE_OPERATOR_1.getOperator().getPassword()
                   );

            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("generalSettings");
            GeneralSettings generalSettings = mapper.readValue(new File(System.getProperty("user.dir")
                    + csvDataFilePath), GeneralSettings.class);


            Assert.assertEquals(businessLogicControl.setGeneralSettingsDetails(driver, generalSettings)
                    , "Operation completed successfully! The new configuration will take place after 10 minutes maximum."
                    , "Failed to set general settings details.");

            commonTestMethods.restartNewArch();

            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Step("Import format")
    public String importFormat(RemoteWebDriver driver, String zone) throws Exception {
        String timestamp = new SimpleDateFormat("hmmsssssss").format(new Date());
        String formatName = "Format-Name-" + timestamp;
        String fileContent = textFilesHandler.getTextFileContentAsString(System.getProperty("user.dir") + GeneralConstants.IMPORT_FORMAT_SAMPLE_FILE);
        fileContent = fileContent.replace("#FORMAT-NAME#", formatName);
        Allure.step("Start coping data from sample file to another file to be upload.");
        textFilesHandler.writeToFile(System.getProperty("user.dir") + GeneralConstants.IMPORT_FORMAT_FILE, fileContent);

        Assert.assertNull(formatManagerControl.importFormat(driver, zone, System.getProperty("user.dir") + GeneralConstants.IMPORT_FORMAT_FILE)
                , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
        return formatName;
    }

    @DataProvider(name = "generalSettingsTestData")
    public Object[][] generalSettingsTestData() throws IOException {
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("checkArabicPhonetics");
        DataResource resource =
                new InputStreamResource(new FileInputStream(System.getProperty("user.dir") + csvDataFilePath),
                        GeneralSettings[].class, "json");
        SeLionDataProvider dataProvider =
                DataProviderFactory.getDataProvider(resource);
        return dataProvider.getAllData();

    }

    @DataProvider(name = "engineSettingsTestData")
    public Object[][] engineSettingsTestData() throws IOException {
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("checkEngineSettings");
        DataResource resource =
                new InputStreamResource(new FileInputStream(System.getProperty("user.dir") + csvDataFilePath),
                        EngineTuning[].class, "json");
        SeLionDataProvider dataProvider =
                DataProviderFactory.getDataProvider(resource);
        return dataProvider.getAllData();

    }
}
