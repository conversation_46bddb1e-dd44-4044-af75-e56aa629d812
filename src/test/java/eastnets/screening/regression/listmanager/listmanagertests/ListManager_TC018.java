package eastnets.screening.regression.listmanager.listmanagertests;

import com.paypal.selion.platform.dataprovider.DataProviderFactory;
import com.paypal.selion.platform.dataprovider.DataResource;
import com.paypal.selion.platform.dataprovider.SeLionDataProvider;
import com.paypal.selion.platform.dataprovider.impl.InputStreamResource;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.listManager.BlackListControl;
import eastnets.screening.control.listManager.WorldCheckControl;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.*;

import java.io.FileInputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class ListManager_TC018 extends ListManager_PreCondition {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final BlackListControl blackListControl = new BlackListControl();
    private final WorldCheckControl worldCheckControl = new WorldCheckControl();

    private RemoteWebDriver driver;


    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test Case : Verify that user is able to Add, modify, delete and a World Check flow in different modes
     * STEPS:
     * 1- Create a new black list
     * 2- Add a new World Check flow.
     * 3- Modify the World Check flow.
     * 4- Delete the World Check flow.
     **/

    @Test(groups = {"importlist"}, dataProvider = "worldCheckModesTestData")
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("List Manager")
    public void ListManager_TC018(String worldCheckMode) {

        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setName(String.format("Verify that user is able to Add, modify, delete and a World Check flow as %s type", worldCheckMode)));

            lifecycle.updateTestCase(testResult -> testResult.setDescription(String.format("Verify that user is able to Add a new World Check flow as %s type\n" +
                    "Verify that user is able to Modify a World Check flow\n" +
                    "Verify that user is able to Delete a World Check flow\n" +
                    "Verify that user is able to search for a World Check flow by Zone", worldCheckMode)));

            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getPassword()
            );

            EnList list = commonTestMethods.getEnListData();
            String timestamp = new SimpleDateFormat("hmmsssssss").format(new Date());
            String worldCheckName = "wCheck-" + timestamp;
            String zone = list.getZoneName();

            Allure.step("Create new black list");
            Assert.assertTrue(blackListControl.create_Black_List(driver, list)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new black list.");

            Allure.step(String.format("Verify that user is able to Add a new World Check flow as %s type", worldCheckMode));
            Assert.assertTrue(worldCheckControl.createNewWorldCheck(driver, zone, worldCheckName, list.getName(), worldCheckMode, false, false)
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Allure.step("Verify that user is able to Modify a World Check flow");
            Assert.assertEquals(worldCheckControl.enableWorldCheckByEditing(driver, zone, worldCheckName)
                    , "true"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Allure.step("Verify that user is able to Delete a World Check flow");
            Assert.assertFalse(worldCheckControl.delete_world_check(driver, zone, worldCheckName)
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            commonAction.logout(driver);
        } catch (Exception e) {
            commonAction.logout(driver);
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @DataProvider(name = "worldCheckModesTestData")
    public Object[][] worldCheckModesTestData() throws IOException {
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("verifyWorldCheck");
        DataResource resource =
                new InputStreamResource(new FileInputStream(System.getProperty("user.dir") + csvDataFilePath),
                        String.class, "json");
        SeLionDataProvider dataProvider =
                DataProviderFactory.getDataProvider(resource);
        return dataProvider.getAllData();

    }


}
