package eastnets.screening.regression.listmanager.listmanagertests;

import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.admin.entity.Group;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.common.gui.Navigation;
import eastnets.screening.control.FormatManagerControl;
import eastnets.screening.control.listManager.*;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.Format;
import io.qameta.allure.Allure;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class ListManager_TC019 extends ListManager_PreCondition {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final FormatManagerControl formatManagerControl = new FormatManagerControl();
    private final ActivityControl activityControl = new ActivityControl();
    private final BlackListControl blackListControl = new BlackListControl();
    private final GoodGuyControl goodGuyControl = new GoodGuyControl();
    private final ListExplorerControl listExplorerControl = new ListExplorerControl();
    private final ListSetControl listSetControl = new ListSetControl();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();
    private final core.util.TextFilesHandler textFilesHandler = new core.util.TextFilesHandler();

    private RemoteWebDriver driver;


    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
    /**
     * Test Case : Verify that Good Guys migration for a Private List is functioning properly
     * STEPS:
     * 1- Create a new list set
     * 2- Create a new format to import individual entries.
     * 3- Import an entry to the list.
     * 4- Add the imported entry to the Good Guys list.
     * 5- Create a new version for the black list.
     * 6- Import the same entry to the list.
     * 7- Check black list version.
     **/

    @Test(groups = {"importlist"})
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("List Manager")
    public void listManager_TC019() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that Good Guys migration for a Private List is functioning properly"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that Good Guys migration for a Private List is functioning properly"));


            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getPassword()
            );

            String yesterdayDate = randomizer.yesterday("yyyy/MM/dd");
            String todayDate = randomizer.currentDate;

            Group group = Common.GROUP.FULL_RIGHT_GROUP_02.get();
            EnList list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_002.getZone().getDisplayName());
            list.setOfficialDate(yesterdayDate);
            Allure.step("Create new black list");


            commonTestMethods.createListSet(driver, list, group.getProfile().getName());


            Format format = eastnets.screening.entity.Common.FORMAT.IMPORT_ENTRY_INDIVIDUAL_FORMAT_2.get();
            format.setName("Format-" + randomizer.timestamp);
            format.setZone(Common.ZONE.COMMON_TEST_ZONE_002.getZone().getDisplayName());
            Assert.assertNull(formatManagerControl.AddNewFormat(driver, format), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG + " while creating new format.");


            String entryName = list.getEntry().get(0).getName() + ", " + list.getEntry().get(0).getFirstName();
            String filePath = System.getProperty("user.dir") + GeneralConstants.GG_MIGRATION_ENTRY_LIST_FILE_PATH;
            textFilesHandler.writeToFile(filePath, entryName);
            Assert.assertEquals(listExplorerControl.import_entries(driver, list.getZoneName(), list.getName()
                            , filePath, format.getName(), "", false, false, false, false, false)
                    , "Operation completed successfully."
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Assert.assertEquals(activityControl.get_status(driver)
                    , "SUCCEEDED"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Navigation.SCAN_MANAGER.navigate(driver);

            Assert.assertEquals(listExplorerControl.add_good_guy(driver, list, null, null)
                    , "Good guy successfully created!"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            Assert.assertTrue(goodGuyControl.search_by_name(driver, list.getEntry().get(0).getName())
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            blackListControl.add_black_list_version(driver, randomizer.currentDate, list.getName());

            Assert.assertEquals(listExplorerControl.import_entries(driver, list.getZoneName(), list.getName()
                            , filePath, format.getName(), "", false, true, true, false, false)
                    , "Operation completed successfully."
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Assert.assertEquals(activityControl.get_status(driver)
                    , "SUCCEEDED"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Navigation.SCAN_MANAGER.navigate(driver);

            Assert.assertEquals(listSetControl.get_blackList_version(driver, list.getZoneName(), list.getName()), "v2 - " + randomizer.currentDate
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            commonAction.logout(driver);

        } catch (Exception e) {
            commonAction.logout(driver);
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }




}
