package eastnets.screening.regression.listmanager.listmanagertests;

import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.listManager.BlackListControl;
import io.qameta.allure.Allure;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class ListManager_TC017 extends ListManager_PreCondition {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final BlackListControl blackListControl = new BlackListControl();
    private RemoteWebDriver driver;


    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    /**
     * Test Case : Verify that user is able to Share a Public Black List
     * STEPS:
     * 1- Import 'Congo' Black List
     * 2- Share 'Congo' black list
     * 3- Verify that the black list is shared successfully
     **/

    @Test(groups = {"importlist"})
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("List Manager")
    public void listManager_TC017() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that user is able to Share a Public Black List"));
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that user is able to Share a Public Black List"));

            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getPassword()
            );

            String congoListName = GeneralConstants.CONGO_LIST_NAME;

            screeningServicesDelegate.deleteBlackList(congoListName);

            Allure.step(String.format("Import '%s' Black List", congoListName));
            String filePath = GeneralConstants.CONGO_LIST_FILE_PATH;
            Assert.assertEquals(blackListControl.import_list(driver, null, filePath, false, false)
                    , "SUCCEEDED"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Allure.step(String.format("Share '%s' black list", congoListName));
            Assert.assertNull(blackListControl.share_black_list(driver, congoListName)
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            commonAction.logout(driver);
        } catch (Exception e) {
            commonAction.logout(driver);
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }




}
