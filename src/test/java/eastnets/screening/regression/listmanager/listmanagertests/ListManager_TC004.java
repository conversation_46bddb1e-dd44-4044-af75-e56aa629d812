package eastnets.screening.regression.listmanager.listmanagertests;

import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.common.gui.Navigation;
import eastnets.screening.control.listManager.ListExplorerControl;
import eastnets.screening.control.listManager.ListSetControl;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class ListManager_TC004 extends ListManager_PreCondition {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final ListExplorerControl listExplorerControl = new ListExplorerControl();
    private final ListSetControl listSetControl = new ListSetControl();
    private final NameScanControl nameScanControl = new NameScanControl();

    private RemoteWebDriver driver;


    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test Case: Verify that user is able to Validate a Syntax inside the list set when adding a Violation Filter to it
     * STEPS:
     * 1- Create new entry with details info birthdate = '16/04/1995'
     * 2- Add a Violation Filter to the list set with the following syntax: SW_ENTITY_BIRTHDATE = '16/04/1995'
     * 3- Save the violation filter
     * 4- Navigate to Scan Manager
     * 5- Perform a name scan for the created entry and assert it's clean
     */

    @Test(groups = {"importlist"})
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("Good Guy")
    @Feature("List Manager")
    public void listManager_TC004() {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that user is able to Validate a Syntax inside the list set when adding a Violation Filter to it"));
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that user is able to Validate a Syntax inside the list set when adding a Violation Filter to it"));

            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getPassword()
            );

            EnList list = enList;
            listExplorerControl.search(driver, list);

            Assert.assertTrue(listExplorerControl.create_entry_with_details_info(driver, list.getEntry().get(0))
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            String violationFilter = "SW_ENTITY_BIRTHDATE = '16/04/1995'";
            Assert.assertTrue(listSetControl.add_post_violation_filter(driver, list.getZoneName(), list.getListSet().getName(), list.getName(), violationFilter)
                            .contains("Syntax is valid!")
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);


            Assert.assertEquals(listSetControl.save_violation_filter(driver, false)
                    , "Operation completed successfully."
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            Navigation.SCAN_MANAGER.navigate(driver);
            Assert.assertEquals(nameScanControl.scan_Name(driver, list.getEntry().get(1).getName(), list.getZoneName(), "100", true, true, true, false)
                    , "CLEAN"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            commonAction.logout(driver);
        } catch (Exception e) {
            commonAction.logout(driver);
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


}
