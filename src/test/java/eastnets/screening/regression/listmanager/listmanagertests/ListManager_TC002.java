package eastnets.screening.regression.listmanager.listmanagertests;

import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.listManager.GoodGuyControl;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class ListManager_TC002 extends ListManager_PreCondition {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final GoodGuyControl goodGuyControl = new GoodGuyControl();


    private RemoteWebDriver driver;


    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    /**
     * Test Case: Verify that user is able to delete a Good Guy
     * STEPS:
     * 1- Navigate to List Manager -> Good Guy
     * 2- Click on 'Add' button
     * 3- Fill the required fields to add entry as a Good Guy
     * 4- Click on 'Accept' button
     * 5- Verify that the entry is added successfully
     * 6- Click on 'Delete' button
     */

    @Test(groups = {"importlist"})
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("Good Guy")
    @Feature("List Manager")
    public void listManager_TC002() {
        try {

            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that user is able to delete a Good Guy"));
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that user is able to delete a Good Guy"));

            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getPassword()
            );

            goodGuyControl.search(driver, enList);

            Assert.assertEquals(goodGuyControl.add_good_guy(driver, "deleteGoodGuy, deleteGoodGuy", "50")
                    , "Good guy successfully created!"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Assert.assertFalse(goodGuyControl.delete_good_guy(driver, "deleteGoodGuy, deleteGoodGuy"));

            commonAction.logout(driver);
        } catch (Exception e) {
            commonAction.logout(driver);
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


}
