package eastnets.screening.regression.listmanager.listmanagertests;

import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.admin.entity.Group;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.listManager.ActivityControl;
import eastnets.screening.control.listManager.GoodGuyControl;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class ListManager_TC010 extends ListManager_PreCondition {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final ActivityControl activityControl = new ActivityControl();
    private final GoodGuyControl goodGuyControl = new GoodGuyControl();

    private RemoteWebDriver driver;


    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
    /**
     * Test Case : Verify that user is able to Import a non shared Good Guys.
     * STEPS:
     * 1- Create a new list set.
     * 2- Import black list and link it to the list set.
     * 3- Import a non shared Good Guys.
     * 4- Verify that the import operation is completed successfully.
     **/

    @Test(groups = {"importlist"})
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("List Manager")
    public void listManager_TC010() {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that user is able to Import a non shared Good Guys"));
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that user is able to Import a non shared Good Guys"));

            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getPassword()
            );

            String goodGuyName = "Korea Ryonbong General Corporation";
            String filePath = GeneralConstants.KOREAN_LIST_FILE_PATH;


            Allure.step("Create swift-code, black list and list set.");
            Group group = Common.GROUP.FULL_RIGHT_GROUP_02.get();
            EnList list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_002.getZone().getDisplayName());
            list.setName(GeneralConstants.KOREAN_LIST_NAME);

            screeningServicesDelegate.deleteLinkBtwBlackListAndListSet(list.getZoneName(), GeneralConstants.KOREAN_LIST_NAME);
            screeningServicesDelegate.deleteLinkBtwBlackListAndGoodGuy(list.getZoneName(), GeneralConstants.KOREAN_LIST_NAME);
            screeningServicesDelegate.deleteBlackList(GeneralConstants.KOREAN_LIST_NAME);


            commonTestMethods.importBListAndLinkToNewListSet(driver, list, group.getProfile().getName(), filePath);
            driver.get(driver.getCurrentUrl());
            goodGuyControl.search(driver, list);

            Assert.assertEquals(goodGuyControl.add_good_guy(driver, goodGuyName, "50")
                    , "Good guy successfully created!"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Assert.assertFalse(goodGuyControl.delete_good_guy(driver, goodGuyName));

            String actualResult = goodGuyControl.import_non_shared_gg(driver, list.getZoneName(), list.getListSet().getName(), list.getName()
                    , System.getProperty("user.dir") + GeneralConstants.IMPORT_NON_SHARED_GOOD_GUY_FILE_PATH);

            Assert.assertEquals(actualResult, "Operation completed successfully.", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Assert.assertEquals(activityControl.get_status(driver)
                    , "SUCCEEDED"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while getting import list status.");

            commonAction.logout(driver);
        } catch (Exception e) {
            commonAction.logout(driver);
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


}
