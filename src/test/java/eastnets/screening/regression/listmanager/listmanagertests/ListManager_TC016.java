package eastnets.screening.regression.listmanager.listmanagertests;

import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.listManager.BlackListControl;
import io.qameta.allure.Allure;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class ListManager_TC016 extends ListManager_PreCondition {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final BlackListControl blackListControl = new BlackListControl();
    private RemoteWebDriver driver;


    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test Case : Verify that user is able to search for a List from different zone
     * STEPS:
     * 1- Import 'congo' black list on user linked on 'XYZ Zone'
     * 2- Share 'congo' the black list
     * 3- Import another black list 'yamen' on user linked on 'XYZ Zone'
     * 4- Log out and login with another user linked to 'XYZ Zone'
     * 5- Verify that user linked with 'Common Test Zone' have access to 'congo' shared black list.
     * 6- Verify that user linked with 'Common Test Zone' have access to 'yamen' non-shared black list.
     * 7- Log out and login with another user linked to 'Default Zone'
     * 8- Verify that user linked with 'Default Zone' have access to 'congo' shared black list.
     * 9- Verify that user linked with 'Default Zone' not have access to 'yamen' non-shared black list.
     **/

    @Test(groups = {"importlist"})
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("List Manager")
    public void listManager_TC016() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that user is able to search for a List from different zone"));
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that user is able to search for a List from different zone"));


            loginPage.login(driver
                    , Common.OPERATOR.FOUR_EYES_01.getOperator().getLoginName()
                    , Common.OPERATOR.FOUR_EYES_01.getOperator().getPassword()
            );


            String defaultZone = Common.ZONE.DEFAULT_ZONE.getZone().getDisplayName();
            String commonZone = Common.ZONE.FOUR_EYES_ZONE.getZone().getDisplayName();

            String congoListName = GeneralConstants.CONGO_LIST_NAME;
            String yamenList = GeneralConstants.UN_YAMEN_LIST_NAME;

            screeningServicesDelegate.deleteLinkBtwBlackListAndListSet(commonZone, congoListName);
            screeningServicesDelegate.deleteLinkBtwBlackListAndGoodGuy(commonZone, congoListName);
            screeningServicesDelegate.deleteBlackList(congoListName);
            screeningServicesDelegate.deleteLinkBtwBlackListAndListSet(commonZone, yamenList);
            screeningServicesDelegate.deleteLinkBtwBlackListAndGoodGuy(commonZone, yamenList);
            screeningServicesDelegate.deleteBlackList(yamenList);

            Allure.step(String.format("Import '%s' Black List on user linked on '%s' zone", congoListName, commonZone));
            String filePath = GeneralConstants.CONGO_LIST_FILE_PATH;
            Assert.assertEquals(blackListControl.import_list(driver, commonZone, filePath, false, false)
                    , "SUCCEEDED"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Allure.step(String.format("Share '%s' black list", congoListName));
            Assert.assertNull(blackListControl.share_black_list(driver, congoListName)
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Allure.step(String.format("Import '%s' Black List on user linked on '%s' zone", yamenList, commonZone));
            filePath = System.getProperty("user.dir") + GeneralConstants.UN_YAMEN_LIST_FILE_PATH;
            Assert.assertEquals(blackListControl.import_list(driver, commonZone, filePath, false, false)
                    , "SUCCEEDED"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Allure.step(String.format("Log out and login with another user linked to '%s' zone", commonZone));
            commonAction.logout(driver);
            loginPage.login(driver
                    , Common.OPERATOR.FOUR_EYES_02.getOperator().getLoginName()
                    , Common.OPERATOR.FOUR_EYES_02.getOperator().getPassword()
                    );

            Allure.step(String.format("Verify that user linked with '%s' zone have access to '%s' shared black list.", commonZone, congoListName));
            Assert.assertTrue(blackListControl.verify_list_exist(driver, defaultZone, congoListName)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            Allure.step(String.format("Verify that user linked with '%s' zone have access to '%s' non-shared black list.", commonZone, yamenList));
            Assert.assertTrue(blackListControl.verify_list_exist(driver, defaultZone, yamenList)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);


            Allure.step(String.format("Log out and login with another user linked to '%s' zone", defaultZone));
            commonAction.logout(driver);
            loginPage.login(driver
                    , Common.OPERATOR.DeFAULT_ZONE_OPERATOR_1.getOperator().getLoginName()
                    , Common.OPERATOR.DeFAULT_ZONE_OPERATOR_1.getOperator().getPassword()
                   );

            Allure.step(String.format("Verify that user linked with '%s' zone have access to '%s' shared black list.", defaultZone, congoListName));
            Assert.assertTrue(blackListControl.verify_list_exist(driver, defaultZone, congoListName)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            Allure.step(String.format("Verify that user linked with '%s' zone not have access to '%s' non-shared black list.", defaultZone, yamenList));
            Assert.assertFalse(blackListControl.verify_list_exist(driver, defaultZone, yamenList)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            commonAction.logout(driver);
        } catch (Exception e) {
            commonAction.logout(driver);
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }




}
