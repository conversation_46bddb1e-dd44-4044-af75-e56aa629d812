package eastnets.screening.regression.listmanager.listmanagertests;

import com.fasterxml.jackson.databind.ObjectMapper;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.admin.entity.Group;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.listManager.ListExplorerControl;
import eastnets.screening.control.listManager.ListSetControl;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.ListEntry;
import io.qameta.allure.Allure;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.io.File;
import java.util.List;

public class ListManager_TC021 extends ListManager_PreCondition {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ListExplorerControl listExplorerControl = new ListExplorerControl();
    private final ListSetControl listSetControl = new ListSetControl();
    private final NameScanControl nameScanControl = new NameScanControl();

    String user3 = Common.OPERATOR.FULL_RIGHT_2.getOperator().getLoginName();
    String password = Common.OPERATOR.FULL_RIGHT_2.getOperator().getPassword();

    private RemoteWebDriver driver;


    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test Case : Verify List set configuration - Assign clean alert - Assign Auto create alert - Assign clean & External alert.
     * STEPS:
     * 1- Create a new list set
     * 2- Add a post violation filter
     * 3- Add entries to the list as below :
     * <p>
     * {
     * "name": "Abd ElJabbar Al Omari"
     * },
     * {
     * "name": "Salem Saeed",
     * "birthDate": "16/01/1994"
     * },
     * {
     * "name": "Hasan Hosny"
     * }
     * ]
     * 4- Add 'Hasan Hosny' as good guy
     * 5- Assign clean record
     * 6- Perform a name scan for 'CleanName'
     * 7- Verify that the name is 'CLEAN'
     * 8- Scan the name 'Abd ElJabbar Al Omari' and verify that the name is 'REPNEW'
     * 9- Verify that the investigator is 'operator-05'
     * 10- Scan the name 'Abd ElJabbar Al Omari' and verify that the name is 'REP'
     * 11- Scan the name 'Salem Saeed' and verify that the name is 'EXT'
     * 12- Scan the name 'Hasan Hosny' and verify that the name is 'ACC'
     **/

    @Test(groups = {"importlist"})
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("List Manager")
    public void listManager_TC021() {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify List set configuration - Assign clean alert - Assign Auto create alert - Assign clean & External alert"));
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify List set configuration - Assign clean alert - Assign Auto create alert - Assign clean & External alert"));


            loginPage.login(driver
                    , user3
                    , password
            );

            Allure.step("Create swift-code, black list and list set.");
            Group group = Common.GROUP.FULL_RIGHT_GROUP_02.get();
            EnList list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_002.getZone().getDisplayName());

            commonTestMethods.createListSet(driver, list, group.getProfile().getName());

            String violationFilter = "SW_ENTITY_BIRTHDATE <> '16/01/1994'";
            Assert.assertTrue(listSetControl.add_post_violation_filter(driver, list.getZoneName(), list.getListSet().getName(), list.getName(), violationFilter)
                            .contains("Syntax is valid!")
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            Assert.assertEquals(listSetControl.save_violation_filter(driver, true)
                    , "Operation completed successfully."
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("verifyListSetConfigurations");
            List<ListEntry> entries = List.of(mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), ListEntry[].class));
            for (ListEntry entry : entries) {
                commonTestMethods.createNewEntryWithDetailsInfo(driver, list, entry);
            }

            list.getEntry().get(0).setName(entries.get(2).getName());
            list.getEntry().get(0).setFirstName(null);

            Assert.assertEquals(listExplorerControl.add_good_guy(driver, list, null, "50")
                    , "Good guy successfully created!"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            Assert.assertEquals(listSetControl.assign_clean_record(driver, list.getZoneName(), list.getListSet().getName(), "<Logged operator>")
                    , "Operation completed successfully."
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            Assert.assertEquals(nameScanControl.scan_Name(driver, "CleanName", list.getZoneName(), "100", true, false, false, false)
                    , "CLEAN"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            Assert.assertEquals(nameScanControl.scan_Name(driver, entries.get(0).getName(), list.getZoneName(), "95", true, false, false, false)
                    , "REPNEW"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            Assert.assertEquals(nameScanControl.get_investigator(driver)
                    , user3
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            Assert.assertEquals(nameScanControl.scan_Name(driver, entries.get(0).getName(), list.getZoneName(), "95", false, false, false, false)
                    , "REP"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            Assert.assertEquals(nameScanControl.scan_Name(driver, entries.get(1).getName(), list.getZoneName(), "95", false, false, false, false)
                    , "EXT"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            Assert.assertEquals(nameScanControl.scan_Name(driver, entries.get(2).getName(), list.getZoneName(), "95", false, false, false, false)
                    , "ACC"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            commonAction.logout(driver);
        } catch (Exception e) {
            commonAction.logout(driver);
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


}
