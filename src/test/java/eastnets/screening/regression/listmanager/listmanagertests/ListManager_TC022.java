package eastnets.screening.regression.listmanager.listmanagertests;

import com.fasterxml.jackson.databind.ObjectMapper;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.admin.entity.Group;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.listManager.ListExplorerControl;
import eastnets.screening.control.listManager.ListSetControl;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FileScan;
import io.qameta.allure.Allure;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.io.File;

public class ListManager_TC022 extends ListManager_PreCondition {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ListExplorerControl listExplorerControl = new ListExplorerControl();
    private final ListSetControl listSetControl = new ListSetControl();
    private final ResultManagerControl resultManagerControl = new ResultManagerControl();

    String user3 = Common.OPERATOR.FULL_RIGHT_2.getOperator().getLoginName();
    String password = Common.OPERATOR.FULL_RIGHT_2.getOperator().getPassword();

    private RemoteWebDriver driver;


    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    /**
     * Test Case : Verify GPI Lables as conditions and Add SERV_TYPE_ID (SLA_ID)
     * and UETR as violation filter and make sure that the detection has External status.
     * STEPS:
     * 1- Create a new list set.
     * 2- Add a new entry to the list.
     * 3- Add "SERV_TYPE_ID <> '001'" as violation filter.
     * 4- Save the violation filter.
     * 5- Scan a file = UPLOAD_FILE_PATH+"/MT103-both.txt" and get the detection ID.
     * 6- Verify that the detection has External status.
     * 7- Verify that unmatched roles are equal to "SERV_TYPE_ID <> '001'".
     * 8- Add "UETR <> '8E0FE365-A88E-426B-8484-4FB7FEE92742'" as violation filter.
     * 9- Save the violation filter.
     * 10- Scan the same file and get the detection ID.
     * 11- Verify that the detection has External status.
     * 12- Verify that unmatched roles are equal to "UETR <> '8E0FE365-A88E-426B-8484-4FB7FEE92742'".
     **/

    @Test(groups = {"importlist"})
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("List Manager")
    public void listManager_TC022() {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify GPI Lables as conditions and Add SERV_TYPE_ID (SLA_ID) and UETR as violation filter and make sure that the detection has External status."));
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify GPI Lables as conditions and Add SERV_TYPE_ID (SLA_ID) and UETR as violation filter and make sure that the detection has External status."));

            loginPage.login(driver
                    , user3
                    , password
            );

            Allure.step("Create swift-code, black list and list set.");
            Group group = Common.GROUP.FULL_RIGHT_GROUP_02.get();
            EnList list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_002.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, list, group.getProfile().getName());


            list.getEntry().get(0).setName("Osama bin laden");

            listExplorerControl.search(driver, list);
            Assert.assertTrue(listExplorerControl.create_entry_with_details_info(driver, list.getEntry().get(0))
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            String violationFilter = "SERV_TYPE_ID <> '001'";
            Assert.assertTrue(listSetControl.add_post_violation_filter(driver, list.getZoneName()
                            , list.getListSet().getName(), list.getName(), violationFilter).contains("Syntax is valid!")
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            Assert.assertEquals(listSetControl.save_violation_filter(driver, true)
                    , "Operation completed successfully."
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("blockDetection");
            FileScan fileScan = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), FileScan.class);
            fileScan.setFilePath(GeneralConstants.MT103_BOTH_FILE_PATH);
            fileScan.setResult("All suspected records");

            commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            Assert.assertEquals(resultManagerControl.get_detection_status(driver)
                    , "External"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            Assert.assertEquals(resultManagerControl.get_unmatched_roles(driver)
                    , violationFilter
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            violationFilter = "UETR <> '8E0FE365-A88E-426B-8484-4FB7FEE92742'";
            Assert.assertTrue(listSetControl.add_post_violation_filter(driver, list.getZoneName(), list.getListSet().getName(), list.getName(), violationFilter)
                            .contains("Syntax is valid!")
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            Assert.assertEquals(listSetControl.save_violation_filter(driver, true)
                    , "Operation completed successfully."
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            Assert.assertEquals(resultManagerControl.get_detection_status(driver)
                    , "External"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            Assert.assertEquals(resultManagerControl.get_unmatched_roles(driver)
                    , violationFilter
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);


            commonAction.logout(driver);
        } catch (Exception e) {
            commonAction.logout(driver);
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }




}
