package eastnets.screening.regression.listmanager.listmanagertests;

import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.listManager.ListExplorerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.ListEntry;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class ListManager_TC005 extends ListManager_PreCondition {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final ListExplorerControl listExplorerControl = new ListExplorerControl();


    private RemoteWebDriver driver;


    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
    /**
     * Test Case : Verify that user is able to Edit an entity from Black List
     * STEPS:
     * 1- Navigate to List Manager -> List Manager
     * 2- Create a new entity in Black List
     * 3- Edit the created entity and assert that the entity is edited successfully
     */

    @Test(groups = {"importlist"})
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("List Manager")
    public void ListManager_TC005() {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that user is able to Edit an entity from Black List"));
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that user is able to Edit an entity from Black List"));

            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getPassword()
            );

            EnList list = enList;
            ListEntry entry = list.getEntry().get(1);
            listExplorerControl.search(driver, list);

            Assert.assertTrue(listExplorerControl.create_entry_with_type_and_names(driver, entry)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);


            Assert.assertTrue(listExplorerControl.edit_entry(driver, list.getZoneName(), entry)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            Assert.assertTrue(listExplorerControl.is_entry_exist(driver, list.getZoneName(), entry)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            commonAction.logout(driver);
        } catch (Exception e) {
            commonAction.logout(driver);
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

}
