package eastnets.screening.regression.listmanager.listmanagertests;

import com.paypal.selion.platform.dataprovider.DataProviderFactory;
import com.paypal.selion.platform.dataprovider.DataResource;
import com.paypal.selion.platform.dataprovider.SeLionDataProvider;
import com.paypal.selion.platform.dataprovider.impl.InputStreamResource;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.listManager.BlackListControl;
import io.qameta.allure.Allure;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.*;

import java.io.FileInputStream;
import java.io.IOException;

public class ListManager_TC014 extends ListManager_PreCondition {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final BlackListControl blackListControl = new BlackListControl();
    private RemoteWebDriver driver;


    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test Case : Verify that user is able to Export private/public black list in different formats
     **/

    @Test(groups = {"importlist"}, dataProvider = "exportBlackListTestData")
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("List Manager")
    public void listManager_TC014(String privateFlagAndExportType) {

        try {
            String privateFlag = privateFlagAndExportType.split(",")[0];
            String exportType = privateFlagAndExportType.split(",")[1];
            String finalPrivateFlag = privateFlag;

            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that user is able to Export a version from a " + finalPrivateFlag + " black list as " + exportType));
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that user is able to Export a version from a " + finalPrivateFlag + " black list as " + exportType));


            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getPassword()
            );

            privateFlag = privateFlag.equalsIgnoreCase("private") ? "Yes" : "No";

            Assert.assertEquals(blackListControl.export_black_list(driver, Common.ZONE.COMMON_TEST_ZONE_002.getZone().getDisplayName(), privateFlag, exportType)
                    , "SUCCEEDED"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            commonAction.logout(driver);

        } catch (Exception e) {
            commonAction.logout(driver);
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
            commonAction.logout(driver);

        }
    }




    @DataProvider(name = "exportBlackListTestData")
    public Object[][] exportBlackListTestData() throws IOException {
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("exportBlackList");
        DataResource resource =
                new InputStreamResource(new FileInputStream(System.getProperty("user.dir") + csvDataFilePath),
                        String.class, "json");
        SeLionDataProvider dataProvider =
                DataProviderFactory.getDataProvider(resource);
        return dataProvider.getAllData();

    }


}
