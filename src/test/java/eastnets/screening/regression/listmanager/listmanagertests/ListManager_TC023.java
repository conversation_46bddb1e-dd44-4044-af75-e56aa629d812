package eastnets.screening.regression.listmanager.listmanagertests;


import core.ExceptionHandler;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.listManager.BlackListControl;
import io.qameta.allure.Allure;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;

public class ListManager_TC023 extends ListManager_PreCondition {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final BlackListControl blackListControl = new BlackListControl();

    String user3 = Common.OPERATOR.FULL_RIGHT_2.getOperator().getLoginName();
    String password = Common.OPERATOR.FULL_RIGHT_2.getOperator().getPassword();

    private RemoteWebDriver driver;


    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test Case : Verify that all the buttons are visible on the list manager page.
     * STEPS:
     * 1- Navigate to the Black List page.
     * 2- Verify that rest button is visible.
     * 3- Verify that search button is visible.
     * 4- Verify that create new list button is visible.
     * 5- Verify that delete list button is visible.
     * 6- Verify that share list button is visible.
     * 7- Verify that import list button is visible.
     */

    @Test(groups = {"importlist"})
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("List Manager")
    public void listManager_TC023() {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that user all the buttons are visible on the Import Black List page"));
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that user all the buttons are visible on the Import Black List page"));

            loginPage.login(driver
                    , user3
                    , password
            );

            SoftAssert softAssert = new SoftAssert();

            driver.navigate().refresh();
            blackListControl.navigate(driver);
            softAssert.assertTrue(blackListControl.verify_reset_button_visibility(driver)
                    , "Reset button not visible");

            softAssert.assertTrue(blackListControl.verify_search_button_visibility(driver)
                    , "Search button not visible");

            softAssert.assertTrue(blackListControl.verify_create_new_list_button_visibility(driver)
                    , "Create new button not visible");

            softAssert.assertTrue(blackListControl.verify_delete_list_button_visibility(driver)
                    , "Delete button not visible");

            softAssert.assertTrue(blackListControl.verify_share_list_button_visibility(driver)
                    , "Share button not visible");

            softAssert.assertTrue(blackListControl.verify_import_list_button_visibility(driver)
                    , "Import button not visible");

            softAssert.assertAll();

            commonAction.logout(driver);
        } catch (Exception e) {
            commonAction.logout(driver);
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }



}
