package eastnets.screening.regression.listmanager.listmanagertests;

import com.paypal.selion.platform.dataprovider.DataProviderFactory;
import com.paypal.selion.platform.dataprovider.DataResource;
import com.paypal.selion.platform.dataprovider.SeLionDataProvider;
import com.paypal.selion.platform.dataprovider.impl.InputStreamResource;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.listManager.ListExplorerControl;
import io.qameta.allure.Allure;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.*;

import java.io.FileInputStream;
import java.io.IOException;

public class ListManager_TC013 extends ListManager_PreCondition {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final ListExplorerControl listExplorerControl = new ListExplorerControl();
    private RemoteWebDriver driver;


    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
    /**
     * Test Case : Verify that user is able to Export Entries from a black list in different formats.
     **/

    @Test(groups = {"importlist"}, dataProvider = "exportEntriesTestData")
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("List Manager")
    public void listManager_TC013(String type) {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that user is able to Export Entries from a black list as " + type));
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that user is able to Export Entries from a black list as " + type));


            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getPassword()
                    );

            clearDownloadedFiles();
            Assert.assertTrue(listExplorerControl.export_entries(driver, Common.ZONE.COMMON_TEST_ZONE_002.getZone().getDisplayName(), "black-list-entries", type, dockerServerIp)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            commonAction.logout(driver);

        } catch (Exception e) {
            commonAction.logout(driver);
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }



    @DataProvider(name = "exportEntriesTestData")
    public Object[][] exportEntriesTestData() throws IOException {
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("exportEntry");
        DataResource resource =
                new InputStreamResource(new FileInputStream(System.getProperty("user.dir") + csvDataFilePath),
                        String.class, "json");
        SeLionDataProvider dataProvider =
                DataProviderFactory.getDataProvider(resource);
        return dataProvider.getAllData();

    }


}
