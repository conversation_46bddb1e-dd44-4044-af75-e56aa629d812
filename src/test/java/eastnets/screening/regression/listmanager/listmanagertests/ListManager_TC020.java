package eastnets.screening.regression.listmanager.listmanagertests;

import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.common.gui.Navigation;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.listManager.BlackListControl;
import eastnets.screening.control.listManager.GoodGuyControl;
import eastnets.screening.control.listManager.ListExplorerControl;
import eastnets.screening.control.listManager.ListSetControl;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class ListManager_TC020 extends ListManager_PreCondition {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final BlackListControl blackListControl = new BlackListControl();
    private final GoodGuyControl goodGuyControl = new GoodGuyControl();
    private final ListExplorerControl listExplorerControl = new ListExplorerControl();
    private final ListSetControl listSetControl = new ListSetControl();


    private RemoteWebDriver driver;
    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test Case : Verify that Good Guys migration for a public List is functioning properly
     * STEPS:
     * 1- Create a new list
     * 2- Import a black list 'Congo' and link it to a new list set.
     * 3- Add a good guy to the list
     * 3- Import upgrade file for the black list 'Congo'
     * 4- Verify that the black list version is updated to v2
     **/

    @Test(groups = {"importlist"})
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("List Manager")
    public void listManager_TC020() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that Good Guys migration for a public List is functioning properly"));
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that Good Guys migration for a public List is functioning properly"));

            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getPassword()
            );

            EnList list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_002.getZone().getDisplayName());
            list.setName(GeneralConstants.CONGO_LIST_NAME);
            list.getEntry().get(0).setName("BWAMBALE");
            list.getEntry().get(0).setFirstName("Frank Kakolele");
            String filePath = GeneralConstants.CONGO_LIST_FILE_PATH;


            screeningServicesDelegate.deleteLinkBtwBlackListAndListSet(list.getZoneName(), GeneralConstants.CONGO_LIST_NAME);
            screeningServicesDelegate.deleteLinkBtwBlackListAndGoodGuy(list.getZoneName(), GeneralConstants.CONGO_LIST_NAME);
            screeningServicesDelegate.deleteBlackList(GeneralConstants.CONGO_LIST_NAME);

            commonTestMethods.importBListAndLinkToNewListSet(driver, list, Common.PROFILE.FULL_RIGHT_002.getProfile().getName(), filePath);

            Assert.assertEquals(listExplorerControl.add_good_guy(driver, list, null, "50")
                    , "Good guy successfully created!"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            Assert.assertTrue(goodGuyControl.search_by_name(driver, list.getEntry().get(0).getName())
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            Assert.assertEquals(blackListControl.import_list(driver, list.getZoneName(), GeneralConstants.UPGRADE_CONGO_LIST_FILE_PATH, false, true)
                    , "SUCCEEDED"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            Navigation.SCAN_MANAGER.navigate(driver);

            Assert.assertTrue(listSetControl.get_blackList_version(driver, list.getZoneName(), list.getName()).contains("v2")
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            commonAction.logout(driver);
        } catch (Exception e) {
            commonAction.logout(driver);
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

}
