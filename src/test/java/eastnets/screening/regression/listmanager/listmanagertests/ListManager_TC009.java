package eastnets.screening.regression.listmanager.listmanagertests;

import com.paypal.selion.platform.dataprovider.DataProviderFactory;
import com.paypal.selion.platform.dataprovider.DataResource;
import com.paypal.selion.platform.dataprovider.SeLionDataProvider;
import com.paypal.selion.platform.dataprovider.impl.InputStreamResource;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.listManager.GoodGuyControl;
import io.qameta.allure.Allure;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.*;

import java.io.FileInputStream;
import java.io.IOException;

public class ListManager_TC009 extends ListManager_PreCondition {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final GoodGuyControl goodGuyControl = new GoodGuyControl();

    private RemoteWebDriver driver;


    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test Case : Verify that user is able to Export Good Guys in different formats
     * STEPS:
     * 1- Navigate to List Manager --> Good Guys
     * 2- Select Export format then click on Export button
     * 3- Verify that the file is downloaded successfully
     */

    @Test(groups = {"importlist"}, dataProvider = "exportGGTestData")
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("List Manager")
    public void listManager_TC009(String exportType) {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that user is able to Export Good Guys"));
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that user is able to Export Good Guys"));

            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getPassword()
            );

            clearDownloadedFiles();
            Assert.assertTrue(goodGuyControl.export_good_guy(driver, "good-guys", exportType, dockerServerIp), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            commonAction.logout(driver);
        } catch (Exception e) {

            commonAction.logout(driver);
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @DataProvider(name = "exportGGTestData")
    public Object[][] exportGGTestData() throws IOException {
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("exportGG");
        DataResource resource =
                new InputStreamResource(new FileInputStream(System.getProperty("user.dir") + csvDataFilePath),
                        String.class, "json");
        SeLionDataProvider dataProvider =
                DataProviderFactory.getDataProvider(resource);
        return dataProvider.getAllData();

    }

}
