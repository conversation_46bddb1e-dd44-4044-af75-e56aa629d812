package eastnets.screening.regression.listmanager.listmanagertests;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.admin.entity.Profile;
import eastnets.admin.entity.Zone;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.Format;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeGroups;

public class ListManager_PreCondition extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();

    private RemoteWebDriver driver;
    static EnList enList;


    @BeforeGroups({"importlist"})
    public void importList() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setName("List Manager Pre-Condition"));
            lifecycle.updateTestCase(testResult -> testResult.setDescription("List Manager Pre-Condition"));
            driver = getDriver();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getPassword());
            Profile profile = Common.PROFILE.FULL_RIGHT_002.getProfile();
            Zone commanZone = Common.ZONE.COMMON_TEST_ZONE_002.getZone();
            String filePathIndividual = System.getProperty("user.dir") + GeneralConstants.IMPORT_ENTRY_NAMES_AKA_LIST_FILE_PATH;

            Format format = eastnets.screening.entity.Common.FORMAT.IMPORT_ENTRY_NAMES_AKA.get();
            format.setZone(commanZone.getDisplayName());

            Allure.step(String.format("Start add entries on '%s' zone.", commanZone.getDisplayName()));
            enList = commonTestMethods.getEnListData();
            enList.setZoneName(commanZone.getDisplayName());
            commonTestMethods.addListSetAndimportEntries(driver,enList, profile, commanZone, filePathIndividual, format, null);
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
