package eastnets.screening.regression.listmanager.listmanagertests;

import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.listManager.BlackListControl;
import eastnets.screening.control.listManager.ListSetControl;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class ListManager_TC007 extends ListManager_PreCondition {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final BlackListControl blackListControl = new BlackListControl();
    private final ListSetControl listSetControl = new ListSetControl();
    private RemoteWebDriver driver;


    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test Case : Verify that user is able to Lock a Public Black List version.
     * STEPS:
     * 1- Imort a Black List = 'CongoList'
     * 2- Link the Black List to a List Set
     * 3- Lock the Black List.
     */

    @Test(groups = {"importlist"})
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("List Manager")
    public void listManager_TC007() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that user is able to Lock a Public Black List version"));
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that user is able to Lock a Public Black List version"));

            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getPassword()
            );

            EnList list = enList;
            list.setName(GeneralConstants.CONGO_LIST_NAME);
            String filePath = GeneralConstants.CONGO_LIST_FILE_PATH;

            screeningServicesDelegate.deleteLinkBtwBlackListAndListSet(list.getZoneName(), GeneralConstants.CONGO_LIST_NAME);
            screeningServicesDelegate.deleteLinkBtwBlackListAndGoodGuy(list.getZoneName(), GeneralConstants.CONGO_LIST_NAME);
            screeningServicesDelegate.deleteBlackList(GeneralConstants.CONGO_LIST_NAME);

            Assert.assertEquals(blackListControl.import_list(driver, list.getZoneName(), filePath, false, false)
                    ,"SUCCEEDED"
                    ,"" + GeneralConstants.CONGO_LIST_NAME + " list import failed");
            listSetControl.link_black_list_to_exist_listSet(driver, list);
            Assert.assertTrue(blackListControl.Lock_black_list(driver, list.getZoneName(), GeneralConstants.CONGO_LIST_NAME)
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            commonAction.logout(driver);
        } catch (Exception e) {
            commonAction.logout(driver);
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


}
