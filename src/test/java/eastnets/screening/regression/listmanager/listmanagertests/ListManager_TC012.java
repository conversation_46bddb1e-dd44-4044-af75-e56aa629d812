package eastnets.screening.regression.listmanager.listmanagertests;

import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.SwiftManagerControl;
import eastnets.screening.control.listManager.ListSetControl;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import core.CommonTestMethods;

public class ListManager_TC012 extends ListManager_PreCondition {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final SwiftManagerControl swiftManagerControl = new SwiftManagerControl();
    private final ListSetControl listSetControl = new ListSetControl();

    private RemoteWebDriver driver;


    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
    /**
     * Test Case : Verify that user is able to Delete a list set
     * STEPS:
     * 1- Create list set
     * 2- Delete list set
     **/

    @Test(groups = {"importlist"})
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("List Manager")
    public void listManager_TC012() {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that user is able to Delete a list set"));
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that user is able to Delete a list set"));

            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getPassword()
            );


            EnList list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_002.getZone().getDisplayName());


            Assert.assertTrue(swiftManagerControl.CreateSwiftTemplate(driver, list.getListSet().getSwiftTemplate(), list.getZoneName())
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while create new swift template.");

            Assert.assertTrue(listSetControl.create_list_set(driver, list).contains(list.getExpectedMessage())
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new list set.");

            driver.get(driver.getCurrentUrl());
            Assert.assertFalse(listSetControl.delete_list_set(driver, list.getZoneName(), list.getListSet().getName())
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while delete list set.");

            commonAction.logout(driver);
        } catch (Exception e) {
            commonAction.logout(driver);
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }




}
