package eastnets.screening.regression.listmanager.worldchecktest;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.backendServices.AdminServicesDelegate;
import eastnets.admin.entity.Common;
import eastnets.admin.entity.Operator;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.listManager.BlackListControl;
import eastnets.screening.control.listManager.ListExplorerControl;
import eastnets.screening.control.listManager.WorldCheckControl;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Feature;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class WorldCheck_TC_94752 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final AdminServicesDelegate adminServicesDelegate = new AdminServicesDelegate();
    private final BlackListControl blackListControl = new BlackListControl();
    private final ListExplorerControl listExplorerControl = new ListExplorerControl();
    private final WorldCheckControl worldCheckControl = new WorldCheckControl();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();

    private final Operator operator = Common.OPERATOR.FULL_RIGHT_3.getOperator();
    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(
                    driver
                    , Common.OPERATOR.FULL_RIGHT_3.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_3.getOperator().getPassword()
            );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test Case 94752 - Verify data file loading using 'selectionSetName' and 'data' parameters.
     * STEPS:
     * 1- Create a new black list
     * 2- Create a new World Check with PEP mode
     * 3- Run the World Check command
     * "java -jar #JAR_FILE_NAME#.jar connectString=localhost:2181,localhost:2182,
     * localhost:2183 swlUri=#LOGIN_FILE# data=#DATA_FILE#.csv selectionSetName=#WORLD_CHECK_LIST#"
     * 4- Verify the number of rows in the list
     */

    @Test()
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("List Manager")
    @Feature("World Check")
    @Issue("94752")
    public void WorldCheck_94752() {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Test Case 94752- Verify data file loading using 'selectionSetName' and 'data' parameters"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Test Case 94752- Verify data file loading using 'selectionSetName' and 'data' parameters"));


            EnList blackList = commonTestMethods.getEnListData();
            blackList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_003.getZone().getDisplayName());
            Assert.assertTrue(blackListControl.create_Black_List(driver, blackList)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new black list.");

            String worldCheckName = "WCheck-" + randomizer.timestamp1;
            Assert.assertTrue(worldCheckControl.createNewWorldCheck(driver, blackList.getZoneName()
                            , worldCheckName
                            , blackList.getName()
                            , "PEP", true
                            , false)
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


            String zoneID = adminServicesDelegate.getZoneId(blackList.getZoneName());
            Assert.assertTrue(worldCheckControl.create_wc_files_and_run_cmd(GeneralConstants.WORLD_CHECK_COMMANDS[1]
                    , operator.getLoginName()
                    , operator.getPassword()
                    , zoneID
                    , "premium-world-check-day"
                    , worldCheckName
                    , ""
                    , "").contains("[World-Check] 2006 records matched the filters"), GeneralConstants.POM_EXCEPTION_ERR_MSG);

            Assert.assertEquals(listExplorerControl.get_Rows_Count(driver, blackList),
                    "2006 row(s)",
                    GeneralConstants.POM_EXCEPTION_ERR_MSG + " while getting rows count.");


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
