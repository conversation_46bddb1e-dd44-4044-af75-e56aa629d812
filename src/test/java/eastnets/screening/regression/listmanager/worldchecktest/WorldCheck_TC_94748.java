package eastnets.screening.regression.listmanager.worldchecktest;

import core.BaseTest;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.backendServices.AdminServicesDelegate;
import eastnets.admin.entity.Common;
import eastnets.admin.entity.Operator;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.listManager.WorldCheckControl;
import io.qameta.allure.*;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class WorldCheck_TC_94748 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final AdminServicesDelegate adminServicesDelegate = new AdminServicesDelegate();
    private final WorldCheckControl worldCheckControl = new WorldCheckControl();

    private final Operator operator = Common.OPERATOR.FULL_RIGHT_3.getOperator();
    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(
                    driver
                    , Common.OPERATOR.FULL_RIGHT_3.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_3.getOperator().getPassword()
            );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test Case 94748 - Verify umbrella keywords loading using 'umbrella'.
     * STEPS:
     * 1- Create a new World-Check file using 'umbrella' keywords.
     * "java -jar #JAR_FILE_NAME#.jar connectString=localhost:2181,
     * localhost:2182,localhost:2183 swlUri=#LOGIN_FILE# umbrella=\"#REF_FILE#.csv\"",
     * 2- Verify that the file is loaded successfully.
     */

    @Test()
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("List Manager")
    @Feature("World Check")
    @Issue("94748")
    public void WorldCheck_94748() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Test Case 94748 - Verify umbrella keywords loading using 'umbrella'"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Test Case 94748 - Verify umbrella keywords loading using 'umbrella'"));

            String zoneID = adminServicesDelegate.getZoneId(Common.ZONE.COMMON_TEST_ZONE_003.getZone().getDisplayName());
            Assert.assertTrue(worldCheckControl.create_wc_files_and_run_cmd(GeneralConstants.WORLD_CHECK_COMMANDS[5]
                    , operator.getLoginName()
                    , operator.getPassword()
                    , zoneID
                    , ""
                    , ""
                    , ""
                    , "Umbrella Keywords").contains("[World-Check] Umbrella Keywords file loaded with 227 records"), GeneralConstants.POM_EXCEPTION_ERR_MSG);

            Assert.assertTrue(worldCheckControl.get_avail_umbrella_list_size(driver, Common.ZONE.COMMON_TEST_ZONE_003.getZone().getDisplayName()
                            , "Umbrella") > 0
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
