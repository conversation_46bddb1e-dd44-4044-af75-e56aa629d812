package eastnets.screening.regression.listmanager.worldchecktest;

import core.BaseTest;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.backendServices.AdminServicesDelegate;
import eastnets.admin.entity.Common;
import eastnets.admin.entity.Operator;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.listManager.WorldCheckControl;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Feature;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class WorldCheck_TC_94746 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final AdminServicesDelegate adminServicesDelegate = new AdminServicesDelegate();
    private final WorldCheckControl worldCheckControl = new WorldCheckControl();

    private final Operator operator = Common.OPERATOR.FULL_RIGHT_3.getOperator();
    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(
                    driver
                    , Common.OPERATOR.FULL_RIGHT_3.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_3.getOperator().getPassword()
            );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test Case 94746 - Verify sanction keywords loading using 'ref'
     * STEPS:
     * 1- Create World-Check files and run it on the server to load referential
     * "java -jar #JAR_FILE_NAME#.jar connectString=localhost:2181,localhost:2182,
     * localhost:2183 swlUri=#LOGIN_FILE# ref=\"#REF_FILE#.txt\"",
     * 2- Verify that the keywords file is loaded with 1170 records
     */

    @Test()
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("List Manager")
    @Feature("World Check")
    @Issue("94746")
    public void WorldCheck_94746() {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Test Case 94746 - Verify sanction keywords loading using 'ref'"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Test Case 94746 - Verify sanction keywords loading using 'ref'"));

            String zoneID = adminServicesDelegate.getZoneId(Common.ZONE.COMMON_TEST_ZONE_003.getZone().getDisplayName());
            Assert.assertTrue(worldCheckControl.create_wc_files_and_run_cmd(GeneralConstants.WORLD_CHECK_COMMANDS[4]
                    , operator.getLoginName()
                    , operator.getPassword()
                    , zoneID
                    , ""
                    , ""
                    , ""
                    , "World-Check_Keyword-list").contains("Keywords file loaded with 1170 records"), GeneralConstants.POM_EXCEPTION_ERR_MSG);

            Assert.assertEquals(worldCheckControl.get_avail_country_list_size(driver, Common.ZONE.COMMON_TEST_ZONE_003.getZone().getDisplayName()
                            , "Sanction")
                    , "1170"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
