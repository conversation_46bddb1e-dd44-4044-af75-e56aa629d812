package eastnets.screening.regression.listmanager.prefiltertests;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.listManager.ListSetControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.gui.listManager.listSet.ListSetEditor;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class PreFilter_TC002 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ListSetControl listSetControl = new ListSetControl();
    private final ListSetEditor listSetEditor = new ListSetEditor();

    private RemoteWebDriver driver;


    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    /**
     * Test Case : Verify that the validation message should be displayed when validating the mentioned prefilter
     * STEPS:
     * 1- Create a new list set
     * 2- Add a prefilter with invalid syntax
     * 3- Verify that the validation message is displayed
     **/

    @Test()
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("List Manager")
    @Feature("PreFilter")
    public void PreFilter_TC002() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that the validation message should be displayed when validating the mentioned prefilter"));
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that the validation message should be displayed when validating the mentioned prefilter"));

            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getPassword()
            );
            String filter = "import java.io.*;\n" +
                    "import java.nio.file.Files;\n" +
                    "import java.nio.file.Path;\n" +
                    "import java.nio.file.Paths;\n" +
                    "File file = new File(\"D:\\\\scanFile.txt\");\n" +
                    "Path path = Paths.get(\"File Path and Name\");\n" +
                    " boolean value = Files.deleteIfExists(path);\n" +
                    "      if(value) {\n" +
                    "        System.out.println(\"JavaFile.java is successfully deleted.\");\n" +
                    "      }\n" +
                    "      else {\n" +
                    "        System.out.println(\"File doesn't exit\");\n" +
                    "      }\n" +
                    "return false;\n";


            EnList list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_002.getZone().getDisplayName());


            commonTestMethods.createListSet(driver, list, Common.PROFILE.FULL_RIGHT_002.getProfile().getName()); //create list

            Assert.assertEquals(listSetControl.add_pre_violation_filter(driver, list.getZoneName(), list.getListSet().getName(), list.getName(), filter)
                    , "Syntax could not be validated!"
                    , "Error message is not correct");
            listSetEditor.click_save_button(driver);

            commonAction.logout(driver);
        } catch (Exception e) {
            commonAction.logout(driver);
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }



}