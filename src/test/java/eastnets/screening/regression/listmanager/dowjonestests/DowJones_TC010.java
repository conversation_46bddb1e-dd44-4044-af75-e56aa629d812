package eastnets.screening.regression.listmanager.dowjonestests;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.SwiftManagerControl;
import eastnets.screening.control.listManager.BlackListControl;
import eastnets.screening.control.listManager.DowJonesControl;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class DowJones_TC010 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final SwiftManagerControl swiftManagerControl = new SwiftManagerControl();
    private final BlackListControl blackListControl = new BlackListControl();
    private final DowJonesControl dowJonesControl = new DowJonesControl();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    /**
     * Test Case: Verify Dow Jones
     * STEPS:
     * Verify that user is able to create a new Dow Jones flow as Adverse Media Entities type
     * Verify that user is able to enable a Dow Jones flow
     * Verify that user is able to disable a Dow Jones flow
     * Verify that user is able to search for a Dow Jones flow by Zone
     * Verify that user is able to create a new Dow Jones flow as PEP type
     * Verify that user is able to create a new Dow Jones flow as Lists (including Sanctions) type
     * Verify that user is able to create a new Dow Jones flow as Other Categories type
     */
    @Test()
    @Owner("Sarah Abdellatif")
    @Tag("Regression")
    @Feature("Dow Jones")
    public void dowJones_TC010() {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify Dow Jones"));
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that user is able to create a new Dow Jones flow as Adverse Media Entities type\n" +
                    "Verify that user is able to delete a Dow Jones flow\n" +
                    "Verify that user is able to enable a Dow Jones flow\n" +
                    "Verify that user is able to disable a Dow Jones flow\n" +
                    "Verify that user is able to search for a Dow Jones flow by Zone\n" +
                    "Verify that user is able to create a new Dow Jones flow as PEP type\n" +
                    "Verify that user is able to create a new Dow Jones flow as Lists (including Sanctions) type\n" +
                    "Verify that user is able to create a new Dow Jones flow as Other Categories type"));

            EnList list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());

            Assert.assertTrue(swiftManagerControl.CreateSwiftTemplate(driver, list.getListSet().getSwiftTemplate(), list.getZoneName())
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while create new swift template.");

            Assert.assertTrue(blackListControl.create_Black_List(driver, list)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new black list.");

            //create a new Dow Jones flow as Adverse Media Entities type
            Assert.assertTrue(dowJonesControl.create_dj_list(driver
                    , list
                    , "Adverse Media Entities"
                    , false), GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new Dow-Jones list.");

            Assert.assertEquals(dowJonesControl.enable_dj_list(driver), list.getExpectedMessage(), GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new Dow-Jones list.");

            Assert.assertEquals(dowJonesControl.disable_dj_list(driver), list.getExpectedMessage(), GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new Dow-Jones list.");

            Assert.assertFalse(dowJonesControl.delete_dj_list(driver, list.getName())
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while deleting Dow-Jones list.");


            //create a new Dow Jones flow as PEP type
            Assert.assertTrue(dowJonesControl.create_dj_list(driver
                    , list
                    , "PEP"
                    , false), GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new Dow-Jones list.");

            Assert.assertEquals(dowJonesControl.enable_dj_list(driver), list.getExpectedMessage(), GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new Dow-Jones list.");

            Assert.assertEquals(dowJonesControl.disable_dj_list(driver), list.getExpectedMessage(), GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new Dow-Jones list.");

            Assert.assertFalse(dowJonesControl.delete_dj_list(driver, list.getName())
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while deleting Dow-Jones list.");

            //create a new Dow Jones flow as Lists (including Sanctions) type
            Assert.assertTrue(dowJonesControl.create_dj_list(driver
                    , list
                    , "Lists (including Sanctions) - By Sanction References"
                    , false), GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new Dow-Jones list.");

            Assert.assertEquals(dowJonesControl.enable_dj_list(driver), list.getExpectedMessage(), GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new Dow-Jones list.");

            Assert.assertEquals(dowJonesControl.disable_dj_list(driver), list.getExpectedMessage(), GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new Dow-Jones list.");

            Assert.assertFalse(dowJonesControl.delete_dj_list(driver, list.getName())
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while deleting Dow-Jones list.");

            //create a new Dow Jones flow as Other Categories type
            Assert.assertTrue(dowJonesControl.create_dj_list(driver
                    , list
                    , "Other Categories"
                    , false), GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new Dow-Jones list.");

            Assert.assertEquals(dowJonesControl.enable_dj_list(driver), list.getExpectedMessage(), GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new Dow-Jones list.");

            Assert.assertEquals(dowJonesControl.disable_dj_list(driver), list.getExpectedMessage(), GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new Dow-Jones list.");

            Assert.assertFalse(dowJonesControl.delete_dj_list(driver, list.getName())
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while deleting Dow-Jones list.");
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
