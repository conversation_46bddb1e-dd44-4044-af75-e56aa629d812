package eastnets.screening.regression.listmanager.dowjonestests;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.backendServices.AdminServicesDelegate;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.SwiftManagerControl;
import eastnets.screening.control.listManager.BlackListControl;
import eastnets.screening.control.listManager.DowJonesControl;
import eastnets.screening.control.listManager.ListExplorerControl;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class DowJones_TC006 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final AdminServicesDelegate adminServicesDelegate = new AdminServicesDelegate();
    private final SwiftManagerControl swiftManagerControl = new SwiftManagerControl();
    private final BlackListControl blackListControl = new BlackListControl();
    private final DowJonesControl dowJonesControl = new DowJonesControl();
    private final ListExplorerControl listExplorerControl = new ListExplorerControl();
    private final NameScanControl nameScanControl = new NameScanControl();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getPassword()
                   );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    /**
     * Test Case: Dow Jones List Loading (List State Owned Company) - perform a name scan
     * STEPS:
     * 1- Create a new swift template
     * 2- Create a new black list
     * 3- Create a new Dow-Jones List (List State Owned Company)
     * 4- Enable the Dow-Jones list
     * 5- Create a Dow-Jones files and run it on the server to load referential
     * 6- run it again to load names from list name = 'State_Owned_Company'
     * 7- Select countries from UI
     * 8- Create a new list set
     * 9- Perform a name scan for 'Agarwal, Sanjay'
     */
    @Test()
    @Owner("Sarah Abdellatif")
    @Tag("Regression")
    @Feature("Dow Jones")
    public void DowJones_TC006() {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Dow Jones List Loading (List State Owned Company) - " +
                    " perform a name scan"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Dow Jones List Loading (List State Owned Company) - " +
                    " perform a name scan"));
            String listName = "State_Owned_Company";

            EnList list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());

            Assert.assertTrue(swiftManagerControl.CreateSwiftTemplate(driver, list.getListSet().getSwiftTemplate(), list.getZoneName())
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while create new swift template.");

            Assert.assertTrue(blackListControl.create_Black_List(driver, list)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new black list.");


            Assert.assertTrue(dowJonesControl.create_dj_list(driver
                    , list
                    , "State Owned Company"
                    , false), GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new Dow-Jones list.");

            Assert.assertEquals(dowJonesControl.enable_dj_list(driver), list.getExpectedMessage(), GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new Dow-Jones list.");

            String userName = Common.OPERATOR.FULL_RIGHT_1.getOperator().getLoginName();
            String password = Common.OPERATOR.FULL_RIGHT_1.getOperator().getPassword();
            String zoneID = adminServicesDelegate.getZoneId(list.getZoneName());
            Assert.assertTrue(dowJonesControl.create_dj_files(list, true
                            , false, userName, password, zoneID, listName, false)
                    .contains("INFO  - Bye bye!"), GeneralConstants.POM_EXCEPTION_ERR_MSG);

            dowJonesControl.add_sanction_list(driver, list);

            Assert.assertTrue(dowJonesControl.load_dj_list_load_referential_only(list, false
                            , false, listName, false).contains("Process successfully finished!")
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            commonTestMethods.createListSetOnly(driver, list, Common.PROFILE.FULL_RIGHT_001.getProfile().getName());

            Assert.assertNotEquals(listExplorerControl.get_Rows_Count(driver, list),
                    "0 row(s)",
                    GeneralConstants.POM_EXCEPTION_ERR_MSG + " while getting rows count.");

            Assert.assertEquals(nameScanControl.scan_Name(driver
                            , "Agarwal, Sanjay"
                            , Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName()
                            , "95"
                            , true
                            , true
                            , true
                            , false)
                    , "REPNEW"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
