package eastnets.screening.regression.iso20022configurations;

import core.BaseTest;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import core.constants.screening.ISOConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.ISO20022Control;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class ISO20022_TC001 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final ISO20022Control iso20022Control = new ISO20022Control();
    private final core.util.Property property = new core.util.Property();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_7.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_7.getOperator().getPassword()
            );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test Case: Invalidate Import new schema with invalid file from 'ISO2022 Schema Configuration' module tab.
     * STEPS:
     * 1- Navigate to 'ISO2022 Schema Configuration' module tab.
     * 2- Import new schema with invalid file.
     * 3- Verify that the validation message is displayed.
     */

    @Test()
    @Owner("Jérémie Reuter")
    @Tag("Regression")
    @Tag("ISO20022")
    public void iso20022_TC001() {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Invalidate Import new schema with invalid file from 'ISO2022 Schema Configuration' module tab."));
            lifecycle.updateTestCase(testResult -> testResult.setName("Invalidate Import new schema with invalid file from 'ISO2022 Schema Configuration' module tab."));
            String fileName = ISOConstants.ISO_WITHOUT_EXTENSION_FILENAME;
            String filePath = System.getProperty("user.dir")
                    + property.fromFile(GeneralConstants.GENERAL_CONFIG_FILE_NAME).getProperty(GeneralConstants.DEFAULT_UPLOAD_PATH)
                    + fileName;
            Allure.step("Schema File Path = " + filePath);
            iso20022Control.navigateToISO20022SchemaManager(driver);
            Assert.assertEquals(iso20022Control.importSchema(driver, filePath)
                    , "Invalid File Type."
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while delete old schema");

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e, new Object() {
            }.getClass().getName(), new Object() {
            }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e, new Object() {
            }.getClass().getName(), new Object() {
            }.getClass().getEnclosingMethod().getName());
        }
    }


}
