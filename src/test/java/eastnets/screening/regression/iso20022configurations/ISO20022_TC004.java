package eastnets.screening.regression.iso20022configurations;

import com.fasterxml.jackson.databind.ObjectMapper;
import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.ISOTestMethods;
import core.constants.screening.GeneralConstants;
import core.constants.screening.ISOConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.ISO20022Control;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.ISO20022FormatConfiguration;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import java.io.File;
import java.io.IOException;

public class ISO20022_TC004 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ISOTestMethods isoTestMethods = new ISOTestMethods();
    private final ISO20022Control iso20022Control = new ISO20022Control();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();
    private final core.util.Property property = new core.util.Property();
    private final core.util.Wait wait = new core.util.Wait();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_7.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_7.getOperator().getPassword()
            );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test Case: Verify that the user can import custom ISO20022 XSD
     */

    @Test(dataProvider = "data-provider")
    @Owner("Jérémie Reuter")
    @Tag("Regression")
    @Tag("ISO20022")
    public void iso20022_TC0012(ISO20022FormatConfiguration formatConfiguration) {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that the user can import custom ISO20022 XSD"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that the user can export import ISO20022 XSD"));
            EnList enList = commonTestMethods.getEnListData();
            formatConfiguration.setZone(Common.ZONE.COMMON_TEST_ZONE_007.getZone().getDisplayName());
            formatConfiguration.setGroupName("GroupName" + randomizer.getInt());
            iso20022Control.navigateToISO20022FormatManager(driver);
            iso20022Control.createGroup(driver, formatConfiguration);
            enList.getListSet().setIsoGroup(formatConfiguration.getGroupName());
            enList.getListSet().setSwiftTemplate(null);
            commonTestMethods.createListSet(driver, enList, Common.PROFILE.FULL_RIGHT_007.getProfile().getName());
            iso20022Control.navigateToISO20022SchemaManager(driver);

            formatConfiguration.setExpectedResults(String.format(formatConfiguration.getExpectedResults(),
                    formatConfiguration.getIso20022SchemaConfiguration().getSchemaName(),
                    formatConfiguration.getIso20022SchemaConfiguration().getSchemaVersion()));
            iso20022Control.deleteSchema(driver, formatConfiguration.getIso20022SchemaConfiguration().getSchemaName());
            String fileName = ISOConstants.MERGE_ISO_FORMAT_CONFIGURATIONS_FILE_PATH1;
            iso20022Control.importSchema(driver, fileName);
            formatConfiguration.setExpectedResults("Exported successfully");
            iso20022Control.navigateToISO20022FormatManager(driver);
            isoTestMethods.addISOMessage(driver, formatConfiguration, formatConfiguration.getIso20022SchemaConfiguration().getHeaderSwift());
            fileName = ISOConstants.MERGE_ISO_FORMAT_CONFIGURATIONS_FILE_PATH2;
            isoTestMethods.mergeISOSchema(driver, formatConfiguration, fileName);
            String prefix = "ExportISO20022Format_";
            String suffix = ".xml";
            clearDownloadedFiles();
            isoTestMethods.exportISOSchema(driver, formatConfiguration, prefix, dockerServerIp);
            Assert.assertEquals(iso20022Control.deleteSchema(driver, formatConfiguration.getIso20022SchemaConfiguration().getSchemaName())
                    ,"Schema version [pacs.009.001.08] successfully deleted."
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while deleting ISO20022 schema.");
            wait.time(wait.ONE_SECOND*5);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e, new Object() {
            }.getClass().getName(), new Object() {
            }.getClass().getEnclosingMethod().getName());
        }
    }


    @DataProvider(name = "data-provider")
    public Object[][] prepareMergeISO20022FormatTestData() {
        Object[][] isoConfDP = new Object[1][1];
        ObjectMapper mapper = new ObjectMapper();
        ISO20022FormatConfiguration iso20022FormatConfiguration = new ISO20022FormatConfiguration();
        try {
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("mergeISOFormat");
            iso20022FormatConfiguration = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), ISO20022FormatConfiguration.class);
            int random = randomizer.getInt();
            iso20022FormatConfiguration.setGroupName(String.format(iso20022FormatConfiguration.getGroupName(), random));
            iso20022FormatConfiguration.setExpectedResults(String.format(iso20022FormatConfiguration.getExpectedResults(),
                    iso20022FormatConfiguration.getIso20022SchemaConfiguration().getSchemaName(),
                    iso20022FormatConfiguration.getIso20022SchemaConfiguration().getSchemaVersion()));
            iso20022FormatConfiguration.setZone(property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("zone.display.name"));

            isoConfDP[0][0] = iso20022FormatConfiguration;
        } catch (IOException e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
        Allure.step("Test Data = " + iso20022FormatConfiguration.toString());
        return isoConfDP;
    }


}
