package eastnets.screening.regression.iso20022configurations;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paypal.selion.platform.dataprovider.DataProviderFactory;
import com.paypal.selion.platform.dataprovider.DataResource;
import com.paypal.selion.platform.dataprovider.SeLionDataProvider;
import com.paypal.selion.platform.dataprovider.impl.InputStreamResource;
import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.ISOTestMethods;
import core.constants.screening.GeneralConstants;
import core.constants.screening.ISOConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.ISO20022Control;
import eastnets.screening.control.listManager.BlackListControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FileScan;
import eastnets.screening.entity.ISO20022FormatConfiguration;
import eastnets.screening.entity.ISO20022FormatDetailsConfiguration;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

public class ISO20022_TC002 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ISOTestMethods isoTestMethods = new ISOTestMethods();
    private final ISO20022Control iso20022Control = new ISO20022Control();
    private final BlackListControl blackListControl = new BlackListControl();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();
    private final core.util.Property property = new core.util.Property();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_7.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_7.getOperator().getPassword()
            );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test Case: Scan ISO message without xml tag having body fields only while Header is added to config and no Wrapper defined.
     * STEPS:
     * 1-Create new Iso20022 format.
     * 2- Create new list set with created Iso20022 format.
     * 3- Create "OmarBajaal" as entry.
     */

    @Test(dataProvider = "data-provider")
    @Owner("Jérémie Reuter")
    @Tag("Regression")
    @Tag("ISO20022")
    @Issue("87504")
    public void iso20022_TC0012(ISO20022FormatDetailsConfiguration ISO20022FormatConfigurationDetails1) {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Tfs 87504: Scan ISO message without xml tag having body fields only while Header is added to config and no Wrapper defined."));
            lifecycle.updateTestCase(testResult -> testResult.setName("Tfs 87504: Scan ISO message without xml tag having body fields only while Header is added to config and no Wrapper defined."));
            EnList enList = commonTestMethods.getEnListData();

            ISO20022FormatConfiguration ISOFormat2 = ISO20022FormatConfigurationDetails1.getIso20022FormatConfiguration();

            Assert.assertTrue(blackListControl.create_Black_List(driver, enList)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new black list.");
            ISOFormat2.setZone(Common.ZONE.COMMON_TEST_ZONE_007.getZone().getDisplayName());
            ISOFormat2.setGroupName("GroupName" + randomizer.getInt());
            iso20022Control.navigateToISO20022FormatManager(driver);
            iso20022Control.createGroup(driver, ISOFormat2);

            enList.getListSet().setIsoGroup(ISOFormat2.getGroupName());
            enList.getListSet().setSwiftTemplate(null);
            commonTestMethods.createListSet(driver, enList, Common.PROFILE.FULL_RIGHT_007.getProfile().getName());

            enList.getEntry().get(0).setName("OmarBajaal");
            enList.getEntry().get(0).setFirstName(null);
            enList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_007.getZone().getDisplayName());

            commonTestMethods.createNewEntryWithTypeAndNames(driver, enList);

            ISO20022FormatConfiguration ISO20022FormatConfiguration1 = (ISO20022FormatConfiguration) prepareTestData()[0][0];

            isoTestMethods.importISOSchema(driver, ISO20022FormatConfiguration1);


            ISOFormat2.setExpectedResults(String.format(ISOFormat2.getExpectedResults(),
                    ISOFormat2.getIso20022SchemaConfiguration().getSchemaName(),
                    ISOFormat2.getIso20022SchemaConfiguration().getSchemaVersion()));

            isoTestMethods.importISOSchema(driver, ISOFormat2);
            ISO20022FormatConfigurationDetails1.setXpath(ISO20022FormatConfigurationDetails1.getBodyField());
            isoTestMethods.addISOMessage(driver, ISO20022FormatConfiguration1, ISOFormat2.getIso20022SchemaConfiguration().getHeaderSwift());
            ISO20022FormatConfigurationDetails1.setIso20022FormatConfiguration(ISO20022FormatConfiguration1);
            isoTestMethods.addBodyField(driver, ISO20022FormatConfigurationDetails1);
            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("blockDetection");
            FileScan fileScan = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), FileScan.class);
            fileScan.setFilePath(ISOConstants.ISO_NOWRAPPER_NOXML_FILENAME);
            fileScan.setFormat("ISO20022");
            commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }

    }


    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e, new Object() {
            }.getClass().getName(), new Object() {
            }.getClass().getEnclosingMethod().getName());
        }
    }


    public Object[][] prepareTestData() {
        Object[][] isoConfDP = new Object[1][1];
        ObjectMapper mapper = new ObjectMapper();
        ISO20022FormatConfiguration iso20022FormatConfiguration = new ISO20022FormatConfiguration();
        try {
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("isoConfiguration");
            iso20022FormatConfiguration = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), ISO20022FormatConfiguration.class);
            int random = randomizer.getInt();
            iso20022FormatConfiguration.setGroupName(String.format(iso20022FormatConfiguration.getGroupName(), random));
            iso20022FormatConfiguration.setExpectedResults(String.format(iso20022FormatConfiguration.getExpectedResults(),
                    iso20022FormatConfiguration.getIso20022SchemaConfiguration().getSchemaName(),
                    iso20022FormatConfiguration.getIso20022SchemaConfiguration().getSchemaVersion()));
            iso20022FormatConfiguration.setZone(property.fromFile(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME).getProperty("zone.display.name"));


            isoConfDP[0][0] = iso20022FormatConfiguration;
        } catch (IOException e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
        Allure.step("Test Data = " + iso20022FormatConfiguration.toString());
        return isoConfDP;
    }

    @DataProvider(name = "data-provider")
    public Object[][] prepareAddBodyFieldISOTestData() throws IOException {
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("scanISOMessageWithoutXML");
        System.out.println(csvDataFilePath);
        DataResource resource =
                new InputStreamResource(new FileInputStream(System.getProperty("user.dir") + csvDataFilePath),
                        ISO20022FormatDetailsConfiguration.class, "json");
        SeLionDataProvider dataProvider =
                DataProviderFactory.getDataProvider(resource);
        return dataProvider.getAllData();
    }


}
