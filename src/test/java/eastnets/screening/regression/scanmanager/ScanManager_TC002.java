package eastnets.screening.regression.scanmanager;

import com.fasterxml.jackson.databind.ObjectMapper;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.screening.control.dbManager.DBFlowConfigControl;
import eastnets.screening.control.scanManger.DBScanControl;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.DBConfigurations;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.io.File;

public class ScanManager_TC002 extends ScanManager_PreCondition {

    // Additional instance variables for this test class
    private final DBFlowConfigControl dbFlowConfigControl = new DBFlowConfigControl();
    private final DBScanControl dbScanControl = new DBScanControl();
    private final ResultManagerControl resultManagerControl = new ResultManagerControl();

    private RemoteWebDriver driver;

    @BeforeMethod()
    public void login() {
        try {
            driver = getDriver();
            driver.navigate().refresh();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_4.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_4.getOperator().getPassword()
                   );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    /**
     * Test Case: Verify that user is able to add a Good Guy for a detection created from DB Scan
     * STEPS:
     * 1- Create a new DB Scan
     * 2- Run the DB Scan
     * 3- Check the result of the scan
     * 4- Add a Good Guy
     */


    @Test(groups = {"scanFile"}, enabled = false)
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("Scan Manager")
    public void scanManager_TC002() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that user is able to add a Good Guy for a detection created from DB Scan"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that user is able to add a Good Guy for a detection created from DB Scan"));

            int random = randomizer.getInt();
            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("dbScan");
            DBConfigurations db = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), DBConfigurations.class);
            db.setName(String.format(db.getName(), random));
            db.setZoneName(Common.ZONE.COMMON_TEST_ZONE_004.getZone().getDisplayName());

            csvHandler.updateCSV(System.getProperty("user.dir") + GeneralConstants.DB_SCAN_FILE_PATH,
                    nameForDBScan.split(",")[0], 1, 0);
            csvHandler.updateCSV(System.getProperty("user.dir") + GeneralConstants.DB_SCAN_FILE_PATH,
                    nameForDBScan.split(",")[1], 1, 1);

            screeningServicesDelegate.CreateDBScanTable();
            screeningServicesDelegate.insertDataInDBScanTable(GeneralConstants.DB_SCAN_FILE_PATH);
            db.setHost(screeningGeneralConfigsProps.getProperty(GeneralConstants.REMOTE_SERVER_IP));
            db.setPort(screeningGeneralConfigsProps.getProperty(GeneralConstants.DB_PORT));
            db.setUserName(screeningGeneralConfigsProps.getProperty(GeneralConstants.DB_NAME));
            db.setDbType(screeningGeneralConfigsProps.getProperty(GeneralConstants.DB_TYPE));

            Assert.assertEquals(dbFlowConfigControl.create_new_db_configurations(driver, db)
                    , GeneralConstants.SUCCESS
                    , "Error occurs while create new DB config");
            dbScanControl.db_Scan(driver, db);
            Assert.assertEquals(resultManagerControl.check_scanned_file_result(driver), "SUCCEEDED", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            String detectionID = resultManagerControl.get_detection_id(driver);
            Assert.assertNotNull(detectionID, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Assert.assertEquals(resultManagerControl.add_good_guy(driver, null), "Good guy successfully created!", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void afterMethod() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


}
