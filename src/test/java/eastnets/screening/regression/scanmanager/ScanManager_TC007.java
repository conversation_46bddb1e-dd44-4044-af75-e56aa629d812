package eastnets.screening.regression.scanmanager;

import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.screening.control.scanManger.NameScanControl;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class ScanManager_TC007 extends ScanManager_PreCondition {

    // Additional instance variables for this test class
    private final NameScanControl nameScanControl = new NameScanControl();
    private RemoteWebDriver driver;

    @BeforeMethod()
    public void login() {
        try {
            driver = getDriver();
            driver.navigate().refresh();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_4.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_4.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    /**
     * Test Case: Verify that user is able to add a Good Guy for a detection by clicking accept as shared button for shared black list
     * STEPS:
     * 1- Scan a name = ياسر عمر
     * 2- Choose the scanned name from result table
     * 3- Click on accept as shared button
     * 4- Verify that the message "Good guy successfully created!" is displayed
     */


    @Test(groups = {"scanFile"})
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("Scan Manager")
    public void scanManager_TC007() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that user is able to add a Good Guy for a detection by clicking accept as shared button for shared black list"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that user is able to add a Good Guy for a detection by clicking accept as shared button for shared black list"));


            Assert.assertNull(blackListControl.share_black_list(driver, blackListName)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while share black list.");

            Assert.assertEquals(nameScanControl.scan_Name(driver
                            , arabicName
                            , Common.ZONE.COMMON_TEST_ZONE_004.getZone().getDisplayName()
                            , null
                            , true
                            , true
                            , true
                            , false)
                    , "REPNEW"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            nameScanManager.chooseScannedNameFromResultTable(driver, arabicName);

            Assert.assertEquals(nameScanControl.accept_gg_as_shared(driver)
                    , "Good guy successfully created!"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


        } catch (Exception e) {
            driver.navigate().refresh();
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void afterMethod() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


}
