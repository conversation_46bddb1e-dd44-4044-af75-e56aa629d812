package eastnets.screening.regression.scanmanager;

import com.paypal.selion.platform.dataprovider.DataProviderFactory;
import com.paypal.selion.platform.dataprovider.DataResource;
import com.paypal.selion.platform.dataprovider.SeLionDataProvider;
import com.paypal.selion.platform.dataprovider.impl.InputStreamResource;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.FileScan;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import java.io.FileInputStream;
import java.io.IOException;

public class ScanManager_TC008 extends ScanManager_PreCondition {

    // Additional instance variables for this test class
    private final ResultManagerControl resultManagerControl = new ResultManagerControl();
    private RemoteWebDriver driver;

    @BeforeMethod()
    public void login() {
        try {
            driver = getDriver();
            driver.navigate().refresh();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_4.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_4.getOperator().getPassword()
            );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    /**
     * Test Case: Verify that user is able to add an entity as Good Guy from the results tab for a scanned file with SWIFT RJE Records type.
     * STEPS:
     * 1- Scan a file with SWIFT RJE Records type
     * "filePath" : "/src/test/resources/testDataFiles/fileScanTD/RJE_Swift.txt",
     * "format" : "SWIFT RJE Records",
     * "detectVessels" : true,
     * "detectCountries": true,
     * "createAlertsAutomatically": false,
     * 2- Add an entity as Good Guy from the results tab
     * 3- Verify that the entity is added successfully
     * Test Case: Verify that user is able to add an entity as Good Guy from the results tab for a scanned file with Custom Format type.
     * STEPS:
     * 1- Scan a file with Custom Format type
     * "filePath" : "/src/test/resources/testDataFiles/fileScanTD/ScanFile.txt",
     * "format" : "Custom Format File",
     * "detectVessels" : true,
     * "detectCountries": true,
     * "createAlertsAutomatically": false,
     * 2- Add an entity as Good Guy from the results tab
     * 3- Verify that the entity is added successfully
     */


    @Test(dataProvider = "scanAndAddGGTestData", groups = {"scanFile"})
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("Scan Manager")
    public void scanManager_TC008(FileScan fileScan) {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription(fileScan.getTestCaseTitle()));
            lifecycle.updateTestCase(testResult -> testResult.setName(fileScan.getTestCaseTitle()));

            driver.navigate().refresh();

            modifyScannedFile(fileScan.getFormat(), fileScan.getFilePath(), fileScan.getEntryName());

            String formatName = createFormat(fileScan.getFormat());
            if (formatName != null)
                fileScan.setFormat(formatName);

            fileScan.setFilePath(System.getProperty("user.dir") + fileScan.getFilePath());
            commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            Assert.assertEquals(resultManagerControl.add_good_guy(driver, null), fileScan.getExpectedResults(), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void afterMethod() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @DataProvider(name = "scanAndAddGGTestData")
    public Object[][] scanAndAddGGTestData() throws IOException {
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("scanFileAndAddGoodGuy");
        DataResource resource =
                new InputStreamResource(new FileInputStream(System.getProperty("user.dir") + csvDataFilePath),
                        FileScan.class, "json");
        SeLionDataProvider dataProvider =
                DataProviderFactory.getDataProvider(resource);
        return dataProvider.getAllData();

    }

}
