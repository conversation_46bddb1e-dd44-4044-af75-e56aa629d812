package eastnets.screening.regression.scanmanager;

import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.FileScan;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class ScanManager_TC005 extends ScanManager_PreCondition {

    // Additional instance variables for this test class
    private final ResultManagerControl resultManagerControl = new ResultManagerControl();
    private RemoteWebDriver driver;

    @BeforeMethod()
    public void login() {
        try {
            driver = getDriver();
            driver.navigate().refresh();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_4.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_4.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    /**
     * Test Case: Verify that user is able to Print an Alert created from 'File Scan' from Result Tab
     * STEPS:
     * 1- Scan a file = UPLOAD_FILE_PATH+"\\Entities.txt"
     * 2- Navigate to Result Tab
     * 3- Click on detection data
     * 4- Click export button and choose type = pdf
     * 5- Verify that the file is downloaded successfully
     */


    @Test(groups = {"scanFile"})
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("Scan Manager")
    public void scanManager_TC005() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that user is able to Print an Alert created from 'File Scan' from Result Tab"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that user is able to Print an Alert created from 'File Scan' from Result Tab"));

            driver.navigate().refresh();
            FileScan fileScan = testData()[1];

            fileScan.setFilePath(System.getProperty("user.dir") + GeneralConstants.UPLOAD_FILE_PATH + "\\Entities.txt");
            commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            Assert.assertTrue(textFilesHandler.deleteFileOnRemoteNode(driver, dockerServerIp));
            Assert.assertTrue(resultManagerControl.export_alert(driver, "all", "PDF", "rptDetectionDetailsReport.pdf", dockerServerIp), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void afterMethod() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


}
