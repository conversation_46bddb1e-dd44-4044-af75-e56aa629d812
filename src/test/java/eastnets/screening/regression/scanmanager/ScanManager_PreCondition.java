package eastnets.screening.regression.scanmanager;

import com.fasterxml.jackson.databind.ObjectMapper;
import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import core.util.CSVHandler;
import core.util.Property;
import core.util.Randomizer;
import core.util.TextFilesHandler;
import eastnets.admin.entity.Common;
import eastnets.admin.entity.Operator;
import eastnets.admin.entity.Profile;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.control.FormatManagerControl;
import eastnets.screening.control.listManager.BlackListControl;
import eastnets.screening.control.scanManger.FileScanControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FileScan;
import eastnets.screening.entity.Format;
import eastnets.screening.gui.scanManager.scanName.NameScanManager;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeGroups;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;

public class ScanManager_PreCondition extends BaseTest {

    // Instance variables for page objects and controls
    final LoginPage loginPage = new LoginPage();
    final CommonAction commonAction = new CommonAction();
    final CommonTestMethods commonTestMethods = new CommonTestMethods();
    final BlackListControl blackListControl = new BlackListControl();
    final FormatManagerControl formatManagerControl = new FormatManagerControl();
    final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();
    final FileScanControl fileScanControl = new FileScanControl();
    final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    final Property property = new Property();
    final Randomizer randomizer = new Randomizer();
    final NameScanManager nameScanManager = new NameScanManager();
    final CSVHandler csvHandler = new CSVHandler();
    final TextFilesHandler textFilesHandler = new TextFilesHandler();

    private RemoteWebDriver driver;

    String name = "EntryName, EntryName";
    String nameForDBScan = "DBScanName, DBScanName";
    String arabicName = "ياسر عمر";
    String arabicName2 = "محمد احمد";
    String deleteEntityName = "Bishr, Al-Sabban";
    static String blackListName = "";
    Operator operator = Common.OPERATOR.FULL_RIGHT_4.getOperator();

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
            System.out.println("Running testOne on thread: " + Thread.currentThread().getId());
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeGroups(value = "scanFile", alwaysRun = true)
    public void importList() {
        try {
            lifecycle = Allure.getLifecycle();
            loginPage.login(driver
                    , operator.getLoginName()
                    , operator.getPassword()
                   );

            ArrayList<EnList> enLists = new ArrayList<>();
            Profile profile = Common.PROFILE.FULL_RIGHT_004.getProfile();
            EnList enList = commonTestMethods.getEnListData();
            enList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_004.getZone().getDisplayName());
            enLists.add(enList);
            enList = new EnList();
            enList = commonTestMethods.getEnListData();
            enList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_004.getZone().getDisplayName());
            enList.setName(GeneralConstants.IRAN_LIST_NAME);
            enLists.add(enList);
            enList = new EnList();
            enList = commonTestMethods.getEnListData();
            enList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_004.getZone().getDisplayName());
            enList.getEntry().get(0).setName(arabicName.split(",")[0].trim());
            enList.getEntry().get(0).setFirstName(null);
            enLists.add(enList);

            screeningServicesDelegate.deleteLinkBtwBlackListAndListSet(enList.getZoneName(), GeneralConstants.IRAN_LIST_NAME);
            screeningServicesDelegate.deleteLinkBtwBlackListAndGoodGuy(enList.getZoneName(), GeneralConstants.IRAN_LIST_NAME);
            screeningServicesDelegate.deleteBlackList(GeneralConstants.IRAN_LIST_NAME);


            String importStatus = blackListControl.import_list(driver, enList.getZoneName(), GeneralConstants.IRAN_LIST_FILE_PATH, true, false);
            Assert.assertEquals(importStatus, "SUCCEEDED", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


            blackListName = enLists.get(0).getName();
            Assert.assertTrue(blackListControl.create_Black_List(driver, enLists.get(0))
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new black list.");


            Allure.step("Start Link Imported List to List Set.");
            commonTestMethods.linkImportedListToNewListSet(driver, enLists, profile.getName());

            Format formatIndividual = eastnets.screening.entity.Common.FORMAT.IMPORT_ENTRY_INDIVIDUAL_FORMAT_2.get();
            String filePathIndividual = System.getProperty("user.dir") + GeneralConstants.IMPORT_ENTRY_INDIVIDUAL_LIST_FILE_PATH;
            enList = enLists.get(0);
            commonTestMethods.importEntries(driver, enList, Common.ZONE.COMMON_TEST_ZONE_004.getZone(), filePathIndividual, formatIndividual);
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Step("Create Format")
    public String createFormat(String fileFormatType) throws Exception {

        Format format = eastnets.screening.entity.Common.FORMAT.SCAN_FORMAT.get();
        if (fileFormatType.contains("Format")) {
            format.setName("Format-" + randomizer.getInt());
            format.setZone(Common.ZONE.COMMON_TEST_ZONE_004.getZone().getDisplayName());
            Assert.assertNull(formatManagerControl.AddNewFormat(driver, format), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG + " while creating new format.");
            Assert.assertTrue(formatManagerControl.checkFormatExist(driver, format), GeneralConstants.POM_EXCEPTION_ERR_MSG);
        }

        return format.getName();
    }


    @Step("Take action")
    public void takeAction(Boolean flag, String detectionID, String expectedResults) throws Exception {
        String status;
        if (flag)
            status = "REPNEW";
        else
            status = "REP";

        Assert.assertEquals(detectionManagerControl.search_by_id(driver, detectionID), status, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
        Assert.assertEquals(detectionManagerControl.release_detection_4_eyes(driver, detectionID, "Approved and Release", ""), String.format(expectedResults, detectionID), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

    }

    public FileScan[] testData() throws IOException {
        ObjectMapper mapper = new ObjectMapper();
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("scanFile");
        FileScan[] fileScan = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), FileScan[].class);
        return fileScan;

    }

    @Step("Modify scanned file")
    public void modifyScannedFile(String fileFormatType, String filePath, String entryName) throws Exception {

        if (fileFormatType.contains("RJE")) {
            String rjeSampleFile = System.getProperty("user.dir") + property.fromFile(GeneralConstants.SCREENING_TEST_DATA_CONFIG_FILE_PATH).getProperty("RJESwiftSampleFile");
            fileScanControl.edit_rje_file(rjeSampleFile, System.getProperty("user.dir") + filePath, entryName);

        } else {
            String genericSampleFile = System.getProperty("user.dir") + property.fromFile(GeneralConstants.SCREENING_TEST_DATA_CONFIG_FILE_PATH).getProperty("GenericSampleFile");
            fileScanControl.edit_rje_file(genericSampleFile, System.getProperty("user.dir") + filePath, entryName);
        }


    }
}
