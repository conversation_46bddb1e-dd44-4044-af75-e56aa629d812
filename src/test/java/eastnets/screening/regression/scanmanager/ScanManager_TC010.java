package eastnets.screening.regression.scanmanager;

import com.fasterxml.jackson.databind.ObjectMapper;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.screening.control.dbManager.DBFlowConfigControl;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.control.scanManger.DBScanControl;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.DBConfigurations;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.io.File;

public class ScanManager_TC010 extends ScanManager_PreCondition {

    // Additional instance variables for this test class
    private final DBFlowConfigControl dbFlowConfigControl = new DBFlowConfigControl();
    private final DBScanControl dbScanControl = new DBScanControl();
    private final ResultManagerControl resultManagerControl = new ResultManagerControl();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();
    private RemoteWebDriver driver;

    @BeforeMethod()
    public void login() {
        try {
            driver = getDriver();
            driver.navigate().refresh();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_4.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_4.getOperator().getPassword()
                   );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    /**
     * Test Case: Verify that user is able to Assign a detection created from DB Scan to another User
     * STEPS:
     * 1- Create a new DB flow
     * 2- Perform Db scan and check the result = SUCCEEDED
     * 3- Create an alert for the detection
     * 4- Assign the detection to another user
     */


    @Test(groups = {"scanFile"}, enabled = false)
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("Scan Manager")
    public void scanManager_TC010() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that user is able to Assign a detection created from DB Scan to another User"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that user is able to Assign a detection created from DB Scan to another User"));

            int random = randomizer.getInt();
            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("dbScan");
            DBConfigurations db = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), DBConfigurations.class);
            db.setName(String.format(db.getName(), random));
            db.setZoneName(Common.ZONE.COMMON_TEST_ZONE_004.getZone().getDisplayName());
            String ip = screeningGeneralConfigsProps.getProperty(GeneralConstants.REMOTE_SERVER_IP);
            String port = screeningGeneralConfigsProps.getProperty(GeneralConstants.DB_PORT);

            db.setHost(ip);
            db.setPort(port);


            csvHandler.updateCSV(System.getProperty("user.dir") + GeneralConstants.DB_SCAN_FILE_PATH,
                    name.split(",")[0], 1, 0);
            csvHandler.updateCSV(System.getProperty("user.dir") + GeneralConstants.DB_SCAN_FILE_PATH,
                    name.split(",")[1], 1, 1);

            screeningServicesDelegate.CreateDBScanTable();
            screeningServicesDelegate.insertDataInDBScanTable(GeneralConstants.DB_SCAN_FILE_PATH);


            dbFlowConfigControl.create_new_db_configurations(driver, db);
            dbScanControl.db_Scan(driver, db);

            Assert.assertEquals(resultManagerControl.check_scanned_file_result(driver), "SUCCEEDED", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            String detectionID = resultManagerControl.get_detection_id(driver);
            Assert.assertNotNull(detectionID, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            String operator = Common.OPERATOR.FULL_RIGHT_10.getOperator().getLoginName();


            System.out.println(operator);
            Assert.assertEquals(detectionManagerControl.create_alert(driver, detectionID, "Create alert")
                    , "1 violation(s) successfully modified!"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.assignDetection(driver, detectionID, operator, "Assign detection to another operator")
                    , "1 detection(s) successfully modified!"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void afterMethod() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


}
