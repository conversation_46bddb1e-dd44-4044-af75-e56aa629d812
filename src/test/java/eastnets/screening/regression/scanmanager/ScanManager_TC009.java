package eastnets.screening.regression.scanmanager;

import com.paypal.selion.platform.dataprovider.DataProviderFactory;
import com.paypal.selion.platform.dataprovider.DataResource;
import com.paypal.selion.platform.dataprovider.SeLionDataProvider;
import com.paypal.selion.platform.dataprovider.impl.InputStreamResource;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.FileScan;
import eastnets.screening.entity.Report;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import java.io.FileInputStream;
import java.io.IOException;

public class ScanManager_TC009 extends ScanManager_PreCondition {

    // Additional instance variables for this test class
    private final ResultManagerControl resultManagerControl = new ResultManagerControl();
    private RemoteWebDriver driver;

    @BeforeMethod()
    public void login() {
        try {
            driver = getDriver();
            driver.navigate().refresh();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_4.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_4.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    /**
     * Test Case: Verify that user is able to Print an alert report in different formats PDF, Excel, RTF, word
     * STEPS:
     * 1- Scan a file = UPLOAD_FILE_PATH+"\\Entities.txt" and get the detection ID
     * 2- Navigate to Result Manager
     * 3- Export the alert in different formats PDF, Excel, RTF, word
     * 4- Verify that the alert is exported successfully
     */


    @Test(groups = {"scanFile"}, dataProvider = "exportAlertTestData")
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("Scan Manager")
    public void scanManager_TC009(Report report) {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription(report.getTestCaseTitle()));
            lifecycle.updateTestCase(testResult -> testResult.setName(report.getTestCaseTitle()));

            driver.navigate().refresh();
            FileScan fileScan = testData()[1];
            clearDownloadedFiles();

            fileScan.setFilePath(System.getProperty("user.dir") + GeneralConstants.UPLOAD_FILE_PATH + "\\Entities.txt");
            commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            Assert.assertTrue(textFilesHandler.deleteFileOnRemoteNode(driver, dockerServerIp));
            Assert.assertTrue(resultManagerControl.export_alert(driver, "all", report.getReportFormat(), report.getReportName(), dockerServerIp), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void afterMethod() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @DataProvider(name = "exportAlertTestData")
    public Object[][] exportAlertTestData() throws IOException {
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("printAlert");
        DataResource resource =
                new InputStreamResource(new FileInputStream(System.getProperty("user.dir") + csvDataFilePath),
                        Report.class, "json");
        SeLionDataProvider dataProvider =
                DataProviderFactory.getDataProvider(resource);
        return dataProvider.getAllData();

    }

}
