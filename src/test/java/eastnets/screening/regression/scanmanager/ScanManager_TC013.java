package eastnets.screening.regression.scanmanager;

import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.screening.control.listManager.ListExplorerControl;
import eastnets.screening.control.scanManger.NameScanControl;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class ScanManager_TC013 extends ScanManager_PreCondition {

    // Additional instance variables for this test class
    private final NameScanControl nameScanControl = new NameScanControl();
    private final ListExplorerControl listExplorerControl = new ListExplorerControl();
    private RemoteWebDriver driver;

    @BeforeMethod()
    public void login() {
        try {
            driver = getDriver();
            driver.navigate().refresh();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_4.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_4.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    /**
     * Test Case: Verify that Deleting a related black listed entity is not giving violations on the second scan
     * STEPS:
     * 1- Scan a black listed entity and verify scan result = REPNEW
     * 2- Delete the entity
     * 3- Scan the entity again and verify scan result = CLEAN
     */


    @Test(groups = {"scanFile"})
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("Scan Manager")
    public void scanManager_TC013() {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that Deleting a related black listed entity is not giving violations on the second scan"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that Deleting a related black listed entity is not giving violations on the second scan"));

            Assert.assertEquals(nameScanControl.scan_Name(driver
                            , deleteEntityName
                            , Common.ZONE.COMMON_TEST_ZONE_004.getZone().getDisplayName()
                            , null
                            , true
                            , true
                            , true
                            , false)
                    , "REPNEW"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            listExplorerControl.delete_entry(driver, Common.ZONE.COMMON_TEST_ZONE_004.getZone().getDisplayName(), blackListName, deleteEntityName);

            Assert.assertEquals(nameScanControl.scan_Name(driver
                            , deleteEntityName
                            , Common.ZONE.COMMON_TEST_ZONE_004.getZone().getDisplayName()
                            , null
                            , true
                            , false
                            , false
                            , false)
                    , "CLEAN"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void afterMethod() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


}
