package eastnets.screening.regression.scanmanager;

import com.fasterxml.jackson.databind.ObjectMapper;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.screening.control.dbManager.DBFlowConfigControl;
import eastnets.screening.control.scanManger.DBScanControl;
import eastnets.screening.entity.DBConfigurations;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.io.File;

public class ScanManager_TC003 extends ScanManager_PreCondition {

    // Additional instance variables for this test class
    private final DBFlowConfigControl dbFlowConfigControl = new DBFlowConfigControl();
    private final DBScanControl dbScanControl = new DBScanControl();

    private RemoteWebDriver driver;

    @BeforeMethod()
    public void login() {
        try {
            driver = getDriver();
            driver.navigate().refresh();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_4.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_4.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    /**
     * Test Case: Verify that Test Connection button is functioning properly when using the correct password
     * STEPS:
     * 1- Create a new DB configuration
     * 2- Navigate to Scan Manager -> DB Scan
     * 3- Choose DB flow , enter the correct password and click on Test Connection button
     * 4- Verify that the connection is successful
     */


    @Test(groups = {"scanFile"}, enabled = false)
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("Scan Manager")
    public void ScanManager_TC003() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that Test Connection button is functioning properly when using the correct password"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that Test Connection button is functioning properly when using the correct password"));

            int random = randomizer.getInt();
            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("dbScan");
            DBConfigurations db = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), DBConfigurations.class);
            db.setName(String.format(db.getName(), random));
            db.setHost(screeningGeneralConfigsProps.getProperty(GeneralConstants.REMOTE_SERVER_IP));
            db.setPort(screeningGeneralConfigsProps.getProperty(GeneralConstants.DB_PORT));

            db.setZoneName(Common.ZONE.COMMON_TEST_ZONE_004.getZone().getDisplayName());

            dbFlowConfigControl.create_new_db_configurations(driver, db);
            Assert.assertEquals(dbScanControl.test_Connection(driver, db), "Successfully connected to Database", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @AfterMethod(alwaysRun = true)
    public void afterMethod() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


}
