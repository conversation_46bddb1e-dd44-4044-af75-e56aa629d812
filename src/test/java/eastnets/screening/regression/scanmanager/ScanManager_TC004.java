package eastnets.screening.regression.scanmanager;

import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.FileScan;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class ScanManager_TC004 extends ScanManager_PreCondition {

    // Additional instance variables for this test class
    private final ResultManagerControl resultManagerControl = new ResultManagerControl();
    private RemoteWebDriver driver;

    @BeforeMethod()
    public void login() {
        try {
            driver = getDriver();
            driver.navigate().refresh();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_4.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_4.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    /**
     * Test Case: Validate that user is able to Print a detection created from 'File Scan' from Result Tab.
     * STEPS:
     * 1- Scan a file = UPLOAD_FILE_PATH+"\\Entities.txt"
     * 2- Print the detection created from the file scan
     * 3- Verify that the detection is printed successfully
     */


    @Test(groups = {"scanFile"})
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("Scan Manager")
    public void ScanManager_TC004() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Validate that user is able to Print a detection created from 'File Scan' from Result Tab."));
            lifecycle.updateTestCase(testResult -> testResult.setName("Validate that user is able to Print a detection created from 'File Scan' from Result Tab."));

            FileScan fileScan = testData()[1];

            fileScan.setFilePath(System.getProperty("user.dir") + GeneralConstants.UPLOAD_FILE_PATH + "\\Entities.txt");
            commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            clearDownloadedFiles();
            Assert.assertTrue(resultManagerControl.export_detection_details(driver
                    , "ProcessActivity"
                    , dockerServerIp), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void afterMethod() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


}
