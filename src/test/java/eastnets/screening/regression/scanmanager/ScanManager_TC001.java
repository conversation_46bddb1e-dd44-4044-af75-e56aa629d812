package eastnets.screening.regression.scanmanager;

import com.paypal.selion.platform.dataprovider.DataProviderFactory;
import com.paypal.selion.platform.dataprovider.DataResource;
import com.paypal.selion.platform.dataprovider.SeLionDataProvider;
import com.paypal.selion.platform.dataprovider.impl.InputStreamResource;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.screening.entity.FileScan;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import java.io.FileInputStream;
import java.io.IOException;

public class ScanManager_TC001 extends ScanManager_PreCondition {

    private RemoteWebDriver driver;

    @BeforeMethod()
    public void login() {
        try {
            driver = getDriver();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_4.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_4.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    /**
     * Test covers the below cases:
     * 1-Verify that user is able to scan a swift rje record without creating alerts automatically option checked and take decision
     * Expected Results: Detection(s) with id(s) ' %s ,' has no alerts created. Status or assignee can be changed only for detections with alerts!
     * 2-Verify that user is able to scan a generic text format file without creating alerts automatically option checked and take decision .
     * Expected Results: Detection(s) with id(s) ' %s ,' has no alerts created. Status or assignee can be changed only for detections with alerts!
     * 3- Verify that user is able to scan a custom format file without creating alerts option checked and take decision.
     * Expected Results: Detection(s) with id(s) ' %s ,' has no alerts created. Status or assignee can be changed only for detections with alerts!"
     * 4-Verify that user is able to scan a custom format file with creating alerts option checked and take decision.
     * Expected Results: 1 detection(s) successfully modified!
     */


    @Test(dataProvider = "scanFileTestData", groups = {"scanFile"})
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("Scan Manager")
    public void scanManager_TC001(FileScan fileScan) {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription(fileScan.getTestCaseTitle()));
            lifecycle.updateTestCase(testResult -> testResult.setName(fileScan.getTestCaseTitle()));

            String formatName = createFormat(fileScan.getFormat());
            if (formatName != null)
                fileScan.setFormat(formatName);

            fileScan.setFilePath(System.getProperty("user.dir") + GeneralConstants.UPLOAD_FILE_PATH + fileScan.getFilePath());
            String detectionID = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            takeAction(fileScan.getCreateAlertsAutomatically(), detectionID, fileScan.getExpectedResults());

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod()
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @DataProvider(name = "scanFileTestData")
    public Object[][] scanFileTestData() throws IOException {
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("scanFile");
        DataResource resource =
                new InputStreamResource(new FileInputStream(System.getProperty("user.dir") + csvDataFilePath),
                        FileScan.class, "json");
        SeLionDataProvider dataProvider =
                DataProviderFactory.getDataProvider(resource);
        return dataProvider.getAllData();

    }
}
