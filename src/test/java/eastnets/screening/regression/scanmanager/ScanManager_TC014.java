package eastnets.screening.regression.scanmanager;

import core.ExceptionHandler;
import eastnets.admin.entity.Common;
import eastnets.screening.control.scanManger.NameScanControl;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class ScanManager_TC014 extends ScanManager_PreCondition {

    // Additional instance variables for this test class
    private final NameScanControl nameScanControl = new NameScanControl();
    private RemoteWebDriver driver;

    @BeforeMethod()
    public void login() {
        try {
            driver = getDriver();
            driver.navigate().refresh();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_4.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_4.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    /**
     * Test Case: Verify Scan with Korean letters
     * STEPS:
     * 1- Perform a name scan for '하루에 세 번, 식사할 때마다 한 알씩 드세요'
     * 2- Verify that the status is 'REP'
     */


    @Test(groups = {"scanFile"})
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("Scan Manager")
    public void scanManager_TC014() {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify Scan with Korean letters"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify Scan with Korean letters"));


            String entityDisplayedName = "하루에 세 번, 식사할 때마다 한 알씩 드세요";
            String status = nameScanControl.scan_Name(driver
                    , entityDisplayedName
                    , Common.ZONE.COMMON_TEST_ZONE_004.getZone().getDisplayName()
                    , "75"
                    , false
                    , false
                    , false
                    , false);
            Assert.assertEquals(status, "REP", "Status for the scan is not correct");


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void afterMethod() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


}
