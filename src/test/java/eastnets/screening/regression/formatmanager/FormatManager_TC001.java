package eastnets.screening.regression.formatmanager;

import com.paypal.selion.platform.dataprovider.DataProviderFactory;
import com.paypal.selion.platform.dataprovider.DataResource;
import com.paypal.selion.platform.dataprovider.SeLionDataProvider;
import com.paypal.selion.platform.dataprovider.impl.InputStreamResource;
import core.BaseTest;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import core.util.TextFilesHandler;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.FormatManagerControl;
import eastnets.screening.entity.Format;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import io.qameta.allure.Step;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.*;

import java.io.FileInputStream;
import java.io.IOException;

public class FormatManager_TC001 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final FormatManagerControl formatManagerControl = new FormatManagerControl();
    private final TextFilesHandler textFilesHandler = new TextFilesHandler();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    @Step("Login to filtering.")
    public void login() {
        try {
            screeningServicesDelegate.cleanFormatTable(Common.ZONE.COMMON_TEST_ZONE_006.getZone().getDisplayName());
            loginPage.login(driver,
                    Common.OPERATOR.FULL_RIGHT_6.getOperator().getLoginName(),
                    Common.OPERATOR.FULL_RIGHT_6.getOperator().getPassword());

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test Case: V"Verify that user is able to Import, Export, Edit and Delete a format of List/Scan type.
     * STEPS:
     * 1- Create a new format
     * 2- Export the format
     * 3- Import the format
     * 4- Delete the format
     */

    @Test(dataProvider = "createFormatRegTestData")
    @Owner("Tarek Allam")
    @Tag("Regression")
    public void formatManager_TC001(Format format) {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription(format.getTestCaseTitle()));
            lifecycle.updateTestCase(testResult -> testResult.setName(format.getTestCaseTitle()));

            int random = randomizer.getInt();
            clearDownloadedFiles();
            textFilesHandler.deleteFileOnRemoteNode(driver, dockerServerIp);
            format.setName(String.format(format.getName(), random));
            for (int i = 0; i < format.getFields().size(); i++) {
                format.getFields().get(i).setName(String.format(format.getFields().get(i).getName(), random));
            }

            formatManagerControl.AddNewFormat(driver, format);
            Assert.assertTrue(formatManagerControl.checkFormatExist(driver, format), GeneralConstants.POM_EXCEPTION_ERR_MSG);
            clearDownloadedFiles();
            formatManagerControl.exportFormat(driver, format);
            Assert.assertTrue(textFilesHandler.checkExistOfFileWithDynamicName(driver, "ExportFormat_Format", dockerServerIp), GeneralConstants.POM_EXCEPTION_ERR_MSG);
            formatManagerControl.deleteFormat(driver, format);
            String fileName = textFilesHandler.get_downloaded_file_name(driver, dockerServerIp);
            String filePath = System.getProperty("user.dir") + GeneralConstants.DOWNLOAD_FILE_PATH + "/" + fileName;
            textFilesHandler.move_file_to_local_dir(driver, fileName, browserDefaultDownloadPath, dockerServerIp);
            formatManagerControl.importFormat(driver, format.getZone(), filePath);
            formatManagerControl.deleteFormat(driver, format);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @DataProvider(name = "createFormatRegTestData" )
    public Object[][] createFormatRegTestData() throws IOException {
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("createNewFormat");
        DataResource resource =
                new InputStreamResource(new FileInputStream(System.getProperty("user.dir") + csvDataFilePath),
                        Format.class, "json");
        SeLionDataProvider dataProvider =
                DataProviderFactory.getDataProvider(resource);
        return dataProvider.getAllData();
    }
}
