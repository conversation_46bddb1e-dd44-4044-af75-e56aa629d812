package eastnets.screening.regression.formatmanager;

import com.fasterxml.jackson.databind.ObjectMapper;
import core.*;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.FormatManagerControl;
import eastnets.screening.control.listManager.ListSetControl;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FileScan;
import eastnets.screening.entity.Format;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import io.qameta.allure.Step;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.io.File;

public class FormatManager_TC002 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final FormatManagerControl formatManagerControl = new FormatManagerControl();
    private final ListSetControl listSetControl = new ListSetControl();
    private final ResultManagerControl resultManagerControl = new ResultManagerControl();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();

    private RemoteWebDriver driver;

    @BeforeClass(alwaysRun = true)
    @Step("Login to filtering.")
    public void login() {
        try {
            driver = getDriver();
            screeningServicesDelegate.cleanFormatTable(Common.ZONE.COMMON_TEST_ZONE_006.getZone().getDisplayName());
            loginPage.login(driver,
                    Common.OPERATOR.FULL_RIGHT_6.getOperator().getLoginName(),
                    Common.OPERATOR.FULL_RIGHT_6.getOperator().getPassword());

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test Case: verify that the unmatched rule appears in the detection details from the results tab
     * 1- verify that the additional context variables appear when adding a good guy.
     * 2- verify the out of context result based on the violation filter added.
     * 3- verify the suspected record selection in drop down list.
     * 4- verify that the unmatched rule appears in the detection details from the results tab.
     * STEPS:
     * 1- Create list set and link with black list
     * 2- Add a violation filter = "CN <> '146'".
     * 3- add "Abdullah" as new entity
     * 4- Create a new scan format
     * 5- Scan the file "/src/test/resources/uploadsAndDownloads/uploads/ContextTestFileScan.txt"
     * 6- Add the detection as a context good guy
     * 7- Scan the file "/src/test/resources/uploadsAndDownloads/uploads/ContextTestFileScan.txt"
     * 8- Verify the status list
     * 9- Verify the unmatched roles.
     */

    @Test()
    @Owner("Tarek Allam")
    @Tag("Regression")
    public void formatManager_TC002() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Format manager - Additional context variables \n" +
                    "* verify that the additional context variables appear when adding a good guy\n" +
                    "* verify the out of context result based on the violation filter added\n" +
                    "* verify the suspected record selection in drop down list\n" +
                    "* verify that the unmatched rule appears in the detection details from the results tab"));
            lifecycle.updateTestCase(testResult -> testResult.setName("verify that the unmatched rule appears in the detection details from the results tab"));

            int random = randomizer.getInt();
            EnList enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.COMMON_TEST_ZONE_006.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, enlist, Common.PROFILE.FULL_RIGHT_006.getProfile().getName());
            String violationFilter = "CN <> '146'";
            Assert.assertTrue(listSetControl.add_post_violation_filter(driver, enlist.getZoneName(), enlist.getListSet().getName(), enlist.getName(), violationFilter)
                            .contains("Syntax is valid!")
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            Assert.assertEquals(listSetControl.save_violation_filter(driver, true)
                    , "Operation completed successfully."
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            enlist.getEntry().get(0).setName("Abdullah");
            enlist.getEntry().get(0).setFirstName(null);
            commonTestMethods.createNewEntryWithTypeAndNames(driver, enlist);

            Format format = eastnets.screening.entity.Common.FORMAT.SCAN_FORMAT2.get();
            format.setName(String.format(format.getName(), random));
            formatManagerControl.AddNewFormat(driver, format);

            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("blockDetection");
            FileScan fileScan = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), FileScan.class);
            fileScan.setFilePath(GeneralConstants.SCAN_FILE_FOR_FORMAT);
            fileScan.setResult("All suspected records");
            fileScan.setFormat(format.getName());
            commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            resultManagerControl.add_detection_as_context_good_guy(driver, format.getFields().get(1).getName());

            commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            Assert.assertEquals(resultManagerControl.get_status_list(driver).get(0)
                    , "Accepted"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            Assert.assertEquals(resultManagerControl.get_status_list(driver).get(1)
                    , "External"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            Assert.assertEquals(resultManagerControl.get_status_list(driver).get(2)
                    , "Reported"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            Assert.assertEquals(resultManagerControl.get_unmatched_roles(driver, "External")
                    , violationFilter
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterClass(alwaysRun = true)
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
