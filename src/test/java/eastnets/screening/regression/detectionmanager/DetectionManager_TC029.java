package eastnets.screening.regression.detectionmanager;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import core.util.TextFilesHandler;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.common.gui.Navigation;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.AdvancedSettingsControls.AdvancedSettingsControl;
import eastnets.screening.control.AdvancedSettingsControls.EngineTuningControl;
import eastnets.screening.control.FormatManagerControl;
import eastnets.screening.control.listManager.ListSetControl;
import eastnets.screening.entity.*;
import eastnets.screening.gui.scanManager.resultManager.ResultManager;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

public class DetectionManager_TC029 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final AdvancedSettingsControl advancedSettingsControl = new AdvancedSettingsControl();
    private final EngineTuningControl engineTuningControl = new EngineTuningControl();
    private final ListSetControl listSetControl = new ListSetControl();
    private final FormatManagerControl formatManagerControl = new FormatManagerControl();
    private final TextFilesHandler textFilesHandler = new TextFilesHandler();
    private final ResultManager resultManager = new ResultManager();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getPassword()
            );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test Case: Verify that violation Filter "SW_MATCH_FIELD" is working fine with all field types
     * STEPS:
     * 1- Insert BICS data from file  "/ src/ test/ resources/ DataFiles/ sqlQueries/ InsertBicsQuery. txt"
     * 1- Navigate to Advanced Settings -> Engine Tuning
     * 2- Select 'Always" for detect swift option
     * 3- perform restart services
     * 4- Add new list Set and import entries from file =  "/src/test/resources/uploadsAndDownloads/uploads/ImportEntryAndType.txt"
     * 5- Add post violation filter with the following syntax:
     * (SW_MATCH_FIELD = 'PayerBankBic') OR (SW_MATCH_FIELD = 'PayerName')
     * OR (SW_MATCH_FIELD = 'PayerName') OR (SW_MATCH_FIELD = 'BenefIBAN')
     * OR (SW_MATCH_FIELD = 'PayerCtry') OR (SW_MATCH_FIELD = 'PayerAddress')
     * OR (SW_MATCH_FIELD = 'Type') OR (SW_MATCH_FIELD = 'PayerResidence')
     * OR (SW_MATCH_FIELD = 'BenefAgentInstr1') OR (SW_MATCH_FIELD = 'FeePayerBank')
     * 6- Import scan format from file = "/src/test/resources/uploadsAndDownloads/uploads/ExportFormat_Payment.xml"
     * 7- Perform file scan with 'Payment' format , file path =  UPLOAD_FILE_PATH+"/ Scan_BIC. xml"
     * 8- Verify that the detection is done successfully
     * 9- Verify that the violations are displayed correctly as in the expected result = /testDataFiles/ViolationList-Assertions.json
     */


    @Test()
    @Owner("Sarah Abdellatif")
    @Tag("Regression")
    @Feature("Detection Manager")
    public void detectionManager_TC029() {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Title Verify that violation Filter \"SW_MATCH_FIELD\" is working fine with all field types"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Title Verify that violation Filter \"SW_MATCH_FIELD\" is working fine with all field types"));

            String queryString = textFilesHandler.getTextFileContentAsString(GeneralConstants.INSERT_BICS_QUERY_FILE_PATH);
            screeningServicesDelegate.excuteQueryAdmin(queryString);

            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("swMatchFields_Format");
            EngineTuning engineTuning = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), EngineTuning.class);


            Navigation.ADVANCED_SETTINGS.navigate(driver);
            advancedSettingsControl.navigateToEngineTuning(driver);

            String actualResult = engineTuningControl.addEngineSetting(driver, engineTuning);
            Assert.assertTrue(actualResult.contains("Please keep in mind that changing the engine settings may drastically change the scanning results." +
                            " Note that some of the changed parameters require the SafeWatch Screening solution to be restarted.")
                            || actualResult.contains("Nothing has been changed.")
                    , "Failed to add engine setting.");

            commonTestMethods.restartNewArch();

            String filePath = System.getProperty("user.dir") + GeneralConstants.IMPORT_ENTRY_NAME_TYPE_LIST_FILE_PATH;
            Format format = eastnets.screening.entity.Common.FORMAT.IMPORT_ENTRY_NAME_TYPE.get();
            EnList enList = commonTestMethods.getEnListData();
            enList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_002.getZone().getDisplayName());
            commonTestMethods.addListSetAndimportEntries(driver, enList, Common.PROFILE.FULL_RIGHT_002.getProfile()
                    , Common.ZONE.COMMON_TEST_ZONE_002.getZone(), filePath, format, null);

            commonAction.logout(driver);
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getPassword()
                   );



            String violationFilter = textFilesHandler.getTextFileContentAsString(GeneralConstants.COMPLEX_VIOLATION_FILTER_FILE_PATH);
            Assert.assertTrue(listSetControl.add_post_violation_filter(driver, enList.getZoneName(), enList.getListSet().getName()
                            , enList.getName(), violationFilter).contains("Syntax is valid!")
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            Assert.assertEquals(listSetControl.save_violation_filter(driver, true)
                    , "Operation completed successfully."
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            String scanFormatFilePath = System.getProperty("user.dir") + GeneralConstants.BICS_SCAN_FORMAT_FILE_PATH;

            formatManagerControl.importFormat(driver, enList.getZoneName(), scanFormatFilePath);

            csvDataFilePath = screeningTestDataConfigsProps.getProperty("blockDetection");
            FileScan fileScan = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), FileScan.class);

            fileScan.setEnList(enList);
            fileScan.setFilePath(System.getProperty("user.dir") + GeneralConstants.BICS_SCAN_FILE_PATH);
            fileScan.setRank("50");
            fileScan.setFormat("Payment");

            commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            ArrayList<Violation> violationsFromUI = resultManager.getViolationList(driver);

            csvDataFilePath = screeningTestDataConfigsProps.getProperty("violationList_Assertions");
            List<Violation> violations = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), new TypeReference<List<Violation>>() {
            });

            Assert.assertEqualsNoOrder(violationsFromUI, violations);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

}
