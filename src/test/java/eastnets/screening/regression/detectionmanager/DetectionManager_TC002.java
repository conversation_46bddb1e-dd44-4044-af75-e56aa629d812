package eastnets.screening.regression.detectionmanager;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.gui.scanManager.scanName.NameScanManager;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class DetectionManager_TC002 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();
    private final NameScanControl nameScanControl = new NameScanControl();

    //private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            System.out.println("Running testTwo on thread: " + Thread.currentThread().getId());
            //driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(getDriver()
                    , Common.OPERATOR.FULL_RIGHT_5.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_5.getOperator().getPassword()
                   );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test Case: Verify that user is able to perform a Don't Know action on an alert created from 'Name Checker' from the 'Alerts Section'
     * STEPS:
     * 1- Create a new list set
     * 2- Create a new entry with type and names
     * 3- Perform a name scan for the created entry
     * 4- Verify that the detection status is "REPNEW"
     * 5- Perform a Don't Know action on the detection
     * 6- Verify that the detection status is "REPDK"
     */


    @Test()
    @Owner("Sarah Abdellatif")
    @Tag("Regression")
    @Feature("Detection Manager")
    public void detectionManager_TC002() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that user is able to perform a Don't Know action on an alert created from 'Name Checker' from the 'Alerts Section'"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that user is able to perform a Don't Know action on an alert created from 'Name Checker' from the 'Alerts Section'"));

            screeningServicesDelegate.excuteQueryAdmin(GeneralConstants.DB_QUERY[22]);

            EnList list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_005.getZone().getDisplayName());

            commonTestMethods.createListSet(getDriver(), list, Common.PROFILE.FULL_RIGHT_005.getProfile().getName());
            commonTestMethods.createNewEntryWithTypeAndNames(getDriver(), list);

            String name = list.getEntry().get(0).getName() + ", " + list.getEntry().get(0).getFirstName();
            Assert.assertEquals(nameScanControl.scan_Name(getDriver()
                            , name,
                            list.getZoneName()
                            , "50"
                            , true
                            , true
                            , true
                            , false)
                    , "REPNEW"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            String detectionID = new NameScanManager().getDetectionID(getDriver());
            Assert.assertNotNull(detectionID, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


            Assert.assertEquals(detectionManagerControl.perform_dont_Know(getDriver(), detectionID, "Don't Know")
                    , "1 detection(s) successfully modified!"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            detectionManagerControl.search_by_id(getDriver(), detectionID);
            Assert.assertEquals(detectionManagerControl.get_first_listed_detection_status(getDriver()), "REPDK", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            commonAction.logout(getDriver());
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
