package eastnets.screening.regression.detectionmanager;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.gui.scanManager.scanName.NameScanManager;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class DetectionManager_TC008 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();
    private final NameScanControl nameScanControl = new NameScanControl();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_5.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_5.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test Case: 1- Verify that user is able to perform a Pending action on a detection
     * created from 'Name Checker' from the 'Detections Section'
     * 2- Verify that user is able to Assign a detection created from 'Name Checker'
     * to User/Group from the 'Detections Section'
     * STEPS:
     * 1- Create a new list set
     * 2- Create a new entry with type and names
     * 3- Perform a name scan for the created entry
     * 4- Assign the detection to a group
     * 5- Perform a pending action on the detection
     */


    @Test()
    @Owner("Sarah Abdellatif")
    @Tag("Regression")
    @Feature("Detection Manager")
    public void detectionManager_TC008() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that user is able to perform a Pending action on a detection created from 'Name Checker' from the 'Detections Section'\n" +
                    "Verify that user is able to Assign a detection created from 'Name Checker' to User/Group from the 'Detections Section' "));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that user is able to perform a Pending action on a detection created from 'Name Checker' from the 'Detections Section'\n" +
                    "Verify that user is able to Assign a detection created from 'Name Checker' to User/Group from the 'Detections Section' "));

            screeningServicesDelegate.excuteQueryAdmin(GeneralConstants.DB_QUERY[22]);

            EnList list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_005.getZone().getDisplayName());


            commonTestMethods.createListSet(driver, list, Common.PROFILE.FULL_RIGHT_005.getProfile().getName());
            commonTestMethods.createNewEntryWithTypeAndNames(driver, list);

            String name = list.getEntry().get(0).getName() + ", " + list.getEntry().get(0).getFirstName();
            Assert.assertEquals(nameScanControl.scan_Name(driver
                            , name
                            , list.getZoneName()
                            , "50"
                            , true
                            , true
                            , true
                            , false)
                    , "REPNEW"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            String detectionID = new NameScanManager().getDetectionID(driver);
            Assert.assertNotNull(detectionID, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Assert.assertEquals(detectionManagerControl.assignDetection(driver, detectionID, Common.GROUP.FULL_RIGHT_GROUP_05.get().getName(), "Assign")
                    , "1 detection(s) successfully modified!"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Assert.assertEquals(detectionManagerControl.pending_detection(driver, detectionID, "Assign")
                    , "1 detection(s) successfully modified!"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @AfterMethod()
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

}
