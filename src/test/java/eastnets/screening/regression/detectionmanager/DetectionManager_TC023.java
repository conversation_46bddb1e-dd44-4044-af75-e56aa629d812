package eastnets.screening.regression.detectionmanager;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import core.util.TextFilesHandler;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class DetectionManager_TC023 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();
    private final TextFilesHandler textFilesHandler = new TextFilesHandler();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_5.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_5.getOperator().getPassword()
                  );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test Case: Verify that user is able to Print a detection created from 'DB Scan' from the 'Alerts Section'
     * STEPS:
     * 1- Import "Korean" list and link it to a new list set
     * 2- Perform a DB scan and verify that the detection is created successfully
     * 3- Verify that the detection status is "Reported"
     * 4- Navigate to detection manager and export the detection with "ALL" and "SELECTED" options
     * 5- Verify that the detection is exported successfully
     */


    @Test(enabled = false)
    @Owner("Sarah Abdellatif")
    @Tag("Regression")
    @Feature("Detection Manager")
    public void detectionManager_TC023() {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that user is able to Print a detection created from 'DB Scan' from the 'Alerts Section'"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that user is able to Print a detection created from 'DB Scan' from the 'Alerts Section'"));

            screeningServicesDelegate.excuteQueryAdmin(GeneralConstants.DB_QUERY[22]);

            EnList list = commonTestMethods.getEnListData();
            list.setName(GeneralConstants.KOREAN_LIST_NAME);
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_005.getZone().getDisplayName());
            String fileName = GeneralConstants.KOREAN_LIST_FILE_PATH;

            commonTestMethods.deleteBlackListFromDB(list);

            commonTestMethods.importBListAndLinkToNewListSet(driver, list, Common.PROFILE.FULL_RIGHT_005.getProfile().getName(), fileName);

            String detectionID = commonTestMethods.dbScan(driver, list.getZoneName(), GeneralConstants.DB_SCAN_DETECTION_MANAGER_FILE_PATH);
            fileName = "rptDetectionDetailsReport.pdf";

            textFilesHandler.deleteFileOnRemoteNode(driver, dockerServerIp);
            Assert.assertTrue(detectionManagerControl.export_detection_alerts(driver, detectionID, "ALL", null, fileName, dockerServerIp)
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            textFilesHandler.deleteFileOnRemoteNode(driver, dockerServerIp);
            Assert.assertTrue(detectionManagerControl.export_detection_alerts(driver, detectionID, "SELECTED", null, fileName, dockerServerIp)
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

}
