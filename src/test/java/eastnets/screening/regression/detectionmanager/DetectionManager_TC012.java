package eastnets.screening.regression.detectionmanager;

import com.fasterxml.jackson.databind.ObjectMapper;
import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.control.FormatManagerControl;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FileScan;
import eastnets.screening.entity.Format;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;

public class DetectionManager_TC012 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();
    private final FormatManagerControl formatManagerControl = new FormatManagerControl();
    private final ResultManagerControl resultManagerControl = new ResultManagerControl();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod()
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_5.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_5.getOperator().getPassword()
                   );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test Case: 1- Verify that user is able to perform a Don't Know action on a detection created
     * from 'File Scan' with 'Custom Format' format from the 'Detections Section'
     * 2- Verify that the Structured Records for the Custom format are appearing properly
     * STEPS:
     * 1- Import "Korean" list and link it to a new list set
     * 2- Create a new scan format with 2 fields First Name and Last Name
     * 3- Scan a file with the created format , File path = UPLOAD_FILE_PATH+"/ FileScannerFormat. txt"
     * 4- Verify that the detection status is "Reported"
     * 5- Perform a Don't Know action on the detection
     * 6- Verify that the detection status is "REPDK"
     * 7- Verify that the structured records are appearing properly
     */


    @Test()
    @Owner("Sarah Abdellatif")
    @Tag("Regression")
    @Feature("Detection Manager")
    public void detectionManager_TC012() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that user is able to perform a Don't Know action on a detection created from 'File Scan' with 'Custom Format' format from the 'Detections Section'\n" +
                    "Verify that the Structured Records for the Custom format are appearing properly"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that user is able to perform a Don't Know action on a detection created from 'File Scan' with 'Custom Format' format from the 'Detections Section'\n" +
                    "Verify that the Structured Records for the Custom format are appearing properly"));

            screeningServicesDelegate.excuteQueryAdmin(GeneralConstants.DB_QUERY[22]);

            EnList list = commonTestMethods.getEnListData();
            list.setName(GeneralConstants.KOREAN_LIST_NAME);
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_005.getZone().getDisplayName());
            String filePath = GeneralConstants.KOREAN_LIST_FILE_PATH;

            commonTestMethods.deleteBlackListFromDB(list);

            commonTestMethods.importBListAndLinkToNewListSet(driver, list, Common.PROFILE.FULL_RIGHT_005.getProfile().getName(), filePath);

            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("blockDetection");
            FileScan fileScan = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), FileScan.class);
            fileScan.setFilePath(GeneralConstants.FILE_SCANNER_FORMAT_FILE_PATH);

            Format format = eastnets.screening.entity.Common.FORMAT.SCAN_FORMAT.get();
            format.setName("format" + new Date().getTime());
            Assert.assertNull(formatManagerControl.AddNewFormat(driver, format), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG + " while creating new format.");
            fileScan.setFormat(format.getName());

            commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            Assert.assertEquals(resultManagerControl.get_detection_status(driver)
                    , "Reported"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            String detectionID = resultManagerControl.get_detection_id(driver);
            Assert.assertNotNull(detectionID, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Assert.assertEquals(detectionManagerControl.perform_dont_Know(driver, detectionID, "Don't Know")
                    , "1 detection(s) successfully modified!"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            detectionManagerControl.search_by_id(driver, detectionID);
            Assert.assertEquals(detectionManagerControl.get_first_listed_detection_status(driver), "REPDK", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            ArrayList<ArrayList<String>> structuredRecords = detectionManagerControl.get_structured_record_data(driver, detectionID);

            SoftAssert softAssert = new SoftAssert();
            softAssert.assertEquals(structuredRecords.get(0).get(1), format.getFields().get(0).getName(), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            softAssert.assertEquals(structuredRecords.get(0).get(2), format.getFields().get(0).getType(), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            softAssert.assertEquals(structuredRecords.get(0).get(3), "CHUN-SIK", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            softAssert.assertEquals(structuredRecords.get(1).get(1), format.getFields().get(1).getName(), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            softAssert.assertEquals(structuredRecords.get(1).get(2), format.getFields().get(1).getType(), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            softAssert.assertEquals(structuredRecords.get(1).get(3), "CHOE", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            softAssert.assertAll();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @AfterMethod()
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

}
