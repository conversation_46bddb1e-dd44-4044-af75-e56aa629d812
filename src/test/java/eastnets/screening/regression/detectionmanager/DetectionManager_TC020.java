package eastnets.screening.regression.detectionmanager;

import com.fasterxml.jackson.databind.ObjectMapper;
import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import core.util.TextFilesHandler;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FileScan;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.io.File;

public class DetectionManager_TC020 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();
    private final ResultManagerControl resultManagerControl = new ResultManagerControl();
    private final TextFilesHandler textFilesHandler = new TextFilesHandler();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_5.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_5.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test Case: Verify that user is able to Print a detection created from 'File Scan' with 'Generic Text' format from the 'Alerts Section'
     * STEPS:
     * 1- Import "Korean" list and link it to a new list set
     * 2- Perform a file scan with 'Generic Text' format, File Path = UPLOAD_FILE_PATH+"/ FileScannerFormat. txt"
     * 3- Verify that detection status is 'Reported'
     * 4- Navigate to detection manager and export the detection with 'ALL' Scope
     * 5- Verify that the detection is exported successfully
     * 6- Navigate to detection manager and export the detection with 'SELECTED' Scope
     * 7- Verify that the detection is exported successfully
     */


    @Test()
    @Owner("Sarah Abdellatif")
    @Tag("Regression")
    @Feature("Detection Manager")
    public void detectionManager_TC020() {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that user is able to Print a detection created from 'File Scan' with 'Generic Text' format from the 'Alerts Section'"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that user is able to Print a detection created from 'File Scan' with 'Generic Text' format from the 'Alerts Section'"));

            screeningServicesDelegate.excuteQueryAdmin(GeneralConstants.DB_QUERY[22]);

            EnList list = commonTestMethods.getEnListData();
            list.setName(GeneralConstants.KOREAN_LIST_NAME);
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_005.getZone().getDisplayName());
            String filePath = GeneralConstants.KOREAN_LIST_FILE_PATH;

            commonTestMethods.deleteBlackListFromDB(list);

            commonTestMethods.importBListAndLinkToNewListSet(driver, list, Common.PROFILE.FULL_RIGHT_005.getProfile().getName(), filePath);

            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("blockDetection");
            FileScan fileScan = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), FileScan.class);
            fileScan.setFilePath(GeneralConstants.FILE_SCANNER_FORMAT_FILE_PATH);
            fileScan.setFormat("Generic Text");

            commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            Assert.assertEquals(resultManagerControl.get_detection_status(driver)
                    , "Reported"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            String detectionID = resultManagerControl.get_detection_id(driver);
            Assert.assertNotNull(detectionID, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            filePath = "rptDetectionDetailsReport.pdf";
            textFilesHandler.deleteFileOnRemoteNode(driver, dockerServerIp);
            Assert.assertTrue(detectionManagerControl.export_detection_alerts(driver, detectionID, "ALL", null, filePath, dockerServerIp)
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            textFilesHandler.deleteFileOnRemoteNode(driver, dockerServerIp);
            Assert.assertTrue(detectionManagerControl.export_detection_alerts(driver, detectionID, "SELECTED", null, filePath, dockerServerIp)
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

}
