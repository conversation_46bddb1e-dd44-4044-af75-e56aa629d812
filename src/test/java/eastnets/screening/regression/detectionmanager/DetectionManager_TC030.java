package eastnets.screening.regression.detectionmanager;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paypal.selion.platform.dataprovider.DataProviderFactory;
import com.paypal.selion.platform.dataprovider.DataResource;
import com.paypal.selion.platform.dataprovider.SeLionDataProvider;
import com.paypal.selion.platform.dataprovider.impl.InputStreamResource;
import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import core.util.TextFilesHandler;
import core.util.Wait;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.common.gui.Navigation;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.AdvancedSettingsControls.AdvancedSettingsControl;
import eastnets.screening.control.AdvancedSettingsControls.EngineTuningControl;
import eastnets.screening.control.ISO20022Control;
import eastnets.screening.control.listManager.ListSetControl;
import eastnets.screening.entity.*;
import eastnets.screening.gui.scanManager.resultManager.ResultManager;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.*;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;

public class DetectionManager_TC030 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final AdvancedSettingsControl advancedSettingsControl = new AdvancedSettingsControl();
    private final EngineTuningControl engineTuningControl = new EngineTuningControl();
    private final ISO20022Control iso20022Control = new ISO20022Control();
    private final ListSetControl listSetControl = new ListSetControl();
    private final TextFilesHandler textFilesHandler = new TextFilesHandler();
    private final ResultManager resultManager = new ResultManager();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();
    private final Wait wait = new Wait();

    private RemoteWebDriver driver;

    EnList enList;
    @BeforeClass()
    public void pre_conditions() {
        try {
            driver = getDriver();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getPassword()
                    );

            String queryString = textFilesHandler.getTextFileContentAsString(GeneralConstants.INSERT_BICS_QUERY_FILE_PATH);
            screeningServicesDelegate.excuteQueryAdmin(queryString);

            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("swMatchFields_Format");
            EngineTuning engineTuning = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), EngineTuning.class);


            Navigation.ADVANCED_SETTINGS.navigate(driver);
            advancedSettingsControl.navigateToEngineTuning(driver);

            String actualResult = engineTuningControl.addEngineSetting(driver, engineTuning);
            Assert.assertTrue(actualResult.contains("Please keep in mind that changing the engine settings may drastically change the scanning results." +
                            " Note that some of the changed parameters require the SafeWatch Screening solution to be restarted.")
                            || actualResult.contains("Nothing has been changed.")
                    , "Failed to add engine setting.");

            commonTestMethods.stopAllScanServices();
            commonTestMethods.startDefaultScanServices();
            wait.time(Wait.ONE_SECOND * 20);

            iso20022Control.navigateToISO20022FormatManager(driver);

            csvDataFilePath = screeningTestDataConfigsProps.getProperty("isoConfiguration");
            ISO20022FormatConfiguration formatConfiguration = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath)
                    , ISO20022FormatConfiguration.class);

            formatConfiguration.setZone(Common.ZONE.COMMON_TEST_ZONE_002.getZone().getDisplayName());
            formatConfiguration.setGroupName(String.format(formatConfiguration.getGroupName(), randomizer.getInt()));
            Assert.assertEquals(iso20022Control.createGroup(driver, formatConfiguration)
                    , String.format("New group [%s] successfully added", formatConfiguration.getGroupName())
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while create new group.");

            iso20022Control.clickConfigure(driver);

            String filePath = GeneralConstants.IMPORT_ISO_GROUP_CONFIGURATIONS_FILE_PATH;
            actualResult = iso20022Control.importConfigurations(driver, filePath);
            Assert.assertTrue(actualResult.contains(" successfully imported")
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while create new group.");

            filePath = System.getProperty("user.dir") + GeneralConstants.IMPORT_ENTRY_NAME_TYPE_LIST_FILE_PATH;
            Format format = eastnets.screening.entity.Common.FORMAT.IMPORT_ENTRY_NAME_TYPE.get();
            enList = commonTestMethods.getEnListData();
            commonTestMethods.addListSetAndimportEntries(driver, enList, Common.PROFILE.FULL_RIGHT_002.getProfile()
                    , Common.ZONE.COMMON_TEST_ZONE_002.getZone(), filePath, format, formatConfiguration.getGroupName());

            commonAction.logout(driver);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_2.getOperator().getPassword()
                   );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test Case:
     */

    @Test(dataProvider = "violationsTestData")
    @Owner("Sarah Abdellatif")
    @Tag("Regression")
    @Feature("Detection Manager")
    public void detectionManager_TC030(Violation violation) {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Title Verify that violation Filter \"SW_MATCH_FIELD\" is working fine with all field types"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Title Verify that violation Filter \"SW_MATCH_FIELD\" is working fine with all field types"));


            EnList list = enList;


            Assert.assertTrue(listSetControl.add_post_violation_filter(driver, list.getZoneName(), list.getListSet().getName()
                            , list.getName(), violation.getViolationFilter()).contains("Syntax is valid!")
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);


            Assert.assertEquals(listSetControl.save_violation_filter(driver, true)
                    , "Operation completed successfully."
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);


            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("blockDetection");
            FileScan fileScan = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), FileScan.class);

            fileScan.setEnList(enList);
            fileScan.setFilePath(System.getProperty("user.dir") + GeneralConstants.BICS_SCAN_IOS_FILE_PATH);
            fileScan.setResult("All suspected records");
            fileScan.setFormat("ISO20022");

            commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            ArrayList<Violation> violationsFromUI = resultManager.getViolationList(driver);
            Assert.assertEquals(violationsFromUI.get(0).getMatchedEntity(), violation.getMatchedEntity(), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(violationsFromUI.get(0).getStatus(), violation.getStatus(), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @DataProvider(name = "violationsTestData")
    public Object[][] violationsTestData() throws IOException {
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("swMatchFields_IOS");
        System.out.println(csvDataFilePath);
        DataResource resource =
                new InputStreamResource(new FileInputStream(System.getProperty("user.dir") + csvDataFilePath),
                        Violation.class, "json");
        SeLionDataProvider dataProvider =
                DataProviderFactory.getDataProvider(resource);
        return dataProvider.getAllData();

    }


}
