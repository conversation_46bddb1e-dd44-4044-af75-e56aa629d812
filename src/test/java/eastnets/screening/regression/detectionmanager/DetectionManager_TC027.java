package eastnets.screening.regression.detectionmanager;

import com.fasterxml.jackson.databind.ObjectMapper;
import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import core.util.TextFilesHandler;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FileScan;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.io.File;

public class DetectionManager_TC027 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();
    private final ResultManagerControl resultManagerControl = new ResultManagerControl();
    private final TextFilesHandler textFilesHandler = new TextFilesHandler();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_5.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_5.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test Case:
     * 1-   Filtering  - Verify that columns display properly correct value in 'DetectionManagerReport'
     * 2-   Filtering  - Verify that 'SLA ID' column display properly correct value in 'DetectionManagerReport'
     * 3-   Filtering  - Verify that 'UETR' column display properly correct value in 'DetectionManagerReport'
     * 4-   Filtering - Verify that 'SLA ID' and 'UETR' display properly with correct values in 'DetectionManagerReport'
     * STEPS:
     * 1- Create a new list set
     * 2- Create a new entry with name 'Osama bin laden'
     * 3- Perform Scan For RJE file , File path = UPLOAD_FILE_PATH+"/ MT103-both. txt"
     * 4- Verify that the detection status is 'Reported'
     * 5- Navigate to Detection Manager and make GPITableField visible for 'Serv. Type ID' and 'UETR'
     * 6- Export the detection to PDF file
     * 7- Verify that the PDF file contains the correct values for 'Serv. Type ID' and 'UETR'
     */


    @Test()
    @Owner("Sarah Abdellatif")
    @Tag("Regression")
    @Feature("Detection Manager")
    public void detectionManager_TC027() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setName("Filtering  - Verify that columns display properly correct value in 'DetectionManagerReport'"));
            lifecycle.updateTestCase(testResult -> testResult.setDescription("* Filtering  - Verify that 'SLA ID' column display properly correct value in 'DetectionManagerReport'\n" +
                    "* Filtering  - Verify that 'UETR' column display properly correct value in 'DetectionManagerReport'\n" +
                    "* Filtering - Verify that 'SLA ID' and 'UETR' display properly with correct values in 'DetectionManagerReport' "));

            screeningServicesDelegate.excuteQueryAdmin(GeneralConstants.DB_QUERY[22]);

            EnList list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_005.getZone().getDisplayName());

            commonTestMethods.createListSet(driver, list, Common.PROFILE.FULL_RIGHT_005.getProfile().getName());
            list.getEntry().get(0).setName("Osama bin laden");
            list.getEntry().get(0).setFirstName(null);
            commonTestMethods.createNewEntryWithTypeAndNames(driver, list);

            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("blockDetection");
            FileScan fileScan = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), FileScan.class);

            fileScan.setFilePath(GeneralConstants.MT103_BOTH_FILE_PATH);

            commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            Assert.assertEquals(resultManagerControl.get_detection_status(driver)
                    , "Reported"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            String bothDetectionID = resultManagerControl.get_detection_id(driver);
            Assert.assertNotNull(bothDetectionID, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            detectionManagerControl.search_by_id(driver, null);

            detectionManagerControl.showGPITableField(driver, "Serv. Type ID");
            detectionManagerControl.showGPITableField(driver, "UETR");

            String fileName = "rptDetectionDetailsReport.pdf";
            textFilesHandler.deleteFileOnRemoteNode(driver, dockerServerIp);
            Assert.assertTrue(detectionManagerControl.export_detection_alerts(driver, bothDetectionID, "ALL", null, fileName, dockerServerIp)
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            String _121 = "8E0FE365-A88E-426B-8484-4FB7FEE92742";
            String _111 = "001";

            String pdfContent = textFilesHandler.get_pdf_file_content(driver, fileName, System.getProperty("user.dir") + GeneralConstants.DOWNLOAD_FILE_PATH, dockerServerIp);
            Assert.assertTrue(pdfContent.contains(_111), "Pdf not contains :" + _111);
            Assert.assertTrue(pdfContent.contains(_111), "Pdf not contains :" + _111);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

}
