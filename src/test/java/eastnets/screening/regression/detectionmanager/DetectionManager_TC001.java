package eastnets.screening.regression.detectionmanager;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.common.gui.Navigation;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class DetectionManager_TC001 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_5.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_5.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test Case: 1- Verify that user is able to Create alert for any open alert that is created from 'DB Scan' from the 'Alerts Section'.
     * 2- Verify that user is able to search for a detection created from 'DB Scan' with an Open Alerts
     * STEPS:
     * 1- Create a new list
     * 2- Import the list "Korean" and link it to a new list set
     * 3- Run a DB Scan
     * 4- Create an alert for the detection
     * 5- Verify that the alert is created successfully
     * 6- Verify detection status is "REPNEW"
     */


    @Test(enabled = false)
    @Owner("Sarah Abdellatif")
    @Tag("Regression")
    @Feature("Detection Manager")
    public void detectionManager_TC001() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that user is able to Create alert for any open alert that is created from 'DB Scan' from the 'Alerts Section'.\n" +
                    "Verify that user is able to search for a detection created from 'DB Scan' with an Open Alerts"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that user is able to Create alert for any open alert that is created from 'DB Scan' from the 'Alerts Section'.\n" +
                    "Verify that user is able to search for a detection created from 'DB Scan' with an Open Alerts"));

            EnList list = commonTestMethods.getEnListData();
            list.setName(GeneralConstants.KOREAN_LIST_NAME);
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_005.getZone().getDisplayName());
            String filePath = GeneralConstants.KOREAN_LIST_FILE_PATH;

            commonTestMethods.deleteBlackListFromDB(list);

            commonTestMethods.importBListAndLinkToNewListSet(driver, list, Common.PROFILE.FULL_RIGHT_005.getProfile().getName(), filePath);

            String detectionID = commonTestMethods.dbScan(driver, Common.ZONE.COMMON_TEST_ZONE_005.getZone().getDisplayName(), GeneralConstants.DB_SCAN_DETECTION_MANAGER_FILE_PATH);

            Navigation.DETECTION_MANAGER.navigate(driver);
            Assert.assertEquals(detectionManagerControl.create_alert(driver, detectionID, "Create Alert")
                    , "1 violation(s) successfully modified!"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            detectionManagerControl.search_by_id(driver, detectionID);
            Assert.assertEquals(detectionManagerControl.get_first_listed_detection_status(driver), "REPNEW", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
