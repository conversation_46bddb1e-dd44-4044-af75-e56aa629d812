package eastnets.screening.regression.detectionmanager;

import com.paypal.selion.platform.dataprovider.DataProviderFactory;
import com.paypal.selion.platform.dataprovider.DataResource;
import com.paypal.selion.platform.dataprovider.SeLionDataProvider;
import com.paypal.selion.platform.dataprovider.impl.InputStreamResource;
import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.control.FormatManagerControl;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FileScan;
import eastnets.screening.entity.Format;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.Date;

public class DetectionManager_TC003 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();
    private final FormatManagerControl formatManagerControl = new FormatManagerControl();
    private final ResultManagerControl resultManagerControl = new ResultManagerControl();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_5.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_5.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test Case: Verify that user is able to perform a Don't Know action on an alert
     * created from 'File Scan' with 'Custom Format/Generic Text' format from the Alerts Section
     * STEPS:
     * 1- Create a new list set
     * 2- Import 'Korean' list and link it to the list set
     * 3- Create a new format
     * 4- Perform a file scan with the created format for UPLOAD_FILE_PATH+/FileScannerFormat.txt
     * 5- Verify that the detection status is 'Reported'
     * 6- Perform a Don't Know action on the detection
     * 7- Verify that the detection status is 'REPDK'
     * 8- Perform scan with Generic Text format for UPLOAD_FILE_PATH+/FileScannerGenericText.txt
     * 9- Verify that the detection status is 'Reported'
     * 10- Perform a Don't Know action on the detection
     * 11- Verify that the detection status is 'REPDK'
     */


    @Test(dataProvider = "fileScan_DontKnowTestData")
    @Owner("Sarah Abdellatif")
    @Tag("Regression")
    @Feature("Detection Manager")
    public void detectionManager_TC003(FileScan fileScan) {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription(String.format("Verify that user is able to perform a Don't Know action on an alert created " +
                    "from 'File Scan' with '%s' format from the 'Alerts Section", fileScan.getFormat())));
            lifecycle.updateTestCase(testResult -> testResult.setName(String.format("Verify that user is able to perform a Don't Know action on an alert created " +
                    "from 'File Scan' with '%s' format from the 'Alerts Section", fileScan.getFormat())));

            screeningServicesDelegate.excuteQueryAdmin(GeneralConstants.DB_QUERY[22]);

            EnList list = commonTestMethods.getEnListData();
            list.setName(GeneralConstants.KOREAN_LIST_NAME);
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_005.getZone().getDisplayName());
            String filePath = GeneralConstants.KOREAN_LIST_FILE_PATH;

            screeningServicesDelegate.deleteLinkBtwBlackListAndListSet(list.getZoneName(), list.getName());
            screeningServicesDelegate.deleteLinkBtwBlackListAndGoodGuy(list.getZoneName(), list.getName());
            screeningServicesDelegate.deleteBlackList(list.getName());

            commonTestMethods.importBListAndLinkToNewListSet(driver, list, Common.PROFILE.FULL_RIGHT_005.getProfile().getName(), filePath);

            fileScan.setFilePath(System.getProperty("user.dir") + GeneralConstants.UPLOAD_FILE_PATH + fileScan.getFilePath());

            if (fileScan.getFormat().equals("Custom Format")) {
                Format format = eastnets.screening.entity.Common.FORMAT.SCAN_FORMAT.get();
                format.setName(String.format("format%s", new Date().getTime()));
                Assert.assertNull(formatManagerControl.AddNewFormat(driver, format), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG + " while creating new format.");
                fileScan.setFormat(format.getName());
            }


            commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            Assert.assertEquals(resultManagerControl.get_detection_status(driver)
                    , "Reported"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            String detectionID = resultManagerControl.get_detection_id(driver);
            Assert.assertNotNull(detectionID, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


            Assert.assertEquals(detectionManagerControl.perform_dont_Know(driver, detectionID, "Don't Know")
                    , "1 detection(s) successfully modified!"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            detectionManagerControl.search_by_id(driver, detectionID);
            Assert.assertEquals(detectionManagerControl.get_first_listed_detection_status(driver), "REPDK", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @AfterMethod()
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @DataProvider(name = "fileScan_DontKnowTestData")
    public Object[][] fileScan_DontKnowTestData() throws IOException {
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("performDontKnowAction_FileScan");
        DataResource resource =
                new InputStreamResource(new FileInputStream(System.getProperty("user.dir") + csvDataFilePath),
                        FileScan.class, "json");
        SeLionDataProvider dataProvider =
                DataProviderFactory.getDataProvider(resource);
        return dataProvider.getAllData();

    }
}
