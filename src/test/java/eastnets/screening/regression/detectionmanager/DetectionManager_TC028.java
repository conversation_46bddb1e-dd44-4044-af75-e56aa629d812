package eastnets.screening.regression.detectionmanager;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.gui.scanManager.scanName.NameScanManager;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Feature;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class DetectionManager_TC028 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();
    private final NameScanControl nameScanControl = new NameScanControl();
    private final NameScanManager nameScanManager = new NameScanManager();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_5.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_5.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Bug 81179: AIX - Cant create an alert without inserting comment
     * Verify that user is able to Create alert for any open alert that is
     * created from 'Name Checker' from the 'Alerts Section' WITHOUT adding a comment
     * STEPS:
     * 1- Create a new list set
     * 2- Create a new entry
     * 3- Perform a name scan for the created entry
     * 4- Create an alert for the detection without adding a comment
     * 5- Verify that the validation message is displayed
     */


    @Test()
    @Owner("Sarah Abdellatif")
    @Tag("Regression")
    @Feature("Detection Manager")
    @Issue("81179")
    public void detectionManager_TC028() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("related to Bug 81179: AIX - Cant creat an alert without inserting comment\n" +
                    "*Verify that user is able to Create alert for any open alert that is created from 'Name Checker' from the 'Alerts Section' WITHOUT adding a comment"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that user is able to Create alert for any open alert that is created from 'Name Checker' from the 'Alerts Section' WITHOUT adding a comment"));
            clearDownloadedFiles();

            EnList list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_005.getZone().getDisplayName());


            commonTestMethods.createListSet(driver, list, Common.PROFILE.FULL_RIGHT_005.getProfile().getName());
            commonTestMethods.createNewEntryWithTypeAndNames(driver, list);

            String name = list.getEntry().get(0).getName() + ", " + list.getEntry().get(0).getFirstName();
            Assert.assertEquals(nameScanControl.scan_Name(driver
                            , name
                            , list.getZoneName()
                            , "50"
                            , false
                            , false
                            , false
                            , false)
                    , "REP"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            String detectionID = nameScanManager.getDetectionID(driver);
            Assert.assertNotNull(detectionID, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Assert.assertEquals(detectionManagerControl.create_alert(driver, detectionID, "")
                    , "Please, enter a note before saving!"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

}
