package eastnets.screening.regression.detectionmanager;

import com.fasterxml.jackson.databind.ObjectMapper;
import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FileScan;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.io.File;

public class DetectionManager_TC026 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();
    private final ResultManagerControl resultManagerControl = new ResultManagerControl();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_5.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_5.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test Case:
     * 1-   Detection Manager - Verify that Detections return from RJE msg without and with GPI labels return when selecting 'both' option from  drop down list
     * 2-   Verify that the detection was found in detection manager
     * 3-   Filtering - Verify that 'SLA ID' column showing the value for '111'  field in RJE msgs in 'Detections' table
     * 4-   Filtering - Verify that 'UETR' column showing the value for '121'  field in RJE msgs in 'Detections' table
     * 5-   Filtering - Detection Manager - Verify that only Detections return from RJE msgs without gpi lables return when selecting 'RJE' option from 'Filter By' drop down list
     * 6-   Filtering - Detection Manager - Verify that only Detections return from msgs with gpi lables return when selecting 'gpi' option from 'Filter By' drop down list
     * 7-   Filtering - Verify that Search working properly when searching for RJE msgs using 'SLA ID' and  'UETR' values
     * 8-   Filtering - Detection Manager - Verify that new 'UETR' checkboxe dispalys in 'Column Panel'
     * 9-   Filtering - Detection Manager - Verify that new 'SLA ID' checkboxe dispalys in 'Column Panel'
     */


    @Test()
    @Owner("Sarah Abdellatif")
    @Tag("Regression")
    @Feature("Detection Manager")
    public void detectionManager_TC026() {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setName("Detection Manager - Verify that Detections return from RJE msg without and with GPI labels return when selecting 'both' option from  drop down list\n" +
                    " Verify that the detection was found in detection manager"));
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that the detection was found in detection manager\n" +
                    "* Filtering - Verify that 'SLA ID' column showing the value for '111'  field in RJE msgs in 'Detections' table\n" +
                    "* Filtering - Verify that 'UETR' column showing the value for '121'  field in RJE msgs in 'Detections' table\n" +
                    "* Filtering - Detection Manager - Verify that only Detections return from RJE msgs without gpi lables return when selecting 'RJE' option from 'Filter By' drop down list\n" +
                    "* Filtering - Detection Manager - Verify that only Detections return from msgs with gpi lables return when selecting 'gpi' option from 'Filter By' drop down list\n" +
                    "* Filtering - Verify that Search working properly when searching for RJE msgs using 'SLA ID' and  'UETR' values\n" +
                    "* Filtering - Detection Manager - Verify that new 'UETR' checkboxe dispalys in 'Column Panel'\n" +
                    "* Filtering - Detection Manager - Verify that new 'SLA ID' checkboxe dispalys in 'Column Panel'"));

            screeningServicesDelegate.excuteQueryAdmin(GeneralConstants.DB_QUERY[22]);


            EnList list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_005.getZone().getDisplayName());


            commonTestMethods.createListSet(driver, list, Common.PROFILE.FULL_RIGHT_005.getProfile().getName());
            list.getEntry().get(0).setName("Osama bin laden");
            list.getEntry().get(0).setFirstName(null);
            commonTestMethods.createNewEntryWithTypeAndNames(driver, list);


            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("blockDetection");
            FileScan fileScan = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), FileScan.class);
            fileScan.setFilePath(GeneralConstants.MT103_NO_FILE_PATH);

            commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            Assert.assertEquals(resultManagerControl.get_detection_status(driver)
                    , "Reported"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            String noDetectionID = resultManagerControl.get_detection_id(driver);
            Assert.assertNotNull(noDetectionID, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


            fileScan.setFilePath(GeneralConstants.MT103_BOTH_FILE_PATH);

            commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            Assert.assertEquals(resultManagerControl.get_detection_status(driver)
                    , "Reported"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            String bothDetectionID = resultManagerControl.get_detection_id(driver);
            Assert.assertNotNull(bothDetectionID, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


            detectionManagerControl.Customize_search_columns(driver, "Format Filter By");
            detectionManagerControl.Customize_search_columns(driver, "Serv. Type Id");
            detectionManagerControl.Customize_search_columns(driver, "UETR");
            detectionManagerControl.Customize_search_columns(driver, "Format");

            detectionManagerControl.search_by_id(driver, null);

            detectionManagerControl.showGPITableField(driver, "Serv. Type ID");
            detectionManagerControl.showGPITableField(driver, "UETR");


            String _121 = "8E0FE365-A88E-426B-8484-4FB7FEE92742";
            String _111 = "001";
            detectionManagerControl.search(driver, null, null, "All Records", "SWIFT RJE Records");
            Assert.assertEquals(detectionManagerControl.get_field_value_by_column_number(driver, bothDetectionID, 29)
                    , _111
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.get_field_value_by_column_number(driver, bothDetectionID, 30)
                    , _121
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            Assert.assertNotEquals(detectionManagerControl.get_row_number_for_detection_id(driver, noDetectionID, "RJE", "SWIFT RJE Records")
                    , -1
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            Assert.assertNotEquals(detectionManagerControl.get_row_number_for_detection_id(driver, bothDetectionID, "gpi", "SWIFT RJE Records")
                    , -1
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            detectionManagerControl.search(driver, _121, _111, "All Records", "SWIFT RJE Records");

            Assert.assertEquals(detectionManagerControl.get_field_value_by_column_number(driver, bothDetectionID, 29)
                    , _111
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.get_field_value_by_column_number(driver, bothDetectionID, 30)
                    , _121
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

}
