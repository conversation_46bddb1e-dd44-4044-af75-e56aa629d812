package eastnets.screening.regression.detectionmanager;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Feature;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class DetectionManager_TC011 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_5.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_5.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test Case: 1- Verify that user is able to perform a Pending action on a detection created from 'DB Scan' from the 'Detections Section'
     * 2- Verify that user is able to Assign a detection created from 'DB Scan' to User/Group from the 'Detections Section'
     * STEPS:
     * 1- Import "Korean" list and link it to a new list set
     * 2- Perform a DB Scan
     * 3- Navigate to the 'Detections Section' and create an alert for the detection
     * 4- Navigate to the 'Detections Section' and release the detection
     * 5- Verify that the detection is released successfully
     */


    @Test(enabled = false)
    @Owner("Sarah Abdellatif")
    @Tag("Regression")
    @Feature("Detection Manager")
    public void detectionManager_TC011() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that user is able to perform a Pending action on a detection created from 'DB Scan' from the 'Detections Section' \n" +
                    "Verify that user is able to Assign a detection created from 'DB Scan' to User/Group from the 'Detections Section'"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that user is able to perform a Pending action on a detection created from 'DB Scan' from the 'Detections Section' \n" +
                    "Verify that user is able to Assign a detection created from 'DB Scan' to User/Group from the 'Detections Section'"));

            screeningServicesDelegate.excuteQueryAdmin(GeneralConstants.DB_QUERY[22]);

            EnList list = commonTestMethods.getEnListData();
            list.setName(GeneralConstants.KOREAN_LIST_NAME);
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_005.getZone().getDisplayName());
            String filePath = GeneralConstants.KOREAN_LIST_FILE_PATH;

            commonTestMethods.deleteBlackListFromDB(list);

            commonTestMethods.importBListAndLinkToNewListSet(driver, list, Common.PROFILE.FULL_RIGHT_005.getProfile().getName(), filePath);

            String detectionID = commonTestMethods.dbScan(driver, list.getZoneName(), GeneralConstants.DB_SCAN_DETECTION_MANAGER_FILE_PATH);

            Assert.assertEquals(detectionManagerControl.create_alert(driver, detectionID, "Create Alert")
                    , "1 violation(s) successfully modified!"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


            Assert.assertEquals(detectionManagerControl.release_detection_4_eyes(driver, detectionID, "Assign", "")
                    , "1 detection(s) successfully modified!"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

}
