package eastnets.screening.regression.bugs.restartServicesRequired;

import com.fasterxml.jackson.databind.ObjectMapper;
import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.admin.entity.Operator;
import eastnets.common.control.Application;
import eastnets.common.gui.LoginPage;
import eastnets.common.gui.Navigation;
import eastnets.screening.control.AdvancedSettingsControls.BusinessLogicControl;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.GeneralSettings;
import io.qameta.allure.Allure;
import io.qameta.allure.Owner;
import io.qameta.allure.Step;
import io.qameta.allure.testng.AllureTestNg;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Listeners;
import org.testng.annotations.Test;

import java.io.File;

@Listeners({AllureTestNg.class})
public class Tk92703Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final BusinessLogicControl businessLogicControl = new BusinessLogicControl();
    private final NameScanControl nameScanControl = new NameScanControl();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();

    private RemoteWebDriver driver;

    Operator operator = Common.OPERATOR.FULL_RIGHT_1.getOperator();

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    @Step("Login to filtering.")
    public void login() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver
                    , operator.getLoginName()
                    , operator.getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This test method is related to bug number = 92703
     * -login to the SWS
     * -Navigate to " Advance settings " module and then select the "Business logic" tab
     * -Enable "Automatically rest assignees" option then restart services or wait 10 m.
     * -Move to the "detection manager".
     * -Assign detection to the user/group
     * - now select another detection and check "Assign To" DDL
     * - observe that "Assign to" shows value select in previous steps (does not reset)
     * - repeat the same steps on alerts section and observe same issue      *
     */
    @Test(priority = 0, enabled = true, groups = {"regression"})
    @Owner("SunilThokala")
    public void tkt_92703() {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Automatically rest assignees does not function on detections alerts"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Automatically rest assignees does not function on detections alerts"));

            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("generalSettings");
            GeneralSettings generalSettings = mapper.readValue(new File(System.getProperty("user.dir")
                    + csvDataFilePath), GeneralSettings.class);
            Assert.assertEquals(businessLogicControl.setGeneralSettingsDetails(driver, generalSettings),
                    "Operation completed successfully! The new configuration will take place after 10 minutes maximum."
                    , "Failed to set general settings details.");
            commonTestMethods.restartNewArch();

            EnList list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, list, Common.PROFILE.FULL_RIGHT_001.getProfile().getName());
            String[] entities = {"Diana", "Omar", "Omar Salman"};
            for (int i = 0; i < entities.length; i++) {
                list.getEntry().get(0).setName(entities[i]);
                list.getEntry().get(0).setFirstName(null);
                commonTestMethods.createNewEntryWithDetailsInfo(driver, list, list.getEntry().get(0));
            }
            Assert.assertEquals(nameScanControl.scan_Name(driver
                            , "Diana"
                            , Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName()
                            , null
                            , true
                            , true
                            , true
                            , false)
                    , "REPNEW"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            String detectionID = nameScanControl.get_detection_id(driver);
            Navigation.DETECTION_MANAGER.navigate(driver);
            Assert.assertEquals(detectionManagerControl.assignDetection(driver, detectionID, Common.GROUP.FULL_RIGHT_GROUP_01.get().getName(), "Assign")
                    , "1 detection(s) successfully modified!"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(nameScanControl.scan_Name(driver
                            , "Omar salman"
                            , Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName()
                            , null
                            , true
                            , true
                            , true
                            , false)
                    , "REPNEW"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            String detectionID2 = nameScanControl.get_detection_id(driver);
            Navigation.DETECTION_MANAGER.navigate(driver);
            Assert.assertTrue(detectionManagerControl.get_assignee_value(driver, detectionID2).contains("< None >")
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }

    }
}
