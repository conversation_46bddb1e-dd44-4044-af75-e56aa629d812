package eastnets.screening.regression.bugs.restartServicesRequired;

import com.fasterxml.jackson.databind.ObjectMapper;
import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.Application;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.AdvancedSettingsControls.BusinessLogicControl;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.control.listManager.BlackListControl;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.GeneralSettings;
import io.qameta.allure.Allure;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.io.File;

public class Tkt_70179_Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final BusinessLogicControl businessLogicControl = new BusinessLogicControl();
    private final BlackListControl blackListControl = new BlackListControl();
    private final NameScanControl nameScanControl = new NameScanControl();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getPassword()
                   );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This test method is related to bug number = 70179
     * Steps:
     * 1. Disable the option "Enable group based permissions for Block/Release/DontKnow buttons"
     * 2. Enable the AutoBlock on one of the blacklist in the used ListSet.
     * 3. Scan a name that generates multiple alerts, including an AutoBlocked alert
     * 4. Check that  perform release that detection not allowed.
     * 5. Detection status should be  Real Violation
     */
    @Test(enabled = true)
    @Owner("Sarah Abdellatif")
    @Issue("70179")
    @Tag("Regression Test")
    public void bug_70179() {
        try {

            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Bug 70179: Wrong behavior when using AutoBlock"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Bug 70179: Wrong behavior when using AutoBlock"));


            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("generalSettings");
            GeneralSettings generalSettings = mapper.readValue(new File(System.getProperty("user.dir")
                    + csvDataFilePath), GeneralSettings.class);


            generalSettings.setEnableGroupForBlockReleaseDonKnow(false);
            Assert.assertEquals(businessLogicControl.setGeneralSettingsDetails(driver, generalSettings)
                    , "Operation completed successfully! The new configuration will take place after 10 minutes maximum."
                    , "Failed to set general settings details.");


            commonTestMethods.restartNewArch();


            EnList blackList = commonTestMethods.getEnListData();
            blackList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            String profileName = Common.PROFILE.FULL_RIGHT_001.getProfile().getName();
            commonTestMethods.createListSet(driver, blackList, profileName);


            blackListControl.select_auto_block(driver, blackList.getZoneName(), blackList.getName(), true);

            blackList.getEntry().get(0).setName("Osama bin laden");
            blackList.getEntry().get(0).setFirstName(null);
            commonTestMethods.createNewEntryWithTypeAndNames(driver, blackList);


            Assert.assertEquals(nameScanControl.scan_Name(driver
                            , "Osama bin laden"
                            , blackList.getZoneName()
                            , null
                            , true
                            , false
                            , false
                            , false)
                    , "REPRV"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            String detection_id = nameScanControl.get_detection_id(driver);

            Assert.assertFalse(detectionManagerControl.check_if_block_option_enabled(driver, detection_id)
                    , "Block button enabled");


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
