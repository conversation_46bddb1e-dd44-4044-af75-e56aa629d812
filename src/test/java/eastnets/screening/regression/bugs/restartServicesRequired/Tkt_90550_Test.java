package eastnets.screening.regression.bugs.restartServicesRequired;

import com.fasterxml.jackson.databind.ObjectMapper;
import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.AdvancedSettingsControls.BusinessLogicControl;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.GeneralSettings;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.io.File;

public class Tkt_90550_Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final NameScanControl nameScanControl = new NameScanControl();
    private final BusinessLogicControl businessLogicControl = new BusinessLogicControl();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_7.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_7.getOperator().getPassword()
            );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This test method is related to bug number = 90550
     * Steps:
     * 1- Go to general Setting under business logic and enable "select first detection" option
     * 2- Restart New Arch(Services and Swserver)
     * 3- Create Black List and link it with list set
     * 4- Create new Entry
     * 5- Scan Entry name
     * 6- Go Detection Manager and search by detection id
     * 7- Make sure that first detection selected without click on it
     * 8- Block detection
     * 9- Make sure that first detection still selected after reload
     */
    @Test(enabled = true)
    @Owner("Sarah Abdellatif")
    @Issue("90550")
    @Tag("Regression Test")
    public void tc_90550() {
        try {

            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Test Case 90550: verify that Once user takes action the system will re-load the data again and select first detection if the option is enabled"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Test Case 90550: verify that Once user takes action the system will re-load the data again and select first detection if the option is enabled"));

            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("generalSettings");
            GeneralSettings generalSettings = mapper.readValue(new File(System.getProperty("user.dir")
                    + csvDataFilePath), GeneralSettings.class);
            generalSettings.setEnableAutomaticFirstDetection(true);


            Assert.assertEquals(businessLogicControl.setGeneralSettingsDetails(driver, generalSettings)
                    , "Operation completed successfully! The new configuration will take place after 10 minutes maximum."
                    , "Failed to set general settings details.");

            commonTestMethods.restartNewArch();

            EnList blackList = commonTestMethods.getEnListData();
            blackList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_007.getZone().getDisplayName());

            commonTestMethods.createListSet(driver, blackList, Common.PROFILE.FULL_RIGHT_007.getProfile().getName());

            commonTestMethods.createNewEntryWithTypeAndNames(driver, blackList);

            String name = blackList.getEntry().get(0).getName() + ", " + blackList.getEntry().get(0).getFirstName();
            String detection_status = nameScanControl.scan_Name(driver
                    , name
                    , blackList.getZoneName()
                    , null
                    , true
                    , true
                    , true
                    , false);
            Assert.assertEquals(detection_status, "REPNEW", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            String detection_id = nameScanControl.get_detection_id(driver);

            Assert.assertEquals(detectionManagerControl.block_detection_without_click_on_detection(driver
                            , detection_id
                            , "block detection alert message")
                    , "1 detection(s) successfully modified!"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


            Assert.assertEquals(detectionManagerControl.add_comment(driver
                            , "add new comment")
                    , "1 detection(s) and 1 alert(s) successfully modified!"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    /**
     * This test method is related to bug number = 90553
     * Steps:
     * 1- Go to general Setting under business logic and enable "select first detection" option
     * 2- Restart New Arch(Services and Swserver)
     * 3- Create Black List and link it with list set
     * 4- Create new Entry
     * 5- Scan Entry name
     * 6- Go Detection Manager and search by detection id
     * 7- Make sure that first detection selected not selected
     */
    @Test(enabled = true)
    @Owner("Sarah Abdellatif")
    @Issue("90553")
    @Tag("Regression Test")
    public void tc_90553() {
        try {

            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Test Case 90553: verify that Once user takes action the system will re-load the data again without selecting first detection if the option is disabled"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Test Case 90553: verify that Once user takes action the system will re-load the data again without selecting first detection if the option is disabled"));

            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("generalSettings");
            GeneralSettings generalSettings = mapper.readValue(new File(System.getProperty("user.dir")
                    + csvDataFilePath), GeneralSettings.class);


            Assert.assertEquals(businessLogicControl.setGeneralSettingsDetails(driver, generalSettings)
                    , "Operation completed successfully! The new configuration will take place after 10 minutes maximum."
                    , "Failed to set general settings details.");

            commonTestMethods.restartNewArch();

            EnList blackList = commonTestMethods.getEnListData();
            blackList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_007.getZone().getDisplayName());

            commonTestMethods.createListSet(driver, blackList, Common.PROFILE.FULL_RIGHT_007.getProfile().getName());

            commonTestMethods.createNewEntryWithTypeAndNames(driver, blackList);

            String name = blackList.getEntry().get(0).getName() + ", " + blackList.getEntry().get(0).getFirstName();
            String detection_status = nameScanControl.scan_Name(driver
                    , name
                    , blackList.getZoneName()
                    , null
                    , true
                    , true
                    , true
                    , false);
            Assert.assertEquals(detection_status, "REPNEW", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            String detection_id = nameScanControl.get_detection_id(driver);

            Assert.assertFalse(
                    detectionManagerControl.check_if_block_option_appear_without_click_on_detection(driver, detection_id
                    )
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
