package eastnets.screening.regression.bugs.restartServicesRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import core.util.TextFilesHandler;
import eastnets.admin.backendServices.AdminServicesDelegate;
import eastnets.admin.entity.Common;
import eastnets.common.control.Application;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.common.gui.Navigation;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.control.SwiftManagerControl;
import eastnets.screening.control.listManager.*;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.Step;
import io.qameta.allure.testng.AllureTestNg;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Listeners;
import org.testng.annotations.Test;

@Listeners({AllureTestNg.class})
public class Tkt91542Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final SwiftManagerControl swiftManagerControl = new SwiftManagerControl();
    private final BlackListControl blackListControl = new BlackListControl();
    private final DowJonesControl dowJonesControl = new DowJonesControl();
    private final AdminServicesDelegate adminServicesDelegate = new AdminServicesDelegate();
    private final ListExplorerControl listExplorerControl = new ListExplorerControl();
    private final NameScanControl nameScanControl = new NameScanControl();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();
    private final GoodGuyControl goodGuyControl = new GoodGuyControl();
    private final ActivityControl activityControl = new ActivityControl();
    private final core.util.TextFilesHandler textFilesHandler = new core.util.TextFilesHandler();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    @Step("Login to filtering.")
    public void login() {
        try {

            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getPassword()
                   );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This test method is related to bug number = 91542
     * Steps:
     * 1- Create a blacklist and  dow jones list
     * 2- Create a good guy
     * 3- Export the good guy
     * 4- Import the good guy with accentuated characters in file name
     * 5- Assert that the import is successful with accentuated characters in file name
     * 6- Assert that the import list status is succeeded
     */

    @Test()
    @Owner("Sunil")
    @Issue("91542")
    @Tag("Regression")
    public void tkt_91542() {
        try {

            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("GoodGuys import fails due accentuated characters in file name "));
            lifecycle.updateTestCase(testResult -> testResult.setName("GoodGuys import fails due accentuated characters in file name"));
            String listName = "PFA2_201407242200_D";
            EnList list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            Assert.assertTrue(swiftManagerControl.CreateSwiftTemplate(driver, list.getListSet().getSwiftTemplate(), list.getZoneName())
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while create new swift template.");
            Assert.assertTrue(blackListControl.create_Black_List(driver, list)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new black list.");
            Assert.assertTrue(dowJonesControl.create_dj_list(driver
                            , list
                            , "Lists (including Sanctions) - By Sanction References"
                            , false)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new Dow-Jones list.");
            Assert.assertEquals(dowJonesControl.enable_dj_list(driver), list.getExpectedMessage(), GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new Dow-Jones list.");
            String userName = Common.OPERATOR.FULL_RIGHT_1.getOperator().getLoginName();
            String password = Common.OPERATOR.FULL_RIGHT_1.getOperator().getPassword();
            String zoneID = adminServicesDelegate.getZoneId(list.getZoneName());
            Assert.assertTrue(dowJonesControl.create_dj_files(list, true, false, userName,
                    password,
                    zoneID, listName, false).contains("INFO  - Bye bye!"), GeneralConstants.POM_EXCEPTION_ERR_MSG);
            dowJonesControl.add_sanction_list(driver, list);
            Assert.assertTrue(dowJonesControl.load_dj_list_load_referential_only(list, false, false, listName, false).contains("Process successfully finished!"), GeneralConstants.POM_EXCEPTION_ERR_MSG);
            commonTestMethods.createListSetOnly(driver, list, Common.PROFILE.FULL_RIGHT_001.getProfile().getName());
            Assert.assertNotEquals(listExplorerControl.get_Rows_Count(driver, list),
                    "0 row(s)",
                    GeneralConstants.POM_EXCEPTION_ERR_MSG + " while getting rows count.");
            Assert.assertEquals(nameScanControl.scan_Name(driver
                            , "Amini, Mahmoud"
                            , Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName()
                            , null
                            , true
                            , true
                            , true
                            , false)
                    , "REPNEW"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            String detectionID = nameScanControl.get_detection_id(driver);
            Navigation.DETECTION_MANAGER.navigate(driver);
            Assert.assertEquals(detectionManagerControl.add_good_guy(driver, detectionID)
                    , "Good guy successfully created!"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            String exportType = "XML";
            Assert.assertTrue(goodGuyControl.search_by_name(driver, "Amini, Mahmoud"), GeneralConstants.POM_EXCEPTION_ERR_MSG);
            clearDownloadedFiles();
            Assert.assertTrue(goodGuyControl.export_good_guy(driver, "good-guys", exportType, dockerServerIp), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            String fileName = textFilesHandler.get_downloaded_file_name(driver, dockerServerIp);
            textFilesHandler.move_file_to_local_dir(driver, fileName, browserDefaultDownloadPath, dockerServerIp);
            Assert.assertFalse(goodGuyControl.delete_good_guy(driver, "Amini, Mahmoud"), GeneralConstants.POM_EXCEPTION_ERR_MSG);
            String actualResult = goodGuyControl.import_non_shared_gg(driver, list.getZoneName(), list.getListSet().getName(), list.getName()
                    , System.getProperty("user.dir") + GeneralConstants.IMPORT_NON_SHARED_GOOD_GUY_WITH_ACCENTATA_PATH);
            Assert.assertEquals(actualResult, "Operation completed successfully.", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(activityControl.get_status(driver)
                    , "SUCCEEDED"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while getting import list status.");

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
        Allure.step("###  DJ list Loaded Successfully  ###");


    }


    @AfterMethod(alwaysRun = true)
    @Step("Logout")
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
