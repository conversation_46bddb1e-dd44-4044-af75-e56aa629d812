package eastnets.screening.regression.bugs.restartServicesRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import eastnets.admin.entity.Common;
import eastnets.common.control.Application;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class Tkt108112Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(groups = {"regression"})
    public void loginForRegression() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getPassword()
                    );

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
    @Test()
    @Owner("Mohamed Hamed")
    @Issue("108112")
    @Tag("Regression")
    public void bug108112()  {
        try{
//            AllureLifecycle lifecycle = Allure.getLifecycle();
//            lifecycle.updateTestCase(testResult -> testResult.setDescription("Changing Advanced Settings is not reflected after restarting scan service and only reflected after being modified directly from DB"));
//            lifecycle.updateTestCase(testResult -> testResult.setName("Changing Advanced Settings is not reflected after restarting scan service and only reflected after being modified directly from DB"));
//
//            EnList blackList = CommonTestMethods.getEnListData();
//            blackList.setZoneName(Common.ZONE.COMMON_TEST_ZONE.getZone().getDisplayName());
//            String profileName = Common.PROFILE.FULL_RIGHT.getProfile().getName();
//            CommonTestMethods.createListSet(driver, blackList, profileName);
//
//            blackList.getEntry().get(0).setName("ISMAIL");
//            blackList.getEntry().get(0).setFirstName("TALIB HUSAYN ALI JARAK");
//            CommonTestMethods.createNewEntryWithTypeAndNames(driver, blackList);
//            EngineTuning engineTuning = new EngineTuning();
//            engineTuning.setShowSettings("Engine Settings");
//            engineTuning.setZone(Common.ZONE.COMMON_TEST_ZONE.getZone().getDisplayName());
//            engineTuning.setEnableDoubleLettersEnhancement(true);
//            Navigation.ADVANCED_SETTINGS.navigate(driver);
//            AdvancedSettingsControl.navigateToEngineTuning(driver);
//            EngineTuningControl.addEngineSetting(driver, engineTuning);
            commonTestMethods.restartNewArch();
//            Assert.assertEquals(NameScanControl.scan_Name(driver,"TALEB HUSSAIN ALI JRAQ",Common.ZONE.COMMON_TEST_ZONE.getZone().getDisplayName(),
//                            "50",true, true, true, false),
//                    "REPNEW", "Scan result is not correct");
//            Assert.assertEquals(NameScanControl.getDetectionRank(driver), "91", "Rank is not correct");
//            engineTuning.setEnableDoubleLettersEnhancement(false);
//            Navigation.ADVANCED_SETTINGS.navigate(driver);
//            AdvancedSettingsControl.navigateToEngineTuning(driver);
//            EngineTuningControl.addEngineSetting(driver, engineTuning);
//            CommonTestMethods.restartNewArch();
//            Assert.assertEquals(NameScanControl.scan_Name(driver,"TALEB HUSSAIN ALI JRAQ",Common.ZONE.COMMON_TEST_ZONE.getZone().getDisplayName(),
//                            "50",true, true, true, false),
//                    "REPNEW", "Scan result is not correct");
//            Assert.assertEquals(NameScanControl.getDetectionRank(driver), "70", "Rank is not correct");



        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }

    }
    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
