package eastnets.screening.regression.bugs.restartServicesRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.scanManger.FileScanControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FileScan;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;


public class Tkt_74566_Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final FileScanControl fileScanControl = new FileScanControl();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_7.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_7.getOperator().getPassword()
            );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This test method is related to bug number = 74566
     * Steps:
     * 1- Make sure that parameter with variable_name=fileSizeLimit	and variable_value=2024 Exits in TConfiguration Table
     * 2- Try to scan file that exceed configured size (File path = src/test/resources/testDataFiles/74566/5000RJE-100PercHits_blocked.rje)
     * 3- Assert that there is Validation message appear
     */
    @Test(enabled = true)
    @Owner("Sarah Abdellatif")
    @Issue("74566")
    @Tag("Regression Test")
    public void tc_74566() {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Bug 74566: No Rate Limiting on File Upload Module"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Bug 74566: No Rate Limiting on File Upload Module"));

            screeningServicesDelegate.excuteQuerSFP(String.format(GeneralConstants.DB_QUERY[45], "2024"));
            commonTestMethods.restartNewArch();
            EnList blackList = commonTestMethods.getEnListData();
            blackList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_007.getZone().getDisplayName());

            commonTestMethods.createListSet(driver, blackList, Common.PROFILE.FULL_RIGHT_007.getProfile().getName());

            FileScan fileScan = commonTestMethods.getFileScanData(GeneralConstants.TKT74566_SCAN_FILE_PATH);
            Assert.assertEquals(fileScanControl.scan_file(driver, fileScan)
                    , "File size exceeded limit [2024]KB ,uploaded file [2263]KB."
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            screeningServicesDelegate.excuteQuerSFP(String.format(GeneralConstants.DB_QUERY[45], "99999999"));

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}


