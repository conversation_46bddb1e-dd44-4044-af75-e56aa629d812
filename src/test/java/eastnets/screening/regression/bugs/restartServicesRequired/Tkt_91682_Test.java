package eastnets.screening.regression.bugs.restartServicesRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import core.constants.screening.MDI_91682_Constants;
import eastnets.admin.entity.Common;
import eastnets.common.control.Application;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.AdvancedSettingsControls.BusinessLogicControl;
import eastnets.screening.control.FormatManagerControl;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FileScan;
import eastnets.screening.entity.Format;
import eastnets.screening.entity.GeneralSettings;
import io.qameta.allure.Allure;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;

import java.util.ArrayList;
import java.util.HashMap;

public class Tkt_91682_Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final BusinessLogicControl businessLogicControl = new BusinessLogicControl();
    private final FormatManagerControl formatManagerControl = new FormatManagerControl();
    private final ResultManagerControl resultManagerControl = new ResultManagerControl();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void pre_requisites() {
        try {
            if (screeningGeneralConfigsProps.getProperty(GeneralConstants.DB_TYPE).equalsIgnoreCase("oracle"))
                screeningServicesDelegate.excuteQuerSFP(MDI_91682_Constants.TKT91682_SET_METAPHONE_BOOST_ORCALE_QUERY);
            else
                screeningServicesDelegate.excuteQuerSFP(MDI_91682_Constants.TKT91682_SET_METAPHONE_BOOST_SQL_QUERY);

            screeningServicesDelegate.excuteQuerSFP(MDI_91682_Constants.TKT91682_INSERT_ARABIC_PREFIX_QUERY);
            screeningServicesDelegate.excuteQuerSFP(MDI_91682_Constants.TKT91682_INSERT_CUSTOM_PHONETICS_QUERY);
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_10.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_10.getOperator().getPassword()
                 );

            GeneralSettings generalSettings = new GeneralSettings();
            generalSettings.setEnableCustomPhonetic(true);
            Assert.assertEquals(businessLogicControl.updatePhoneticSettingsDetails(driver, generalSettings)
                    , "Operation completed successfully! The new configuration will take place after 10 minutes maximum."
                    , "Failed to set Phonetic Settings Details");

            commonTestMethods.restartNewArch();

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This test method is related to bug number = 91682
     * This also covers the below bugs:
     * Bug 90891: SWS 5.1.3: Unexpected low rank match for a phonetically similar names
     * Bug 90897: SWS 5.0: Unexpected low match rank while scanning a name that sounds similar to an entity
     * Task 88578: Enable custom phonetics feature
     */
    @Test(enabled = true)
    @Owner("Sarah Abdellatif")
    @Issue("91682")
    @Tag("Regression Test")
    public void bug_91682() {
        try {

            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setName("Task 110314: Automate Issue 91682: SWS 5.1.3: MDI concerns on the detection algorithm"));
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Task 110314: Automate Issue 91682: SWS 5.1.3: MDI concerns on the detection algorithm\n" +
                    "This also covers the below bugs:\n" +
                    "     * Bug 90891: SWS 5.1.3: Unexpected low rank match for a phonetically similar names\n" +
                    "     * Bug 90897: SWS 5.0: Unexpected low match rank while scanning a name that sounds similar to an entity\n" +
                    "     * Task 88578: Enable custom phonetics feature"));


            ArrayList<EnList> enLists = new ArrayList<>();
            EnList blackList1 = commonTestMethods.getEnListData();
            blackList1.setZoneName(Common.ZONE.COMMON_TEST_ZONE_010.getZone().getDisplayName());
            blackList1.setName("black-list-001");
            enLists.add(blackList1);
            EnList blackList2 = commonTestMethods.getEnListData();
            blackList2.setZoneName(Common.ZONE.COMMON_TEST_ZONE_010.getZone().getDisplayName());
            blackList2.setName("black-list-002");
            enLists.add(blackList2);
            String profileName = Common.PROFILE.FULL_RIGHT_010.getProfile().getName();

            String black_list_path = MDI_91682_Constants.TKT91682_BLACK_LIST_FILE_PATH;
            commonTestMethods.importBList(driver, blackList1, black_list_path, true, false);

            black_list_path = MDI_91682_Constants.TKT91682_BLACK_LIST2_FILE_PATH;
            commonTestMethods.importBList(driver, blackList2, black_list_path, true, false);

            commonTestMethods.linkImportedListToNewListSet(driver, enLists, profileName);

            String file_path = MDI_91682_Constants.TKT91682_SCAN_FORMAT_FILE_PATH;
            Format format = new Format();
            format.setZone(blackList1.getZoneName());
            format.setName("MDI");
            formatManagerControl.importFormat(driver, format.getZone(), file_path);

            FileScan file_scan = commonTestMethods.getFileScanData(MDI_91682_Constants.TKT91682_SCAN_FILE_PATH);
            file_scan.setFormat(format.getName());

            commonTestMethods.scanFile(driver, file_scan);

            HashMap<String, String> detection_details = resultManagerControl.get_all_detections_details(driver);

            SoftAssert softAssert = new SoftAssert();
            for (String key : MDI_91682_Constants.EXPECTED_SCAN_RESULT_MAP.keySet()) {
                if (detection_details.containsKey(key)) {

                    int actualRank = Integer.parseInt(detection_details.get(key));
                    int expectedRank = Integer.parseInt(MDI_91682_Constants.EXPECTED_SCAN_RESULT_MAP.get(key));

                    softAssert.assertTrue(actualRank >= expectedRank
                            , String.format("Expected Rank value = %s, Actual Rank value = %s for scanned name = %s"
                                    , MDI_91682_Constants.EXPECTED_SCAN_RESULT_MAP.get(key)
                                    , detection_details.get(key)
                                    , key));
                }
            }
            softAssert.assertAll();

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            screeningServicesDelegate.excuteQuerSFP(MDI_91682_Constants.TKT91682_DELETE_CUSTOM_PHONETICS_QUERY);
            screeningServicesDelegate.excuteQuerSFP(MDI_91682_Constants.TKT91682_DELETE_ARABIC_PREFIX_QUERY);
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
