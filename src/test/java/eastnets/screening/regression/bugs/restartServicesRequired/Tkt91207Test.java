package eastnets.screening.regression.bugs.restartServicesRequired;

import com.fasterxml.jackson.databind.ObjectMapper;
import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.Application;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.AdvancedSettingsControls.BusinessLogicControl;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.GeneralSettings;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.io.File;

public class Tkt91207Test  extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final BusinessLogicControl businessLogicControl = new BusinessLogicControl();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final NameScanControl nameScanControl = new NameScanControl();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    /**
     * This Test Method is for Bug 91207
     * Steps:
     * Login
     * Adding ListSet
     * Add entity -> name as "木村拓哉"
     * Add entity -> name as "MICHELANGLO"
     * Navigate to advance setting --> set the value of Enable glued words symSpell to 12
     * update the value of Symspell fix length value to 10 by running the below query
     * INSERT INTO SafeWatchDB.dbo.tConfiguration
     * (application, user_name, section_name, variable_name, variable_value, ZONE_ID, CAN_SEGREGATE_PER_ZONE)
     * VALUES( 'SafeWatch Server', 'User', 'Scanning', 'SYMSPELL_PREFIX_LENGTH', 10 , NULL, 0);
     * Restart the services
     * Navigate to Name check
     * Scan the name "木村拓哉" and the rank should be greater than 90
     */
    @Test(priority = 0)
    @Owner("SunilThokala")
    @Tag("Regression Test")
    @Issue("91207")
    public void tkt_91207_Case1() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("validating the rank is greater than 95 for the name MICHELANGL when SET_SYMSPELL_PREFIX_LENGTH_QUERY is set to 10"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Bug 91207: SWS 5.1.x: Screening 5.x: Low Rank of SYM spell glued words comparing with 4.0.10 HF (BNP)"));

            EnList list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            //create a list
            commonTestMethods.createListSet(driver, list, Common.PROFILE.FULL_RIGHT_001.getProfile().getName());
            String[] entities = {"木村拓哉", "MICHELANGLO"};
            for (int i = 0; i < entities.length; i++) {
                list.getEntry().get(0).setName(entities[i]);
                list.getEntry().get(0).setFirstName(null);
                commonTestMethods.createNewEntryWithDetailsInfo(driver, list, list.getEntry().get(0));
            }
            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("symSpellWordSizeSettings");
            GeneralSettings generalSettings = mapper.readValue(new File(System.getProperty("user.dir")
                    + csvDataFilePath), GeneralSettings.class);
            Assert.assertEquals(businessLogicControl.setSymspellWordSize(driver, generalSettings),
                    "Operation completed successfully! The new configuration will take place after 10 minutes maximum."
                    , "Failed to set general settings details.");
            screeningServicesDelegate.excuteQuerSFP(String.format(GeneralConstants.SET_SYMSPELL_PREFIX_LENGTH_QUERY, 10));

            commonTestMethods.restartNewArch();

            Assert.assertEquals(nameScanControl.scan_Name(driver, "MICHELANGLO", Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName(), "50",
                            true, true, true, false), "REPNEW",
                    "Detection status is not correct");
            String rank = nameScanControl.get_detection_rank(driver);
            Assert.assertTrue(Integer.parseInt(rank) > 95, "The ranks is low");




        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());


        }
    }

    @Test(priority = 1)
    @Owner("SunilThokala")
    @Tag("Regression Test")
    @Issue("91207")
    public void tkt_91207_Case2() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("validating the rank is greater than 95 for the name MICHELANGL when SET_SYMSPELL_PREFIX_LENGTH_QUERY is set to 4"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Bug 91207: SWS 5.1.x: Screening 5.x: Low Rank of SYM spell glued words comparing with 4.0.10 HF (BNP)"));


            screeningServicesDelegate.excuteQuerSFP(String.format(GeneralConstants.SET_SYMSPELL_PREFIX_LENGTH_QUERY, 4));

            commonTestMethods.restartNewArch();

            Assert.assertEquals(nameScanControl.scan_Name(driver, "MICHELANGLO", Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName(), "50",
                            true, true, true, false), "REPNEW",
                    "Detection status is not correct");
            String rank = nameScanControl.get_detection_rank(driver);
            Assert.assertTrue(Integer.parseInt(rank) > 95, "The ranks is low");

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());

        }
    }

    @Test(priority = 0, enabled = true)
    @Owner("SunilThokala")
    @Tag("Regression Test")
    @Issue("91207")
    public void tkt_91207_Case3() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("validating the rank is greater than 95 for the name 木村拓哉 when SET_SYMSPELL_PREFIX_LENGTH_QUERY is set to 2"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Bug 91207: SWS 5.1.x: Screening 5.x: Low Rank of SYM spell glued words comparing with 4.0.10 HF (BNP)"));


            screeningServicesDelegate.excuteQuerSFP(String.format(GeneralConstants.SET_SYMSPELL_PREFIX_LENGTH_QUERY, 2));

            commonTestMethods.restartNewArch();

            Assert.assertEquals(nameScanControl.scan_Name(driver, "木村拓哉", Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName(), "50",
                            true, true, true, false), "REPNEW",
                    "Detection status is not correct");
            String rank = nameScanControl.get_detection_rank(driver);
            Assert.assertTrue(Integer.parseInt(rank) > 95, "The ranks is low");

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());

        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}

