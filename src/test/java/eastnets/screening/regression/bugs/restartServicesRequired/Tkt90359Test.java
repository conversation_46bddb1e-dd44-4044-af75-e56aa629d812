package eastnets.screening.regression.bugs.restartServicesRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.util.Randomizer;
import eastnets.admin.entity.Common;
import eastnets.common.control.Application;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.AdvancedSettingsControls.FourEyesControl;
import eastnets.screening.control.AdvancedSettingsControls.FourEyesSettingsControl;
import eastnets.screening.control.AdvancedSettingsControls.InvestigatorsControl;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FourEyes;
import io.qameta.allure.Allure;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.Step;
import io.qameta.allure.testng.AllureTestNg;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Listeners;
import org.testng.annotations.Test;

@Listeners({AllureTestNg.class})
public class Tkt90359Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final FourEyesControl fourEyesControl = new FourEyesControl();
    private final FourEyesSettingsControl fourEyesSettingsControl = new FourEyesSettingsControl();
    private final NameScanControl nameScanControl = new NameScanControl();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod()
    @Step("Login to filtering.")
    public void login() {
        try {

            loginPage.login(driver
                    , Common.OPERATOR.FOUR_EYES_01.getOperator().getLoginName()
                    , Common.OPERATOR.FOUR_EYES_01.getOperator().getPassword()

            );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 0, enabled = true, groups = {"regression"})
    @Owner("SunilThokala")
    @Issue("90359")
    @Tag("Regression")
    public void tkt_90359() {
        try {

            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that user able to scan when has 4 eyes configuration enabled with context condition "));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that user able to scan when has 4 eyes configuration enabled with context condition "));

            EnList list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.FOUR_EYES_ZONE.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, list, Common.PROFILE.FOUR_EYES_PROFILE.getProfile().getName());

            list.getEntry().get(0).setName("China");
            list.getEntry().get(0).setPrograms("China");
            list.getEntry().get(0).setFirstName(null);
            list.getEntry().get(1).setName("Iran");
            list.getEntry().get(1).setPrograms("Iran");
            list.getEntry().get(1).setFirstName(null);

            commonTestMethods.createNewEntryWithDetailsInfo(driver, list, list.getEntry().get(0));
            commonTestMethods.createNewEntryWithDetailsInfo(driver, list, list.getEntry().get(1));

            fourEyesControl.disableFourEyes(driver);
            FourEyes fourEyes = getFourEyesData(list, "SW_ENTITY_PROGRAM= 'China'");
            Assert.assertEquals(fourEyesSettingsControl.create4EyesConfigurationAddContext(driver, fourEyes)
                    , "Operation completed successfully! The new configuration will take place after 10 minutes maximum.");
            fourEyesSettingsControl.enableConfiguration(driver, fourEyes.getName());
            commonTestMethods.restartNewArch();
            String firstScanStatus = nameScanControl.scan_Name(driver
                    , list.getEntry().get(0).getName()
                    , list.getZoneName()
                    , "50"
                    , true
                    , true
                    , true
                    , true);
            String detectionID1 = nameScanControl.get_detection_id(driver);
            String secondScanStatus = nameScanControl.scan_Name(driver
                    , list.getEntry().get(1).getName()
                    , list.getZoneName()
                    , "50"
                    , true
                    , true
                    , true
                    , false);
            String detectionID2 = nameScanControl.get_detection_id(driver);
            Assert.assertEquals(firstScanStatus, "REPNEW", "With 4eyes");
            Assert.assertEquals(secondScanStatus, "REPNEW", "Without 4eyes");

            detectionManagerControl.Customize_search_columns(driver, "4 Eyes");
            detectionManagerControl.select_four_eyes_option(driver, "Yes");
            Assert.assertEquals(detectionManagerControl.search_by_id(driver, detectionID1), "REPNEW", "With 4eyes");
            Assert.assertEquals(detectionManagerControl.search_by_id(driver, detectionID2), "REPNEW", "Without 4eyes");


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }


    }

    private FourEyes getFourEyesData(EnList list, String contextFilter) {
        FourEyes fourEyes = new FourEyes();
        fourEyes.setName("Config" + new Randomizer().timestamp);
        fourEyes.setZone(list.getZoneName());
        fourEyes.setListSet(list.getListSet().getName());
        fourEyes.setContextFilter(contextFilter);
        return fourEyes;
    }

    @AfterMethod()
    @Step("Logout.")
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}

