package eastnets.screening.regression.bugs.restartServicesRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import core.util.Randomizer;
import eastnets.admin.entity.Common;
import eastnets.common.control.Application;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.common.gui.Navigation;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.ISO20022Control;
import eastnets.screening.control.listManager.ListSetControl;
import eastnets.screening.control.scanManger.FileScanControl;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FileScan;
import eastnets.screening.entity.ISO20022FormatConfiguration;
import eastnets.screening.gui.scanManager.resultManager.ResultManager;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;

public class Tkt92810Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ISO20022Control iso20022Control = new ISO20022Control();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final ListSetControl listSetControl = new ListSetControl();
    private final FileScanControl fileScanControl = new FileScanControl();
    private final ResultManagerControl resultManagerControl = new ResultManagerControl();
    private final ResultManager resultManager = new ResultManager();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod()
    public void loginForRegression() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getPassword()
                   );

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This Test Method is for bug 92810
     * Steps:
     * 1. Navigate to Format Manager.
     * 2. Create new group.
     * 3. Configure the group and import the format in the ticket.
     * 4. Edit the message and set Field type to BIC_CODE.
     * 5. Run the query in the ticket.
     * 6. Restart all services.
     * 7. Create List set and Black list with the iso group created and add post filter to it.
     * 8. Add entity to the list.
     * 9. Add another entity as in the ticket.
     * 10. Scan the message in the ticket.
     * 11. Check the result to be Reported.
     * 12. Go to ISO Format Configuration and configure the group again.
     * 13. Change the Field type to Other.
     * 14. Restart all services.
     * 15. Scan the message again.
     * 16. Check the result to be reported.
     */

    @Test()
    @Owner("Mohamed Hamed")
    @Issue("92810")
    @Tag("Regression")
    public void bug_92810() {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Bug 92810: ISO20022 - scan BIC - External violation return instead of reporrted when field type is \"Other\""));
            lifecycle.updateTestCase(testResult -> testResult.setName("Bug 92810: ISO20022 - scan BIC - External violation return instead of reporrted when field type is \"Other\""));


            SoftAssert softassert = new SoftAssert();
            EnList enList = commonTestMethods.getEnListData();
            enList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            ISO20022FormatConfiguration formatConfiguration = new ISO20022FormatConfiguration();
            formatConfiguration.setZone(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            formatConfiguration.setGroupName("GroupName" + randomizer.getInt());
            iso20022Control.navigateToISO20022FormatManager(driver);
            iso20022Control.createGroup(driver, formatConfiguration);
            iso20022Control.clickConfigure(driver);
            iso20022Control.importConfigurations(driver, System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/" + "ExportISO20022Format_[pacs.008.001.08]_31-12-2023_151851.xml");
            Assert.assertEquals(iso20022Control.updateFieldType(driver, "pacs.008.001", "/CdtTrfTxInf/InstdAmt", "BIC_CODE"),
                    "Field successfully updated!",
                    "field is not updated");

            screeningServicesDelegate.deleteBics();
            screeningServicesDelegate.excuteQuerSFP(GeneralConstants.DB_QUERY[38]);

            commonTestMethods.restartNewArch();

            enList.getListSet().setIsoGroup(formatConfiguration.getGroupName());
            enList.getListSet().setSwiftTemplate(null);
            commonTestMethods.createListSet(driver, enList, Common.PROFILE.FULL_RIGHT_001.getProfile().getName());

            String filter = " Amount >= 1000";
            Assert.assertEquals(listSetControl.add_post_violation_filter(driver, enList.getZoneName(), enList.getListSet().getName(), enList.getName(), filter)
                    , "Syntax is valid!"
                    , "Success message is not correct");
            Assert.assertEquals(listSetControl.save_violation_filter(driver, true)
                    , "Operation completed successfully."
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            enList.getEntry().get(0).setName("Armania");
            enList.getEntry().get(0).setType("Country");
            enList.getEntry().get(0).setFirstName(null);
            commonTestMethods.createNewEntryWithTypeAndNames(driver, enList);

            enList.getEntry().get(0).setName("ARARATBANK OJSC");
            enList.getEntry().get(0).setType("Group");
            enList.getEntry().get(0).setFirstName(null);
            commonTestMethods.createNewEntryWithTypeAndNames(driver, enList);
            FileScan message = commonTestMethods.getFileScanData(GeneralConstants.TKT92810_MESSAGE
                    , "ISO20022",
                    "All records"
                    , "50");
            Navigation.SCAN_MANAGER.navigate(driver);

            fileScanControl.scan_file(driver, message);
            Assert.assertEquals(resultManagerControl.check_scanned_file_result(driver), "SUCCEEDED", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            resultManager.clickOnResult(driver);
            softassert.assertEquals(resultManager.getStatusFromResultPage(driver), "Reported", "Status is not correct");
            iso20022Control.navigateToISO20022FormatManager(driver);
            iso20022Control.clickConfigure(driver);
            Assert.assertEquals(iso20022Control.updateFieldType(driver, "pacs.008.001", "/CdtTrfTxInf/InstdAmt", "OTHER"),
                    "Field successfully updated!",
                    "field is not updated");

            commonTestMethods.restartNewArch();
            fileScanControl.scan_file(driver, message);
            Assert.assertEquals(resultManagerControl.check_scanned_file_result(driver), "SUCCEEDED", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            resultManager.clickOnResult(driver);
            softassert.assertEquals(resultManager.getStatusFromResultPage(driver), "Reported", "Status is not correct");

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true, groups = {"regression"})
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
