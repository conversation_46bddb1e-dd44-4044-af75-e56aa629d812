package eastnets.screening.regression.bugs.restartServicesRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.admin.entity.Operator;
import eastnets.common.control.Application;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.common.gui.Navigation;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.AdvancedSettingsControls.AdvancedSettingsControl;
import eastnets.screening.control.FormatManagerControl;
import eastnets.screening.control.listManager.ListSetControl;
import eastnets.screening.control.scanManger.FileScanControl;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FileScan;
import eastnets.screening.gui.advancedSettings.EngineTuningManager;
import eastnets.screening.gui.listManager.listSet.ListSetEditor;
import eastnets.screening.gui.scanManager.resultManager.ResultManager;
import io.qameta.allure.Allure;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.Step;
import io.qameta.allure.testng.AllureTestNg;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Listeners;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;

import java.util.HashMap;
import java.util.Map;


@Listeners({AllureTestNg.class})
public class Tkt89427Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final AdvancedSettingsControl advancedSettingsControl = new AdvancedSettingsControl();
    private final EngineTuningManager engineTuningManager = new EngineTuningManager();
    private final ListSetControl listSetControl = new ListSetControl();
    private final ListSetEditor listSetEditor = new ListSetEditor();
    private final FormatManagerControl formatManagerControl = new FormatManagerControl();
    private final FileScanControl fileScanControl = new FileScanControl();
    private final ResultManagerControl resultManagerControl = new ResultManagerControl();
    private final ResultManager resultManager = new ResultManager();

    private RemoteWebDriver driver;

    Operator operator = Common.OPERATOR.FULL_RIGHT_1.getOperator();
    EnList list;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true, groups = "regression")
    @Step("Login to filtering.")
    public void login() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver
                    , operator.getLoginName()
                    , operator.getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This Test Method is for bug 89427
     * Steps:
     * 1. Run Database queries in the ticket.
     * 2. Go to Engine Tuning and select Show settings.
     * 3. Set Engine settings to Default Configuration.
     * 4. Set Detect Swift Bic to Always.
     * 5. Restart the services.
     * 6. Create list set and add post filter to it.
     * 7. Create blacklist and add the mentioned entities to it.
     * 8. Go to the Format manager and add import the payment format.
     * 9. Go to Scan manager and scan the message. Use the payment format.
     * 10. Go to Results tab and check the result.
     */

    @Test(groups = {"regression"})
    @Issue("89427")
    @Tag("regression")
    @Owner("mohammed hammed")
    public void bug_89427() {
        lifecycle = Allure.getLifecycle();
        lifecycle.updateTestCase(testResult -> testResult.setDescription("Bug 89427: SW_MATCH_FIELD filter is not working"));
        lifecycle.updateTestCase(testResult -> testResult.setName("Bug 89427: SW_MATCH_FIELD filter is not working"));
        try {
            SoftAssert softAssert = new SoftAssert();

            screeningServicesDelegate.deleteBics();
            screeningServicesDelegate.excuteQuerSFP(GeneralConstants.DB_QUERY[39]);
            screeningServicesDelegate.deleteBics();
            screeningServicesDelegate.excuteQuerSFP(GeneralConstants.DB_QUERY[40]);
            screeningServicesDelegate.deleteBics();
            screeningServicesDelegate.excuteQuerSFP(GeneralConstants.DB_QUERY[41]);
            advancedSettingsControl.navigateToEngineTuning(driver);
            engineTuningManager.selectShowSettings(driver, "Engine Settings", "Default Configuration");
            engineTuningManager.selectDetectSwiftBic(driver, "Always");
            commonTestMethods.restartNewArch();
            HashMap<String, String> entities = new HashMap<>();

            entities.put("Iraq", "Country");
            entities.put("AGRIUS3MXXX", "Group");
            entities.put("Lithuania", "Country");
            entities.put("usama bin laden", "Individual");
            entities.put("Cyprus", "Country");
            entities.put("Libya", "Country");
            entities.put("Tripoli", "Unknown");
            entities.put("Syrian Fund", "Group");
            entities.put("Syria", "Country");
            entities.put("the United Arab Emirates", "Country");
            entities.put("LATVIA", "Country");
            entities.put("France", "Country");
            String filter = "(SW_MATCH_FIELD = 'PayerBankBic') OR (SW_MATCH_FIELD = 'PayerName') OR (SW_MATCH_FIELD = 'PayerName') OR (SW_MATCH_FIELD = 'BenefIBAN') OR (SW_MATCH_FIELD = 'PayerCtry') OR (SW_MATCH_FIELD = 'PayerAddress') OR (SW_MATCH_FIELD = 'Type') OR (SW_MATCH_FIELD = 'PayerResidence') OR (SW_MATCH_FIELD = 'BenefAgentInstr1') OR (SW_MATCH_FIELD = 'FeePayerBank')";
            list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, list, Common.PROFILE.FULL_RIGHT_001.getProfile().getName()); //create list
            listSetControl.add_post_violation_filter(driver, list.getZoneName(), list.getListSet().getName(), list.getName(), filter);

            listSetEditor.click_save_button(driver);
            Navigation.SCAN_MANAGER.navigate(driver);
            Navigation.LIST_MANAGER.navigate(driver);
            for (Map.Entry<String, String> entry : entities.entrySet()) {
                list.getEntry().get(0).setName(entry.getKey());
                list.getEntry().get(0).setType(entry.getValue());
                list.getEntry().get(0).setFirstName(null);
                commonTestMethods.createNewEntryWithTypeAndNames(driver, list); //add entities
            }
            formatManagerControl.importFormat(driver, Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName(), System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/" + "ExportFormat_Payment[2023_09_28 _11_17].xml");
            FileScan message = commonTestMethods.getFileScanData(GeneralConstants.TKT89427_Message
                    , "Payment",
                    "All records"
                    , "50");
            Navigation.SCAN_MANAGER.navigate(driver);
            fileScanControl.scan_file(driver, message);
            Assert.assertEquals(resultManagerControl.check_scanned_file_result(driver), "SUCCEEDED", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            resultManager.clickOnResult(driver);
            softAssert.assertEquals(resultManager.getStatusFromResultPage(driver), "Reported", "Status is not correct");
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
