package eastnets.screening.regression.bugs.restartServicesRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import core.util.TextFilesHandler;
import eastnets.admin.entity.Common;
import eastnets.admin.entity.Operator;
import eastnets.common.control.Application;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.FileScan;
import io.qameta.allure.Allure;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.Step;
import io.qameta.allure.testng.AllureTestNg;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Listeners;
import org.testng.annotations.Test;

@Listeners({AllureTestNg.class})
public class Tkt89438Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final ResultManagerControl resultManagerControl = new ResultManagerControl();
    private final core.util.TextFilesHandler textFilesHandler = new core.util.TextFilesHandler();

    private RemoteWebDriver driver;

    Operator operator = Common.OPERATOR.FULL_RIGHT_1.getOperator();

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    @Step("Login to filtering.")
    public void login() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver
                    , operator.getLoginName()
                    , operator.getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This Test Method is for bug 89438
     * Steps:
     * 1. Run the database query in the ticket to disable EXPOSE_PATH.
     * 2. Restart Tomcat.
     * 3. Scan the message in the ticket.
     * 4. Go to Results tab and check the result is Succeeded.
     * 5. Clear all downloaded files.
     * 6. Click on Log icon.
     * 7. Check that the path is not in the downloaded file
     * 8. Run another query to enable the EXPOSE_PATH.
     * 9. Restart Tomcat.
     * 10. Scan the message again and download the log.
     * 11. Check the path is in the downloaded file.
     */

    @Test()
    @Owner("Mohamed Hammed")
    @Tag("Regression")
    @Issue("89438")
    public void bug_89438() {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Issue 89438: SWS:NBK_Egypt_VA_Stack trace disclosure in logs."));
            lifecycle.updateTestCase(testResult -> testResult.setName("Issue 89438: SWS:NBK_Egypt_VA_Stack trace disclosure in logs."));

            screeningServicesDelegate.excuteQuerSFP(GeneralConstants.DB_QUERY[42]);
            commonTestMethods.restartNewArch();

            FileScan message = commonTestMethods.getFileScanData(GeneralConstants.TKT90302_Message
                    , "SWIFT RJE Records",
                    "All records"
                    , "50");

            commonTestMethods.scanFile(driver, message);
            clearDownloadedFiles();
            resultManagerControl.download_log_file(driver);
            String fileName = textFilesHandler.get_downloaded_file_name(driver, dockerServerIp);
            String content = textFilesHandler.get_txt_file_content(driver, fileName, System.getProperty("user.dir") + GeneralConstants.DOWNLOAD_FILE_PATH, dockerServerIp);


            Assert.assertFalse(content.contains("/output/good/760_2_ok.txt"), "'/output/good/760_2_ok.txt'Output file path exists in the log.");
            Assert.assertFalse(content.contains("/output/bad/760_2_blocked.txt"), "'/output/bad/760_2_blocked.txt' Output file path exists in the log.");


            screeningServicesDelegate.excuteQuerSFP(GeneralConstants.DB_QUERY[43]);
            commonTestMethods.restartNewArch();

            commonTestMethods.scanFile(driver, message);
            textFilesHandler.deleteFileOnRemoteNode(driver, dockerServerIp);
            clearDownloadedFiles();
            resultManagerControl.download_log_file(driver);
            fileName = textFilesHandler.get_downloaded_file_name(driver, dockerServerIp);
            content = textFilesHandler.get_txt_file_content(driver, fileName, System.getProperty("user.dir") + GeneralConstants.DOWNLOAD_FILE_PATH, dockerServerIp);

            Assert.assertTrue(content.contains("/output/good/760_2_ok.txt")
                    , "'/output/good/760_2_ok.txt' Output file path does not exist in the log.");
            Assert.assertTrue(content.contains("/output/bad/760_2_blocked.txt"),
                    "'/output/bad/760_2_blocked.txt' Output file path does not exist in the log.");

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }

    }

    @AfterMethod(alwaysRun = true)
    @Step("Logout.")
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
