package eastnets.screening.regression.bugs.restartServicesRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.MDI_91682_Constants;
import eastnets.admin.entity.Common;
import eastnets.common.control.Application;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class Tkt_89277_Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final NameScanControl nameScanControl = new NameScanControl();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void pre_requisites() {
        try {
            screeningServicesDelegate.excuteQuerSFP(MDI_91682_Constants.TKT91682_INSERT_CUSTOM_PHONETICS_QUERY);

            commonTestMethods.restartNewArch();
            driver.navigate().refresh();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_9.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_9.getOperator().getPassword()
                   );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This test method is related to bug number = 89277
     * Steps:
     * 1) Add Libya as a black listed entity
     * Scan the name Lybia
     */
    @Test(enabled = true)
    @Owner("Sarah Abdellatif")
    @Issue("89277")
    @Tag("Regression Test")
    public void bug_89277() {
        try {

            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setName("Bug 89277: No hit when the first/last 2 letters are different"));
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Bug 89277: No hit when the first/last 2 letters are different"));


            EnList blackList = commonTestMethods.getEnListData();
            blackList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_009.getZone().getDisplayName());

            commonTestMethods.createListSet(driver, blackList, Common.PROFILE.FULL_RIGHT_009.getProfile().getName());

            blackList.getEntry().get(0).setName("Libya");
            blackList.getEntry().get(0).setFirstName(null);
            blackList.getEntry().get(0).setType("Country");
            commonTestMethods.createNewEntryWithTypeAndNames(driver, blackList);

            nameScanControl.scan_Name(driver
                    , "Lybia"
                    , Common.ZONE.COMMON_TEST_ZONE_009.getZone().getDisplayName()
                    , "50"
                    , true
                    , true
                    , true
                    , false);
            String rank1 = nameScanControl.get_detection_rank(driver);


            Assert.assertEquals(rank1, "100", "Rank value for Lybia is not as expected");

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            screeningServicesDelegate.excuteQuerSFP(MDI_91682_Constants.TKT91682_DELETE_CUSTOM_PHONETICS_QUERY);

            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}

