package eastnets.screening.regression.bugs.restartServicesRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import eastnets.admin.entity.Common;
import eastnets.common.control.Application;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.AdvancedSettingsControls.FourEyesControl;
import eastnets.screening.control.AdvancedSettingsControls.InvestigatorsControl;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.Step;
import io.qameta.allure.testng.AllureTestNg;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Listeners;
import org.testng.annotations.Test;

@Listeners({AllureTestNg.class})
public class Tkt90347Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final FourEyesControl fourEyesControl = new FourEyesControl();
    private final InvestigatorsControl investigatorsControl = new InvestigatorsControl();
    private final NameScanControl nameScanControl = new NameScanControl();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(groups = {"regression"})
    public void loginForRegression() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.DeFAULT_ZONE_OPERATOR_1.getOperator().getLoginName()
                    , Common.OPERATOR.DeFAULT_ZONE_OPERATOR_1.getOperator().getPassword()
            );

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This Test Method is for Bug 90347
     * Steps:
     * 1. Navigate to Advanced Settings.
     * 2. Navigate to Four Eyes Settings.
     * 3. Enable Four Eyes.
     * 4. Navigate to Advanced Settings.
     * 5. Navigate to Investigators.
     * 6. Assign Investigator Role Checker to the current operator.
     * 7. Create a List Set.
     * 8. Create a blacklist and link it with the list set.
     * 9. Go to Name scan and scan Osama Bin Laden.
     * 1- Go to Detection manager and check the result.
     */
    @Test(enabled = true, groups = {"regression"})
    @Issue("90347")
    @Owner("Mohamed Hamed")
    @Tag("Regression")
    public void bug_90347() {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Issue 90347: SWS 5.1.2 : Default Checker Zone Issue"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Issue 90347: SWS 5.1.2 : Default Checker Zone Issue"));

            String checker_name = Common.OPERATOR.FOUR_EYES_02.getOperator().getLoginName();
            fourEyesControl.enableFourEyes(driver);
            investigatorsControl.assignInvestigatorRoleChecker(driver
                    , checker_name
                    , true);

            commonTestMethods.restartNewArch();
            EnList list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.DEFAULT_ZONE.getZone().getDisplayName());
            list.getEntry().get(0).setName("Osama Bin Laden");
            list.getEntry().get(0).setFirstName(null);
            commonTestMethods.createListSet(driver, list, Common.PROFILE.DEFAULT_PROFILE.getProfile().getName());
            commonTestMethods.createNewEntryWithTypeAndNames(driver, list);

            Assert.assertEquals(nameScanControl.scan_Name(driver
                            , "Osama Bin Landen"
                            , Common.ZONE.DEFAULT_ZONE.getZone().getDisplayName()
                            , "50"
                            , true
                            , true
                            , true
                            , true)
                    , "REPNEW"
                    , "Detection status is not correct");

            String detectionID = nameScanControl.get_detection_id(driver);
            Assert.assertFalse(detectionManagerControl.verify_if_checker_exists(driver, detectionID, checker_name),
                    "Operator exists in the incorrect zone");

            driver.navigate().refresh();
            fourEyesControl.disableFourEyes(driver);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @AfterMethod(alwaysRun = true)
    @Step("Logout")
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

}
