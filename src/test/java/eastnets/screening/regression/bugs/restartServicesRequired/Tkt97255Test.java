package eastnets.screening.regression.bugs.restartServicesRequired;

import com.fasterxml.jackson.databind.ObjectMapper;
import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.Application;
import eastnets.common.gui.LoginPage;
import eastnets.common.gui.Navigation;
import eastnets.screening.control.AdvancedSettingsControls.BusinessLogicControl;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FileScan;
import eastnets.screening.entity.GeneralSettings;
import eastnets.screening.gui.scanManager.resultManager.ResultManager;
import io.qameta.allure.Allure;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.io.File;

public class Tkt97255Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final BusinessLogicControl businessLogicControl = new BusinessLogicControl();
    private final ResultManagerControl resultManagerControl = new ResultManagerControl();
    private final ResultManager resultManager = new ResultManager();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 0, enabled = true, groups = {"regression"})
    @Owner("SunilThokala")
    @Tag("Regression Test")
    public void tkt_97255() {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that clean records displayed on UI when audit tail option is Off and All records is selected"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that clean records displayed on UI when audit tail option is Off and All records is selected"));
            EnList blackList = commonTestMethods.getEnListData();
            blackList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            String profileName = Common.PROFILE.FULL_RIGHT_001.getProfile().getName();

            commonTestMethods.createListSet(driver, blackList, profileName);
            blackList.getEntry().get(0).setName("Osama bin laden");
            blackList.getEntry().get(0).setFirstName(null);
            commonTestMethods.createNewEntryWithTypeAndNames(driver, blackList);

            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("generalSettings");
            GeneralSettings generalSettings = mapper.readValue(new File(System.getProperty("user.dir")
                    + csvDataFilePath), GeneralSettings.class);
            Assert.assertEquals(businessLogicControl.setGeneralSettingsDetails(driver, generalSettings),
                    "Operation completed successfully! The new configuration will take place after 10 minutes maximum."
                    , "Failed to set general settings details.");
            commonTestMethods.restartNewArch();

            FileScan message = commonTestMethods.getFileScanData(GeneralConstants.DEFECT_97255
                    , "Generic Text",
                    "All records"
                    , "50");
            Navigation.SCAN_MANAGER.navigate(driver);

            commonTestMethods.scanFileAndGetDetectionID(driver, message);
            // FileScanControl.scan_file(driver, message);
            Assert.assertEquals(resultManagerControl.check_scanned_file_result(driver), "SUCCEEDED", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            resultManager.clickOnResult(driver);
            //softassert.assertEquals(ResultManager.getStatusFromResultPage(driver), "Reported", "Status is not correct");

            Assert.assertEquals(resultManagerControl.get_detection_status(driver)
                    , "Clean"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

}



