package eastnets.screening.regression.bugs.restartServicesRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.backendServices.AdminServicesDelegate;
import eastnets.admin.entity.Common;
import eastnets.common.control.Application;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.SwiftManagerControl;
import eastnets.screening.control.listManager.BlackListControl;
import eastnets.screening.control.listManager.DowJonesControl;
import eastnets.screening.control.listManager.ListExplorerControl;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class Tkt_90148_Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final NameScanControl nameScanControl = new NameScanControl();
    private final SwiftManagerControl swiftManagerControl = new SwiftManagerControl();
    private final BlackListControl blackListControl = new BlackListControl();
    private final DowJonesControl dowJonesControl = new DowJonesControl();
    private final AdminServicesDelegate adminServicesDelegate = new AdminServicesDelegate();
    private final ListExplorerControl listExplorerControl = new ListExplorerControl();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_7.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_7.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This test method is related to bug number = 90148
     * Steps:
     * 1- Create new list set and blacklist
     * 2- Create New DowJones List (Sanction)
     * 3- Enable DowJones List
     * 4- Load DowJones list References
     * 5- Add List Categories
     * 6- Load list Names
     * 7- Search for names =['',''] and make sure its exit
     * 8- Load List after remove some names
     * 9- Search for names =['',''] and make sure its not exit
     */
    @Test(enabled = true)
    @Owner("Sarah Abdellatif")
    @Issue("90148")
    @Tag("Regression Test")
    public void bug_90148() {
        try {

            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Test Case 90303: Verify load and remove from DJ Incremental list"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Test Case 90303: Verify load and remove from DJ Incremental list"));

            String listName = "90148-FullList";

            EnList list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_007.getZone().getDisplayName());

            Assert.assertTrue(swiftManagerControl.CreateSwiftTemplate(driver, list.getListSet().getSwiftTemplate(), list.getZoneName())
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while create new swift template.");

            Assert.assertTrue(blackListControl.create_Black_List(driver, list)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new black list.");


            Assert.assertTrue(dowJonesControl.create_dj_list(driver
                    , list
                    , "Lists (including Sanctions) - By Sanction References"
                    , true), GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new Dow-Jones list.");

            Assert.assertEquals(dowJonesControl.enable_dj_list(driver), list.getExpectedMessage(), GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new Dow-Jones list.");


            String userName = Common.OPERATOR.FULL_RIGHT_7.getOperator().getLoginName();
            String password = Common.OPERATOR.FULL_RIGHT_7.getOperator().getPassword();
            String zoneID = adminServicesDelegate.getZoneId(list.getZoneName());
            Assert.assertTrue(dowJonesControl.create_dj_files(list, true, false, userName,
                    password,
                    zoneID, listName, false).contains("INFO  - Bye bye!"), GeneralConstants.POM_EXCEPTION_ERR_MSG);

            dowJonesControl.add_sanction_list(driver, list);

            Assert.assertTrue(dowJonesControl.load_dj_list_load_referential_only(list, false, false, listName, false).contains("Process successfully finished!"), GeneralConstants.POM_EXCEPTION_ERR_MSG);

            commonTestMethods.createListSetOnly(driver, list, Common.PROFILE.APPROVAL_RIGHT.getProfile().getName());

            Assert.assertTrue(listExplorerControl.is_entry_exist(driver, list.getZoneName(), "Engineering Construction & Contracting Co."),
                    GeneralConstants.POM_EXCEPTION_ERR_MSG + " while getting rows count.");
            Assert.assertTrue(listExplorerControl.is_entry_exist(driver, list.getZoneName(), "Advanced Technology Solutions"),
                    GeneralConstants.POM_EXCEPTION_ERR_MSG + " while getting rows count.");

            listName = "90148-ListWithRemovedNames";

            Assert.assertTrue(dowJonesControl.load_dj_list_load_referential_only(list, false, false, listName, false).contains("Process successfully finished!"), GeneralConstants.POM_EXCEPTION_ERR_MSG);
            Assert.assertFalse(listExplorerControl.is_entry_exist(driver, list.getZoneName(), "Engineering Construction & Contracting Co."),
                    GeneralConstants.POM_EXCEPTION_ERR_MSG + " while getting rows count.");
            Assert.assertFalse(listExplorerControl.is_entry_exist(driver, list.getZoneName(), "Advanced Technology Solutions"),
                    GeneralConstants.POM_EXCEPTION_ERR_MSG + " while getting rows count.");


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
