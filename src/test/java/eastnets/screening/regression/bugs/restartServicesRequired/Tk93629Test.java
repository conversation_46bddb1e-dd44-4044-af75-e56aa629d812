package eastnets.screening.regression.bugs.restartServicesRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.Application;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.control.RepeatManagerControl;
import eastnets.screening.control.listManager.BlackListControl;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FileScan;
import eastnets.screening.entity.Repeat;
import eastnets.screening.entity.RepeatedData;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.AllureTestNg;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Listeners;
import org.testng.annotations.Test;

import java.util.List;

@Listeners({AllureTestNg.class})
public class Tk93629Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final BlackListControl blackListControl = new BlackListControl();
    private final RepeatManagerControl repeatManagerControl = new RepeatManagerControl();
    private final ResultManagerControl resultManagerControl = new ResultManagerControl();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(groups = {"regression"})
    public void loginForRegression() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getPassword()
                   );

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This Test Method is for TKT 93629
     * Steps:
     * 1. Login to ALMUI.
     * 2. Create a new Blacklist.
     * 3. Create new List set and link it to the created list.
     * 4. Add new Entry to the list (Osama Bin Laden).
     * 5. Share the blacklist.
     * 6. Go to Stripping Detector Manager.
     * 7. Delete all configurations.
     * 8. Create a new configuration.
     * 9. Restart services.
     * 10. SCAN the STP File.
     * 11. Go to Detection Manager and search with detection ID.
     * 12. Check the data exists with Stripping label and message.
     */
    @Test(enabled = true, groups = {"regression"})
    @Owner("Mohamed Hamed")
    public void bug_93629_STP() {
        AllureLifecycle lifecycle = Allure.getLifecycle();
        lifecycle.updateTestCase(testResult -> testResult.setDescription("Bug 93629: Repeat: error when processing MT103 STP"));
        lifecycle.updateTestCase(testResult -> testResult.setName("Bug 93629: Repeat: error when processing MT103 STP"));
        try {
            EnList list;
            list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            list.getEntry().get(0).setName("Osama ben laden");
            list.getEntry().get(0).setFirstName(null);
            list.getEntry().get(0).setType("Individual");
            commonTestMethods.createListSet(driver, list, Common.PROFILE.FULL_RIGHT_001.getProfile().getName()); //create list
            commonTestMethods.createNewEntryWithTypeAndNames(driver, list); //add entities
            blackListControl.share_black_list(driver, list.getName());
            Repeat repeat = new Repeat();
            repeat.setRetentionTime("100000");
            repeat.setConfigurationEnable(true);
            repeat.setListName(list.getName());
            repeat.setConfigName(list.getName() + " STP");
            repeat.setSubType("STP");
            repeat.setRule1("32.CURR,32.AMNT");
            repeatManagerControl.deleteAllRepeatConfigurations(driver);
            System.out.println("alert is " + repeatManagerControl.createRepeatConfig(driver, repeat));
            commonTestMethods.restartNewArch();

            FileScan message = commonTestMethods.getFileScanData(GeneralConstants.TKT93629_Message_STP
                    , "SWIFT RJE Records",
                    "All records"
                    , "50");
            String detectionID = commonTestMethods.scanFileAndGetDetectionID(driver, message);
            Assert.assertEquals(resultManagerControl.get_detection_status(driver)
                    , "Reported"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            detectionManagerControl.search_by_id(driver, detectionID);
            detectionManagerControl.repeat_detection(driver, detectionID, list.getName() + " message STP");
            detectionID = commonTestMethods.scanFileAndGetDetectionID(driver, message);
            Assert.assertEquals(resultManagerControl.get_detection_status(driver)
                    , "Reported"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            detectionManagerControl.search_by_id(driver, detectionID);
            List<RepeatedData> repeatedData = repeatManagerControl.getRepeatData(driver);
            Assert.assertEquals(repeatedData.get(0).getRemarks(), list.getName() + " message STP", "Stripping does not exist in Data");
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This Test Method is for TKT 93629
     * Steps:
     * 1. Login to ALMUI.
     * 2. Create a new Blacklist.
     * 3. Create new List set and link it to the created list.
     * 4. Add new Entry to the list (Osama Bin Laden).
     * 5. Share the blacklist.
     * 6. Go to Stripping Detector Manager.
     * 7. Delete all configurations.
     * 8. Create a new configuration.
     * 9. Restart services.
     * 10. SCAN the REMIT File.
     * 11. Go to Detection Manager and search with detection ID.
     * 12. Check the data exists with Stripping label and message.
     */
    @Test(enabled = true, groups = {"regression"})
    @Owner("Mohamed Hamed")
    public void bug_93629_REMIT() {
        lifecycle = Allure.getLifecycle();
        lifecycle.updateTestCase(testResult -> testResult.setDescription("Bug 93629: Repeat: error when processing MT103 REMIT"));
        lifecycle.updateTestCase(testResult -> testResult.setName("Bug 93629: Repeat: error when processing MT103 REMIT"));
        try {
            EnList list;
            list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            list.getEntry().get(0).setName("Osama ben laden");
            list.getEntry().get(0).setFirstName(null);
            list.getEntry().get(0).setType("Individual");
            commonTestMethods.createListSet(driver, list, Common.PROFILE.FULL_RIGHT_001.getProfile().getName()); //create list
            commonTestMethods.createNewEntryWithTypeAndNames(driver, list); //add entities
            blackListControl.share_black_list(driver, list.getName());
            Repeat repeat = new Repeat();
            repeat.setRetentionTime("100000");
            repeat.setConfigurationEnable(true);
            repeat.setListName(list.getName());
            repeat.setConfigName(list.getName() + "REMIT");
            repeat.setSubType("REMIT");
            repeat.setRule1("32.CURR,32.AMNT");
            repeatManagerControl.deleteAllRepeatConfigurations(driver);
            System.out.println("alert is " + repeatManagerControl.createRepeatConfig(driver, repeat));
            commonTestMethods.restartNewArch();
            FileScan message = commonTestMethods.getFileScanData(GeneralConstants.TKT93629_Message_REMIT
                    , "SWIFT RJE Records",
                    "All records"
                    , "50");
            String detectionID = commonTestMethods.scanFileAndGetDetectionID(driver, message);
            Assert.assertEquals(resultManagerControl.get_detection_status(driver)
                    , "Reported"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            detectionManagerControl.search_by_id(driver, detectionID);
            detectionManagerControl.repeat_detection(driver, detectionID, list.getName() + " message REMIT");
            detectionID = commonTestMethods.scanFileAndGetDetectionID(driver, message);
            Assert.assertEquals(resultManagerControl.get_detection_status(driver)
                    , "Reported"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            detectionManagerControl.search_by_id(driver, detectionID);
            List<RepeatedData> repeatedData = repeatManagerControl.getRepeatData(driver);
            Assert.assertEquals(repeatedData.get(0).getRemarks(), list.getName() + " message REMIT", "Stripping does not exist in Data");
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @AfterMethod(alwaysRun = true, groups = {"regression"})
    public void logout() {
        try {
            driver.navigate().refresh();
//           CommonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
