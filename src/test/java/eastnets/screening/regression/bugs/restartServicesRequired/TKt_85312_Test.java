package eastnets.screening.regression.bugs.restartServicesRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.MDI_91682_Constants;
import eastnets.admin.entity.Common;
import eastnets.common.control.Application;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;


public class TKt_85312_Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final NameScanControl nameScanControl = new NameScanControl();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void pre_requisites() {
        try {
            screeningServicesDelegate.excuteQuerSFP(MDI_91682_Constants.TKT91682_INSERT_CUSTOM_PHONETICS_QUERY);

            commonTestMethods.restartNewArch();
            driver.navigate().refresh();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_9.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_9.getOperator().getPassword()
            );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This test method is related to bug number = 85312
     * Steps:
     * 1) Add Vertikal as a black listed entity
     * Scan the name Vertikal à rank 100
     * Scan the name Wertikal à rank is 100
     * 2) Add "Volmet" as a black listed entity
     * Scan the name "Wolmet" à rank 100
     * 3) Add "Vtormet" as a black listed entity
     * Scan the name "Wtormett" à rank 90
     */
    @Test(enabled = true)
    @Owner("Sarah Abdellatif")
    @Issue("85312")
    @Tag("Regression Test")
    public void bug_91682() {
        try {

            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setName("Task 110312: Automate Bug 85312: Fuzzy logic rank impact with one letter change"));
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Task 110312: Automate Bug 85312: Fuzzy logic rank impact with one letter change"));


            EnList blackList = commonTestMethods.getEnListData();
            blackList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_009.getZone().getDisplayName());

            commonTestMethods.createListSet(driver, blackList, Common.PROFILE.FULL_RIGHT_009.getProfile().getName());

            blackList.getEntry().get(0).setName("vertikal");
            blackList.getEntry().get(0).setFirstName(null);
            commonTestMethods.createNewEntryWithTypeAndNames(driver, blackList);

            blackList.getEntry().get(0).setName("volmet");
            commonTestMethods.createNewEntryWithTypeAndNames(driver, blackList);

            blackList.getEntry().get(0).setName("vtormet");
            commonTestMethods.createNewEntryWithTypeAndNames(driver, blackList);

            nameScanControl.scan_Name(driver
                    , "vertikal"
                    , Common.ZONE.COMMON_TEST_ZONE_009.getZone().getDisplayName()
                    , "50"
                    , true
                    , true
                    , true
                    , false);
            String rank1 = nameScanControl.get_detection_rank(driver);

            nameScanControl.scan_Name(driver
                    , "wertikal"
                    , Common.ZONE.COMMON_TEST_ZONE_009.getZone().getDisplayName()
                    , "50"
                    , true
                    , true
                    , true
                    , false);
            String rank2 = nameScanControl.get_detection_rank(driver);

            nameScanControl.scan_Name(driver
                    , "wolmet"
                    , Common.ZONE.COMMON_TEST_ZONE_009.getZone().getDisplayName()
                    , "50"
                    , true
                    , true
                    , true
                    , false);
            String rank3 = nameScanControl.get_detection_rank(driver);

            nameScanControl.scan_Name(driver
                    , "wtormett"
                    , Common.ZONE.COMMON_TEST_ZONE_009.getZone().getDisplayName()
                    , "50"
                    , true
                    , true
                    , true
                    , false);
            String rank4 = nameScanControl.get_detection_rank(driver);


            SoftAssert softAssert = new SoftAssert();
            softAssert.assertEquals(rank1, "100", "Rank value for vertikal is not as expected");
            softAssert.assertEquals(rank2, "100", "Rank value for wertikal is not as expected");
            softAssert.assertEquals(rank3, "100", "Rank value for wolmet is not as expected");
            softAssert.assertEquals(rank4, "90", "Rank value for wtormett is not as expected");

            softAssert.assertAll();

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            screeningServicesDelegate.excuteQuerSFP(MDI_91682_Constants.TKT91682_DELETE_CUSTOM_PHONETICS_QUERY);
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
