package eastnets.screening.regression.bugs.restartServicesRequired;

import com.fasterxml.jackson.databind.ObjectMapper;
import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.Application;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.FormatManagerControl;
import eastnets.screening.control.listManager.ListSetControl;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FileScan;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import io.qameta.allure.Step;
import io.qameta.allure.testng.AllureTestNg;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.*;
import org.testng.asserts.SoftAssert;

import java.io.File;

@Listeners({AllureTestNg.class})
public class Ticket92216Tests extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ListSetControl listSetControl = new ListSetControl();
    private final ResultManagerControl resultManagerControl = new ResultManagerControl();
    private final FormatManagerControl formatManagerControl = new FormatManagerControl();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();

    private RemoteWebDriver driver;

    @BeforeClass(alwaysRun = true)
    public void beforeClass() {
        try {
            driver = getDriver();
            Allure.step(String.format("Excute Insert Bic Query  : %s", GeneralConstants.DB_QUERY[37]));
            screeningServicesDelegate.deleteBics();
            screeningServicesDelegate.excuteQuerSFP(GeneralConstants.DB_QUERY[37]);
            commonTestMethods.restartNewArch();

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    @Step("Login to filtering.")
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_8.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_8.getOperator().getPassword()
                   );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 0, enabled = true, groups = {"Pre-requisite", "regression"})
    @Owner("Sara Abdellatif")
    public void vfWithIso20022() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that when VF rule \"Amount >= 1000\" is added as a violation filter on blacklist," +
                    " scanning  \"LT_ISO_002.xml\" having amount \"5280\" will generate 4 reported violations, 3 of them against the BIC, scanning  \"LT_ISO_001.xml\" having " +
                    " amount \"280\" will generate \"4 External violations are generated, 3 of them against the BIC, while rescanning \"LT_ISO_002.xml\" will generate reported violations"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that when VF rule \"Amount >= 1000\" is added as a violation filter on blacklist," +
                    " scanning  \"LT_ISO_002.xml\" having amount \"5280\" will generate 4 reported violations, 3 of them against the BIC, scanning  \"LT_ISO_001.xml\" having" +
                    "  amount \"280\" will generate \"4 External violations are generated, 3 of them against the BIC, while rescanning \"LT_ISO_002.xml\" will generate reported violations"));
            SoftAssert softAssert = new SoftAssert();


            EnList enList = commonTestMethods.getEnListData();
            enList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_008.getZone().getDisplayName());
            enList.setName(GeneralConstants.TEST_ARMENIA_LIST_NAME);
            String blackListFilePath = GeneralConstants.TEST_ARMENIA_LIST_FILE_PATH;
            commonTestMethods.importBListAndLinkToNewListSetWithIsoGroup(driver, enList
                    , Common.PROFILE.FULL_RIGHT_008.getProfile().getName(), blackListFilePath, GeneralConstants.PACS_008_001_08_FILE_PATH);

            String violationFilter = "Amount >= 1000";
            Assert.assertTrue(listSetControl.add_post_violation_filter(driver, enList.getZoneName(), enList.getListSet().getName()
                            , enList.getName(), violationFilter).contains("Syntax is valid!")
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            Assert.assertEquals(listSetControl.save_violation_filter(driver, true)
                    , "Operation completed successfully."
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("blockDetection");
            FileScan fileScan = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), FileScan.class);

            fileScan.setFilePath(GeneralConstants.LT_ISO_001_FILE_PATH);
            fileScan.setFormat("ISO20022");
            fileScan.setResult("All records");
            commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            softAssert.assertEquals(resultManagerControl.get_detection_status(driver)
                    , "External"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            softAssert.assertEquals(resultManagerControl.get_number_of_alerts_for_detection(driver),
                    4, "Number of expected alerts not match the actual number of alerts");


            driver.navigate().refresh();
            fileScan.setFilePath(GeneralConstants.LT_ISO_002_FILE_PATH);
            commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            Assert.assertEquals(resultManagerControl.get_detection_status(driver)
                    , "Reported"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            softAssert.assertEquals(resultManagerControl.get_number_of_alerts_for_detection(driver),
                    4, "Number of expected alerts not match the actual number of alerts");

            softAssert.assertAll();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 1, enabled = true, groups = {"Pre-requisite", "regression"})
    @Owner("Sara Abdellatif")
    public void vfWithRje() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that when scanning RJE message  having the same BIC code in previously scanned  ISO20022" +
                    " messages with amount less thant 1000 / greater than 1000 while VF is used on black list" +
                    "  Amount >= 1000 , the correct violation status will be generated on violations returning due to bic code expansion"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that when scanning RJE message  having the same BIC code in previously scanned  ISO20022" +
                    " messages with amount less thant 1000 / greater than 1000 while VF is used on black list " +
                    " Amount >= 1000 , the correct violation status will be generated on violations returning due to bic code expansion"));
            SoftAssert softAssert = new SoftAssert();

            EnList enList = commonTestMethods.getEnListData();
            enList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_008.getZone().getDisplayName());
            enList.setName(GeneralConstants.TEST_ARMENIA_LIST_NAME);
            String blackListFilePath = GeneralConstants.TEST_ARMENIA_LIST_FILE_PATH;
            commonTestMethods.importBListAndLinkToNewListSet(driver, enList
                    , Common.PROFILE.FULL_RIGHT_008.getProfile().getName(), blackListFilePath);

            String violationFilter = "Amount >= 1000";
            Assert.assertTrue(listSetControl.add_post_violation_filter(driver, enList.getZoneName(), enList.getListSet().getName()
                            , enList.getName(), violationFilter).contains("Syntax is valid!")
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            Assert.assertEquals(listSetControl.save_violation_filter(driver, true)
                    , "Operation completed successfully."
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("blockDetection");
            FileScan fileScan = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), FileScan.class);

            fileScan.setFilePath(GeneralConstants.RJE_LESS_THAN_1000_EXTERNAL_FILE_PATH);
            fileScan.setResult("All records");
            commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            softAssert.assertEquals(resultManagerControl.get_detection_status(driver)
                    , "External"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            softAssert.assertEquals(resultManagerControl.get_number_of_alerts_for_detection(driver),
                    2, "Number of expected alerts not match the actual number of alerts");


            driver.navigate().refresh();
            fileScan.setFilePath(GeneralConstants.RJE_MORE_THAN_1000_REPORTED_FILE_PATH);
            commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            Assert.assertEquals(resultManagerControl.get_detection_status(driver)
                    , "Reported"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            softAssert.assertEquals(resultManagerControl.get_number_of_alerts_for_detection(driver),
                    1, "Number of expected alerts not match the actual number of alerts");

            softAssert.assertAll();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 2, enabled = true, groups = {"Pre-requisite", "regression"})
    @Owner("Sara Abdellatif")
    public void vfWithCustomXml() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that when scanning custom xml having the same BIC code in previously scanned  ISO20022" +
                    " messages with amount less thant 1000 / greater than 1000 while VF is used on black list " +
                    " Amount >= 1000 , the correct violation status will be generated on violations returning due to bic code expansion"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that when scanning custom xml  having the same BIC code in previously scanned  ISO20022" +
                    " messages with amount less thant 1000 / greater than 1000 while VF is used on black list " +
                    " Amount >= 1000 , the correct violation status will be generated on violations returning due to bic code expansion"));
            SoftAssert softAssert = new SoftAssert();

            EnList enList = commonTestMethods.getEnListData();
            enList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_008.getZone().getDisplayName());
            enList.setName(GeneralConstants.TEST_ARMENIA_LIST_NAME);
            String blackListFilePath = GeneralConstants.TEST_ARMENIA_LIST_FILE_PATH;

            String actual_result = formatManagerControl.importFormat(driver, enList.getZoneName(), GeneralConstants.PAYMENT_FORMAT_FILE_PATH);
            ;
            Assert.assertTrue(actual_result.equals(null) || actual_result.equals("Import aborted. Format already exists:PaymentLT")
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            commonTestMethods.importBListAndLinkToNewListSet(driver, enList
                    , Common.PROFILE.FULL_RIGHT_008.getProfile().getName(), blackListFilePath);

            String violationFilter = "Amount >= 1000";
            Assert.assertTrue(listSetControl.add_post_violation_filter(driver, enList.getZoneName(), enList.getListSet().getName()
                            , enList.getName(), violationFilter).contains("Syntax is valid!")
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            Assert.assertEquals(listSetControl.save_violation_filter(driver, true)
                    , "Operation completed successfully."
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("blockDetection");
            FileScan fileScan = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), FileScan.class);

            fileScan.setFilePath(GeneralConstants.CUSTOM_XML_LESS_THAN_1000_EXTERNAL_FILE_PATH);
            fileScan.setFormat("PaymentLT");
            fileScan.setResult("All records");
            commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            softAssert.assertEquals(resultManagerControl.get_detection_status(driver)
                    , "External"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            softAssert.assertEquals(resultManagerControl.get_number_of_alerts_for_detection(driver),
                    1, "Number of expected alerts not match the actual number of alerts");


            driver.navigate().refresh();
            fileScan.setFilePath(GeneralConstants.CUSTOM_XML_MORE_THAN_1000_REPORTED_FILE_PATH);
            commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            Assert.assertEquals(resultManagerControl.get_detection_status(driver)
                    , "Reported"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            softAssert.assertEquals(resultManagerControl.get_number_of_alerts_for_detection(driver),
                    1, "Number of expected alerts not match the actual number of alerts");

            softAssert.assertAll();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

}
