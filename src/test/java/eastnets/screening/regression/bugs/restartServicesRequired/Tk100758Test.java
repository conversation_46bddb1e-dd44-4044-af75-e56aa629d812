package eastnets.screening.regression.bugs.restartServicesRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.Application;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.AdvancedSettingsControls.FourEyesControl;
import eastnets.screening.control.AdvancedSettingsControls.InvestigatorsControl;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.control.listManager.ListExplorerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FileScan;
import eastnets.screening.gui.detectionManager.DetectionEditor;
import io.qameta.allure.Allure;
import io.qameta.allure.Owner;
import io.qameta.allure.Step;
import io.qameta.allure.testng.AllureTestNg;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Listeners;
import org.testng.annotations.Test;

@Listeners({AllureTestNg.class})
public class Tk100758Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final FourEyesControl fourEyesControl = new FourEyesControl();
    private final InvestigatorsControl investigatorsControl = new InvestigatorsControl();
    private final ListExplorerControl listExplorerControl = new ListExplorerControl();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();
    private final DetectionEditor detectionEditor = new DetectionEditor();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    @Step("Login to filtering.")
    public void login() {
        try {

            loginPage.login(driver
                    , Common.OPERATOR.FOUR_EYES_01.getOperator().getLoginName()
                    , Common.OPERATOR.FOUR_EYES_01.getOperator().getPassword()

            );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This test method is related to bug number = 100758
     * Enable 4Eyes
     * Create a list,List Set and entities in the list
     * Scan a file and Tentative block the detection
     * click on bulk assign and  select investigator  from checker list.
     * verify the investigator is assigned to the detection
     * assigned the detection to new investgator
     * verify the new investigator is assigned to the detection     *
     */

    @Test(priority = 0, enabled = true, groups = {"regression"})
    @Owner("SunilThokala")
    public void tkt_100758() {
        try {

            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Bulk Assign - 4Eyes detections assign from Checker "));
            lifecycle.updateTestCase(testResult -> testResult.setName("Bulk Assign - 4Eyes detections assign from Checker"));
            fourEyesControl.enableFourEyes(driver);
            String checker_02 = Common.OPERATOR.FOUR_EYES_02.getOperator().getLoginName();
            String checker_03 = Common.OPERATOR.FOUR_EYES_03.getOperator().getLoginName();

            investigatorsControl.assignInvestigatorRoleChecker(driver, checker_02, true);
            investigatorsControl.assignInvestigatorRoleChecker(driver, checker_03, true);
            commonTestMethods.restartNewArch();


            EnList list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.FOUR_EYES_ZONE.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, list, Common.PROFILE.FOUR_EYES_PROFILE.getProfile().getName());
            listExplorerControl.search(driver, list);
            String[] entities = {"Abdullah", "Dastjerdi Ahmad Vahid", "Eslami Mohammad", "Esmaeli Reza Gholi", "Osama Bin laden"};
            for (int i = 0; i < entities.length; i++) {
                list.getEntry().get(0).setName(entities[i]);
                list.getEntry().get(0).setFirstName(null);
                listExplorerControl.create_entry_with_details_info(driver, list.getEntry().get(0));
            }

            FileScan fileScan = commonTestMethods.getFileScanData(GeneralConstants.BULK_ASSIGN_FILE_PATH);
            fileScan.setFormat("Generic Text");
            String detectionID = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            Assert.assertEquals(detectionManagerControl.search_by_id(driver, detectionID), "REPNEW", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            String expectedResults = "1 detection(s) successfully modified!";
            Assert.assertEquals(detectionManagerControl.block_detection_4_eyes(driver, detectionID, "Test Message", checker_02), expectedResults, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            String NewInvestigator = "4eyes03 / 4eyes03, 4eyes03";
            String NewAssignments = "1";


            Assert.assertEquals(detectionManagerControl.bulk_assignment_of_detections(driver, NewInvestigator, NewAssignments)
                    , "0"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            Assert.assertEquals(detectionEditor.get_investigator(driver), NewInvestigator, GeneralConstants.POM_EXCEPTION_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
