package eastnets.screening.regression.bugs.restartServicesRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import eastnets.admin.entity.Common;
import eastnets.admin.entity.Operator;
import eastnets.common.control.Application;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.AdvancedSettingsControls.EngineTuningControl;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.EngineTuning;
import io.qameta.allure.Allure;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.Step;
import io.qameta.allure.testng.AllureTestNg;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Listeners;
import org.testng.annotations.Test;

@Listeners({AllureTestNg.class})
public class Tkt91551Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final EngineTuningControl engineTuningControl = new EngineTuningControl();
    private final NameScanControl nameScanControl = new NameScanControl();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true, groups = "regression")
    @Step("Login to filtering.")
    public void login() {
        try {
            Operator operator = Common.OPERATOR.FULL_RIGHT_1.getOperator();
            driver.navigate().refresh();
            loginPage.login(driver
                    , operator.getLoginName()
                    , operator.getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This Test Method is for Tkt 91551
     * Steps:
     * 1. Navigate to Advanced settings then to Engine Tuning.
     * 2. Add a new Bank word with Category Company type.
     * 3. Create a new list and add bank to it.
     * 4. Scan the added bank and check the rank.
     */
    @Test()
    @Issue("91551")
    @Tag("Regression")
    @Owner("Mohamed Hammed")
    public void bug_91551() {
        try {

            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Issue 91551: Neutral words default type"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Issue 91551: Neutral words default type"));

            EnList list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            list.getEntry().get(0).setName("TINXKOFF BANK");
            list.getEntry().get(0).setFirstName(null);
            list.getEntry().get(0).setType("Group");
            commonTestMethods.createListSet(driver, list, Common.PROFILE.FULL_RIGHT_001.getProfile().getName());
            commonTestMethods.createNewEntryWithTypeAndNames(driver, list);

            EngineTuning engineTuning = new EngineTuning();
            engineTuning.setWord("Bank");
            engineTuning.setCategory("Company Type");
            String validationMessage = engineTuningControl.addNewNeutralWord(driver, engineTuning);
            System.out.println(validationMessage);
            Assert.assertTrue(validationMessage.equalsIgnoreCase("Please keep in mind that changing the " +
                            "engine settings may drastically change the scanning results. Note that some of the changed " +
                            "parameters require the SafeWatch Screening solution to be restarted.")
                            || validationMessage.equalsIgnoreCase("Neutral word name already exists!")
                    , "The validation message is not as expected");
            commonTestMethods.restartNewArch();


            nameScanControl.scan_Name(driver
                    , "TINXKOFF"
                    , Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName()
                    , "75"
                    , true
                    , true
                    , true
                    , false);
            String rank = nameScanControl.get_detection_rank(driver);
            Assert.assertTrue(Integer.parseInt(rank) > 85, "The ranks is low");


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

}
