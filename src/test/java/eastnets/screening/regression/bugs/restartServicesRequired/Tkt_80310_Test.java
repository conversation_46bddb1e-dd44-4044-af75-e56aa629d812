package eastnets.screening.regression.bugs.restartServicesRequired;


import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.control.listManager.ListSetControl;
import io.qameta.allure.Allure;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

public class Tkt_80310_Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final ListSetControl listSetControl = new ListSetControl();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This test method is related to bug number = 80310
     * Steps:
     * 1- Update TConfiguration Table by the below data:
     * **********  APPLICATION | SECTION_NAME | VARIABLE_NAME   | VARIABLE_VALUE  **********
     * **********  General     |SearchOnLoad  |detectionManager |true             **********
     * **********  General     |SearchOnLoad  | listSetManager  |true             **********
     * 2- In UI go to list manager and make sure that table rows appear without click on search.
     * 3- In UI go to detection manager and make sure that table rows appear without click on search.
     */
    @Test(enabled = true)
    @Owner("Sarah Abdellatif")
    @Issue("80310")
    @Tag("Regression Test")
    public void tc_80310() {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Issue 80310: Screening: Regression - No results without clicking on search (SEB)"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Issue 80310: Screening: Regression - No results without clicking on search (SEB)"));

            screeningServicesDelegate.excuteQuerSFP(GeneralConstants.DB_QUERY[46]);
            screeningServicesDelegate.excuteQuerSFP(GeneralConstants.DB_QUERY[47]);

            commonTestMethods.restartTomcat();


            driver.navigate().refresh();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_7.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_7.getOperator().getPassword()
            );

            Assert.assertTrue(listSetControl.verify_table_rows_appear(driver)
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


            Assert.assertTrue(detectionManagerControl.verify_table_rows_appear(driver)
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
