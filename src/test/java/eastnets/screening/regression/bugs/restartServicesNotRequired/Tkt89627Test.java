package eastnets.screening.regression.bugs.restartServicesNotRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.admin.entity.Operator;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.ISO20022Control;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FileScan;
import eastnets.screening.entity.ISO20022FormatConfiguration;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class Tkt89627Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ISO20022Control iso20022Control = new ISO20022Control();
    private final ResultManagerControl resultManagerControl = new ResultManagerControl();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();

    private RemoteWebDriver driver;

    Operator operator = Common.OPERATOR.FULL_RIGHT_1.getOperator();
    EnList list;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true, groups = "regression")
    @Step("Login to filtering.")
    public void login() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver
                    , operator.getLoginName()
                    , operator.getPassword()
                   );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This Test Method is for Bug 89627
     * Steps:
     * 1. Navigate to Format manager.
     * 2. Create a new group and configure it.
     * 3. Import the format in the ticket.
     * 4. Create a new List Set and black list and set the ISO group the created group.
     * 5. Add Osama Bin Laden to the List.
     * 6. Scan the Message in the ticket and go to Results tab.
     * 7. Check the result is reported.
     */

    @Test(groups = {"regression"})
    public void bug89627() {
        AllureLifecycle lifecycle = Allure.getLifecycle();
        lifecycle.updateTestCase(testResult -> testResult.setDescription("A crash occurs on Scan Persistence Service when SEPA Amount field is multi occurence"));
        lifecycle.updateTestCase(testResult -> testResult.setName("A crash occurs on Scan Persistence Service when SEPA Amount field is multi occurence"));
        try {
            ISO20022FormatConfiguration formatConfiguration = new ISO20022FormatConfiguration();
            formatConfiguration.setZone(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            formatConfiguration.setGroupName("GroupName" + randomizer.getInt());
            iso20022Control.navigateToISO20022FormatManager(driver);
            iso20022Control.createGroup(driver, formatConfiguration);
            iso20022Control.clickConfigure(driver);
            iso20022Control.importConfigurations(driver, GeneralConstants.TKT89627_Format);
            list = commonTestMethods.getEnListData();
            list.getListSet().setSwiftTemplate(null);
            list.getListSet().setIsoGroup(formatConfiguration.getGroupName());
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, list, Common.PROFILE.FULL_RIGHT_001.getProfile().getName()); //create list
            list.getEntry().get(0).setName("Osama Bin Laden");
            list.getEntry().get(0).setType("Individual");
            list.getEntry().get(0).setFirstName(null);
            commonTestMethods.createNewEntryWithTypeAndNames(driver, list); //add entities
            FileScan message = commonTestMethods.getFileScanData(GeneralConstants.TKT89627_Message
                    , "ISO20022",
                    "All records"
                    , "50");
            commonTestMethods.scanFileAndGetDetectionID(driver, message);
            Assert.assertEquals(resultManagerControl.get_detection_status(driver)
                    , "Reported"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
