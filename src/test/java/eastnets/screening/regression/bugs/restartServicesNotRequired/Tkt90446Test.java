package eastnets.screening.regression.bugs.restartServicesNotRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.FormatManagerControl;
import eastnets.screening.control.listManager.GoodGuyControl;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FileScan;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class Tkt90446Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final FormatManagerControl formatManagerControl = new FormatManagerControl();
    private final GoodGuyControl goodGuyControl = new GoodGuyControl();
    private final ResultManagerControl resultManagerControl = new ResultManagerControl();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    @Step("Login to filtering.")
    public void login() {
        try {

            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 0, enabled = true, groups = {"regression"})
    @Owner("SunilThokala")
    public void tkt_90446() {

        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("verify that no errors in scan service when scanning against GG has the mentioned condition"));
            lifecycle.updateTestCase(testResult -> testResult.setName("verify that no errors in scan service when scanning against GG has the mentioned condition"));


            EnList list = commonTestMethods.getEnListData();

            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            //create a list
            commonTestMethods.createListSet(driver, list, Common.PROFILE.FULL_RIGHT_001.getProfile().getName());
            //entity to added to the list
            list.getEntry().get(0).setName("Diana");
            list.getEntry().get(0).setFirstName(null);
            //add an entity to the list
            commonTestMethods.createNewEntryWithTypeAndNames(driver, list);
            String contextConditionName = "(SW_MATCH_FIELD IN ('DESCRIPTION1','PAYERINSTRUCTION2') AND\n" +
                    "(PAYERNAME LIKE '%DIANA%' OR\n" +
                    "BENEFNAME LIKE '%DIANA%' OR\n" +
                    "ULTIMBENEFNAME LIKE '%DIANA%' OR\n" +
                    "ULTIMPAYERNAME LIKE '%DIANA%')) AND\n" +
                    "SW_MATCH_NAME = 'DIANA'";
            //GoodGuyControl.searchGG(driver , CommonTestMethods.enList);
            Assert.assertEquals(goodGuyControl.add_gg_with_context_condition(driver, list, list.getEntry().get(0).getName(), "50", contextConditionName)
                    , "Good guy successfully created!"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            //Import the format from given location
            formatManagerControl.importFormat(driver, Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName(), GeneralConstants.GG_CONTEXT_CONDITON_FORMAT_PATH);
            //Scan part
            FileScan fileScan = commonTestMethods.getFileScanData(GeneralConstants.GG_CONTEXT_CONDITON_SCANFILE_PATH);
            //Format name
            fileScan.setFormat("SEB");
            //Scan the file
            commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            Assert.assertEquals(resultManagerControl.get_detection_status(driver), "Reported", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
