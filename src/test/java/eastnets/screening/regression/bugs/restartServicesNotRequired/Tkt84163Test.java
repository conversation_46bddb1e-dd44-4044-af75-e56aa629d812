package eastnets.screening.regression.bugs.restartServicesNotRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.ISO20022Control;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FileScan;
import eastnets.screening.entity.ISO20022FormatConfiguration;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class Tkt84163Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ISO20022Control iso20022Control = new ISO20022Control();
    private final ResultManagerControl resultManagerControl = new ResultManagerControl();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(groups = {"regression"})
    public void loginForRegression() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getPassword()
                  );

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This Test Method is for Tkt 84163
     * Steps:
     * 1. Navigate To ISO20022FormatManager
     * 2. Create a new Group.
     * 3. Import the ISO format.
     * 4. Update the format and change the category.
     * 5. Create a list set.
     * 6- Create a blacklist and link it to the list set.
     * 7- Create entities wtih all types and scan each time then check the result
     * 8. Expect Clean when type is Vessel.
     */

    @Test(enabled = true, groups = {"regression"})
    @Owner("Mohamed Hamed")
    public void bug_84163() {
        AllureLifecycle lifecycle = Allure.getLifecycle();
        lifecycle.updateTestCase(testResult -> testResult.setDescription("Change Request 84163: Screening: ISO field category - ALL EXCEPT VESSELS"));
        lifecycle.updateTestCase(testResult -> testResult.setName("Change Request 84163: Screening: ISO field category - ALL EXCEPT VESSELS"));
        try {

            EnList enList = commonTestMethods.getEnListData();
            enList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            ISO20022FormatConfiguration formatConfiguration = new ISO20022FormatConfiguration();
            formatConfiguration.setZone(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            formatConfiguration.setGroupName("GroupName" + randomizer.getInt());
            iso20022Control.navigateToISO20022FormatManager(driver);
            iso20022Control.createGroup(driver, formatConfiguration);
            iso20022Control.clickConfigure(driver);
            iso20022Control.importConfigurations(driver, GeneralConstants.TKT84163_Format);
            Assert.assertEquals(iso20022Control.updateCategory(driver, "pacs.008.001", "/CdtTrfTxInf/Cdtr/Nm", "<All Except Vessels>"),
                    "Field successfully updated!",
                    "Field is not updated");

            enList.getListSet().setIsoGroup(formatConfiguration.getGroupName());
            enList.getListSet().setSwiftTemplate(null);
            enList.getListSet().setRank("50");
            enList.getListSet().setDetectCountriesFlag("true");
            enList.getListSet().setDetectVesselsFlag("true");

            commonTestMethods.createListSet(driver, enList, Common.PROFILE.FULL_RIGHT_001.getProfile().getName());
            String[] types = {"Vessel", "Individual", "Group", "Country", "Unknown", "Goods (DUG)"};
            FileScan message = commonTestMethods.getFileScanData(GeneralConstants.TKT84163_Message
                    , "ISO20022",
                    "All records"
                    , "50");

            for (int i = 0; i < types.length; i++) {
                enList.getEntry().get(0).setName("Osama Bin Laden");
                enList.getEntry().get(0).setType(types[i]);
                enList.getEntry().get(0).setFirstName(null);
                commonTestMethods.createNewEntryWithTypeAndNames(driver, enList);
                commonTestMethods.scanFileAndGetDetectionID(driver, message);
                if (!types[i].equals("Vessel"))
                    Assert.assertEquals(resultManagerControl.get_detection_status(driver)
                            , "Reported"
                            , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
                else
                    Assert.assertEquals(resultManagerControl.get_detection_status(driver)
                            , "Clean"
                            , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


            }
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true, groups = {"regression"})
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
