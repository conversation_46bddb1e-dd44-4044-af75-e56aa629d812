package eastnets.screening.regression.bugs.restartServicesNotRequired;

import core.BaseTest;
import core.ExceptionHandler;
import eastnets.admin.entity.Common;
import eastnets.admin.entity.Operator;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

public class Tkt94952Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final core.util.TextFilesHandler textFilesHandler = new core.util.TextFilesHandler();

    private RemoteWebDriver driver;

    /**
     * This test method is related to bug number = 94952
     * Steps:
     * 1. Create the file.
     * 2. Write a message to the file
     * 3. Login to ALMUI.
     * 4. Check the Legal notice title.
     * 5. Check the Legal notice message.
     */
    Operator operator = Common.OPERATOR.FULL_RIGHT_1.getOperator();

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(groups = {"regression"})
    public void bug94952() {

        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Bug 94952: CR - Display of legal notice on login "));
            lifecycle.updateTestCase(testResult -> testResult.setName("Bug 94952: Screening: CR - Display of legal notice on login "));

            String file_path = System.getenv("EASTNETS_CONFIG_HOME")+"/aml/login-disclaimer-notice.txt";
            textFilesHandler.createFile(file_path);
            textFilesHandler.writeToFile(file_path, "Test by Automation Team");
            loginPage.login(driver
                    , operator.getLoginName()
                    , operator.getPassword()
                   );
            Assert.assertEquals(commonAction.getAlertMessageString(driver), "Legal Notice", "The Legal notice title");
            Assert.assertEquals(commonAction.getLegalNoticeMessageString(driver), "Test by Automation Team", "The Legal notice message is not correct");

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
