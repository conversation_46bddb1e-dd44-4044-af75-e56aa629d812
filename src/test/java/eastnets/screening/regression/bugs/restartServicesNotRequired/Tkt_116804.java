package eastnets.screening.regression.bugs.restartServicesNotRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.Format;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.*;

public class Tkt_116804 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final NameScanControl nameScanControl = new NameScanControl();
    private final core.util.Log log = new core.util.Log();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();

    private RemoteWebDriver driver;

    EnList list = new EnList();
    String matched_entity_name = null;
    @BeforeClass
    public void add_list_set() {
        try {
            driver = getDriver();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_10.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_10.getOperator().getPassword()
            );

            list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_010.getZone().getDisplayName());

            commonTestMethods.createListSet(driver
                    , list
                    , Common.PROFILE.FULL_RIGHT_010.getProfile().getName());

            String filePath = GeneralConstants.TKT116804_ENTITIES_FILE_PATH;
            Format format = eastnets.screening.entity.Common.FORMAT.IMPORT_ENTRY_INDIVIDUAL_FORMAT_2.get();
            format.setName("Format-" + randomizer.getInt());
            format.setZone(list.getZoneName());

            commonTestMethods.importEntries(driver
                    , list
                    , Common.ZONE.COMMON_TEST_ZONE_010.getZone()
                    , filePath
                    , format);
            commonAction.logout(driver);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeClass
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_10.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_10.getOperator().getPassword()
            );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    /**
     * Bug 116804: Behavior of name with spaces
     * Scan name with spaces and check the detection rank
     * Example of names to be tested:
     *                 {Matched Entity, Scaned Name, Rank},
     *                 {"MEDICA SPA", "MEDICA S P A", 99},
     *                 {"M E D I C A S P A", "M E D I C A SPA", 93},
     *                 {"ITI", "I T I", 99},
     *                 {"I T I", "ITI", 100},
     *                 {"CEBORA S P A", "CEBORA SPA", 97},
     *                 {"CEBORA S P A", "CEBORA S.P.A", 97}
     */
    @Test(dataProvider = "listNameWithSpaces")
    @Owner("Sarah Abdellatif")
    @Issue("116804")
    public void tkt_116804(String matchedEntityName, String scannedName, int expectedRank) {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Bug 116804: Behavior of name with spaces"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Bug 116804: Behavior of name with spaces"));
            matched_entity_name = matchedEntityName;

            Assert.assertEquals(nameScanControl.scan_Name(driver
                            ,scannedName
                            ,list.getZoneName()
                            ,"50"
                            ,true
                            ,true
                            ,true
                            ,false)
                    ,"REPNEW"
                    ,"Scan result is not REPNEW");

            String detection_rank = nameScanControl.get_detection_rank_by_matched_entity(driver,scannedName, matchedEntityName);
            log.info("Detection rank: " + detection_rank);
            log.info("Expected rank: " + expectedRank);

            Assert.assertTrue(Integer.parseInt(detection_rank)>expectedRank ||
                            Integer.parseInt(detection_rank)==expectedRank
                    ,"Detection rank is not as expected");



        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterClass
    public void afterMethod() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    /*
     * @return Object[][] - each object contains 3 values:
     * 1. Matched entity name
     * 2. Scanned name
     * 3. Expected rank
     */
    @DataProvider(name = "listNameWithSpaces")
    public Object[][] listNameWithSpaces() {
        return new Object[][]{
                {"MEDICA SPA", "MEDICA S P A", 99},
                {"M E D I C A S P A", "M E D I C A SPA", 93},
                {"ITI", "I T I", 99},
                {"I T I", "ITI", 100},
                {"CEBORA S P A", "CEBORA SPA", 97},
                {"CEBORA S P A", "CEBORA S.P.A", 97}
        };
    }

}