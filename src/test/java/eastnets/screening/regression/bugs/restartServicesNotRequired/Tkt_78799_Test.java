package eastnets.screening.regression.bugs.restartServicesNotRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.util.HashMap;

public class Tkt_78799_Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final NameScanControl nameScanControl = new NameScanControl();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();

    private RemoteWebDriver driver;

    HashMap<String, String> detection = new HashMap<>();

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_7.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_7.getOperator().getPassword()
            );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @BeforeClass(alwaysRun = true)
    public void create_required_data() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_7.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_7.getOperator().getPassword()
            );

            EnList blackList = commonTestMethods.getEnListData();
            blackList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_007.getZone().getDisplayName());

            commonTestMethods.createListSet(driver, blackList, Common.PROFILE.FULL_RIGHT_007.getProfile().getName());

            blackList.getEntry().get(0).setName("New_Entry");
            blackList.getEntry().get(0).setFirstName(null);
            commonTestMethods.createNewEntryWithTypeAndNames(driver, blackList);
            nameScanControl.scan_Name(driver
                    , "New_Entry"
                    , blackList.getZoneName()
                    , "100"
                    , true
                    , true
                    , true
                    , false);
            String detection_id = nameScanControl.get_detection_id(driver);
            detection.put("new", detection_id);

            blackList.getEntry().get(0).setName("Pending_Entry");
            commonTestMethods.createNewEntryWithTypeAndNames(driver, blackList);
            nameScanControl.scan_Name(driver
                    , "Pending_Entry"
                    , blackList.getZoneName()
                    , "100"
                    , true
                    , true
                    , true
                    , false);
            detection_id = nameScanControl.get_detection_id(driver);
            Assert.assertEquals(detectionManagerControl.pending_detection(driver, detection_id, "pending")
                    , "1 detection(s) successfully modified!"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            detection.put("pending", detection_id);

            blackList.getEntry().get(0).setName("Real_Violation_Entry");
            commonTestMethods.createNewEntryWithTypeAndNames(driver, blackList);
            nameScanControl.scan_Name(driver
                    , "Real_Violation_Entry"
                    , blackList.getZoneName()
                    , "100"
                    , true
                    , true
                    , true
                    , false);
            detection_id = nameScanControl.get_detection_id(driver);
            Assert.assertEquals(detectionManagerControl.block_detection(driver, detection_id, "block")
                    , "1 detection(s) successfully modified!"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            detection.put("real violation", detection_id);

            blackList.getEntry().get(0).setName("False_Positive_Entry");
            commonTestMethods.createNewEntryWithTypeAndNames(driver, blackList);
            nameScanControl.scan_Name(driver
                    , "False_Positive_Entry"
                    , blackList.getZoneName()
                    , "100"
                    , true
                    , true
                    , true
                    , false);
            detection_id = nameScanControl.get_detection_id(driver);
            Assert.assertEquals(detectionManagerControl.release_detection(driver, detection_id, "release")
                    , "1 detection(s) successfully modified!"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            detection.put("false positive", detection_id);

            blackList.getEntry().get(0).setName("Dont_Know_Entry");
            commonTestMethods.createNewEntryWithTypeAndNames(driver, blackList);
            nameScanControl.scan_Name(driver
                    , "Dont_Know_Entry"
                    , blackList.getZoneName()
                    , "100"
                    , true
                    , true
                    , true
                    , false);
            detection_id = nameScanControl.get_detection_id(driver);
            Assert.assertEquals(detectionManagerControl.perform_dont_Know(driver, detection_id, "dont know")
                    , "1 detection(s) successfully modified!"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            detection.put("dont know", detection_id);

            driver.navigate().refresh();
            detectionManagerControl.Customize_search_columns(driver, "Contains Opened Alert");

            commonAction.logout(driver);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This test method is related to bug number = 78799
     * Steps:
     * 1- Search by contains open alerts = true
     * 2- Check that new and pending detections appears
     * 3- check that real-violation, false-positive and don't know didn't appear
     */
    @Owner("Sarah Abdellatif")
    @Issue("78799")
    @Tag("Regression Test")
    @Test(enabled = true)
    public void tc_78799_enable_contains_open_alert_option() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription(" verify that only open alerts return when searching for detections while this option is checked (open alerts: pending, new, investigate)"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Issue 78799: Screening: Search criteria 'Contains opened alerts' missing"));

            detectionManagerControl.search_by_contains_open_alerts(driver, true);
            Assert.assertTrue(detectionManagerControl.verify_detection_id_exist(driver, detection.get("new")), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertTrue(detectionManagerControl.verify_detection_id_exist(driver, detection.get("pending")), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertFalse(detectionManagerControl.verify_detection_id_exist(driver, detection.get("real violation")), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertFalse(detectionManagerControl.verify_detection_id_exist(driver, detection.get("false positive")), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertFalse(detectionManagerControl.verify_detection_id_exist(driver, detection.get("dont know")), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    /**
     * This test method is related to bug number = 78799
     * Steps:
     * 1- Search by contains open alerts = false
     * 2- Check that real-violation, false-positive, don't know, new and pending detections appears
     */
    @Test(enabled = true)
    @Owner("Sarah Abdellatif")
    @Issue("78799")
    @Tag("Regression Test")
    public void tc_78799_disable_contains_open_alert_option() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("verify closed + open alerts return when search f" +
                    "or detections while this option is unchecked " +
                    "(closed alerts: false positive, real violations, don't know, violation (alerts not created. open alerts: pending, new, investigate) "));
            lifecycle.updateTestCase(testResult -> testResult.setName("Issue 78799: Screening: Search criteria 'Contains opened alerts' missing"));

            detectionManagerControl.search_by_contains_open_alerts(driver, false);
            Assert.assertTrue(detectionManagerControl.verify_detection_id_exist(driver, detection.get("new")), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertTrue(detectionManagerControl.verify_detection_id_exist(driver, detection.get("pending")), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertTrue(detectionManagerControl.verify_detection_id_exist(driver, detection.get("real violation")), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertTrue(detectionManagerControl.verify_detection_id_exist(driver, detection.get("false positive")), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertTrue(detectionManagerControl.verify_detection_id_exist(driver, detection.get("dont know")), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
