package eastnets.screening.regression.bugs.restartServicesNotRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.control.FormatManagerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FileScan;
import eastnets.screening.entity.Format;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.util.ArrayList;

public class Tkt_93989_Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final FormatManagerControl formatManagerControl = new FormatManagerControl();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This test method is related to bug number = 93989
     * Steps:
     * 1. Login to AMLUI
     * 2. Navigate to format manager and import Format
     * 3. Go to Scan manager then File scan and do scan with the attached txt file
     * 4. Check the Structured record tab >> fields are displayed
     * 5. Then go to format manager and delete the added format
     * 6. Navigate to Detection manager
     * 7. Check structured record tab for the previous detection
     */
    @Test(enabled = true)
    @Owner("Sarah Abdellatif")
    @Issue("93989")
    @Tag("Regression Test")
    public void bug_93989() {
        try {

            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Bug 93989: Detection manager - Data in Structured record tab disappeared after deleting the used Custom format from format manager."));
            lifecycle.updateTestCase(testResult -> testResult.setName("Bug 93989: Detection manager - Data in Structured record tab disappeared after deleting the used Custom format from format manager."));

            EnList blackList = commonTestMethods.getEnListData();
            blackList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            String profileName = Common.PROFILE.FULL_RIGHT_001.getProfile().getName();
            commonTestMethods.createListSet(driver, blackList, profileName);

            blackList.getEntry().get(0).setName("Osama bin laden");
            blackList.getEntry().get(0).setFirstName(null);
            commonTestMethods.createNewEntryWithTypeAndNames(driver, blackList);

            String file_path = GeneralConstants.TKT93989_FORMAT_FILE_PATH;
            Format format = new Format();
            format.setZone(blackList.getZoneName());
            format.setName("BNP_Circumflex1");
            formatManagerControl.importFormat(driver, format.getZone(), file_path);


            FileScan file_scan = commonTestMethods.getFileScanData(GeneralConstants.TKT93989_SCAN_FILE_PATH);
            file_scan.setFormat(format.getName());

            String detection_id = commonTestMethods.scanFileAndGetDetectionID(driver, file_scan);

            ArrayList<ArrayList<String>> structuredRecords = detectionManagerControl.get_structured_record_data(driver, detection_id);
            Assert.assertTrue(structuredRecords.size() != 0, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


            formatManagerControl.deleteFormat(driver, format);


            structuredRecords = detectionManagerControl.get_structured_record_data(driver, detection_id);
            Assert.assertTrue(structuredRecords.size() != 0, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
