package eastnets.screening.regression.bugs.restartServicesNotRequired;

import core.BaseTest;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.control.GroupControl;
import eastnets.admin.control.OperatorControl;
import eastnets.admin.entity.Common;
import eastnets.admin.entity.Operator;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class Tkt77840Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final OperatorControl operatorControl = new OperatorControl();
    private final GroupControl groupControl = new GroupControl();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true, groups = "regression")
    public void login() {
        try {
            loginPage.login(driver
                    , screeningGeneralConfigsProps.getProperty(GeneralConstants.VALID_ADMIN_MAIL)
                    , screeningGeneralConfigsProps.getProperty(GeneralConstants.VALID_ADMIN_PASSWORD)
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This Test Method is for bug 77840
     * Steps:
     * 1. Login to ALMUI with Admin user.
     * 2. Create a new operator with random name.
     * 3. Add the new operator to the admin group.
     * 4. Log out.
     * 5. Login with the new operator.
     * 6. Create a new operator with random name.
     * 7. Add the new operator to the full right group.
     * 8. Log out.
     * 9. Log in with the new operator.
     */
    @Test(enabled = true)
    @Owner("Mohamed Hammed")
    @Issue("77840")
    @Tag("Regression Test")
    public void bug77840() {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Bug 77840: Group turn to disable once we assign new user in"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Bug 77840: Group turn to disable once we assign new user in"));

            int randomNumber = randomizer.getInt();
            String username = "selenium-user-" + randomNumber;
            Operator operator = new Operator(username,
                    "Random",
                    "User",
                    "hello00", Common.ZONE.SYSTEM_ZONE.getZone());
            Assert.assertTrue(operatorControl.createOperator(driver, operator),
                    String.format("Can't create operator %s", operator.getLoginName()));
            groupControl.add_operator_to_group(driver, Common.GROUP.ADMINISTRATOR_GROUP.get(), operator.getLoginName());

            commonAction.logout(driver);
            loginPage.login(driver
                    , username
                    , "hello00"
                   );

            Assert.assertTrue(commonAction.isUserLogged(driver), String.format("Login operator %s failed", operator.getLoginName()));
            username = "selenium-user-1" + randomNumber;
            operator = new Operator(username,
                    "Random",
                    "User",
                    "hello00", Common.OPERATOR.FULL_RIGHT_1.getOperator().getZone());
            Assert.assertTrue(operatorControl.createOperator(driver, operator),
                    String.format("Can't create operator %s", operator.getLoginName()));
            groupControl.add_operator_to_group(driver, Common.GROUP.FULL_RIGHT_GROUP_01.get(), operator.getLoginName());

            commonAction.logout(driver);
            loginPage.login(driver
                    , username
                    , "hello00"
                   );
            Assert.assertTrue(commonAction.isUserLogged(driver), String.format("Login operator %s failed", operator.getLoginName()));
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }

    }


    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
