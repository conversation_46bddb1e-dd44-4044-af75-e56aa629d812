package eastnets.screening.regression.bugs.restartServicesNotRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class Tkt96083Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final NameScanControl nameScanControl = new NameScanControl();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(groups = {"regression"})
    public void loginForRegression() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getPassword()
                    );

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test()
    @Owner("Mohamed Hamed")
    @Issue("96083")
    @Tag("Regression")
    public void bug96083()  {
        try{
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Bug 93629: Repeat: error when processing MT103 REMIT"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Bug 93629: Repeat: error when processing MT103 REMIT"));
            EnList list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, list,Common.PROFILE.FULL_RIGHT_001.getProfile().getName());
            list.getEntry().get(0).setName("Osama Bin laden");
            list.getEntry().get(0).setFirstName(null);
            commonTestMethods.createNewEntryWithDetailsInfo(driver, list,list.getEntry().get(0));
            Assert.assertEquals(nameScanControl.scan_Name(driver
                    ,"Osama Bin laden"
                    ,Common.ZONE.APPROVAL_ZONE.getZone().getDisplayName()
                    ,"75"
                    ,true
                    ,true
                    ,true
                    ,false), "REPNEW", "Status is not correct");
            String detectionID = nameScanControl.get_detection_id(driver);
            detectionManagerControl.add_comment_to_alert(driver,detectionID,"<Test>");
            Assert.assertTrue(detectionManagerControl.verify_comment_exist(driver,detectionID, "<Test>"), "Comment does not exist");

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }

    }
    @AfterMethod(alwaysRun = true)
    public void logout()
    {
        try
        {
            commonAction.logout(driver);
        }
        catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
