package eastnets.screening.regression.bugs.restartServicesNotRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.common.gui.Navigation;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class Tkt91459Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final NameScanControl nameScanControl = new NameScanControl();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This test method is related to bug number = 91459
     * Steps:
     * 1- Verify adding comments on Block alerts in detection manager
     * 2- Verify adding comments on Release alerts in detection manager
     * 3- Verify adding comments on Pending alerts in detection manager
     */

    @Test(priority = 0, enabled = true)
    @Owner("SunilThokala")
    @Tag("Regression Test")
    @Issue("91459")
    public void tkt_91459() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Issue 91459: Screening: Regression - Comments disabled for closed alerts"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Issue 91459: Screening: Regression - Comments disabled for closed alerts"));

            EnList list;
            list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            list.getEntry().get(0).setName("Osama Bin Laden");
            list.getEntry().get(0).setFirstName(null);
            commonTestMethods.createListSet(driver, list, Common.PROFILE.FULL_RIGHT_001.getProfile().getName());
            //create list
            commonTestMethods.createNewEntryWithTypeAndNames(driver, list); //add entities
            Assert.assertEquals(nameScanControl.scan_Name(driver, "Osama Bin Landen", Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName(), "50",
                            true, true, true, false), "REPNEW",
                    "Detection status is not correct");
            String detectionID = nameScanControl.get_detection_id(driver);
            Navigation.DETECTION_MANAGER.navigate(driver);
            Assert.assertEquals(detectionManagerControl.block_detection(driver, detectionID, "Real Violations"), "1 detection(s) successfully modified!", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            String comment = "Add comment to blocked detection." + randomizer.getInt();
            Assert.assertEquals(detectionManagerControl.add_comment_to_alert(driver, detectionID, comment)
                    , "1 detection(s) and 1 alert(s) successfully modified!"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(nameScanControl.scan_Name(driver, "Osama Bin", Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName(), "50",
                            true, true, true, false), "REPNEW",
                    "Detection status is not correct");
            detectionID = nameScanControl.get_detection_id(driver);
            Navigation.DETECTION_MANAGER.navigate(driver);
            Assert.assertEquals(detectionManagerControl.release_detection_4_eyes(driver, detectionID, "False Positive", "")
                    , "1 detection(s) successfully modified!"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            comment = "Add comment to released detection." + randomizer.getInt();
            Assert.assertEquals(detectionManagerControl.add_comment_to_alert(driver, detectionID, comment)
                    , "1 detection(s) and 1 alert(s) successfully modified!"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}