package eastnets.screening.regression.bugs.restartServicesNotRequired;

import core.BaseTest;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import core.constants.screening.ISOConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.ISO20022Control;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;


public class Tkt94486Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final ISO20022Control iso20022Control = new ISO20022Control();
    private final core.util.Property property = new core.util.Property();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_3.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_3.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This test method is related to bug number = 94486
     * Steps:
     * 1- Login to SWF
     * 2- Navigate to ISO20022 Configuration Module
     * 3- Click "Browse" and upload the attached file
     * 4- Note that "Unexpected processing error" appears.
     */

    @Test(priority = 0, enabled = true)
    @Owner("SunilThokala")
    @Tag("Regression Test")
    @Issue("94486")
    public void tkt_94486() {
        try {

            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Bug 94486: Filtering: ISO20022 Configuration - 'Unexpected processing error' appears when uploading invalid schema"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Bug 94486: Filtering: ISO20022 Configuration - 'Unexpected processing error' appears when uploading invalid schema"));


            String fileName = ISOConstants.ISO_VALID_MESSAGEFORMAT_FILENAME;
            String fileName1 = ISOConstants.ISO_INVALID_MESSAGEFORMAT_FILENAME;


            String filePath = System.getProperty("user.dir")
                    + property.fromFile(GeneralConstants.GENERAL_CONFIG_FILE_NAME).getProperty(GeneralConstants.DEFAULT_UPLOAD_PATH)
                    + fileName;
            Allure.step("Schema File Path = " + filePath);
            iso20022Control.navigateToISO20022SchemaManager(driver);
            Assert.assertEquals(iso20022Control.importSchema(driver, fileName1), "Invalid File Type."
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while import schema");
            iso20022Control.deleteSchema(driver, "pacs.008.001");
            Assert.assertEquals(iso20022Control.importSchema(driver, fileName)
                    , "Successfully import schema [pacs.008.001] with version [04]"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while import schema");
            Assert.assertEquals(iso20022Control.deleteSchema(driver, "pacs.008.001")
                    , "Schema version [pacs.008.001.04] successfully deleted"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while import schema");

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


}

