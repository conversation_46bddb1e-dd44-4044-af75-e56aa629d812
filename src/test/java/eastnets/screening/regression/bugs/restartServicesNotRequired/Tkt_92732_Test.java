package eastnets.screening.regression.bugs.restartServicesNotRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.FormatManagerControl;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FileScan;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.util.ArrayList;

public class Tkt_92732_Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final FormatManagerControl formatManagerControl = new FormatManagerControl();
    private final ResultManagerControl resultManagerControl = new ResultManagerControl();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getPassword()
            );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This test method is related to bug number = 92732
     * Steps:
     * 1- Insert new parameter ("CSV_QUOTE") in tConfiguration Table in DB
     * 2- Create new list set and blacklist
     * 3- Create new Entry = Osama Bin Laden
     * 4- Import (BNP_test) format
     * 5- Scan File
     * 6- Assert that there is 2 detection returned
     */
    @Test(enabled = true)
    @Owner("Sarah Abdellatif")
    @Issue("92732")
    @Tag("Regression Test")
    public void bug_92732() {
        try {

            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Bug 92732: Screening 5.x: No violation is generated when single quote is placed before the name of a field for custom format"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Bug 92732: Screening 5.x: No violation is generated when single quote is placed before the name of a field for custom format"));

            screeningServicesDelegate.excuteQuerSFP(GeneralConstants.DB_QUERY[42]);

            EnList blackList = commonTestMethods.getEnListData();
            blackList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            String profileName = Common.PROFILE.FULL_RIGHT_001.getProfile().getName();
            commonTestMethods.createListSet(driver, blackList, profileName);

            blackList.getEntry().get(0).setName("Osama Bin Laden");
            blackList.getEntry().get(0).setFirstName(null);
            commonTestMethods.createNewEntryWithTypeAndNames(driver, blackList);

            String filePath = GeneralConstants.Format_92732;
            String zone = Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName();
            formatManagerControl.importFormat(driver, zone, filePath);

            FileScan fileScan = commonTestMethods.getFileScanData(GeneralConstants.FILE_SCAN_92732);
            fileScan.setFormat("BNP_test");

            commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            ArrayList<String> status_List = resultManagerControl.get_status_list(driver);
            Assert.assertEquals(status_List.size()
                    , 2
                    , String.format("Returned detection from saning file must be 2 but its %s", status_List.size()));

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
