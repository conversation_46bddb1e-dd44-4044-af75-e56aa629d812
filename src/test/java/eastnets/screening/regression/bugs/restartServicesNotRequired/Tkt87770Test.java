package eastnets.screening.regression.bugs.restartServicesNotRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.backendServices.AdminServicesDelegate;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.SwiftManagerControl;
import eastnets.screening.control.listManager.BlackListControl;
import eastnets.screening.control.listManager.DowJonesControl;
import eastnets.screening.control.listManager.ListExplorerControl;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class Tkt87770Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final AdminServicesDelegate adminServicesDelegate = new AdminServicesDelegate();
    private final SwiftManagerControl swiftManagerControl = new SwiftManagerControl();
    private final BlackListControl blackListControl = new BlackListControl();
    private final DowJonesControl dowJonesControl = new DowJonesControl();
    private final ListExplorerControl listExplorerControl = new ListExplorerControl();

    private RemoteWebDriver driver;

    String userName = Common.OPERATOR.FULL_RIGHT_4.getOperator().getLoginName();
    String password = Common.OPERATOR.FULL_RIGHT_4.getOperator().getPassword();

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    @Step("Login to filtering.")
    public void login() {
        try {
            loginPage.login(driver
                    , userName
                    , password
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 0, enabled = true, groups = {"regression"})
    @Owner("Sarah Abdellatif")
    public void tkt_87770_Test() {
        try {


            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Issue 87770: Screening: Dow Jones - Maiden names no longer loaded"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Issue 87770: Screening: Dow Jones - Maiden names no longer loaded"));
            String listName = "DowJones-13748";

            EnList list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_004.getZone().getDisplayName());

            Assert.assertTrue(swiftManagerControl.CreateSwiftTemplate(driver, list.getListSet().getSwiftTemplate(), list.getZoneName())
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while create new swift template.");

            Assert.assertTrue(blackListControl.create_Black_List(driver, list)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new black list.");


            Assert.assertTrue(dowJonesControl.create_dj_list(driver
                    , list
                    , "Lists (including Sanctions) - By Sanction References"
                    , false), GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new Dow-Jones list.");

            Assert.assertEquals(dowJonesControl.enable_dj_list(driver), list.getExpectedMessage(), GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new Dow-Jones list.");


            String zoneID = adminServicesDelegate.getZoneId(list.getZoneName());
            Assert.assertTrue(dowJonesControl.create_dj_files(list, true, false, userName,
                    password,
                    zoneID, listName, false).contains("INFO  - Bye bye!"), GeneralConstants.POM_EXCEPTION_ERR_MSG);

            dowJonesControl.add_sanction_list(driver, list);

            Assert.assertTrue(dowJonesControl.load_dj_list_load_referential_only(list, false, false, listName, false).contains("Process successfully finished!"), GeneralConstants.POM_EXCEPTION_ERR_MSG);

            commonTestMethods.createListSetOnly(driver, list, Common.PROFILE.FULL_RIGHT_004.getProfile().getName());

            Assert.assertTrue(listExplorerControl.is_entry_exist(driver, list.getZoneName(), "Tilfah, Sajida Khayrallah"),
                    GeneralConstants.POM_EXCEPTION_ERR_MSG + " while getting rows count.");


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

}
