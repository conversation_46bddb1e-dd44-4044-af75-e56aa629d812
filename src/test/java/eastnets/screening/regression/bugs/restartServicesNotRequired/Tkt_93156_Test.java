package eastnets.screening.regression.bugs.restartServicesNotRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.backendServices.AdminServicesDelegate;
import eastnets.admin.entity.Common;
import eastnets.admin.entity.Operator;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.listManager.ListSetControl;
import eastnets.screening.control.listManager.WorldCheckControl;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class Tkt_93156_Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ListSetControl listSetControl = new ListSetControl();
    private final WorldCheckControl worldCheckControl = new WorldCheckControl();
    private final NameScanControl nameScanControl = new NameScanControl();
    private final AdminServicesDelegate adminServicesDelegate = new AdminServicesDelegate();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();

    private RemoteWebDriver driver;

    Operator operator = Common.OPERATOR.FULL_RIGHT_5.getOperator();

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(driver
                    , operator.getLoginName()
                    , operator.getPassword()
            );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This test method is related to bug number = 93156
     * Steps:
     * 1. Create WC list and Enable It.
     * 2. On server Load List References then select categories from UI.
     * 3. On server load WC List --> file: original.csv .
     * 4. Create list Set then Link the WC list to a list set.
     * 5. Set the violation filter on WC list : SW_MATCH_NAME ='ABDAOUI, Youssef'
     * 6. Scan On Name Checker "ABDAOUI, Youssef" , Reported violation generated ( VF worked).
     * 7. Change on the "Official name" of the entity to be "ABDAOUI, Youssff" , save the edition in a file , and load it in a new version , using "New version on update".
     * 8. Now Scan "ABDAOUI, Youssff" , the result is external should be raised in this case ( as the VF not matched) .
     */

    @Test(enabled = true)
    @Owner("Sarah Abdellatif")
    @Issue("93156")
    @Tag("Regression Test")
    public void bug_93156() {
        try {

            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Bug 93156: SWS 5.1.3: Violation filter not working on WC list when using 'New version on update'"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Bug 93156: SWS 5.1.3: Violation filter not working on WC list when using 'New version on update'"));


            EnList blackList = commonTestMethods.getEnListData();
            blackList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_005.getZone().getDisplayName());
            blackList.setOfficialDate(randomizer.yesterday("yyyy/MM/dd"));
            String profileName = Common.PROFILE.FULL_RIGHT_005.getProfile().getName();

            commonTestMethods.createListSet(driver, blackList, profileName);

            String worldCheckName = "WCheck-" + randomizer.timestamp1;
            Assert.assertTrue(worldCheckControl.createNewWorldCheck(driver
                            , blackList.getZoneName()
                            , worldCheckName
                            , blackList.getName()
                            , "Sanction"
                            , true
                            , false)
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


            String zoneID = adminServicesDelegate.getZoneId(blackList.getZoneName());
            Assert.assertTrue(worldCheckControl.create_wc_files_and_run_cmd(GeneralConstants.WORLD_CHECK_COMMANDS[1]
                    , operator.getLoginName()
                    , operator.getPassword()
                    , zoneID
                    , "93156-original"
                    , worldCheckName
                    , ""
                    , "").contains("[World-Check] 1 records matched the filters"), GeneralConstants.POM_EXCEPTION_ERR_MSG);

            String violationFilter = "SW_MATCH_NAME ='ABDAOUI, Youssef'";
            Assert.assertTrue(listSetControl.add_post_violation_filter(driver
                                    , blackList.getZoneName()
                                    , blackList.getListSet().getName()
                                    , blackList.getName(), violationFilter)
                            .contains("Syntax is valid!")
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            Assert.assertEquals(listSetControl.save_violation_filter(driver, true)
                    , "Operation completed successfully."
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            Assert.assertEquals(nameScanControl.scan_Name(driver
                            , "ABDAOUI, Youssef"
                            , blackList.getZoneName()
                            , null
                            , false
                            , false,
                            false
                            , false)
                    , "REP"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            worldCheckControl.enable_new_version_on_update_editing(driver, blackList.getZoneName(), worldCheckName);

            Assert.assertTrue(worldCheckControl.create_wc_files_and_run_cmd(GeneralConstants.WORLD_CHECK_COMMANDS[6]
                    , operator.getLoginName()
                    , operator.getPassword()
                    , zoneID
                    , "93156-modified"
                    , worldCheckName
                    , ""
                    , "").contains("[World-Check] 1 records matched the filters"), GeneralConstants.POM_EXCEPTION_ERR_MSG);

            Assert.assertEquals(nameScanControl.scan_Name(driver
                            , "ABDAOUI, Youssff"
                            , blackList.getZoneName()
                            , null
                            , false
                            , false,
                            false
                            , false)
                    , "EXT"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

}
