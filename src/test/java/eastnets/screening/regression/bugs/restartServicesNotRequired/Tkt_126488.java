package eastnets.screening.regression.bugs.restartServicesNotRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FileScan;
import eastnets.screening.entity.Format;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;

import java.util.HashMap;

public class Tkt_126488 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ResultManagerControl resultManagerControl = new ResultManagerControl();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_10.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_10.getOperator().getPassword()
            );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {}.getClass().getName()
                    , new Object() {}.getClass().getEnclosingMethod().getName());
        }
    }


    /**
     * Test Case 126488: Verify that scanning text with spaced letters
     *                  " S A D D A M H U S S E I N A L - T I K R I T I" vs "AL-TIKRITI, Saddam Hussein"
     *                  generates high rank (97) when scanned in Generic Text or RJE Formats
     * Steps:
     * 1. Login to SWS with valid credentials
     * 2. Click on "List Management" Module Manager
     * 3. Click "Import"
     * 4. Click "Browse" and locate attached file
     * 5. Select Zone and click on "Import" button
     * 6. Click on "Scan Manager"
     * 7. Click on "File Scan" tab
     * 8. Click "Browse" and locate attached text file to be scanned
     * 9. Select Format as "Generic Text" and click "Scan" button
     * 10. Go to Results tab -> Scan status will be displayed as "Succeeded"
     * 11. Click on the scanned record
     * 12. Click on Detection -> Matches will be displayed and a match is  generated
     *                  on "AL-TIKRITI, Saddam Hussein" with high rank (97)
     */
    @Test
    @Issue("126488")
    @Owner("Sarah Abdellatif")
    public void tkt_126488() {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription(" Test Case 126488: Verify that scanning text with spaced letters \n" +
                    "     *                  \" S A D D A M H U S S E I N A L - T I K R I T I\" vs \"AL-TIKRITI, Saddam Hussein\"\n" +
                    "     *                  generates high rank (97) when scanned in Generic Text or RJE Formats"));
            lifecycle.updateTestCase(testResult -> testResult.setName(" Test Case 126488: Verify that scanning text with spaced letters \n" +
                    "     *                  \" S A D D A M H U S S E I N A L - T I K R I T I\" vs \"AL-TIKRITI, Saddam Hussein\"\n" +
                    "     *                  generates high rank (97) when scanned in Generic Text or RJE Formats"));


            EnList list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_010.getZone().getDisplayName());

            commonTestMethods.createListSet(driver
                    , list
                    , Common.PROFILE.FULL_RIGHT_010.getProfile().getName());

            String filePath = GeneralConstants.TKT126488_ENTITIES_FILE_PATH;
            Format format = eastnets.screening.entity.Common.FORMAT.IMPORT_ENTRY_INDIVIDUAL_FORMAT_2.get();
            format.setName("Format-" + randomizer.getInt());
            format.setZone(list.getZoneName());

            commonTestMethods.importEntries(driver
                    , list
                    , Common.ZONE.COMMON_TEST_ZONE_010.getZone()
                    , filePath
                    , format);


            FileScan fileScan = commonTestMethods.getFileScanData(GeneralConstants.TKT126488_SCAN_FILE_PATH
                    , "Generic Text"
                    , null
                    , "50");
            fileScan.setResult("All records");

            commonTestMethods.scanFile(driver, fileScan);

            HashMap<String, String> detection_details = resultManagerControl.get_scanned_record_and_rank(driver);


            SoftAssert softAssert = new SoftAssert();
            String[] expectedNames = {
                "S A D D A M H U S S E I N A L - T I K R I T I",
                "SADDAM H U S S E I N A L - T I K R I T I",
                "S A D D A M  HUS S E I N  A L - TIK R I T I",
                "S A D DAM H U S S EIN  A L - T I K R ITI",
                "S A D D A M H U S S E I N A L - TIKRITI",
                "S-A-D-D-A-M H-U-S-S-E-I-N A-L - T- I- K- R- I- T- I",
                "SADDAM HUSSEIN AL-TIKRITI"
            };

            int[] expectedMinRanks = {97, 97, 84, 75, 99, 97, 100};

            for (int i = 0; i < expectedNames.length; i++) {
                String rank = detection_details.get(expectedNames[i]);
                if (rank != null) {
                    int actualRank = Integer.parseInt(rank);
                    softAssert.assertTrue(actualRank >= expectedMinRanks[i], 
                        String.format("Detection rank for '%s' is %d, expected >= %d", 
                            expectedNames[i], actualRank, expectedMinRanks[i]));
                } else {
                    softAssert.fail(String.format("No detection rank found for '%s'", expectedNames[i]));
                }
            }

            softAssert.assertAll("Some of the detection ranks are not as expected");
               } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @AfterClass
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {}.getClass().getName()
                    , new Object() {}.getClass().getEnclosingMethod().getName());
        }
    }
}