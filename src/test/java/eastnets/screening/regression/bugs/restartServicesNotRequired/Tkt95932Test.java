package eastnets.screening.regression.bugs.restartServicesNotRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.admin.entity.Operator;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.control.ISO20022Control;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FileScan;
import eastnets.screening.entity.ISO20022FormatConfiguration;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class Tkt95932Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ISO20022Control iso20022Control = new ISO20022Control();
    private final ResultManagerControl resultManagerControl = new ResultManagerControl();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();

    private RemoteWebDriver driver;

    EnList list;
    Operator operator = Common.OPERATOR.FULL_RIGHT_1.getOperator();

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true, groups = "regression")
    @Step("Login to filtering.")
    public void login() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver
                    , operator.getLoginName()
                    , operator.getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 1, enabled = true, groups = {"regression"})
    @Owner("Mohamed Hamed")
    public void bug95932() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Structured  Record :[Skipped ] is displayed when scanning ISO2002 message that has Sepa fields "));
            lifecycle.updateTestCase(testResult -> testResult.setName("Structured  Record :[Skipped ] is displayed when scanning ISO2002 message that has Sepa fields "));
            EnList enList = commonTestMethods.getEnListData();
            enList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            ISO20022FormatConfiguration formatConfiguration = new ISO20022FormatConfiguration();
            formatConfiguration.setZone(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            formatConfiguration.setGroupName("GroupName" + randomizer.getInt());
            iso20022Control.navigateToISO20022FormatManager(driver);
            iso20022Control.createGroup(driver, formatConfiguration);
            iso20022Control.clickConfigure(driver);
            iso20022Control.importConfigurations(driver, GeneralConstants.TKT95932_Format);
            list = commonTestMethods.getEnListData();
            list.getListSet().setSwiftTemplate(null);
            list.getListSet().setIsoGroup(formatConfiguration.getGroupName());
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, list, Common.PROFILE.FULL_RIGHT_001.getProfile().getName()); //create list
            list.getEntry().get(0).setName("Egypt");
            list.getEntry().get(0).setType("Country");
            list.getEntry().get(0).setFirstName(null);
            commonTestMethods.createNewEntryWithTypeAndNames(driver, list);

            FileScan message = commonTestMethods.getFileScanData(GeneralConstants.TKT95932_Message
                    , "ISO20022",
                    "All records"
                    , "50");
            String detectionID = commonTestMethods.scanFileAndGetDetectionID(driver, message);


            Assert.assertEquals(resultManagerControl.get_detection_status(driver)
                    , "Reported"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.get_Field_Type(driver, "CdtTrfTxInf/Cdtr/PstlAdr/AdrLine",
                            detectionID),
                    "ADDRESS",
                    "Field type is not correct");


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true, groups = {"regression"})
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
