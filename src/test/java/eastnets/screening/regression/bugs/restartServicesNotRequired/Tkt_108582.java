package eastnets.screening.regression.bugs.restartServicesNotRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.entity.AKA;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;


public class Tkt_108582 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final NameScanControl nameScanControl = new NameScanControl();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_10.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_10.getOperator().getPassword()
            );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    /**
     * Test Case 127936: Verify adding GG for AKA of entity have "&"
     * Steps:
     * 1. Should have in the blacklist an entity with AKA include "&", ex Entity: "goodsample" , it's AKA : "Test&&@Test"
     * 2. Login to AMLUI
     * 3. Scan for the entity
     * 4. Add good guy for the violation of the AKA of entity
     * 5. Notice the matched entity displayed in Matches Table
     * Expected Result: Matched entity in Matches Table should be the same as in Entity details without "amp" text
     */
    @Test
    @Issue("127936")
    @Owner("Sarah Abdellatif")
    public void tkt_127936() {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Test Case 127936: Verify adding GG for AKA of entity have \"&\""));
            lifecycle.updateTestCase(testResult -> testResult.setName("Test Case 127936: Verify adding GG for AKA of entity have \"&\""));


            EnList blackList = commonTestMethods.getEnListData();
            blackList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_010.getZone().getDisplayName());

            commonTestMethods.createListSet(driver, blackList, Common.PROFILE.FULL_RIGHT_010.getProfile().getName());

            blackList.getEntry().get(0).setName("goodsample");
            blackList.getEntry().get(0).setFirstName(null);
            blackList.getEntry().get(0).setAkas(new java.util.ArrayList<>());
            AKA aka = new AKA();
            aka.setFirstName("Test&&@Test");
            blackList.getEntry().get(0).getAkas().add(aka);

            commonTestMethods.createNewEntryWithTypeAndNames(driver, blackList);

            Assert.assertEquals(nameScanControl.scan_Name(driver
                            , blackList.getEntry().get(0).getName()
                            , blackList.getZoneName()
                            , null
                            , true
                            , true
                            , true
                            , false)
                    , "REPNEW"
                    , "The scanned name should be detected as REP");


            Assert.assertEquals(nameScanControl.get_matched_entity_name_GG(driver, blackList.getEntry().get(0).getName())
                    , blackList.getEntry().get(0).getName()
                    , "The matched entity name should be the same as the entity first name without 'amp' text");
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }



    /**
     * Test Case 127932: Verify adding GG for entity with "&" in the first
     * Steps:
     * 1. Should have in the blacklist an entity include "&" in the first, ex: "&goodsample"
     * 2. Login to AMLUI
     * 3. Scan for the entity
     * Expected Result: should return a reported violation against the entity
     * 4. Add good guy for the violation of the entity
     * 5. Notice the matched entity displayed in Matches Table
     * Expected Result: Matched entity in Matches Table should be the same as in Entity details without "amp" text
     */
    @Test
    @Issue("127932")
    @Owner("Sarah Abdellatif")
    public void tkt_127932() {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Test Case 127932: Verify adding GG for entity with \"&\" in the first"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Test Case 127932: Verify adding GG for entity with \"&\" in the first"));

            EnList blackList = commonTestMethods.getEnListData();
            blackList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_010.getZone().getDisplayName());

            commonTestMethods.createListSet(driver, blackList, Common.PROFILE.FULL_RIGHT_010.getProfile().getName());

            blackList.getEntry().get(0).setName("&goodsample");
            blackList.getEntry().get(0).setFirstName(null);

            commonTestMethods.createNewEntryWithTypeAndNames(driver, blackList);

            String scanResult = nameScanControl.scan_Name(driver
                    , blackList.getEntry().get(0).getName()
                    , blackList.getZoneName()
                    , null
                    , true
                    , true
                    , true
                    , false);

            Assert.assertEquals(scanResult, "REPNEW", "The scanned name should be detected as REP");

            Assert.assertEquals(nameScanControl.get_matched_entity_name_GG(driver, blackList.getEntry().get(0).getName())
                    , blackList.getEntry().get(0).getName()
                    , "The matched entity name should be the same as the entity name without 'amp' text");

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    /**
     * Test Case 127935: Verify adding GG for entity with "&" in the last
     * Steps:
     * 1. Should have in the blacklist an entity include "&" in the last, ex: "goodsample&&"
     * 2. Login to AMLUI
     * 3. Scan for the entity
     * 4. Add good guy for the violation of the entity
     * 5. Notice the matched entity displayed in Matches Table
     *    Expected Result: Matched entity in Matches Table should be the same as in Entity details without "amp" text
     */
    @Test
    @Issue("127935")
    @Owner("Sarah Abdellatif")
    public void tkt_127935() {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Test Case 127935: Verify adding GG for entity with \"&\" in the last"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Test Case 127935: Verify adding GG for entity with \"&\" in the last"));

            EnList blackList = commonTestMethods.getEnListData();
            blackList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_010.getZone().getDisplayName());

            commonTestMethods.createListSet(driver, blackList, Common.PROFILE.FULL_RIGHT_010.getProfile().getName());

            blackList.getEntry().get(0).setName("goodsample&&");
            blackList.getEntry().get(0).setFirstName(null);

            commonTestMethods.createNewEntryWithTypeAndNames(driver, blackList);

            String scanResult = nameScanControl.scan_Name(driver
                    , blackList.getEntry().get(0).getName()
                    , blackList.getZoneName()
                    , null
                    , true
                    , true
                    , true
                    , false);

            Assert.assertEquals(scanResult, "REPNEW", "The scanned name should be detected as REP");

            Assert.assertEquals(nameScanControl.get_matched_entity_name_GG(driver, blackList.getEntry().get(0).getName())
                    , blackList.getEntry().get(0).getName()
                    , "The matched entity name should be the same as the entity name without 'amp' text");

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    /**
     * Test Case 127933: Verify adding GG for entity with "&" in the middle
     * Steps:
     * 1. Should have in the blacklist an entity include "&" in the middle, ex: "good&&sample"
     * 2. Login to AMLUI
     * 3. Scan for the entity
     * 4. Add good guy for the violation of the entity
     * 5. Notice the matched entity displayed in Matches Table
     *    Expected Result: Matched entity in Matches Table should be the same as in Entity details without "amp" text
     */
    @Test
    @Issue("127933")
    @Owner("Sarah Abdellatif")
    public void tkt_127933() {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Test Case 127933: Verify adding GG for entity with \"&\" in the middle"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Test Case 127933: Verify adding GG for entity with \"&\" in the middle"));

            EnList blackList = commonTestMethods.getEnListData();
            blackList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_010.getZone().getDisplayName());

            commonTestMethods.createListSet(driver, blackList, Common.PROFILE.FULL_RIGHT_010.getProfile().getName());

            blackList.getEntry().get(0).setName("good&&sample");
            blackList.getEntry().get(0).setFirstName(null);

            commonTestMethods.createNewEntryWithTypeAndNames(driver, blackList);

            String scanResult = nameScanControl.scan_Name(driver
                    , blackList.getEntry().get(0).getName()
                    , blackList.getZoneName()
                    , null
                    , true
                    , true
                    , true
                    , false);

            Assert.assertEquals(scanResult, "REPNEW", "The scanned name should be detected as REP");

            Assert.assertEquals(nameScanControl.get_matched_entity_name_GG(driver, blackList.getEntry().get(0).getName())
                    , blackList.getEntry().get(0).getName()
                    , "The matched entity name should be the same as the entity name without 'amp' text");

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
