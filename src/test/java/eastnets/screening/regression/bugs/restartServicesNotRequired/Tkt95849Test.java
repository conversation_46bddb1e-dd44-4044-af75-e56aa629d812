package eastnets.screening.regression.bugs.restartServicesNotRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.listManager.ListSetControl;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.Step;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class Tkt95849Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final NameScanControl nameScanControl = new NameScanControl();
    private final ListSetControl listSetControl = new ListSetControl();
    private final core.util.Log log = new core.util.Log();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    @Step("Login to filtering.")
    public void login() {
        try {

            loginPage.login(driver
                    , Common.OPERATOR.FOUR_EYES_01.getOperator().getLoginName()
                    , Common.OPERATOR.FOUR_EYES_01.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This test method is related to bug number = 95849
     * Steps:
     * 1- Create new list set and blacklist
     * 3- Create new Entry = Osama Bin Laden
     * 4- perform scan with the name "Osama Bin laden"
     * 5- navigate to list manager and delete the list set
     * 6- Assert that waring message is displayed
     */
    @Test()
    @Tag("Regression")
    @Issue("95849")
    @Owner("SunilThokala")
    public void tkt_95849() {
        try {

            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Bug 95849:Bug 95849: listSetManager_ no validation message appears when delete ListSet has associated profile or open detections "));
            lifecycle.updateTestCase(testResult -> testResult.setName("Bug 95849: Bug 95849: listSetManager_ no validation message appears when delete ListSet has associated profile or open detections"));
            EnList list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.FOUR_EYES_ZONE.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, list, Common.PROFILE.FOUR_EYES_PROFILE.getProfile().getName());
            list.getEntry().get(0).setName("Osama Bin laden");
            list.getEntry().get(0).setFirstName(null);
            commonTestMethods.createNewEntryWithDetailsInfo(driver, list, list.getEntry().get(0));

            Assert.assertEquals(nameScanControl.scan_Name(driver
                            , "Osama Bin laden"
                            , Common.ZONE.FOUR_EYES_ZONE.getZone().getDisplayName()
                            , null
                            , true
                            , true
                            , true
                            , false)
                    , "REPNEW"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            String validationMessage = listSetControl.delete_list_Set(driver, list.getZoneName(), list.getListSet().getName());
            log.info(validationMessage);
            Assert.assertEquals(validationMessage, "Can't delete the list set because there are open detections found by the list set. " +
                    ", Can't delete the list set because there are profiles using that list set.", "Validation message is not as expected");
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
