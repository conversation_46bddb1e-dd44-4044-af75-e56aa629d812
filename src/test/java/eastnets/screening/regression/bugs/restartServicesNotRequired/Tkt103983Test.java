package eastnets.screening.regression.bugs.restartServicesNotRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class Tkt103983Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final NameScanControl nameScanControl = new NameScanControl();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(groups = {"regression"})
    public void loginForRegression() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getPassword()
                    );

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
    @Test()
    @Owner("Mohamed Hamed")
    @Issue("103983")
    @Tag("Regression")
    public void bug103983_LongText()  {
        try{
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify Full Text Display with Truncation for Long Scanned Text in Detection Manager"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify Full Text Display with Truncation for Long Scanned Text in Detection Manager"));

            EnList blackList = commonTestMethods.getEnListData();
            blackList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            String profileName = Common.PROFILE.FULL_RIGHT_001.getProfile().getName();
            commonTestMethods.createListSet(driver, blackList, profileName);

            blackList.getEntry().get(0).setName("Mohamed Ahmed Mohamed Hamed Mohamed Ahmed");
            blackList.getEntry().get(0).setFirstName(null);
            commonTestMethods.createNewEntryWithTypeAndNames(driver, blackList);

            Assert.assertEquals(nameScanControl.scan_Name(driver,"Mohamed Ahmed Mohamed Hamed",Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName(),
                    "75",true, true, true, false),
                    "REPNEW", "Scan result is not correct");
            String detectionID = nameScanControl.get_detection_id(driver);
            Assert.assertEquals(detectionManagerControl.get_violation_text(driver, detectionID),"Mohamed Ahmed Mohame (...)",
                    "Violation is not correct.");



        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }

    }

    @Test(enabled = true, groups = {"regression"})
    public void bug103983_ShortText()  {
        try{
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify Proper Display of Short Text Without Truncation"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify Proper Display of Short Text Without Truncation"));

            EnList blackList = commonTestMethods.getEnListData();
            blackList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            String profileName = Common.PROFILE.FULL_RIGHT_001.getProfile().getName();
            commonTestMethods.createListSet(driver, blackList, profileName);

            blackList.getEntry().get(0).setName("Mohamed Ahmed");
            blackList.getEntry().get(0).setFirstName(null);
            commonTestMethods.createNewEntryWithTypeAndNames(driver, blackList);

            Assert.assertEquals(nameScanControl.scan_Name(driver,"Mohamed Ahmed",Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName(),
                            "75",true, true, true, false),
                    "REPNEW", "Scan result is not correct");
            String detectionID = nameScanControl.get_detection_id(driver);
            Assert.assertEquals(detectionManagerControl.get_violation_text(driver, detectionID),"Mohamed Ahmed",
                    "Violation is not correct.");



        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }

    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
