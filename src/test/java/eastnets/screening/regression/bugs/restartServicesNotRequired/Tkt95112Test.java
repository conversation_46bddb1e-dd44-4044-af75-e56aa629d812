package eastnets.screening.regression.bugs.restartServicesNotRequired;

import com.fasterxml.jackson.databind.ObjectMapper;
import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.common.gui.Navigation;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.control.ISO20022Control;
import eastnets.screening.control.listManager.BlackListControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FileScan;
import eastnets.screening.entity.ISO20022FormatConfiguration;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import org.testng.asserts.SoftAssert;

import java.io.File;

public class Tkt95112Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ISO20022Control iso20022Control = new ISO20022Control();
    private final BlackListControl blackListControl = new BlackListControl();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getPassword()
                   );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    /**
     * This Test Method is for Bug 95112
     * Steps:
     * Login
     * Adding ListSet
     * Add entity -> country "North Korea"
     * Navigate to ISO -> Add ISO format Configuration
     * Add new group and enable
     * Import the format
     * Scan manager/File scanner
     * Use the file to scan
     * Navigate to detection manager
     * select the detection and validate the message
     */
    @Test(priority = 0, enabled = true)
    @Owner("SunilThokala")
    @Tag("Regression Test")
    @Issue("95112")
    public void tkt_95112() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Bug 95112: SWS 5.1.x: Highlight issue when scanning ISO messages"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Bug 95112: SWS 5.1.x: Highlight issue when scanning ISO messages"));

            SoftAssert softassert = new SoftAssert();
            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("isoConfiguration");
            ISO20022FormatConfiguration formatConfiguration = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath)
                    , ISO20022FormatConfiguration.class);
            formatConfiguration.setZone(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            formatConfiguration.setGroupName(String.format(formatConfiguration.getGroupName(), randomizer.getInt()));
            //System.out.println("iso group is " +formatConfiguration.getGroupName());

            Assert.assertEquals(iso20022Control.createGroup(driver, formatConfiguration)
                    , String.format("New group [%s] successfully added", formatConfiguration.getGroupName())
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while create new group.");
            iso20022Control.clickConfigure(driver);
            String actualResult = iso20022Control.importConfigurations(driver, GeneralConstants.TKT95112_Format);
            Assert.assertTrue(actualResult.contains(" successfully imported")
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while create new group.");

            EnList enList = commonTestMethods.getEnListData();

            Assert.assertTrue(blackListControl.create_Black_List(driver, enList)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new black list.");
            enList.getListSet().setIsoGroup(formatConfiguration.getGroupName());
            enList.getListSet().setSwiftTemplate(null);
            //System.out.println("iso group is " +enList.getListSet().getIsoGroup());
            commonTestMethods.createListSet(driver, enList, Common.PROFILE.FULL_RIGHT_001.getProfile().getName());
            enList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            enList.getEntry().get(0).setName("North Korea");
            enList.getEntry().get(0).setType("Country");
            enList.getEntry().get(0).setFirstName(null);
            enList.getEntry().get(0).setBirthCountry(null);
            commonTestMethods.createNewEntryWithTypeAndNames(driver, enList);
            enList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            FileScan message = commonTestMethods.getFileScanData(GeneralConstants.TKT95112_MESSAGE
                    , "ISO20022",
                    "All records"
                    , "50");
            Navigation.SCAN_MANAGER.navigate(driver);
            String detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, message);
            String highlightedText = "DBTR NAME North Korea";
            String data = "DBTR NAME North Korea";
            Assert.assertEquals(detectionManagerControl.get_scanned_record_data(driver, detectionId, data)
                    , highlightedText
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
