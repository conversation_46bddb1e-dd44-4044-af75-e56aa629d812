package eastnets.screening.regression.bugs.restartServicesNotRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.FormatManagerControl;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FileScan;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class Tkt95148Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final FormatManagerControl formatManagerControl = new FormatManagerControl();
    private final ResultManagerControl resultManagerControl = new ResultManagerControl();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(groups = {"regression"})
    public void loginForRegression() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getPassword()
                    );

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @Test()
    @Owner("Mohamed Hamed")
    @Issue("95148")
    @Tag("Regression")
    public void bug95148_SingleLine()  {
        try{
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that scanning with XML message with single line (doesn't has xml declaration) is working properly and violations generated"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that scanning with XML message with single line (doesn't has xml declaration) is working properly and violations generated"));

            EnList blackList = commonTestMethods.getEnListData();
            blackList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            String profileName = Common.PROFILE.FULL_RIGHT_001.getProfile().getName();
            commonTestMethods.createListSet(driver, blackList, profileName);

            blackList.getEntry().get(0).setName("Kevin Bontemps");
            blackList.getEntry().get(0).setFirstName(null);
            commonTestMethods.createNewEntryWithTypeAndNames(driver, blackList);

            String filePath = GeneralConstants.TKT95148_Format;
            String zone = Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName();
            formatManagerControl.importFormat(driver, zone, filePath);

            FileScan fileScan =  commonTestMethods.getFileScanData(GeneralConstants.TKT95148_MESSAGE_SINGLE);
            fileScan.setFormat("CAS_IPIN");

            commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            Assert.assertEquals(resultManagerControl.get_detection_status(driver)
                    ,"Reported"
                    ,GeneralConstants.POM_EXCEPTION_ERR_MSG);



        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }

    }
    @Test()
    @Owner("Mohamed Hamed")
    @Issue("95148")
    @Tag("Regression")
    public void bug95148_SingleLine_Without_XML()  {
        try{
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that scanning with XML message with single line (doesn't has xml declaration) is working properly and violations generated"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that scanning with XML message with single line (doesn't has xml declaration) is working properly and violations generated"));
            EnList blackList = commonTestMethods.getEnListData();
            blackList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            String profileName = Common.PROFILE.FULL_RIGHT_001.getProfile().getName();
            commonTestMethods.createListSet(driver, blackList, profileName);

            blackList.getEntry().get(0).setName("Kevin Bontemps");
            blackList.getEntry().get(0).setFirstName(null);
            commonTestMethods.createNewEntryWithTypeAndNames(driver, blackList);

            String filePath = GeneralConstants.TKT95148_Format;
            String zone = Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName();
            formatManagerControl.importFormat(driver, zone, filePath);

            FileScan fileScan =  commonTestMethods.getFileScanData(GeneralConstants.TKT95148_MESSAGE_SINGLE_WITHOUT_XML);
            fileScan.setFormat("CAS_IPIN");

            commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            Assert.assertEquals(resultManagerControl.get_detection_status(driver)
                    ,"Reported"
                    ,GeneralConstants.POM_EXCEPTION_ERR_MSG);



        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }

    }
    @Test()
    @Owner("Mohamed Hamed")
    @Issue("95148")
    @Tag("Regression")
    public void bug95148_MultipleLines()  {
        try{
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that scanning with XML message with multiple lines (has xml declaration) is working properly and generates violations"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that scanning with XML message with multiple lines (has xml declaration) is working properly and generates violations"));
            EnList blackList = commonTestMethods.getEnListData();
            blackList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            String profileName = Common.PROFILE.FULL_RIGHT_001.getProfile().getName();
            commonTestMethods.createListSet(driver, blackList, profileName);

            blackList.getEntry().get(0).setName("Kevin Bontemps");
            blackList.getEntry().get(0).setFirstName(null);
            commonTestMethods.createNewEntryWithTypeAndNames(driver, blackList);

            String filePath = GeneralConstants.TKT95148_Format;
            String zone = Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName();
            formatManagerControl.importFormat(driver, zone, filePath);

            FileScan fileScan =  commonTestMethods.getFileScanData(GeneralConstants.TKT95148_MESSAGE_MULTIPLE);
            fileScan.setFormat("CAS_IPIN");

            commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            Assert.assertEquals(resultManagerControl.get_detection_status(driver)
                    ,"Reported"
                    ,GeneralConstants.POM_EXCEPTION_ERR_MSG);



        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }

    }
    @Test()
    @Owner("Mohamed Hamed")
    @Issue("95148")
    @Tag("Regression")
    public void bug95148_MultipleLines_Without_XML()  {
        try{
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that scanning with XML message with multiple lines (has no xml declaration) is working properly and generates violations"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that scanning with XML message with multiple lines (has no xml declaration) is working properly and generates violations"));
            EnList blackList = commonTestMethods.getEnListData();
            blackList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            String profileName = Common.PROFILE.FULL_RIGHT_001.getProfile().getName();
            commonTestMethods.createListSet(driver, blackList, profileName);

            blackList.getEntry().get(0).setName("Kevin Bontemps");
            blackList.getEntry().get(0).setFirstName(null);
            commonTestMethods.createNewEntryWithTypeAndNames(driver, blackList);

            String filePath = GeneralConstants.TKT95148_Format;
            String zone = Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName();
            formatManagerControl.importFormat(driver, zone, filePath);

            FileScan fileScan =  commonTestMethods.getFileScanData(GeneralConstants.TKT95148_MESSAGE_MULTIPLE_WITHOUT_XML);
            fileScan.setFormat("CAS_IPIN");

            commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            Assert.assertEquals(resultManagerControl.get_detection_status(driver)
                    ,"Reported"
                    ,GeneralConstants.POM_EXCEPTION_ERR_MSG);



        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }

    }
    @AfterMethod(alwaysRun = true)
    public void logout()
    {
        try
        {
            commonAction.logout(driver);
        }
        catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
