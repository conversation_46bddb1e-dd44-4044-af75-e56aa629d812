package eastnets.screening.regression.bugs.restartServicesNotRequired;

import core.BaseTest;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import core.constants.screening.ISOConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.ISO20022Control;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class Tkt94729Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final ISO20022Control iso20022Control = new ISO20022Control();
    private final core.util.Property property = new core.util.Property();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod()
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This test method is related to bug number = 94729
     * Steps:
     * 1- Login to SWF
     * 2- Navigate to ISO20022 Configuration module
     * 3- Try to import invalid schemas in different file extensions for example: JPG, exe, XML, doc
     * 4- Note that unclear validation messages appear:
     * - The validation "An error occurred during execution of the application: [Content is not allowed in prolog] appears when trying to import .xml file
     * - The validation "An error occurred during the execution of the application: [null]." appears when trying to import .doc or .JPG file
     * - The validation "An error occurred during the execution of the application: [Unexpected <xml> appears at line 1 column 6]." when trying to import ISO xml message instead of .xsd schema
     */

    @Test(priority = 0, enabled = true)
    @Owner("SunilThokala")
    @Tag("Regression Test")
    @Issue("94729")
    public void tkt_94729() {


        try {

            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Bug 94729: Filtering: ISO20022 Phase II - Unclear validation messages appear when importing invalid schemas"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Bug 94729: Filtering: ISO20022 Phase II - Unclear validation messages appear when importing invalid schemas"));


            String fileName = ISOConstants.ISO_VALID_MESSAGEFORMAT_FILENAME;
            String fileName1 = ISOConstants.ISO_INVALID_MESSAGEFORMAT_FILENAME;
            String fileName2 = ISOConstants.ISO_VALID_BIG_PDF_MESSAGEFORMAT_FILENAME;
            String fileName3 = ISOConstants.ISO_VALID_DOCX_MESSAGEFORMAT_FILENAME;
            String fileName4 = ISOConstants.ISO_VALID_HTML_MESSAGEFORMAT_FILENAME;

            String filePath = System.getProperty("user.dir")
                    + property.fromFile(GeneralConstants.GENERAL_CONFIG_FILE_NAME).getProperty(GeneralConstants.DEFAULT_UPLOAD_PATH)
                    + fileName;
            Allure.step("Schema File Path = " + filePath);
            iso20022Control.navigateToISO20022SchemaManager(driver);

            Assert.assertEquals(iso20022Control.importSchema(driver, fileName1), "Invalid File Type."
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while import schema");

            Assert.assertEquals(iso20022Control.importSchema(driver, fileName2), "Invalid file extension only XSD extension is allowed."
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while import schema");

            Assert.assertEquals(iso20022Control.importSchema(driver, fileName3), "Invalid File Type."
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while import schema");

            Assert.assertEquals(iso20022Control.importSchema(driver, fileName4), "Invalid File Type."
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while import schema");

            iso20022Control.deleteSchema(driver, "pacs.008.001");
            Assert.assertEquals(iso20022Control.importSchema(driver, fileName)
                    , "Successfully import schema [pacs.008.001] with version [04]"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while import schema");
            Assert.assertEquals(iso20022Control.deleteSchema(driver, "pacs.008.001")
                    , "Schema version [pacs.008.001.04] successfully deleted"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while import schema");

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod()
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
