package eastnets.screening.regression.bugs.restartServicesNotRequired;

import core.BaseTest;
import core.ExceptionHandler;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.time.Duration;


public class Tkt_99782 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

/**
 * Test Case 120976: Verify open login page of the system then keep it idle 10 mins without any clicking on the page
 * Test Case 120978: Verify open login page of the system then keep it idle 15 mins without any clicking on the page
 * Test Case 120974: Verify open login page of the system then keep it idle 3 min without any clicking on the page
 * Test Case Steps:
 * 1. Open AMLUI Login page
 * 2. Keep login page idle without any click or logging to the system for 15 mins
 * 3. Return to login page after 10 mins
 * 4. Try to enter username and password directly
 *    Expected Result: Login page session will be kept and never terminates till user login to the system
 */

    @Test
    @Issue("120976 , 120978")
    @Owner("Sarah Abdellatif")
    public void tkt_99782() {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Test Case 120976: Verify open login page of the system then keep it idle 10 mins without any clicking on the page"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Test Case 120976: Verify open login page of the system then keep it idle 10 mins without any clicking on the page"));

            Allure.step("Keep login page idle for 15 minutes without any interaction");
            Thread.sleep(Duration.ofMinutes(15).toMillis());

            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_11.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_11.getOperator().getPassword()
            );

            Assert.assertTrue(commonAction.isUserLogged(driver)
                    ,"User is not logged in after keeping the login page idle for 10 minutes");

            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }





}
