package eastnets.screening.regression.bugs.restartServicesNotRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.admin.entity.Operator;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.ISO20022Control;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FileScan;
import eastnets.screening.entity.ISO20022FormatConfiguration;
import eastnets.screening.entity.ISO20022SchemaConfiguration;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class Tkt87047Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ISO20022Control iso20022Control = new ISO20022Control();
    private final ResultManagerControl resultManagerControl = new ResultManagerControl();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();

    private RemoteWebDriver driver;

    Operator operator = Common.OPERATOR.FULL_RIGHT_1.getOperator();
    EnList list;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true, groups = "regression")
    @Step("Login to filtering.")
    public void login() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver
                    , operator.getLoginName()
                    , operator.getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This Test Method is for Tkt 87047
     * Steps:
     * 1. Navigate to ISO20022 Manager and open Premapped fields.
     * 2. Add new Xsd Element.
     * 3. Navigate to Schema Configuration and import 2 schemas.
     * 4. Navigate to ISO20022 Format manager and create new group then click configure.
     * 5. Create new messages with the two imported schemas.
     * 6. Create a list set.
     * 7. Create blacklist and link it with the created list set.
     * 8. Add Syrian Arab Republic to the list.
     * 9. Scan the two messages in the ticket.
     */

    @Test(groups = {"regression"})
    public void bug87047() {
        AllureLifecycle lifecycle = Allure.getLifecycle();
        lifecycle.updateTestCase(testResult -> testResult.setDescription("Pre-processing of the XML fields"));
        lifecycle.updateTestCase(testResult -> testResult.setName("Pre-processing of the XML fields"));
        try {
            iso20022Control.addXSDElement(driver, "CountryCode", "COUNTRY");
            iso20022Control.navigateToSchemaConfiguration(driver);
            iso20022Control.importSchema(driver, GeneralConstants.TKT87047_SCHEMA_1);
            iso20022Control.importSchema(driver, GeneralConstants.TKT87047_SCHEMA_2);
            ISO20022FormatConfiguration formatConfiguration = new ISO20022FormatConfiguration();
            formatConfiguration.setZone(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            formatConfiguration.setGroupName("GroupName" + randomizer.getInt());
            iso20022Control.navigateToISO20022FormatManager(driver);
            iso20022Control.createGroup(driver, formatConfiguration);
            iso20022Control.clickConfigure(driver);
            ISO20022SchemaConfiguration schemaConfiguration = new ISO20022SchemaConfiguration();
            schemaConfiguration.setSchemaName("pacs.008.001");
            schemaConfiguration.setSchemaVersion("08");
            schemaConfiguration.setWrapper("/xml");
            iso20022Control.createNewMessage(driver, schemaConfiguration, null);
            schemaConfiguration.setSchemaVersion("02");
            iso20022Control.createNewMessage(driver, schemaConfiguration, null);
            list = commonTestMethods.getEnListData();
            list.getListSet().setSwiftTemplate(null);
            list.getListSet().setIsoGroup(formatConfiguration.getGroupName());
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, list, Common.PROFILE.FULL_RIGHT_001.getProfile().getName()); //create list
            list.getEntry().get(0).setName("Syrian Arab Republic");
            list.getEntry().get(0).setType("Country");
            list.getEntry().get(0).setFirstName(null);
            commonTestMethods.createNewEntryWithTypeAndNames(driver, list); //add entities

            FileScan message = commonTestMethods.getFileScanData(GeneralConstants.TKT87047_Message_1
                    , "ISO20022",
                    "All records"
                    , "50");
            commonTestMethods.scanFileAndGetDetectionID(driver, message);

            Assert.assertEquals(resultManagerControl.get_detection_status(driver)

                    , "Clean"

                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            message = commonTestMethods.getFileScanData(GeneralConstants.TKT87047_Message_2
                    , "ISO20022",
                    "All records"
                    , "50");
            commonTestMethods.scanFileAndGetDetectionID(driver, message);

            Assert.assertEquals(resultManagerControl.get_detection_status(driver)

                    , "Reported"

                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }

    }


    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
