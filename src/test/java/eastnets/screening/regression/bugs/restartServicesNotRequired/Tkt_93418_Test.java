package eastnets.screening.regression.bugs.restartServicesNotRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.listManager.GoodGuyControl;
import eastnets.screening.control.scanManger.ResultManagerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FileScan;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.util.List;

public class Tkt_93418_Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final GoodGuyControl goodGuyControl = new GoodGuyControl();
    private final ResultManagerControl resultManagerControl = new ResultManagerControl();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This test method is related to bug number = 93418
     * Steps:
     * 1. Create new entity with "Afghanistan"
     * 2. Create another entity with "Kevin Bontemps"
     * 3. Go to scan manager and scan file ----> 2 reported violations displayed
     * 4. Go to Good guy explorer and add the added text in file in accepted text field Then select both violations
     * 5. Go to scan manager and scan the attached file ----> 2 accepted violations are displayed
     */
    @Test(enabled = true)
    @Owner("Sarah Abdellatif")
    @Issue("93418")
    @Tag("Regression Test")
    public void bug_93418() {
        try {

            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Issue 93418: Screening: Good Guys not working with parenthesis in the accepted text"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Issue 93418: Screening: Good Guys not working with parenthesis in the accepted text"));

            EnList blackList = commonTestMethods.getEnListData();
            blackList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            String profileName = Common.PROFILE.FULL_RIGHT_001.getProfile().getName();
            commonTestMethods.createListSet(driver, blackList, profileName);

            blackList.getEntry().get(0).setName("Afghanistan");
            blackList.getEntry().get(0).setFirstName(null);
            commonTestMethods.createNewEntryWithTypeAndNames(driver, blackList);

            blackList.getEntry().get(0).setName("Kevin Bontemps");
            commonTestMethods.createNewEntryWithTypeAndNames(driver, blackList);


            FileScan fileScan = commonTestMethods.getFileScanData(GeneralConstants.FILE_SCAN_93418);
            fileScan.setFormat("Generic Text");

            commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);


            Assert.assertEquals(resultManagerControl.get_detection_status(driver)
                    , "Reported"
                    , "Returned detection from scanning file must be Reported");

            List<String> status_list = resultManagerControl.get_status_List_For_alerts(driver);
            Assert.assertEquals(status_list.size()
                    , 2
                    , "Returned Alerts from scanning file must be 2");

            Assert.assertEquals(status_list.get(0)
                    , "REPNEW"
                    , "Returned Alerts from scanning file must be REP");

            Assert.assertEquals(status_list.get(1)
                    , "REPNEW"
                    , "Returned Alerts from scanning file must be REP");

            goodGuyControl.search(driver, blackList);
            Assert.assertEquals(goodGuyControl.add_gg_by_select_all_matched_names(driver, "Afghanistan(Kevin Bontemps)", "50")
                    , "Good guy successfully created!"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


            fileScan.setResult("All records");
            commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);


            Assert.assertEquals(resultManagerControl.get_detection_status(driver)
                    , "Accepted"
                    , "Returned detection from scanning file must be Reported");

            status_list = resultManagerControl.get_status_List_For_alerts(driver);
            Assert.assertEquals(status_list.size()
                    , 2
                    , "Returned Alerts from scanning file must be 2");

            Assert.assertEquals(status_list.get(0)
                    , "ACC"
                    , "Returned Alerts from scanning file must be REP");

            Assert.assertEquals(status_list.get(1)
                    , "ACC"
                    , "Returned Alerts from scanning file must be REP");


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
