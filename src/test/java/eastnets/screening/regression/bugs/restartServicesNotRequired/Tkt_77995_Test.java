package eastnets.screening.regression.bugs.restartServicesNotRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.AdvancedSettingsControls.ReplaySettingControls;
import eastnets.screening.control.listManager.BlackListControl;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.util.List;

public class Tkt_77995_Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final BlackListControl blackListControl = new BlackListControl();
    private final ReplaySettingControls replaySettingControls = new ReplaySettingControls();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void pre_requisites() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_9.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_9.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This test method is related to bug number = 77995
     * Steps:
     * 1-Add a blacklist.
     * 2-Delete the blacklist.
     * 3-Check that deleted blacklists are not displayed in the replay config editor.
     */
    @Test(enabled = true)
    @Owner("Sarah Abdellatif")
    @Issue("77995")
    @Tag("Regression Test")
    public void bug_77995() {
        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setName("Bug 77995: Screening: Deleted blacklists are shown in the Replay Config editor."));
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Bug 77995: Screening: Deleted blacklists are shown in the Replay Config editor."));

            EnList list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_009.getZone().getDisplayName());

            Assert.assertTrue(blackListControl.create_Black_List(driver, list)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new black list.");

            List<String> blackLists = replaySettingControls.get_available_black_lists(driver, list.getName());
            Assert.assertEquals(blackLists.size()
                    , 1
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while getting available black lists.");
            Assert.assertEquals(blackLists.get(0)
                    , list.getName()
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while getting available black lists.");


            Assert.assertEquals(blackListControl.delete_black_list_edition(driver, list.getZoneName(), list.getName())
                    , "Successfully deleted."
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while deleting black list.");

            Assert.assertFalse(blackListControl.delete_black_list(driver, list.getName())
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while deleting black list.");

            Assert.assertEquals(replaySettingControls.get_available_black_lists(driver, list.getName()).size()
                    , 0
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while getting available black lists.");
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}

