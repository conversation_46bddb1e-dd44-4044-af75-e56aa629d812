package eastnets.screening.regression.bugs.restartServicesNotRequired;

import core.BaseTest;
import core.ExceptionHandler;
import eastnets.admin.control.GroupControl;
import eastnets.admin.control.OperatorControl;
import eastnets.admin.entity.Common;
import eastnets.admin.entity.Operator;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class Tkt96002Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final OperatorControl operatorControl = new OperatorControl();
    private final GroupControl groupControl = new GroupControl();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();

    private RemoteWebDriver driver;

    EnList list;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true, groups = "regression")
    @Step("Login to filtering.")
    public void login() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver
                    , "sysadmin"
                    , "manager"
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This Test Method is for bug 96002
     * Steps:
     * 1. Login to ALMUI with Sys admin.
     * 2. Create a new operator.
     * 3. Add the new operator to the Full Right group.
     * 4. Log out.
     * 5. Log in with the new operator.
     */
    @Test(groups = {"regression"})
    public void bug96002() {
        AllureLifecycle lifecycle = Allure.getLifecycle();
        lifecycle.updateTestCase(testResult -> testResult.setDescription("Group turn to disable once we assign new user in"));
        lifecycle.updateTestCase(testResult -> testResult.setName("Group turn to disable once we assign new user in"));

        try {
            int randomNumber = randomizer.getInt();
            String username = "selenium-user-" + randomNumber;
            Operator operator = new Operator(username,
                    "Random",
                    "User",
                    "hello00", Common.OPERATOR.FULL_RIGHT_1.getOperator().getZone());
            Assert.assertTrue(operatorControl.createOperator(driver, operator),
                    String.format("Can't create operator %s", operator.getLoginName()));
            groupControl.add_operator_to_group(driver, Common.GROUP.FULL_RIGHT_GROUP_01.get(), operator.getLoginName());
            commonAction.logout(driver);
            loginPage.login(driver
                    , username
                    , "hello00"
                    );

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }

    }


    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
