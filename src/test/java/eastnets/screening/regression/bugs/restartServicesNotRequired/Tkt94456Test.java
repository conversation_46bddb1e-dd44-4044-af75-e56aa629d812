package eastnets.screening.regression.bugs.restartServicesNotRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import core.util.Wait;
import eastnets.admin.entity.Common;
import eastnets.admin.entity.Operator;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.ApprovalControl;
import eastnets.screening.control.FormatManagerControl;
import eastnets.screening.control.listManager.BlackListControl;
import eastnets.screening.control.listManager.ListExplorerControl;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class Tkt94456Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final FormatManagerControl formatManagerControl = new FormatManagerControl();
    private final BlackListControl blackListControl = new BlackListControl();
    private final ListExplorerControl listExplorerControl = new ListExplorerControl();
    private final ApprovalControl approvalControl = new ApprovalControl();
    private final Wait wait = new Wait();

    private RemoteWebDriver driver;

    EnList list;
    Operator operator = Common.OPERATOR.FULL_RIGHT_1.getOperator();

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true, groups = "regression")
    @Step("Login to filtering.")
    public void login() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver
                    , operator.getLoginName()
                    , operator.getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This test is for ticket 95317
     * Steps:
     * 1. Go to Format Manager and import the format in the ticket.
     * 2. Create a black list.
     * 3. Choose the list and import the entries in the ticket.
     * 4. Go to list explorer and open the blacklist.
     * 5. Export all the entries.
     * 6. Check the ALMUI not logged out.
     */
    @Test(enabled = false, groups = {"regression"}, description = "Verify that user does not log out from AMLUI when trying to export entities after loading private list then to back to list explorer.")
    public void bug_94456_Test_95317() {
        AllureLifecycle lifecycle = Allure.getLifecycle();
        lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that user does not log out from AMLUI when trying to export entities after loading private list then to back to list explorer."));
        lifecycle.updateTestCase(testResult -> testResult.setName("Verify that user does not log out from AMLUI when trying to export entities after loading private list then to back to list explorer."));
        try {
            formatManagerControl.importFormat(driver, Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName(),
                    GeneralConstants.BUG_94456_FORMAT);
            list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            blackListControl.create_Black_List(driver, list);
            listExplorerControl.import_entries(driver, Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName()
                    , list.getName(), GeneralConstants.BUG_94456_ENTRIES, "94456_List", "", false, false, false, false, false);
            listExplorerControl.search(driver, list);
            listExplorerControl.export_entry(driver, "XML");
            wait.time(Wait.ONE_SECOND * 5);
            Assert.assertTrue(commonAction.isUserLogged(driver), "ALMUI logged out");


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This Test is for ticket 95318
     * Steps:
     * 1. Delete the blacklist before import.
     * 2. Import the first blacklist in the ticket.
     * 3. Import the second blacklist in the ticket.
     * 4. Check ALMUI not logged out.
     */

    @Test(enabled = false, groups = {"regression"}, description = "Verify that user does not log out from AMLUI when trying to import list after loading OFC list then to back to list Manager")
    public void bug_94456_Test_95318() {
        AllureLifecycle lifecycle = Allure.getLifecycle();
        lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that user does not log out from AMLUI when trying to import list after loading OFC list then to back to list Manager"));
        lifecycle.updateTestCase(testResult -> testResult.setName("Verify that user does not log out from AMLUI when trying to import list after loading OFC list then to back to list Manager"));
        try {
            approvalControl.deleteBlacklistBeforeImport(driver, Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName(), GeneralConstants.BUG_94456_Test1_Name);
            Assert.assertEquals(blackListControl.import_list(driver, Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName(), GeneralConstants.BUG_94456_TEST1_LIST, true, false)
                    , "SUCCEEDED"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            approvalControl.deleteBlacklistBeforeImport(driver, Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName()
                    , GeneralConstants.BUG_94456_TEST2_LIST);

            Assert.assertEquals(blackListControl.import_list(driver, Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName()
                            , GeneralConstants.BUG_94456_TEST2_LIST, true, false)
                    , "SUCCEEDED"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            Assert.assertTrue(commonAction.isUserLogged(driver), "ALMUI logged out");


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This Test is for ticket 95313
     * Steps:
     * 1. Go to Format manager and import the format in the ticket.
     * 2. Create a blacklist.
     * 3. Import the entries in the ticket.
     * 4. Import the second entries in the ticket.
     * 5. Check ALMUI not logged out.
     */

    @Test(enabled = false, groups = {"regression"}, description = "Verify that user does not log out from AMLUI when trying to load new private list after loading list then to back to list explorer.")
    public void bug_94456_Test_95313() {
        AllureLifecycle lifecycle = Allure.getLifecycle();
        lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that user does not log out from AMLUI when trying to load new private list after loading list then to back to list explorer."));
        lifecycle.updateTestCase(testResult -> testResult.setName("Verify that user does not log out from AMLUI when trying to load new private list after loading list then to back to list explorer."));
        try {
            formatManagerControl.importFormat(driver, Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName(),
                    GeneralConstants.BUG_94456_FORMAT);
            list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            blackListControl.create_Black_List(driver, list);
            listExplorerControl.import_entries(driver, Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName()
                    , list.getName(), GeneralConstants.BUG_94456_ENTRIES, "94456_List", "", false, false, false, false, false);
            listExplorerControl.import_entries(driver, Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName()
                    , list.getName(), GeneralConstants.BUG_94456_ENTRIES_2, "94456_List", "", false, false, false, false, false);
            wait.time(Wait.ONE_SECOND * 5);
            Assert.assertTrue(commonAction.isUserLogged(driver), "ALMUI logged out");


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This Test is for ticket 95321
     * Steps:
     * 1. Go to Format manager and import the format in the ticket.
     * 2. Create a blacklist.
     * 3. Import the entries in the ticket.
     * 4. Import the second entries in the ticket.
     * 5. Check ALMUI not logged out.
     */

    @Test(enabled = true, groups = {"regression"}, description = "Verify that user does not log out from AMLUI when trying to load new UFC list after loading private list then to back to list Manager.")
    public void bug_94456_Test_95321() {
        AllureLifecycle lifecycle = Allure.getLifecycle();
        lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that user does not log out from AMLUI when trying to load new UFC list after loading private list then to back to list Manager."));
        lifecycle.updateTestCase(testResult -> testResult.setName("Verify that user does not log out from AMLUI when trying to load new UFC list after loading private list then to back to list Manager."));
        try {
            formatManagerControl.importFormat(driver, Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName(),
                    GeneralConstants.BUG_94456_FORMAT);
            list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            blackListControl.create_Black_List(driver, list);
            listExplorerControl.import_entries(driver, Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName()
                    , list.getName(), GeneralConstants.BUG_94456_ENTRIES, "94456_List", "", false, false, false, false, false);
            listExplorerControl.import_entries(driver, Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName()
                    , list.getName(), GeneralConstants.BUG_94456_UFC_LIST, "94456_List", "", false, false, false, false, false);
            wait.time(Wait.ONE_SECOND * 5);
            Assert.assertTrue(commonAction.isUserLogged(driver), "ALMUI logged out");


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
