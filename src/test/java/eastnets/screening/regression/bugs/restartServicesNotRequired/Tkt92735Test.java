package eastnets.screening.regression.bugs.restartServicesNotRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.admin.entity.Operator;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.control.FormatManagerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FileScan;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class Tkt92735Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();
    private final FormatManagerControl formatManagerControl = new FormatManagerControl();

    private RemoteWebDriver driver;

    Operator operator = Common.OPERATOR.FULL_RIGHT_1.getOperator();
    EnList list;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true, groups = "regression")
    @Step("Login to filtering.")
    public void login() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver
                    , operator.getLoginName()
                    , operator.getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This Test Method is for bug 92735.
     * Steps:
     * 1. Create List set.
     * 2. Create Black list and link it to the list set.
     * 3. Add Osama Bin Laden entity to the blacklist.
     * 4. Navigate to Format manager and import the format in the ticket.
     * 5. Go to File Scan and scan the message in the ticket.
     * 6. Go to Detection Manager and check the result with the detection ID.
     * 7. Check the Structured record.
     */
    @Test(groups = {"regression"})
    public void bug92735() {
        AllureLifecycle lifecycle = Allure.getLifecycle();
        lifecycle.updateTestCase(testResult -> testResult.setDescription("Detection Manager - Structured record is not working when the separator is ^ 'Circumflex'"));
        lifecycle.updateTestCase(testResult -> testResult.setName("Detection Manager - Structured record is not working when the separator is ^ 'Circumflex'"));

        try {
            EnList enList = commonTestMethods.getEnListData();
            enList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, enList, Common.PROFILE.FULL_RIGHT_001.getProfile().getName());
            enList.getEntry().get(0).setName("Osama Bin Laden");
            enList.getEntry().get(0).setFirstName(null);

            commonTestMethods.createNewEntryWithTypeAndNames(driver, enList); //add entities
            String filePath = GeneralConstants.FORMAT_92732;
            String zone = Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName();
            formatManagerControl.importFormat(driver, zone, filePath);
            FileScan fileScan = commonTestMethods.getFileScanData(GeneralConstants.TKT92735_Message);
            fileScan.setFormat("BNP_Circumflex");
            String detectionID = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);
            Assert.assertEquals(detectionManagerControl.get_structured_record_data(driver, detectionID).size(), 4, "Structured data are not correct");
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
