package eastnets.screening.regression.bugs.restartServicesNotRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.listManager.GoodGuyControl;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;


public class Tkt91894Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final GoodGuyControl goodGuyControl = new GoodGuyControl();
    private final NameScanControl nameScanControl = new NameScanControl();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    @Step("Login to filtering.")
    public void login() {
        try {

            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 0, enabled = true, groups = {"regression"})
    public void tkt_91894() {
        try {
            // Test code goes here
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription(" Good Guy Editor - target checkboxes get unselected "));
            lifecycle.updateTestCase(testResult -> testResult.setName(" Good Guy Editor - target checkboxes get unselected"));

            EnList list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            //create a list
            commonTestMethods.createListSet(driver, list, Common.PROFILE.FULL_RIGHT_001.getProfile().getName());


            String[] entities = {"Shoaib Mohamad", "Shoaib Mohamad malik", "Shoaib Mohamad malik irfan"};
            for (int i = 0; i < entities.length; i++) {
                list.getEntry().get(0).setName(entities[i]);
                list.getEntry().get(0).setFirstName(null);
                commonTestMethods.createNewEntryWithDetailsInfo(driver, list, list.getEntry().get(0));
            }
            nameScanControl.scan_Name(driver
                    , list.getEntry().get(0).getName()
                    , list.getZoneName()
                    , "50"
                    , true
                    , true
                    , true
                    , true);
            String detectionID = nameScanControl.get_detection_id(driver);
            goodGuyControl.sort_by_rank_then_click_gg_button(driver, detectionID);

            Assert.assertTrue(goodGuyControl.check_if_matched_entity_checked(driver, entities[2]), String.format("After click on matched entity = %s checkbox is not checked.", entities[2]));
            Assert.assertTrue(goodGuyControl.check_if_matched_entity_checked(driver, entities[1]), String.format("After click on matched entity = %s checkbox is not checked.", entities[1]));


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
