package eastnets.screening.regression.bugs.restartServicesNotRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.FormatManagerControl;
import eastnets.screening.control.listManager.ActivityControl;
import eastnets.screening.control.listManager.ListExplorerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.Format;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class Tkt_91115_Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final FormatManagerControl formatManagerControl = new FormatManagerControl();
    private final ListExplorerControl listExplorerControl = new ListExplorerControl();
    private final ActivityControl activityControl = new ActivityControl();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void pre_requisites() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_9.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_9.getOperator().getPassword()
            );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This test method is related to bug number = 91115
     * Steps:
     * 1-After adding your blacklist to the list set:
     * 2-Load a private list with an old version.
     * 3-Load a private list with upgrade option enabled.
     */
    @Test(enabled = true)
    @Owner("Sarah Abdellatif")
    @Issue("91115")
    @Tag("Regression Test")
    public void bug_91115() {
        try {

            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setName("Bug 91115: ORA-01858 when loading a private list."));
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Bug 91115: ORA-01858 when loading a private list"));

            EnList list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_009.getZone().getDisplayName());

            commonTestMethods.createListSet(driver, list, Common.PROFILE.FULL_RIGHT_009.getProfile().getName());

            Format format = eastnets.screening.entity.Common.FORMAT.IMPORT_ENTRY_INDIVIDUAL_FORMAT_2.get();
            String filePath = GeneralConstants.TKT91115_CUSTOM_LIST_FILE_PATH;
            format.setName("Format-" + randomizer.getInt());
            format.setZone(list.getZoneName());

            Assert.assertNull(formatManagerControl.AddNewFormat(driver, format), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG + " while creating new format.");

            Assert.assertEquals(listExplorerControl.import_entries(driver, list.getZoneName(), list.getName(), filePath, format.getName(), "UTF8", false, false, false, true, false)
                    , "Operation completed successfully."
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while importing entries.");
            Assert.assertEquals(activityControl.get_status(driver)
                    , "SUCCEEDED"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while getting import list status.");

            filePath = GeneralConstants.TKT91115_CUSTOM_LIST_UPGRADE_FILE_PATH;
            Assert.assertEquals(listExplorerControl.import_entries(driver, list.getZoneName(), list.getName(), filePath, format.getName(), "UTF8", false, true, false, false, false)
                    , "Operation completed successfully."
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while importing entries.");
            Assert.assertEquals(activityControl.get_status(driver)
                    , "SUCCEEDED"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while getting import list status.");

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}

