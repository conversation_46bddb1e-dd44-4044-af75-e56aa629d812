package eastnets.screening.regression.bugs.restartServicesNotRequired;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.control.GroupControl;
import eastnets.admin.control.OperatorControl;
import eastnets.admin.entity.Common;
import eastnets.admin.entity.Operator;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.common.gui.Navigation;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

/*
1.login to sws by "sysadmin"
2.Navigate to "operator manager"
 3.create a group with a "disabled" status
 then switch to screening and navigate to "detection manager"
4.select a detection and check if the disabled group  appear in the alert/detection investigators assign list or not
the disabled group shouldn't appear in the alert/detection investigators assign list
 */
public class Tkt91527Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final OperatorControl operatorControl = new OperatorControl();
    private final GroupControl groupControl = new GroupControl();
    private final NameScanControl nameScanControl = new NameScanControl();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(groups = {"regression"})
    public void login() {
        try {
            loginPage.login(driver
                    , "sysadmin"
                    , "manager"
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @Test(priority = 0, enabled = false, groups = {"regression"})
    @Owner("SunilThokala")
    @Tag("Regression Test")
    public void tkt_91527_DisabledUser() {
        AllureLifecycle lifecycle = Allure.getLifecycle();
        lifecycle.updateTestCase(testResult -> testResult.setDescription("System should not allow to assigne alert to disabled users ."));
        lifecycle.updateTestCase(testResult -> testResult.setName("System should not allow to assigne alert to disabled users ."));

        try {
            int randomNumber = randomizer.getInt();
            String username = "DISABLED_USER" + randomizer.getInt();
            Operator operator = new Operator(username,
                    "Random",
                    "User",
                    "hello00", Common.ZONE.COMMON_TEST_ZONE_001.getZone());
            Assert.assertTrue(operatorControl.createDisabledOperator(driver, operator),
                    String.format("Can't create operator %s", operator.getLoginName()));
            System.out.println("Operator name is " + operator.getLoginName());
            groupControl.add_operator_to_group(driver, Common.GROUP.FULL_RIGHT_GROUP_01.get(), operator.getLoginName());
            commonAction.logout(driver);
            driver.navigate().refresh();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getPassword()

            );
            EnList blackList = commonTestMethods.getEnListData();
            blackList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            String profileName = Common.PROFILE.FULL_RIGHT_001.getProfile().getName();

            commonTestMethods.createListSet(driver, blackList, profileName);
            blackList.getEntry().get(0).setName("Osama bin laden");
            blackList.getEntry().get(0).setFirstName(null);
            commonTestMethods.createNewEntryWithTypeAndNames(driver, blackList);
            Assert.assertEquals(nameScanControl.scan_Name(driver
                            , "Osama bin laden"
                            , Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName()
                            , null
                            , true
                            , true
                            , true
                            , false)
                    , "REPNEW"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            String detectionID = nameScanControl.get_detection_id(driver);
            Navigation.DETECTION_MANAGER.navigate(driver);
            Assert.assertFalse(detectionManagerControl.get_assignee_value(driver, detectionID).contains("DISABLED_USER")
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }

    }
/*
login to sws by "sysadmin"
    navigate to "operator manager"
     lock an operator  "Locked" status
     then switch to screening and navigate to "detection manager"
     select a detection and check if the disabled user  appear in the alert/detection investigators assign list or not
    * */

    @Test(priority = 1, enabled = true, groups = {"regression"})
    @Owner("Sunil Thokala")
    @Tag("Regression Test")
    public void tkt_91527_LockedUser() {
        AllureLifecycle lifecycle = Allure.getLifecycle();
        lifecycle.updateTestCase(testResult -> testResult.setDescription("System should not allow to assign alert to Locked users ."));
        lifecycle.updateTestCase(testResult -> testResult.setName("System should not allow to assign alert to Locked users ."));
        try {

            Operator operator = Common.OPERATOR.FULL_RIGHT_10.getOperator();
            operatorControl.navigateToOperatorModule(driver);
            operatorControl.lockOperator(driver, operator);
            commonAction.logout(driver);
            driver.navigate().refresh();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getPassword()

            );
            EnList blackList = commonTestMethods.getEnListData();
            blackList.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            String profileName = Common.PROFILE.FULL_RIGHT_001.getProfile().getName();

            commonTestMethods.createListSet(driver, blackList, profileName);
            blackList.getEntry().get(0).setName("Osama bin laden");
            blackList.getEntry().get(0).setFirstName(null);
            commonTestMethods.createNewEntryWithTypeAndNames(driver, blackList);
            Assert.assertEquals(nameScanControl.scan_Name(driver
                            , "Osama bin laden"
                            , Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName()
                            , null
                            , true
                            , true
                            , true
                            , false)
                    , "REPNEW"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            String detectionID = nameScanControl.get_detection_id(driver);
            Navigation.DETECTION_MANAGER.navigate(driver);
            Assert.assertFalse(detectionManagerControl.verify_assignee_exists(driver, detectionID, "operator-10 / Operator, With full rights")
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            String newInvestigator = Common.OPERATOR.FULL_RIGHT_10.getOperator().getLoginName();
            Assert.assertFalse(detectionManagerControl.search_user_in_bulk_assign(driver, "operator-10 / Operator, With full rights")
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }

    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            driver.navigate().refresh();
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}