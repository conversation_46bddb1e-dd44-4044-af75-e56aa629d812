package eastnets.screening.regression.bugs.restartServicesNotRequired;

import core.BaseTest;
import core.ExceptionHandler;
import eastnets.admin.control.ZoneControl;
import eastnets.admin.entity.Zone;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class Tkt93444Test extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final ZoneControl zoneControl = new ZoneControl();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();

    private RemoteWebDriver driver;

    EnList list;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true, groups = "regression")
    @Step("Login to filtering.")
    public void login() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver
                    , "sysadmin"
                    , "manager"
                   );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * This test method is related to bug number = 93444
     * Steps:
     * 1. Login with Sysadmin user.
     * 2. Go to Zone manager and create a new Zone with special characters.
     * 3- Search by the Zone name.
     */
    @Test(groups = {"regression"})
    public void bug93444() {
        AllureLifecycle lifecycle = Allure.getLifecycle();
        lifecycle.updateTestCase(testResult -> testResult.setDescription("Bug 93444: Admin 4.0: AMLUI crash and exception occurs when trying to add zone name contain' "));
        lifecycle.updateTestCase(testResult -> testResult.setName("Bug 93444: Admin 4.0: AMLUI crash and exception occurs when trying to add zone name contain' "));
        try {
            int randomNumber = randomizer.getInt();
            Zone zone = new Zone(0, "TestZone\'-/" + randomNumber, "TestZone-" + randomNumber, "Zone for test");
            Assert.assertTrue(zoneControl.createZone(driver, zone), "Zone is not created");
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }


    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
