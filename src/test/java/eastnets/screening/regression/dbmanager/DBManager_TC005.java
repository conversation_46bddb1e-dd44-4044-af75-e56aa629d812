package eastnets.screening.regression.dbmanager;

import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.dbManager.DBFlowConfigControl;
import eastnets.screening.entity.DBConfigurations;
import io.qameta.allure.Allure;
import io.qameta.allure.Feature;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class DBManager_TC005 extends eastnets.screening.regression.dbmanager.Common {

    // Instance variables for page objects and controls
    private LoginPage loginPage = new LoginPage();
    private CommonAction commonAction = new CommonAction();
    private DBFlowConfigControl dbFlowConfigControl = new DBFlowConfigControl();

    @BeforeMethod()
    public void login() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_9.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_9.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test covers the below cases:
     * Test Case : 113664 - Add DB flow - Verify that validation added on name field when insert only spaces in the field
     * Steps:
     * 1- navigate to DB Manager
     * 2- Add new DB flow with name that contains only spaces like " "
     * 3- Verify that the validation message is displayed
     */

    @Test()
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("DB Manager")
    @Issue("113664")
    public void dbManager_TC005() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Test Case 113664: Add DB flow - Verify that validation added on name field when insert only spaces in the field"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Test Case 113664: Add DB flow - Verify that validation added on name field when insert only spaces in the field"));

            DBConfigurations dbConfigurations = new DBConfigurations();
            dbConfigurations.setTableName("DBScan");
            dbConfigurations = set_db_configurations(Common.ZONE.COMMON_TEST_ZONE_009.getZone().getDisplayName(), dbConfigurations);
            dbConfigurations.setName(" ");


            create_db_scan_table();
            Assert.assertEquals(dbFlowConfigControl.create_new_db_configurations(driver, dbConfigurations)
                    ,"Connected to database Successfully"
                    ,"Error in creating new DB configuration after click test connection button");
            Assert.assertEquals(dbFlowConfigControl.save_db_configurations(driver, dbConfigurations)
                    ,"Name required!"
                    ,GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod()
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
