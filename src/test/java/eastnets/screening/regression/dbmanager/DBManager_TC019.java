package eastnets.screening.regression.dbmanager;

import core.ExceptionHandler;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.dbManager.DBFlowConfigControl;
import eastnets.screening.entity.DBConfigurations;
import io.qameta.allure.Allure;
import io.qameta.allure.Feature;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class DBManager_TC019 extends eastnets.screening.regression.dbmanager.Common {

    // Instance variables for page objects and controls
    private LoginPage loginPage = new LoginPage();
    private CommonAction commonAction = new CommonAction();
    private DBFlowConfigControl dbFlowConfigControl = new DBFlowConfigControl();

    @BeforeMethod()
    public void login() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_9.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_9.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test covers the below cases:
     * Test Case 113810: DB manager [Search] - Verify that search with Zone is working properly
     * Steps:
     * 1- Navigate to DB manager module
     * 2- From Database Flow Configuration tab, Select Zone "Test", Click on Search button
     * 3- Search results should return only the DB flows created on Zone "Test"
     * if there is no data, then "No records found." will display
     */

    @Test()
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("DB Manager")
    @Issue("113810")
    public void dbManager_TC019() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Test Case 113810: DB manager [Search] - Verify that search with Zone is working properly"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Test Case 113810: DB manager [Search] - Verify that search with Zone is working properly"));

            DBConfigurations dbConfigurations = new DBConfigurations();
            dbConfigurations.setZoneName(Common.ZONE.COMMON_TEST_ZONE_009.getZone().getDisplayName());
            Assert.assertFalse(dbFlowConfigControl.search(driver, dbConfigurations)
                    ,"Failed to search for DB configurations");

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod()
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
