package eastnets.screening.regression.dbmanager;

import core.ExceptionHandler;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.dbManager.DBFlowConfigControl;
import eastnets.screening.entity.DBConfigurations;
import io.qameta.allure.Allure;
import io.qameta.allure.Feature;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class DBManager_TC012 extends eastnets.screening.regression.dbmanager.Common {

    // Instance variables for page objects and controls
    private LoginPage loginPage = new LoginPage();
    private CommonAction commonAction = new CommonAction();
    private DBFlowConfigControl dbFlowConfigControl = new DBFlowConfigControl();
    private core.util.Randomizer randomizer = new core.util.Randomizer();

    @BeforeMethod()
    public void login() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_9.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_9.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test covers the below cases:
     * Test Case 113673: Add DB flow - Verify that validation added on Table name field if the field is empty or invalid name inserted when test connection
     * Steps:
     * 1- navigate to DB Manager
     * 2- From Database Flow Configuration tab, Click on New button
     * 3- Select zone from Zone DDL
     * 4- Insert DB flow name in Name field
     * 5- Select Type as "INPUT"
     * 6- Leave Table name field empty or insert invalid table name
     * 7- Keep Output Flow value selected as "None"
     * 8- in Database Login Information section, insert valid DB Name
     * 9- Insert valid DB Host value
     * 10- Insert valid DB Port value
     * 11- Insert valid username and valid password
     * 12- Click on Test Connection button
     * 13- Validation message should be displayed "Table Name does not exist!"
     */

    @Test()
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("DB Manager")
    @Issue("113673")
    public void dbManager_TC012() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Test Case 113673: Add DB flow - Verify that validation added on Table name field if the field is empty or invalid name inserted when test connection"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Test Case 113673: Add DB flow - Verify that validation added on Table name field if the field is empty or invalid name inserted when test connection"));

            DBConfigurations dbConfigurations = new DBConfigurations();
            dbConfigurations.setName(String.format("DB_%s", randomizer.getInt()));

            dbConfigurations = set_db_configurations(Common.ZONE.COMMON_TEST_ZONE_009.getZone().getDisplayName(), dbConfigurations);

            Assert.assertEquals(dbFlowConfigControl.create_new_db_configurations(driver, dbConfigurations)
                    ,"Table Name does not exist!"
                    ,"Error message should be displayed");
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod()
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
