package eastnets.screening.regression.dbmanager;

import com.paypal.selion.platform.dataprovider.DataProviderFactory;
import com.paypal.selion.platform.dataprovider.DataResource;
import com.paypal.selion.platform.dataprovider.SeLionDataProvider;
import com.paypal.selion.platform.dataprovider.impl.InputStreamResource;
import core.BaseTest;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import core.util.Randomizer;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.entity.DBConfigurations;
import io.qameta.allure.Allure;
import io.qameta.allure.Feature;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import java.io.FileInputStream;
import java.io.IOException;

public class DBManager_TC001 extends eastnets.screening.regression.dbmanager.Common {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final Randomizer randomizer = new Randomizer();

    @BeforeMethod()
    public void login() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_9.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_9.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test covers the below cases:
     * Test Case : 113645 - Add DB flow - Verify that user can create new DB flow and save it "Connected to SQL DB & with Input type"
     * Test Case : 113655 - Add DB flow - Verify that user can create new DB flow and save it "Connected to Oracle DB & with Input type"
     * Test Case : 113656 - Add DB flow - Verify that user can create new DB flow and save it "Connected to SQL DB & with Output type"
     * Test Case : 113657 - Add DB flow - Verify that user can create new DB flow and save it "Connected to Oracle DB & with Output type"
     * Test Case : 113658 - Add DB flow - Verify that user can create new DB flow and save it "Connected to SQL DB & INPUT type with output flow"
     * Test Case : 113659 - Add DB flow - Verify that user can create new DB flow and save it "Connected to Oracle DB & INPUT type with output flow"
     *
     * NOTE: Cases Will run based on deployed dataBase type
     *              so if database type is SQL then only SQL cases will run
     *              and if database type is Oracle then only Oracle cases will run
     * */
    @Test(dataProvider = "dataProvider")
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("DB Manager")
    @Issue("113645, 113655, 113656, 113657, 113658, 113659")
    public void dbManager_TC001(DBConfigurations dbConfigurations) {

        try {
            DBConfigurations finalDbConfigurations = dbConfigurations;
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription(finalDbConfigurations.getTestCaseTitle()));
            lifecycle.updateTestCase(testResult -> testResult.setName(finalDbConfigurations.getTestCaseTitle()));

            dbConfigurations = set_db_configurations(Common.ZONE.COMMON_TEST_ZONE_009.getZone().getDisplayName(), dbConfigurations);
            dbConfigurations.setName(String.format(dbConfigurations.getName(), randomizer.getInt()));

            create_db_scan_table();
            add_new_db_flow(driver, dbConfigurations);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod()
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @DataProvider(name = "dataProvider")
    public Object[][] dataProvider() throws IOException {
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("addDBConfigurations");
        DataResource resource =
                new InputStreamResource(new FileInputStream(System.getProperty("user.dir") + csvDataFilePath),
                        DBConfigurations.class, "json");
        SeLionDataProvider dataProvider =
                DataProviderFactory.getDataProvider(resource);
        return dataProvider.getAllData();

    }
}
