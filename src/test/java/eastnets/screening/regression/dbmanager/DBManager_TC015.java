package eastnets.screening.regression.dbmanager;

import core.ExceptionHandler;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.dbManager.DBFlowConfigControl;
import eastnets.screening.entity.DBConfigurations;
import io.qameta.allure.Allure;
import io.qameta.allure.Feature;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class DBManager_TC015 extends eastnets.screening.regression.dbmanager.Common {

    // Instance variables for page objects and controls
    private LoginPage loginPage = new LoginPage();
    private CommonAction commonAction = new CommonAction();
    private DBFlowConfigControl dbFlowConfigControl = new DBFlowConfigControl();

    @BeforeMethod()
    public void login() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_9.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_9.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test covers the below cases:
     * Test Case 113822: DB manager - Verify that Reset functionality is working properly
     * Steps:
     * 1- navigate to DB Manager
     * 2- From Database Flow Configuration tab, Click on New button
     * 3- Select zone from Zone DDL
     * 4- Insert DB flow name in Name field
     * 5- Select Type as "INPUT"
     * 6- Insert Table name in Table Name field Which is created on the DB
     * 7- in Database Login Information section, Insert invalid data in all fields or any field of them
     * 8- Click on Test Connection button
     * 9- Validation message should be displayed -> "Problem on SQL string connection please check your login information!"
     */

    @Test()
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("DB Manager")
    @Issue("113822")
    public void dbManager_TC015() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Test Case 113822: DB manager - Verify that Reset functionality is working properly"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Test Case 113822: DB manager - Verify that Reset functionality is working properly"));

            DBConfigurations dbConfigurations = new DBConfigurations();
            dbConfigurations.setZoneName(Common.ZONE.COMMON_TEST_ZONE_009.getZone().getDisplayName());
            dbConfigurations.setType("INPUT");
            dbConfigurations.setDbType("SQLSERVER");

            Assert.assertTrue(dbFlowConfigControl.verify_reset_search(driver, dbConfigurations)
                    , "Failed to verify that Reset functionality is working properly");
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod()
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
