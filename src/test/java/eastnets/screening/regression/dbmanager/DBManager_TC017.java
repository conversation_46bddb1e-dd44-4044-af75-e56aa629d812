package eastnets.screening.regression.dbmanager;

import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.dbManager.DBFlowConfigControl;
import eastnets.screening.entity.DBConfigurations;
import io.qameta.allure.Allure;
import io.qameta.allure.Feature;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class DBManager_TC017 extends eastnets.screening.regression.dbmanager.Common {

    // Instance variables for page objects and controls
    private LoginPage loginPage = new LoginPage();
    private CommonAction commonAction = new CommonAction();
    private DBFlowConfigControl dbFlowConfigControl = new DBFlowConfigControl();
    private core.util.Randomizer randomizer = new core.util.Randomizer();

    @BeforeMethod()
    public void login() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_9.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_9.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test covers the below cases:
     * Test Case 114422: DB manager - Verify Configuration of the DB fields after creating DB flow "OUTPUT" type on SQL database
     * Steps:
     * 1- DB flow with "OUTPUT" type should be created on SQL database
     * 2- Navigate to DB manager module
     * 3- From Database Flow Configuration, Click on search button
     * 4- Select the created DB flow
     * 5- insert DB password  and click on show fields button
     * 6- Move all fields to the selected fields
     * 7- Select field and click on show button
     * 8- Verify that the field details are displayed correctly
         * Under field details
         * 1. Name should be displayed and shouldn't be editable
         * 2. Field type DDL: Should contain below values
         * [SKIPPED]
         * FULL_NAME
         * LAST_NAME
         * FIRST_NAME
         * AKA_FULL_NAME
         * AKA_LAST_NAME
         * AKA_FIRST_NAME
         * ADDRESS
         * CITY
         * COUNTRY
         * 3. Primary key checkbox
         * 4. Output(Client Request Id)checkbox
         * 5. Foreign key (For Input Table)
     *9- Verify that the below checkboxes are displayed
             * Primary key
             * Output
             * Foreign key
     * 10- Verify behavior when check the below checkboxes:
             * Primary key
             * Output
             * Foreign key
     */

    @Test()
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("DB Manager")
    @Issue("114422")
    public void dbManager_TC017() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Test Case 114422: DB manager - Verify Configuration of the DB fields after creating DB flow 'OUTPUT' type on SQL database"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Test Case 114422: DB manager - Verify Configuration of the DB fields after creating DB flow 'OUTPUT' type on SQL database"));

            DBConfigurations dbConfigurations = new DBConfigurations();
            dbConfigurations = set_db_configurations(Common.ZONE.COMMON_TEST_ZONE_009.getZone().getDisplayName(), dbConfigurations);
            dbConfigurations.setTableName("DBScan");
            dbConfigurations.setName(String.format("DB_%s", randomizer.getInt()));
            dbConfigurations.setType("OUTPUT");

            create_db_scan_table();

            Assert.assertEquals(dbFlowConfigControl.create_new_db_configurations(driver, dbConfigurations)
                    ,"Connected to database Successfully"
                    ,"Error in creating new DB configuration after click test connection button");
            Assert.assertEquals(dbFlowConfigControl.save_db_configurations(driver, dbConfigurations)
                    ,"Operation completed successfully."
                    ,GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


            Assert.assertNull(dbFlowConfigControl.select_field_details(driver
                    , dbConfigurations.getName()
                    , dbConfigurations.getPassword()
                    , "FirstName")
            ,GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Assert.assertEqualsNoOrder(dbFlowConfigControl.get_all_field_types(driver)
                    ,GeneralConstants.DB_FIELED_TYPES
                    ,"DB field types are not as expected");


            Assert.assertTrue(dbFlowConfigControl.verify_primary_key_checkbox_displayed(driver)
                    ,"Primary key checkbox is not displayed");
            Assert.assertTrue(dbFlowConfigControl.verify_output_checkbox_displayed(driver)
                    ,"Output checkbox is not displayed");
            Assert.assertTrue(dbFlowConfigControl.verify_foreign_key_checkbox_displayed(driver)
                    ,"Foreign key checkbox is not displayed");



            Assert.assertTrue(dbFlowConfigControl.verify_primary_key_checkbox_checked_output_flow(driver)
                    ,"After check primary key checkbox, wrong fileds are displayed");
            Assert.assertTrue(dbFlowConfigControl.verify_output_checkbox_checked_output_flow(driver)
                    ,"After check output checkbox, wrong fileds are displayed");
            Assert.assertTrue(dbFlowConfigControl.verify_foreign_key_checkbox_checked(driver)
                    ,"After check foreign key checkbox, wrong fileds are displayed");


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod()
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
