package eastnets.screening.regression.dbmanager;

import core.ExceptionHandler;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.dbManager.DBFlowConfigControl;
import eastnets.screening.entity.DBConfigurations;
import io.qameta.allure.Allure;
import io.qameta.allure.Feature;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class DBManager_TC013 extends eastnets.screening.regression.dbmanager.Common {

    // Instance variables for page objects and controls
    private LoginPage loginPage = new LoginPage();
    private CommonAction commonAction = new CommonAction();
    private DBFlowConfigControl dbFlowConfigControl = new DBFlowConfigControl();
    private core.util.Randomizer randomizer = new core.util.Randomizer();

    @BeforeMethod()
    public void login() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_9.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_9.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test covers the below cases:
     * Test Case 113674: Add DB flow - Verify that validation added on login information fields when test connection with empty fields
     * Steps:
     * 1- navigate to DB Manager
     * 2- From Database Flow Configuration tab, Click on New button
     * 3- Select zone from Zone DDL
     * 4- Insert DB flow name in Name field
     * 5- Select Type as "INPUT"
     * 6- Insert Table name in Table Name field Which is created on the DB
     * 7- in Database Login Information section, Leave all fields empty
     * 8- Click on Test Connection button
     * 9- Validation messages should be displayed
         * Database Name required!
         * Host Name required!
         * Port required!
         * User Name required!
         * Password required!
     */

    @Test()
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("DB Manager")
    @Issue("113674")
    public void dbManager_TC013() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Test Case 113674: Add DB flow - Verify that validation added on login information fields when test connection with empty fields"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Test Case 113674: Add DB flow - Verify that validation added on login information fields when test connection with empty fields"));

            create_db_scan_table();
            DBConfigurations dbConfigurations = new DBConfigurations();
            dbConfigurations.setZoneName(Common.ZONE.COMMON_TEST_ZONE_009.getZone().getDisplayName());
            dbConfigurations.setName(String.format("DB_%s", randomizer.getInt()));
            dbConfigurations.setTableName("DBScan");


            Assert.assertEquals(dbFlowConfigControl.create_new_db_configurations(driver, dbConfigurations)
                    ,"Database Name required! , Host Name required! , Port required! , User Name required! , Password required!"
                    ,"Error message should be displayed");
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod()
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
