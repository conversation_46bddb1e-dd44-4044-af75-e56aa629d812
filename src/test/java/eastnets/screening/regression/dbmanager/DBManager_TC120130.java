package eastnets.screening.regression.dbmanager;

import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.dbManager.DBFlowConfigControl;
import eastnets.screening.entity.DBConfigurations;
import io.qameta.allure.Allure;
import io.qameta.allure.Feature;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class DBManager_TC120130 extends Common{

    // Instance variables for page objects and controls
    private LoginPage loginPage = new LoginPage();
    private CommonAction commonAction = new CommonAction();
    private DBFlowConfigControl dbFlowConfigControl = new DBFlowConfigControl();
    private core.util.Randomizer randomizer = new core.util.Randomizer();

    @BeforeMethod()
    public void login() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver
                    , eastnets.admin.entity.Common.OPERATOR.FULL_RIGHT_9.getOperator().getLoginName()
                    , eastnets.admin.entity.Common.OPERATOR.FULL_RIGHT_9.getOperator().getPassword()
            );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test covers the below cases:
     * Test Case 120130: DB flow (Configure Fields) - Verify Field details page opened with correct DB password
     * Steps:
     * 1- Login to AMLUI
     * 2- Navigate to DB manager
     * 3- Click on DB flow name
     * 4- Insert valid password
     * 5- Click on Show fields button
     * 6- Validate that fields details page should be opened
     */

    @Test()
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("DB Manager")
    @Issue("120130")
    public void dbManager_TC120130() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Test Case 120130: DB flow (Configure Fields) - Verify Field details page opened with correct DB password"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Test Case 120130: DB flow (Configure Fields) - Verify Field details page opened with correct DB password"));

            DBConfigurations dbConfigurations = new DBConfigurations();
            dbConfigurations.setZoneName(eastnets.admin.entity.Common.ZONE.COMMON_TEST_ZONE_009.getZone().getDisplayName());
            dbConfigurations.setTableName("DBScan");
            dbConfigurations = set_db_configurations(eastnets.admin.entity.Common.ZONE.COMMON_TEST_ZONE_009.getZone().getDisplayName(), dbConfigurations);
            dbConfigurations.setName(String.format("DB_%s", randomizer.getInt()));


            create_db_scan_table();
            Assert.assertEquals(dbFlowConfigControl.create_new_db_configurations(driver, dbConfigurations)
                    ,"Connected to database Successfully"
                    ,"Error in creating new DB configuration after click test connection button");
            Assert.assertEquals(dbFlowConfigControl.save_db_configurations(driver, dbConfigurations)
                    ,"Operation completed successfully."
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertTrue(dbFlowConfigControl.click_show_filed_btn(driver, dbConfigurations)
                    , "Error in opening field details page");

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod()
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
