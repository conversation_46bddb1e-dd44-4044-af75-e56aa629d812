package eastnets.screening.regression.dbmanager;

import core.BaseTest;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.dbManager.DBFlowConfigControl;
import eastnets.screening.entity.DBConfigurations;
import io.qameta.allure.AllureLifecycle;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.BeforeClass;

import java.sql.SQLException;

public class Common extends BaseTest {

    // Instance variables for page objects and controls
    protected final DBFlowConfigControl dbFlowConfigControl = new DBFlowConfigControl();
    protected final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();

    public RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    public void add_new_db_flow(RemoteWebDriver driver, DBConfigurations dbConfigurations) throws Exception {
        Assert.assertEquals(dbFlowConfigControl.create_new_db_configurations(driver, dbConfigurations)
                , "Connected to database Successfully"
                , "Error in creating new DB configuration after click test connection button");
        Assert.assertEquals(dbFlowConfigControl.save_db_configurations(driver, dbConfigurations)
                , "Operation completed successfully."
                , "Error in saving new DB configuration");
        Assert.assertEquals(dbFlowConfigControl.add_field_details(driver, dbConfigurations)
                , "Operation completed successfully."
                , "Error in adding fields details to DB configuration");
    }

    public void create_db_scan_table() throws SQLException {
        screeningServicesDelegate.CreateDBScanTable();
    }

    public DBConfigurations set_db_configurations(String zone, DBConfigurations dbConfigurations) {
        dbConfigurations.setZoneName(zone);
        dbConfigurations.setDbType(screeningGeneralConfigsProps.getProperty(GeneralConstants.DB_TYPE));
        dbConfigurations.setDbName(screeningGeneralConfigsProps.getProperty(GeneralConstants.DB_NAME));
        dbConfigurations.setHost(screeningGeneralConfigsProps.getProperty(GeneralConstants.DB_HOST));
        dbConfigurations.setPort(screeningGeneralConfigsProps.getProperty(GeneralConstants.DB_PORT));
        dbConfigurations.setUserName(screeningGeneralConfigsProps.getProperty(GeneralConstants.DB_USERNAME));
        dbConfigurations.setPassword(screeningGeneralConfigsProps.getProperty(GeneralConstants.DB_PASSWORD));
        return dbConfigurations;
    }
}
