package eastnets.screening.regression.dbmanager;

import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.dbManager.DBFlowConfigControl;
import eastnets.screening.entity.DBConfigurations;
import eastnets.screening.gui.dbManager.dbFlowConfiguration.DBConfigManager;
import io.qameta.allure.Allure;
import io.qameta.allure.Feature;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class DBManager_TC020 extends eastnets.screening.regression.dbmanager.Common {

    // Instance variables for page objects and controls
    private LoginPage loginPage = new LoginPage();
    private CommonAction commonAction = new CommonAction();
    private DBFlowConfigControl dbFlowConfigControl = new DBFlowConfigControl();
    private DBConfigManager dbConfigManager = new DBConfigManager();
    private core.util.Randomizer randomizer = new core.util.Randomizer();

    @BeforeMethod()
    public void login() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_9.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_9.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test covers the below cases:
     * Test Case 113813: DB manager [Search] - Verify that search with Name is working properly
     * Steps:
     * 1- DB flow should be created
     * 2- Navigate to DB manager module
     * 3- From Database Flow Configuration tab, Insert DB flow name in Name field
     * 4- Click on Search button
     * 5- Search results should return only the DB flow created with the inserted name
     */

    @Test()
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("DB Manager")
    @Issue("113813")
    public void dbManager_TC020() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Test Case 113813: DB manager [Search] - Verify that search with Name is working properly"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Test Case 113813: DB manager [Search] - Verify that search with Name is working properly"));

            DBConfigurations dbConfigurations = new DBConfigurations();
            dbConfigurations = set_db_configurations(Common.ZONE.COMMON_TEST_ZONE_009.getZone().getDisplayName(), dbConfigurations);
            dbConfigurations.setTableName("DBScan");
            dbConfigurations.setName(String.format("DB_%s", randomizer.getInt()));

            create_db_scan_table();

            Assert.assertEquals(dbFlowConfigControl.create_new_db_configurations(driver, dbConfigurations)
                    ,"Connected to database Successfully"
                    ,"Error in creating new DB configuration after click test connection button");
            Assert.assertEquals(dbFlowConfigControl.save_db_configurations(driver, dbConfigurations)
                    ,"Operation completed successfully."
                    ,GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Assert.assertFalse(dbFlowConfigControl.search_by_name(driver, dbConfigurations.getName())
                    ,"Failed to search for DB configurations");
            Assert.assertTrue(dbConfigManager.select_flow_by_name_from_result(driver, dbConfigurations.getName())
                    ,"Failed to select flow by name from search results");



        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod()
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
