package eastnets.screening.regression.dbmanager;

import core.ExceptionHandler;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.dbManager.DBFlowConfigControl;
import eastnets.screening.entity.DBConfigurations;
import io.qameta.allure.Allure;
import io.qameta.allure.Feature;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class DBManager_TC010 extends eastnets.screening.regression.dbmanager.Common {

    // Instance variables for page objects and controls
    private LoginPage loginPage = new LoginPage();
    private CommonAction commonAction = new CommonAction();
    private DBFlowConfigControl dbFlowConfigControl = new DBFlowConfigControl();
    private core.util.Randomizer randomizer = new core.util.Randomizer();

    @BeforeMethod()
    public void login() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_9.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_9.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test covers the below cases:
     * Test Case 113668: Add DB flow - Verify that validation added on the required fields when leave any field empty
     * Steps:
     * 1- navigate to DB Manager
     * 2- From Database Flow Configuration tab, Click on New button
     * 3- Click on Save button without filling any field
     * 4- Validation messages should be displayed on the required fields
     * messages as below:
         * Name required!
         * Table Name required!
         * Database Name required!
         * Host Name required!
         * Port required!
         * User Name required!
         * Password required!
     */

    @Test()
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("DB Manager")
    @Issue("113668")
    public void dbManager_TC010() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Test Case 113668: Add DB flow - Verify that validation added on the required fields when leave any field empty"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Test Case 113668: Add DB flow - Verify that validation added on the required fields when leave any field empty"));
            create_db_scan_table();
            DBConfigurations dbConfigurations = new DBConfigurations();
            dbConfigurations.setName(String.format("DB_%s", randomizer.getInt()));
            dbConfigurations.setTableName("DBScan");

            dbConfigurations = set_db_configurations(Common.ZONE.COMMON_TEST_ZONE_009.getZone().getDisplayName(), dbConfigurations);


            Assert.assertEquals(dbFlowConfigControl.save_db_configurations_without_entering_data(driver, dbConfigurations)
                    ,"Name required! , Table Name required! , Database Name required! " +
                            ", Host Name required! , Port required! , User Name required! , Password required!"
                    ,"Error message should be displayed");
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod()
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
