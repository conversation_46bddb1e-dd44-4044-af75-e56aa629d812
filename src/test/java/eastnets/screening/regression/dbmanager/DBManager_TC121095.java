package eastnets.screening.regression.dbmanager;

import com.fasterxml.jackson.databind.ObjectMapper;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import core.util.CSVHandler;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.control.dbManager.DBScanReqControl;
import eastnets.screening.control.scanManger.DBScanControl;
import eastnets.screening.entity.DBConfigurations;
import eastnets.screening.entity.EnList;
import eastnets.screening.gui.dbManager.dbScanRequest.DBScanReqEditor;
import io.qameta.allure.Allure;
import io.qameta.allure.Feature;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.io.File;

public class DBManager_TC121095 extends Common{

    // Instance variables for page objects and controls
    private LoginPage loginPage = new LoginPage();
    private CommonAction commonAction = new CommonAction();
    private CommonTestMethods commonTestMethods = new CommonTestMethods();
    private ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private DetectionManagerControl detectionManagerControl = new DetectionManagerControl();
    private DBScanReqControl dbScanReqControl = new DBScanReqControl();
    private DBScanControl dbScanControl = new DBScanControl();
    private DBScanReqEditor dbScanReqEditor = new DBScanReqEditor();
    private core.util.Randomizer randomizer = new core.util.Randomizer();
    private final CSVHandler csvHandler = new CSVHandler();

    @BeforeMethod()
    public void login() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver
                    , eastnets.admin.entity.Common.OPERATOR.FULL_RIGHT_9.getOperator().getLoginName()
                    , eastnets.admin.entity.Common.OPERATOR.FULL_RIGHT_9.getOperator().getPassword()
            );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

   /**
     * Test covers the below cases:
     * Test Case 121095: Verify that DB scanning working properly "DB flow connected to SQLSERVER and linked to output flow"
     * Steps:
     * 1- Login to AMLUI
     * 2- Navigate to DB manager
     * 3- Add new DB flow of type output and configure fields
     * 4- Add new DB flow of type input and configure fields
     * 5- Go to scan manager then to DB scan
     * 6- Select zone and list set
     * 7- Select the input flow --> two login information sections should be displayed
     * 8- Insert the password in both of them and test the connection
     * 9- Click on Test connection button
     *          --> message should be displayed "Successfully connected to Database"
     * 10- Select run type "one time" and select start date
     * 11- Click on Init scan button --> Database scan request added successfully with id [ID].
     * 12- Go to DB manager module then to Database scan request tab
     * 13- Click on search button and check the created scan request
     * 14- Select scan request and click on view processes details button --> Status should be success
     * 15- Select the process and click on view running processes details button
     *          --> In process details tab process status should be "Process was completed"
     * 16- Navigate to statistics tab and check Total Records Updated
     * 17- From process details tab, take scan session id and go to detection manager
     * 18- Search with scan session id
     * 19- Check the displayed detection
     *            --> Count of the detections should be equal to Total Records Updated in statistics tab
     * 20- By checking Database table for output flow client request id should be saved for each record
     * 21- Verify that client request id in output table for specific record is correct
     *      by checking in tDetections table "search by scan session id" -->
     *      Client request id should be same for same scanned name in output table and in tDetections table
     */

    @Test()
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("DB Manager")
    @Issue("121093")
    public void dbManager_TC121095() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Test Case 121095: Verify that DB scanning working properly 'DB flow connected to SQLSERVER and linked to output flow'"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Test Case 121095: Verify that DB scanning working properly 'DB flow connected to SQLSERVER and linked to output flow'"));

            EnList blist = commonTestMethods.getEnListData();
            blist.setZoneName(eastnets.admin.entity.Common.ZONE.COMMON_TEST_ZONE_009.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, blist, eastnets.admin.entity.Common.PROFILE.FULL_RIGHT_009.getProfile().getName());

            commonTestMethods.createNewEntryWithTypeAndNames(driver, blist);

            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("addDBConfigurations");

            DBConfigurations output_flow = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath)
                    , DBConfigurations[].class)[1];
            output_flow = set_db_configurations(eastnets.admin.entity.Common.ZONE.COMMON_TEST_ZONE_009.getZone().getDisplayName(),output_flow );
            output_flow.setName(String.format(output_flow.getName(), randomizer.getInt()));
            output_flow.setListSetName(blist.getListSet().getName());


            DBConfigurations inputFlow = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath)
                    , DBConfigurations[].class)[0];
            inputFlow = set_db_configurations(eastnets.admin.entity.Common.ZONE.COMMON_TEST_ZONE_009.getZone().getDisplayName(), inputFlow);
            inputFlow.setName(String.format(inputFlow.getName(), randomizer.getInt()));
            inputFlow.setListSetName(blist.getListSet().getName());
            inputFlow.setOutputFlow(output_flow.getName());




            create_db_scan_table();


            csvHandler.updateCSV(System.getProperty("user.dir") + GeneralConstants.DB_SCAN_FILE_PATH,
                    blist.getEntry().get(0).getName(), 1, 0);
            csvHandler.updateCSV(System.getProperty("user.dir") + GeneralConstants.DB_SCAN_FILE_PATH,
                    blist.getEntry().get(0).getFirstName(), 1, 1);

            screeningServicesDelegate.CreateDBScanTable();
            screeningServicesDelegate.insertDataInDBScanTable(GeneralConstants.DB_SCAN_FILE_PATH);
            add_new_db_flow(driver, output_flow);
            add_new_db_flow(driver, inputFlow);

            System.out.println("input flow name: " + inputFlow.getName());
            System.out.println("output flow name: " + output_flow.getName());

            Assert.assertTrue(dbScanControl.db_Scan(driver, inputFlow)
                            .contains("Database scan request added successfully with id ")
                    , "Error in clicking init scan button");

            Assert.assertEquals(dbScanReqControl.get_process_status(driver)
                    ,"Success"
                    , "Error in clicking view process details button");

            String session_id = dbScanReqControl.get_scan_session_id(driver);
            Assert.assertNotNull(session_id
                    , "Error in clicking view running process details button");

            Assert.assertEquals(dbScanReqControl.get_process_status_from_details_tab(driver)
                    ,"Process was completed"
                    , "Error in clicking view running process details button");

            String total_records_updated = dbScanReqControl.get_total_records_updated(driver);
            dbScanReqEditor.click_close_button(driver);

            Assert.assertEquals(detectionManagerControl.get_number_of_detections(driver, session_id)
                    , Integer.parseInt(total_records_updated)
                    , "Error in clicking view running process details button");


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod()
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
