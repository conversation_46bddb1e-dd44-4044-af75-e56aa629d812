package eastnets.screening.regression.dbmanager;

import core.ExceptionHandler;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.dbManager.DBFlowConfigControl;
import io.qameta.allure.Allure;
import io.qameta.allure.Feature;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.util.List;

public class DBManager_TC008 extends eastnets.screening.regression.dbmanager.Common {

    // Instance variables for page objects and controls
    private LoginPage loginPage = new LoginPage();
    private CommonAction commonAction = new CommonAction();
    private ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private DBFlowConfigControl dbFlowConfigControl = new DBFlowConfigControl();

    @BeforeMethod()
    public void login() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_9.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_9.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test covers the below cases:
     * Test Case : 113666 - Add DB flow - Verify that Output Flow DDL is displayed if the type is "INPUT" & it displays the correct values
     * Steps:
     * 1- navigate to DB Manager
     * 2- Click add new DB flow
     * 3- Check Zone drop down list
     * 4- It should display only the user's zones "any zone the user has access to it"
     */

    @Test()
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("DB Manager")
    @Issue("113666")
    public void dbManager_TC008() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Test Case 113666: Add DB flow - Verify that Output Flow DDL is displayed if the type is 'INPUT' & it displays the correct values"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Test Case 113666: Add DB flow - Verify that Output Flow DDL is displayed if the type is 'INPUT' & it displays the correct values"));
            create_db_scan_table();


            Allure.step("Get all output flows from the DB");
            List<String> output_flows_from_DB = screeningServicesDelegate.get_db_flows_list(Common.ZONE.COMMON_TEST_ZONE_009.getZone().getDisplayName(), "OUTPUT");
            List<String> output_flows_from_UI = dbFlowConfigControl.get_all_output_db_types(driver);
            output_flows_from_UI.remove("< None >");


            Assert.assertEquals(output_flows_from_DB.size(), output_flows_from_UI.size(), "Output flows  count not match in the DB and UI");
            Assert.assertEqualsNoOrder(output_flows_from_DB, output_flows_from_UI, "Output flows are not displayed correctly");
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod()
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
