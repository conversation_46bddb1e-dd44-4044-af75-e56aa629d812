package eastnets.screening.regression.dbmanager;

import core.ExceptionHandler;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.dbManager.DBFlowConfigControl;
import io.qameta.allure.Allure;
import io.qameta.allure.Feature;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class DBManager_TC011 extends eastnets.screening.regression.dbmanager.Common {

    // Instance variables for page objects and controls
    private LoginPage loginPage = new LoginPage();
    private CommonAction commonAction = new CommonAction();
    private DBFlowConfigControl dbFlowConfigControl = new DBFlowConfigControl();

    @BeforeMethod()
    public void login() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_9.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_9.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test covers the below cases:
     * Test Case 113669: Add DB flow - Verify that cancel button functionality is working properly
     * Steps:
     * 1- navigate to DB Manager
     * 2- From Database Flow Configuration tab, Click on New button
     * 3- Click on Cancel button
     * 4- System should redirect the user to main page "Database Flow Configuration"
     */

    @Test()
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("DB Manager")
    @Issue("113669")
    public void dbManager_TC011() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Test Case 113669: Add DB flow - Verify that cancel button functionality is working properly"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Test Case 113669: Add DB flow - Verify that cancel button functionality is working properly"));

            Assert.assertTrue(dbFlowConfigControl.cancel_db_configurations(driver)
                    ,"Cancel button functionality is not working properly");

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod()
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
