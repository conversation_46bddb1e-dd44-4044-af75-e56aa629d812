package eastnets.screening.regression.dbmanager;

import core.ExceptionHandler;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.dbManager.DBFlowConfigControl;
import io.qameta.allure.Allure;
import io.qameta.allure.Feature;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.util.List;

public class DBManager_TC007 extends eastnets.screening.regression.dbmanager.Common {

    // Instance variables for page objects and controls
    private LoginPage loginPage = new LoginPage();
    private CommonAction commonAction = new CommonAction();
    private DBFlowConfigControl dbFlowConfigControl = new DBFlowConfigControl();

    @BeforeMethod()
    public void login() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_1.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test covers the below cases:
     * Test Case : 113665 - Add DB flow - Verify that Zone DDL displays the correct values
     * Steps:
     * 1- navigate to DB Manager
     * 2- Click add new DB flow
     * 3- Check Zone drop down list
     * 4- It should display only the user's zones "any zone the user has access to it"
     */

    @Test()
    @Owner("Sara Abdellatif")
    @Tag("Regression")
    @Feature("DB Manager")
    @Issue("113665")
    public void dbManager_TC007() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Test Case 113665: Add DB flow - Verify that Zone DDL displays the correct values"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Test Case 113665: Add DB flow - Verify that Zone DDL displays the correct values"));

            List<String> available_zones_list = dbFlowConfigControl.get_all_zones(driver);
            Assert.assertEquals(available_zones_list.size()
                    ,2
                    ,"The zones list should contain only the user's zones");
            Assert.assertTrue(available_zones_list.contains(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName())
                    ,"The zones list should contain the common zone");
            Assert.assertTrue(available_zones_list.contains(Common.ZONE.SYSTEM_ZONE.getZone().getDisplayName())
                    ,"The zones list should contain the system zone");
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod()
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
