package eastnets.screening.regression.reports;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.ReportsControl;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class Report_TC009 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final NameScanControl nameScanControl = new NameScanControl();
    private final ReportsControl reportsControl = new ReportsControl();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();
    private final core.util.TextFilesHandler textFilesHandler = new core.util.TextFilesHandler();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_8.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_8.getOperator().getPassword()
                   );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test Case : Verify Detections Grouped By Investigator.
     * Steps:
     * 1- Import " Iran List" and link it to a new list set
     * 2- Perform name scan for "7th of Tir" for 4 times
     * 3- Create a new report with the type "Detections Grouped By Investigator"
     * 5- Assert that the report status is done
     * 6- Download the report and verify that its successfully downloaded
     * 7- verify report content
     */

    @Test()
    @Owner("Sarah Abdellatif")
    @Tag("Regression")
    @Tag("Reports")
    public void report_TC009() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify Detections Grouped By Investigator."));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify Detections Grouped By Investigator."));

            EnList list = commonTestMethods.getEnListData();
            list.setName(GeneralConstants.IRAN_LIST_NAME);
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_008.getZone().getDisplayName());
            String filePath = GeneralConstants.IRAN_LIST_FILE_PATH;

            screeningServicesDelegate.deleteLinkBtwBlackListAndListSet(list.getZoneName(), list.getName());
            screeningServicesDelegate.deleteLinkBtwBlackListAndGoodGuy(list.getZoneName(), list.getName());
            screeningServicesDelegate.deleteBlackList(list.getName());

            commonTestMethods.importBListAndLinkToNewListSet(driver, list, Common.PROFILE.FULL_RIGHT_008.getProfile().getName(), filePath);


            Assert.assertNotNull(nameScanControl.scan_Name(driver
                            , "7th of Tir"
                            , Common.ZONE.DEFAULT_ZONE.getZone().getDisplayName()
                            , null
                            , true
                            , true
                            , true
                            , false)
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Assert.assertNotNull(nameScanControl.scan_Name(driver
                            , "7th of Tir"
                            , Common.ZONE.DEFAULT_ZONE.getZone().getDisplayName()
                            , null
                            , true
                            , true
                            , true
                            , false)
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Assert.assertNotNull(nameScanControl.scan_Name(driver
                            , "7th of Tir"
                            , Common.ZONE.DEFAULT_ZONE.getZone().getDisplayName()
                            , null
                            , true
                            , true
                            , true
                            , false)
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Assert.assertNotNull(nameScanControl.scan_Name(driver
                            , "7th of Tir"
                            , Common.ZONE.DEFAULT_ZONE.getZone().getDisplayName()
                            , null
                            , true
                            , true
                            , true
                            , false)
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            String comment = "comment" + randomizer.getInt();

            reportsControl.createDetectionGroupedByInvestigatorReport(driver, "Detections Grouped By Investigator", comment
                    , "PDF", randomizer.currentDate2 + " 00:00:00", randomizer.currentDate2 + " 23:59:59");
            Assert.assertEquals(reportsControl.getReportStatus(driver, "Detections Grouped By Investigator", "PDF", comment, "Done")
                    , "Done"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            clearDownloadedFiles();
            String downloadPath = "rptDetectionsGroupedByInvestigator.pdf";
            Assert.assertTrue(reportsControl.downloadReport(driver, "Detections Grouped By Investigator", comment, downloadPath, dockerServerIp)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            textFilesHandler.move_file_to_local_dir(driver, downloadPath, browserDefaultDownloadPath, dockerServerIp);

            String fileContent = textFilesHandler.readPDF(System.getProperty("user.dir")
                    + GeneralConstants.DOWNLOAD_FILE_PATH
                    + "/" + downloadPath);

            System.out.println(fileContent);

            Assert.assertTrue(fileContent.contains(Common.OPERATOR.FULL_RIGHT_8.getOperator().getLoginName()), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertTrue(fileContent.contains(String.format("From Date %s 00:00:00", randomizer.currentDate)), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertTrue(fileContent.contains(String.format("To Date %s 23:59:59", randomizer.currentDate)), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
