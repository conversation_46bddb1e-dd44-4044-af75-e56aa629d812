package eastnets.screening.regression.reports;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.ReportsControl;
import eastnets.screening.control.listManager.ListExplorerControl;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class Report_TC013 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ListExplorerControl listExplorerControl = new ListExplorerControl();
    private final ReportsControl reportsControl = new ReportsControl();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();
    private final core.util.TextFilesHandler textFilesHandler = new core.util.TextFilesHandler();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_8.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_8.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test Case : Verify List Set Info Report
     * Steps:
     * 1- Create new list set
     * 2- create new entry with the name "Khaled Hussam"
     * 3- Delete the created entry
     * 4- Create a new report with the type "Lists Updates Events"
     * 5- Assert that the report status is done
     * 6- Download the report and verify that its successfully downloaded
     * 7- verify report content
     */

    @Test()
    @Owner("Sarah Abdellatif")
    @Tag("Regression")
    @Tag("Reports")
    public void report_TC013() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify Lists Updates Events"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify Lists Updates Events"));

            EnList list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_008.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, list, Common.PROFILE.FULL_RIGHT_008.getProfile().getName());

            String entryName = list.getEntry().get(0).getName();
            list.getEntry().get(0).setFirstName(null);
            commonTestMethods.createNewEntryWithTypeAndNames(driver, list);


            list.getEntry().get(0).setName(entryName + "1");
            commonTestMethods.createNewEntryWithTypeAndNames(driver, list);

            Assert.assertEquals(listExplorerControl.delete_entry(driver, list.getZoneName(), list.getName(), entryName),
                    "Operation completed successfully.",
                    GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


            String comment = "comment" + randomizer.getInt();
            reportsControl.createNewReport(driver, "Lists Updates Events", comment
                    , "PDF", null, null, list.getName(), null, null);
            Assert.assertEquals(reportsControl.getReportStatus(driver, "Lists Updates Events", "PDF", comment, "Done")
                    , "Done"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            clearDownloadedFiles();
            String downloadPath = "rptListsUpdatesEvents.pdf";
            Assert.assertTrue(reportsControl.downloadReport(driver, "Lists Updates Events", comment, downloadPath, dockerServerIp)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            textFilesHandler.move_file_to_local_dir(driver, downloadPath, browserDefaultDownloadPath, dockerServerIp);

            String fileContent = textFilesHandler.readPDF(System.getProperty("user.dir")
                    + GeneralConstants.DOWNLOAD_FILE_PATH
                    + "/" + downloadPath);

            System.out.println(fileContent);

            Assert.assertTrue(fileContent.contains("List Updates Events"), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertTrue(fileContent.contains(list.getName()), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertTrue(fileContent.contains("Individual Added"), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertTrue(fileContent.contains(String.format("%s Deleted",
                    entryName)), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertTrue(fileContent.contains("Total Number of Deleted : 1"), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertTrue(fileContent.contains("# Deleted non-Hit Entities : 1"), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
