package eastnets.screening.regression.reports;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.ReportsControl;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Issue;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class TC_105773 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final NameScanControl nameScanControl = new NameScanControl();
    private final ReportsControl reportsControl = new ReportsControl();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();
    private final core.util.TextFilesHandler textFilesHandler = new core.util.TextFilesHandler();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_8.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_8.getOperator().getPassword()
            );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test Case 105773: Verify Display of Chinese name in Alert list report on [WIN - Tomcat]
     * Preconditions:
     * 1- Make sure that user profile has access to Reports for report manager.
     * 2- Make sure to add the attached jar file (jasper-noto-fonts-1.0.4-en.jar) in the config folder.
     * 3- Make sure to update catalina.bat for Tomcat bin folder:
     *      From: set CLASSPATH=
     *      To: set CLASSPATH=%EASTNETS_CONFIG_HOME%\jasper-noto-fonts-1.0.4-en.jar
     *Steps:
     * 1. Log in to the AMLUI.
     * 2. Add an entity with a Chinese name.
     * 3. Example: 张永明 文杰
     * 4. Navigate to the "Report Manager" module.
     * 5. Select the "Alert List" report.
     * 6. Generate a report with PDF and CSV format.
     * 7. Expected Result: The Chinese name should be displayed correctly in the
     *    CSV file without any corruption or replacement symbols.
     *
     */
    @Test()
    @Owner("Sarah Abdellatif")
    @Tag("Regression")
    @Tag("Reports")
    @Issue("105773")
    public void tc_105773() {
        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Test Case 105773: Verify Display of Chinese name in Alert list report on [WIN - Tomcat]"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Test Case 105773: Verify Display of Chinese name in Alert list report on [WIN - Tomcat]"));
            String chineseName = "张永明 文杰";
            EnList list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_008.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, list, Common.PROFILE.FULL_RIGHT_008.getProfile().getName());

            list.getEntry().get(0).setName(chineseName);
            list.getEntry().get(0).setFirstName(null);
            commonTestMethods.createNewEntryWithTypeAndNames(driver, list);

            nameScanControl.scan_Name(driver
                    , chineseName
                    , list.getZoneName()
                    , null
                    ,true
                    , true
                    , true
                    , false);

            String comment = "comment" + randomizer.getInt();
            reportsControl.createNewReportWithSessionData(driver, "Alert list", comment, "PDF", null, null);
            Assert.assertEquals(reportsControl.getReportStatus(driver, "Alert list", "PDF", comment, "Done")
                    , "Done"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            clearDownloadedFiles();
            String downloadPath = "rptAlertList.pdf";
            Assert.assertTrue(reportsControl.downloadReport(driver, "Alert list", comment, downloadPath, dockerServerIp)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            textFilesHandler.move_file_to_local_dir(driver, downloadPath, browserDefaultDownloadPath, dockerServerIp);

            String fileContent = textFilesHandler.readPDF(System.getProperty("user.dir")
                    + GeneralConstants.DOWNLOAD_FILE_PATH
                    + "/" + downloadPath);

            System.out.println(fileContent);

            Assert.assertTrue(fileContent.contains("Alert List"), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertTrue(fileContent.contains(chineseName), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
