package eastnets.screening.regression.reports;

import com.fasterxml.jackson.databind.ObjectMapper;
import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.control.ReportsControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FileScan;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

import java.io.File;

public class Report_TC015 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ReportsControl reportsControl = new ReportsControl();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();
    private final core.util.TextFilesHandler textFilesHandler = new core.util.TextFilesHandler();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_8.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_8.getOperator().getPassword()
                  );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test Case : Verify Violation Overview Report
     * Steps:
     * 1- Create new list set
     * 2- create new entry with the name "Khaled Hussam"
     * 3- Scan generic file =UPLOAD_FILE_PATH+"/ CompareSessionsInfo. txt
     * 4- Create a new report with the type "Violation overview"
     * 5- Assert that the report status is done
     * 6- Download the report and verify that its successfully downloaded
     * 7- verify report content
     */

    @Test()
    @Owner("Sarah Abdellatif")
    @Tag("Regression")
    @Tag("Reports")
    public void report_TC015() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify Violation Overview Report"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify Violation Overview Report"));
            String entryName = "Khaled Hussam";
            EnList list = commonTestMethods.getEnListData();
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_008.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, list, Common.PROFILE.FULL_RIGHT_008.getProfile().getName());

            list.getEntry().get(0).setName(entryName);
            list.getEntry().get(0).setFirstName(null);
            commonTestMethods.createNewEntryWithTypeAndNames(driver, list);

            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("blockDetection");
            FileScan fileScan = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), FileScan.class);
            fileScan.setFilePath(GeneralConstants.COMPARE_SESSION_INFO_FILE_PATH);
            fileScan.setFormat("Generic");

            String detectionId = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            String comment = "comment" + randomizer.getInt();
            reportsControl.createNewReportWithSessionData(driver, "Violation overview", comment
                    , "PDF", null, null);
            Assert.assertEquals(reportsControl.getReportStatus(driver, "Violation overview", "PDF", comment, "Done")
                    , "Done"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            clearDownloadedFiles();
            String downloadPath = "rptViolationOverview.pdf";
            Assert.assertTrue(reportsControl.downloadReport(driver, "Violation overview", comment, downloadPath, dockerServerIp)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            textFilesHandler.move_file_to_local_dir(driver, downloadPath, browserDefaultDownloadPath, dockerServerIp);

            String fileContent = textFilesHandler.readPDF(System.getProperty("user.dir")
                    + GeneralConstants.DOWNLOAD_FILE_PATH
                    + "/" + downloadPath);

            System.out.println(fileContent);


            Assert.assertTrue(fileContent.contains("Violation Overview"), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertTrue(fileContent.contains("Det ID Scanned Data Violation Rank User Application"), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertTrue(fileContent.contains(String.format("%s %s %s 100 %s File Scanner",
                    detectionId, entryName, entryName, Common.OPERATOR.FULL_RIGHT_8.getOperator().getLoginName())), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
