package eastnets.screening.regression.reports;

import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.ReportsControl;
import eastnets.screening.control.listManager.BlackListControl;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import io.qameta.allure.testng.Tag;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;

public class Report_TC007 extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final ScreeningServicesDelegate screeningServicesDelegate = new ScreeningServicesDelegate();
    private final BlackListControl blackListControl = new BlackListControl();
    private final ReportsControl reportsControl = new ReportsControl();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();
    private final core.util.TextFilesHandler textFilesHandler = new core.util.TextFilesHandler();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(alwaysRun = true)
    public void login() {
        try {
            loginPage.login(driver
                    , Common.OPERATOR.FULL_RIGHT_8.getOperator().getLoginName()
                    , Common.OPERATOR.FULL_RIGHT_8.getOperator().getPassword()
                    );
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    /**
     * Test Case : Verify Compare Black Lists Versions Report
     * Steps:
     * 1- Import "UN Yemen" blacklist and link it to a new list set
     * 2- import upgraded version of "UN Yemen" blacklist
     * 4- Create a new report with the type "Compare Black Lists Versions"
     * 5- Assert that the report status is done
     * 6- Download the report and verify that its successfully downloaded
     * 7- verify report content
     */

    @Test()
    @Owner("Sarah Abdellatif")
    @Tag("Regression")
    @Tag("Reports")
    public void report_TC007() {

        try {
            lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify Compare Black Lists Versions Report"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify Compare Black Lists Versions Report"));

            EnList list = commonTestMethods.getEnListData();
            list.setName(GeneralConstants.UN_YAMEN_LIST_NAME);
            list.setZoneName(Common.ZONE.COMMON_TEST_ZONE_008.getZone().getDisplayName());
            String filePath = System.getProperty("user.dir") + GeneralConstants.UN_YAMEN_LIST_FILE_PATH;

            screeningServicesDelegate.deleteLinkBtwBlackListAndListSet(list.getZoneName(), list.getName());
            screeningServicesDelegate.deleteLinkBtwBlackListAndGoodGuy(list.getZoneName(), list.getName());
            screeningServicesDelegate.deleteBlackList(list.getName());

            commonTestMethods.importBListAndLinkToNewListSet(driver, list, Common.PROFILE.FULL_RIGHT_008.getProfile().getName(), filePath);

            filePath = System.getProperty("user.dir") + GeneralConstants.UN_YAMEN2_LIST_FILE_PATH;
            Assert.assertEquals(blackListControl.import_list(driver, list.getZoneName(), filePath, false, true)
                    , "SUCCEEDED");


            String comment = "comment" + randomizer.getInt();

            reportsControl.createNewReport(driver, "Compare Black Lists Versions", comment
                    , "PDF", list.getZoneName(), "select", list.getName(), "UN Yemen_Thu Nov", "UN Yemen_Wed Sep");
            Assert.assertEquals(reportsControl.getReportStatus(driver, "Compare Black Lists Versions", "PDF", comment, "Done")
                    , "Done"
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);

            clearDownloadedFiles();
            String downloadPath = "rptCompareListVersions.pdf";
            Assert.assertTrue(reportsControl.downloadReport(driver, "Compare Black Lists Versions", comment, downloadPath, dockerServerIp)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG);
            textFilesHandler.move_file_to_local_dir(driver, downloadPath, browserDefaultDownloadPath, dockerServerIp);

            String fileContent = textFilesHandler.readPDF(System.getProperty("user.dir")
                    + GeneralConstants.DOWNLOAD_FILE_PATH
                    + "/" + downloadPath);

            System.out.println(fileContent);


            Assert.assertTrue(fileContent.contains("Version1 UN Yemen (version: 2014/11/20)"), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertTrue(fileContent.contains("Version2 UN Yemen (version: 2015/09/16)"), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertTrue(fileContent.contains("# Modified items 3"), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertTrue(fileContent.contains("# Added items 2"), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertTrue(fileContent.contains("# Removed items 0"), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertTrue(fileContent.contains("AL-HOUTHI, ABDULMALIK"), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertTrue(fileContent.contains("SALEH, AHMED ALI ABDULLAH"), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);


        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod(alwaysRun = true)
    public void logout() {
        try {
            commonAction.logout(driver);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }
}
