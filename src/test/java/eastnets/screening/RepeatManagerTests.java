package eastnets.screening;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.paypal.selion.platform.dataprovider.DataProviderFactory;
import com.paypal.selion.platform.dataprovider.DataResource;
import com.paypal.selion.platform.dataprovider.SeLionDataProvider;
import com.paypal.selion.platform.dataprovider.impl.InputStreamResource;
import core.BaseTest;
import core.CommonTestMethods;
import core.ExceptionHandler;
import core.constants.screening.GeneralConstants;
import core.util.Wait;
import eastnets.admin.entity.Common;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.LoginPage;
import eastnets.common.gui.Navigation;
import eastnets.screening.control.AdvancedSettingsControls.AdvancedSettingsControl;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.control.RepeatManagerControl;
import eastnets.screening.control.listManager.BlackListControl;
import eastnets.screening.control.scanManger.NameScanControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.FileScan;
import eastnets.screening.entity.Repeat;
import eastnets.screening.entity.RepeatedData;
import eastnets.screening.gui.advancedSettings.FourEyesSettingsManager;
import eastnets.screening.gui.detectionManager.DetectionManager;
import io.qameta.allure.Allure;
import io.qameta.allure.AllureLifecycle;
import io.qameta.allure.Owner;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.List;

public class RepeatManagerTests extends BaseTest {

    // Instance variables for page objects and controls
    private final LoginPage loginPage = new LoginPage();
    private final CommonAction commonAction = new CommonAction();
    private final AdvancedSettingsControl advancedSettingsControl = new AdvancedSettingsControl();
    private final DetectionManagerControl detectionManagerControl = new DetectionManagerControl();
    private final RepeatManagerControl repeatManagerControl = new RepeatManagerControl();
    private final BlackListControl blackListControl = new BlackListControl();
    private final NameScanControl nameScanControl = new NameScanControl();
    private final CommonTestMethods commonTestMethods = new CommonTestMethods();
    private final core.util.Randomizer randomizer = new core.util.Randomizer();
    private final Wait wait = new Wait();
    private final FourEyesSettingsManager fourEyesSettingsManager = new FourEyesSettingsManager();
    private final DetectionManager detectionManager = new DetectionManager();

    private RemoteWebDriver driver;

    @BeforeClass(description = "Initialize Driver")
    public void initializeDriver() {
        try {
            driver = getDriver();
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod()
    @Step("Login to filtering.")
    public void loginByFullRightOperator() {
        try {
            driver.navigate().refresh();
            loginPage.login(driver,
                    Common.OPERATOR.FULL_RIGHT_1.getOperator().getLoginName(),
                    Common.OPERATOR.FULL_RIGHT_1.getOperator().getPassword());
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @BeforeMethod(groups = {"noRepeatManagerRight"})
    @Step("Login to filtering.")
    public void loginByNoRepeatManagerRightOperator() {
        try {
            loginPage.login(driver,
                    Common.OPERATOR.NO_REPEAT_MANAGER_RIGHT.getOperator().getLoginName(),
                    Common.OPERATOR.NO_REPEAT_MANAGER_RIGHT.getOperator().getPassword());
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    //Regression_RepeatMgr_01
    @Test(priority = 0, dataProvider = "repeatDifferentSubTypesTestData", enabled = true, groups = {"regression", "repeatManagerDefault"})
    @Owner("Tarek Allam")
    public void repeatFunction(Repeat repeat) {

        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that user is able to create a Simple work flow of repeat function by clicking on repeat button"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that user is able to create a Simple work flow of repeat function by clicking on repeat button"));

            EnList enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, enlist, Common.PROFILE.FULL_RIGHT_001.getProfile().getName());
            enlist.getEntry().get(0).setName("DIE SCHWEIZERISCHE POST");
            enlist.getEntry().get(0).setFirstName(null);
            commonTestMethods.createNewEntryWithTypeAndNames(driver, enlist);
            EnList sharedList = enlist;
            sharedList.setName(sharedList.getName() + "1");
            Assert.assertTrue(blackListControl.create_Black_List(driver, sharedList)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new black list.");

            Assert.assertNull(blackListControl.share_black_list(driver, sharedList.getName())
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while share black list.");

            repeatManagerControl.deleteAllRepeatConfigurations(driver);
            repeat.setListName(sharedList.getName());
            repeat.setZone(sharedList.getZoneName());
            repeat.setConfigName(repeat.getConfigName() + randomizer.getInt());
            repeat.setType("103");
            repeat.setSubType("");
            repeat.setRule1("50K");
            Assert.assertEquals(repeatManagerControl.createRepeatConfig(driver, repeat)
                    , "Configuration effects will take place after restarting SafeWatch Screening solution!"
                    , GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Navigation.ADVANCED_SETTINGS.navigate(driver);
            advancedSettingsControl.navigateToFourEyesSettings(driver);
            fourEyesSettingsManager.disableFourEyes(driver);

            commonTestMethods.restartNewArch();

            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("blockDetection");
            FileScan fileScan = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), FileScan.class);
            fileScan.setFilePath(GeneralConstants.RJE_REPEAT_SCAN_FILE);
            fileScan.setResult("All suspected records");
            String detectionID = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            //repeatDetection
            String expectedResults = "Operation completed successfully.";
            Assert.assertEquals(detectionManagerControl.search_by_id(driver, detectionID), repeat.getStatus(), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.repeat_detection(driver, detectionID, String.format(repeat.getMassage(), detectionID)), expectedResults, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            List<RepeatedData> repeatedDataResult = repeatManagerControl.getRepeatData(driver);
            Assert.assertEquals(repeatedDataResult.get(0).getRemarks(), String.format(repeat.getRemarks(), detectionID), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            fileScan.setFilePath(GeneralConstants.RJE_REPEAT_SCAN_FILE);
            wait.time(Wait.ONE_SECOND * 10);
            detectionID = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            //verifyRepeatDetection
            Assert.assertEquals(detectionManagerControl.search_by_id(driver, detectionID), repeat.getStatus(), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManager.get_violation(driver), repeat.getViolation(), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.get_matched_entity(driver), repeat.getViolation(), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Assert.assertEquals(detectionManagerControl.get_related_message_detection_id(driver), "Related message detection ID:", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.get_related_message_detection_status(driver), "Related message detection status:", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    //Regression_RepeatMgr_03
    @Test(priority = 1, dataProvider = "repeatDifferentSubTypesTestData", enabled = true, groups = {"regression", "repeatManagerDefault"})
    @Owner("Tarek Allam")
    public void duplicatedRepeatedMT(Repeat repeat) {

        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Ensure that only one configuration can be created per each MT per zone"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Ensure that only one configuration can be created per each MT per zone"));

            EnList sharedList = commonTestMethods.getEnListData();
            Assert.assertTrue(blackListControl.create_Black_List(driver, sharedList)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new black list.");

            Assert.assertNull(blackListControl.share_black_list(driver, sharedList.getName())
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while share black list.");

            repeatManagerControl.deleteAllRepeatConfigurations(driver);
            repeat.setListName(sharedList.getName());
            repeat.setZone(sharedList.getZoneName());
            repeat.setConfigName(repeat.getConfigName() + randomizer.getInt());
            repeat.setType("103");

            Assert.assertEquals(repeatManagerControl.createRepeatConfig(driver, repeat)
                    , repeat.getExpectedMessage(), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            repeat.setConfigName(repeat.getConfigName() + "1");
            repeat.setExpectedMessage("Stripping configuration already exists for this message type");

            Assert.assertEquals(repeatManagerControl.createRepeatConfigDuplicate(driver, repeat)
                    , repeat.getExpectedMessage(), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            repeat.setType("202");
            repeat.setSubType("COV");
            repeat.setExpectedMessage("Configuration effects will take place after restarting SafeWatch Screening solution!");
            Assert.assertEquals(repeatManagerControl.createRepeatConfig(driver, repeat)
                    , repeat.getExpectedMessage(), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    //Regression_RepeatMgr_04
    @Test(priority = 2, dataProvider = "repeatDifferentSubTypesTestData", enabled = false, groups = {"regression", "repeatManagerDefault"})
    @Owner("Tarek Allam")
    public void sameNameConfigs(Repeat repeat) {

        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Create 2 configurations having the same name but under different zones"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Create 2 configurations having the same name but under different zones"));


            EnList sharedList = commonTestMethods.getEnListData();
            Assert.assertTrue(blackListControl.create_Black_List(driver, sharedList)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new black list.");

            Assert.assertNull(blackListControl.share_black_list(driver, sharedList.getName())
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while share black list.");


            repeatManagerControl.createRepeatConfig(driver, repeat);
            Navigation.ADVANCED_SETTINGS.navigate(driver);
            advancedSettingsControl.navigateToFourEyesSettings(driver);
            fourEyesSettingsManager.disableFourEyes(driver);

            repeatManagerControl.deleteAllRepeatConfigurations(driver);
            Assert.assertEquals(repeatManagerControl.createRepeatConfig(driver, repeat)
                    , repeat.getExpectedMessage(), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    //Regression_RepeatMgr_05
    @Test(priority = 3, dataProvider = "repeatDifferentSubTypesTestData", enabled = true, groups = {"regression", "repeatManagerDefault"})
    @Owner("Tarek Allam")
    public void differentSubTypes(Repeat repeat) {

        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that user is able to create MT 202 with different sub Type"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that user is able to create MT 202 with different sub Type"));

            EnList sharedList = commonTestMethods.getEnListData();
            Assert.assertTrue(blackListControl.create_Black_List(driver, sharedList)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new black list.");

            Assert.assertNull(blackListControl.share_black_list(driver, sharedList.getName())
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while share black list.");

            repeat.setListName(sharedList.getName());
            repeat.setZone(sharedList.getZoneName());
            repeat.setConfigName(repeat.getConfigName() + randomizer.getInt());

            Assert.assertTrue(blackListControl.create_Black_List(driver, sharedList)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new black list.");

            repeatManagerControl.deleteAllRepeatConfigurations(driver);
            Assert.assertEquals(repeatManagerControl.createRepeatConfig(driver, repeat)
                    , repeat.getExpectedMessage(), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            repeat.setConfigName(repeat.getConfigName() + "1");
            repeat.setSubType("COV");
            Assert.assertEquals(repeatManagerControl.createRepeatConfig(driver, repeat)
                    , repeat.getExpectedMessage(), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    //Regression_RepeatMgr_07
    @Test(priority = 5, enabled = true, groups = {"regression", "noRepeatManagerRight"})
    @Owner("Tarek Allam")
    public void noAccessRights() {

        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that the repeat manager will not displayed if the user have no access rights \n" +
                    "Verify that the repeat button will not displayed on the detection manager under the detections have not come from RJE message"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that the repeat manager will not displayed if the user have no access rights \n" +
                    "Verify that the repeat button will not displayed on the detection manager under the detections have not come from RJE message"));

            EnList enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, enlist, Common.PROFILE.NO_REPEAT_RIGHT.getProfile().getName());

            commonTestMethods.createNewEntryWithTypeAndNames(driver, enlist);

            String detectionID = nameScanControl.scan_Name(driver
                    , enlist.getEntry().get(0).getName()
                    , Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName()
                    , "75"
                    , true
                    , true
                    , true
                    , false);
            Assert.assertNotNull(detectionID, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Navigation.verifyRepeatManagerExists(driver, false);
            Assert.assertFalse(detectionManagerControl.verify_repeat_button_exists(driver), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    //Regression_RepeatMgr_08
    @Test(priority = 6, dataProvider = "repeatDifferentSubTypesTestData", enabled = true, groups = {"regression", "repeatManagerDefault"})
    @Owner("Tarek Allam")
    public void repeatBlockButton(Repeat repeat) {

        try {
            AllureLifecycle lifecycle = Allure.getLifecycle();
            lifecycle.updateTestCase(testResult -> testResult.setDescription("Verify that user is able to create a Simple work flow of repeat function by clicking on block button"));
            lifecycle.updateTestCase(testResult -> testResult.setName("Verify that user is able to create a Simple work flow of repeat function by clicking on block button"));

            EnList enlist = commonTestMethods.getEnListData();
            enlist.setZoneName(Common.ZONE.COMMON_TEST_ZONE_001.getZone().getDisplayName());
            commonTestMethods.createListSet(driver, enlist, Common.PROFILE.FULL_RIGHT_001.getProfile().getName());
            enlist.getEntry().get(0).setName("DIE SCHWEIZERISCHE POST");
            enlist.getEntry().get(0).setFirstName(null);

            commonTestMethods.createNewEntryWithTypeAndNames(driver, enlist);
            EnList sharedList = enlist;
            sharedList.setName(sharedList.getName() + "1");
            Assert.assertTrue(blackListControl.create_Black_List(driver, sharedList)
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while creating new black list.");

            Assert.assertNull(blackListControl.share_black_list(driver, sharedList.getName())
                    , GeneralConstants.POM_EXCEPTION_ERR_MSG + " while share black list.");

            repeatManagerControl.deleteAllRepeatConfigurations(driver);
            repeat.setListName(sharedList.getName() + "1");
            repeat.setZone(sharedList.getZoneName());
            repeat.setConfigName(repeat.getConfigName() + randomizer.getInt());
            repeat.setType("103");
            repeat.setSubType("");
            repeat.setRule1("50K");
            repeatManagerControl.createRepeatConfig(driver, repeat);

            Navigation.ADVANCED_SETTINGS.navigate(driver);
            advancedSettingsControl.navigateToFourEyesSettings(driver);
            fourEyesSettingsManager.disableFourEyes(driver);

            commonTestMethods.restartNewArch();

            ObjectMapper mapper = new ObjectMapper();
            String csvDataFilePath = screeningTestDataConfigsProps.getProperty("blockDetection");
            FileScan fileScan = mapper.readValue(new File(System.getProperty("user.dir") + csvDataFilePath), FileScan.class);
            fileScan.setFilePath(GeneralConstants.RJE_REPEAT_SCAN_FILE);
            fileScan.setResult("All suspected records");
            String detectionID = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            //takeDecision
            String expectedResults = "1 detection(s) successfully modified!";
            Assert.assertEquals(detectionManagerControl.search_by_id(driver, detectionID), repeat.getStatus(), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.block_detection(driver, detectionID, "Repeated by Block Button"), expectedResults, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Assert.assertEquals(repeatManagerControl.getRepeatData(driver).get(0).getRemarks(), "Automatic repeat from 'Real Violation'", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            wait.time(Wait.ONE_SECOND * 10);
            fileScan.setFilePath(GeneralConstants.RJE_REPEAT_SCAN_FILE);
            detectionID = commonTestMethods.scanFileAndGetDetectionID(driver, fileScan);

            Assert.assertNotNull(detectionID, GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            //verifyRepeatDetection
            Assert.assertEquals(detectionManagerControl.search_by_id(driver, detectionID), repeat.getStatus(), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManager.get_violation(driver), repeat.getViolation(), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.get_matched_entity(driver), repeat.getMatchedEntity(), GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);

            Assert.assertEquals(detectionManagerControl.get_related_message_detection_id(driver), "Related message detection ID:", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
            Assert.assertEquals(detectionManagerControl.get_related_message_detection_status(driver), "Related message detection status:", GeneralConstants.ACTUAL_EXPECTED_ERR_MSG);
        } catch (Exception e) {
            ExceptionHandler.onExceptionRaised(e
                    , new Object() {
                    }.getClass().getName()
                    , new Object() {
                    }.getClass().getEnclosingMethod().getName());
        }
    }

    @AfterMethod()
    @Step("Logout from filtering.")
    public void logout() {
        driver.navigate().refresh();
        commonAction.logout(driver);
    }

    @DataProvider(name = "repeatDifferentSubTypesTestData")
    public Object[][] repeatDifferentSubTypesTestData() throws IOException {
        String csvDataFilePath = screeningTestDataConfigsProps.getProperty("repeatDifferentSubTypes");
        DataResource resource =
                new InputStreamResource(new FileInputStream(System.getProperty("user.dir") + csvDataFilePath),
                        Repeat.class, "json");
        SeLionDataProvider dataProvider =
                DataProviderFactory.getDataProvider(resource);
        return dataProvider.getAllData();
    }
}