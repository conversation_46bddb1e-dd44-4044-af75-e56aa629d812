<?xml version="1.0" encoding="UTF-8"?>
<XML>
    <LICENSE>
        <LICENSE_INFO>
            <KEY>30B1-0709-C286-3C84</KEY>
            <STATION_CONNECTIONS>5</STATION_CONNECTIONS>
            <SERVER_INSTANCES>1</SERVER_INSTANCES>
            <SCAN_VOLUME></SCAN_VOLUME>
            <CLIENT_ID>1006</CLIENT_ID>
            <CLIENT_NAME>Eastnets - Jordan</CLIENT_NAME>
            <PRODUCT_EDITION>Enterprise</PRODUCT_EDITION>
            <UNICODE_DATABASE>Yes</UNICODE_DATABASE>
            <GRACE_PERIOD>60</GRACE_PERIOD>
            <LICENSED_BICS>
                <BIC>ABNAMXMMXXX</BIC>
                <BIC>BARCGB22XXX</BIC>
                <BIC>BFICCUHHXXX</BIC>
                <BIC>BICIGALXXXX</BIC>
                <BIC>BICISNDXXXX</BIC>
                <BIC>BNLIITRRXXX</BIC>
                <BIC>BNPAFRPPXXX</BIC>
                <BIC>BNPAPLPXXXX</BIC>
                <BIC>BNPAUS3NXXX</BIC>
                <BIC>BOFAGB22XXX</BIC>
                <BIC>BPTSAAEAXXX</BIC>
                <BIC>BPXAAEAAXXX</BIC>
                <BIC>BPXAAEAOXXX</BIC>
                <BIC>BSILCH22XXX</BIC>
                <BIC>BTEJFRPPXXX</BIC>
                <BIC>CITIGB2LXXX</BIC>
                <BIC>COSRPAPAXXX</BIC>
                <BIC>GIBAHUHBXXX</BIC>
                <BIC>IRVTUS3NXXX</BIC>
                <BIC>KOEXNL2AXXX</BIC>
                <BIC>MHCBJPJTXXX</BIC>
                <BIC>MMEBMTMTXXX</BIC>
                <BIC>NATAAU33XXX</BIC>
                <BIC>NCBKSAJEXXX</BIC>
                <BIC>PARBGB2LXXX</BIC>
                <BIC>PTSABEMMXXX</BIC>
                <BIC>RABOAUSSXXX</BIC>
                <BIC>SLIIGB2LXXX</BIC>
                <BIC>UBCITNTTXXX</BIC>
                <BIC>UBSWGB2LXXX</BIC>
            </LICENSED_BICS>
        </LICENSE_INFO>
        <APPLICATION_LIST>
            <APPLICATION>
                <NAME>DBConnector Scanner</NAME>
                <LICENSE_STATUS>Yes</LICENSE_STATUS>
                <EXPIRATION_DATE></EXPIRATION_DATE>
                <SCAN_QUOTA></SCAN_QUOTA>
                <PERIOD_OF_TIME></PERIOD_OF_TIME>
                <NB_OF_WARNINGS></NB_OF_WARNINGS>
            </APPLICATION>
            <APPLICATION>
                <NAME>File Scanner</NAME>
                <LICENSE_STATUS>Yes</LICENSE_STATUS>
                <EXPIRATION_DATE></EXPIRATION_DATE>
                <SCAN_QUOTA></SCAN_QUOTA>
                <PERIOD_OF_TIME></PERIOD_OF_TIME>
                <NB_OF_WARNINGS></NB_OF_WARNINGS>
            </APPLICATION>
            <APPLICATION>
                <NAME>MQ Connector</NAME>
                <LICENSE_STATUS>Yes</LICENSE_STATUS>
                <EXPIRATION_DATE></EXPIRATION_DATE>
                <SCAN_QUOTA></SCAN_QUOTA>
                <PERIOD_OF_TIME></PERIOD_OF_TIME>
                <NB_OF_WARNINGS></NB_OF_WARNINGS>
            </APPLICATION>
            <APPLICATION>
                <NAME>Name Checker</NAME>
                <LICENSE_STATUS>Yes</LICENSE_STATUS>
                <EXPIRATION_DATE></EXPIRATION_DATE>
                <SCAN_QUOTA>9999999</SCAN_QUOTA>
                <PERIOD_OF_TIME></PERIOD_OF_TIME>
                <NB_OF_WARNINGS></NB_OF_WARNINGS>
            </APPLICATION>
            <APPLICATION>
                <NAME>SAA OFCA Station</NAME>
                <LICENSE_STATUS>Yes</LICENSE_STATUS>
                <EXPIRATION_DATE></EXPIRATION_DATE>
                <SCAN_QUOTA>9999999</SCAN_QUOTA>
                <PERIOD_OF_TIME></PERIOD_OF_TIME>
                <NB_OF_WARNINGS></NB_OF_WARNINGS>
            </APPLICATION>
            <APPLICATION>
                <NAME>SAA OFCS Detect</NAME>
                <LICENSE_STATUS>Yes</LICENSE_STATUS>
                <EXPIRATION_DATE></EXPIRATION_DATE>
                <SCAN_QUOTA>9999999</SCAN_QUOTA>
                <PERIOD_OF_TIME></PERIOD_OF_TIME>
                <NB_OF_WARNINGS></NB_OF_WARNINGS>
            </APPLICATION>
            <APPLICATION>
                <NAME>SAA OFCS Monitor</NAME>
                <LICENSE_STATUS>Yes</LICENSE_STATUS>
                <EXPIRATION_DATE></EXPIRATION_DATE>
                <SCAN_QUOTA></SCAN_QUOTA>
                <PERIOD_OF_TIME></PERIOD_OF_TIME>
                <NB_OF_WARNINGS></NB_OF_WARNINGS>
            </APPLICATION>
            <APPLICATION>
                <NAME>SafeWatch API</NAME>
                <LICENSE_STATUS>Yes</LICENSE_STATUS>
                <EXPIRATION_DATE></EXPIRATION_DATE>
                <SCAN_QUOTA>9999999</SCAN_QUOTA>
                <PERIOD_OF_TIME></PERIOD_OF_TIME>
                <NB_OF_WARNINGS></NB_OF_WARNINGS>
            </APPLICATION>
            <APPLICATION>
                <NAME>SafeWatch Server</NAME>
                <LICENSE_STATUS>Yes</LICENSE_STATUS>
                <EXPIRATION_DATE></EXPIRATION_DATE>
                <SCAN_QUOTA></SCAN_QUOTA>
                <PERIOD_OF_TIME></PERIOD_OF_TIME>
                <NB_OF_WARNINGS></NB_OF_WARNINGS>
            </APPLICATION>
        </APPLICATION_LIST>
    </LICENSE>
</XML>