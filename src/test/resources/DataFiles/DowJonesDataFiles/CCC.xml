<?xml version="1.0" encoding="UTF-8"?>
<!-- <PERSON> Sanction Alert Chinese Commercial Codes -->
<!-- © 2017 Dow Jones & Company. All rights reserved. -->
<DowJonesSanctionAlert date="20170706_1" type="full">
	<CountryTypeList>
		<CountryTypeName RecordType="Person" action="add" code="1" date="25-Oct-2009">Citizenship</CountryTypeName>
		<CountryTypeName RecordType="Person" action="add" code="2" date="25-Oct-2009">Resident of</CountryTypeName>
		<CountryTypeName RecordType="Entity" action="add" code="3" date="06-Sep-2009">Country of Affiliation</CountryTypeName>
		<CountryTypeName RecordType="Entity" action="add" code="4" date="06-Sep-2009">Country of Registration</CountryTypeName>
		<CountryTypeName RecordType="Entity" action="add" code="7" date="22-Dec-2012">Enhanced Risk Country</CountryTypeName>
		<CountryTypeName RecordType="Person" action="add" code="7" date="22-Dec-2012">Enhanced Risk Country</CountryTypeName>
	</CountryTypeList>
	<CountryList>
		<CountryName DJIIRegionCode="AFGH" ISO2CountryCode="AF" ISO3CountryCode="AFG" IsTerritory="False" action="add" code="1" date="27-Feb-2010">Afghanistan</CountryName>
		<CountryName DJIIRegionCode="ALB" ISO2CountryCode="AL" ISO3CountryCode="ALB" IsTerritory="False" action="add" code="2" date="27-Feb-2010">Albania</CountryName>
		<CountryName DJIIRegionCode="ALG" ISO2CountryCode="DZ" ISO3CountryCode="DZA" IsTerritory="False" action="add" code="3" date="27-Feb-2010">Algeria</CountryName>
		<CountryName DJIIRegionCode="AMSAM" ISO2CountryCode="AS" ISO3CountryCode="ASM" IsTerritory="True" action="add" code="4" date="30-Sep-2009">American Samoa</CountryName>
		<CountryName DJIIRegionCode="ANDO" ISO2CountryCode="AD" ISO3CountryCode="AND" IsTerritory="False" action="add" code="5" date="27-Feb-2010">Andorra</CountryName>
		<CountryName DJIIRegionCode="ANGOL" ISO2CountryCode="AO" ISO3CountryCode="AGO" IsTerritory="False" action="add" code="6" date="27-Feb-2010">Angola</CountryName>
		<CountryName DJIIRegionCode="ANGUIL" ISO2CountryCode="AI" ISO3CountryCode="AIA" IsTerritory="True" action="add" code="7" date="30-Sep-2009">Anguilla</CountryName>
		<CountryName DJIIRegionCode="AARCT" ISO2CountryCode="AQ" ISO3CountryCode="ATA" IsTerritory="True" action="add" code="8" date="30-Sep-2009">Antarctica</CountryName>
		<CountryName DJIIRegionCode="ANTA" ISO2CountryCode="AG" ISO3CountryCode="ATG" IsTerritory="False" action="add" code="9" date="27-Feb-2010">Antigua and Barbuda</CountryName>
		<CountryName DJIIRegionCode="ARG" ISO2CountryCode="AR" ISO3CountryCode="ARG" IsTerritory="False" action="add" code="10" date="27-Feb-2010">Argentina</CountryName>
		<CountryName DJIIRegionCode="ARMEN" ISO2CountryCode="AM" ISO3CountryCode="ARM" IsTerritory="False" action="add" code="11" date="27-Feb-2010">Armenia</CountryName>
		<CountryName DJIIRegionCode="ARUBA" ISO2CountryCode="AW" ISO3CountryCode="ABW" IsTerritory="True" action="add" code="12" date="30-Sep-2009">Aruba</CountryName>
		<CountryName DJIIRegionCode="AUSTR" ISO2CountryCode="AU" ISO3CountryCode="AUS" IsTerritory="False" action="add" code="13" date="27-Feb-2010">Australia</CountryName>
		<CountryName DJIIRegionCode="AUST" ISO2CountryCode="AT" ISO3CountryCode="AUT" IsTerritory="False" action="add" code="14" date="27-Feb-2010">Austria</CountryName>
		<CountryName DJIIRegionCode="AZERB" ISO2CountryCode="AZ" ISO3CountryCode="AZE" IsTerritory="False" action="add" code="15" date="27-Feb-2010">Azerbaijan</CountryName>
		<CountryName DJIIRegionCode="BAH" ISO2CountryCode="BS" ISO3CountryCode="BHS" IsTerritory="False" action="add" code="16" date="27-Feb-2010">Bahamas</CountryName>
		<CountryName DJIIRegionCode="BAHRN" ISO2CountryCode="BH" ISO3CountryCode="BHR" IsTerritory="False" action="add" code="17" date="27-Feb-2010">Bahrain</CountryName>
		<CountryName DJIIRegionCode="BANDH" ISO2CountryCode="BD" ISO3CountryCode="BGD" IsTerritory="False" action="add" code="18" date="27-Feb-2010">Bangladesh</CountryName>
		<CountryName DJIIRegionCode="BARB" ISO2CountryCode="BB" ISO3CountryCode="BRB" IsTerritory="False" action="add" code="19" date="27-Feb-2010">Barbados</CountryName>
		<CountryName DJIIRegionCode="BYELRS" ISO2CountryCode="BY" ISO3CountryCode="BLR" IsTerritory="False" action="add" code="20" date="27-Feb-2010">Belarus</CountryName>
		<CountryName DJIIRegionCode="BELG" ISO2CountryCode="BE" ISO3CountryCode="BEL" IsTerritory="False" action="add" code="21" date="27-Feb-2010">Belgium</CountryName>
		<CountryName DJIIRegionCode="BELZ" ISO2CountryCode="BZ" ISO3CountryCode="BLZ" IsTerritory="False" action="add" code="22" date="27-Feb-2010">Belize</CountryName>
		<CountryName DJIIRegionCode="BENIN" ISO2CountryCode="BJ" ISO3CountryCode="BEN" IsTerritory="False" action="add" code="23" date="27-Feb-2010">Benin</CountryName>
		<CountryName DJIIRegionCode="BERM" ISO2CountryCode="BM" ISO3CountryCode="BMU" IsTerritory="True" action="add" code="24" date="30-Sep-2009">Bermuda</CountryName>
		<CountryName DJIIRegionCode="BHUTAN" ISO2CountryCode="BT" ISO3CountryCode="BTN" IsTerritory="False" action="add" code="25" date="27-Feb-2010">Bhutan</CountryName>
		<CountryName DJIIRegionCode="BOL" ISO2CountryCode="BO" ISO3CountryCode="BOL" IsTerritory="False" action="add" code="26" date="27-Feb-2010">Bolivia</CountryName>
		<CountryName DJIIRegionCode="BSHZG" ISO2CountryCode="BA" ISO3CountryCode="BIH" IsTerritory="False" action="add" code="27" date="28-May-2010">Bosnia and Herzegovina</CountryName>
		<CountryName DJIIRegionCode="BOTS" ISO2CountryCode="BW" ISO3CountryCode="BWA" IsTerritory="False" action="add" code="28" date="27-Feb-2010">Botswana</CountryName>
		<CountryName DJIIRegionCode="BOUV" ISO2CountryCode="BV" ISO3CountryCode="BVT" IsTerritory="True" action="add" code="29" date="30-Sep-2009">Bouvet Island</CountryName>
		<CountryName DJIIRegionCode="BRAZ" ISO2CountryCode="BR" ISO3CountryCode="BRA" IsTerritory="False" action="add" code="30" date="27-Feb-2010">Brazil</CountryName>
		<CountryName DJIIRegionCode="BIOT" ISO2CountryCode="IO" ISO3CountryCode="IOT" IsTerritory="True" action="add" code="31" date="30-Sep-2009">British Indian Ocean Territory</CountryName>
		<CountryName DJIIRegionCode="BVI" ISO2CountryCode="VG" ISO3CountryCode="VGB" IsTerritory="True" action="add" code="32" date="30-Sep-2009">British Virgin Islands</CountryName>
		<CountryName DJIIRegionCode="BRUNEI" ISO2CountryCode="BN" ISO3CountryCode="BRN" IsTerritory="False" action="add" code="33" date="27-Feb-2010">Brunei</CountryName>
		<CountryName DJIIRegionCode="BUL" ISO2CountryCode="BG" ISO3CountryCode="BGR" IsTerritory="False" action="add" code="34" date="27-Feb-2010">Bulgaria</CountryName>
		<CountryName DJIIRegionCode="UPVOLA" ISO2CountryCode="BF" ISO3CountryCode="BFA" IsTerritory="False" action="add" code="35" date="27-Feb-2010">Burkina Faso</CountryName>
		<CountryName DJIIRegionCode="BURUN" ISO2CountryCode="BI" ISO3CountryCode="BDI" IsTerritory="False" action="add" code="36" date="27-Feb-2010">Burundi</CountryName>
		<CountryName DJIIRegionCode="KAMPA" ISO2CountryCode="KH" ISO3CountryCode="KHM" IsTerritory="False" action="add" code="37" date="27-Feb-2010">Cambodia</CountryName>
		<CountryName DJIIRegionCode="CAMER" ISO2CountryCode="CM" ISO3CountryCode="CMR" IsTerritory="False" action="add" code="38" date="27-Feb-2010">Cameroon</CountryName>
		<CountryName DJIIRegionCode="CANA" ISO2CountryCode="CA" ISO3CountryCode="CAN" IsTerritory="False" action="add" code="39" date="27-Feb-2010">Canada</CountryName>
		<CountryName DJIIRegionCode="CVI" ISO2CountryCode="CV" ISO3CountryCode="CPV" IsTerritory="False" action="add" code="40" date="27-Feb-2010">Cape Verde</CountryName>
		<CountryName DJIIRegionCode="CAYI" ISO2CountryCode="KY" ISO3CountryCode="CYM" IsTerritory="True" action="add" code="41" date="30-Sep-2009">Cayman Islands</CountryName>
		<CountryName DJIIRegionCode="CAFR" ISO2CountryCode="CF" ISO3CountryCode="CAF" IsTerritory="False" action="add" code="42" date="27-Feb-2010">Central African Republic</CountryName>
		<CountryName DJIIRegionCode="CHAD" ISO2CountryCode="TD" ISO3CountryCode="TCD" IsTerritory="False" action="add" code="43" date="27-Feb-2010">Chad</CountryName>
		<CountryName DJIIRegionCode="CHIL" ISO2CountryCode="CL" ISO3CountryCode="CHL" IsTerritory="False" action="add" code="44" date="30-Jun-2010">Chile</CountryName>
		<CountryName DJIIRegionCode="CHINA" ISO2CountryCode="CN" ISO3CountryCode="CHN" IsTerritory="False" action="add" code="45" date="27-Feb-2010">China</CountryName>
		<CountryName DJIIRegionCode="CHR" ISO2CountryCode="CX" ISO3CountryCode="CXR" IsTerritory="True" action="add" code="46" date="30-Sep-2009">Christmas Island</CountryName>
		<CountryName DJIIRegionCode="COCOS" ISO2CountryCode="CC" ISO3CountryCode="CCK" IsTerritory="True" action="add" code="47" date="30-Sep-2009">Cocos (Keeling) Islands</CountryName>
		<CountryName DJIIRegionCode="COL" ISO2CountryCode="CO" ISO3CountryCode="COL" IsTerritory="False" action="add" code="48" date="27-Feb-2010">Colombia</CountryName>
		<CountryName DJIIRegionCode="COMOR" ISO2CountryCode="KM" ISO3CountryCode="COM" IsTerritory="False" action="add" code="49" date="27-Feb-2010">Comoros</CountryName>
		<CountryName DJIIRegionCode="CONGO" ISO2CountryCode="CG" ISO3CountryCode="COG" IsTerritory="False" action="add" code="50" date="27-Feb-2010">Congo Republic</CountryName>
		<CountryName DJIIRegionCode="COOKIS" ISO2CountryCode="CK" ISO3CountryCode="COK" IsTerritory="True" action="add" code="51" date="30-Sep-2009">Cook Islands</CountryName>
		<CountryName DJIIRegionCode="COSR" ISO2CountryCode="CR" ISO3CountryCode="CRI" IsTerritory="False" action="add" code="52" date="30-Jun-2010">Costa Rica</CountryName>
		<CountryName DJIIRegionCode="ICST" ISO2CountryCode="CI" ISO3CountryCode="CIV" IsTerritory="False" action="add" code="53" date="27-Feb-2010">Cote d'Ivoire</CountryName>
		<CountryName DJIIRegionCode="CRTIA" ISO2CountryCode="HR" ISO3CountryCode="HRV" IsTerritory="False" action="add" code="54" date="27-Feb-2010">Croatia</CountryName>
		<CountryName DJIIRegionCode="CUBA" ISO2CountryCode="CU" ISO3CountryCode="CUB" IsTerritory="False" action="add" code="55" date="30-Jun-2010">Cuba</CountryName>
		<CountryName DJIIRegionCode="CYPR" ISO2CountryCode="CY" ISO3CountryCode="CYP" IsTerritory="False" action="add" code="56" date="27-Feb-2010">Cyprus</CountryName>
		<CountryName DJIIRegionCode="CZREP" ISO2CountryCode="CZ" ISO3CountryCode="CZE" IsTerritory="False" action="add" code="57" date="27-Feb-2010">Czech Republic</CountryName>
		<CountryName DJIIRegionCode="ZAIRE" ISO2CountryCode="CD" ISO3CountryCode="COD" IsTerritory="False" action="add" code="58" date="27-Feb-2010">Democratic Republic of the Congo</CountryName>
		<CountryName DJIIRegionCode="DEN" ISO2CountryCode="DK" ISO3CountryCode="DNK" IsTerritory="False" action="add" code="59" date="27-Feb-2010">Denmark</CountryName>
		<CountryName DJIIRegionCode="TAI" ISO2CountryCode="DJ" ISO3CountryCode="DJI" IsTerritory="False" action="add" code="60" date="27-Feb-2010">Djibouti</CountryName>
		<CountryName DJIIRegionCode="DOMA" ISO2CountryCode="DM" ISO3CountryCode="DMA" IsTerritory="False" action="add" code="61" date="27-Feb-2010">Dominica</CountryName>
		<CountryName DJIIRegionCode="DOMR" ISO2CountryCode="DO" ISO3CountryCode="DOM" IsTerritory="False" action="add" code="62" date="27-Feb-2010">Dominican Republic</CountryName>
		<CountryName DJIIRegionCode="TIMOR" ISO2CountryCode="TL" ISO3CountryCode="TLS" IsTerritory="False" action="add" code="63" date="06-Sep-2009">Timor Leste</CountryName>
		<CountryName DJIIRegionCode="ECU" ISO2CountryCode="EC" ISO3CountryCode="ECU" IsTerritory="False" action="add" code="64" date="30-Jun-2010">Ecuador</CountryName>
		<CountryName DJIIRegionCode="EGYPT" ISO2CountryCode="EG" ISO3CountryCode="EGY" IsTerritory="False" action="add" code="65" date="27-Feb-2010">Egypt</CountryName>
		<CountryName DJIIRegionCode="ELSAL" ISO2CountryCode="SV" ISO3CountryCode="SLV" IsTerritory="False" action="add" code="66" date="30-Jun-2010">El Salvador</CountryName>
		<CountryName DJIIRegionCode="EQGNA" ISO2CountryCode="GQ" ISO3CountryCode="GNQ" IsTerritory="False" action="add" code="67" date="27-Feb-2010">Equatorial Guinea</CountryName>
		<CountryName DJIIRegionCode="ERTRA" ISO2CountryCode="ER" ISO3CountryCode="ERI" IsTerritory="False" action="add" code="68" date="27-Feb-2010">Eritrea</CountryName>
		<CountryName DJIIRegionCode="ESTNIA" ISO2CountryCode="EE" ISO3CountryCode="EST" IsTerritory="False" action="add" code="69" date="27-Feb-2010">Estonia</CountryName>
		<CountryName DJIIRegionCode="ETHPA" ISO2CountryCode="ET" ISO3CountryCode="ETH" IsTerritory="False" action="add" code="70" date="27-Feb-2010">Ethiopia</CountryName>
		<CountryName DJIIRegionCode="FAEROE" ISO2CountryCode="FO" ISO3CountryCode="FRO" IsTerritory="True" action="add" code="71" date="16-Mar-2017">Faroe Islands</CountryName>
		<CountryName DJIIRegionCode="FALK" ISO2CountryCode="FK" ISO3CountryCode="FLK" IsTerritory="True" action="add" code="72" date="16-Mar-2017">Falkland Islands</CountryName>
		<CountryName DJIIRegionCode="FIJI" ISO2CountryCode="FJ" ISO3CountryCode="FJI" IsTerritory="False" action="add" code="73" date="27-Feb-2010">Fiji</CountryName>
		<CountryName DJIIRegionCode="FIN" ISO2CountryCode="FI" ISO3CountryCode="FIN" IsTerritory="False" action="add" code="74" date="27-Feb-2010">Finland</CountryName>
		<CountryName DJIIRegionCode="FRA" ISO2CountryCode="FR" ISO3CountryCode="FRA" IsTerritory="False" action="add" code="75" date="27-Feb-2010">France</CountryName>
		<CountryName DJIIRegionCode="FGNA" ISO2CountryCode="GF" ISO3CountryCode="GUF" IsTerritory="True" action="add" code="76" date="30-Sep-2009">French Guiana</CountryName>
		<CountryName DJIIRegionCode="FPOLY" ISO2CountryCode="PF" ISO3CountryCode="PYF" IsTerritory="True" action="add" code="77" date="30-Sep-2009">French Polynesia</CountryName>
		<CountryName DJIIRegionCode="GABON" ISO2CountryCode="GA" ISO3CountryCode="GAB" IsTerritory="False" action="add" code="78" date="27-Feb-2010">Gabon</CountryName>
		<CountryName DJIIRegionCode="GAMB" ISO2CountryCode="GM" ISO3CountryCode="GMB" IsTerritory="False" action="add" code="79" date="27-Feb-2010">Gambia</CountryName>
		<CountryName DJIIRegionCode="GRGIA" ISO2CountryCode="GE" ISO3CountryCode="GEO" IsTerritory="False" action="add" code="80" date="16-Feb-2017">Georgia</CountryName>
		<CountryName DJIIRegionCode="GFR" ISO2CountryCode="DE" ISO3CountryCode="DEU" IsTerritory="False" action="add" code="81" date="27-Feb-2010">Germany</CountryName>
		<CountryName DJIIRegionCode="GHANA" ISO2CountryCode="GH" ISO3CountryCode="GHA" IsTerritory="False" action="add" code="82" date="27-Feb-2010">Ghana</CountryName>
		<CountryName DJIIRegionCode="GIB" ISO2CountryCode="GI" ISO3CountryCode="GIB" IsTerritory="True" action="add" code="83" date="30-Sep-2009">Gibraltar</CountryName>
		<CountryName DJIIRegionCode="GREECE" ISO2CountryCode="GR" ISO3CountryCode="GRC" IsTerritory="False" action="add" code="84" date="27-Feb-2010">Greece</CountryName>
		<CountryName DJIIRegionCode="GREENL" ISO2CountryCode="GL" ISO3CountryCode="GRL" IsTerritory="True" action="add" code="85" date="30-Sep-2009">Greenland</CountryName>
		<CountryName DJIIRegionCode="GREN" ISO2CountryCode="GD" ISO3CountryCode="GRD" IsTerritory="False" action="add" code="86" date="27-Feb-2010">Grenada</CountryName>
		<CountryName DJIIRegionCode="GUAD" ISO2CountryCode="GP" ISO3CountryCode="GLP" IsTerritory="True" action="add" code="87" date="30-Sep-2009">Guadeloupe</CountryName>
		<CountryName DJIIRegionCode="GUAM" ISO2CountryCode="GU" ISO3CountryCode="GUM" IsTerritory="True" action="add" code="88" date="30-Sep-2009">Guam</CountryName>
		<CountryName DJIIRegionCode="GUAT" ISO2CountryCode="GT" ISO3CountryCode="GTM" IsTerritory="False" action="add" code="89" date="30-Jun-2010">Guatemala</CountryName>
		<CountryName DJIIRegionCode="GUREP" ISO2CountryCode="GN" ISO3CountryCode="GIN" IsTerritory="False" action="add" code="90" date="27-Feb-2010">Guinea</CountryName>
		<CountryName DJIIRegionCode="GUBI" ISO2CountryCode="GW" ISO3CountryCode="GNB" IsTerritory="False" action="add" code="91" date="27-Feb-2010">Guinea-Bissau</CountryName>
		<CountryName DJIIRegionCode="GUY" ISO2CountryCode="GY" ISO3CountryCode="GUY" IsTerritory="False" action="add" code="92" date="27-Feb-2010">Guyana</CountryName>
		<CountryName DJIIRegionCode="HAIT" ISO2CountryCode="HT" ISO3CountryCode="HTI" IsTerritory="False" action="add" code="93" date="27-Feb-2010">Haiti</CountryName>
		<CountryName DJIIRegionCode="HEARD" ISO2CountryCode="HM" ISO3CountryCode="HMD" IsTerritory="True" action="add" code="94" date="30-Sep-2009">Heard and McDonald Islands</CountryName>
		<CountryName DJIIRegionCode="HON" ISO2CountryCode="HN" ISO3CountryCode="HND" IsTerritory="False" action="add" code="95" date="27-Feb-2010">Honduras</CountryName>
		<CountryName DJIIRegionCode="HKONG" ISO2CountryCode="HK" ISO3CountryCode="HKG" IsTerritory="True" action="add" code="96" date="30-Sep-2009">Hong Kong</CountryName>
		<CountryName DJIIRegionCode="HUNG" ISO2CountryCode="HU" ISO3CountryCode="HUN" IsTerritory="False" action="add" code="97" date="27-Feb-2010">Hungary</CountryName>
		<CountryName DJIIRegionCode="ICEL" ISO2CountryCode="IS" ISO3CountryCode="ISL" IsTerritory="False" action="add" code="98" date="27-Feb-2010">Iceland</CountryName>
		<CountryName DJIIRegionCode="INDIA" ISO2CountryCode="IN" ISO3CountryCode="IND" IsTerritory="False" action="add" code="99" date="27-Feb-2010">India</CountryName>
		<CountryName DJIIRegionCode="INDON" ISO2CountryCode="ID" ISO3CountryCode="IDN" IsTerritory="False" action="add" code="100" date="27-Feb-2010">Indonesia</CountryName>
		<CountryName DJIIRegionCode="IRAN" ISO2CountryCode="IR" ISO3CountryCode="IRN" IsTerritory="False" action="add" code="101" date="27-Feb-2010">Iran</CountryName>
		<CountryName DJIIRegionCode="IRAQ" ISO2CountryCode="IQ" ISO3CountryCode="IRQ" IsTerritory="False" action="add" code="102" date="27-Feb-2010">Iraq</CountryName>
		<CountryName DJIIRegionCode="IRE" ISO2CountryCode="IE" ISO3CountryCode="IRL" IsTerritory="False" action="add" code="103" date="27-Feb-2010">Ireland</CountryName>
		<CountryName DJIIRegionCode="ISRAEL" ISO2CountryCode="IL" ISO3CountryCode="ISR" IsTerritory="False" action="add" code="104" date="27-Feb-2010">Israel</CountryName>
		<CountryName DJIIRegionCode="ITALY" ISO2CountryCode="IT" ISO3CountryCode="ITA" IsTerritory="False" action="add" code="105" date="27-Feb-2010">Italy</CountryName>
		<CountryName DJIIRegionCode="JAMA" ISO2CountryCode="JM" ISO3CountryCode="JAM" IsTerritory="False" action="add" code="106" date="27-Feb-2010">Jamaica</CountryName>
		<CountryName DJIIRegionCode="JAP" ISO2CountryCode="JP" ISO3CountryCode="JPN" IsTerritory="False" action="add" code="107" date="27-Feb-2010">Japan</CountryName>
		<CountryName DJIIRegionCode="JORDAN" ISO2CountryCode="JO" ISO3CountryCode="JOR" IsTerritory="False" action="add" code="108" date="27-Feb-2010">Jordan</CountryName>
		<CountryName DJIIRegionCode="KAZK" ISO2CountryCode="KZ" ISO3CountryCode="KAZ" IsTerritory="False" action="add" code="109" date="27-Feb-2010">Kazakhstan</CountryName>
		<CountryName DJIIRegionCode="KENYA" ISO2CountryCode="KE" ISO3CountryCode="KEN" IsTerritory="False" action="add" code="110" date="27-Feb-2010">Kenya</CountryName>
		<CountryName DJIIRegionCode="KIRB" ISO2CountryCode="KI" ISO3CountryCode="KIR" IsTerritory="False" action="add" code="111" date="27-Feb-2010">Kiribati</CountryName>
		<CountryName DJIIRegionCode="KUWAIT" ISO2CountryCode="KW" ISO3CountryCode="KWT" IsTerritory="False" action="add" code="112" date="27-Feb-2010">Kuwait</CountryName>
		<CountryName DJIIRegionCode="KIRGH" ISO2CountryCode="KG" ISO3CountryCode="KGZ" IsTerritory="False" action="add" code="113" date="27-Feb-2010">Kyrgyzstan</CountryName>
		<CountryName DJIIRegionCode="LAOS" ISO2CountryCode="LA" ISO3CountryCode="LAO" IsTerritory="False" action="add" code="114" date="27-Feb-2010">Laos</CountryName>
		<CountryName DJIIRegionCode="LATV" ISO2CountryCode="LV" ISO3CountryCode="LVA" IsTerritory="False" action="add" code="115" date="27-Feb-2010">Latvia</CountryName>
		<CountryName DJIIRegionCode="LEBAN" ISO2CountryCode="LB" ISO3CountryCode="LBN" IsTerritory="False" action="add" code="116" date="27-Feb-2010">Lebanon</CountryName>
		<CountryName DJIIRegionCode="LESOT" ISO2CountryCode="LS" ISO3CountryCode="LSO" IsTerritory="False" action="add" code="117" date="27-Feb-2010">Lesotho</CountryName>
		<CountryName DJIIRegionCode="LIBER" ISO2CountryCode="LR" ISO3CountryCode="LBR" IsTerritory="False" action="add" code="118" date="28-Aug-2006">Liberia</CountryName>
		<CountryName DJIIRegionCode="LIBYA" ISO2CountryCode="LY" ISO3CountryCode="LBY" IsTerritory="False" action="add" code="119" date="28-Aug-2006">Libya</CountryName>
		<CountryName DJIIRegionCode="LIECHT" ISO2CountryCode="LI" ISO3CountryCode="LIE" IsTerritory="False" action="add" code="120" date="20-Oct-2009">Liechtenstein</CountryName>
		<CountryName DJIIRegionCode="LITH" ISO2CountryCode="LT" ISO3CountryCode="LTU" IsTerritory="False" action="add" code="121" date="20-Oct-2009">Lithuania</CountryName>
		<CountryName DJIIRegionCode="LUX" ISO2CountryCode="LU" ISO3CountryCode="LUX" IsTerritory="False" action="add" code="122" date="20-Oct-2009">Luxembourg</CountryName>
		<CountryName DJIIRegionCode="MACAO" ISO2CountryCode="MO" ISO3CountryCode="MAC" IsTerritory="True" action="add" code="123" date="30-Sep-2009">Macau</CountryName>
		<CountryName DJIIRegionCode="MCDNIA" ISO2CountryCode="MK" ISO3CountryCode="MKD" IsTerritory="False" action="add" code="124" date="20-Oct-2009">Macedonia</CountryName>
		<CountryName DJIIRegionCode="MALAG" ISO2CountryCode="MG" ISO3CountryCode="MDG" IsTerritory="False" action="add" code="125" date="20-Oct-2009">Madagascar</CountryName>
		<CountryName DJIIRegionCode="MALAW" ISO2CountryCode="MW" ISO3CountryCode="MWI" IsTerritory="False" action="add" code="126" date="20-Oct-2009">Malawi</CountryName>
		<CountryName DJIIRegionCode="MALAY" ISO2CountryCode="MY" ISO3CountryCode="MYS" IsTerritory="False" action="add" code="127" date="20-Oct-2009">Malaysia</CountryName>
		<CountryName DJIIRegionCode="MALDR" ISO2CountryCode="MV" ISO3CountryCode="MDV" IsTerritory="False" action="add" code="128" date="20-Oct-2009">Maldives</CountryName>
		<CountryName DJIIRegionCode="MALI" ISO2CountryCode="ML" ISO3CountryCode="MLI" IsTerritory="False" action="add" code="129" date="20-Oct-2009">Mali</CountryName>
		<CountryName DJIIRegionCode="MALTA" ISO2CountryCode="MT" ISO3CountryCode="MLT" IsTerritory="False" action="add" code="130" date="20-Oct-2009">Malta</CountryName>
		<CountryName DJIIRegionCode="MAH" ISO2CountryCode="MH" ISO3CountryCode="MHL" IsTerritory="False" action="add" code="131" date="20-Oct-2009">Marshall Islands</CountryName>
		<CountryName DJIIRegionCode="MARQ" ISO2CountryCode="MQ" ISO3CountryCode="MTQ" IsTerritory="True" action="add" code="132" date="30-Sep-2009">Martinique</CountryName>
		<CountryName DJIIRegionCode="MAURTN" ISO2CountryCode="MR" ISO3CountryCode="MRT" IsTerritory="False" action="add" code="133" date="20-Oct-2009">Mauritania</CountryName>
		<CountryName DJIIRegionCode="MAURTS" ISO2CountryCode="MU" ISO3CountryCode="MUS" IsTerritory="False" action="add" code="134" date="20-Oct-2009">Mauritius</CountryName>
		<CountryName DJIIRegionCode="MAYOT" ISO2CountryCode="YT" ISO3CountryCode="MYT" IsTerritory="True" action="add" code="135" date="30-Sep-2009">Mayotte</CountryName>
		<CountryName DJIIRegionCode="MEX" ISO2CountryCode="MX" ISO3CountryCode="MEX" IsTerritory="False" action="add" code="136" date="20-Oct-2009">Mexico</CountryName>
		<CountryName DJIIRegionCode="FESMIC" ISO2CountryCode="FM" ISO3CountryCode="FSM" IsTerritory="False" action="add" code="137" date="20-Oct-2009">Micronesia</CountryName>
		<CountryName DJIIRegionCode="MOLDV" ISO2CountryCode="MD" ISO3CountryCode="MDA" IsTerritory="False" action="add" code="138" date="17-May-2016">Moldova</CountryName>
		<CountryName DJIIRegionCode="MONAC" ISO2CountryCode="MC" ISO3CountryCode="MCO" IsTerritory="False" action="add" code="139" date="20-Oct-2009">Monaco</CountryName>
		<CountryName DJIIRegionCode="MONGLA" ISO2CountryCode="MN" ISO3CountryCode="MNG" IsTerritory="False" action="add" code="140" date="20-Oct-2009">Mongolia</CountryName>
		<CountryName DJIIRegionCode="MONT" ISO2CountryCode="MS" ISO3CountryCode="MSR" IsTerritory="True" action="add" code="141" date="30-Sep-2009">Montserrat</CountryName>
		<CountryName DJIIRegionCode="MOROC" ISO2CountryCode="MA" ISO3CountryCode="MAR" IsTerritory="False" action="add" code="142" date="20-Oct-2009">Morocco</CountryName>
		<CountryName DJIIRegionCode="MOZAM" ISO2CountryCode="MZ" ISO3CountryCode="MOZ" IsTerritory="False" action="add" code="143" date="20-Oct-2009">Mozambique</CountryName>
		<CountryName DJIIRegionCode="BURMA" ISO2CountryCode="MM" ISO3CountryCode="MMR" IsTerritory="False" action="add" code="144" date="20-Oct-2009">Myanmar</CountryName>
		<CountryName DJIIRegionCode="NAMIB" ISO2CountryCode="NA" ISO3CountryCode="NAM" IsTerritory="False" action="add" code="145" date="20-Oct-2009">Namibia</CountryName>
		<CountryName DJIIRegionCode="NAURU" ISO2CountryCode="NR" ISO3CountryCode="NRU" IsTerritory="False" action="add" code="146" date="20-Oct-2009">Nauru</CountryName>
		<CountryName DJIIRegionCode="NEPAL" ISO2CountryCode="NP" ISO3CountryCode="NPL" IsTerritory="False" action="add" code="147" date="20-Oct-2009">Nepal</CountryName>
		<CountryName DJIIRegionCode="NETH" ISO2CountryCode="NL" ISO3CountryCode="NLD" IsTerritory="False" action="add" code="148" date="20-Oct-2009">Netherlands</CountryName>
		<CountryName DJIIRegionCode="NANT" ISO2CountryCode="CW" ISO3CountryCode="CUW" IsTerritory="True" action="add" code="149" date="16-Mar-2017">Curaçao</CountryName>
		<CountryName DJIIRegionCode="NEWCAL" ISO2CountryCode="NC" ISO3CountryCode="NCL" IsTerritory="True" action="add" code="150" date="30-Sep-2009">New Caledonia</CountryName>
		<CountryName DJIIRegionCode="NZ" ISO2CountryCode="NZ" ISO3CountryCode="NZL" IsTerritory="False" action="add" code="151" date="20-Oct-2009">New Zealand</CountryName>
		<CountryName DJIIRegionCode="NICG" ISO2CountryCode="NI" ISO3CountryCode="NIC" IsTerritory="False" action="add" code="152" date="30-Jun-2010">Nicaragua</CountryName>
		<CountryName DJIIRegionCode="NIGER" ISO2CountryCode="NE" ISO3CountryCode="NER" IsTerritory="False" action="add" code="153" date="20-Oct-2009">Niger</CountryName>
		<CountryName DJIIRegionCode="NIGEA" ISO2CountryCode="NG" ISO3CountryCode="NGA" IsTerritory="False" action="add" code="154" date="28-May-2010">Nigeria</CountryName>
		<CountryName DJIIRegionCode="NIUE" ISO2CountryCode="NU" ISO3CountryCode="NIU" IsTerritory="True" action="add" code="155" date="30-Sep-2009">Niue</CountryName>
		<CountryName DJIIRegionCode="NORFIS" ISO2CountryCode="NF" ISO3CountryCode="NFK" IsTerritory="True" action="add" code="156" date="30-Sep-2009">Norfolk Island</CountryName>
		<CountryName DJIIRegionCode="NKOREA" ISO2CountryCode="KP" ISO3CountryCode="PRK" IsTerritory="False" action="add" code="157" date="20-Oct-2009">North Korea</CountryName>
		<CountryName DJIIRegionCode="NOMARI" ISO2CountryCode="MP" ISO3CountryCode="MNP" IsTerritory="True" action="add" code="158" date="30-Sep-2009">Northern Mariana Islands</CountryName>
		<CountryName DJIIRegionCode="NORW" ISO2CountryCode="NO" ISO3CountryCode="NOR" IsTerritory="False" action="add" code="159" date="20-Oct-2009">Norway</CountryName>
		<CountryName DJIIRegionCode="OMAN" ISO2CountryCode="OM" ISO3CountryCode="OMN" IsTerritory="False" action="add" code="160" date="20-Oct-2009">Oman</CountryName>
		<CountryName DJIIRegionCode="PAKIS" ISO2CountryCode="PK" ISO3CountryCode="PAK" IsTerritory="False" action="add" code="161" date="20-Oct-2009">Pakistan</CountryName>
		<CountryName DJIIRegionCode="PALAU" ISO2CountryCode="PW" ISO3CountryCode="PLW" IsTerritory="False" action="add" code="162" date="20-Oct-2009">Palau</CountryName>
		<CountryName DJIIRegionCode="PALEST" ISO2CountryCode="PS" ISO3CountryCode="PSE" IsTerritory="False" action="add" code="163" date="20-Oct-2009">Palestine</CountryName>
		<CountryName DJIIRegionCode="PANA" ISO2CountryCode="PA" ISO3CountryCode="PAN" IsTerritory="False" action="add" code="164" date="30-Jun-2010">Panama</CountryName>
		<CountryName DJIIRegionCode="PAPNG" ISO2CountryCode="PG" ISO3CountryCode="PNG" IsTerritory="False" action="add" code="165" date="20-Oct-2009">Papua New Guinea</CountryName>
		<CountryName DJIIRegionCode="PARA" ISO2CountryCode="PY" ISO3CountryCode="PRY" IsTerritory="False" action="add" code="166" date="20-Oct-2009">Paraguay</CountryName>
		<CountryName DJIIRegionCode="PERU" ISO2CountryCode="PE" ISO3CountryCode="PER" IsTerritory="False" action="add" code="167" date="20-Oct-2009">Peru</CountryName>
		<CountryName DJIIRegionCode="PHLNS" ISO2CountryCode="PH" ISO3CountryCode="PHL" IsTerritory="False" action="add" code="168" date="20-Oct-2009">Philippines</CountryName>
		<CountryName DJIIRegionCode="PITCIS" ISO2CountryCode="PN" ISO3CountryCode="PCN" IsTerritory="True" action="add" code="169" date="30-Sep-2009">Pitcairn</CountryName>
		<CountryName DJIIRegionCode="POL" ISO2CountryCode="PL" ISO3CountryCode="POL" IsTerritory="False" action="add" code="170" date="30-Apr-2010">Poland</CountryName>
		<CountryName DJIIRegionCode="PORL" ISO2CountryCode="PT" ISO3CountryCode="PRT" IsTerritory="False" action="add" code="171" date="20-Oct-2009">Portugal</CountryName>
		<CountryName DJIIRegionCode="PURI" ISO2CountryCode="PR" ISO3CountryCode="PRI" IsTerritory="True" action="add" code="172" date="30-Sep-2009">Puerto Rico</CountryName>
		<CountryName DJIIRegionCode="QATAR" ISO2CountryCode="QA" ISO3CountryCode="QAT" IsTerritory="False" action="add" code="173" date="20-Oct-2009">Qatar</CountryName>
		<CountryName DJIIRegionCode="REUNI" ISO2CountryCode="RE" ISO3CountryCode="REU" IsTerritory="True" action="add" code="174" date="30-Sep-2009">Reunion</CountryName>
		<CountryName DJIIRegionCode="ROM" ISO2CountryCode="RO" ISO3CountryCode="ROU" IsTerritory="False" action="add" code="175" date="20-Oct-2009">Romania</CountryName>
		<CountryName DJIIRegionCode="RUSS" ISO2CountryCode="RU" ISO3CountryCode="RUS" IsTerritory="False" action="add" code="176" date="20-Oct-2009">Russia</CountryName>
		<CountryName DJIIRegionCode="RWANDA" ISO2CountryCode="RW" ISO3CountryCode="RWA" IsTerritory="False" action="add" code="177" date="20-Oct-2009">Rwanda</CountryName>
		<CountryName DJIIRegionCode="SLUC" ISO2CountryCode="LC" ISO3CountryCode="LCA" IsTerritory="False" action="add" code="178" date="20-Oct-2009">Saint Lucia</CountryName>
		<CountryName DJIIRegionCode="WSOMOA" ISO2CountryCode="WS" ISO3CountryCode="WSM" IsTerritory="False" action="add" code="179" date="20-Oct-2009">Samoa</CountryName>
		<CountryName DJIIRegionCode="SMARNO" ISO2CountryCode="SM" ISO3CountryCode="SMR" IsTerritory="False" action="add" code="180" date="30-Apr-2010">San Marino</CountryName>
		<CountryName DJIIRegionCode="PST" ISO2CountryCode="ST" ISO3CountryCode="STP" IsTerritory="False" action="add" code="181" date="20-Oct-2009">Sao Tome and Principe</CountryName>
		<CountryName DJIIRegionCode="SAARAB" ISO2CountryCode="SA" ISO3CountryCode="SAU" IsTerritory="False" action="add" code="182" date="20-Oct-2009">Saudi Arabia</CountryName>
		<CountryName DJIIRegionCode="SENEG" ISO2CountryCode="SN" ISO3CountryCode="SEN" IsTerritory="False" action="add" code="183" date="20-Oct-2009">Senegal</CountryName>
		<CountryName DJIIRegionCode="SEYCH" ISO2CountryCode="SC" ISO3CountryCode="SYC" IsTerritory="False" action="add" code="184" date="20-Oct-2009">Seychelles</CountryName>
		<CountryName DJIIRegionCode="SILEN" ISO2CountryCode="SL" ISO3CountryCode="SLE" IsTerritory="False" action="add" code="185" date="20-Oct-2009">Sierra Leone</CountryName>
		<CountryName DJIIRegionCode="SINGP" ISO2CountryCode="SG" ISO3CountryCode="SGP" IsTerritory="False" action="add" code="186" date="20-Oct-2009">Singapore</CountryName>
		<CountryName DJIIRegionCode="SLVAK" ISO2CountryCode="SK" ISO3CountryCode="SVK" IsTerritory="False" action="add" code="187" date="20-Oct-2009">Slovakia</CountryName>
		<CountryName DJIIRegionCode="SLVNIA" ISO2CountryCode="SI" ISO3CountryCode="SVN" IsTerritory="False" action="add" code="188" date="20-Oct-2009">Slovenia</CountryName>
		<CountryName DJIIRegionCode="SOLIL" ISO2CountryCode="SB" ISO3CountryCode="SLB" IsTerritory="False" action="add" code="189" date="20-Oct-2009">Solomon Islands</CountryName>
		<CountryName DJIIRegionCode="SOMAL" ISO2CountryCode="SO" ISO3CountryCode="SOM" IsTerritory="False" action="add" code="190" date="20-Oct-2009">Somalia</CountryName>
		<CountryName DJIIRegionCode="SAFR" ISO2CountryCode="ZA" ISO3CountryCode="ZAF" IsTerritory="False" action="add" code="191" date="20-Oct-2009">South Africa</CountryName>
		<CountryName DJIIRegionCode="SGSSI" ISO2CountryCode="GS" ISO3CountryCode="SGS" IsTerritory="True" action="add" code="192" date="30-Sep-2009">South Georgia and South Sandwich Islands</CountryName>
		<CountryName DJIIRegionCode="SKOREA" ISO2CountryCode="KR" ISO3CountryCode="KOR" IsTerritory="False" action="add" code="193" date="20-Oct-2009">South Korea</CountryName>
		<CountryName DJIIRegionCode="SPAIN" ISO2CountryCode="ES" ISO3CountryCode="ESP" IsTerritory="False" action="add" code="194" date="20-Oct-2009">Spain</CountryName>
		<CountryName DJIIRegionCode="SRILAN" ISO2CountryCode="LK" ISO3CountryCode="LKA" IsTerritory="False" action="add" code="195" date="20-Oct-2009">Sri Lanka</CountryName>
		<CountryName DJIIRegionCode="STHEL" ISO2CountryCode="SH" ISO3CountryCode="SHN" IsTerritory="True" action="add" code="196" date="30-Sep-2009">St. Helena</CountryName>
		<CountryName DJIIRegionCode="SKIT" ISO2CountryCode="KN" ISO3CountryCode="KNA" IsTerritory="False" action="add" code="197" date="20-Oct-2009">St. Kitts and Nevis</CountryName>
		<CountryName DJIIRegionCode="STMART" ISO2CountryCode="MF" ISO3CountryCode="MAF" IsTerritory="True" action="add" code="198" date="30-Sep-2009">St. Martin</CountryName>
		<CountryName DJIIRegionCode="STPM" ISO2CountryCode="PM" ISO3CountryCode="SPM" IsTerritory="True" action="add" code="199" date="30-Sep-2009">St. Pierre and Miquelon</CountryName>
		<CountryName DJIIRegionCode="SVIN" ISO2CountryCode="VC" ISO3CountryCode="VCT" IsTerritory="False" action="add" code="200" date="20-Oct-2009">St. Vincent and the Grenadines</CountryName>
		<CountryName DJIIRegionCode="SUDAN" ISO2CountryCode="SD" ISO3CountryCode="SDN" IsTerritory="False" action="add" code="201" date="20-Oct-2009">Sudan</CountryName>
		<CountryName DJIIRegionCode="SURM" ISO2CountryCode="SR" ISO3CountryCode="SUR" IsTerritory="False" action="add" code="202" date="20-Oct-2009">Suriname</CountryName>
		<CountryName DJIIRegionCode="SVALB" ISO2CountryCode="SJ" ISO3CountryCode="SJM" IsTerritory="True" action="add" code="203" date="30-Sep-2009">Svalbard and Jan Mayen Islands</CountryName>
		<CountryName DJIIRegionCode="SWAZD" ISO2CountryCode="SZ" ISO3CountryCode="SWZ" IsTerritory="False" action="add" code="204" date="20-Oct-2009">Swaziland</CountryName>
		<CountryName DJIIRegionCode="SWED" ISO2CountryCode="SE" ISO3CountryCode="SWE" IsTerritory="False" action="add" code="205" date="20-Oct-2009">Sweden</CountryName>
		<CountryName DJIIRegionCode="SWITZ" ISO2CountryCode="CH" ISO3CountryCode="CHE" IsTerritory="False" action="add" code="206" date="20-Oct-2009">Switzerland</CountryName>
		<CountryName DJIIRegionCode="SYRIA" ISO2CountryCode="SY" ISO3CountryCode="SYR" IsTerritory="False" action="add" code="207" date="20-Oct-2009">Syria</CountryName>
		<CountryName DJIIRegionCode="TAIWAN" ISO2CountryCode="TW" ISO3CountryCode="TWN" IsTerritory="False" action="add" code="208" date="20-Oct-2009">Taiwan</CountryName>
		<CountryName DJIIRegionCode="TADZK" ISO2CountryCode="TJ" ISO3CountryCode="TJK" IsTerritory="False" action="add" code="209" date="20-Oct-2009">Tajikistan</CountryName>
		<CountryName DJIIRegionCode="TANZA" ISO2CountryCode="TZ" ISO3CountryCode="TZA" IsTerritory="False" action="add" code="210" date="30-Jun-2010">Tanzania</CountryName>
		<CountryName DJIIRegionCode="THAIL" ISO2CountryCode="TH" ISO3CountryCode="THA" IsTerritory="False" action="add" code="211" date="20-Oct-2009">Thailand</CountryName>
		<CountryName DJIIRegionCode="TOGO" ISO2CountryCode="TG" ISO3CountryCode="TGO" IsTerritory="False" action="add" code="213" date="20-Oct-2009">Togo</CountryName>
		<CountryName DJIIRegionCode="TOKLAU" ISO2CountryCode="TK" ISO3CountryCode="TKL" IsTerritory="True" action="add" code="214" date="30-Sep-2009">Tokelau</CountryName>
		<CountryName DJIIRegionCode="TONGA" ISO2CountryCode="TO" ISO3CountryCode="TON" IsTerritory="False" action="add" code="215" date="20-Oct-2009">Tonga</CountryName>
		<CountryName DJIIRegionCode="TRTO" ISO2CountryCode="TT" ISO3CountryCode="TTO" IsTerritory="False" action="add" code="216" date="20-Oct-2009">Trinidad and Tobago</CountryName>
		<CountryName DJIIRegionCode="TUNIS" ISO2CountryCode="TN" ISO3CountryCode="TUN" IsTerritory="False" action="add" code="217" date="20-Oct-2009">Tunisia</CountryName>
		<CountryName DJIIRegionCode="TURK" ISO2CountryCode="TR" ISO3CountryCode="TUR" IsTerritory="False" action="add" code="218" date="20-Oct-2009">Turkey</CountryName>
		<CountryName DJIIRegionCode="TURKM" ISO2CountryCode="TM" ISO3CountryCode="TKM" IsTerritory="False" action="add" code="219" date="20-Oct-2009">Turkmenistan</CountryName>
		<CountryName DJIIRegionCode="TCAI" ISO2CountryCode="TC" ISO3CountryCode="TCA" IsTerritory="True" action="add" code="220" date="30-Sep-2009">Turks and Caicos Islands</CountryName>
		<CountryName DJIIRegionCode="TVLU" ISO2CountryCode="TV" ISO3CountryCode="TUV" IsTerritory="False" action="add" code="221" date="20-Oct-2009">Tuvalu</CountryName>
		<CountryName DJIIRegionCode="UGANDA" ISO2CountryCode="UG" ISO3CountryCode="UGA" IsTerritory="False" action="add" code="222" date="20-Oct-2009">Uganda</CountryName>
		<CountryName DJIIRegionCode="UKRN" ISO2CountryCode="UA" ISO3CountryCode="UKR" IsTerritory="False" action="add" code="223" date="20-Oct-2009">Ukraine</CountryName>
		<CountryName DJIIRegionCode="UAE" ISO2CountryCode="AE" ISO3CountryCode="ARE" IsTerritory="False" action="add" code="224" date="20-Oct-2009">United Arab Emirates</CountryName>
		<CountryName DJIIRegionCode="UK" ISO2CountryCode="GB" ISO3CountryCode="GBR" IsTerritory="False" action="add" code="225" date="02-Jul-2010">United Kingdom</CountryName>
		<CountryName DJIIRegionCode="USA" ISO2CountryCode="US" ISO3CountryCode="USA" IsTerritory="False" action="add" code="226" date="20-Oct-2009">United States</CountryName>
		<CountryName DJIIRegionCode="URU" ISO2CountryCode="UY" ISO3CountryCode="URY" IsTerritory="False" action="add" code="227" date="20-Oct-2009">Uruguay</CountryName>
		<CountryName DJIIRegionCode="UZBK" ISO2CountryCode="UZ" ISO3CountryCode="UZB" IsTerritory="False" action="add" code="228" date="20-Oct-2009">Uzbekistan</CountryName>
		<CountryName DJIIRegionCode="VANU" ISO2CountryCode="VU" ISO3CountryCode="VUT" IsTerritory="False" action="add" code="229" date="20-Oct-2009">Vanuatu</CountryName>
		<CountryName DJIIRegionCode="VCAN" ISO2CountryCode="VA" ISO3CountryCode="VAT" IsTerritory="False" action="add" code="230" date="20-Oct-2009">Vatican City</CountryName>
		<CountryName DJIIRegionCode="VEN" ISO2CountryCode="VE" ISO3CountryCode="VEN" IsTerritory="False" action="add" code="231" date="20-Oct-2009">Venezuela</CountryName>
		<CountryName DJIIRegionCode="VIETN" ISO2CountryCode="VN" ISO3CountryCode="VNM" IsTerritory="False" action="add" code="232" date="20-Oct-2009">Vietnam</CountryName>
		<CountryName DJIIRegionCode="WALLIS" ISO2CountryCode="WF" ISO3CountryCode="WLF" IsTerritory="True" action="add" code="233" date="30-Sep-2009">Wallis and Futuna Islands</CountryName>
		<CountryName DJIIRegionCode="SPSAH" ISO2CountryCode="EH" ISO3CountryCode="ESH" IsTerritory="True" action="add" code="234" date="20-Oct-2009">Western Sahara</CountryName>
		<CountryName DJIIRegionCode="YEMAR" ISO2CountryCode="YE" ISO3CountryCode="YEM" IsTerritory="False" action="add" code="235" date="20-Oct-2009">Yemen</CountryName>
		<CountryName DJIIRegionCode="YUG" ISO2CountryCode="RS" ISO3CountryCode="SRB" IsTerritory="False" action="add" code="236" date="17-Jun-2012">Serbia</CountryName>
		<CountryName DJIIRegionCode="ZAMBIA" ISO2CountryCode="ZM" ISO3CountryCode="ZMB" IsTerritory="False" action="add" code="237" date="20-Oct-2009">Zambia</CountryName>
		<CountryName DJIIRegionCode="ZIMBAB" ISO2CountryCode="ZW" ISO3CountryCode="ZWE" IsTerritory="False" action="add" code="238" date="20-Oct-2009">Zimbabwe</CountryName>
		<CountryName DJIIRegionCode="NONE" IsTerritory="False" action="add" code="239" date="06-Sep-2009">None</CountryName>
		<CountryName DJIIRegionCode="NOTK" IsTerritory="False" action="add" code="240" date="06-Sep-2009">Not Known</CountryName>
		<CountryName DJIIRegionCode="INTERNATIONAL" IsTerritory="False" action="add" code="241" date="06-Sep-2009">International</CountryName>
		<CountryName DJIIRegionCode="MNTNG" ISO2CountryCode="ME" ISO3CountryCode="MNE" IsTerritory="False" action="add" code="242" date="20-Oct-2009">Montenegro</CountryName>
		<CountryName DJIIRegionCode="KOSOVO" IsTerritory="False" action="add" code="243" date="27-Feb-2010">Kosovo</CountryName>
		<CountryName DJIIRegionCode="VI" ISO2CountryCode="VI" ISO3CountryCode="VIR" IsTerritory="True" action="add" code="244" date="13-Apr-2014">U.S. Virgin Islands</CountryName>
		<CountryName DJIIRegionCode="SBRTHY" ISO2CountryCode="BL" ISO3CountryCode="BLM" IsTerritory="True" action="add" code="245" date="13-Apr-2014">Saint Barthélemy</CountryName>
		<CountryName DJIIRegionCode="GUERN" ISO2CountryCode="GG" ISO3CountryCode="GGY" IsTerritory="True" action="add" code="246" date="16-Mar-2017">Guernsey</CountryName>
		<CountryName DJIIRegionCode="ISLEOM" ISO2CountryCode="IM" ISO3CountryCode="IMN" IsTerritory="True" action="add" code="247" date="02-Nov-2009">Isle of Man</CountryName>
		<CountryName DJIIRegionCode="JERSEY" ISO2CountryCode="JE" ISO3CountryCode="JEY" IsTerritory="True" action="add" code="248" date="02-Nov-2009">Jersey</CountryName>
		<CountryName DJIIRegionCode="SOSSRT" ISO2CountryCode="" ISO3CountryCode="" IsTerritory="True" action="add" code="249" date="16-Jan-2011">South Ossetia</CountryName>
		<CountryName DJIIRegionCode="ABKHAZ" ISO2CountryCode="" ISO3CountryCode="" IsTerritory="True" action="add" code="250" date="16-Jan-2011">Abkhazia</CountryName>
		<CountryName DJIIRegionCode="TURNC" ISO2CountryCode="" ISO3CountryCode="" IsTerritory="True" action="add" code="251" date="16-Jan-2011">Turkish Republic of Northern Cyprus</CountryName>
		<CountryName DJIIRegionCode="SINTMA" ISO2CountryCode="SX" ISO3CountryCode="SXM" IsTerritory="True" action="add" code="252" date="16-Mar-2017">St. Maarten</CountryName>
		<CountryName DJIIRegionCode="SOUSUD" ISO2CountryCode="SS" ISO3CountryCode="SSD" IsTerritory="False" action="add" code="253" date="25-Oct-2011">South Sudan</CountryName>
	</CountryList>
	<Description1List>
		<Description1Name Description1Code="1" RecordType="Person" action="add" date="06-Sep-2009">Politically Exposed Person (PEP)</Description1Name>
		<Description1Name Description1Code="3" RecordType="Person" action="add" date="06-Sep-2009">Special Interest Person (SIP)</Description1Name>
		<Description1Name Description1Code="4" RecordType="Entity" action="add" date="06-Sep-2009">Special Interest Entity (SIE)</Description1Name>
	</Description1List>
	<Description2List>
		<Description2Name Description1Code="1" Description2Code="1" action="add" date="01-Nov-2009">Additional Domestic Screening Requirement</Description2Name>
		<Description2Name Description1Code="3" Description2Code="4" action="add" date="06-Sep-2009">Corruption</Description2Name>
		<Description2Name Description1Code="4" Description2Code="4" action="add" date="06-Sep-2009">Corruption</Description2Name>
		<Description2Name Description1Code="3" Description2Code="5" action="add" date="06-Sep-2009">Financial Crime</Description2Name>
		<Description2Name Description1Code="4" Description2Code="5" action="add" date="06-Sep-2009">Financial Crime</Description2Name>
		<Description2Name Description1Code="3" Description2Code="6" action="add" date="06-Sep-2009">Organised Crime</Description2Name>
		<Description2Name Description1Code="4" Description2Code="6" action="add" date="06-Sep-2009">Organised Crime</Description2Name>
		<Description2Name Description1Code="3" Description2Code="7" action="add" date="06-Sep-2009">Other Official Lists</Description2Name>
		<Description2Name Description1Code="4" Description2Code="7" action="add" date="06-Sep-2009">Other Official Lists</Description2Name>
		<Description2Name Description1Code="3" Description2Code="8" action="add" date="25-Apr-2010">Sanctions Lists</Description2Name>
		<Description2Name Description1Code="4" Description2Code="8" action="add" date="06-Sep-2009">Sanctions Lists</Description2Name>
		<Description2Name Description1Code="3" Description2Code="10" action="add" date="06-Sep-2009">Terror</Description2Name>
		<Description2Name Description1Code="4" Description2Code="10" action="add" date="06-Sep-2009">Terror</Description2Name>
		<Description2Name Description1Code="3" Description2Code="11" action="add" date="20-Dec-2009">Trafficking</Description2Name>
		<Description2Name Description1Code="4" Description2Code="11" action="add" date="06-Sep-2009">Trafficking</Description2Name>
		<Description2Name Description1Code="3" Description2Code="12" action="add" date="06-Sep-2009">War Crimes</Description2Name>
		<Description2Name Description1Code="4" Description2Code="12" action="add" date="06-Sep-2009">War Crimes</Description2Name>
		<Description2Name Description1Code="3" Description2Code="14" action="add" date="16-Jan-2011">Organised Crime Japan</Description2Name>
		<Description2Name Description1Code="4" Description2Code="14" action="add" date="16-Jan-2011">Organised Crime Japan</Description2Name>
		<Description2Name Description1Code="3" Description2Code="15" action="add" date="22-Dec-2012">Enhanced Country Risk</Description2Name>
		<Description2Name Description1Code="4" Description2Code="15" action="add" date="17-Apr-2011">Enhanced Country Risk</Description2Name>
		<Description2Name Description1Code="3" Description2Code="22" action="add" date="13-Apr-2014">Tax Crime</Description2Name>
		<Description2Name Description1Code="4" Description2Code="23" action="add" date="12-Dec-2015">Sanctions Control and Ownership</Description2Name>
	</Description2List>
	<Description3List>
		<Description3Name Description2Code="7" Description3Code="1" action="add" date="06-Sep-2009">Charity</Description3Name>
		<Description3Name Description2Code="8" Description3Code="1" action="add" date="06-Sep-2009">Charity</Description3Name>
		<Description3Name Description2Code="7" Description3Code="2" action="add" date="06-Sep-2009">Company</Description3Name>
		<Description3Name Description2Code="8" Description3Code="2" action="add" date="06-Sep-2009">Company</Description3Name>
		<Description3Name Description2Code="7" Description3Code="3" action="add" date="06-Sep-2009">Country</Description3Name>
		<Description3Name Description2Code="8" Description3Code="3" action="add" date="06-Sep-2009">Country</Description3Name>
		<Description3Name Description2Code="7" Description3Code="4" action="add" date="06-Sep-2009">Criminal/Terrorist</Description3Name>
		<Description3Name Description2Code="8" Description3Code="4" action="add" date="06-Sep-2009">Criminal/Terrorist</Description3Name>
		<Description3Name Description2Code="7" Description3Code="5" action="add" date="06-Sep-2009">Government Entity</Description3Name>
		<Description3Name Description2Code="8" Description3Code="5" action="add" date="06-Sep-2009">Government Entity</Description3Name>
		<Description3Name Description2Code="7" Description3Code="6" action="add" date="06-Sep-2009">Religious Sect</Description3Name>
		<Description3Name Description2Code="8" Description3Code="6" action="add" date="06-Sep-2009">Religious Sect</Description3Name>
		<Description3Name Description2Code="7" Description3Code="7" action="add" date="03-Dec-2013">Bank</Description3Name>
		<Description3Name Description2Code="8" Description3Code="7" action="add" date="03-Dec-2013">Bank</Description3Name>
		<Description3Name Description2Code="7" Description3Code="8" action="add" date="06-Sep-2009">Ship</Description3Name>
		<Description3Name Description2Code="8" Description3Code="8" action="add" date="20-Dec-2009">Ship</Description3Name>
		<Description3Name Description2Code="15" Description3Code="8" action="add" date="25-Mar-2012">Ship</Description3Name>
		<Description3Name Description2Code="7" Description3Code="9" action="add" date="03-Dec-2013">State Owned Company</Description3Name>
		<Description3Name Description2Code="8" Description3Code="9" action="add" date="03-Dec-2013">State Owned Company</Description3Name>
		<Description3Name Description2Code="14" Description3Code="12" action="add" date="16-Jan-2011">Stimulants Control Act Violation</Description3Name>
		<Description3Name Description2Code="14" Description3Code="13" action="add" date="16-Jan-2011">Battery or Assault</Description3Name>
		<Description3Name Description2Code="14" Description3Code="14" action="add" date="16-Jan-2011">Larceny</Description3Name>
		<Description3Name Description2Code="14" Description3Code="15" action="add" date="16-Jan-2011">Robbery</Description3Name>
		<Description3Name Description2Code="14" Description3Code="16" action="add" date="16-Jan-2011">Extortion</Description3Name>
		<Description3Name Description2Code="14" Description3Code="17" action="add" date="16-Jan-2011">Fraud</Description3Name>
		<Description3Name Description2Code="14" Description3Code="18" action="add" date="16-Jan-2011">Gambling</Description3Name>
		<Description3Name Description2Code="14" Description3Code="19" action="add" date="16-Jan-2011">Bookmaking</Description3Name>
		<Description3Name Description2Code="14" Description3Code="20" action="add" date="16-Jan-2011">Intimidation</Description3Name>
		<Description3Name Description2Code="14" Description3Code="21" action="add" date="16-Jan-2011">Prostitution</Description3Name>
		<Description3Name Description2Code="14" Description3Code="22" action="add" date="16-Jan-2011">Possession of gun or knife</Description3Name>
		<Description3Name Description2Code="14" Description3Code="23" action="add" date="16-Jan-2011">Sarin and Similar Substances Act Violation</Description3Name>
		<Description3Name Description2Code="14" Description3Code="24" action="add" date="16-Jan-2011">Unfair Competition</Description3Name>
		<Description3Name Description2Code="14" Description3Code="25" action="add" date="16-Jan-2011">Murder or Manslaughter</Description3Name>
		<Description3Name Description2Code="14" Description3Code="26" action="add" date="16-Jan-2011">Illegal arrest and confinement</Description3Name>
		<Description3Name Description2Code="14" Description3Code="27" action="add" date="16-Jan-2011">Kidnapping</Description3Name>
		<Description3Name Description2Code="14" Description3Code="28" action="add" date="16-Jan-2011">Defamation</Description3Name>
		<Description3Name Description2Code="14" Description3Code="29" action="add" date="16-Jan-2011">Business Obstruction</Description3Name>
		<Description3Name Description2Code="14" Description3Code="30" action="add" date="16-Jan-2011">Other</Description3Name>
		<Description3Name Description2Code="7" Description3Code="33" action="add" date="22-Dec-2012">Aircraft</Description3Name>
		<Description3Name Description2Code="8" Description3Code="33" action="add" date="22-Dec-2012">Aircraft</Description3Name>
		<Description3Name Description2Code="15" Description3Code="33" action="add" date="22-Dec-2012">Aircraft</Description3Name>
		<Description3Name Description2Code="15" Description3Code="52" action="add" date="03-Dec-2013">State Owned Company</Description3Name>
		<Description3Name Description2Code="15" Description3Code="53" action="add" date="03-Dec-2013">Bank</Description3Name>
		<Description3Name Description2Code="4" Description3Code="54" action="add" date="22-Feb-2014">Lower Threshold</Description3Name>
		<Description3Name Description2Code="5" Description3Code="54" action="add" date="22-Feb-2014">Lower Threshold</Description3Name>
		<Description3Name Description2Code="6" Description3Code="54" action="add" date="22-Feb-2014">Lower Threshold</Description3Name>
		<Description3Name Description2Code="11" Description3Code="54" action="add" date="22-Feb-2014">Lower Threshold</Description3Name>
		<Description3Name Description2Code="22" Description3Code="54" action="add" date="13-Apr-2014">Lower Threshold</Description3Name>
		<Description3Name Description2Code="23" Description3Code="55" action="add" date="12-Dec-2015">OFAC Related - Majority Owned</Description3Name>
		<Description3Name Description2Code="23" Description3Code="56" action="add" date="12-Dec-2015">OFAC Related - Minority Owned</Description3Name>
		<Description3Name Description2Code="23" Description3Code="57" action="add" date="12-Dec-2015">OFAC Related - Ownership Unknown</Description3Name>
		<Description3Name Description2Code="23" Description3Code="58" action="add" date="12-Dec-2015">EU Related - Minority Owned</Description3Name>
		<Description3Name Description2Code="23" Description3Code="59" action="add" date="12-Dec-2015">EU Related - Majority Owned</Description3Name>
		<Description3Name Description2Code="23" Description3Code="60" action="add" date="12-Dec-2015">EU Related - Ownership Unknown</Description3Name>
		<Description3Name Description2Code="23" Description3Code="61" action="add" date="12-Dec-2015">Formerly EU Related</Description3Name>
		<Description3Name Description2Code="23" Description3Code="62" action="add" date="12-Dec-2015">Formerly OFAC Related</Description3Name>
	</Description3List>
	<ReferenceGroupList>
		<ListGroupName ReferenceGroupCode="13566" action="add" date="22-Nov-2012">Executive Order 13566, Blocking Property and Prohibiting Certain Transactions Related to Libya, Department of the Treasury, United States</ListGroupName>
		<ListGroupName ReferenceGroupCode="13599" action="add" date="20-Jan-2016">List of Persons Identified as Blocked Solely Pursuant to Executive Order 13599, Office of Foreign Assets Control, United States</ListGroupName>
		<ListGroupName ReferenceGroupCode="BISDPL" action="add" date="17-Sep-2012">Denied Persons List, Bureau of Industry &amp; Security, Department of Commerce, United States</ListGroupName>
		<ListGroupName ReferenceGroupCode="BISOT" action="add" date="17-Sep-2012">Other Lists published by Bureau of Industry &amp; Security, Department of Commerce, United States</ListGroupName>
		<ListGroupName ReferenceGroupCode="CALICA" action="add" date="06-Nov-2012">Iran Contracting Act, California Department of General Services, United States</ListGroupName>
		<ListGroupName ReferenceGroupCode="CANPUB" action="add" date="07-Nov-2012">Entities List, Public Safety and Emergency Preparedness Canada</ListGroupName>
		<ListGroupName ReferenceGroupCode="CCDI" action="add" date="01-Jan-1900">CCDI (China) 100 Fugitives List</ListGroupName>
		<ListGroupName ReferenceGroupCode="CIMAOT" action="add" date="28-Jan-2011">Other Official Lists, Cayman Islands Monetary Authority</ListGroupName>
		<ListGroupName ReferenceGroupCode="CSSFOT" action="add" date="25-Oct-2009">Other Lists published by Commission de Surveillance du Secteur Financier, Luxembourg</ListGroupName>
		<ListGroupName ReferenceGroupCode="CSSFSA" action="add" date="06-Sep-2009">Circulars concerning Anti-money laundering and terrorist financing and Circulars concerning Restrictive financial measures, Commission de Surveillance du Secteur Financier,  Luxembourg</ListGroupName>
		<ListGroupName ReferenceGroupCode="DDTCDP" action="add" date="09-Nov-2012">Statutorily and Administratively Debarred Parties, Directorate of Defense Trade Controls, Department of State, United States</ListGroupName>
		<ListGroupName ReferenceGroupCode="DFAITE" action="add" date="15-Jun-2012">Country sanctions/embargoes, Department of Foreign Affairs and International Trade, Canada</ListGroupName>
		<ListGroupName ReferenceGroupCode="DFAITF" action="add" date="17-Sep-2012">Freezing Assets of Corrupt Foreign Officials Regulations, Department of Foreign Affairs and International Trade, Canada</ListGroupName>
		<ListGroupName ReferenceGroupCode="DFAITS" action="add" date="21-Sep-2012">Special Economic Measures Regulations, Department of Foreign Affairs and International Trade, Canada</ListGroupName>
		<ListGroupName ReferenceGroupCode="DFATCO" action="add" date="06-Sep-2009">Consolidated List, Department of Foreign Affairs and Trade, Australia</ListGroupName>
		<ListGroupName ReferenceGroupCode="DFATOT" action="add" date="06-Sep-2009">Other Lists published by the Department of Foreign Affairs and Trade, Australia</ListGroupName>
		<ListGroupName ReferenceGroupCode="DFATRU" action="add" date="20-Apr-2015">Autonomous Sanctions (Russia, Crimea and Sevastopol), Department of Foreign Affairs and Trade, Australia</ListGroupName>
		<ListGroupName ReferenceGroupCode="DJ" action="add" date="06-Sep-2009">Dow Jones Provenance</ListGroupName>
		<ListGroupName ReferenceGroupCode="DOSISA" action="add" date="06-Nov-2012">Sanctions imposed under the Iran Sanctions Act, Department of State, United States</ListGroupName>
		<ListGroupName ReferenceGroupCode="DOSSST" action="add" date="05-Dec-2014">State Sponsors of Terrorism, Department of State, United States</ListGroupName>
		<ListGroupName ReferenceGroupCode="EUCON" action="add" date="07-Nov-2012">Consolidated List and equivalent Official Journal legislation, European Union</ListGroupName>
		<ListGroupName ReferenceGroupCode="EUEMB" action="add" date="01-Aug-2011">Country sanctions/embargoes, European Union</ListGroupName>
		<ListGroupName ReferenceGroupCode="EUOT" action="add" date="06-Dec-2012">Other Lists published by the European Union</ListGroupName>
		<ListGroupName ReferenceGroupCode="EUOTTE" action="add" date="12-Feb-2014">Council Decisions, Common Positions, etc. amending Common Position 2001/931/CFSP on the application of specific measures to combat terrorism, European Union</ListGroupName>
		<ListGroupName ReferenceGroupCode="EURM" action="add" date="01-Jan-1900">Property subject to European Union restrictive measures</ListGroupName>
		<ListGroupName ReferenceGroupCode="EUSSI" action="add" date="03-Jul-2017">European Union Sectoral Sanctions</ListGroupName>
		<ListGroupName ReferenceGroupCode="FATFJ" action="add" date="01-Jan-1900">Strategic Deficiencies Jurisdictions and those subject to an on-going process, Financial Action Task Force (FATF)</ListGroupName>
		<ListGroupName ReferenceGroupCode="FATFNC" action="add" date="28-Jan-2011">Non-Cooperative Countries and Territories, Financial Action Task Force</ListGroupName>
		<ListGroupName ReferenceGroupCode="FBIWAN" action="add" date="08-Feb-2013">Wanted lists published by the Federal Bureau of Investigation, United States</ListGroupName>
		<ListGroupName ReferenceGroupCode="FCSFIA" action="add" date="01-Jan-1900">Federal Council (Switzerland) Foreign Illicit Assets Act, Switzerland</ListGroupName>
		<ListGroupName ReferenceGroupCode="FIN311" action="add" date="28-Jan-2011">Section 311 - Special Measures for Jurisdictions, Financial Institutions or International Transactions of Primary Money Laundering Concern, Financial Crimes Enforcement Network, Department of the Treasury, United States</ListGroupName>
		<ListGroupName ReferenceGroupCode="FJMB" action="add" date="01-Jan-1900">Federal Justice (Belgium) MB List</ListGroupName>
		<ListGroupName ReferenceGroupCode="FRNAT" action="add" date="22-Apr-2013">Liste nationale, Ministère de l'Économie, des Finances et de l'Industrie, France</ListGroupName>
		<ListGroupName ReferenceGroupCode="GAOIR" action="add" date="06-Nov-2012">Report on Exporters of Refined Petroleum Products to Iran, Government Accountability Office, United States</ListGroupName>
		<ListGroupName ReferenceGroupCode="HKMAOT" action="add" date="06-Sep-2009">Other Lists published by the Hong Kong Monetary Authority</ListGroupName>
		<ListGroupName ReferenceGroupCode="HKMASA" action="add" date="06-Sep-2009">Hong Kong Monetary Authority Sanctions</ListGroupName>
		<ListGroupName ReferenceGroupCode="HMTASS" action="add" date="06-Sep-2009">Asset Freeze Targets, Consolidated List of Financial Sanctions Targets in the UK, Her Majesty's Treasury</ListGroupName>
		<ListGroupName ReferenceGroupCode="HMTINV" action="add" date="06-Sep-2009">Investment Ban Targets, Consolidated List of Financial Sanctions Targets in the UK, Her Majesty's Treasury</ListGroupName>
		<ListGroupName ReferenceGroupCode="HMTOT" action="add" date="09-Sep-2010">Other Lists published by UK Her Majesty's Treasury</ListGroupName>
		<ListGroupName ReferenceGroupCode="IMODIO" action="add" date="23-Feb-2015">List of illegal organisations, Defence Ministry, Israel</ListGroupName>
		<ListGroupName ReferenceGroupCode="ISNSA" action="add" date="28-Jan-2011">Nonproliferation Sanctions, Bureau of International Security and Nonproliferation, Department of State, United States</ListGroupName>
		<ListGroupName ReferenceGroupCode="JAFISA" action="add" date="06-Sep-2009">Sanctions programmes published by the Japanese Finance Ministry</ListGroupName>
		<ListGroupName ReferenceGroupCode="MASOT" action="add" date="06-Sep-2009">Other Lists published by the Monetary Authority of Singapore</ListGroupName>
		<ListGroupName ReferenceGroupCode="MASTER" action="add" date="04-Oct-2013">List associated with the Terrorism (Suppression of Financing) Act (Chapter 325), Monetary Authority of Singapore</ListGroupName>
		<ListGroupName ReferenceGroupCode="METIE" action="add" date="01-Jan-1900">METI (Japan) End User List</ListGroupName>
		<ListGroupName ReferenceGroupCode="METIEN" action="add" date="01-Jan-1900">Minister of Economy Trade and Industry (Japan) Export Control</ListGroupName>
		<ListGroupName ReferenceGroupCode="MPSTE" action="add" date="05-Sep-2011">Terrorist List, Public Security Ministry, China</ListGroupName>
		<ListGroupName ReferenceGroupCode="NLSAN" action="add" date="07-Jan-2016">Terrorism Sanctions Regulations 2007-II issued by the Netherlands government</ListGroupName>
		<ListGroupName ReferenceGroupCode="NZPONO" action="add" date="28-Jan-2011">Designated terrorist individuals and organisations - non-UN listed entities, New Zealand Police</ListGroupName>
		<ListGroupName ReferenceGroupCode="NZPOUN" action="add" date="28-Jan-2011">Designated terrorist individuals and organisations - UN Resolution 1267, New Zealand Police</ListGroupName>
		<ListGroupName ReferenceGroupCode="OCCUNB" action="add" date="20-Jun-2014">Unauthorized Banks, Office of the Comptroller of the Currency (OCC)</ListGroupName>
		<ListGroupName ReferenceGroupCode="OFACCS" action="add" date="22-Feb-2011">Country sanctions/embargoes, Office of Foreign Assets Control, United States</ListGroupName>
		<ListGroupName ReferenceGroupCode="OFACNS" action="add" date="16-Feb-2011">Palestinian Legislative Council (PLC) List, Office of Foreign Assets Control, United States</ListGroupName>
		<ListGroupName ReferenceGroupCode="OFACOT" action="add" date="06-Sep-2009">Other Lists published by the Office of Foreign Assets Control, United States</ListGroupName>
		<ListGroupName ReferenceGroupCode="OFACSD" action="add" date="06-Sep-2009">Specially Designated Nationals List, Office of Foreign Assets Control, United States</ListGroupName>
		<ListGroupName ReferenceGroupCode="OFACTR" action="add" date="22-Feb-2011">Weapons Of Mass Destruction Trade Control Regulations, 31 C.F.R Part 539, Office of Foreign Assets Control, United States</ListGroupName>
		<ListGroupName ReferenceGroupCode="OFC561" action="add" date="01-Aug-2012">List of Foreign Financial Institutions Subject to Part 561 (the Part 561 List), Office of Foreign Assets Control,  United States</ListGroupName>
		<ListGroupName ReferenceGroupCode="OFCFSE" action="add" date="07-Feb-2014">Foreign Sanctions Evaders List (Executive Order 13608), Office of Foreign Assets Control, United States</ListGroupName>
		<ListGroupName ReferenceGroupCode="OFCISA" action="add" date="28-Apr-2015">Non-SDN Iranian Sanctions Act List (NS-ISA),  Office of Foreign Assets Control, United States</ListGroupName>
		<ListGroupName ReferenceGroupCode="OFCSSI" action="add" date="16-Jul-2014">Sectoral Sanctions Identifications List (Executive Order 13662), Office of Foreign Assets Control, United States</ListGroupName>
		<ListGroupName ReferenceGroupCode="OFHFSR" action="add" date="01-Jan-1900">List of Foreign Financial Institutions Subject to Part 566 (the Part 566 List), Office of Foreign Assets Control, United States</ListGroupName>
		<ListGroupName ReferenceGroupCode="OSFIOT" action="add" date="06-Sep-2009">Non-Sanctions Lists published by the Office of the Superintendent of Financial Institutions, Canada</ListGroupName>
		<ListGroupName ReferenceGroupCode="OSFISA" action="add" date="06-Sep-2009">Other Sanctions Lists published by the Office of the Superintendent of Financial Institutions, Canada</ListGroupName>
		<ListGroupName ReferenceGroupCode="OSFITE" action="add" date="06-Sep-2009">Terrorism Financing List, Office of the Superintendent of Financial Institutions, Canada</ListGroupName>
		<ListGroupName ReferenceGroupCode="RBASA" action="add" date="26-Apr-2012">Financial Sanctions, Reserve Bank of Australia</ListGroupName>
		<ListGroupName ReferenceGroupCode="RUCBR" action="add" date="01-Jan-1900">Special Economic Measures List, Central Bank of Russia</ListGroupName>
		<ListGroupName ReferenceGroupCode="RUFFMD" action="add" date="12-Mar-2015">Federal Financial Monitoring Services (Russia) Domestic Sanctions List</ListGroupName>
		<ListGroupName ReferenceGroupCode="RUFFMF" action="add" date="04-Mar-2015">Federal Financial Monitoring Services (Russia) Foreign Sanctions List</ListGroupName>
		<ListGroupName ReferenceGroupCode="SECOEM" action="add" date="20-Dec-2012">Country sanctions/embargoes published by the Secrétariat d'Etat à l'économie, Switzerland</ListGroupName>
		<ListGroupName ReferenceGroupCode="SECOOT" action="add" date="06-Sep-2009">Other Lists published by the Secrétariat d'Etat à l'économie, Switzerland</ListGroupName>
		<ListGroupName ReferenceGroupCode="SECOSA" action="add" date="20-Jan-2013">Sanctions programmes published by the Secrétariat d'Etat à l'économie, Switzerland</ListGroupName>
		<ListGroupName ReferenceGroupCode="SECOSP" action="add" date="21-Jan-2013">Special Publication sanctions published by the Secrétariat d'Etat à l'économie, Switzerland</ListGroupName>
		<ListGroupName ReferenceGroupCode="TRA221" action="add" date="07-Feb-2013">List published pursuant to the requirements of Section 221 of the Iran Threat Reduction and Syria Human Rights Act (TRA) of 2012, Department of State, United States</ListGroupName>
		<ListGroupName ReferenceGroupCode="UKBISI" action="add" date="24-Aug-2015">Military and Weapons of Mass Destruction (WMD) End-Use Control Iran List, The Department for Business, Innovation &amp; Skills (BIS), United Kingdom</ListGroupName>
		<ListGroupName ReferenceGroupCode="UKFCAW" action="add" date="04-Jan-2017">Unauthorised Firms and Individuals, Financial Conduct Authority (FCA), United Kingdom</ListGroupName>
		<ListGroupName ReferenceGroupCode="UKHOME" action="add" date="27-Mar-2013">Proscribed terror groups or organsiations banned under UK law, Home Office, United Kingdom</ListGroupName>
		<ListGroupName ReferenceGroupCode="UNCON" action="add" date="29-Feb-2012">List published by the United Nations Security Council Committee established pursuant to resolution 1267 (1999) - individuals, groups, undertakings and entities associated with Al-Qaida</ListGroupName>
		<ListGroupName ReferenceGroupCode="UNEMB" action="add" date="29-Jun-2011">Country sanctions/embargoes, United Nations Security Council Committees</ListGroupName>
		<ListGroupName ReferenceGroupCode="UNOT" action="add" date="06-Sep-2009">Other Lists published by United Nations Security Council Committees</ListGroupName>
		<ListGroupName ReferenceGroupCode="UNRM" action="add" date="01-Jan-1900">Property subject to United Nations restrictive measures</ListGroupName>
		<ListGroupName ReferenceGroupCode="UNTAL" action="add" date="24-Aug-2015">List published by the United Nations Security Council Committee established pursuant to resolution 1988 (2011) - individuals, groups, undertakings and entities associated with the Taliban</ListGroupName>
		<ListGroupName ReferenceGroupCode="USTEL" action="add" date="15-Jun-2012">Terrorist Exclusion List, Office of the Coordinator for Counterterrorism, Department of State, United States</ListGroupName>
	</ReferenceGroupList>
	<SanctionsReferencesList>
		<ReferenceName CountryCode="226" Description2Code="7" ListProviderName="Bureau of Industry &amp; Security, Department of Commerce" ReferenceGroupCode="BISDPL" Status="Current" action="add" code="77" date="06-Sep-2009">BIS Denied Persons List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="7" ListProviderName="Bureau of Industry &amp; Security, Department of Commerce" ReferenceGroupCode="BISOT" Status="Current" action="add" code="78" date="06-Jan-2014">BIS Entity List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="7" ListProviderName="Bureau of Industry &amp; Security, Department of Commerce" ReferenceGroupCode="BISOT" Status="Current" action="add" code="80" date="03-Jul-2014">BIS Unverified List</ReferenceName>
		<ReferenceName CountryCode="45" Description2Code="8" ListProviderName="Public Security Ministry" ReferenceGroupCode="MPSTE" Status="Current" action="add" code="94" date="12-Oct-2011">Chinese Public Security Ministry Terrorist List</ReferenceName>
		<ReferenceName CountryCode="41" Description2Code="7" ListProviderName="Cayman Islands Monetary Authority (CIMA)" ReferenceGroupCode="CIMAOT" Status="Current" action="add" code="96" date="06-Sep-2009">CIMA (Cayman Islands) Non-Licensed Entities</ReferenceName>
		<ReferenceName CountryCode="41" Description2Code="7" ListProviderName="Cayman Islands Monetary Authority (CIMA)" ReferenceGroupCode="CIMAOT" Status="Current" action="add" code="97" date="16-May-2013">CIMA (Cayman Islands) Public Notices - Regulatory Action</ReferenceName>
		<ReferenceName CountryCode="122" Description2Code="8" ListProviderName="Commission de Surveillance du Secteur Financier" ReferenceGroupCode="CSSFSA" Status="Current" action="add" code="113" date="06-Sep-2009">CSSF (Luxembourg) Sanctions List</ReferenceName>
		<ReferenceName CountryCode="122" Description2Code="7" ListProviderName="Commission de Surveillance du Secteur Financier" ReferenceGroupCode="CSSFOT" Status="Current" action="add" code="114" date="21-Feb-2014">CSSF (Luxembourg) Warnings</ReferenceName>
		<ReferenceName CountryCode="39" Description2Code="8" ListProviderName="Department of Foreign Affairs, Trade and Development Canada" ReferenceGroupCode="DFAITE" Status="Current" action="add" code="118" date="03-Jul-2015">DFATD (Canada) Current Sanctions - Countries</ReferenceName>
		<ReferenceName CountryCode="39" Description2Code="8" ListProviderName="Department of Foreign Affairs, Trade and Development Canada" ReferenceGroupCode="DFAITS" Status="Current" action="add" code="119" date="03-Jul-2015">DFATD (Canada) Special Economic Measures (Burma) Regulations</ReferenceName>
		<ReferenceName CountryCode="39" Description2Code="8" ListProviderName="Department of Foreign Affairs, Trade and Development Canada" ReferenceGroupCode="DFAITS" Status="Current" action="add" code="120" date="03-Jul-2015">DFATD (Canada) Special Economic Measures (Zimbabwe) Regulations</ReferenceName>
		<ReferenceName CountryCode="13" Description2Code="8" ListProviderName="Department of Foreign Relations &amp; Trade" ReferenceGroupCode="DFATOT" Status="Current" action="add" code="121" date="27-Feb-2014">DFAT (Australia) Arms Embargoes</ReferenceName>
		<ReferenceName CountryCode="13" Description2Code="8" ListProviderName="Department of Foreign Relations &amp; Trade" ReferenceGroupCode="DFATCO" Status="Current" action="add" code="122" date="17-Mar-2014">DFAT (Australia) Consolidated List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="127" date="14-May-2010">EC 100/2004-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="128" date="14-May-2010">EC 1012/2003-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="129" date="14-May-2010">EC 1025/2007-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="133" date="09-Jun-2011">EC 1086/2004 Iraq List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="134" date="14-May-2010">EC 1087/2005 Iraq List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="136" date="14-May-2010">EC 110/2010-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="138" date="14-May-2010">EC 1102/2009-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="139" date="14-May-2010">EC 1104/2007-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="140" date="14-May-2010">EC 1109/2008-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="145" date="14-May-2010">EC 1184/2003-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="146" date="14-May-2010">EC 1187/2004-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="147" date="14-May-2010">EC 1189/2006-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="149" date="14-May-2010">EC 1190/2008-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="151" date="14-May-2010">EC 1210/2003 Iraq List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="152" date="14-May-2010">EC 1210/2006-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="154" date="14-May-2010">EC 1217/2006-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="155" date="14-May-2010">EC 1220/2009-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="157" date="14-May-2010">EC 1228/2006-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="158" date="14-May-2010">EC 1237/2004-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="163" date="09-Jun-2011">EC 1277/2004-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="164" date="14-May-2010">EC 1278/2005-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="165" date="09-Jun-2011">EC 1283/2009 Democratic People's Republic of Korea List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="168" date="14-May-2010">EC 1286/2005 Iraq List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="169" date="14-May-2010">EC 1286/2006-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="170" date="08-Jun-2011">EC 1291/2007-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="171" date="14-May-2010">EC 1330/2008-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="173" date="14-May-2010">EC 1378/2005-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="175" date="14-May-2010">EC 14/2007-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="178" date="14-May-2010">EC 1456/2003-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="180" date="14-May-2010">EC 1508/2006-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="182" date="09-Jun-2011">EC 1551/2005-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="183" date="14-May-2010">EC 1580/2002-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="186" date="14-May-2010">EC 1607/2003-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="187" date="14-May-2010">EC 1629/2005-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="189" date="09-Jun-2011">EC 1644/2002-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="190" date="09-Jun-2011">EC 1685/2006-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="191" date="14-May-2010">EC 1690/2005-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="192" date="14-May-2010">EC 1724/2003-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="193" date="09-Jun-2011">EC 1728/2004-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="194" date="09-Jun-2011">EC 1754/2002-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="196" date="14-May-2010">EC 180/2004-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="197" date="14-May-2010">EC 1823/2002-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="198" date="14-May-2010">EC 1823/2006-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="200" date="14-May-2010">EC 1825/2005-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="201" date="14-May-2010">EC 184/2009-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="202" date="14-May-2010">EC 1840/2004-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="204" date="14-May-2010">EC 187/2005-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="206" date="14-May-2010">EC 1893/2002-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="207" date="09-Jun-2011">EC 19/2004-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="208" date="14-May-2010">EC 1935/2002-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="212" date="14-May-2010">EC 198/2008-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="213" date="14-May-2010">EC 1991/2003-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="299" date="14-May-2010">EC 2018/2005-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="301" date="14-May-2010">EC 2034/2004-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="302" date="09-Jun-2011">EC 2049/2003-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="303" date="14-May-2010">EC 2083/2002-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="305" date="14-May-2010">EC 2119/2003 Iraq List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="306" date="09-Jun-2011">EC 2145/2004-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="307" date="09-Jun-2011">EC 215/2003-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="308" date="14-May-2010">EC 2157/2003-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="310" date="14-May-2010">EC 220/2008-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="317" date="14-May-2010">EC 244/2003-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="320" date="19-Sep-2013">EC 262/2010-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="324" date="14-May-2010">EC 290/2010-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="326" date="14-May-2010">EC 301/2005-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="331" date="14-May-2010">EC 344/2009-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="332" date="14-May-2010">EC 350/2003-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="336" date="19-Sep-2013">EC 366/2010-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="337" date="14-May-2010">EC 370/2003-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="338" date="14-May-2010">EC 372/2010-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="339" date="14-May-2010">EC 374/2008-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="342" date="14-May-2010">EC 391/2004-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="344" date="14-May-2010">EC 400/2008-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="347" date="14-May-2010">EC 414/2003-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="350" date="14-May-2010">EC 46/2008-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="354" date="14-May-2010">EC 492/2007-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="357" date="14-May-2010">EC 507/2007-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="358" date="09-Jun-2011">EC 524/2004-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="359" date="14-May-2010">EC 553/2007-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="360" date="19-Sep-2013">EC 574/2009-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="361" date="14-May-2010">EC 580/2008-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="362" date="19-Sep-2013">EC 59/2008-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="363" date="19-Sep-2013">EC 601/2009-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="366" date="14-May-2010">EC 639/2007-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="367" date="14-May-2010">EC 667/2004-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="369" date="14-May-2010">EC 674/2006-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="370" date="19-Sep-2013">EC 678/2008-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="372" date="14-May-2010">EC 70/2010-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="375" date="14-May-2010">EC 717/2005-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="376" date="19-Sep-2013">EC 732/2007-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="377" date="14-May-2010">EC 732/2009-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="379" date="14-May-2010">EC 757/2005-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="380" date="14-May-2010">EC 76/2006-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="382" date="14-May-2010">EC 760/2007-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="386" date="14-May-2010">EC 785/2006 Iraq List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="389" date="19-Sep-2013">EC 803/2008-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="393" date="14-May-2010">EC 844/2007-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="394" date="14-May-2010">EC 853/2005-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="396" date="14-May-2010">EC 866/2003-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="400" date="14-May-2010">EC 881/2002-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="403" date="14-May-2010">EC 924/2004 Iraq List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="405" date="14-May-2010">EC 950/2004-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="406" date="14-May-2010">EC 951/2002-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="408" date="14-May-2010">EC 969/2007-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="411" date="14-May-2010">EC 979/2004 Iraq List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="412" date="14-May-2010">EC 984/2004-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="413" date="14-May-2010">EC 996/2007-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUEMB" Status="Current" action="add" code="416" date="01-Aug-2011">EU Embargoes On Arms And Related Materiel</ReferenceName>
		<ReferenceName CountryCode="241" Description2Code="7" ListProviderName="The Financial Action Task Force (FATF)" ReferenceGroupCode="FATFJ" Status="Current" action="add" code="418" date="31-May-2016">FATF Strategic Deficiencies Jurisdictions</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="7" ListProviderName="Federal Bureau of Investigation (FBI)" ReferenceGroupCode="FBIWAN" Status="Current" action="add" code="419" date="25-Feb-2013">FBI 9/11 Suspected Hijackers</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="7" ListProviderName="Federal Bureau of Investigation (FBI)" ReferenceGroupCode="FBIWAN" Status="Current" action="add" code="420" date="28-May-2014">FBI Crime Alert</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="7" ListProviderName="Federal Bureau of Investigation (FBI)" ReferenceGroupCode="FBIWAN" Status="Current" action="add" code="421" date="08-Feb-2013">FBI Featured Fugitives - Crimes Against Children</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="7" ListProviderName="Federal Bureau of Investigation (FBI)" ReferenceGroupCode="FBIWAN" Status="Current" action="add" code="422" date="08-Feb-2013">FBI Featured Fugitives - Criminal Enterprise Investigations</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="7" ListProviderName="Federal Bureau of Investigation (FBI)" ReferenceGroupCode="FBIWAN" Status="Current" action="add" code="423" date="08-Feb-2013">FBI Featured Fugitives - Cyber Crimes</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="7" ListProviderName="Federal Bureau of Investigation (FBI)" ReferenceGroupCode="FBIWAN" Status="Current" action="add" code="424" date="08-Feb-2013">FBI Featured Fugitives - Domestic Terrorism</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="7" ListProviderName="Federal Bureau of Investigation (FBI)" ReferenceGroupCode="FBIWAN" Status="Current" action="add" code="425" date="08-Feb-2013">FBI Featured Fugitives - Violent Crimes</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="7" ListProviderName="Federal Bureau of Investigation (FBI)" ReferenceGroupCode="FBIWAN" Status="Current" action="add" code="426" date="08-Feb-2013">FBI Featured Fugitives - White Collar Crimes</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="7" ListProviderName="Federal Bureau of Investigation (FBI)" ReferenceGroupCode="FBIWAN" Status="Current" action="add" code="427" date="08-Feb-2013">FBI Most Wanted Fugitive List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="7" ListProviderName="Federal Bureau of Investigation (FBI)" ReferenceGroupCode="FBIWAN" Status="Current" action="add" code="428" date="08-Feb-2013">FBI Most Wanted Terrorists List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="7" ListProviderName="Federal Bureau of Investigation (FBI)" ReferenceGroupCode="FBIWAN" Status="Current" action="add" code="429" date="08-Feb-2013">FBI Seeking Information (War on Terrorism) List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="7" ListProviderName="Financial Crimes Enforcement Network, Department of the Treasury" ReferenceGroupCode="FIN311" Status="Current" action="add" code="439" date="17-Aug-2016">FinCEN Section 311 - Special Measures</ReferenceName>
		<ReferenceName CountryCode="75" Description2Code="8" ListProviderName="Ministère de l'Économie, des Finances et de l'Industrie" ReferenceGroupCode="FRNAT" Status="Current" action="add" code="443" date="14-Feb-2013">French Economy Ministry National List - Article L.562</ReferenceName>
		<ReferenceName CountryCode="96" Description2Code="7" ListProviderName="Hong Kong Monetary Authority" ReferenceGroupCode="HKMAOT" Status="Current" action="add" code="460" date="07-Oct-2011">HKMA (Hong Kong) Fraudulent Websites &amp; E-mails List</ReferenceName>
		<ReferenceName CountryCode="96" Description2Code="8" ListProviderName="Hong Kong Monetary Authority" ReferenceGroupCode="HKMASA" Status="Current" action="add" code="461" date="06-Sep-2009">HKMA (Hong Kong) List</ReferenceName>
		<ReferenceName CountryCode="225" Description2Code="8" ListProviderName="HM Treasury" ReferenceGroupCode="HMTASS" Status="Current" action="add" code="462" date="10-Aug-2013">HM Treasury Consolidated List</ReferenceName>
		<ReferenceName CountryCode="225" Description2Code="8" ListProviderName="HM Treasury" ReferenceGroupCode="HMTINV" Status="Current" action="add" code="463" date="04-Aug-2014">HM Treasury Investment Ban List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Bureau of International Security and Nonproliferation (ISN), Department of State" ReferenceGroupCode="ISNSA" Status="Current" action="add" code="481" date="07-Nov-2014">ISN (US) Sanctions - Chemical &amp; Biological Weapons</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Bureau of International Security and Nonproliferation (ISN), Department of State" ReferenceGroupCode="ISNSA" Status="Current" action="add" code="482" date="07-Nov-2014">ISN (US) Sanctions - Executive Order 12938</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Bureau of International Security and Nonproliferation (ISN), Department of State" ReferenceGroupCode="ISNSA" Status="Current" action="add" code="483" date="07-Nov-2014">ISN (US) Sanctions - Iran Nonproliferation Act of 2000</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Bureau of International Security and Nonproliferation (ISN), Department of State" ReferenceGroupCode="ISNSA" Status="Current" action="add" code="484" date="07-Nov-2014">ISN (US) Sanctions - Iran, North Korea, and Syria Nonproliferation Act</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Bureau of International Security and Nonproliferation (ISN), Department of State" ReferenceGroupCode="ISNSA" Status="Current" action="add" code="485" date="07-Nov-2014">ISN (US) Sanctions - Iran-Iraq</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Bureau of International Security and Nonproliferation (ISN), Department of State" ReferenceGroupCode="ISNSA" Status="Current" action="add" code="486" date="07-Nov-2014">ISN (US) Sanctions - Missile Sanctions Laws</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Bureau of International Security and Nonproliferation (ISN), Department of State" ReferenceGroupCode="ISNSA" Status="Current" action="add" code="487" date="07-Nov-2014">ISN (US) Sanctions - Transfer of Lethal Military Equipment</ReferenceName>
		<ReferenceName CountryCode="104" Description2Code="8" ListProviderName="Defence Ministry" ReferenceGroupCode="IMODIO" Status="Current" action="add" code="488" date="23-Feb-2015">Israeli Defence Ministry Illegal Organisations List</ReferenceName>
		<ReferenceName CountryCode="107" Description2Code="8" ListProviderName="Ministry of Finance Japan" ReferenceGroupCode="JAFISA" Status="Current" action="add" code="492" date="20-Sep-2013">Japanese Finance Ministry - Democratic Republic of the Congo List</ReferenceName>
		<ReferenceName CountryCode="107" Description2Code="8" ListProviderName="Ministry of Finance Japan" ReferenceGroupCode="JAFISA" Status="Current" action="add" code="493" date="20-Sep-2013">Japanese Finance Ministry - Iran List</ReferenceName>
		<ReferenceName CountryCode="107" Description2Code="8" ListProviderName="Ministry of Finance Japan" ReferenceGroupCode="JAFISA" Status="Current" action="add" code="494" date="20-Sep-2013">Japanese Finance Ministry - Iraq List</ReferenceName>
		<ReferenceName CountryCode="107" Description2Code="8" ListProviderName="Ministry of Finance Japan" ReferenceGroupCode="JAFISA" Status="Current" action="add" code="496" date="20-Sep-2013">Japanese Finance Ministry - North Korea List</ReferenceName>
		<ReferenceName CountryCode="107" Description2Code="8" ListProviderName="Ministry of Finance Japan" ReferenceGroupCode="JAFISA" Status="Current" action="add" code="497" date="20-Sep-2013">Japanese Finance Ministry - Sudan List</ReferenceName>
		<ReferenceName CountryCode="107" Description2Code="8" ListProviderName="Ministry of Finance Japan" ReferenceGroupCode="JAFISA" Status="Current" action="add" code="498" date="20-Sep-2013">Japanese Finance Ministry - Taliban List</ReferenceName>
		<ReferenceName CountryCode="107" Description2Code="8" ListProviderName="Ministry of Finance Japan" ReferenceGroupCode="JAFISA" Status="Current" action="add" code="499" date="20-Sep-2013">Japanese Finance Ministry - Terrorist List</ReferenceName>
		<ReferenceName CountryCode="107" Description2Code="8" ListProviderName="Ministry of Finance Japan" ReferenceGroupCode="JAFISA" Status="Current" action="add" code="500" date="20-Sep-2013">Japanese Finance Ministry - Yugoslavia List</ReferenceName>
		<ReferenceName CountryCode="186" Description2Code="8" ListProviderName="Monetary Authority of Singapore" ReferenceGroupCode="MASTER" Status="Current" action="add" code="509" date="04-Oct-2013">MAS (Singapore) Terrorism Suppression of Financing Act</ReferenceName>
		<ReferenceName CountryCode="186" Description2Code="7" ListProviderName="Monetary Authority of Singapore" ReferenceGroupCode="MASOT" Status="Current" action="add" code="510" date="03-Oct-2013">MAS (Singapore) Enforcement Actions</ReferenceName>
		<ReferenceName CountryCode="186" Description2Code="7" ListProviderName="Monetary Authority of Singapore" ReferenceGroupCode="MASOT" Status="Current" action="add" code="511" date="27-Jun-2012">MAS (Singapore) Investor Alert List</ReferenceName>
		<ReferenceName CountryCode="107" Description2Code="7" ListProviderName="Ministry of Economy, Trade and Industry (METI)" ReferenceGroupCode="METIE" Status="Current" action="add" code="514" date="04-Aug-2015">METI (Japan) End User List</ReferenceName>
		<ReferenceName CountryCode="151" Description2Code="8" ListProviderName="New Zealand Police" ReferenceGroupCode="NZPOUN" Status="Current" action="add" code="541" date="13-Aug-2014">New Zealand Police Designated Terrorists</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="7" ListProviderName="Office of the Comptroller of the Currency (OCC)" ReferenceGroupCode="OCCUNB" Status="Current" action="add" code="545" date="06-Sep-2009">OCC Unauthorized Banks List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Office of Foreign Assets Control, Department of the Treasury" ReferenceGroupCode="OFACSD" Status="Current" action="add" code="547" date="20-Feb-2006">OFAC - Blocked Pending Investigation-Patriot Act List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Office of Foreign Assets Control, Department of the Treasury" ReferenceGroupCode="OFACSD" Status="Current" action="add" code="548" date="06-Sep-2009">OFAC - Blocked Pending Investigation-SDNT List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Office of Foreign Assets Control, Department of the Treasury" ReferenceGroupCode="OFACSD" Status="Current" action="add" code="549" date="06-Sep-2009">OFAC - Blocked Pending Investigation-SDNTK List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Office of Foreign Assets Control, Department of the Treasury" ReferenceGroupCode="OFACCS" Status="Current" action="add" code="550" date="18-Feb-2015">OFAC - Country-related Sanctions Programs</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="7" ListProviderName="Office of Foreign Assets Control, Department of the Treasury" ReferenceGroupCode="OFACOT" Status="Current" action="add" code="551" date="06-Sep-2009">OFAC - Enforcement Actions</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Office of Foreign Assets Control, Department of the Treasury" ReferenceGroupCode="OFACNS" Status="Current" action="add" code="552" date="16-Feb-2011">OFAC - Palestinian Legislative Council List (NS-PLC)</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Office of Foreign Assets Control, Department of the Treasury" ReferenceGroupCode="OFACSD" Status="Current" action="add" code="553" date="13-Dec-2010">OFAC - Principal Significant Foreign Narcotics Trafficker List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Office of Foreign Assets Control, Department of the Treasury" ReferenceGroupCode="OFACSD" Status="Current" action="add" code="554" date="13-Dec-2010">OFAC - Specially Designated Global Terrorist List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Office of Foreign Assets Control, Department of the Treasury" ReferenceGroupCode="OFACSD" Status="Current" action="add" code="555" date="13-Dec-2010">OFAC - Specially Designated Narcotics Trafficker List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Office of Foreign Assets Control, Department of the Treasury" ReferenceGroupCode="OFACSD" Status="Current" action="add" code="556" date="13-Dec-2010">OFAC - Specially Designated National List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Office of Foreign Assets Control, Department of the Treasury" ReferenceGroupCode="OFACSD" Status="Current" action="add" code="557" date="13-Dec-2010">OFAC - Specially Designated Terrorist List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Office of Foreign Assets Control, Department of the Treasury" ReferenceGroupCode="OFACSD" Status="Current" action="add" code="558" date="08-Mar-2013">OFAC - WMD Proliferators &amp; Supporters List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Office of Foreign Assets Control, Department of the Treasury" ReferenceGroupCode="OFACTR" Status="Current" action="add" code="559" date="22-Feb-2011">OFAC - WMD Trade Control Regulations List</ReferenceName>
		<ReferenceName CountryCode="39" Description2Code="7" ListProviderName="Office of the Superintendent of Financial Institutions-Bureau du surintendant des institutions financières" ReferenceGroupCode="OSFIOT" Status="Current" action="add" code="563" date="06-Dec-2013">OSFI-BSIF (Canada) Cumulative Warnings List</ReferenceName>
		<ReferenceName CountryCode="39" Description2Code="8" ListProviderName="Office of the Superintendent of Financial Institutions-Bureau du surintendant des institutions financières" ReferenceGroupCode="OSFITE" Status="Current" action="add" code="564" date="27-Nov-2013">OSFI-BSIF (Canada) Entities List</ReferenceName>
		<ReferenceName CountryCode="39" Description2Code="8" ListProviderName="Office of the Superintendent of Financial Institutions-Bureau du surintendant des institutions financières" ReferenceGroupCode="OSFITE" Status="Current" action="add" code="565" date="27-Nov-2013">OSFI-BSIF (Canada) Individuals List</ReferenceName>
		<ReferenceName CountryCode="39" Description2Code="8" ListProviderName="Office of the Superintendent of Financial Institutions-Bureau du surintendant des institutions financières" ReferenceGroupCode="OSFISA" Status="Current" action="add" code="566" date="16-Dec-2013">OSFI-BSIF (Canada) Iran Sanctions</ReferenceName>
		<ReferenceName CountryCode="39" Description2Code="8" ListProviderName="Office of the Superintendent of Financial Institutions-Bureau du surintendant des institutions financières" ReferenceGroupCode="OSFISA" Status="Current" action="add" code="567" date="27-Nov-2013">OSFI-BSIF (Canada) North Korea Sanctions</ReferenceName>
		<ReferenceName CountryCode="39" Description2Code="8" ListProviderName="Public Safety and Emergency Preparedness Canada" ReferenceGroupCode="CANPUB" Status="Current" action="add" code="574" date="26-Aug-2013">Public Safety (Canada) Entities List</ReferenceName>
		<ReferenceName CountryCode="206" Description2Code="8" ListProviderName="Secrétariat d'Etat à l'économie (State Secretariat for Economic Affairs)" ReferenceGroupCode="SECOSA" Status="Current" action="add" code="608" date="14-Dec-2012">SECO (Switzerland) Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName CountryCode="206" Description2Code="8" ListProviderName="Secrétariat d'Etat à l'économie (State Secretariat for Economic Affairs)" ReferenceGroupCode="SECOOT" Status="Current" action="add" code="609" date="06-Sep-2009">SECO (Switzerland) Arms Embargoes</ReferenceName>
		<ReferenceName CountryCode="206" Description2Code="8" ListProviderName="Secrétariat d'Etat à l'économie (State Secretariat for Economic Affairs)" ReferenceGroupCode="SECOSA" Status="Current" action="add" code="610" date="26-Dec-2012">SECO (Switzerland) Belarus List</ReferenceName>
		<ReferenceName CountryCode="206" Description2Code="8" ListProviderName="Secrétariat d'Etat à l'économie (State Secretariat for Economic Affairs)" ReferenceGroupCode="SECOSA" Status="Current" action="add" code="612" date="28-Dec-2012">SECO (Switzerland) Democratic People’s Republic of Korea List</ReferenceName>
		<ReferenceName CountryCode="206" Description2Code="8" ListProviderName="Secrétariat d'Etat à l'économie (State Secretariat for Economic Affairs)" ReferenceGroupCode="SECOSA" Status="Current" action="add" code="613" date="19-Dec-2012">SECO (Switzerland) Democratic Republic of the Congo List</ReferenceName>
		<ReferenceName CountryCode="206" Description2Code="8" ListProviderName="Secrétariat d'Etat à l'économie (State Secretariat for Economic Affairs)" ReferenceGroupCode="SECOSA" Status="Current" action="add" code="614" date="28-Dec-2012">SECO (Switzerland) Guinea List</ReferenceName>
		<ReferenceName CountryCode="206" Description2Code="8" ListProviderName="Secrétariat d'Etat à l'économie (State Secretariat for Economic Affairs)" ReferenceGroupCode="SECOSA" Status="Current" action="add" code="615" date="18-Nov-2011">SECO (Switzerland) Iran List</ReferenceName>
		<ReferenceName CountryCode="206" Description2Code="8" ListProviderName="Secrétariat d'Etat à l'économie (State Secretariat for Economic Affairs)" ReferenceGroupCode="SECOSA" Status="Current" action="add" code="616" date="26-Dec-2012">SECO (Switzerland) Iraq List</ReferenceName>
		<ReferenceName CountryCode="206" Description2Code="8" ListProviderName="Secrétariat d'Etat à l'économie (State Secretariat for Economic Affairs)" ReferenceGroupCode="SECOSA" Status="Current" action="add" code="620" date="28-Dec-2012">SECO (Switzerland) Somalia List</ReferenceName>
		<ReferenceName CountryCode="206" Description2Code="8" ListProviderName="Secrétariat d'Etat à l'économie (State Secretariat for Economic Affairs)" ReferenceGroupCode="SECOSA" Status="Current" action="add" code="621" date="28-Dec-2012">SECO (Switzerland) Sudan List</ReferenceName>
		<ReferenceName CountryCode="206" Description2Code="8" ListProviderName="Secrétariat d'Etat à l'économie (State Secretariat for Economic Affairs)" ReferenceGroupCode="SECOSA" Status="Current" action="add" code="624" date="05-Dec-2012">SECO (Switzerland) Zimbabwe List</ReferenceName>
		<ReferenceName CountryCode="225" Description2Code="8" ListProviderName="Home Office" ReferenceGroupCode="UKHOME" Status="Current" action="add" code="657" date="19-Jul-2013">UK Proscribed Terrorist Group</ReferenceName>
		<ReferenceName CountryCode="241" Description2Code="8" ListProviderName="United Nations Security Council Sanctions Committees" ReferenceGroupCode="UNEMB" Status="Current" action="add" code="667" date="11-Mar-2014">UN SC Sanctions Committees Arms Embargoes</ReferenceName>
		<ReferenceName CountryCode="241" Description2Code="8" ListProviderName="United Nations Security Council Sanctions Committees" ReferenceGroupCode="UNOT" Status="Current" action="add" code="671" date="06-Sep-2009">UN Security Council Resolution 1483 (2003) Iraq List</ReferenceName>
		<ReferenceName CountryCode="241" Description2Code="8" ListProviderName="United Nations Security Council Sanctions Committees" ReferenceGroupCode="UNOT" Status="Current" action="add" code="675" date="24-Feb-2011">UN Security Council Resolution 1596 (2005) DR Congo List</ReferenceName>
		<ReferenceName CountryCode="241" Description2Code="8" ListProviderName="United Nations Security Council Sanctions Committees" ReferenceGroupCode="UNOT" Status="Current" action="add" code="676" date="06-Sep-2009">UN Security Council Resolution 1672 (2006) Sudan</ReferenceName>
		<ReferenceName CountryCode="241" Description2Code="8" ListProviderName="United Nations Security Council Sanctions Committees" ReferenceGroupCode="UNOT" Status="Current" action="add" code="677" date="27-Apr-2009">UN Security Council Resolution 1718 (2006) North Korea List</ReferenceName>
		<ReferenceName CountryCode="241" Description2Code="8" ListProviderName="United Nations Security Council Sanctions Committees" ReferenceGroupCode="UNOT" Status="Current" action="add" code="681" date="14-Apr-2010">UN Security Council Resolution 1844 (2008) Somalia and Eritrea List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="7" ListProviderName="Directorate of Defense Trade Controls, Department of State" ReferenceGroupCode="DDTCDP" Status="Current" action="add" code="685" date="06-Sep-2009">US Defense Trade Controls Administratively Debarred Parties</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="7" ListProviderName="Directorate of Defense Trade Controls, Department of State" ReferenceGroupCode="DDTCDP" Status="Current" action="add" code="686" date="05-Oct-2009">US Defense Trade Controls Debarred Parties List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Office of the Coordinator for Counterterrorism, Department of State" ReferenceGroupCode="OFACSD" Status="Current" action="add" code="692" date="27-Jan-2012">US Foreign Terrorist Organization</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Office of the Coordinator for Counterterrorism, Department of State" ReferenceGroupCode="USTEL" Status="Current" action="add" code="700" date="11-Jan-2012">US Terrorist Exclusion List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="722" date="26-May-2010">EC 450/2010-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName CountryCode="107" Description2Code="8" ListProviderName="Ministry of Finance Japan" ReferenceGroupCode="JAFISA" Status="Current" action="add" code="727" date="20-Sep-2013">Japanese Finance Ministry - Somalia List</ReferenceName>
		<ReferenceName CountryCode="148" Description2Code="8" ListProviderName="Netherlands Government" ReferenceGroupCode="NLSAN" Status="Current" action="add" code="729" date="30-Nov-2015">Netherlands Antiterrorist Sanctions Regulation 2007-II</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="733" date="19-Sep-2013">EC 663/2010-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="736" date="19-Sep-2013">EC 681/2010-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName CountryCode="39" Description2Code="8" ListProviderName="Department of Foreign Affairs, Trade and Development Canada" ReferenceGroupCode="DFAITS" Status="Current" action="add" code="737" date="03-Jul-2015">DFATD (Canada) Special Economic Measures (Iran) Regulations</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Office of Foreign Assets Control, Department of the Treasury" ReferenceGroupCode="OFACSD" Status="Current" action="add" code="739" date="13-Dec-2010">OFAC - Islamic Revolutionary Guard Corps List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Office of Foreign Assets Control, Department of the Treasury" ReferenceGroupCode="OFACSD" Status="Current" action="add" code="740" date="17-Feb-2011">OFAC - Iranian Financial Sanctions Regulations</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="744" date="19-Sep-2013">EC 787/2010-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="749" date="28-Sep-2010">EC 851/2010-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Office of Foreign Assets Control, Department of the Treasury" ReferenceGroupCode="OFACSD" Status="Current" action="add" code="751" date="13-Dec-2010">OFAC - Iran Human Rights List</ReferenceName>
		<ReferenceName CountryCode="241" Description2Code="7" ListProviderName="The Financial Action Task Force (FATF)" ReferenceGroupCode="FATFJ" Status="Current" action="add" code="760" date="31-May-2016">FATF Strategic Deficiencies Jurisdictions - On-going Process</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="761" date="08-Nov-2010">EC 1001/2010-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName CountryCode="151" Description2Code="8" ListProviderName="New Zealand Police" ReferenceGroupCode="NZPONO" Status="Current" action="add" code="763" date="13-Aug-2014">New Zealand Police Designated Terrorists - Non-UN Listed Entities</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="776" date="17-Dec-2010">EC 1204/2010-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="788" date="19-Jan-2011">EC 36/2011-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="798" date="04-Feb-2011">EC 98/2011-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="799" date="31-Jul-2013">EC 101/2011 Tunisia List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="808" date="19-Sep-2013">EC 178/2011-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName CountryCode="241" Description2Code="8" ListProviderName="United Nations Security Council Sanctions Committees" ReferenceGroupCode="UNOT" Status="Current" action="add" code="810" date="28-Feb-2011">UN Security Council Resolution 1970 (2011) Libya List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Department of the Treasury" ReferenceGroupCode="13566" Status="Current" action="add" code="814" date="21-Nov-2012">US Executive Order 13566</ReferenceName>
		<ReferenceName CountryCode="107" Description2Code="8" ListProviderName="Ministry of Finance Japan" ReferenceGroupCode="JAFISA" Status="Current" action="add" code="817" date="20-Sep-2013">Japanese Finance Ministry - Libya List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="821" date="19-Sep-2013">EC 260/2011-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName CountryCode="241" Description2Code="8" ListProviderName="United Nations Security Council Sanctions Committees" ReferenceGroupCode="UNOT" Status="Current" action="add" code="823" date="18-Mar-2011">UN Security Council Resolution 1973 (2011) Libya List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="825" date="22-Mar-2011">EC 270/2011 Egypt List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="830" date="22-Mar-2011">EC 269/2011 Republic of Guinea List</ReferenceName>
		<ReferenceName CountryCode="39" Description2Code="8" ListProviderName="Department of Foreign Affairs, Trade and Development Canada" ReferenceGroupCode="DFAITF" Status="Current" action="add" code="835" date="09-Aug-2016">DFATD (Canada) Freezing Assets of Corrupt Foreign Officials (Tunisia) Regulations</ReferenceName>
		<ReferenceName CountryCode="206" Description2Code="8" ListProviderName="Secrétariat d'Etat à l'économie (State Secretariat for Economic Affairs)" ReferenceGroupCode="SECOSA" Status="Current" action="add" code="836" date="16-Jan-2013">SECO (Switzerland) Libya List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="837" date="01-Apr-2011">EC 317/2011-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="843" date="19-Sep-2013">EC 359/2011 Iran List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="852" date="19-Sep-2013">EC 480/2011-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName CountryCode="206" Description2Code="8" ListProviderName="Secrétariat d'Etat à l'économie (State Secretariat for Economic Affairs)" ReferenceGroupCode="SECOSA" Status="Current" action="add" code="853" date="07-Jan-2013">SECO (Switzerland) Syria List</ReferenceName>
		<ReferenceName CountryCode="39" Description2Code="8" ListProviderName="Department of Foreign Affairs, Trade and Development Canada" ReferenceGroupCode="DFAITS" Status="Current" action="add" code="862" date="03-Jul-2015">DFATD (Canada) Special Economic Measures (Syria) Regulations</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Department of State" ReferenceGroupCode="DOSISA" Status="Current" action="add" code="866" date="13-Nov-2012">US Department of State – Iran Sanctions Act</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="869" date="19-Sep-2013">EC 577/2011-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="873" date="22-Jun-2011">EC 597/2011-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="7" ListProviderName="Government Accountability Office (GAO)" ReferenceGroupCode="GAOIR" Status="Current" action="add" code="875" date="29-Jan-2013">GAO (US) Report on Exporters of Refined Petroleum Products to Iran</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="878" date="27-Jun-2011">EC 621/2011-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName CountryCode="241" Description2Code="8" ListProviderName="United Nations Security Council Sanctions Committees" ReferenceGroupCode="UNTAL" Status="Current" action="add" code="879" date="06-Mar-2013">UN Taliban Sanctions List</ReferenceName>
		<ReferenceName CountryCode="241" Description2Code="8" ListProviderName="United Nations Security Council Sanctions Committees" ReferenceGroupCode="UNCON" Status="Current" action="add" code="880" date="01-Aug-2016">UN ISIL (Da'esh) and Al-Qaida Sanctions List</ReferenceName>
		<ReferenceName CountryCode="176" Description2Code="8" ListProviderName="Federal Financial Monitoring Services" ReferenceGroupCode="RUFFMD" Status="Current" action="add" code="883" date="11-Feb-2013">Federal Financial Monitoring Services (Russia) Domestic Sanctions List</ReferenceName>
		<ReferenceName CountryCode="176" Description2Code="8" ListProviderName="Federal Financial Monitoring Services" ReferenceGroupCode="RUFFMF" Status="Current" action="add" code="884" date="11-Feb-2013">Federal Financial Monitoring Services (Russia) Foreign Sanctions List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="891" date="01-Aug-2011">EC 748/2011-Al-Qaida &amp; Taliban List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="896" date="10-Aug-2011">EC 796/2011 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="902" date="19-Sep-2013">EC 853/2011 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="903" date="19-Sep-2013">EC 876/2011 Al-Qaida List</ReferenceName>
		<ReferenceName CountryCode="107" Description2Code="8" ListProviderName="Ministry of Finance Japan" ReferenceGroupCode="JAFISA" Status="Current" action="add" code="908" date="20-Sep-2013">Japanese Finance Ministry - Syria List</ReferenceName>
		<ReferenceName CountryCode="241" Description2Code="8" ListProviderName="United Nations Security Council Sanctions Committees" ReferenceGroupCode="UNOT" Status="Current" action="add" code="909" date="19-Sep-2011">UN Security Council Resolution 2009 (2011) Libya List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="912" date="27-Sep-2011">EC 956/2011 Somalia List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="914" date="19-Sep-2013">EC 960/2011 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="916" date="19-Sep-2013">EC 965/2011 Libya List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="922" date="12-Oct-2011">EC 1002/2011 Iran List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="926" date="17-Oct-2011">EC 1024/2011 Al-Qaida List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="7" ListProviderName="California Department of General Services" ReferenceGroupCode="CALICA" Status="Current" action="add" code="927" date="19-Oct-2011">California Iran Contracting Act of 2010</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="946" date="10-Dec-2011">EC 1285/2011 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="951" date="21-Dec-2011">EC 1355/2011 Democratic People's Republic of Korea List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="963" date="06-Jul-2012">EC 34/2012 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="964" date="28-Feb-2012">EC 36/2012 Syria List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1010" date="08-Feb-2012">EC 97/2012 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1020" date="22-Feb-2012">EC 151/2012 Zimbabwe List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1027" date="02-Mar-2012">EC 177/2012 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1030" date="14-Mar-2012">EC 215/2012 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1031" date="23-Mar-2012">EC 253/2012 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1035" date="19-Sep-2013">EC 264/2012 Iran List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1041" date="16-Oct-2012">EC 267/2012 Iran List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1043" date="13-Apr-2012">EC 316/2012 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1044" date="20-Apr-2012">EC 335/2012 Al-Qaida List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Office of Foreign Assets Control, Department of the Treasury" ReferenceGroupCode="OFACSD" Status="Current" action="add" code="1045" date="23-Apr-2012">OFAC - Human Rights Abuses Via Information Technology List</ReferenceName>
		<ReferenceName CountryCode="225" Description2Code="7" ListProviderName="Financial Conduct Authority" ReferenceGroupCode="UKFCAW" Status="Current" action="add" code="1048" date="28-Nov-2016">FCA (UK) Unauthorised Firms and Individuals List</ReferenceName>
		<ReferenceName CountryCode="241" Description2Code="8" ListProviderName="United Nations Security Council Sanctions Committees" ReferenceGroupCode="UNOT" Status="Current" action="add" code="1055" date="19-May-2012">UN Security Council Resolution 2048 (2012) Guinea-Bissau List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1056" date="01-Jun-2012">EC 458/2012 Republic of Guinea-Bissau List</ReferenceName>
		<ReferenceName CountryCode="206" Description2Code="8" ListProviderName="Secrétariat d'Etat à l'économie (State Secretariat for Economic Affairs)" ReferenceGroupCode="SECOSA" Status="Current" action="add" code="1058" date="28-Dec-2012">SECO (Switzerland) Guinea-Bissau List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1066" date="06-Jul-2012">EC 598/2012 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1069" date="17-Jul-2012">EC 641/2012 Somalia List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Office of Foreign Assets Control, Department of the Treasury" ReferenceGroupCode="OFC561" Status="Current" action="add" code="1074" date="31-Jul-2012">OFAC - Part 561 List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1075" date="02-Aug-2012">EC 706/2012 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1076" date="19-Sep-2013">EC 709/2012 Iran List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1090" date="12-Oct-2012">EC 933/2012 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1094" date="16-Oct-2012">EC 945/2012 Iran List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1096" date="16-Oct-2012">EC 943/2012 Somalia List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1099" date="30-Oct-2012">EC 1002/2012 Al-Qaida List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Office of Foreign Assets Control, Department of the Treasury" ReferenceGroupCode="OFACSD" Status="Current" action="add" code="1103" date="08-Nov-2012">OFAC - Iran Threat Reduction and Syria Human Rights Act of 2012</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1109" date="04-Dec-2012">EC 1142/2012 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1117" date="13-Dec-2012">EC 1187/2012 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1124" date="19-Sep-2013">EC 1264/2012 Iran List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="7" ListProviderName="Government Accountability Office (GAO)" ReferenceGroupCode="GAOIR" Status="Current" action="add" code="1134" date="29-Jan-2013">GAO (US) Report on firms to have Engaged in Commercial Activities in Iran’s Energy Sector</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="7" ListProviderName="Department of State" ReferenceGroupCode="TRA221" Status="Current" action="add" code="1140" date="07-Feb-2013">US Department of State - Iran Threat Reduction and Syria Human Rights Act - Section 221</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1141" date="13-Feb-2013">EC 123/2013 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1142" date="19-Feb-2013">EC 137/2013 Democratic People's Republic of Korea List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1150" date="02-Mar-2013">EC 180/2013 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1152" date="19-Sep-2013">EC 206/2013 Iran List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1157" date="27-Mar-2013">EC 290/2013 Al-Qaida List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Office of Foreign Assets Control, Department of the Treasury" ReferenceGroupCode="OFACSD" Status="Current" action="add" code="1162" date="12-Apr-2013">OFAC - Sergei Magnitsky Rule of Law Accountability Act of 2012</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1166" date="23-Apr-2013">EC 370/2013 Democratic People's Republic of Korea List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1167" date="23-Apr-2013">EC 363/2013 Syria List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Office of Foreign Assets Control, Department of the Treasury" ReferenceGroupCode="OFCFSE" Status="Current" action="add" code="1175" date="17-Apr-2014">OFAC - Foreign Sanctions Evaders with Respect to Iran</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1177" date="10-Jun-2013">EC 522/2013 Iran List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1181" date="19-Jun-2013">EC 559/2013 Republic of Guinea-Bissau List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1182" date="25-Jun-2013">EC 596/2013 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1183" date="29-Jun-2013">EC 632/2013 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1184" date="07-Oct-2013">EC 652/2013 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1196" date="07-Oct-2013">EC 731/2013 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1198" date="31-Jul-2013">EC 735/2013 Tunisia List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1200" date="06-Aug-2013">EC 754/2013 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1204" date="31-Aug-2013">EC 831/2013 Al-Qaida List</ReferenceName>
		<ReferenceName CountryCode="176" Description2Code="8" ListProviderName="Central Bank of Russia" ReferenceGroupCode="RUCBR" Status="Current" action="add" code="1206" date="14-Jul-2015">Central Bank of Russia Special Economic Measures List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1207" date="30-Oct-2013">EC 1054/2013 Belarus List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1210" date="05-Nov-2013">EC 1091/2013 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1211" date="18-Nov-2013">EC 1154/2013 Iran List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1215" date="06-Dec-2013">EC 1267/2013 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1222" date="31-Jan-2014">EC 81/2014 Tunisia List</ReferenceName>
		<ReferenceName CountryCode="39" Description2Code="8" ListProviderName="Department of Foreign Affairs, Trade and Development Canada" ReferenceGroupCode="DFAITS" Status="Current" action="add" code="1234" date="03-Jul-2015">DFATD (Canada) Freezing Assets of Corrupt Foreign Officials (Ukraine) Regulations List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1240" date="18-Mar-2014">EC 269/2014 Ukraine List</ReferenceName>
		<ReferenceName CountryCode="39" Description2Code="8" ListProviderName="Department of Foreign Affairs, Trade and Development Canada" ReferenceGroupCode="DFAITS" Status="Current" action="add" code="1242" date="03-Jul-2015">DFATD (Canada) Special Economic Measures (Ukraine) Regulations</ReferenceName>
		<ReferenceName CountryCode="39" Description2Code="8" ListProviderName="Department of Foreign Affairs, Trade and Development Canada" ReferenceGroupCode="DFAITS" Status="Current" action="add" code="1243" date="03-Jul-2015">DFATD (Canada) Special Economic Measures (Russia) Regulations</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1245" date="21-Mar-2014">EC 284/2014 Ukraine List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1251" date="02-Apr-2014">EC 329/2014 Al-Qaida List</ReferenceName>
		<ReferenceName CountryCode="206" Description2Code="8" ListProviderName="Secrétariat d'Etat à l'économie (State Secretariat for Economic Affairs)" ReferenceGroupCode="SECOSA" Status="Current" action="add" code="1252" date="27-Aug-2014">SECO (Switzerland) Ukraine List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1254" date="16-Apr-2014">EC 371/2014 Iran List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1256" date="15-Apr-2014">EC 386/2014 Democratic People's Republic of Korea List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1267" date="29-Apr-2014">EC 433/2014 Ukraine List</ReferenceName>
		<ReferenceName CountryCode="241" Description2Code="8" ListProviderName="United Nations Security Council Sanctions Committees" ReferenceGroupCode="UNOT" Status="Current" action="add" code="1269" date="10-May-2014">UN Security Council Resolution 2134 (2014) Central African Republic List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1270" date="12-May-2014">EC 477/2014 Ukraine List</ReferenceName>
		<ReferenceName CountryCode="206" Description2Code="8" ListProviderName="Secrétariat d'Etat à l'économie (State Secretariat for Economic Affairs)" ReferenceGroupCode="SECOSA" Status="Current" action="add" code="1276" date="20-May-2014">SECO (Switzerland) Central African Republic List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="7" ListProviderName="Federal Bureau of Investigation (FBI)" ReferenceGroupCode="FBIWAN" Status="Current" action="add" code="1277" date="28-May-2014">FBI Most Wanted Counterintelligence List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="7" ListProviderName="Federal Bureau of Investigation (FBI)" ReferenceGroupCode="FBIWAN" Status="Current" action="add" code="1278" date="28-May-2014">FBI Most Wanted Human Trafficking List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1279" date="02-Sep-2014">EC 577/2014 Ukraine List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1282" date="29-May-2014">EC 578/2014 Syria List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1283" date="29-May-2014">EC 583/2014 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1286" date="13-Jun-2014">EC 630/2014 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1287" date="24-Jun-2014">EC 693/2014 Syria List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1292" date="24-Jun-2014">EC 691/2014 Central African Republic List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1294" date="05-Jul-2014">EC 735/2014 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1299" date="11-Jul-2014">EC 747/2014 Sudan List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1302" date="12-Jul-2014">EC 753/2014 Ukraine List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Office of Foreign Assets Control, Department of the Treasury" ReferenceGroupCode="OFCSSI" Status="Current" action="add" code="1306" date="12-Sep-2014">OFAC - Executive Order 13662</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1309" date="23-Jul-2014">EC 793/2014 Syria List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1311" date="28-Jul-2014">EC 810/2014 Ukraine List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1313" date="31-Jul-2014">EC 826/2014 Ukraine List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1315" date="15-Sep-2014">EC 833/2014 Ukraine List</ReferenceName>
		<ReferenceName CountryCode="107" Description2Code="8" ListProviderName="Ministry of Finance Japan" ReferenceGroupCode="JAFISA" Status="Current" action="add" code="1317" date="05-Aug-2014">Japanese Finance Ministry - Ukraine List</ReferenceName>
		<ReferenceName CountryCode="107" Description2Code="8" ListProviderName="Ministry of Finance Japan" ReferenceGroupCode="JAFISA" Status="Current" action="add" code="1320" date="20-Aug-2014">Japanese Finance Ministry - Central African Republic List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1322" date="22-Aug-2014">EC 914/2014 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1324" date="29-Aug-2014">EC 930/2014 Al-Qaida List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="7" ListProviderName="Federal Bureau of Investigation (FBI)" ReferenceGroupCode="FBIWAN" Status="Current" action="add" code="1326" date="05-Sep-2014">FBI Most Wanted Known Bank Robbers</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1327" date="12-Sep-2014">EC 961/2014 Ukraine List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1329" date="15-Sep-2014">EC 960/2014 Ukraine List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Department of State" ReferenceGroupCode="DOSSST" Status="Current" action="add" code="1331" date="19-Sep-2014">US Department of State - State Sponsors of Terrorism</ReferenceName>
		<ReferenceName CountryCode="107" Description2Code="8" ListProviderName="Ministry of Finance Japan" ReferenceGroupCode="JAFISA" Status="Current" action="add" code="1332" date="24-Sep-2014">Japanese Finance Ministry - Russia Prohibition on Issuance of Securities List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1333" date="28-Sep-2014">EC 1022/2014 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1335" date="28-Sep-2014">EC 1013/2014 Syria List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1338" date="09-Oct-2014">EC 1058/2014 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1339" date="09-Oct-2014">EC 1059/2014 Democratic People's Republic of Korea List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1342" date="21-Oct-2014">EC 1105/2014 Syria List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1343" date="21-Oct-2014">EC 1104/2014 Somalia List</ReferenceName>
		<ReferenceName CountryCode="39" Description2Code="8" ListProviderName="Department of Foreign Affairs, Trade and Development Canada" ReferenceGroupCode="DFAITS" Status="Current" action="add" code="1347" date="03-Jul-2015">DFATD (Canada) Special Economic Measures (South Sudan) Regulations</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1348" date="31-Oct-2014">EC 1159/2014 Belarus List</ReferenceName>
		<ReferenceName CountryCode="241" Description2Code="8" ListProviderName="United Nations Security Council Sanctions Committees" ReferenceGroupCode="UNOT" Status="Current" action="add" code="1351" date="08-Nov-2014">UN Security Council Resolution 2140 (2014) Yemen List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1352" date="08-Nov-2014">EC 1202/2014 Iran List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1355" date="18-Nov-2014">EC 1225/2014 Ukraine List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1357" date="02-Dec-2014">EC 1270/2014 Ukraine List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1359" date="02-Dec-2014">EC 1273/2014 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1360" date="02-Dec-2014">EC 1276/2014 Central African Republic List</ReferenceName>
		<ReferenceName CountryCode="206" Description2Code="8" ListProviderName="Secrétariat d'Etat à l'économie (State Secretariat for Economic Affairs)" ReferenceGroupCode="SECOSA" Status="Current" action="add" code="1364" date="05-Dec-2014">SECO (Switzerland) Yemen List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1365" date="05-Dec-2014">EC 1290/2014 Ukraine List</ReferenceName>
		<ReferenceName CountryCode="107" Description2Code="8" ListProviderName="Ministry of Finance Japan" ReferenceGroupCode="JAFISA" Status="Current" action="add" code="1367" date="16-Dec-2014">Japanese Finance Ministry - Yemen List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Office of Foreign Assets Control, Department of the Treasury" ReferenceGroupCode="OFCFSE" Status="Current" action="add" code="1368" date="17-Dec-2014">OFAC - Foreign Sanctions Evaders with Respect to Syria</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1369" date="19-Dec-2014">EC 1352/2014 Yemen List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1371" date="19-Jan-2015">EC 2015/64 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1372" date="27-Jan-2015">EC 2015/108 Syria List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1374" date="31-Jan-2015">EC 2015/147 Tunisia List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1376" date="04-Feb-2015">EC 2015/167 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1379" date="16-Feb-2015">EC 2015/240 Ukraine List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1381" date="20-Feb-2015">EC 2015/274 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1383" date="06-Mar-2015">EC 2015/357 Ukraine List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1386" date="07-Mar-2015">EC 2015/375 Syria List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1390" date="14-Mar-2015">EC 2015/427 Ukraine List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1392" date="21-Mar-2015">EC 2015/480 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1396" date="08-Apr-2015">EC 2015/548 Iran List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1400" date="13-Apr-2015">EC 2015/576 Al-Qaida List</ReferenceName>
		<ReferenceName CountryCode="13" Description2Code="8" ListProviderName="Department of Foreign Relations &amp; Trade" ReferenceGroupCode="DFATRU" Status="Current" action="add" code="1401" date="20-Apr-2015">DFAT (Australia) Autonomous Sanctions - Russia, Crimea and Sevastopol</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1402" date="21-Apr-2015">EC 2015/614 Democratic Republic of the Congo List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1406" date="21-Apr-2015">EC 2015/617 Al-Qaida List</ReferenceName>
		<ReferenceName CountryCode="45" Description2Code="7" ListProviderName="Central Commission for Discipline Inspection" ReferenceGroupCode="CCDI" Status="Current" action="add" code="1407" date="09-Sep-2015">CCDI (China) 100 Fugitives List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Office of Foreign Assets Control, Department of the Treasury" ReferenceGroupCode="OFCISA" Status="Current" action="add" code="1408" date="27-Apr-2015">OFAC - Non-SDN Iranian Sanctions Act</ReferenceName>
		<ReferenceName CountryCode="186" Description2Code="8" ListProviderName="Monetary Authority of Singapore" ReferenceGroupCode="MASTER" Status="Current" action="add" code="1409" date="23-Mar-2016">MAS (Singapore) Terrorism Suppression of Financing Act Order 2015</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1413" date="20-May-2015">EC 2015/780 Syria List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1419" date="29-May-2015">EC 2015/828 Syria List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1423" date="09-Jun-2015">EC 2015/879 Yemen List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1426" date="26-Jun-2015">EC 2015/1001 Iran List</ReferenceName>
		<ReferenceName CountryCode="241" Description2Code="8" ListProviderName="United Nations Security Council Sanctions Committees" ReferenceGroupCode="UNOT" Status="Current" action="add" code="1429" date="02-Jul-2015">UN Security Council Resolution 2206 (2015) South Sudan List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1432" date="10-Jul-2015">EC 2015/1112 South Sudan List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1442" date="01-Aug-2015">EC 2015/1330 Al-Qaida List</ReferenceName>
		<ReferenceName CountryCode="206" Description2Code="8" ListProviderName="Secrétariat d'Etat à l'économie (State Secretariat for Economic Affairs)" ReferenceGroupCode="SECOSA" Status="Current" action="add" code="1443" date="12-Aug-2015">SECO (Switzerland) South Sudan List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1444" date="14-Aug-2015">EC 2015/1390 Al-Qaida List</ReferenceName>
		<ReferenceName CountryCode="241" Description2Code="8" ListProviderName="United Nations Security Council Sanctions Committees" ReferenceGroupCode="UNOT" Status="Current" action="add" code="1445" date="21-Aug-2015">UN Security Council Resolution 2196 (2015) Central African Republic List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1446" date="28-Aug-2015">EC 2015/1473 Al Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1447" date="03-Sep-2015">EC 2015/1485 Central African Republic List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1449" date="15-Sep-2015">EC 2015/1514 Ukraine List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1451" date="15-Sep-2015">EC 2015/1517 Al-Qaida List</ReferenceName>
		<ReferenceName CountryCode="107" Description2Code="8" ListProviderName="Ministry of Finance Japan" ReferenceGroupCode="JAFISA" Status="Current" action="add" code="1452" date="14-Jun-2017">Japanese Finance Ministry - South Sudan List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1455" date="30-Sep-2015">EC 2015/1740 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1456" date="02-Oct-2015">EC 2015/1755 Burundi List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1459" date="06-Oct-2015">EC 2015/1777 Ukraine List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1473" date="09-Oct-2015">EC 2015/1815 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1487" date="27-Oct-2015">EC 2015/1920 Yemen List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1494" date="30-Oct-2015">EC 2015/1949 Belarus List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1508" date="04-Dec-2015">EC 2015/2245 Al-Qaida List</ReferenceName>
		<ReferenceName CountryCode="206" Description2Code="8" ListProviderName="Secrétariat d'Etat à l'économie (State Secretariat for Economic Affairs)" ReferenceGroupCode="SECOSA" Status="Current" action="add" code="1510" date="07-Dec-2015">SECO (Switzerland) Burundi List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1525" date="24-Dec-2015">EC 2015/2454 Central African Republic List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1531" date="07-Jan-2016">EC 2016/13 Al-Qaida List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Office of Foreign Assets Control, Department of the Treasury" ReferenceGroupCode="13599" Status="Current" action="add" code="1535" date="18-Jan-2016">OFAC - Executive Order 13599</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1538" date="19-Jan-2016">EC 2016/47 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1539" date="19-Jan-2016">EC 2016/44 Libya List</ReferenceName>
		<ReferenceName CountryCode="241" Description2Code="8" ListProviderName="United Nations Security Council" ReferenceGroupCode="UNOT" Status="Current" action="add" code="1541" date="22-Jan-2016">UN Security Council Resolution 2231 (2015) Iran List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1547" date="29-Jan-2016">EC 2016/111 Tunisia List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1564" date="17-Feb-2016">EC 2016/218 Zimbabwe List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1567" date="29-Feb-2016">EC 2016/276 Belarus List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1570" date="02-Mar-2016">EC 2016/294 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1575" date="04-Mar-2016">EC 2016/307 Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1576" date="05-Mar-2016">EC 2016/311 Ukraine List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1578" date="05-Mar-2016">EC 2016/315 Democratic People's Republic of Korea List</ReferenceName>
		<ReferenceName CountryCode="241" Description2Code="8" ListProviderName="United Nations Security Council Sanctions Committees" ReferenceGroupCode="UNOT" Status="Current" action="add" code="1581" date="08-Mar-2016">UN Security Council Resolution 2262 (2016) Central African Republic List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1584" date="12-Mar-2016">EC 2016/354 Central African Republic List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1586" date="12-Mar-2016">EC 2016/353 Ukraine List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1594" date="01-Apr-2016">EC 2016/473 ISIL (Da'esh) and Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1595" date="01-Apr-2016">EC 2016/466 Libya List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1607" date="12-Apr-2016">EC 2016/556 Iran List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1610" date="13-Apr-2016">EC 2016/569 Democratic People's Republic of Korea List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1616" date="20-Apr-2016">EC 2016/603 Iran List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1618" date="26-Apr-2016">EC 2016/647 ISIL (Da'esh) and Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1621" date="05-May-2016">EC 2016/690 Libya List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1628" date="29-May-2016">EC 2016/840 Syria List</ReferenceName>
		<ReferenceName CountryCode="206" Description2Code="8" ListProviderName="The Federal Council, Switzerland" ReferenceGroupCode="FCSFIA" Status="Current" action="add" code="1639" date="01-Jul-2016">Federal Council (Switzerland) Foreign Illicit Assets Act - Ukraine</ReferenceName>
		<ReferenceName CountryCode="206" Description2Code="8" ListProviderName="The Federal Council, Switzerland" ReferenceGroupCode="FCSFIA" Status="Current" action="add" code="1640" date="01-Jul-2016">Federal Council (Switzerland) Foreign Illicit Assets Act - Tunisia</ReferenceName>
		<ReferenceName CountryCode="206" Description2Code="8" ListProviderName="The Federal Council, Switzerland" ReferenceGroupCode="FCSFIA" Status="Current" action="add" code="1641" date="01-Jul-2016">Federal Council (Switzerland) Foreign Illicit Assets Act - Egypt</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1642" date="01-Jul-2016">EC 2016/1063 ISIL (Da'esh) and Al-Qaida List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="7" ListProviderName="Federal Bureau of Investigation (FBI)" ReferenceGroupCode="FBIWAN" Status="Current" action="add" code="1647" date="25-Jul-2016">FBI Featured Fugitives - Seeking Information</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1650" date="05-Aug-2016">EC 2016/1334 Libya List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1652" date="09-Aug-2016">EC 2016/1347 ISIL (Da'esh) and Al-Qaida List</ReferenceName>
		<ReferenceName CountryCode="21" Description2Code="8" ListProviderName="Public Federal Justice" ReferenceGroupCode="FJMB" Status="Current" action="add" code="1655" date="23-Aug-2016">Federal Justice (Belgium) MB List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1656" date="27-Aug-2016">EC 2016/1430 ISIL (Da'esh) and Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1659" date="01-Sep-2016">EC 2016/1442 Central African Republic List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1661" date="13-Sep-2016">EC 2016/1641 ISIL (Da'esh) and Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1667" date="16-Sep-2016">EC 2016/1661 Ukraine List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1671" date="20-Sep-2016">EC 2016/1683 ISIL (Da'esh) and Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1673" date="30-Sep-2016">EC 2016/1737 Yemen List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1676" date="30-Sep-2016">EC 2016/1735 Syria List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1679" date="01-Oct-2016">EC 2016/1752 Libya List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1686" date="19-Dec-2016">EC 2016/1827 ISIL (Da'esh) and Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1694" date="28-Oct-2016">EC 2016/1893 Syria List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1696" date="29-Oct-2016">EC 2016/1906 ISIL (Da'esh) and Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1698" date="09-Nov-2016">EC 2016/1955 Ukraine List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1700" date="15-Nov-2016">EC 2016/1984 Syria List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1704" date="09-Dec-2016">EC 2016/2215 Democratic People's Republic of Korea List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1707" date="13-Dec-2016">EC 2016/2230 Democratic Republic of the Congo List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1709" date="16-Dec-2016">EC 2016/2262 ISIL (Da'esh) and Al-Qaida List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="8" ListProviderName="Office of Foreign Assets Control, Department of the Treasury" ReferenceGroupCode="OFACSD" Status="Current" action="add" code="1712" date="29-Dec-2016">OFAC - Executive Order 13694</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1714" date="17-Jan-2017">EC 2017/80 Democratic People's Republic of Korea List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1715" date="28-Jan-2017">EC 2017/142 ISIL (Da'esh) and Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1716" date="28-Jan-2017">EC 2017/149 Tunisia List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1718" date="28-Jan-2017">EC 2017/150 Terrorism List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUOTTE" Status="Current" action="add" code="1719" date="28-Jan-2017">EC (CFSP) 2017/154 EU Terrorism List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1721" date="08-Feb-2017">EC 2017/199 Democratic Republic of the Congo List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1725" date="21-Feb-2017">EC 2017/296 ISIL (Da'esh) and Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1726" date="25-Feb-2017">EC 2017/326 ISIL (Da'esh) and Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1729" date="08-Mar-2017">EC 2017/395 Somalia List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1731" date="09-Mar-2017">EC 2017/396 Democratic Republic of the Congo List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1734" date="09-Mar-2017">EC 2017/402 South Sudan List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1736" date="09-Mar-2017">EC 2017/401 Sudan List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1738" date="09-Mar-2017">EC 2017/403 Republic of Guinea-Bissau List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1740" date="09-Mar-2017">EC 2017/404 Afghanistan List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1742" date="14-Mar-2017">EC 2017/437 Ukraine List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1745" date="21-Mar-2017">EC 2017/480 Syria List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1748" date="23-Mar-2017">EC 2017/489 Libya List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1751" date="23-Mar-2017">EC 2017/491 Egypt List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1754" date="27-Mar-2017">EC 2017/557 ISIL (Da'esh) and Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1756" date="04-Apr-2017">EC 2017/628 Yemen List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1758" date="05-Apr-2017">EC 2017/630 ISIL (Da'esh) and Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1759" date="06-Apr-2017">EC 2017/637 ISIL (Da'esh) and Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1763" date="12-Apr-2017">EC 2017/685 Iran List</ReferenceName>
		<ReferenceName CountryCode="226" Description2Code="7" ListProviderName="Federal Bureau of Investigation (FBI)" ReferenceGroupCode="FBIWAN" Status="Current" action="add" code="1764" date="04-May-2017">FBI Featured Fugitives - Parental Kidnappings</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1769" date="25-May-2017">EC 2017/890 Central African Republic List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1771" date="30-May-2017">EC 2017/904 Democratic Republic of the Congo List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1773" date="30-May-2017">EC 2017/906 Central African Republic List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1775" date="31-May-2017">EC 2017/907 Syria List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1778" date="09-Jun-2017">EC 2017/970 Democratic People's Republic of Korea List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1779" date="14-Jun-2017">EC 2017/993 Democratic People's Republic of Korea List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1781" date="15-Jun-2017">EC 2017/998 ISIL (Da'esh) and Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1783" date="21-Jun-2017">EC 2017/1090 Central African Republic List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1785" date="21-Jun-2017">EC 2017/1094 ISIL (Da'esh) and Al-Qaida List</ReferenceName>
		<ReferenceName Description2Code="8" ListProviderName="European Union" ReferenceGroupCode="EUCON" Status="Current" action="add" code="1786" date="26-Jun-2017">EC 2017/1124 Iran List</ReferenceName>
	</SanctionsReferencesList>
	<IdentificationTypeList>
		<IdentificationType RecordType="Person" action="add" code="1" date="06-Sep-2009">National ID</IdentificationType>
		<IdentificationType RecordType="Person" action="add" code="2" date="06-Sep-2009">Social Security No.</IdentificationType>
		<IdentificationType RecordType="Person" action="add" code="3" date="06-Sep-2009">Passport No.</IdentificationType>
		<IdentificationType RecordType="Person" action="add" code="4" date="06-Sep-2009">Driving Licence No.</IdentificationType>
		<IdentificationType RecordType="Person" action="add" code="5" date="06-Sep-2009">National Criminal Identification Code (USA)</IdentificationType>
		<IdentificationType RecordType="Entity" action="add" code="6" date="06-Sep-2009">Others</IdentificationType>
		<IdentificationType RecordType="Person" action="add" code="6" date="06-Sep-2009">Others</IdentificationType>
		<IdentificationType RecordType="Entity" action="add" code="7" date="06-Sep-2009">Company Identification No.</IdentificationType>
		<IdentificationType RecordType="Entity" action="add" code="8" date="29-Jan-2012">National Tax No.</IdentificationType>
		<IdentificationType RecordType="Person" action="add" code="8" date="06-Sep-2009">National Tax No.</IdentificationType>
		<IdentificationType RecordType="Entity" action="add" code="9" date="06-Sep-2009">OFAC Program ID</IdentificationType>
		<IdentificationType RecordType="Person" action="add" code="9" date="06-Sep-2009">OFAC Program ID</IdentificationType>
		<IdentificationType RecordType="Entity" action="add" code="10" date="06-Sep-2009">OFAC Unique ID</IdentificationType>
		<IdentificationType RecordType="Person" action="add" code="10" date="06-Sep-2009">OFAC Unique ID</IdentificationType>
		<IdentificationType RecordType="Entity" action="add" code="11" date="06-Sep-2009">EU Sanctions Programme Indicator</IdentificationType>
		<IdentificationType RecordType="Person" action="add" code="11" date="06-Sep-2009">EU Sanctions Programme Indicator</IdentificationType>
		<IdentificationType RecordType="Entity" action="add" code="12" date="06-Sep-2009">EU Consolidated Electronic List ID</IdentificationType>
		<IdentificationType RecordType="Person" action="add" code="12" date="06-Sep-2009">EU Consolidated Electronic List ID</IdentificationType>
		<IdentificationType RecordType="Entity" action="add" code="13" date="06-Sep-2009">UN Permanent Reference No.</IdentificationType>
		<IdentificationType RecordType="Person" action="add" code="13" date="06-Sep-2009">UN Permanent Reference No.</IdentificationType>
		<IdentificationType RecordType="Entity" action="add" code="14" date="06-Sep-2009">HM Treasury Group ID</IdentificationType>
		<IdentificationType RecordType="Person" action="add" code="14" date="06-Sep-2009">HM Treasury Group ID</IdentificationType>
		<IdentificationType RecordType="Entity" action="add" code="15" date="01-Nov-2009">Bank Identifier Code (BIC)</IdentificationType>
		<IdentificationType RecordType="Entity" action="add" code="16" date="01-Nov-2009">International Maritime Organization (IMO) Ship No.</IdentificationType>
		<IdentificationType RecordType="Entity" action="add" code="17" date="29-Jan-2012">DUNS Number</IdentificationType>
		<IdentificationType RecordType="Person" action="add" code="18" date="01-Oct-2012">National Provider Identifier (NPI)</IdentificationType>
		<IdentificationType RecordType="Person" action="add" code="19" date="01-Oct-2012">Federal Bureau of Prisons Register Number</IdentificationType>
		<IdentificationType RecordType="Entity" action="add" code="20" date="22-Dec-2012">Aircraft Manufacturer's Serial Number (MSN)</IdentificationType>
		<IdentificationType RecordType="Entity" action="add" code="21" date="20-Jan-2013">Aircraft Construction, Line, Fleet or Serial Number</IdentificationType>
		<IdentificationType RecordType="Entity" action="add" code="22" date="07-Apr-2013">Legal Entity Identifier (LEI)</IdentificationType>
		<IdentificationType RecordType="Entity" action="add" code="23" date="19-Aug-2013">Standard Industrial Classification (SIC)</IdentificationType>
		<IdentificationType RecordType="Entity" action="add" code="24" date="19-Aug-2013">North American Industry Classification System (NAICS)</IdentificationType>
		<IdentificationType RecordType="Entity" action="add" code="25" date="19-Aug-2013">NACE (European Union Economic Activity Classification System)</IdentificationType>
		<IdentificationType RecordType="Entity" action="add" code="26" date="12-Dec-2015">International Securities Identification Number (ISIN)</IdentificationType>
		<IdentificationType RecordType="Entity" action="add" code="27" date="12-Dec-2015">Related OFAC Program ID</IdentificationType>
		<IdentificationType RecordType="Entity" action="add" code="28" date="12-Dec-2015">Related OFAC Unique ID</IdentificationType>
		<IdentificationType RecordType="Entity" action="add" code="29" date="12-Dec-2015">Related EU Sanctions Programme Indicator</IdentificationType>
		<IdentificationType RecordType="Entity" action="add" code="30" date="12-Dec-2015">Related EU Consolidated Electronic List ID</IdentificationType>
		<IdentificationType RecordType="Entity" action="add" code="31" date="01-Apr-2017">SECO SSID</IdentificationType>
		<IdentificationType RecordType="Person" action="add" code="31" date="01-Apr-2017">SECO SSID</IdentificationType>
		<IdentificationType RecordType="Entity" action="add" code="32" date="01-Apr-2017">DFAT Reference Number</IdentificationType>
		<IdentificationType RecordType="Person" action="add" code="32" date="01-Apr-2017">DFAT Reference Number</IdentificationType>
		<IdentificationType RecordType="Person" action="add" code="33" date="01-Apr-2017">OSFI Individuals ID</IdentificationType>
		<IdentificationType RecordType="Entity" action="add" code="34" date="01-Apr-2017">OSFI Entities ID</IdentificationType>
		<IdentificationType RecordType="Entity" action="add" code="35" date="01-Apr-2017">OSFI Iran ID</IdentificationType>
		<IdentificationType RecordType="Person" action="add" code="35" date="01-Apr-2017">OSFI Iran ID</IdentificationType>
		<IdentificationType RecordType="Entity" action="add" code="36" date="01-Apr-2017">OSFI North Korea ID</IdentificationType>
		<IdentificationType RecordType="Person" action="add" code="36" date="01-Apr-2017">OSFI North Korea ID</IdentificationType>
		<IdentificationType RecordType="Entity" action="add" code="37" date="01-Apr-2017">Central Registration Depository (CRD)</IdentificationType>
		<IdentificationType RecordType="Person" action="add" code="37" date="01-Apr-2017">Central Registration Depository (CRD)</IdentificationType>
	</IdentificationTypeList>
	<DateTypeList>
		<DateTypeName RecordType="Person" action="add" code="1" date="08-Nov-2009">Date of Birth</DateTypeName>
		<DateTypeName RecordType="Person" action="add" code="2" date="25-Oct-2009">Deceased Date</DateTypeName>
		<DateTypeName RecordType="Entity" action="add" code="3" date="06-Sep-2009">Date of Registration</DateTypeName>
		<DateTypeName RecordType="Entity" action="add" code="6" date="06-Sep-2009">Cessation Date</DateTypeName>
		<DateTypeName RecordType="Person" action="add" code="7" date="12-Dec-2015">Inactive as of (PEP)</DateTypeName>
		<DateTypeName RecordType="Person" action="add" code="8" date="12-Dec-2015">Inactive as of (RCA related to PEP)</DateTypeName>
	</DateTypeList>
	<ReferenceGroupLanguagesList>
		<ReferenceGroupLanguageName ISO2LanguageCode="en" action="add" code="1" date="06-Sep-2009">English</ReferenceGroupLanguageName>
		<ReferenceGroupLanguageName ISO2LanguageCode="fr" action="add" code="3" date="06-Sep-2009">French</ReferenceGroupLanguageName>
		<ReferenceGroupLanguageName ISO2LanguageCode="es" action="add" code="4" date="21-Jun-2011">Spanish</ReferenceGroupLanguageName>
		<ReferenceGroupLanguageName ISO2LanguageCode="de" action="add" code="5" date="18-Nov-2011">German</ReferenceGroupLanguageName>
		<ReferenceGroupLanguageName ISO2LanguageCode="bg" action="add" code="7" date="07-Jun-2013">Bulgarian</ReferenceGroupLanguageName>
		<ReferenceGroupLanguageName ISO2LanguageCode="cs" action="add" code="8" date="14-Jun-2013">Czech</ReferenceGroupLanguageName>
		<ReferenceGroupLanguageName ISO2LanguageCode="da" action="add" code="9" date="14-Jun-2013">Danish</ReferenceGroupLanguageName>
		<ReferenceGroupLanguageName ISO2LanguageCode="el" action="add" code="10" date="14-Jun-2013">Greek</ReferenceGroupLanguageName>
		<ReferenceGroupLanguageName ISO2LanguageCode="et" action="add" code="11" date="14-Jun-2013">Estonian</ReferenceGroupLanguageName>
		<ReferenceGroupLanguageName ISO2LanguageCode="fi" action="add" code="12" date="14-Jun-2013">Finnish</ReferenceGroupLanguageName>
		<ReferenceGroupLanguageName ISO2LanguageCode="ga" action="add" code="13" date="14-Jun-2013">Irish</ReferenceGroupLanguageName>
		<ReferenceGroupLanguageName ISO2LanguageCode="it" action="add" code="14" date="14-Jun-2013">Italian</ReferenceGroupLanguageName>
		<ReferenceGroupLanguageName ISO2LanguageCode="hu" action="add" code="16" date="14-Jun-2013">Hungarian</ReferenceGroupLanguageName>
		<ReferenceGroupLanguageName ISO2LanguageCode="lt" action="add" code="19" date="14-Jun-2013">Lithuanian</ReferenceGroupLanguageName>
		<ReferenceGroupLanguageName ISO2LanguageCode="lv" action="add" code="20" date="14-Jun-2013">Latvian</ReferenceGroupLanguageName>
		<ReferenceGroupLanguageName ISO2LanguageCode="mt" action="add" code="21" date="14-Jun-2013">Maltese</ReferenceGroupLanguageName>
		<ReferenceGroupLanguageName ISO2LanguageCode="nl" action="add" code="22" date="01-Aug-2010">Dutch</ReferenceGroupLanguageName>
		<ReferenceGroupLanguageName ISO2LanguageCode="pl" action="add" code="24" date="27-Oct-2011">Polish</ReferenceGroupLanguageName>
		<ReferenceGroupLanguageName ISO2LanguageCode="pt" action="add" code="25" date="14-Jun-2013">Portuguese</ReferenceGroupLanguageName>
		<ReferenceGroupLanguageName ISO2LanguageCode="ro" action="add" code="26" date="14-Jun-2013">Romanian</ReferenceGroupLanguageName>
		<ReferenceGroupLanguageName ISO2LanguageCode="sk" action="add" code="28" date="14-Jun-2013">Slovak</ReferenceGroupLanguageName>
		<ReferenceGroupLanguageName ISO2LanguageCode="sl" action="add" code="29" date="14-Jun-2013">Slovenian</ReferenceGroupLanguageName>
		<ReferenceGroupLanguageName ISO2LanguageCode="sv" action="add" code="30" date="14-Jun-2013">Swedish</ReferenceGroupLanguageName>
	</ReferenceGroupLanguagesList>
	<ScriptLanguageList>
		<ScriptLanguage ScriptLanguageCode="ab" action="add" date="30-Jun-2013" id="1">Abkhazian</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="ar" action="add" date="30-Jun-2013" id="4">Arabic</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="be" action="add" date="30-Jun-2013" id="9">Belarusian</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="bg" action="add" date="30-Jun-2013" id="10">Bulgarian</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="bn" action="add" date="30-Jun-2013" id="13">Bengali</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="dv" action="add" date="01-Dec-2013" id="19">Diveh  Maldivian</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="el" action="add" date="01-Dec-2013" id="22">Greek</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="fa" action="add" date="30-Jun-2013" id="23">Persian</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="gu" action="add" date="30-Jun-2013" id="24">Gujarati</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="he" action="add" date="30-Jun-2013" id="26">Hebrew</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="hi" action="add" date="30-Jun-2013" id="27">Hindi</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="hy" action="add" date="30-Jun-2013" id="29">Armenian</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="ja" action="add" date="30-Jun-2013" id="33">Japanese</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="ka" action="add" date="30-Jun-2013" id="35">Georgian</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="kk" action="add" date="30-Jun-2013" id="36">Kazakh</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="km" action="add" date="30-Jun-2013" id="37">Khmer</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="kn" action="add" date="30-Jun-2013" id="38">Kannada</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="ko" action="add" date="30-Jun-2013" id="39">Korean</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="ky" action="add" date="30-Jun-2013" id="43">Kirghiz</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="lo" action="add" date="30-Jun-2013" id="44">Lao</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="mk" action="add" date="30-Jun-2013" id="45">Macedonian</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="ml" action="add" date="30-Jun-2013" id="46">Malayalam</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="mn" action="add" date="30-Jun-2013" id="47">Mongolian</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="mr" action="add" date="30-Jun-2013" id="49">Marathi</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="my" action="add" date="30-Jun-2013" id="51">Burmese</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="ne" action="add" date="30-Jun-2013" id="52">Nepali</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="ps" action="add" date="30-Jun-2013" id="58">Pushto</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="ru" action="add" date="30-Jun-2013" id="59">Russian</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="sr" action="add" date="30-Jun-2013" id="63">Serbian</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="ta" action="add" date="30-Jun-2013" id="64">Tamil</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="te" action="add" date="30-Jun-2013" id="65">Telugu</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="tg" action="add" date="30-Jun-2013" id="66">Tajik</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="th" action="add" date="30-Jun-2013" id="67">Thai</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="tk" action="add" date="30-Jun-2013" id="69">Turkmen</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="uk" action="add" date="30-Jun-2013" id="75">Ukrainian</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="ur" action="add" date="30-Jun-2013" id="76">Urdu</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="uz" action="add" date="30-Jun-2013" id="77">Uzbek</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="zh" action="add" date="30-Jun-2013" id="80">Chinese - Simplified</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="zh" action="add" date="30-Jun-2013" id="81">Chinese - Traditional</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="ja" action="add" date="30-Jun-2013" id="82">Japanese-Kanji</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="ja" action="add" date="30-Jun-2013" id="83">Japanese-Hiragana</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="ja" action="add" date="30-Jun-2013" id="84">Japanese-Katakana</ScriptLanguage>
		<ScriptLanguage ScriptLanguageCode="kr" action="add" date="30-Jun-2013" id="85">Korean – Hanja</ScriptLanguage>
	</ScriptLanguageList>
	<Records>
		<Person date="20-Oct-2015" id="10441" recordaction="add">
			<ActiveStatusDetails>
				<ActiveStatus>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ActiveStatusValue>Active</ActiveStatusValue>
				</ActiveStatus>
			</ActiveStatusDetails>
			<PersonNameDetails>
				<PersonNames id="Name1">
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PersonNameValue>
						<FirstName>Bozize</FirstName>
						<Surname>Yangouvonda</Surname>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName1">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">博齐泽扬古翁达</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified1">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>0590 7871 3419 2254 0657 5040 6671</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName2">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">博齊澤揚古翁達</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional1">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>0590 7871 3419 2254 0657 5040 6671</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
				<PersonNames id="Name2">
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PersonNameValue>
						<FirstName>Francois</FirstName>
						<Surname>Bozize</Surname>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName3">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">弗朗夸斯博齐泽</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified2">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>1715 2597 1139 2448 0590 7871 3419</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName4">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">弗朗夸斯博齊澤</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional2">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>1715 2597 1139 2448 0590 7871 3419</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
				<PersonNames id="Name3">
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PersonNameValue>
						<FirstName>Francois</FirstName>
						<MiddleName>Yangouvonda</MiddleName>
						<Surname>Bozize</Surname>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName5">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">弗朗夸斯扬古翁达博齐泽</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified3">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>1715 2597 1139 2448 2254 0657 5040 6671 0590 7871 3419</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName6">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">弗朗夸斯揚古翁達博齊澤</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional3">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>1715 2597 1139 2448 2254 0657 5040 6671 0590 7871 3419</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
				<PersonNames id="Name4">
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<PersonNameValue>
						<FirstName>Yangouvonda</FirstName>
						<Surname>Bozize</Surname>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName7">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">扬古翁达博齐泽</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified4">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<PersonNameValue>
								<OriginalScriptName>2254 0657 5040 6671 0590 7871 3419</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName8">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">揚古翁達博齊澤</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional4">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<PersonNameValue>
								<OriginalScriptName>2254 0657 5040 6671 0590 7871 3419</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
			</PersonNameDetails>
			<Descriptions>
				<Description Description1="1"/>
				<Description Description1="3" Description2="7"/>
				<Description Description1="3" Description2="8"/>
			</Descriptions>
			<DateDetails>
				<DateType code="1">
					<DateTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<DateValue Day="14" Month="Oct" Year="1946"/>
					</DateTypeValue>
				</DateType>
			</DateDetails>
			<BirthPlaceDetails>
				<BirthPlace>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PlaceValue RegionCode="78"/>
				</BirthPlace>
				<BirthPlace>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<PlaceValue RegionCode="78" name="Mouila"/>
				</BirthPlace>
			</BirthPlaceDetails>
			<SanctionsReferences>
				<Reference>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ReferenceValue SinceDay="13" SinceMonth="May" SinceYear="2014">556</ReferenceValue>
				</Reference>
				<Reference>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceValue SinceDay="24" SinceMonth="Jun" SinceYear="2014">462</ReferenceValue>
				</Reference>
			</SanctionsReferences>
			<CountryDetails>
				<CountryType code="1">
					<CountryTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<CountryValue code="42"/>
					</CountryTypeValue>
				</CountryType>
				<CountryType code="2">
					<CountryTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<CountryValue code="222"/>
					</CountryTypeValue>
				</CountryType>
			</CountryDetails>
			<IdentificationDetails>
				<IDType code="9">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>CAR</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="10">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>16723</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="14">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<IDValue>12998</IDValue>
					</IDTypeValue>
				</IDType>
			</IdentificationDetails>
			<SourceDetails>
				<Source>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<SourceValue name="http://www.treasury.gov/resource-center/sanctions/OFAC-Enforcement/Pages/car_eo.aspx"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<SourceValue name="http://www.treasury.gov/resource-center/sanctions/Programs/Documents/car_eo.pdf"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<SourceValue name="https://www.gov.uk/government/uploads/system/uploads/attachment_data/file/323909/CAR_Notice_691_2014.pdf"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<SourceValue name="https://www.gov.uk/government/uploads/system/uploads/attachment_data/file/383162/CAR_1276_2014.pdf"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<SourceValue name="http://www.treasury.gov/ofac/downloads/t11sdn.pdf"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<SourceValue name="http://hmt-sanctions.s3.amazonaws.com/sanctionsconlist.htm"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<SourceValue name="https://www.gov.uk/government/uploads/system/uploads/attachment_data/file/409009/central_african_republic.pdf"/>
				</Source>
			</SourceDetails>
		</Person>
		<Person date="20-Oct-2015" id="10441" recordaction="add">
			<ActiveStatusDetails>
				<ActiveStatus>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ActiveStatusValue>Active</ActiveStatusValue>
				</ActiveStatus>
			</ActiveStatusDetails>
			<PersonNameDetails>
				<PersonNames id="Name1">
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PersonNameValue>
						<FirstName>Bozize</FirstName>
						<Surname>Yangouvonda</Surname>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName1">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">博齐泽扬古翁达</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified1">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>0590 7871 3419 2254 0657 5040 6671</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName2">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">博齊澤揚古翁達</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional1">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>0590 7871 3419 2254 0657 5040 6671</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
				<PersonNames id="Name2">
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PersonNameValue>
						<FirstName>Francois</FirstName>
						<Surname>Bozize</Surname>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName3">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">弗朗夸斯博齐泽</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified2">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>1715 2597 1139 2448 0590 7871 3419</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName4">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">弗朗夸斯博齊澤</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional2">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>1715 2597 1139 2448 0590 7871 3419</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
				<PersonNames id="Name3">
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PersonNameValue>
						<FirstName>Francois</FirstName>
						<MiddleName>Yangouvonda</MiddleName>
						<Surname>Bozize</Surname>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName5">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">弗朗夸斯扬古翁达博齐泽</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified3">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>1715 2597 1139 2448 2254 0657 5040 6671 0590 7871 3419</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName6">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">弗朗夸斯揚古翁達博齊澤</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional3">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>1715 2597 1139 2448 2254 0657 5040 6671 0590 7871 3419</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
				<PersonNames id="Name4">
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<PersonNameValue>
						<FirstName>Yangouvonda</FirstName>
						<Surname>Bozize</Surname>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName7">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">扬古翁达博齐泽</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified4">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<PersonNameValue>
								<OriginalScriptName>2254 0657 5040 6671 0590 7871 3419</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName8">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">揚古翁達博齊澤</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional4">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<PersonNameValue>
								<OriginalScriptName>2254 0657 5040 6671 0590 7871 3419</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
			</PersonNameDetails>
			<Descriptions>
				<Description Description1="1"/>
				<Description Description1="3" Description2="7"/>
				<Description Description1="3" Description2="8"/>
			</Descriptions>
			<DateDetails>
				<DateType code="1">
					<DateTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<DateValue Day="14" Month="Oct" Year="1946"/>
					</DateTypeValue>
				</DateType>
			</DateDetails>
			<BirthPlaceDetails>
				<BirthPlace>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PlaceValue RegionCode="78"/>
				</BirthPlace>
				<BirthPlace>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<PlaceValue RegionCode="78" name="Mouila"/>
				</BirthPlace>
			</BirthPlaceDetails>
			<SanctionsReferences>
				<Reference>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ReferenceValue SinceDay="13" SinceMonth="May" SinceYear="2014">556</ReferenceValue>
				</Reference>
				<Reference>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceValue SinceDay="24" SinceMonth="Jun" SinceYear="2014">462</ReferenceValue>
				</Reference>
			</SanctionsReferences>
			<CountryDetails>
				<CountryType code="1">
					<CountryTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<CountryValue code="42"/>
					</CountryTypeValue>
				</CountryType>
				<CountryType code="2">
					<CountryTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<CountryValue code="222"/>
					</CountryTypeValue>
				</CountryType>
			</CountryDetails>
			<IdentificationDetails>
				<IDType code="9">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>CAR</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="10">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>16723</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="14">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<IDValue>12998</IDValue>
					</IDTypeValue>
				</IDType>
			</IdentificationDetails>
			<SourceDetails>
				<Source>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<SourceValue name="http://www.treasury.gov/resource-center/sanctions/OFAC-Enforcement/Pages/car_eo.aspx"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<SourceValue name="http://www.treasury.gov/resource-center/sanctions/Programs/Documents/car_eo.pdf"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<SourceValue name="https://www.gov.uk/government/uploads/system/uploads/attachment_data/file/323909/CAR_Notice_691_2014.pdf"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<SourceValue name="https://www.gov.uk/government/uploads/system/uploads/attachment_data/file/383162/CAR_1276_2014.pdf"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<SourceValue name="http://www.treasury.gov/ofac/downloads/t11sdn.pdf"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<SourceValue name="http://hmt-sanctions.s3.amazonaws.com/sanctionsconlist.htm"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<SourceValue name="https://www.gov.uk/government/uploads/system/uploads/attachment_data/file/409009/central_african_republic.pdf"/>
				</Source>
			</SourceDetails>
		</Person>
		<Person date="08-Dec-2016" id="13747" recordaction="add">
			<ActiveStatusDetails>
				<ActiveStatus>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ActiveStatusValue>Active</ActiveStatusValue>
				</ActiveStatus>
			</ActiveStatusDetails>
			<PersonNameDetails>
				<PersonNames id="Name1">
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PersonNameValue>
						<FirstName>Saddam</FirstName>
						<MiddleName>Hussein</MiddleName>
						<Surname>Al-Tikriti</Surname>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName1">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">萨达姆侯赛因阿勒提克里蒂</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified1">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>5646 6671 1191 0186 6357 0936 7093 0519 2251 0344 6849 5530</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName2">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">薩達姆侯賽因阿勒提克里蒂</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional1">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>5646 6671 1191 0186 6357 0936 7093 0519 2251 0344 6849 5530</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
				<PersonNames id="Name2">
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PersonNameValue>
						<FirstName>Saddam</FirstName>
						<Surname>Hussein</Surname>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName3">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">萨达姆侯赛因</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified2">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>5646 6671 1191 0186 6357 0936</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName4">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">薩達姆侯賽因</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional2">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>5646 6671 1191 0186 6357 0936</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
				<PersonNames id="Name3">
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PersonNameValue>
						<FirstName>Saddam</FirstName>
						<Surname>Hussain</Surname>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName5">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">萨达姆侯赛因</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified3">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>5646 6671 1191 0186 6357 0936</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName6">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">薩達姆侯賽因</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional3">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>5646 6671 1191 0186 6357 0936</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
				<PersonNames id="Name4">
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PersonNameValue>
						<FirstName>Saddam</FirstName>
						<Surname>Husayn</Surname>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName7">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">萨达姆侯赛因</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified4">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>5646 6671 1191 0186 6357 0936</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName8">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">薩達姆侯賽因</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional4">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>5646 6671 1191 0186 6357 0936</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
				<PersonNames id="Low Quality AKA1">
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PersonNameValue>
						<FirstName>Abu Ali</FirstName>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName9">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">阿布阿里</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified5">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>7093 1580 7093 6849</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName10">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">阿布阿里</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional5">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>7093 1580 7093 6849</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
			</PersonNameDetails>
			<Descriptions>
				<Description Description1="1"/>
				<Description Description1="3" Description2="8"/>
			</Descriptions>
			<DateDetails>
				<DateType code="1">
					<DateTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<DateValue Day="28" Month="Apr" Year="1937"/>
					</DateTypeValue>
				</DateType>
			</DateDetails>
			<BirthPlaceDetails>
				<BirthPlace>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PlaceValue RegionCode="102" name="Al-Awja, Near Tikrit"/>
				</BirthPlace>
			</BirthPlaceDetails>
			<SanctionsReferences>
				<Reference>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ReferenceValue SinceDay="24" SinceMonth="Jun" SinceYear="2003">556</ReferenceValue>
				</Reference>
				<Reference>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceValue SinceDay="02" SinceMonth="Jul" SinceYear="2003">462</ReferenceValue>
				</Reference>
			</SanctionsReferences>
			<CountryDetails>
				<CountryType code="1">
					<CountryTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<CountryValue code="102"/>
					</CountryTypeValue>
				</CountryType>
			</CountryDetails>
			<IdentificationDetails>
				<IDType code="9">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>IRAQ2</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="10">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>7843</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="14">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<IDValue>7612</IDValue>
					</IDTypeValue>
				</IDType>
			</IdentificationDetails>
			<SourceDetails>
				<Source>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<SourceValue name="http://www.hm-treasury.gov.uk/d/sanctionsconlist.htm"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<SourceValue name="http://www.treas.gov/offices/enforcement/ofac/sdn/t11sdn.pdf"/>
				</Source>
			</SourceDetails>
		</Person>
		<Person date="22-Apr-2015" id="13748" recordaction="add">
			<GenderDetails>
				<Gender>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<GenderValue>Female</GenderValue>
				</Gender>
			</GenderDetails>
			<ActiveStatusDetails>
				<ActiveStatus>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ActiveStatusValue>Active</ActiveStatusValue>
				</ActiveStatus>
			</ActiveStatusDetails>
			<PersonNameDetails>
				<PersonNames id="Name1">
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PersonNameValue>
						<FirstName>Sajida</FirstName>
						<MiddleName>Khayrallah</MiddleName>
						<Surname>Tilfah</Surname>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName1">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">赛义达海拉拉提勒法</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified1">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>6357 5030 6671 3189 2139 2139 2251 0519 3127</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName2">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">賽義達海拉拉提勒法</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional1">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>6357 5030 6671 3189 2139 2139 2251 0519 3127</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
			</PersonNameDetails>
			<Descriptions>
				<Description Description1="3" Description2="7"/>
				<Description Description1="3" Description2="8"/>
			</Descriptions>
			<DateDetails>
				<DateType code="1">
					<DateTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<DateValue Year="1937"/>
					</DateTypeValue>
				</DateType>
			</DateDetails>
			<BirthPlaceDetails>
				<BirthPlace>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PlaceValue RegionCode="102" name="Al-Awja, near Tikrit"/>
				</BirthPlace>
			</BirthPlaceDetails>
			<SanctionsReferences>
				<Reference>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ReferenceValue SinceDay="18" SinceMonth="Mar" SinceYear="2004">556</ReferenceValue>
				</Reference>
				<Reference>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceValue>462</ReferenceValue>
				</Reference>
			</SanctionsReferences>
			<CountryDetails>
				<CountryType code="1">
					<CountryTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<CountryValue code="102"/>
					</CountryTypeValue>
				</CountryType>
			</CountryDetails>
			<IdentificationDetails>
				<IDType code="9">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>IRAQ2</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="10">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>8198</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="14">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<IDValue>8259</IDValue>
					</IDTypeValue>
				</IDType>
			</IdentificationDetails>
			<SourceDetails>
				<Source>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<SourceValue name="http://documents.treasury.gov.uk/financialsanctions/sanctionsconlist.htm"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<SourceValue name="http://www.treas.gov/offices/enforcement/ofac/sdn/t11sdn.pdf"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<SourceValue name="http://www.hm-treasury.gov.uk/d/sanctionsconlist.htm"/>
				</Source>
			</SourceDetails>
		</Person>
		<Person date="18-Oct-2015" id="13749" recordaction="add">
			<GenderDetails>
				<Gender>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<GenderValue>Female</GenderValue>
				</Gender>
			</GenderDetails>
			<ActiveStatusDetails>
				<ActiveStatus>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ActiveStatusValue>Active</ActiveStatusValue>
				</ActiveStatus>
			</ActiveStatusDetails>
			<PersonNameDetails>
				<PersonNames id="Name1">
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PersonNameValue>
						<FirstName>Samira</FirstName>
						<Surname>Shahbandar</Surname>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName1">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">萨米拉沙赫班达尔</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified1">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>5646 4717 2139 3097 6378 3803 6671 1422</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName2">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">薩米拉沙赫班達尔</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional1">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>5646 4717 2139 3097 6378 3803 6671 1422</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
				<PersonNames id="Low Quality AKA1">
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PersonNameValue>
						<FirstName>Chadian</FirstName>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName3">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">沙迪安</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified2">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>3097 6611 1344</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName4">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">沙迪安</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional2">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>3097 6611 1344</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
			</PersonNameDetails>
			<Descriptions>
				<Description Description1="3" Description2="8"/>
			</Descriptions>
			<DateDetails>
				<DateType code="1">
					<DateTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<DateValue Year="1946"/>
					</DateTypeValue>
				</DateType>
			</DateDetails>
			<BirthPlaceDetails>
				<BirthPlace>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PlaceValue RegionCode="102" name="Baghdad"/>
				</BirthPlace>
			</BirthPlaceDetails>
			<SanctionsReferences>
				<Reference>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ReferenceValue SinceDay="18" SinceMonth="Mar" SinceYear="2004">556</ReferenceValue>
				</Reference>
				<Reference>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceValue>462</ReferenceValue>
				</Reference>
			</SanctionsReferences>
			<CountryDetails>
				<CountryType code="1">
					<CountryTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<CountryValue code="102"/>
					</CountryTypeValue>
				</CountryType>
			</CountryDetails>
			<IdentificationDetails>
				<IDType code="9">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>IRAQ2</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="10">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>8197</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="14">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<IDValue>8258</IDValue>
					</IDTypeValue>
				</IDType>
			</IdentificationDetails>
			<SourceDetails>
				<Source>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<SourceValue name="http://www.hm-treasury.gov.uk/d/sanctionsconlist.htm"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<SourceValue name="http://documents.treasury.gov.uk/financialsanctions/sanctionsconlist.htm"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<SourceValue name="http://www.treas.gov/offices/enforcement/ofac/sdn/t11sdn.pdf"/>
				</Source>
			</SourceDetails>
		</Person>
		<Person date="28-Feb-2019" id="13756" recordaction="chg">
			<GenderDetails>
				<Gender>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<GenderValue>Male</GenderValue>
				</Gender>
			</GenderDetails>
			<ActiveStatusDetails>
				<ActiveStatus>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ActiveStatusValue>Active</ActiveStatusValue>
				</ActiveStatus>
			</ActiveStatusDetails>
			<PersonNameDetails>
				<PersonNames id="Name1">
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PersonNameValue>
						<FirstName>Raghad</FirstName>
						<MiddleName>Saddam Hussein</MiddleName>
						<Surname>Al-Tikriti</Surname>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName1">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">拉加德萨达姆侯赛因阿勒提克里蒂</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified1">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>2139 0502 1795 5646 6671 1191 0186 6357 0936 7093 0519 2251 0344 6849 5530</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName2">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">拉加德薩達姆侯賽因阿勒提克里蒂</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional1">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>2139 0502 1795 5646 6671 1191 0186 6357 0936 7093 0519 2251 0344 6849 5530 3</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
			</PersonNameDetails>
			<Descriptions>
				<Description Description1="3" Description2="7"/>
				<Description Description1="3" Description2="8"/>
			</Descriptions>
			<DateDetails>
				<DateType code="1">
					<DateTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<DateValue Year="1967"/>
					</DateTypeValue>
				</DateType>
			</DateDetails>
			<BirthPlaceDetails>
				<BirthPlace>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PlaceValue RegionCode="102" name="Baghdad"/>
				</BirthPlace>
			</BirthPlaceDetails>
			<SanctionsReferences>
				<Reference>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ReferenceValue SinceDay="18" SinceMonth="Mar" SinceYear="2004">556</ReferenceValue>
				</Reference>
				<Reference>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceValue>462</ReferenceValue>
				</Reference>
			</SanctionsReferences>
			<AddressDetails>
				<Address>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<AddressValue>
						<AddressCity>Amman</AddressCity>
						<AddressCountry>118</AddressCountry>
					</AddressValue>
				</Address>
			</AddressDetails>
			<CountryDetails>
				<CountryType code="1">
					<CountryTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<CountryValue code="102"/>
					</CountryTypeValue>
				</CountryType>
				<CountryType code="2">
					<CountryTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<CountryValue code="108"/>
					</CountryTypeValue>
				</CountryType>
			</CountryDetails>
			<IdentificationDetails>
				<IDType code="9">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>IRAQ2</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="10">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>8192</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="14">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<IDValue>8253</IDValue>
					</IDTypeValue>
				</IDType>
			</IdentificationDetails>
			<SourceDetails>
				<Source>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<SourceValue name="http://documents.treasury.gov.uk/financialsanctions/sanctionsconlist.htm"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<SourceValue name="http://www.treas.gov/offices/enforcement/ofac/sdn/t11sdn.pdf"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<SourceValue name="http://www.hm-treasury.gov.uk/d/sanctionsconlist.htm"/>
				</Source>
			</SourceDetails>
		</Person>
		<Person date="18-Oct-2015" id="13757" recordaction="add">
			<GenderDetails>
				<Gender>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<GenderValue>Female</GenderValue>
				</Gender>
			</GenderDetails>
			<ActiveStatusDetails>
				<ActiveStatus>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ActiveStatusValue>Active</ActiveStatusValue>
				</ActiveStatus>
			</ActiveStatusDetails>
			<PersonNameDetails>
				<PersonNames id="Name1">
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PersonNameValue>
						<FirstName>Rana</FirstName>
						<MiddleName>Saddam Hussein</MiddleName>
						<Surname>Al-Tikriti</Surname>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName1">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">拉娜萨达姆侯赛因阿勒提克里蒂</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified1">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>2139 1226 5646 6671 1191 0186 6357 0936 7093 0519 2251 0344 6849 5530</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName2">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">拉娜薩達姆侯賽因阿勒提克里蒂</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional1">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>2139 1226 5646 6671 1191 0186 6357 0936 7093 0519 2251 0344 6849 5530</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
			</PersonNameDetails>
			<Descriptions>
				<Description Description1="3" Description2="8"/>
			</Descriptions>
			<DateDetails>
				<DateType code="1">
					<DateTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<DateValue Year="1969"/>
					</DateTypeValue>
				</DateType>
			</DateDetails>
			<BirthPlaceDetails>
				<BirthPlace>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PlaceValue RegionCode="102"/>
				</BirthPlace>
			</BirthPlaceDetails>
			<SanctionsReferences>
				<Reference>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ReferenceValue SinceDay="18" SinceMonth="Mar" SinceYear="2004">556</ReferenceValue>
				</Reference>
				<Reference>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceValue>462</ReferenceValue>
				</Reference>
			</SanctionsReferences>
			<AddressDetails>
				<Address>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<AddressValue>
						<AddressCity>Amman</AddressCity>
						<AddressCountry>108</AddressCountry>
					</AddressValue>
				</Address>
			</AddressDetails>
			<CountryDetails>
				<CountryType code="1">
					<CountryTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<CountryValue code="102"/>
					</CountryTypeValue>
				</CountryType>
				<CountryType code="2">
					<CountryTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<CountryValue code="108"/>
					</CountryTypeValue>
				</CountryType>
			</CountryDetails>
			<IdentificationDetails>
				<IDType code="9">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>IRAQ2</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="10">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>8193</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="14">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<IDValue>8254</IDValue>
					</IDTypeValue>
				</IDType>
			</IdentificationDetails>
			<SourceDetails>
				<Source>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<SourceValue name="http://www.treas.gov/offices/enforcement/ofac/sdn/t11sdn.pdf"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<SourceValue name="http://www.hm-treasury.gov.uk/d/sanctionsconlist.htm"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<SourceValue name="http://documents.treasury.gov.uk/financialsanctions/sanctionsconlist.htm"/>
				</Source>
			</SourceDetails>
		</Person>
		<Person date="22-Apr-2015" id="13759" recordaction="add">
			<ActiveStatusDetails>
				<ActiveStatus>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ActiveStatusValue>Active</ActiveStatusValue>
				</ActiveStatus>
			</ActiveStatusDetails>
			<PersonNameDetails>
				<PersonNames id="Name1">
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PersonNameValue>
						<FirstName>Taha</FirstName>
						<MiddleName>Yassin Ramadan</MiddleName>
						<Surname>Al-Jizrawi</Surname>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName1">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">塔哈亚辛拉马丹阿勒吉兹拉维</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified1">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>1044 0761 0068 6580 2139 7456 0030 7093 0519 0679 5417 2139 4850</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName2">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">塔哈亞辛拉馬丹阿勒吉茲拉維</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional1">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>1044 0761 0068 6580 2139 7456 0030 7093 0519 0679 5417 2139 4850</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
				<PersonNames id="Name2">
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PersonNameValue>
						<FirstName>Taha</FirstName>
						<MiddleName>Yassin</MiddleName>
						<Surname>Ramadan</Surname>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName3">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">塔哈亚辛拉马丹</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified2">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>1044 0761 0068 6580 2139 7456 0030</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName4">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">塔哈亞辛拉馬丹</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional2">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>1044 0761 0068 6580 2139 7456 0030</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
				<PersonNames id="Name3">
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PersonNameValue>
						<FirstName>Taha</FirstName>
						<MiddleName>Yasin</MiddleName>
						<Surname>Ramadan</Surname>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName5">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">塔哈亚辛拉马丹</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified3">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>1044 0761 0068 6580 2139 7456 0030</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName6">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">塔哈亞辛拉馬丹</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional3">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>1044 0761 0068 6580 2139 7456 0030</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
			</PersonNameDetails>
			<Descriptions>
				<Description Description1="1"/>
				<Description Description1="3" Description2="8"/>
			</Descriptions>
			<DateDetails>
				<DateType code="1">
					<DateTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<DateValue DateNotes="Approximate date of birth according to OFAC" Year="1938"/>
					</DateTypeValue>
				</DateType>
			</DateDetails>
			<BirthPlaceDetails>
				<BirthPlace>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<PlaceValue RegionCode="102" name="Mosul"/>
				</BirthPlace>
			</BirthPlaceDetails>
			<SanctionsReferences>
				<Reference>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ReferenceValue>556</ReferenceValue>
				</Reference>
				<Reference>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceValue SinceDay="02" SinceMonth="Jul" SinceYear="2003">462</ReferenceValue>
				</Reference>
			</SanctionsReferences>
			<CountryDetails>
				<CountryType code="1">
					<CountryTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<CountryValue code="102"/>
					</CountryTypeValue>
				</CountryType>
			</CountryDetails>
			<IdentificationDetails>
				<IDType code="9">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>IRAQ2</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="10">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>7862</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="14">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<IDValue>7582</IDValue>
					</IDTypeValue>
				</IDType>
			</IdentificationDetails>
			<SourceDetails>
				<Source>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<SourceValue name="http://www.hm-treasury.gov.uk/d/sanctionsconlist.htm"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<SourceValue name="http://www.treas.gov/offices/enforcement/ofac/sdn/t11sdn.pdf"/>
				</Source>
			</SourceDetails>
		</Person>
		<Person date="18-Oct-2015" id="13760" recordaction="add">
			<ActiveStatusDetails>
				<ActiveStatus>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ActiveStatusValue>Active</ActiveStatusValue>
				</ActiveStatus>
			</ActiveStatusDetails>
			<PersonNameDetails>
				<PersonNames id="Name1">
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PersonNameValue>
						<FirstName>Izzat</FirstName>
						<MiddleName>Ibrahim</MiddleName>
						<Surname>Al-Duri</Surname>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName1">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">伊扎特易卜拉欣阿勒杜里</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified1">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>0122 2089 3676 2496 0592 2139 2946 7093 0519 2629 6849</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName2">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">伊扎特易卜拉欣阿勒杜里</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional1">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>0122 2089 3676 2496 0592 2139 2946 7093 0519 2629 6849</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
				<PersonNames id="Low Quality AKA1">
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PersonNameValue>
						<FirstName>Abu Brays</FirstName>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName3">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">阿布卜赖斯</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified2">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>7093 1580 0592 6351 2448</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName4">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">阿布卜賴斯</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional2">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>7093 1580 0592 6351 2448</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
				<PersonNames id="Low Quality AKA2">
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PersonNameValue>
						<FirstName>Abu Ahmad</FirstName>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName5">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">阿布艾哈迈德</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified3">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>7093 1580 5337 0761 6701 1795</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName6">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">阿布艾哈邁德</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional3">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>7093 1580 5337 0761 6701 1795</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
			</PersonNameDetails>
			<Descriptions>
				<Description Description1="1"/>
				<Description Description1="3" Description2="7"/>
				<Description Description1="3" Description2="8"/>
			</Descriptions>
			<DateDetails>
				<DateType code="1">
					<DateTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<DateValue Year="1942"/>
					</DateTypeValue>
				</DateType>
			</DateDetails>
			<BirthPlaceDetails>
				<BirthPlace>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PlaceValue RegionCode="102" name="al-Dur"/>
				</BirthPlace>
			</BirthPlaceDetails>
			<SanctionsReferences>
				<Reference>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ReferenceValue>556</ReferenceValue>
				</Reference>
				<Reference>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceValue SinceDay="02" SinceMonth="Jul" SinceYear="2003">462</ReferenceValue>
				</Reference>
			</SanctionsReferences>
			<CountryDetails>
				<CountryType code="1">
					<CountryTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<CountryValue code="102"/>
					</CountryTypeValue>
				</CountryType>
			</CountryDetails>
			<IdentificationDetails>
				<IDType code="9">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>IRAQ2</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="10">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>7848</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="14">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<IDValue>7580</IDValue>
					</IDTypeValue>
				</IDType>
			</IdentificationDetails>
			<SourceDetails>
				<Source>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<SourceValue name="http://www.hm-treasury.gov.uk/d/sanctionsconlist.htm"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<SourceValue name="http://www.treas.gov/offices/enforcement/ofac/sdn/t11sdn.pdf"/>
				</Source>
			</SourceDetails>
		</Person>
		<Person date="18-Oct-2015" id="13762" recordaction="add">
			<ActiveStatusDetails>
				<ActiveStatus>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ActiveStatusValue>Active</ActiveStatusValue>
				</ActiveStatus>
			</ActiveStatusDetails>
			<PersonNameDetails>
				<PersonNames id="Name1">
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PersonNameValue>
						<FirstName>Tariq</FirstName>
						<Surname>Aziz</Surname>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName1">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">塔里克阿齐兹</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified1">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>1044 6849 0344 7093 7871 5417</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName2">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">塔里克阿齊茲</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional1">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>1044 6849 0344 7093 7871 5417</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
				<PersonNames id="Name2">
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PersonNameValue>
						<FirstName>Tariq</FirstName>
						<MiddleName>Mikhail</MiddleName>
						<Surname>Aziz</Surname>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName3">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">塔里克米哈伊尔阿齐兹</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified2">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>1044 6849 0344 4717 0761 0122 1422 7093 7871 5417</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName4">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">塔里克米哈伊尔阿齊茲</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional2">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>1044 6849 0344 4717 0761 0122 1422 7093 7871 5417</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
			</PersonNameDetails>
			<Descriptions>
				<Description Description1="1"/>
				<Description Description1="3" Description2="8"/>
			</Descriptions>
			<DateDetails>
				<DateType code="1">
					<DateTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<DateValue Day="01" Month="Jul" Year="1936"/>
					</DateTypeValue>
				</DateType>
			</DateDetails>
			<BirthPlaceDetails>
				<BirthPlace>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PlaceValue RegionCode="102" name="Baghdad"/>
				</BirthPlace>
				<BirthPlace>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PlaceValue RegionCode="102" name="Mosul"/>
				</BirthPlace>
			</BirthPlaceDetails>
			<SanctionsReferences>
				<Reference>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ReferenceValue>556</ReferenceValue>
				</Reference>
				<Reference>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceValue SinceDay="02" SinceMonth="Jul" SinceYear="2003">462</ReferenceValue>
				</Reference>
			</SanctionsReferences>
			<CountryDetails>
				<CountryType code="1">
					<CountryTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<CountryValue code="102"/>
					</CountryTypeValue>
				</CountryType>
			</CountryDetails>
			<IdentificationDetails>
				<IDType code="3">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<IDValue IDNotes="Jul-1997">34409/129</IDValue>
					</IDTypeValue>
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue IDNotes="Issued Jul-1997">No34409/129</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="9">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>IRAQ2</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="10">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>7867</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="14">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<IDValue>7603</IDValue>
					</IDTypeValue>
				</IDType>
			</IdentificationDetails>
			<SourceDetails>
				<Source>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<SourceValue name="http://www.treas.gov/offices/enforcement/ofac/sdn/t11sdn.pdf"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<SourceValue name="http://www.hm-treasury.gov.uk/d/sanctionsconlist.htm"/>
				</Source>
			</SourceDetails>
		</Person>
		<Person date="18-Oct-2015" id="13763" recordaction="add">
			<ActiveStatusDetails>
				<ActiveStatus>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ActiveStatusValue>Active</ActiveStatusValue>
				</ActiveStatus>
			</ActiveStatusDetails>
			<PersonNameDetails>
				<PersonNames id="Name1">
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PersonNameValue>
						<FirstName>Hikmat</FirstName>
						<MiddleName>Mizban Ibrahim</MiddleName>
						<Surname>Al-Azzawi</Surname>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName1">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">希克迈特米兹班易卜拉欣阿勒阿扎维</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified1">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>1585 0344 6701 3676 4717 5417 3803 2496 0592 2139 2946 7093 0519 7093 2089 4850</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName2">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">希克邁特米茲班易卜拉欣阿勒阿扎維</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional1">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>1585 0344 6701 3676 4717 5417 3803 2496 0592 2139 2946 7093 0519 7093 2089 4850</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
			</PersonNameDetails>
			<Descriptions>
				<Description Description1="1"/>
				<Description Description1="3" Description2="8"/>
			</Descriptions>
			<DateDetails>
				<DateType code="1">
					<DateTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<DateValue Year="1934"/>
					</DateTypeValue>
				</DateType>
			</DateDetails>
			<BirthPlaceDetails>
				<BirthPlace>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PlaceValue RegionCode="102" name="Diyala"/>
				</BirthPlace>
			</BirthPlaceDetails>
			<SanctionsReferences>
				<Reference>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ReferenceValue>556</ReferenceValue>
				</Reference>
				<Reference>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceValue SinceDay="02" SinceMonth="Jul" SinceYear="2003">462</ReferenceValue>
				</Reference>
			</SanctionsReferences>
			<CountryDetails>
				<CountryType code="1">
					<CountryTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<CountryValue code="102"/>
					</CountryTypeValue>
				</CountryType>
			</CountryDetails>
			<IdentificationDetails>
				<IDType code="9">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>IRAQ2</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="10">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>7870</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="14">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<IDValue>7578</IDValue>
					</IDTypeValue>
				</IDType>
			</IdentificationDetails>
			<SourceDetails>
				<Source>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<SourceValue name="http://www.treas.gov/offices/enforcement/ofac/sdn/t11sdn.pdf"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<SourceValue name="http://www.hm-treasury.gov.uk/d/sanctionsconlist.htm"/>
				</Source>
			</SourceDetails>
		</Person>
		<Person date="18-Oct-2015" id="13764" recordaction="add">
			<ActiveStatusDetails>
				<ActiveStatus>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ActiveStatusValue>Active</ActiveStatusValue>
				</ActiveStatus>
			</ActiveStatusDetails>
			<PersonNameDetails>
				<PersonNames id="Name1">
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PersonNameValue>
						<FirstName>Ahmad</FirstName>
						<MiddleName>Hussein</MiddleName>
						<Surname>Al-Khodair</Surname>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName1">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">艾哈迈德侯赛因阿勒胡达伊尔</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified1">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>5337 0761 6701 1795 0186 6357 0936 7093 0519 5170 6671 0122 1422</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName2">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">艾哈邁德侯賽因阿勒胡達伊尔</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional1">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>5337 0761 6701 1795 0186 6357 0936 7093 0519 5170 6671 0122 1422</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
				<PersonNames id="Name2">
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PersonNameValue>
						<FirstName>Ahmad</FirstName>
						<MiddleName>Husayn Khudayir</MiddleName>
						<Surname>Samarrai</Surname>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName3">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">艾哈迈德侯赛因胡达伊尔萨马赖</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified2">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>5337 0761 6701 1795 0186 6357 0936 5170 6671 0122 1422 5646 7456 6351</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName4">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">艾哈邁德侯賽因胡達伊尔薩馬賴</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional2">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>5337 0761 6701 1795 0186 6357 0936 5170 6671 0122 1422 5646 7456 6351</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
			</PersonNameDetails>
			<Descriptions>
				<Description Description1="1"/>
				<Description Description1="3" Description2="8"/>
			</Descriptions>
			<DateDetails>
				<DateType code="1">
					<DateTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<DateValue Year="1941"/>
					</DateTypeValue>
				</DateType>
			</DateDetails>
			<SanctionsReferences>
				<Reference>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ReferenceValue>556</ReferenceValue>
				</Reference>
			</SanctionsReferences>
			<IdentificationDetails>
				<IDType code="9">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>IRAQ2</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="10">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>8329</IDValue>
					</IDTypeValue>
				</IDType>
			</IdentificationDetails>
			<SourceDetails>
				<Source>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<SourceValue name="http://www.treasury.gov/ofac/downloads/sdn.xml"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<SourceValue name="http://www.treasury.gov/ofac/downloads/t11sdn.pdf"/>
				</Source>
			</SourceDetails>
		</Person>
		<Person date="30-Mar-2016" id="13768" recordaction="add">
			<ActiveStatusDetails>
				<ActiveStatus>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ActiveStatusValue>Active</ActiveStatusValue>
				</ActiveStatus>
			</ActiveStatusDetails>
			<PersonNameDetails>
				<PersonNames id="Name1">
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PersonNameValue>
						<FirstName>Hamid</FirstName>
						<MiddleName>Yusif</MiddleName>
						<Surname>Al-Hammadi</Surname>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName1">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">哈米德优素福阿勒哈马迪</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified1">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>0761 4717 1795 0327 4790 4395 7093 0519 0761 7456 6611</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName2">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">哈米德優素福阿勒哈馬迪</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional1">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>0761 4717 1795 0327 4790 4395 7093 0519 0761 7456 6611</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
				<PersonNames id="Name2">
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PersonNameValue>
						<FirstName>Hamed</FirstName>
						<MiddleName>Yussef</MiddleName>
						<Surname>Hamadi</Surname>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName3">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">哈米德优素福哈马迪</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified2">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>0761 4717 1795 0327 4790 4395 0761 7456 6611</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName4">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">哈米德優素福哈馬迪</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional2">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>0761 4717 1795 0327 4790 4395 0761 7456 6611</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
			</PersonNameDetails>
			<Descriptions>
				<Description Description1="1"/>
				<Description Description1="3" Description2="8"/>
			</Descriptions>
			<SanctionsReferences>
				<Reference>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ReferenceValue>556</ReferenceValue>
				</Reference>
			</SanctionsReferences>
			<CountryDetails>
				<CountryType code="2">
					<CountryTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<CountryValue code="102"/>
					</CountryTypeValue>
				</CountryType>
			</CountryDetails>
			<IdentificationDetails>
				<IDType code="9">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>IRAQ2</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="10">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>8323</IDValue>
					</IDTypeValue>
				</IDType>
			</IdentificationDetails>
			<SourceDetails>
				<Source>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<SourceValue name="http://www.treas.gov/offices/enforcement/ofac/sdn/sdnlist.txt"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<SourceValue name="https://www.treasury.gov/ofac/downloads/sdnlist.pdf"/>
				</Source>
			</SourceDetails>
		</Person>
		<Person date="22-Apr-2015" id="13769" recordaction="add">
			<ActiveStatusDetails>
				<ActiveStatus>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ActiveStatusValue>Active</ActiveStatusValue>
				</ActiveStatus>
			</ActiveStatusDetails>
			<PersonNameDetails>
				<PersonNames id="Name1">
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PersonNameValue>
						<FirstName>Sultan</FirstName>
						<MiddleName>Hashim Ahmad</MiddleName>
						<Surname>Al-Tai</Surname>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName1">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">苏丹哈希姆艾哈迈德阿勒塔伊</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified1">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>5685 0030 0761 1585 1191 5337 0761 6701 1795 7093 0519 1044 0122</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName2">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">蘇丹哈希姆艾哈邁德阿勒塔伊</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional1">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>5685 0030 0761 1585 1191 5337 0761 6701 1795 7093 0519 1044 0122</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
				<PersonNames id="Name2">
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<PersonNameValue>
						<FirstName>Sultan</FirstName>
						<MiddleName>Hashim Ahmad</MiddleName>
						<Surname>Al-Ta'i</Surname>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName3">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">苏丹哈希姆艾哈迈德阿勒塔伊</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified2">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<PersonNameValue>
								<OriginalScriptName>5685 0030 0761 1585 1191 5337 0761 6701 1795 7093 0519 1044 0122</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName4">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">蘇丹哈希姆艾哈邁德阿勒塔伊</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional2">
							<ReferenceGroup ReferenceGroupCode="HMTASS"/>
							<PersonNameValue>
								<OriginalScriptName>5685 0030 0761 1585 1191 5337 0761 6701 1795 7093 0519 1044 0122</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
			</PersonNameDetails>
			<Descriptions>
				<Description Description1="1"/>
				<Description Description1="3" Description2="8"/>
			</Descriptions>
			<DateDetails>
				<DateType code="1">
					<DateTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<DateValue Year="1944"/>
					</DateTypeValue>
				</DateType>
			</DateDetails>
			<BirthPlaceDetails>
				<BirthPlace>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PlaceValue RegionCode="102" name="Mosul"/>
				</BirthPlace>
			</BirthPlaceDetails>
			<SanctionsReferences>
				<Reference>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ReferenceValue>556</ReferenceValue>
				</Reference>
				<Reference>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<ReferenceValue SinceDay="02" SinceMonth="Jul" SinceYear="2003">462</ReferenceValue>
				</Reference>
			</SanctionsReferences>
			<CountryDetails>
				<CountryType code="1">
					<CountryTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<CountryValue code="102"/>
					</CountryTypeValue>
				</CountryType>
			</CountryDetails>
			<IdentificationDetails>
				<IDType code="9">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>IRAQ2</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="10">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>7869</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="14">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="HMTASS"/>
						<IDValue>7596</IDValue>
					</IDTypeValue>
				</IDType>
			</IdentificationDetails>
			<SourceDetails>
				<Source>
					<ReferenceGroup ReferenceGroupCode="HMTASS"/>
					<SourceValue name="http://www.hm-treasury.gov.uk/d/sanctionsconlist.htm"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<SourceValue name="http://www.treas.gov/offices/enforcement/ofac/sdn/t11sdn.pdf"/>
				</Source>
			</SourceDetails>
		</Person>
		<Person date="18-Oct-2015" id="13771" recordaction="add">
			<ActiveStatusDetails>
				<ActiveStatus>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ActiveStatusValue>Active</ActiveStatusValue>
				</ActiveStatus>
			</ActiveStatusDetails>
			<PersonNameDetails>
				<PersonNames id="Name1">
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PersonNameValue>
						<FirstName>Abd</FirstName>
						<MiddleName>Al-Munim Ahmad</MiddleName>
						<Surname>Salih</Surname>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName1">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">阿布德阿勒穆奈姆艾哈迈德萨利赫</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified1">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>7093 1580 1795 7093 0519 4476 1143 1191 5337 0761 6701 1795 5646 0448 6378</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName2">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">阿布德阿勒穆奈姆艾哈邁德薩利赫</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional1">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>7093 1580 1795 7093 0519 4476 1143 1191 5337 0761 6701 1795 5646 0448 6378</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
				<PersonNames id="Name2">
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PersonNameValue>
						<FirstName>Abdel</FirstName>
						<MiddleName>Moneim Ahmad</MiddleName>
						<Surname>Saleh</Surname>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName3">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">阿卜杜勒穆奈姆艾哈迈德萨利赫</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified2">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>7093 0592 2629 0519 4476 1143 1191 5337 0761 6701 1795 5646 0448 6378</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName4">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">阿卜杜勒穆奈姆艾哈邁德薩利赫</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional2">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>7093 0592 2629 0519 4476 1143 1191 5337 0761 6701 1795 5646 0448 6378</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
			</PersonNameDetails>
			<Descriptions>
				<Description Description1="3" Description2="8"/>
			</Descriptions>
			<DateDetails>
				<DateType code="1">
					<DateTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<DateValue Year="1943"/>
					</DateTypeValue>
				</DateType>
			</DateDetails>
			<SanctionsReferences>
				<Reference>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ReferenceValue>556</ReferenceValue>
				</Reference>
			</SanctionsReferences>
			<CountryDetails>
				<CountryType code="2">
					<CountryTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<CountryValue code="102"/>
					</CountryTypeValue>
				</CountryType>
			</CountryDetails>
			<IdentificationDetails>
				<IDType code="9">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>IRAQ2</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="10">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>8401</IDValue>
					</IDTypeValue>
				</IDType>
			</IdentificationDetails>
			<SourceDetails>
				<Source>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<SourceValue name="http://www.treas.gov/offices/enforcement/ofac/sdn/t11sdn.pdf"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<SourceValue name="http://www.treas.gov/offices/enforcement/ofac/sdn/sdnlist.txt"/>
				</Source>
			</SourceDetails>
		</Person>
		<Person date="18-Oct-2015" id="13773" recordaction="add">
			<ActiveStatusDetails>
				<ActiveStatus>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ActiveStatusValue>Active</ActiveStatusValue>
				</ActiveStatus>
			</ActiveStatusDetails>
			<PersonNameDetails>
				<PersonNames id="Name1">
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PersonNameValue>
						<FirstName>Umid</FirstName>
						<MiddleName>Medhat</MiddleName>
						<Surname>Mubarak</Surname>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName1">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">乌米德迈扎特穆巴拉克</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified1">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>3527 4717 1795 6701 2089 3676 4476 1572 2139 0344</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName2">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">烏米德邁扎特穆巴拉克</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional1">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>3527 4717 1795 6701 2089 3676 4476 1572 2139 0344</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
				<PersonNames id="Name2">
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<PersonNameValue>
						<FirstName>Umid</FirstName>
						<MiddleName>Midhat</MiddleName>
						<Surname>Mubarak</Surname>
					</PersonNameValue>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName3">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">乌米德迈扎特穆巴拉克</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Simplified2">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>3527 4717 1795 6701 2089 3676 4476 1572 2139 0344</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
					<NamesGroup>
						<PersonNames id="CCC_OriginalScriptName4">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">烏米德邁扎特穆巴拉克</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
						<PersonNames id="CCC_Traditional2">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<PersonNameValue>
								<OriginalScriptName>3527 4717 1795 6701 2089 3676 4476 1572 2139 0344</OriginalScriptName>
							</PersonNameValue>
						</PersonNames>
					</NamesGroup>
				</PersonNames>
			</PersonNameDetails>
			<Descriptions>
				<Description Description1="1"/>
				<Description Description1="3" Description2="8"/>
			</Descriptions>
			<DateDetails>
				<DateType code="1">
					<DateTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<DateValue DateNotes="circa" Year="1940"/>
					</DateTypeValue>
				</DateType>
			</DateDetails>
			<SanctionsReferences>
				<Reference>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ReferenceValue>556</ReferenceValue>
				</Reference>
			</SanctionsReferences>
			<CountryDetails>
				<CountryType code="2">
					<CountryTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<CountryValue code="102"/>
					</CountryTypeValue>
				</CountryType>
			</CountryDetails>
			<IdentificationDetails>
				<IDType code="9">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>IRAQ2</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="10">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>8389</IDValue>
					</IDTypeValue>
				</IDType>
			</IdentificationDetails>
			<SourceDetails>
				<Source>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<SourceValue name="http://www.treas.gov/offices/enforcement/ofac/sdn/sdnlist.txt"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<SourceValue name="http://www.treas.gov/offices/enforcement/ofac/sdn/t11sdn.pdf"/>
				</Source>
			</SourceDetails>
		</Person>
		<Entity date="21-Jun-2017" id="11105298" recordaction="add">
			<ActiveStatusDetails>
				<ActiveStatus>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ActiveStatusValue>Active</ActiveStatusValue>
				</ActiveStatus>
			</ActiveStatusDetails>
			<EntityNameDetails>
				<EntityNames id="Name1">
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<EntityNameValue>
						<EntityName>VVB, PAO</EntityName>
					</EntityNameValue>
					<NamesGroup>
						<EntityNames id="CCC_OriginalScriptName1">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">维维比，公共股份公司</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
						<EntityNames id="CCC_Simplified1">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName>4850 4850 3024 0361 0364 5140 0118 0361 0674</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
					</NamesGroup>
					<NamesGroup>
						<EntityNames id="CCC_OriginalScriptName2">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">維維比，公共股份公司</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
						<EntityNames id="CCC_Traditional1">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName>4850 4850 3024 0361 0364 5140 0118 0361 0674</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
					</NamesGroup>
				</EntityNames>
				<EntityNames id="Name2">
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<EntityNameValue>
						<EntityName>Public Joint-Stock Company Bank VVB</EntityName>
					</EntityNameValue>
					<NamesGroup>
						<EntityNames id="CCC_OriginalScriptName9">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">维维比公共股份公司银行</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
						<EntityNames id="CCC_Simplified5">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName>4850 4850 3024 0361 0364 5140 0118 0361 0674 6892 5887</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
					</NamesGroup>
					<NamesGroup>
						<EntityNames id="CCC_OriginalScriptName10">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">維維比公共股份公司銀行</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
						<EntityNames id="CCC_Traditional5">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName>4850 4850 3024 0361 0364 5140 0118 0361 0674 6892 5887</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
					</NamesGroup>
				</EntityNames>
				<EntityNames id="Name3">
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<EntityNameValue>
						<EntityName>Publichnoye Joint-Stock Company Bank VVB</EntityName>
					</EntityNameValue>
					<NamesGroup>
						<EntityNames id="CCC_OriginalScriptName3">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">维维比公共股份公司银行</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
						<EntityNames id="CCC_Simplified2">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName>4850 4850 3024 0361 0364 5140 0118 0361 0674 6892 5887</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
					</NamesGroup>
					<NamesGroup>
						<EntityNames id="CCC_OriginalScriptName4">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">維維比公共股份公司銀行</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
						<EntityNames id="CCC_Traditional2">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName>4850 4850 3024 0361 0364 5140 0118 0361 0674 6892 5887</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
					</NamesGroup>
				</EntityNames>
				<EntityNames id="Name4">
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<EntityNameValue>
						<EntityName>PJSC Bank VVB</EntityName>
					</EntityNameValue>
					<NamesGroup>
						<EntityNames id="CCC_OriginalScriptName5">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">维维比公共股份公司银行</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
						<EntityNames id="CCC_Simplified3">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName>4850 4850 3024 0361 0364 5140 0118 0361 0674 6892 5887</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
					</NamesGroup>
					<NamesGroup>
						<EntityNames id="CCC_OriginalScriptName6">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">維維比公共股份公司銀行</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
						<EntityNames id="CCC_Traditional3">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName>4850 4850 3024 0361 0364 5140 0118 0361 0674 6892 5887</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
					</NamesGroup>
				</EntityNames>
				<EntityNames id="Name5">
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<EntityNameValue>
						<EntityName>Publichnoe Aktsionernoe Obshchestvo Bank VVB</EntityName>
					</EntityNameValue>
					<NamesGroup>
						<EntityNames id="CCC_OriginalScriptName7">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">维维比公共股份公司银行</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
						<EntityNames id="CCC_Simplified4">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName>4850 4850 3024 0361 0364 5140 0118 0361 0674 6892 5887</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
					</NamesGroup>
					<NamesGroup>
						<EntityNames id="CCC_OriginalScriptName8">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">維維比公共股份公司銀行</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
						<EntityNames id="CCC_Traditional4">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName>4850 4850 3024 0361 0364 5140 0118 0361 0674 6892 5887</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
					</NamesGroup>
				</EntityNames>
				<EntityNames id="Name6">
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<EntityNameValue>
						<EntityName>Commercial Joint-Stock Incorporation Bank Yaroslavich</EntityName>
					</EntityNameValue>
					<NamesGroup>
						<EntityNames id="CCC_OriginalScriptName11">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">亚罗斯拉维奇商业股份公司注册银行</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
						<EntityNames id="CCC_Simplified6">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName>0068 5012 2448 2139 4850 1142 0794 2814 5140 0118 0361 0674 3137 0374 6892 5887</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
					</NamesGroup>
					<NamesGroup>
						<EntityNames id="CCC_OriginalScriptName12">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">亞羅斯拉維奇商業股份公司注册銀行</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
						<EntityNames id="CCC_Traditional6">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName>0068 5012 2448 2139 4850 1142 0794 2814 5140 0118 0361 0674 3137 0374 6892 5887</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
					</NamesGroup>
				</EntityNames>
				<EntityNames id="Name7">
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<EntityNameValue>
						<EntityName>Kommercheski Bank Yaroslavich, PAO</EntityName>
					</EntityNameValue>
					<NamesGroup>
						<EntityNames id="CCC_OriginalScriptName13">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">亚罗斯拉维奇商业银行，公共股份公司</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
						<EntityNames id="CCC_Simplified7">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName>0068 5012 2448 2139 4850 1142 0794 2814 6892 5887 0361 0364 5140 0118 0361 0674</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
					</NamesGroup>
					<NamesGroup>
						<EntityNames id="CCC_OriginalScriptName14">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">亞羅斯拉維奇商業銀行，公共股份公司</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
						<EntityNames id="CCC_Traditional7">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName>0068 5012 2448 2139 4850 1142 0794 2814 6892 5887 0361 0364 5140 0118 0361 0674</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
					</NamesGroup>
				</EntityNames>
			</EntityNameDetails>
			<Descriptions>
				<Description Description1="4" Description2="8" Description3="7"/>
			</Descriptions>
			<SanctionsReferences>
				<Reference>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ReferenceValue SinceDay="20" SinceMonth="Jun" SinceYear="2017">556</ReferenceValue>
				</Reference>
			</SanctionsReferences>
			<AddressDetails>
				<Address>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<AddressValue>
						<AddressLine>39A Ul. Suvorova</AddressLine>
						<AddressCity>Sevastopol</AddressCity>
						<AddressState>Crimea</AddressState>
						<AddressCountry>223</AddressCountry>
					</AddressValue>
				</Address>
				<Address>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<AddressValue>
						<AddressLine>3A ul., 4-ya Bastionnaya</AddressLine>
						<AddressCity>Sevastopol</AddressCity>
						<AddressState>Crimea</AddressState>
						<ZipCode>299011</ZipCode>
						<AddressCountry>223</AddressCountry>
					</AddressValue>
				</Address>
				<Address>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<AddressValue>
						<AddressLine>5 Per. Pionerskiy</AddressLine>
						<AddressCity>Simferopol</AddressCity>
						<AddressState>Crimea</AddressState>
						<AddressCountry>223</AddressCountry>
					</AddressValue>
				</Address>
				<Address>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<AddressValue>
						<AddressLine>Voronina, 10</AddressLine>
						<AddressCity>Sevastopol</AddressCity>
						<AddressState>Crimea</AddressState>
						<ZipCode>299011</ZipCode>
						<AddressCountry>223</AddressCountry>
					</AddressValue>
				</Address>
			</AddressDetails>
			<CountryDetails>
				<CountryType code="3">
					<CountryTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<CountryValue code="223"/>
					</CountryTypeValue>
				</CountryType>
			</CountryDetails>
			<IdentificationDetails>
				<IDType code="6">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue IDNotes="BIK (RU)">043510133</IDValue>
					</IDTypeValue>
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue IDNotes="BIK (RU)">046711106</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="9">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>UKRAINE-EO13685</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="10">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>22508</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="15">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>YARORU21</IDValue>
					</IDTypeValue>
				</IDType>
			</IdentificationDetails>
			<SourceDetails>
				<Source>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<SourceValue name="https://www.treasury.gov/press-center/press-releases/Pages/sm0114.aspx"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<SourceValue name="https://www.treasury.gov/ofac/downloads/sdnlist.pdf"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<SourceValue name="https://www.treasury.gov/resource-center/sanctions/OFAC-Enforcement/Pages/20170620.aspx"/>
				</Source>
			</SourceDetails>
		</Entity>
		<Entity date="22-Jun-2017" id="11105302" recordaction="add">
			<ActiveStatusDetails>
				<ActiveStatus>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ActiveStatusValue>Active</ActiveStatusValue>
				</ActiveStatus>
			</ActiveStatusDetails>
			<EntityNameDetails>
				<EntityNames id="Name1">
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<EntityNameValue>
						<EntityName>'Wolf' Holding of Security Structures</EntityName>
					</EntityNameValue>
					<NamesGroup>
						<EntityNames id="CCC_OriginalScriptName1">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">'狼'控股安全结构</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
						<EntityNames id="CCC_Simplified1">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName>3708 2235 5140 1344 0356 4814 2845</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
					</NamesGroup>
					<NamesGroup>
						<EntityNames id="CCC_OriginalScriptName2">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">'狼'控股安全結構</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
						<EntityNames id="CCC_Traditional1">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName>3708 2235 5140 1344 0356 4814 2845</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
					</NamesGroup>
				</EntityNames>
				<EntityNames id="Name2">
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<EntityNameValue>
						<EntityName>Kholding Okhrannykh Struktur Volk</EntityName>
					</EntityNameValue>
					<NamesGroup>
						<EntityNames id="CCC_OriginalScriptName3">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">狼控股安全结构</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
						<EntityNames id="CCC_Simplified2">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName>3708 2235 5140 1344 0356 4814 2845</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
					</NamesGroup>
					<NamesGroup>
						<EntityNames id="CCC_OriginalScriptName4">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">狼控股安全結構</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
						<EntityNames id="CCC_Traditional2">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName>3708 2235 5140 1344 0356 4814 2845</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
					</NamesGroup>
				</EntityNames>
				<EntityNames id="Name3">
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<EntityNameValue>
						<EntityName>Holding Security Structure Wolf</EntityName>
					</EntityNameValue>
					<NamesGroup>
						<EntityNames id="CCC_OriginalScriptName5">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">狼控股安全结构</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
						<EntityNames id="CCC_Simplified3">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName>3708 2235 5140 1344 0356 4814 2845</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
					</NamesGroup>
					<NamesGroup>
						<EntityNames id="CCC_OriginalScriptName6">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">狼控股安全結構</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
						<EntityNames id="CCC_Traditional3">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName>3708 2235 5140 1344 0356 4814 2845</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
					</NamesGroup>
				</EntityNames>
				<EntityNames id="Name4">
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<EntityNameValue>
						<EntityName>Defense Holding Structure 'Wolf'</EntityName>
					</EntityNameValue>
					<NamesGroup>
						<EntityNames id="CCC_OriginalScriptName7">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">'狼'国防控股结构</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
						<EntityNames id="CCC_Simplified4">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName>3708 0948 7089 2235 5140 4814 2845</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
					</NamesGroup>
					<NamesGroup>
						<EntityNames id="CCC_OriginalScriptName8">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">'狼'國防控股結構</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
						<EntityNames id="CCC_Traditional4">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName>3708 0948 7089 2235 5140 4814 2845</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
					</NamesGroup>
				</EntityNames>
				<EntityNames id="Name5">
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<EntityNameValue>
						<EntityName>Wolf Holding Company</EntityName>
					</EntityNameValue>
					<NamesGroup>
						<EntityNames id="CCC_OriginalScriptName9">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">狼控股公司</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
						<EntityNames id="CCC_Simplified5">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName>3708 2235 5140 0361 0674</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
					</NamesGroup>
					<NamesGroup>
						<EntityNames id="CCC_OriginalScriptName10">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">狼控股公司</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
						<EntityNames id="CCC_Traditional5">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName>3708 2235 5140 0361 0674</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
					</NamesGroup>
				</EntityNames>
			</EntityNameDetails>
			<Descriptions>
				<Description Description1="4" Description2="7"/>
				<Description Description1="4" Description2="8"/>
				<Description Description1="4" Description2="23" Description3="55"/>
			</Descriptions>
			<SanctionsReferences>
				<Reference>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ReferenceValue SinceDay="20" SinceMonth="Jun" SinceYear="2017">556</ReferenceValue>
				</Reference>
			</SanctionsReferences>
			<AddressDetails>
				<Address>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<AddressValue>
						<AddressLine>Nizhniye Mnevniki, 110</AddressLine>
						<AddressCity>Moscow</AddressCity>
						<AddressCountry>176</AddressCountry>
					</AddressValue>
				</Address>
				<Address>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<AddressValue>
						<AddressLine>ul. Panferova d. 18</AddressLine>
						<AddressCity>Moscow</AddressCity>
						<ZipCode>119261</ZipCode>
						<AddressCountry>176</AddressCountry>
					</AddressValue>
				</Address>
			</AddressDetails>
			<CountryDetails>
				<CountryType code="3">
					<CountryTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<CountryValue code="176"/>
					</CountryTypeValue>
				</CountryType>
			</CountryDetails>
			<IdentificationDetails>
				<IDType code="8">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue IDNotes="Country of Issue: Russia  ">7736640919</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="9">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>UKRAINE-EO13660</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="10">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>22554</IDValue>
					</IDTypeValue>
				</IDType>
			</IdentificationDetails>
			<SourceDetails>
				<Source>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<SourceValue name="https://www.treasury.gov/press-center/press-releases/Pages/sm0114.aspx"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<SourceValue name="https://www.treasury.gov/resource-center/sanctions/OFAC-Enforcement/Pages/20170620.aspx"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<SourceValue name="https://www.treasury.gov/ofac/downloads/sdnlist.pdf"/>
				</Source>
			</SourceDetails>
		</Entity>
		<Entity date="30-Jun-2017" id="11111495" recordaction="add">
			<ActiveStatusDetails>
				<ActiveStatus>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ActiveStatusValue>Active</ActiveStatusValue>
				</ActiveStatus>
			</ActiveStatusDetails>
			<EntityNameDetails>
				<EntityNames id="Name1">
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<EntityNameValue>
						<EntityName>Dalian Global Unity Shipping Co., Ltd.</EntityName>
					</EntityNameValue>
					<NamesGroup>
						<EntityNames id="CCC_OriginalScriptName1">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">大连宁联船务有限公司</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
						<EntityNames id="CCC_Simplified1">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName>1129 6647 1337 5114 5307 0523 2589 7098 0361 0674</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
					</NamesGroup>
					<NamesGroup>
						<EntityNames id="CCC_OriginalScriptName2">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">大連宁聯船務有限公司</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
						<EntityNames id="CCC_Traditional1">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName>1129 6647 1337 5114 5307 0523 2589 7098 0361 0674</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
					</NamesGroup>
				</EntityNames>
				<EntityNames id="Name2">
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<EntityNameValue>
						<EntityName>Dalian Global Unity Shipping Agency</EntityName>
					</EntityNameValue>
					<NamesGroup>
						<EntityNames id="CCC_OriginalScriptName3">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName OSNLanguageCode="zhcn" ScriptLanguageCode="zh">大连宁联船务代理</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
						<EntityNames id="CCC_Simplified2">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName>1129 6647 1337 5114 5307 0523 0108 3810</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
					</NamesGroup>
					<NamesGroup>
						<EntityNames id="CCC_OriginalScriptName4">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName OSNLanguageCode="zhtw" ScriptLanguageCode="zh">大連宁聯船務代理</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
						<EntityNames id="CCC_Traditional2">
							<ReferenceGroup ReferenceGroupCode="OFACSD"/>
							<EntityNameValue>
								<OriginalScriptName>1129 6647 1337 5114 5307 0523 0108 3810</OriginalScriptName>
							</EntityNameValue>
						</EntityNames>
					</NamesGroup>
				</EntityNames>
				<EntityNames id="OriginalScriptName1">
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<EntityNameValue>
						<EntityName>大连宁联船务有限公司</EntityName>
					</EntityNameValue>
				</EntityNames>
			</EntityNameDetails>
			<Descriptions>
				<Description Description1="4" Description2="8"/>
				<Description Description1="4" Description2="15"/>
			</Descriptions>
			<SanctionsReferences>
				<Reference>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<ReferenceValue SinceDay="29" SinceMonth="Jun" SinceYear="2017">556</ReferenceValue>
				</Reference>
			</SanctionsReferences>
			<AddressDetails>
				<Address>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<AddressValue>
						<AddressCity>Chongjin</AddressCity>
						<AddressCountry>157</AddressCountry>
					</AddressValue>
				</Address>
				<Address>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<AddressValue>
						<AddressCity>Dalian</AddressCity>
						<AddressCountry>45</AddressCountry>
					</AddressValue>
				</Address>
				<Address>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<AddressValue>
						<AddressCity>Hungnam</AddressCity>
						<AddressCountry>157</AddressCountry>
					</AddressValue>
				</Address>
				<Address>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<AddressValue>
						<AddressCity>Najin</AddressCity>
						<AddressCountry>157</AddressCountry>
					</AddressValue>
				</Address>
				<Address>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<AddressValue>
						<AddressCity>Pyongyang</AddressCity>
						<AddressCountry>157</AddressCountry>
					</AddressValue>
				</Address>
			</AddressDetails>
			<CountryDetails>
				<CountryType code="3">
					<CountryTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<CountryValue code="45"/>
					</CountryTypeValue>
					<CountryTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<CountryValue code="157"/>
					</CountryTypeValue>
				</CountryType>
			</CountryDetails>
			<IdentificationDetails>
				<IDType code="9">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>DPRK3</IDValue>
					</IDTypeValue>
				</IDType>
				<IDType code="10">
					<IDTypeValue>
						<ReferenceGroup ReferenceGroupCode="OFACSD"/>
						<IDValue>22624</IDValue>
					</IDTypeValue>
				</IDType>
			</IdentificationDetails>
			<SourceDetails>
				<Source>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<SourceValue name="https://www.treasury.gov/press-center/press-releases/Pages/sm0118.aspx"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<SourceValue name="https://www.treasury.gov/ofac/downloads/sdnlist.pdf"/>
				</Source>
				<Source>
					<ReferenceGroup ReferenceGroupCode="OFACSD"/>
					<SourceValue name="https://www.treasury.gov/resource-center/sanctions/OFAC-Enforcement/Pages/20170629.aspx"/>
				</Source>
			</SourceDetails>
		</Entity>
	</Records>
</DowJonesSanctionAlert>