<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!--Generated by SWIFTStandards Workstation (build:R6.1.0.2) on 2009 Jan 08 17:30:53-->
<xs:schema xmlns="urn:iso:std:iso:20022:tech:xsd:pacs.008.001.02" xmlns:xs="http://www.w3.org/2001/XMLSchema"
           elementFormDefault="qualified" targetNamespace="urn:iso:std:iso:20022:tech:xsd:pacs.008.001.02">
    <xs:element name="Document" type="Document"/>
    <xs:complexType name="AccountIdentification4Choice">
        <xs:sequence>
            <xs:choice>
                <xs:element name="IBAN" type="IBAN2007Identifier"/>
                <xs:element name="Othr" type="GenericAccountIdentification1"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="AccountSchemeName1Choice">
        <xs:sequence>
            <xs:choice>
                <xs:element name="Cd" type="ExternalAccountIdentification1Code"/>
                <xs:element name="Prtry" type="Max35Text"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="ActiveCurrencyAndAmount_SimpleType">
        <xs:restriction base="xs:decimal">
            <xs:minInclusive value="0"/>
            <xs:fractionDigits value="5"/>
            <xs:totalDigits value="18"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="ActiveCurrencyAndAmount">
        <xs:simpleContent>
            <xs:extension base="ActiveCurrencyAndAmount_SimpleType">
                <xs:attribute name="Ccy" type="ActiveCurrencyCode" use="required"/>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:simpleType name="ActiveCurrencyCode">
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z]{3,3}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ActiveOrHistoricCurrencyAndAmount_SimpleType">
        <xs:restriction base="xs:decimal">
            <xs:minInclusive value="0"/>
            <xs:fractionDigits value="5"/>
            <xs:totalDigits value="18"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="ActiveOrHistoricCurrencyAndAmount">
        <xs:simpleContent>
            <xs:extension base="ActiveOrHistoricCurrencyAndAmount_SimpleType">
                <xs:attribute name="Ccy" type="ActiveOrHistoricCurrencyCode" use="required"/>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>
    <xs:simpleType name="ActiveOrHistoricCurrencyCode">
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z]{3,3}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="AddressType2Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="ADDR"/>
            <xs:enumeration value="PBOX"/>
            <xs:enumeration value="HOME"/>
            <xs:enumeration value="BIZZ"/>
            <xs:enumeration value="MLTO"/>
            <xs:enumeration value="DLVY"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="AnyBICIdentifier">
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z]{6,6}[A-Z2-9][A-NP-Z0-9]([A-Z0-9]{3,3}){0,1}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="BICIdentifier">
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z]{6,6}[A-Z2-9][A-NP-Z0-9]([A-Z0-9]{3,3}){0,1}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="BaseOneRate">
        <xs:restriction base="xs:decimal">
            <xs:fractionDigits value="10"/>
            <xs:totalDigits value="11"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="BatchBookingIndicator">
        <xs:restriction base="xs:boolean"/>
    </xs:simpleType>
    <xs:complexType name="BranchAndFinancialInstitutionIdentification4">
        <xs:sequence>
            <xs:element name="FinInstnId" type="FinancialInstitutionIdentification7"/>
            <xs:element maxOccurs="1" minOccurs="0" name="BrnchId" type="BranchData2"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="BranchData2">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Id" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Nm" type="Max140Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PstlAdr" type="PostalAddress6"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CashAccount16">
        <xs:sequence>
            <xs:element name="Id" type="AccountIdentification4Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="CashAccountType2"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Ccy" type="ActiveOrHistoricCurrencyCode"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Nm" type="Max70Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CashAccountType2">
        <xs:sequence>
            <xs:choice>
                <xs:element name="Cd" type="CashAccountType4Code"/>
                <xs:element name="Prtry" type="Max35Text"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="CashAccountType4Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="CASH"/>
            <xs:enumeration value="CHAR"/>
            <xs:enumeration value="COMM"/>
            <xs:enumeration value="TAXE"/>
            <xs:enumeration value="CISH"/>
            <xs:enumeration value="TRAS"/>
            <xs:enumeration value="SACC"/>
            <xs:enumeration value="CACC"/>
            <xs:enumeration value="SVGS"/>
            <xs:enumeration value="ONDP"/>
            <xs:enumeration value="MGLD"/>
            <xs:enumeration value="NREX"/>
            <xs:enumeration value="MOMA"/>
            <xs:enumeration value="LOAN"/>
            <xs:enumeration value="SLRY"/>
            <xs:enumeration value="ODFT"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="CategoryPurpose1Choice">
        <xs:sequence>
            <xs:choice>
                <xs:element name="Cd" type="ExternalCategoryPurpose1Code"/>
                <xs:element name="Prtry" type="Max35Text"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="ChargeBearerType1Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="DEBT"/>
            <xs:enumeration value="CRED"/>
            <xs:enumeration value="SHAR"/>
            <xs:enumeration value="SLEV"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="ChargesInformation5">
        <xs:sequence>
            <xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element name="Pty" type="BranchAndFinancialInstitutionIdentification4"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="ClearingChannel2Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="RTGS"/>
            <xs:enumeration value="RTNS"/>
            <xs:enumeration value="MPNS"/>
            <xs:enumeration value="BOOK"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="ClearingSystemIdentification2Choice">
        <xs:sequence>
            <xs:choice>
                <xs:element name="Cd" type="ExternalClearingSystemIdentification1Code"/>
                <xs:element name="Prtry" type="Max35Text"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ClearingSystemIdentification3Choice">
        <xs:sequence>
            <xs:choice>
                <xs:element name="Cd" type="ExternalCashClearingSystem1Code"/>
                <xs:element name="Prtry" type="Max35Text"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ClearingSystemMemberIdentification2">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="ClrSysId" type="ClearingSystemIdentification2Choice"/>
            <xs:element name="MmbId" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ContactDetails2">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="NmPrfx" type="NamePrefix1Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Nm" type="Max140Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PhneNb" type="PhoneNumber"/>
            <xs:element maxOccurs="1" minOccurs="0" name="MobNb" type="PhoneNumber"/>
            <xs:element maxOccurs="1" minOccurs="0" name="FaxNb" type="PhoneNumber"/>
            <xs:element maxOccurs="1" minOccurs="0" name="EmailAdr" type="Max2048Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Othr" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="CountryCode">
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z]{2,2}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="CreditDebitCode">
        <xs:restriction base="xs:string">
            <xs:enumeration value="CRDT"/>
            <xs:enumeration value="DBIT"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="CreditTransferTransactionInformation11">
        <xs:sequence>
            <xs:element name="PmtId" type="PaymentIdentification3"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PmtTpInf" type="PaymentTypeInformation21"/>
            <xs:element name="IntrBkSttlmAmt" type="ActiveCurrencyAndAmount"/>
            <xs:element maxOccurs="1" minOccurs="0" name="IntrBkSttlmDt" type="ISODate"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SttlmPrty" type="Priority3Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SttlmTmIndctn" type="SettlementDateTimeIndication1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SttlmTmReq" type="SettlementTimeRequest2"/>
            <xs:element maxOccurs="1" minOccurs="0" name="AccptncDtTm" type="ISODateTime"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PoolgAdjstmntDt" type="ISODate"/>
            <xs:element maxOccurs="1" minOccurs="0" name="InstdAmt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element maxOccurs="1" minOccurs="0" name="XchgRate" type="BaseOneRate"/>
            <xs:element name="ChrgBr" type="ChargeBearerType1Code"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="ChrgsInf" type="ChargesInformation5"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PrvsInstgAgt"
                        type="BranchAndFinancialInstitutionIdentification4"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PrvsInstgAgtAcct" type="CashAccount16"/>
            <xs:element maxOccurs="1" minOccurs="0" name="InstgAgt"
                        type="BranchAndFinancialInstitutionIdentification4"/>
            <xs:element maxOccurs="1" minOccurs="0" name="InstdAgt"
                        type="BranchAndFinancialInstitutionIdentification4"/>
            <xs:element maxOccurs="1" minOccurs="0" name="IntrmyAgt1"
                        type="BranchAndFinancialInstitutionIdentification4"/>
            <xs:element maxOccurs="1" minOccurs="0" name="IntrmyAgt1Acct" type="CashAccount16"/>
            <xs:element maxOccurs="1" minOccurs="0" name="IntrmyAgt2"
                        type="BranchAndFinancialInstitutionIdentification4"/>
            <xs:element maxOccurs="1" minOccurs="0" name="IntrmyAgt2Acct" type="CashAccount16"/>
            <xs:element maxOccurs="1" minOccurs="0" name="IntrmyAgt3"
                        type="BranchAndFinancialInstitutionIdentification4"/>
            <xs:element maxOccurs="1" minOccurs="0" name="IntrmyAgt3Acct" type="CashAccount16"/>
            <xs:element maxOccurs="1" minOccurs="0" name="UltmtDbtr" type="PartyIdentification32"/>
            <xs:element maxOccurs="1" minOccurs="0" name="InitgPty" type="PartyIdentification32"/>
            <xs:element name="Dbtr" type="PartyIdentification32"/>
            <xs:element maxOccurs="1" minOccurs="0" name="DbtrAcct" type="CashAccount16"/>
            <xs:element name="DbtrAgt" type="BranchAndFinancialInstitutionIdentification4"/>
            <xs:element maxOccurs="1" minOccurs="0" name="DbtrAgtAcct" type="CashAccount16"/>
            <xs:element name="CdtrAgt" type="BranchAndFinancialInstitutionIdentification4"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtrAgtAcct" type="CashAccount16"/>
            <xs:element name="Cdtr" type="PartyIdentification32"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtrAcct" type="CashAccount16"/>
            <xs:element maxOccurs="1" minOccurs="0" name="UltmtCdtr" type="PartyIdentification32"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="InstrForCdtrAgt" type="InstructionForCreditorAgent1"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="InstrForNxtAgt" type="InstructionForNextAgent1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Purp" type="Purpose2Choice"/>
            <xs:element maxOccurs="10" minOccurs="0" name="RgltryRptg" type="RegulatoryReporting3"/>
            <xs:element maxOccurs="10" minOccurs="0" name="RltdRmtInf" type="RemittanceLocation2"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RmtInf" type="RemittanceInformation5"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CreditorReferenceInformation2">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="CreditorReferenceType2"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Ref" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CreditorReferenceType1Choice">
        <xs:sequence>
            <xs:choice>
                <xs:element name="Cd" type="DocumentType3Code"/>
                <xs:element name="Prtry" type="Max35Text"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="CreditorReferenceType2">
        <xs:sequence>
            <xs:element name="CdOrPrtry" type="CreditorReferenceType1Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="DateAndPlaceOfBirth">
        <xs:sequence>
            <xs:element name="BirthDt" type="ISODate"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PrvcOfBirth" type="Max35Text"/>
            <xs:element name="CityOfBirth" type="Max35Text"/>
            <xs:element name="CtryOfBirth" type="CountryCode"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="DecimalNumber">
        <xs:restriction base="xs:decimal">
            <xs:fractionDigits value="17"/>
            <xs:totalDigits value="18"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="Document">
        <xs:sequence>
            <xs:element name="FIToFICstmrCdtTrf" type="FIToFICustomerCreditTransferV02"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="DocumentAdjustment1">
        <xs:sequence>
            <xs:element name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtDbtInd" type="CreditDebitCode"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Rsn" type="Max4Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="AddtlInf" type="Max140Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="DocumentType3Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="RADM"/>
            <xs:enumeration value="RPIN"/>
            <xs:enumeration value="FXDR"/>
            <xs:enumeration value="DISP"/>
            <xs:enumeration value="PUOR"/>
            <xs:enumeration value="SCOR"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="DocumentType5Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="MSIN"/>
            <xs:enumeration value="CNFA"/>
            <xs:enumeration value="DNFA"/>
            <xs:enumeration value="CINV"/>
            <xs:enumeration value="CREN"/>
            <xs:enumeration value="DEBN"/>
            <xs:enumeration value="HIRI"/>
            <xs:enumeration value="SBIN"/>
            <xs:enumeration value="CMCN"/>
            <xs:enumeration value="SOAC"/>
            <xs:enumeration value="DISP"/>
            <xs:enumeration value="BOLD"/>
            <xs:enumeration value="VCHR"/>
            <xs:enumeration value="AROI"/>
            <xs:enumeration value="TSUT"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalAccountIdentification1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalCashClearingSystem1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="3"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalCategoryPurpose1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalClearingSystemIdentification1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="5"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalFinancialInstitutionIdentification1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalLocalInstrument1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="35"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalOrganisationIdentification1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalPersonIdentification1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalPurpose1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ExternalServiceLevel1Code">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="FIToFICustomerCreditTransferV02">
        <xs:sequence>
            <xs:element name="GrpHdr" type="GroupHeader33"/>
            <xs:element maxOccurs="unbounded" minOccurs="1" name="CdtTrfTxInf"
                        type="CreditTransferTransactionInformation11"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="FinancialIdentificationSchemeName1Choice">
        <xs:sequence>
            <xs:choice>
                <xs:element name="Cd" type="ExternalFinancialInstitutionIdentification1Code"/>
                <xs:element name="Prtry" type="Max35Text"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="FinancialInstitutionIdentification7">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="BIC" type="BICIdentifier"/>
            <xs:element maxOccurs="1" minOccurs="0" name="ClrSysMmbId" type="ClearingSystemMemberIdentification2"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Nm" type="Max140Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PstlAdr" type="PostalAddress6"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Othr" type="GenericFinancialIdentification1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GenericAccountIdentification1">
        <xs:sequence>
            <xs:element name="Id" type="Max34Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SchmeNm" type="AccountSchemeName1Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GenericFinancialIdentification1">
        <xs:sequence>
            <xs:element name="Id" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SchmeNm" type="FinancialIdentificationSchemeName1Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GenericOrganisationIdentification1">
        <xs:sequence>
            <xs:element name="Id" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SchmeNm" type="OrganisationIdentificationSchemeName1Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GenericPersonIdentification1">
        <xs:sequence>
            <xs:element name="Id" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SchmeNm" type="PersonIdentificationSchemeName1Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="GroupHeader33">
        <xs:sequence>
            <xs:element name="MsgId" type="Max35Text"/>
            <xs:element name="CreDtTm" type="ISODateTime"/>
            <xs:element maxOccurs="1" minOccurs="0" name="BtchBookg" type="BatchBookingIndicator"/>
            <xs:element name="NbOfTxs" type="Max15NumericText"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CtrlSum" type="DecimalNumber"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TtlIntrBkSttlmAmt" type="ActiveCurrencyAndAmount"/>
            <xs:element maxOccurs="1" minOccurs="0" name="IntrBkSttlmDt" type="ISODate"/>
            <xs:element name="SttlmInf" type="SettlementInformation13"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PmtTpInf" type="PaymentTypeInformation21"/>
            <xs:element maxOccurs="1" minOccurs="0" name="InstgAgt"
                        type="BranchAndFinancialInstitutionIdentification4"/>
            <xs:element maxOccurs="1" minOccurs="0" name="InstdAgt"
                        type="BranchAndFinancialInstitutionIdentification4"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="IBAN2007Identifier">
        <xs:restriction base="xs:string">
            <xs:pattern value="[A-Z]{2,2}[0-9]{2,2}[a-zA-Z0-9]{1,30}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="ISODate">
        <xs:restriction base="xs:date"/>
    </xs:simpleType>
    <xs:simpleType name="ISODateTime">
        <xs:restriction base="xs:dateTime"/>
    </xs:simpleType>
    <xs:simpleType name="ISOTime">
        <xs:restriction base="xs:time"/>
    </xs:simpleType>
    <xs:simpleType name="Instruction3Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="CHQB"/>
            <xs:enumeration value="HOLD"/>
            <xs:enumeration value="PHOB"/>
            <xs:enumeration value="TELB"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Instruction4Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="PHOA"/>
            <xs:enumeration value="TELA"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="InstructionForCreditorAgent1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Cd" type="Instruction3Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="InstrInf" type="Max140Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="InstructionForNextAgent1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Cd" type="Instruction4Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="InstrInf" type="Max140Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="LocalInstrument2Choice">
        <xs:sequence>
            <xs:choice>
                <xs:element name="Cd" type="ExternalLocalInstrument1Code"/>
                <xs:element name="Prtry" type="Max35Text"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="Max10Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="10"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max140Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="140"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max15NumericText">
        <xs:restriction base="xs:string">
            <xs:pattern value="[0-9]{1,15}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max16Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="16"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max2048Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="2048"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max34Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="34"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max35Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="35"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max4Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="4"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Max70Text">
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <xs:maxLength value="70"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="NameAndAddress10">
        <xs:sequence>
            <xs:element name="Nm" type="Max140Text"/>
            <xs:element name="Adr" type="PostalAddress6"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="NamePrefix1Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="DOCT"/>
            <xs:enumeration value="MIST"/>
            <xs:enumeration value="MISS"/>
            <xs:enumeration value="MADM"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="OrganisationIdentification4">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="BICOrBEI" type="AnyBICIdentifier"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Othr" type="GenericOrganisationIdentification1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="OrganisationIdentificationSchemeName1Choice">
        <xs:sequence>
            <xs:choice>
                <xs:element name="Cd" type="ExternalOrganisationIdentification1Code"/>
                <xs:element name="Prtry" type="Max35Text"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="Party6Choice">
        <xs:sequence>
            <xs:choice>
                <xs:element name="OrgId" type="OrganisationIdentification4"/>
                <xs:element name="PrvtId" type="PersonIdentification5"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PartyIdentification32">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Nm" type="Max140Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PstlAdr" type="PostalAddress6"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Id" type="Party6Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CtryOfRes" type="CountryCode"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CtctDtls" type="ContactDetails2"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PaymentIdentification3">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="InstrId" type="Max35Text"/>
            <xs:element name="EndToEndId" type="Max35Text"/>
            <xs:element name="TxId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="ClrSysRef" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PaymentTypeInformation21">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="InstrPrty" type="Priority2Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="ClrChanl" type="ClearingChannel2Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SvcLvl" type="ServiceLevel8Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="LclInstrm" type="LocalInstrument2Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CtgyPurp" type="CategoryPurpose1Choice"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PersonIdentification5">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="DtAndPlcOfBirth" type="DateAndPlaceOfBirth"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Othr" type="GenericPersonIdentification1"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="PersonIdentificationSchemeName1Choice">
        <xs:sequence>
            <xs:choice>
                <xs:element name="Cd" type="ExternalPersonIdentification1Code"/>
                <xs:element name="Prtry" type="Max35Text"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="PhoneNumber">
        <xs:restriction base="xs:string">
            <xs:pattern value="\+[0-9]{1,3}-[0-9()+\-]{1,30}"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="PostalAddress6">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="AdrTp" type="AddressType2Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Dept" type="Max70Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SubDept" type="Max70Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="StrtNm" type="Max70Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="BldgNb" type="Max16Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="PstCd" type="Max16Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TwnNm" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CtrySubDvsn" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Ctry" type="CountryCode"/>
            <xs:element maxOccurs="7" minOccurs="0" name="AdrLine" type="Max70Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="Priority2Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="HIGH"/>
            <xs:enumeration value="NORM"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="Priority3Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="URGT"/>
            <xs:enumeration value="HIGH"/>
            <xs:enumeration value="NORM"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="Purpose2Choice">
        <xs:sequence>
            <xs:choice>
                <xs:element name="Cd" type="ExternalPurpose1Code"/>
                <xs:element name="Prtry" type="Max35Text"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ReferredDocumentInformation3">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="ReferredDocumentType2"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Nb" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RltdDt" type="ISODate"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ReferredDocumentType1Choice">
        <xs:sequence>
            <xs:choice>
                <xs:element name="Cd" type="DocumentType5Code"/>
                <xs:element name="Prtry" type="Max35Text"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="ReferredDocumentType2">
        <xs:sequence>
            <xs:element name="CdOrPrtry" type="ReferredDocumentType1Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Issr" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="RegulatoryAuthority2">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Nm" type="Max140Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Ctry" type="CountryCode"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="RegulatoryReporting3">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="DbtCdtRptgInd" type="RegulatoryReportingType1Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Authrty" type="RegulatoryAuthority2"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Dtls" type="StructuredRegulatoryReporting3"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="RegulatoryReportingType1Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="CRED"/>
            <xs:enumeration value="DEBT"/>
            <xs:enumeration value="BOTH"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="RemittanceAmount1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="DuePyblAmt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element maxOccurs="1" minOccurs="0" name="DscntApldAmt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtNoteAmt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TaxAmt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="AdjstmntAmtAndRsn" type="DocumentAdjustment1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RmtdAmt" type="ActiveOrHistoricCurrencyAndAmount"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="RemittanceInformation5">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Ustrd" type="Max140Text"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Strd" type="StructuredRemittanceInformation7"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="RemittanceLocation2">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="RmtId" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RmtLctnMtd" type="RemittanceLocationMethod2Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RmtLctnElctrncAdr" type="Max2048Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RmtLctnPstlAdr" type="NameAndAddress10"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="RemittanceLocationMethod2Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="FAXI"/>
            <xs:enumeration value="EDIC"/>
            <xs:enumeration value="URID"/>
            <xs:enumeration value="EMAL"/>
            <xs:enumeration value="POST"/>
            <xs:enumeration value="SMSM"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="ServiceLevel8Choice">
        <xs:sequence>
            <xs:choice>
                <xs:element name="Cd" type="ExternalServiceLevel1Code"/>
                <xs:element name="Prtry" type="Max35Text"/>
            </xs:choice>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="SettlementDateTimeIndication1">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="DbtDtTm" type="ISODateTime"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtDtTm" type="ISODateTime"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="SettlementInformation13">
        <xs:sequence>
            <xs:element name="SttlmMtd" type="SettlementMethod1Code"/>
            <xs:element maxOccurs="1" minOccurs="0" name="SttlmAcct" type="CashAccount16"/>
            <xs:element maxOccurs="1" minOccurs="0" name="ClrSys" type="ClearingSystemIdentification3Choice"/>
            <xs:element maxOccurs="1" minOccurs="0" name="InstgRmbrsmntAgt"
                        type="BranchAndFinancialInstitutionIdentification4"/>
            <xs:element maxOccurs="1" minOccurs="0" name="InstgRmbrsmntAgtAcct" type="CashAccount16"/>
            <xs:element maxOccurs="1" minOccurs="0" name="InstdRmbrsmntAgt"
                        type="BranchAndFinancialInstitutionIdentification4"/>
            <xs:element maxOccurs="1" minOccurs="0" name="InstdRmbrsmntAgtAcct" type="CashAccount16"/>
            <xs:element maxOccurs="1" minOccurs="0" name="ThrdRmbrsmntAgt"
                        type="BranchAndFinancialInstitutionIdentification4"/>
            <xs:element maxOccurs="1" minOccurs="0" name="ThrdRmbrsmntAgtAcct" type="CashAccount16"/>
        </xs:sequence>
    </xs:complexType>
    <xs:simpleType name="SettlementMethod1Code">
        <xs:restriction base="xs:string">
            <xs:enumeration value="INDA"/>
            <xs:enumeration value="INGA"/>
            <xs:enumeration value="COVE"/>
            <xs:enumeration value="CLRG"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:complexType name="SettlementTimeRequest2">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="CLSTm" type="ISOTime"/>
            <xs:element maxOccurs="1" minOccurs="0" name="TillTm" type="ISOTime"/>
            <xs:element maxOccurs="1" minOccurs="0" name="FrTm" type="ISOTime"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RjctTm" type="ISOTime"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="StructuredRegulatoryReporting3">
        <xs:sequence>
            <xs:element maxOccurs="1" minOccurs="0" name="Tp" type="Max35Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Dt" type="ISODate"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Ctry" type="CountryCode"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Cd" type="Max10Text"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Amt" type="ActiveOrHistoricCurrencyAndAmount"/>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="Inf" type="Max35Text"/>
        </xs:sequence>
    </xs:complexType>
    <xs:complexType name="StructuredRemittanceInformation7">
        <xs:sequence>
            <xs:element maxOccurs="unbounded" minOccurs="0" name="RfrdDocInf" type="ReferredDocumentInformation3"/>
            <xs:element maxOccurs="1" minOccurs="0" name="RfrdDocAmt" type="RemittanceAmount1"/>
            <xs:element maxOccurs="1" minOccurs="0" name="CdtrRefInf" type="CreditorReferenceInformation2"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Invcr" type="PartyIdentification32"/>
            <xs:element maxOccurs="1" minOccurs="0" name="Invcee" type="PartyIdentification32"/>
            <xs:element maxOccurs="3" minOccurs="0" name="AddtlRmtInf" type="Max140Text"/>
        </xs:sequence>
    </xs:complexType>
</xs:schema>
