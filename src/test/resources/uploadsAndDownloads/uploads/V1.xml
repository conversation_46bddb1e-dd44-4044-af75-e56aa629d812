<v1:GetCustomerSafewatchScreeningRq xmlns:v1="http://CBoJ/Service/DigitalOnboarding/GetCustomerSafewatchScreening/v2.0">
    <Header>
        <TransactionId>20221010135508342664</TransactionId>
        <BankId>CBOJ</BankId>
        <BranchCode>JO0010002</BranchCode>
        <ChannelId>ESB</ChannelId>
        <SubChannelId>DOSUBCHL</SubChannelId>
        <SessionId>3</SessionId>
        <RequestTime>2022-10-10 13:55:08.342</RequestTime>
        <MWRequestTime>2022-10-10 13:55:08.342</MWRequestTime>
        <ServiceCode>CRSW001</ServiceCode>
        <DebugFlag>0</DebugFlag>
        <AuthorizeLevel>0</AuthorizeLevel>
        <ChannelUserInfo>
            <UserName>DIGITALONBOARDING</UserName>
            <UserPassword>n/liiVI512RJSF99Iub39g==</UserPassword>
        </ChannelUserInfo>
        <UDF>
            <KeyValueArr>
                <key>MsgId</key>
                <value>3030304d5700000000000000000020221010135508342664</value>
            </KeyValueArr>
        </UDF>
    </Header>
    <Body>
        <ReqUniqueID>1234</ReqUniqueID>
        <ArabicFullName>Heartbeat</ArabicFullName>
        <CIF>1</CIF>
        <BranchName>Heartbeat</BranchName>
        <ProcessType>1</ProcessType>
        <FullName>Dawoud Damra</FullName>
        <BirthOfDate>19940829</BirthOfDate>
        <Nationality>JOR</Nationality>
        <NationalID>1</NationalID>
        <PassportID>1</PassportID>
        <MotherName>1</MotherName>
        <Gender>Male</Gender>
    </Body>
</v1:GetCustomerSafewatchScreeningRq>