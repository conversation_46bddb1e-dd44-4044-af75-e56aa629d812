<RequestPayload ESMIGMessageId="tgw106.9757229b-6bef-41fc-9029-a2f62eb4cf85" SwCompression="None">
    <AppHdr xmlns="urn:iso:std:iso:20022:tech:xsd:head.001.001.01">
        <Fr>
            <FIId>
                <FinInstnId>
                    <BICFI>CBVILT2XXXX</BICFI>
                </FinInstnId>
            </FIId>
        </Fr>
        <To>
            <FIId>
                <FinInstnId>
                    <BICFI>CBVILT2XXXX</BICFI>
                </FinInstnId>
            </FIId>
        </To>
        <BizMsgIdr>215264410</BizMsgIdr>
        <MsgDefIdr>pacs.008.001.08</MsgDefIdr>
        <CreDt>2023-11-28T06:37:27.603Z</CreDt>
        <Prty>NORM</Prty>
    </AppHdr>
    <Document xmlns="urn:iso:std:iso:20022:tech:xsd:pacs.008.001.08">
        <FIToFICstmrCdtTrf>
            <GrpHdr>
                <MsgId>NONREF</MsgId>
                <CreDtTm>2023-11-28T06:37:27.603+00:00</CreDtTm>
                <NbOfTxs>1</NbOfTxs>
                <SttlmInf>
                    <SttlmMtd>CLRG</SttlmMtd>
                    <ClrSys>
                        <Cd>TGT</Cd>
                    </ClrSys>
                </SttlmInf>
            </GrpHdr>
            <CdtTrfTxInf>
                <PmtId>
                    <InstrId>TC3605</InstrId>
                    <EndToEndId>NOTPROVIDED</EndToEndId>
                    <UETR>dd178fda-ef31-4035-90d4-211033bb25fd</UETR>
                    <ClrSysRef>75298384</ClrSysRef>
                </PmtId>
                <PmtTpInf>
                    <InstrPrty>NORM</InstrPrty>
                    <SvcLvl>
                        <Cd>G001</Cd>
                    </SvcLvl>
                </PmtTpInf>
                <IntrBkSttlmAmt Ccy="EUR">5280.00</IntrBkSttlmAmt>
                <IntrBkSttlmDt>2023-11-28</IntrBkSttlmDt>
                <SttlmTmIndctn>
                    <CdtDtTm>2023-11-28T06:37:27.452+00:00</CdtDtTm>
                </SttlmTmIndctn>
                <InstdAmt Ccy="EUR">5280.00</InstdAmt>
                <ChrgBr>DEBT</ChrgBr>
                <InstgAgt>
                    <FinInstnId>
                        <BICFI>ARMCAM22XXX</BICFI>
                    </FinInstnId>
                </InstgAgt>
                <InstdAgt>
                    <FinInstnId>
                        <BICFI>CBVILT2XXXX</BICFI>
                    </FinInstnId>
                </InstdAgt>
                <Dbtr>
                    <Nm>Test Payer</Nm>
                    <PstlAdr>
                        <AdrLine>Samarkand jordan</AdrLine>
                    </PstlAdr>
                </Dbtr>
                <DbtrAcct>
                    <Id>
                        <Othr>
                            <Id>11111111111111111111</Id>
                        </Othr>
                    </Id>
                </DbtrAcct>
                <DbtrAgt>
                    <FinInstnId>
                        <BICFI>ARMCAM22XXX</BICFI>
                    </FinInstnId>
                </DbtrAgt>
                <CdtrAgt>
                    <FinInstnId>
                        <BICFI>CBVILT2XXXX</BICFI>
                    </FinInstnId>
                </CdtrAgt>
                <Cdtr>
                    <Nm>Osama bin laden</Nm>
                    <PstlAdr>
                        <AdrLine>LITHUANIA Traku G 1</AdrLine>
                    </PstlAdr>
                </Cdtr>
                <CdtrAcct>
                    <Id>
                        <IBAN>********************</IBAN>
                    </Id>
                </CdtrAcct>
                <RmtInf>
                    <Ustrd>TC36 Alert</Ustrd>
                </RmtInf>
            </CdtTrfTxInf>
        </FIToFICstmrCdtTrf>
    </Document>
</RequestPayload>