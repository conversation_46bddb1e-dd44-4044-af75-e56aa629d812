@echo off
IF NOT DEFINED EASTNETS_SWFNG_HOME (
    ECHO "Please set EASTNETS_SWFNG_HOME variable correctly"
    EXIT /B)
SET LOG_DIR=%EASTNETS_SWFNG_HOME%\logs\services
MD %LOG_DIR%
echo Starting SWFNG services
pushd services
start /MIN powershell -NoExit -Command "& {$host.ui.RawUI.WindowTitle = 'srvc-scan'}; java -XX:-CompactStrings -XX:ParallelGCThreads=4 -XX:+UseParallelGC -Xmx8g -jar srvc-scan-qk-mssql-runner.jar|tee $ENV:EASTNETS_SWFNG_HOME\logs\services\scan.log"
timeout /t 15 /nobreak > NUL
popd
echo SWFNG services started
