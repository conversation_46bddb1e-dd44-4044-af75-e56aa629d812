<?xml version="1.0" encoding="UTF-8"?>
<XML>
    <LICENSE>
        <LICENSE_INFO>
            <KEY>DEF9-B972-B2EA-8416</KEY>
            <STATION_CONNECTIONS>5</STATION_CONNECTIONS>
            <SERVER_INSTANCES>1</SERVER_INSTANCES>
            <SCAN_VOLUME></SCAN_VOLUME>
            <CLIENT_ID>1009</CLIENT_ID>
            <CLIENT_NAME>EastNets Dubai</CLIENT_NAME>
            <PRODUCT_EDITION>Enterprise</PRODUCT_EDITION>
            <UNICODE_DATABASE>No</UNICODE_DATABASE>
            <GRACE_PERIOD>60</GRACE_PERIOD>
            <LICENSED_BICS>
                <BIC>BNPAFRPPXXX</BIC>
                <BIC>BFICCUHHXXX</BIC>
                <BIC>BARCGB22XXX</BIC>
            </LICENSED_BICS>
        </LICENSE_INFO>
        <APPLICATION_LIST>
            <APPLICATION>
                <NAME>File Scanner</NAME>
                <LICENSE_STATUS>Yes</LICENSE_STATUS>
                <EXPIRATION_DATE>2022/05/18</EXPIRATION_DATE>
                <SCAN_QUOTA>5</SCAN_QUOTA>
                <PERIOD_OF_TIME></PERIOD_OF_TIME>
                <NB_OF_WARNINGS></NB_OF_WARNINGS>
            </APPLICATION>
            <APPLICATION>
                <NAME>MQ Connector</NAME>
                <LICENSE_STATUS>Yes</LICENSE_STATUS>
                <EXPIRATION_DATE>2022/05/18</EXPIRATION_DATE>
                <SCAN_QUOTA>9999999</SCAN_QUOTA>
                <PERIOD_OF_TIME></PERIOD_OF_TIME>
                <NB_OF_WARNINGS></NB_OF_WARNINGS>
            </APPLICATION>
            <APPLICATION>
                <NAME>Name Checker</NAME>
                <LICENSE_STATUS>Yes</LICENSE_STATUS>
                <EXPIRATION_DATE>2022/05/18</EXPIRATION_DATE>
                <SCAN_QUOTA>5</SCAN_QUOTA>
                <PERIOD_OF_TIME></PERIOD_OF_TIME>
                <NB_OF_WARNINGS></NB_OF_WARNINGS>
            </APPLICATION>
            <APPLICATION>
                <NAME>SAA OFCA Station</NAME>
                <LICENSE_STATUS>Yes</LICENSE_STATUS>
                <EXPIRATION_DATE>2022/05/18</EXPIRATION_DATE>
                <SCAN_QUOTA>5</SCAN_QUOTA>
                <PERIOD_OF_TIME></PERIOD_OF_TIME>
                <NB_OF_WARNINGS></NB_OF_WARNINGS>
            </APPLICATION>
            <APPLICATION>
                <NAME>SAA OFCS Detect</NAME>
                <LICENSE_STATUS>Yes</LICENSE_STATUS>
                <EXPIRATION_DATE>2022/05/18</EXPIRATION_DATE>
                <SCAN_QUOTA>5</SCAN_QUOTA>
                <PERIOD_OF_TIME></PERIOD_OF_TIME>
                <NB_OF_WARNINGS></NB_OF_WARNINGS>
            </APPLICATION>
            <APPLICATION>
                <NAME>SAA OFCS Monitor</NAME>
                <LICENSE_STATUS>Yes</LICENSE_STATUS>
                <EXPIRATION_DATE>2022/05/18</EXPIRATION_DATE>
                <SCAN_QUOTA></SCAN_QUOTA>
                <PERIOD_OF_TIME></PERIOD_OF_TIME>
                <NB_OF_WARNINGS></NB_OF_WARNINGS>
            </APPLICATION>
            <APPLICATION>
                <NAME>SafeWatch API</NAME>
                <LICENSE_STATUS>Yes</LICENSE_STATUS>
                <EXPIRATION_DATE>2022/05/18</EXPIRATION_DATE>
                <SCAN_QUOTA>9999999</SCAN_QUOTA>
                <PERIOD_OF_TIME></PERIOD_OF_TIME>
                <NB_OF_WARNINGS></NB_OF_WARNINGS>
            </APPLICATION>
            <APPLICATION>
                <NAME>SafeWatch Server</NAME>
                <LICENSE_STATUS>Yes</LICENSE_STATUS>
                <EXPIRATION_DATE>2022/05/18</EXPIRATION_DATE>
                <SCAN_QUOTA></SCAN_QUOTA>
                <PERIOD_OF_TIME></PERIOD_OF_TIME>
                <NB_OF_WARNINGS></NB_OF_WARNINGS>
            </APPLICATION>
        </APPLICATION_LIST>
    </LICENSE>
</XML>