#--------------------- Set Test data source type whether "Excel or DB" --------------------------------------------
#TestDataType = Excel
#--------------------- Define all classes paths that implement test data strategy --------------------------------------------
#TestDataStrategyClassPath_Excel = com.fawry.promoLoyalty.angularAutomation.strategy.testData.ExcelTestData
#TestDataStrategyClassPath_DB = com.fawry.promoLoyalty.angularAutomation.strategy.testData.OracleDBTestData
#--------------------- Define test data configurations for each test  --------------------------------------------
#For Excel --> MethodName = Excel test data file location ; sheet number
addNewEntry=/src/test/resources/testDataFiles/AddEntryTD-SmokeTest.json
addNewFormat=/src/test/resources/testDataFiles/AddFormatTD-SmokeTest.json
blockDetection=/src/test/resources/testDataFiles/FileScanTD-SmokeTest.json
checkProfilePermissions=/src/test/resources/testDataFiles/ProfilePermissionCheck.Json
checkOperatorPermissions=/src/test/resources/testDataFiles/OperatorPermissionCheck.json
checkPermissionWhenAssignSecGroupAndProfile=/src/test/resources/testDataFiles/OpAssignToSecProfileGroup.json
checkPermissionWhenAssignSecGroupProfileZone=/src/test/resources/testDataFiles/OpAssignToSecProfileGroupZone.json
changePasswordPolicy=/src/test/resources/testDataFiles/ChangePasswordPolicy.json
changePassword=/src/test/resources/testDataFiles/ChangePasswordTD.json
checkAuditManager=/src/test/resources/DataFiles/AuditManagerData.json
exportReport=/src/test/resources/testDataFiles/ExportReportsTD.json
scanFile=/src/test/resources/testDataFiles/ScanFile.json
scanFileAndAddGoodGuy=/src/test/resources/testDataFiles/ScanFileAndAddGG.json
addDBConfigurations=/src/test/resources/testDataFiles/DBManager/DBScan.json
checkAuditTrail=/src/test/resources/testDataFiles/AdvancedSettingsTD/CheckAuditTrailTD.json
checkArabicPhonetics=/src/test/resources/testDataFiles/AdvancedSettingsTD/EnablePhoneticsSettings.json
verifyScanSynonymWords=/src/test/resources/testDataFiles/AdvancedSettingsTD/verifyScanSynonymWords.json
verifyScanNeutralWords=/src/test/resources/testDataFiles/AdvancedSettingsTD/VerifyScanNeutralWordsTD.json
verifyGluedWordsSymSpell=/src/test/resources/testDataFiles/AdvancedSettingsTD/VerifyGluedWordsSymSpell.json
checkEngineSettings=/src/test/resources/testDataFiles/AdvancedSettingsTD/EngineSettingsTD.json
assignDetectionFromFileScan=/src/test/resources/testDataFiles/assignDetectionFromFileScan.json
printAlert=/src/test/resources/testDataFiles/ExportAlertReport.json
exportGG=/src/test/resources/testDataFiles/ExportGoodGuyTD.json
importGG=/src/test/resources/testDataFiles/ImportGoodGuyTD.json
exportEntry=/src/test/resources/testDataFiles/ExportEntriesTD.json
searchEntry=/src/test/resources/testDataFiles/SearchEntries.json
exportBlackList=/src/test/resources/testDataFiles/ExportBlackList.json
verifyWorldCheck=/src/test/resources/testDataFiles/WorldCheckModesTD.json
verifyListSetConfigurations=/src/test/resources/testDataFiles/EntriesTD.json
passwordPolicy=/src/test/resources/testDataFiles/PasswordPolicy.json
passwordPolicyList=/src/test/resources/testDataFiles/PasswordPolicyListTD.json
createNewFormat=/src/test/resources/testDataFiles/CreateFormatTD-RegressionTest.json
repeatDifferentSubTypes=/src/test/resources/testDataFiles/RepeatDifferentSubTypesTD.json
createFourEyesConfig=/src/test/resources/testDataFiles/AdvancedSettingsTD/FourEyesTD.json
businessLogicAppearanceSettings=/src/test/resources/testDataFiles/AdvancedSettingsTD/BusinessLogic.json
engineTuningsRegressionSettings = /src/test/resources/testDataFiles/AdvancedSettingsTD/EngineTuningsRegressionSettingsTD.json
engineTuningsTD = /src/test/resources/testDataFiles/AdvancedSettingsTD/EngineTuningsModuleTD.json
generalSettings = /src/test/resources/testDataFiles/AdvancedSettingsTD/GeneralSettings.json
symSpellWordSizeSettings=/src/test/resources/testDataFiles/91207/SysSpellWordSize.json
swMatchFields_Format = /src/test/resources/testDataFiles/SetDetectSwift-EngineSettingsTD.json
violationList_Assertions = /src/test/resources/testDataFiles/ViolationList-Assertions.json
swMatchFields_IOS = /src/test/resources/testDataFiles/Violations.json
performDontKnowAction_FileScan = /src/test/resources/testDataFiles/FileScan-DontKnowTD.json

#------Test data for DJ creation----------------------
loginSampleFile=/src/test/resources/testDataFiles/loginFilesTD/LoginSample.cmd
loginNewFile=/src/test/resources/testDataFiles/loginFilesTD/LoginFileAutomation.cmd
DJSampleFile=/src/test/resources/testDataFiles/DowJonesCreationTD/DJSample.bat
DJNewFile=/src/test/resources/testDataFiles/DowJonesCreationTD/DJAutomation.bat
#------Test data for file scan----------------------
RJESwiftFile=/src/test/resources/testDataFiles/fileScanTD/RJE_Swift.txt
RJESwiftSampleFile=/src/test/resources/testDataFiles/fileScanTD/RJE_Swift_Sample.txt
GenericSampleFile=/src/test/resources/testDataFiles/fileScanTD/GenericSample.txt
#------Test data for FATF16----------------------
updateFatfSettings=/src/test/resources/testDataFiles/updateFatfSettings.json
#------Test data for Report Manager----------------------
generateReport=/src/test/resources/testDataFiles/generateReport.json
#------Test data for Zone Segregation----------------------
zoneSegregation=/src/test/resources/testDataFiles/zoneSegregation.json
#------ Test data for ISO20022----------------------
isoConfiguration=/src/test/resources/testDataFiles/ISO20022TD-SmokeTest.json
scanISOMessageWithoutXML=/src/test/resources/testDataFiles/ISO20022TD-70749.json
mergeISOFormat=/src/test/resources/testDataFiles/ISO20022TD-MergeFormat.json