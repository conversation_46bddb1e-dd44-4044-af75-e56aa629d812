#--------------------- Set Test data source type whether "Excel or DB" --------------------------------------------
#TestDataType = Excel
#--------------------- Define all classes paths that implement test data strategy --------------------------------------------
#TestDataStrategyClassPath_Excel = com.fawry.promoLoyalty.angularAutomation.strategy.testData.ExcelTestData
#TestDataStrategyClassPath_DB = com.fawry.promoLoyalty.angularAutomation.strategy.testData.OracleDBTestData
#--------------------- Define test data configurations for each test  --------------------------------------------
#For Excel --> MethodName = Excel test data file location ; sheet number
createMessageFromSaa=/src/test/resources/testDataFiles/createMessageFromSaa.json
uploadFinMessageFromSaa=/src/test/resources/testDataFiles/uploadFinMessageFromSaa.json
uploadIsoMessageFromSaa=/src/test/resources/testDataFiles/uploadIsoMessageFromSaa.json



