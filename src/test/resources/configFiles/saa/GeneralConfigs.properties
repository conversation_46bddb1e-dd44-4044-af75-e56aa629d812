# default browser's download file path
defaultDownloadPath=/src/test/resources/uploadsAndDownloads/downloads
defaultUploadPath=/src/test/resources/uploadsAndDownloads/uploads
#Extent report filepath
extentReportFilepath=/test-output/ExtentReport/FilteringReports
extentReportTitle=Safe Watch Filtering Test Report
extentReportName=Safe Watch Filtering Test Report
#Failed tests reports screenshot filepath
screenshotsOfFailedTestsPath=/test-output/ExtentReport/FailedTestsScreenshots
addLogToExtentReport=true
#Valid login credentials to be used whenever initializing a new driver to login to the application successfully and proceed in our tests
login.filtering.userMail=Tarek
login.filtering.password=123123
login.admin.mail=sysadmin
login.admin.password=manager
login.saa.userMail=qateam2
login.saa.password=SAA77Bet@2023QASWF2
login.saa.server.instance=qateam
#login.saa.server.instance = QAAIX(***********)7.6
screening.login.url=http://***********:8088/AMLUI/
saa.login.url=https://**********:2443/swp/group/access/
#DataBase Configuration
# Connection string of database
# Example : ***********************************************************
db.url=*************************************************************
# The password that will be used for both Screening databases
db.swserver.password=manhattan2004
db.scdb.password=scdb123
#################### Application's server details #########
remoteServerIp=***********
remoteServerUserName=all_adm
remoteServerPassword=@ll_@dm@2020@20
remoteServerPort=22

