[{"status": "REP", "matchedEntity": "LATVIA", "violationFilter": "SW_MATCH_FIELD = 'Fr/FIId/FinInstnId/BICFI'"}, {"status": "REP", "matchedEntity": "France", "violationFilter": "SW_MATCH_FIELD = 'To/FIId/FinInstnId/BICFI'"}, {"status": "REP", "matchedEntity": "Syria", "violationFilter": "SW_MATCH_FIELD = 'CdtTrfTxInf/CdtrAcct/Id/IBAN'"}, {"status": "REP", "matchedEntity": "Cyprus", "violationFilter": "SW_MATCH_FIELD = 'CdtTrfTxInf/DbtrAgt/FinInstnId/BICFI'"}, {"status": "REP", "matchedEntity": "Libya", "violationFilter": "SW_MATCH_FIELD = 'CdtTrfTxInf/CdtrAgt/FinInstnId/BICFI'"}, {"status": "REP", "matchedEntity": "Russian Federation", "violationFilter": "SW_MATCH_FIELD = 'CdtTrfTxInf/Dbtr/PstlAdr/AdrLine'"}, {"status": "REP", "matchedEntity": "United Kingdom", "violationFilter": "SW_MATCH_FIELD = 'CdtTrfTxInf/Cdtr/PstlAdr/Ctry'"}]