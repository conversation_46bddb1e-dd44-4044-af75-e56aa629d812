[[{"testCaseTitle": "ZoneSegregation-84616", "scriptName": "[Engine settings]Verify that new 'INDIVIDUAL_NAME_PREFIX_NEUTRAL' option can be enabled when select segregate zone.", "showSettings": "Engine Settings", "zone": "Default Zone", "individualNamePrefixNeutral": true, "enhanceMatchRankFirstName": true, "scannedName": "<PERSON><PERSON><PERSON>", "rank": "95", "detectionStatus": "REPNEW"}, {"testCaseTitle": "ZoneSegregation-84616", "scriptName": "[Engine settings]Verify that new 'INDIVIDUAL_NAME_PREFIX_NEUTRAL' option can be enabled when select segregate zone.", "showSettings": "Engine Settings", "zone": "Common Test Zone", "individualNamePrefixNeutral": false, "enhanceMatchRankFirstName": true, "scannedName": "<PERSON><PERSON><PERSON>", "rank": "95", "detectionStatus": "REPNEW"}], [{"testCaseTitle": "ZoneSegregation-84617", "scriptName": "[Engine settings]Verify that new 'Enable Short Words' option can be enabled when select segregate zone.", "showSettings": "Engine Settings", "zone": "Default Zone", "enableShortWords": true, "enhanceMatchRankFirstName": true, "scannedName": "<PERSON>", "rank": "85", "detectionStatus": "REPNEW"}, {"testCaseTitle": "ZoneSegregation-84617", "scriptName": "[Engine settings]Verify that new 'Enable Short Words' option can be enabled when select segregate zone.", "showSettings": "Engine Settings", "zone": "Default Configuration", "enableShortWords": false, "enhanceMatchRankFirstName": true, "scannedName": "<PERSON>", "detectionStatus": "clean"}]]