[[{"testCaseTitle": "ZoneSegregation-83022", "scriptName": "Verify that new 'Enable Slavic phonetics' option can be enabled when select segregate zone", "zone": "Default Zone", "enableSlavicPhonetic": true, "scannedName": "Boghdon Subotich", "rank": "92", "detectionStatus": "REPNEW"}, {"testCaseTitle": "ZoneSegregation-83022", "scriptName": "Verify that new 'Enable Slavic phonetics' option can be enabled when select segregate zone", "zone": "Default Configuration", "enableSlavicPhonetic": false, "maxCommentsCharacters": "70", "maxDataCharacters": "30", "allowDetectionDontKnow": true, "enableAutomaticSearchForModifiedDetections": true, "enableAuditTrail": true, "enableGroupForBlockReleaseDonKnow": "true", "enableGroupForAssignPending": true, "enableAlternativeWay": true, "enableImprovedInterpretationOfSWIFTFields": true, "automaticallyResetAssignees": true, "enableReadColumnOnAlerts": true, "enableReasonCodeWhenAssignReleaseOrBlockDetectionAlert": true, "showOptionUseAdvancedChecksumCalculation": true, "numberOfLastNotes": "5", "alertsDetectionsToBeCreatedModifiedWithCommentsMode": "MANDATORY", "scannedName": "Boghdon Subotich", "rank": "78", "detectionStatus": "REPNEW"}], [{"testCaseTitle": "ZoneSegregation-82978", "scriptName": "Verify that new 'Enable arabic phonetics' option can be enabled when select segregate zone.", "zone": "Default Zone", "enableArabicPhonetic": true, "scannedName": "Abu", "rank": "81", "detectionStatus": "REPNEW"}, {"testCaseTitle": "ZoneSegregation-82978", "scriptName": "Verify that new 'Enable arabic phonetics' option can be enabled when select segregate zone.", "zone": "Default Configuration", "maxCommentsCharacters": "70", "maxDataCharacters": "30", "allowDetectionDontKnow": true, "enableAutomaticSearchForModifiedDetections": true, "enableAuditTrail": true, "enableGroupForBlockReleaseDonKnow": "true", "enableGroupForAssignPending": true, "enableAlternativeWay": true, "enableImprovedInterpretationOfSWIFTFields": true, "automaticallyResetAssignees": true, "enableReadColumnOnAlerts": true, "enableReasonCodeWhenAssignReleaseOrBlockDetectionAlert": true, "showOptionUseAdvancedChecksumCalculation": true, "numberOfLastNotes": "5", "alertsDetectionsToBeCreatedModifiedWithCommentsMode": "MANDATORY", "enableArabicPhonetic": false, "scannedName": "Abu", "rank": "54", "detectionStatus": "REPNEW"}], [{"testCaseTitle": "ZoneSegregation-82983", "scriptName": "Verify that new 'Enable arabic phonetics' option can be enabled when select segregate zone.", "zone": "Default Zone", "enableArabicPhonetic": true, "enableArabicPhoneticPlus": true, "scannedName": "<PERSON><PERSON> dreber", "rank": "96", "detectionStatus": "REPNEW"}, {"testCaseTitle": "ZoneSegregation-82983", "scriptName": "Verify that new 'Enable arabic phonetics plus' option can be enabled when select segregate zone.", "zone": "Default Configuration", "enableArabicPhonetic": false, "enableArabicPhoneticPlus": true, "maxCommentsCharacters": "70", "maxDataCharacters": "30", "allowDetectionDontKnow": true, "enableAutomaticSearchForModifiedDetections": true, "enableAuditTrail": true, "enableGroupForBlockReleaseDonKnow": "true", "enableGroupForAssignPending": true, "enableAlternativeWay": true, "enableImprovedInterpretationOfSWIFTFields": true, "automaticallyResetAssignees": true, "enableReadColumnOnAlerts": true, "enableReasonCodeWhenAssignReleaseOrBlockDetectionAlert": true, "showOptionUseAdvancedChecksumCalculation": true, "numberOfLastNotes": "5", "alertsDetectionsToBeCreatedModifiedWithCommentsMode": "MANDATORY", "scannedName": "<PERSON><PERSON> dreber", "rank": "84", "detectionStatus": "REPNEW"}], [{"testCaseTitle": "ZoneSegregation-83000", "scriptName": "Verify that new 'Enable Russian phonetics' option can be enabled when select segregate zone", "zone": "Default Zone", "enableRussianPhonetic": true, "scannedName": "Alekei", "rank": "90", "detectionStatus": "REPNEW"}, {"testCaseTitle": "ZoneSegregation-83000", "scriptName": "Verify that new 'Enable Russian phonetics' option can be enabled when select segregate zone", "zone": "Default Configuration", "enableRussianPhonetic": false, "maxCommentsCharacters": "70", "maxDataCharacters": "30", "allowDetectionDontKnow": true, "enableAutomaticSearchForModifiedDetections": true, "enableAuditTrail": true, "enableGroupForBlockReleaseDonKnow": "true", "enableGroupForAssignPending": true, "enableAlternativeWay": true, "enableImprovedInterpretationOfSWIFTFields": true, "automaticallyResetAssignees": true, "enableReadColumnOnAlerts": true, "enableReasonCodeWhenAssignReleaseOrBlockDetectionAlert": true, "showOptionUseAdvancedChecksumCalculation": true, "numberOfLastNotes": "5", "alertsDetectionsToBeCreatedModifiedWithCommentsMode": "MANDATORY", "scannedName": "Alekei", "rank": "85", "detectionStatus": "REPNEW"}]]