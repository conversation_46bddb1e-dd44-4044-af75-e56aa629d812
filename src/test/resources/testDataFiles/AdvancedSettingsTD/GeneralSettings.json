{"maxCommentsCharacters": "70", "maxDataCharacters": "30", "allowDetectionDontKnow": true, "enableAutomaticSearchForModifiedDetections": true, "enableAuditTrail": true, "enableGroupForBlockReleaseDonKnow": "true", "enableGroupForAssignPending": true, "enableAlternativeWay": true, "enableDetectionButton": true, "enableImprovedInterpretationOfSWIFTFields": true, "automaticallyResetAssignees": true, "enableReadColumnOnAlerts": true, "enableReasonCodeWhenAssignReleaseOrBlockDetectionAlert": true, "showOptionUseAdvancedChecksumCalculation": true, "enableAutomaticFirstDetection": false, "numberOfLastNotes": "5", "alertsDetectionsToBeCreatedModifiedWithCommentsMode": "MANDATORY"}