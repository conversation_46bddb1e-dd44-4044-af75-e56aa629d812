[{"testCaseTitle": "ZoneSegregation-84612", "scriptName": "Verify that new 'Enable glued words symSpell' option can be enabled when select segregate zone.", "zone": "Default Zone", "enableGluedWordsSymSpell": true, "rank": "82", "detectionStatus": "REPNEW"}, {"testCaseTitle": "ZoneSegregation-84612", "scriptName": "Verify that new 'Enable glued words symSpell' option can be enabled when select segregate zone.", "zone": "Default Configuration", "enableGluedWordsSymSpell": false, "maxCommentsCharacters": "70", "maxDataCharacters": "30", "allowDetectionDontKnow": true, "enableAutomaticSearchForModifiedDetections": true, "enableAuditTrail": true, "enableGroupForBlockReleaseDonKnow": "true", "enableGroupForAssignPending": true, "enableAlternativeWay": true, "enableImprovedInterpretationOfSWIFTFields": true, "automaticallyResetAssignees": true, "enableReadColumnOnAlerts": true, "enableReasonCodeWhenAssignReleaseOrBlockDetectionAlert": true, "showOptionUseAdvancedChecksumCalculation": true, "numberOfLastNotes": "5", "alertsDetectionsToBeCreatedModifiedWithCommentsMode": "MANDATORY", "detectionStatus": "clean"}]