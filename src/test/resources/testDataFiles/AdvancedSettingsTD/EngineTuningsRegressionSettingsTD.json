[{"testCaseTitle": "Regression_Scan_IgnoreMatches_01", "scriptName": "Verify that no hit returned on scanning 'BLA' against individual 'BLA'", "showSettings": "Engine Settings", "zone": "Default Configuration", "cacheExpiration": "1", "ignoreMatches": "3", "ignoreFlag": false, "enableMatchOnNumber": false, "scannedNameList": ["BLA", "CAT", "UAE", "<PERSON><PERSON>", "Mod"], "detectionStatusList": ["CLEAN", "CLEAN", "REPNEW", "REPNEW", "CLEAN"]}, {"testCaseTitle": "Regression_Scan_IgnoreMatches_02", "scriptName": "Verify that no hit returned on scanning 'Hbib' against individual 'Hbib", "showSettings": "Engine Settings", "zone": "Default Configuration", "cacheExpiration": "1", "ignoreMatches": "4", "ignoreFlag": false, "enableMatchOnNumber": false, "scannedNameList": ["Hbib", "Sami", "<PERSON><PERSON>", "<PERSON>", "Iran"], "detectionStatusList": ["CLEAN", "CLEAN", "CLEAN", "REPNEW", "REPNEW"]}, {"testCaseTitle": "Regression_Scan_IgnoreMatches_03", "scriptName": "Verify that no hit returned on scanning 'hande' against unknown 'hande'", "showSettings": "Engine Settings", "zone": "Default Configuration", "cacheExpiration": "1", "ignoreMatches": "5", "ignoreFlag": false, "enableMatchOnNumber": false, "scannedNameList": ["hande", "<PERSON><PERSON>", "<PERSON><PERSON>", "Brakat", "Jordan", "Syria"], "detectionStatusList": ["CLEAN", "REPNEW", "REPNEW", "REPNEW", "REPNEW", "REPNEW"]}]