[{"testCaseTitle": "Check Change password if new password in wrong length.", "newPassword": "AB5!ce", "expectedMessage": "Invalid username/password , the password is too short, expected minimum [#LENGTH#] characters."}, {"testCaseTitle": "Check Change password if new password contains upper case characters count  less than minimum count.", "newPassword": "AB5!cee", "expectedMessage": "Invalid username/password , the Password does not meet the password policy requirements, expected minimum [#UPPER#] upper characters, minimum [#LOWER#] lower characters, minimum [#DIGITS#] numbers , and minimum [#SPECIAL_CHARACTERS#] special characters."}, {"testCaseTitle": "Check Change password if new password contains lower case characters count less than minimum count.", "newPassword": "ABCD5!e", "expectedMessage": "Invalid username/password , the Password does not meet the password policy requirements, expected minimum [#UPPER#] upper characters, minimum [#LOWER#] lower characters, minimum [#DIGITS#] numbers , and minimum [#SPECIAL_CHARACTERS#] special characters."}, {"testCaseTitle": "Check Change password if new password contains digits count less than minimum count.", "newPassword": "ABC!ee$", "expectedMessage": "Invalid username/password , the Password does not meet the password policy requirements, expected minimum [#UPPER#] upper characters, minimum [#LOWER#] lower characters, minimum [#DIGITS#] numbers , and minimum [#SPECIAL_CHARACTERS#] special characters."}, {"testCaseTitle": "Check Change password if new password contains special characters count less than minimum count.", "newPassword": "ABC77ee", "expectedMessage": "Invalid username/password , the Password does not meet the password policy requirements, expected minimum [#UPPER#] upper characters, minimum [#LOWER#] lower characters, minimum [#DIGITS#] numbers , and minimum [#SPECIAL_CHARACTERS#] special characters."}, {"testCaseTitle": "Check Change password with valid new password format for first time.", "newPassword": "ABC$7ee", "expectedMessage": "The paswword has been successfully changed."}, {"testCaseTitle": "Check Change password with valid new password format for second time.", "newPassword": "ABC#5ee", "expectedMessage": "The paswword has been successfully changed."}, {"testCaseTitle": "Check Change password if new password used before.", "newPassword": "ABC#5ee", "expectedMessage": "Invalid username/password , the password has been already used, please enter a different password from the last [#HISTORY_COUNT#] used."}]