[{"testCaseName": "Verify that user is not able to Edit a Profile when 'Allow to modify profile' permission is not granted.", "scriptName": "Regression_Admin_01", "permissionName": "Allow to modify profile", "name": "selenium-full-right-profile ", "functionToCheck": "EditProfile"}, {"testCaseName": "Verify that user is not able to Clone a Profile when 'Create Profile' permission is not granted", "scriptName": "Regression_Admin_02", "permissionName": "Allow to create new profile", "name": "selenium-full-right-profile ", "functionToCheck": "CloneProfile"}, {"testCaseName": "Verify that user is not able to Delete a Profile when 'Delete Profile' permission is not granted", "scriptName": "Regression_Admin_03", "permissionName": "Allow to delete profile", "name": "selenium-full-right-profile ", "functionToCheck": "DeleteProfile"}, {"testCaseName": "Verify that user is not able to Enable a newly created Profile when 'Allow to enable profile' permission is not granted", "scriptName": "Regression_Admin_16", "permissionName": "Allow to enable profile", "name": "selenium-full-right-profile ", "functionToCheck": "EnableNewlyCreatedProfile"}, {"testCaseName": "Verify that user is not able to Enable a Profile by cloning when 'Allow to enable profile' permission is not granted", "scriptName": "Regression_Admin_17", "permissionName": "Allow to enable profile", "name": "selenium-full-right-profile ", "functionToCheck": "EnableProfileByClone"}]