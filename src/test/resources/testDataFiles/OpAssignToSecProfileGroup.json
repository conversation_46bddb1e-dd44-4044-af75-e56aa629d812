[{"testCaseTitle": "Verify that the user that is assigned to Group and Profile have permission to Release but don't have the permission to Block is able to block when this user is assigned to a second Group and Profile that have the permission to Block.", "scriptName": "Regression_Admin_05", "permissionName": "Allow to change detection status to Real Violation(Block detection)", "functionToCheck": "Block"}, {"testCaseTitle": "Verify that the user that is assigned to Group and Profile have permission to Block but don't have the permission to Release is able to release \nwhen this user is assigned to a second Group and Profile that have the permission to Release.", "scriptName": "Regression_Admin_06", "permissionName": "Allow to add attachments to detection", "functionToCheck": "Release"}]