[{"testCaseTitle": "Verify that user is able to add an entity as <PERSON> Guy from the results tab for a scanned file with SWIFT RJE Records type.", "scriptName": "Regression_ScanMgr_05", "filePath": "/src/test/resources/testDataFiles/fileScanTD/RJE_Swift.txt", "format": "SWIFT RJE Records", "detectVessels": true, "detectCountries": true, "createAlertsAutomatically": false, "takeAction": "no", "expectedResults": "Good guy successfully created!", "entryName": "EntryName, EntryName"}, {"testCaseTitle": "Verify that user is able to add an entity as <PERSON> Guy from the results tab for a scanned file with Custom Format type.", "scriptName": "Regression_ScanMgr_06", "filePath": "/src/test/resources/testDataFiles/fileScanTD/ScanFile.txt", "format": "Custom Format File", "detectVessels": true, "detectCountries": true, "createAlertsAutomatically": false, "takeAction": "no", "expectedResults": "Good guy successfully created!", "entryName": "EntryName1, EntryName1"}]