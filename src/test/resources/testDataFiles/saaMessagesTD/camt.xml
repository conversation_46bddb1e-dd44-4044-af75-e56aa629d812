<?xml version="1.0" encoding="UTF-8" ?>
<Saa:DataPDU xmlns:Saa="urn:swift:saa:xsd:saa.2.0"
>
    <Saa:Revision>2.0.10</Saa:Revision>
    <Saa:Header>
        <Saa:Message>
            <Saa:SenderReference>IPTSABEMMXXX056$2112121872</Saa:SenderReference>
            <Saa:MessageIdentifier>camt.056.001.08</Saa:MessageIdentifier>
            <Saa:Format>MX</Saa:Format>
            <Saa:SubFormat>Input</Saa:SubFormat>
            <Saa:Sender>
                <Saa:DN>o=ptsabemm,o=swift</Saa:DN>
                <Saa:FullName>
                    <Saa:X1>PTSABEMMXXX</Saa:X1>
                </Saa:FullName>
            </Saa:Sender>
            <Saa:Receiver>
                <Saa:DN>o=ptsabemm,o=swift</Saa:DN>
                <Saa:FullName>
                    <Saa:X1>PTSABEMMXXX</Saa:X1>
                </Saa:FullName>
            </Saa:Receiver>
            <Saa:InterfaceInfo>
                <Saa:MessageCreator>Messenger</Saa:MessageCreator>
                <Saa:MessageContext>Original</Saa:MessageContext>
                <Saa:MessageNature>Financial</Saa:MessageNature>
                <Saa:Sumid>1E4A450CFFFFF8AF</Saa:Sumid>
                <Saa:ServiceURI>mp/mx/__mRWkJI2EeuXfsPBlWnVxg</Saa:ServiceURI>
                <Saa:MessageTypeURI>mp/mx/__mRWkJI2EeuXfsPBlWnVxg/__mTLwZI2EeuXfsPBlWnVxg</Saa:MessageTypeURI>
            </Saa:InterfaceInfo>
            <Saa:NetworkInfo>
                <Saa:Priority>Normal</Saa:Priority>
                <Saa:IsPossibleDuplicate>false</Saa:IsPossibleDuplicate>
                <Saa:Service>swift.finplus!TEST</Saa:Service>
                <Saa:SWIFTNetNetworkInfo>
                    <Saa:RequestType>camt.056.001.08</Saa:RequestType>
                    <Saa:RequestSubtype>swift.cbprplus.01</Saa:RequestSubtype>
                    <Saa:Reference>17f430ce-bb5e-407d-8221-25a8e09bb25d</Saa:Reference>
                </Saa:SWIFTNetNetworkInfo>
            </Saa:NetworkInfo>
            <Saa:ExpiryDateTime>20220101085932</Saa:ExpiryDateTime>
        </Saa:Message>
    </Saa:Header>
    <Saa:Body>
        <AppHdr xmlns="urn:iso:std:iso:20022:tech:xsd:head.001.001.02">
            <Fr>
                <FIId>
                    <FinInstnId>
                        <BICFI>PTSABEMMXXX</BICFI>
                    </FinInstnId>
                </FIId>
            </Fr>
            <To>
                <FIId>
                    <FinInstnId>
                        <BICFI>ZYGSBEB0XXX</BICFI>
                    </FinInstnId>
                </FIId>
            </To>
            <BizMsgIdr>TC707</BizMsgIdr>
            <MsgDefIdr>camt.056.001.08</MsgDefIdr>
            <BizSvc>swift.cbprplus.01</BizSvc>
            <CreDt>9999-12-31T00:00:00+00:00</CreDt>
            <Prty>NORM</Prty>
        </AppHdr>
        <Document xmlns="urn:iso:std:iso:20022:tech:xsd:camt.056.001.08">
            <FIToFIPmtCxlReq>
                <Assgnmt>
                    <Id>TC707</Id>
                    <Assgnr>
                        <Agt>
                            <FinInstnId>
                                <BICFI>PTSABEMMXXX</BICFI>
                            </FinInstnId>
                        </Agt>
                    </Assgnr>
                    <Assgne>
                        <Agt>
                            <FinInstnId>
                                <BICFI>ZYGSBEB0XXX</BICFI>
                            </FinInstnId>
                        </Agt>
                    </Assgne>
                    <CreDtTm>9999-12-31T00:00:00+00:00</CreDtTm>
                </Assgnmt>
                <Undrlyg>
                    <TxInf>
                        <Case>
                            <Id>TC707</Id>
                            <Cretr>
                                <Agt>
                                    <FinInstnId>
                                        <Nm>NOTPROVIDED</Nm>
                                        <PstlAdr>
                                            <AdrLine>NOTPROVIDED</AdrLine>
                                        </PstlAdr>
                                    </FinInstnId>
                                </Agt>
                            </Cretr>
                        </Case>
                        <OrgnlGrpInf>
                            <OrgnlMsgId>TC707SRP</OrgnlMsgId>
                            <OrgnlMsgNmId>pacs.008</OrgnlMsgNmId>
                            <OrgnlCreDtTm>2020-11-30T00:00:00+00:00</OrgnlCreDtTm>
                        </OrgnlGrpInf>
                        <OrgnlInstrId>TC707SRP</OrgnlInstrId>
                        <OrgnlEndToEndId>NOTPROVIDED</OrgnlEndToEndId>
                        <OrgnlUETR>e711d837-827c-44ea-a9e4-56d3c7d5fbc5</OrgnlUETR>
                        <OrgnlIntrBkSttlmAmt Ccy="USD">980</OrgnlIntrBkSttlmAmt>
                        <OrgnlIntrBkSttlmDt>2020-11-30</OrgnlIntrBkSttlmDt>
                        <CxlRsnInf>
                            <Rsn>
                                <Cd>DUPL</Cd>
                            </Rsn>
                        </CxlRsnInf>
                    </TxInf>
                </Undrlyg>
            </FIToFIPmtCxlReq>
        </Document><!-- {1:F01BIGTGB20XXXX0000000000}{2:O2021556220628BOFIIE20XXXX00000000002206281556N}{3:{111:001}{121:c0ce4210-f885-497d-8f5e-fa1c314f028d}}{4:^~:20:IDPACS009T28A^~:21:e2eid/pacs.008/+^~:32A:220628EUR1000,^~:52A:BOFIIE2DXXX^~:57A:BIGTGB2XXXX^~:58A:BIGTGB2XXXX^~:72:/INS/BOFIIE2DXXX^~/BNF/Helpest This..^~-}{5:{CHK:09E7306FFEBD}{TNG:}} -->


        <!-- TranslationResult=TRFR testing-->


        <!-- TranslationInfo version *******
    {
        "errors": [
      {
       "code": "TRFR",
       "message": "TRUNC_R.T0000T Field content has been truncated.\r\nValue 'e2eid/pacs.008/1-01234567-AABBC' has been altered.",
       "path": "MT202/Block4/RelatedReference"
      },
      {
       "code": "TRNR",
       "message": "TRUNC_N.T0000R Illegal characters have been replaced.\r\nValue 'Helpest This%%' has been altered.",
       "path": "MT202/Block4/SenderToRcvrInfo"
      }
        ]
    }
     -->
    </Saa:Body>
</Saa:DataPDU>