[{"testCaseTitle": "Verify the ability to change password through internal password change module using a user already has password contains sequential numbers in old pass/new pass/confirm pass fields when Prevent Consecutive is unchecked", "scriptName": "Regression_PasswordPolicy_01", "minimumUpperCaseCount": "0", "minimumLowerCaseCount": "0", "minimumDigitsCount": "1", "minimumSpecialCharactersCount": "0", "preventConsecutiveFlag": "false", "statusFlag": "true", "newPassword": "123456789", "historyCount": "0", "expectedMessage": "The paswword has been successfully changed."}, {"testCaseTitle": "Verify the ability to change password through internal password change module using a user already has password contains sequential numbers in old pass/new pass/confirm pass fields when Prevent Consecutive is checked", "scriptName": "Regression_PasswordPolicy_02", "minimumUpperCaseCount": "0", "minimumLowerCaseCount": "0", "minimumDigitsCount": "1", "minimumSpecialCharactersCount": "0", "preventConsecutiveFlag": "true", "statusFlag": "true", "newPassword": "123456789", "historyCount": "0", "expectedMessage": "Password must not contain a numerical and/or alphabetical sequence."}, {"testCaseTitle": "Verify the ability to change password through internal password change module using a user already has password contains sequential numbers in old pass/new pass/confirm pass fields when Prevent Consecutive is checked and status is disabled", "scriptName": "Regression_PasswordPolicy_03", "preventConsecutiveFlag": "true", "statusFlag": "false", "newPassword": "123456789", "historyCount": "0", "expectedMessage": "The paswword has been successfully changed."}, {"testCaseTitle": "Verify that the validation message should be “Password must not contain a numerical and/or alphabetical sequence.” when password contains sequential numbers in Profile Settings module when Prevent Consecutive is checked", "scriptName": "Regression_PasswordPolicy_04", "minimumUpperCaseCount": "0", "minimumLowerCaseCount": "0", "minimumDigitsCount": "1", "minimumSpecialCharactersCount": "0", "preventConsecutiveFlag": "true", "statusFlag": "true", "newPassword": "123456789", "historyCount": "0", "expectedMessage": "Password must not contain a numerical and/or alphabetical sequence."}, {"testCaseTitle": "Verify that the validation message should be “Password must not contain a numerical and/or alphabetical sequence.” when password contains sequential numbers in Operator Editor page when Prevent Consecutive is checked", "scriptName": "Regression_PasswordPolicy_05", "minimumUpperCaseCount": "0", "minimumLowerCaseCount": "0", "minimumDigitsCount": "1", "minimumSpecialCharactersCount": "0", "preventConsecutiveFlag": "true", "statusFlag": "true", "newPassword": "123456789", "historyCount": "0", "expectedMessage": "Password must not contain a numerical and/or alphabetical sequence."}, {"testCaseTitle": "Verify the ability to create user with password contains sequential numbers in pass/confirm pass fields when Prevent Consecutive is unchecked", "scriptName": "Regression_PasswordPolicy_06", "minimumUpperCaseCount": "0", "minimumLowerCaseCount": "0", "minimumDigitsCount": "1", "minimumSpecialCharactersCount": "0", "preventConsecutiveFlag": "false", "statusFlag": "true", "newPassword": "123456789", "historyCount": "0", "expectedMessage": "The paswword has been successfully changed."}, {"testCaseTitle": "Verify the ability to edit user with password contains sequential numbers in  pass/confirm pass fields when Prevent Consecutive is unchecked", "scriptName": "Regression_PasswordPolicy_07", "minimumUpperCaseCount": "0", "minimumLowerCaseCount": "0", "minimumDigitsCount": "1", "minimumSpecialCharactersCount": "0", "preventConsecutiveFlag": "false", "statusFlag": "true", "newPassword": "123456789", "historyCount": "0", "expectedMessage": "The paswword has been successfully changed."}, {"testCaseTitle": "Verify the disability to edit user with password contains sequential numbers in  pass/confirm pass fields when Prevent Consecutive is checked", "scriptName": "Regression_PasswordPolicy_08", "minimumUpperCaseCount": "0", "minimumLowerCaseCount": "0", "minimumDigitsCount": "1", "minimumSpecialCharactersCount": "0", "preventConsecutiveFlag": "true", "statusFlag": "true", "newPassword": "123456789", "historyCount": "0", "expectedMessage": "Password must not contain a numerical and/or alphabetical sequence."}, {"testCaseTitle": "Verify the disability to create user with password contains sequential numbers in  pass/confirm pass fields when Prevent Consecutive is checked", "scriptName": "Regression_PasswordPolicy_09", "minimumUpperCaseCount": "0", "minimumLowerCaseCount": "0", "minimumDigitsCount": "1", "minimumSpecialCharactersCount": "0", "preventConsecutiveFlag": "true", "statusFlag": "true", "newPassword": "123456789", "historyCount": "0", "expectedMessage": "Password must not contain a numerical and/or alphabetical sequence."}, {"testCaseTitle": "Verify that password like \"cba321\" should be accepted while the Prevent Consecutive is checked as it shouldn't be considered consecutive or sequential", "scriptName": "Regression_PasswordPolicy_10", "minimumUpperCaseCount": "0", "minimumLowerCaseCount": "0", "minimumDigitsCount": "1", "minimumSpecialCharactersCount": "0", "preventConsecutiveFlag": "true", "statusFlag": "true", "newPassword": "cba321", "historyCount": "0", "expectedMessage": "The paswword has been successfully changed."}, {"testCaseTitle": "Verify that password like \"qponmlk\" should be accepted while the Prevent Consecutive is checked as it shouldn't be considered consecutive or sequential", "scriptName": "Regression_PasswordPolicy_11", "minimumUpperCaseCount": "0", "minimumLowerCaseCount": "0", "minimumDigitsCount": "0", "minimumSpecialCharactersCount": "0", "preventConsecutiveFlag": "true", "statusFlag": "true", "newPassword": "qponmlk", "historyCount": "0", "expectedMessage": "The paswword has been successfully changed."}, {"testCaseTitle": "Verify that password like \"9876543210\" should be accepted while the Prevent Consecutive is checked as it shouldn't be considered consecutive or sequential", "scriptName": "Regression_PasswordPolicy_12", "minimumUpperCaseCount": "0", "minimumLowerCaseCount": "0", "minimumDigitsCount": "1", "minimumSpecialCharactersCount": "0", "preventConsecutiveFlag": "true", "statusFlag": "true", "newPassword": "9876543210", "historyCount": "0", "expectedMessage": "The paswword has been successfully changed."}, {"testCaseTitle": "Verify that password like \"111111\" should be accepted while the Prevent Consecutive is checked as it shouldn't be considered consecutive or sequential", "scriptName": "Regression_PasswordPolicy_13", "minimumUpperCaseCount": "0", "minimumLowerCaseCount": "0", "minimumDigitsCount": "1", "minimumSpecialCharactersCount": "0", "preventConsecutiveFlag": "true", "statusFlag": "true", "newPassword": "111111", "historyCount": "0", "expectedMessage": "The paswword has been successfully changed."}, {"testCaseTitle": "Verify that password like \"aaaaaa\",\"111111\", \"9876543210\", \"qponmlk\", \"cba321\" should be accepted while the Prevent Consecutive is checked as it shouldn't be considered consecutive or sequential", "scriptName": "Regression_PasswordPolicy_14", "minimumUpperCaseCount": "0", "minimumLowerCaseCount": "0", "minimumDigitsCount": "0", "minimumSpecialCharactersCount": "0", "preventConsecutiveFlag": "true", "statusFlag": "true", "newPassword": "aaaaaa", "historyCount": "0", "expectedMessage": "The paswword has been successfully changed."}]