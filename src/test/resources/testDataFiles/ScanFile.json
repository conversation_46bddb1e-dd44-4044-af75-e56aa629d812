[{"testCaseTitle": "Verify that user is able to scan a swift rje record without creating alerts automatically option checked and take decision.", "scriptName": "Regression_ScanMgr_01", "filePath": "/RJE_Swift.txt", "format": "SWIFT RJE Records", "detectVessels": true, "detectCountries": true, "createAlertsAutomatically": false, "expectedResults": "Detection(s) with id(s) ' %s ,' has no alerts created. Status or assignee can be changed only for detections with alerts!"}, {"testCaseTitle": "Verify that user is able to scan a generic text format file without creating alerts automatically option checked and take decision .", "scriptName": "Regression_ScanMgr_02", "filePath": "/Generic.txt", "format": "Generic Text", "detectVessels": true, "detectCountries": true, "createAlertsAutomatically": false, "expectedResults": "Detection(s) with id(s) ' %s ,' has no alerts created. Status or assignee can be changed only for detections with alerts!"}, {"testCaseTitle": "Verify that user is able to scan a custom format file without creating alerts option checked and take decision. ", "scriptName": "Regression_ScanMgr_03", "filePath": "/Generic.txt", "format": "Custom Format File", "detectVessels": true, "detectCountries": true, "createAlertsAutomatically": false, "expectedResults": "Detection(s) with id(s) ' %s ,' has no alerts created. Status or assignee can be changed only for detections with alerts!"}, {"testCaseTitle": "Verify that user is able to scan a custom format file with creating alerts option checked and take decision. ", "scriptName": "Regression_ScanMgr_04", "filePath": "/Generic.txt", "format": "Custom Format File", "detectVessels": true, "detectCountries": true, "createAlertsAutomatically": true, "takeAction": "yes", "expectedResults": "1 detection(s) successfully modified!"}]