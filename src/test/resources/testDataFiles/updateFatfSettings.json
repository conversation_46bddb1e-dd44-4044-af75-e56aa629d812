[{"testCaseTitle": "Domestic message and  Field57 &52  same Country Code -50F is violated ", "fieldMinimumLineSize": "2", "enableFatfRecommendation": "true", "rank": "50", "europeanCodes": "SEK,CHF", "enableCheckOfUniqueTransactionReferenceNumber": "false", "informationAvailableWithinTransaction": "false", "informationAvailableOutsideTheTransaction": "true", "informationAvailableForBics": "false", "bics": "", "applyForCrossBorderTraffic": "false", "currencies": [{"name": "SEK", "amount": "1"}, {"name": "CHF", "amount": "2"}], "zones": [{"name": "Perf_Zone"}], "blackLists": [{"name": "UN Iran"}], "fileName": "fatf_Tc01.txt", "encoding": "UTF8", "format": "Generic Text", "results": "Only blocked records", "expectedMessage": "", "action": "save"}, {"testCaseTitle": "Domestic message with Field57 &52  same Country Code -50F and reference violated ", "fieldMinimumLineSize": "2", "enableFatfRecommendation": "true", "rank": "50", "europeanCodes": "SEK,CHF", "enableCheckOfUniqueTransactionReferenceNumber": "false", "informationAvailableWithinTransaction": "false", "informationAvailableOutsideTheTransaction": "true", "informationAvailableForBics": "false", "bics": "", "applyForCrossBorderTraffic": "false", "currencies": [{"name": "SEK", "amount": "1"}, {"name": "CHF", "amount": "2"}], "zones": [{"name": "Perf_Zone"}], "blackLists": [{"name": "UN Iran"}], "fileName": "fatf_Tc02.txt", "encoding": "UTF8", "format": "Generic Text", "results": "Only blocked records", "expectedMessage": "", "action": "save"}, {"testCaseTitle": "Domestic message with Field57 &52  same Country Code -50F and refernce not violated ", "fieldMinimumLineSize": "2", "enableFatfRecommendation": "true", "rank": "50", "europeanCodes": "SEK,CHF", "enableCheckOfUniqueTransactionReferenceNumber": "false", "informationAvailableWithinTransaction": "false", "informationAvailableOutsideTheTransaction": "true", "informationAvailableForBics": "false", "bics": "", "applyForCrossBorderTraffic": "false", "currencies": [{"name": "SEK", "amount": "1"}, {"name": "CHF", "amount": "2"}], "zones": [{"name": "Perf_Zone"}], "blackLists": [{"name": "UN Iran"}], "fileName": "fatf_Tc03.txt", "encoding": "UTF8", "format": "Generic Text", "results": "Only blocked records", "expectedMessage": "", "action": "save"}, {"testCaseTitle": "Domestic message with Field57 &52  same Country Code- Field 59 & reference are violated", "fieldMinimumLineSize": "2", "enableFatfRecommendation": "true", "rank": "50", "europeanCodes": "SEK,CHF", "enableCheckOfUniqueTransactionReferenceNumber": "false", "informationAvailableWithinTransaction": "false", "informationAvailableOutsideTheTransaction": "true", "informationAvailableForBics": "false", "bics": "", "applyForCrossBorderTraffic": "false", "currencies": [{"name": "SEK", "amount": "1"}, {"name": "CHF", "amount": "2"}], "zones": [{"name": "Perf_Zone"}], "blackLists": [{"name": "UN Iran"}], "fileName": "fatf_Tc04.txt", "encoding": "UTF8", "format": "Generic Text", "results": "Only blocked records", "expectedMessage": "", "action": "save"}, {"testCaseTitle": "Domestic message with Field57 &52  same Country Code- Field 59 & reference are not violated ", "fieldMinimumLineSize": "2", "enableFatfRecommendation": "true", "rank": "50", "europeanCodes": "SEK,CHF", "enableCheckOfUniqueTransactionReferenceNumber": "false", "informationAvailableWithinTransaction": "false", "informationAvailableOutsideTheTransaction": "true", "informationAvailableForBics": "false", "bics": "", "applyForCrossBorderTraffic": "false", "currencies": [{"name": "SEK", "amount": "1"}, {"name": "CHF", "amount": "2"}], "zones": [{"name": "Perf_Zone"}], "blackLists": [{"name": "UN Iran"}], "fileName": "fatf_Tc05.txt", "encoding": "UTF8", "format": "Generic Text", "results": "Only blocked records", "expectedMessage": "", "action": "save"}, {"testCaseTitle": "Domestic message with Field57 &52  same Country Code- Field 59 not violated only refrence violated ", "fieldMinimumLineSize": "2", "enableFatfRecommendation": "true", "rank": "50", "europeanCodes": "SEK,CHF", "enableCheckOfUniqueTransactionReferenceNumber": "false", "informationAvailableWithinTransaction": "false", "informationAvailableOutsideTheTransaction": "true", "informationAvailableForBics": "false", "bics": "", "applyForCrossBorderTraffic": "false", "currencies": [{"name": "SEK", "amount": "1"}, {"name": "CHF", "amount": "2"}], "zones": [{"name": "Perf_Zone"}], "blackLists": [{"name": "UN Iran"}], "fileName": "fatf_Tc06.txt", "encoding": "UTF8", "format": "Generic Text", "results": "Only blocked records", "expectedMessage": "", "action": "save"}, {"testCaseTitle": "Domestic message Sender&Receiver in EU codes -Field 50F & reference are violated", "fieldMinimumLineSize": "2", "enableFatfRecommendation": "true", "rank": "50", "europeanCodes": "SEK,CHF", "enableCheckOfUniqueTransactionReferenceNumber": "false", "informationAvailableWithinTransaction": "false", "informationAvailableOutsideTheTransaction": "true", "informationAvailableForBics": "false", "bics": "", "applyForCrossBorderTraffic": "false", "currencies": [{"name": "SEK", "amount": "1"}, {"name": "CHF", "amount": "2"}], "zones": [{"name": "Perf_Zone"}], "blackLists": [{"name": "UN Iran"}], "fileName": "fatf_Tc07.txt", "encoding": "UTF8", "format": "Generic Text", "results": "Only blocked records", "expectedMessage": "", "action": "save"}, {"testCaseTitle": "Domestic message Sender&Receiver in EU codes 50A& reference are  violated  ", "fieldMinimumLineSize": "2", "enableFatfRecommendation": "true", "rank": "50", "europeanCodes": "SEK,CHF", "enableCheckOfUniqueTransactionReferenceNumber": "false", "informationAvailableWithinTransaction": "false", "informationAvailableOutsideTheTransaction": "true", "informationAvailableForBics": "false", "bics": "", "applyForCrossBorderTraffic": "false", "currencies": [{"name": "SEK", "amount": "1"}, {"name": "CHF", "amount": "2"}], "zones": [{"name": "Perf_Zone"}], "blackLists": [{"name": "UN Iran"}], "fileName": "fatf_Tc08.txt", "encoding": "UTF8", "format": "Generic Text", "results": "Only blocked records", "expectedMessage": "", "action": "save"}, {"testCaseTitle": "Domestic message Sender&Receiver in EU codes_50A without account number", "fieldMinimumLineSize": "2", "enableFatfRecommendation": "true", "rank": "50", "europeanCodes": "SEK,CHF", "enableCheckOfUniqueTransactionReferenceNumber": "false", "informationAvailableWithinTransaction": "false", "informationAvailableOutsideTheTransaction": "true", "informationAvailableForBics": "false", "bics": "", "applyForCrossBorderTraffic": "false", "currencies": [{"name": "SEK", "amount": "1"}, {"name": "CHF", "amount": "2"}], "zones": [{"name": "Perf_Zone"}], "blackLists": [{"name": "UN Iran"}], "fileName": "fatf_Tc09.txt", "encoding": "UTF8", "format": "Generic Text", "results": "Only blocked records", "expectedMessage": "", "action": "save"}, {"testCaseTitle": "Domestic message Sender&Receiver in EU codes_50A without account number & reference is violated", "fieldMinimumLineSize": "2", "enableFatfRecommendation": "true", "rank": "50", "europeanCodes": "SEK,CHF", "enableCheckOfUniqueTransactionReferenceNumber": "false", "informationAvailableWithinTransaction": "false", "informationAvailableOutsideTheTransaction": "true", "informationAvailableForBics": "false", "bics": "", "applyForCrossBorderTraffic": "false", "currencies": [{"name": "SEK", "amount": "1"}, {"name": "CHF", "amount": "2"}], "zones": [{"name": "Perf_Zone"}], "blackLists": [{"name": "UN Iran"}], "fileName": "fatf_Tc10.txt", "encoding": "UTF8", "format": "Generic Text", "results": "Only blocked records", "expectedMessage": "", "action": "save"}, {"testCaseTitle": "Domestic message Sender&Receiver in EU code _50K and reference are violated ", "fieldMinimumLineSize": "2", "enableFatfRecommendation": "true", "rank": "50", "europeanCodes": "SEK,CHF", "enableCheckOfUniqueTransactionReferenceNumber": "false", "informationAvailableWithinTransaction": "false", "informationAvailableOutsideTheTransaction": "true", "informationAvailableForBics": "false", "bics": "", "applyForCrossBorderTraffic": "false", "currencies": [{"name": "SEK", "amount": "1"}, {"name": "CHF", "amount": "2"}], "zones": [{"name": "Perf_Zone"}], "blackLists": [{"name": "UN Iran"}], "fileName": "fatf_Tc11.txt", "encoding": "UTF8", "format": "Generic Text", "results": "Only blocked records", "expectedMessage": "", "action": "save"}, {"testCaseTitle": "Domestic message Sender&Receiver in EU code _50K is only violated ", "fieldMinimumLineSize": "2", "enableFatfRecommendation": "true", "rank": "50", "europeanCodes": "SEK,CHF", "enableCheckOfUniqueTransactionReferenceNumber": "false", "informationAvailableWithinTransaction": "false", "informationAvailableOutsideTheTransaction": "true", "informationAvailableForBics": "false", "bics": "", "applyForCrossBorderTraffic": "false", "currencies": [{"name": "SEK", "amount": "1"}, {"name": "CHF", "amount": "2"}], "zones": [{"name": "Perf_Zone"}], "blackLists": [{"name": "UN Iran"}], "fileName": "fatf_Tc12.txt", "encoding": "UTF8", "format": "Generic Text", "results": "Only blocked records", "expectedMessage": "", "action": "save"}, {"testCaseTitle": "Domestic message_Sender and Receiver 57A &52A with Same country Code , \n59 account is violated , refernce not checked ", "fieldMinimumLineSize": "2", "enableFatfRecommendation": "true", "rank": "50", "europeanCodes": "SEK,CHF", "enableCheckOfUniqueTransactionReferenceNumber": "false", "informationAvailableWithinTransaction": "false", "informationAvailableOutsideTheTransaction": "true", "informationAvailableForBics": "false", "bics": "", "applyForCrossBorderTraffic": "false", "currencies": [{"name": "SEK", "amount": "1"}, {"name": "CHF", "amount": "2"}], "zones": [{"name": "Perf_Zone"}], "blackLists": [{"name": "UN Iran"}], "fileName": "fatf_Tc13.txt", "encoding": "UTF8", "format": "Generic Text", "results": "Only blocked records", "expectedMessage": "", "action": "save"}, {"testCaseTitle": "Domestic message_Sender and Receiver  in EU codes _59 with no account   & reference is not violated but its not checekd ", "fieldMinimumLineSize": "2", "enableFatfRecommendation": "true", "rank": "50", "europeanCodes": "SEK,CHF", "enableCheckOfUniqueTransactionReferenceNumber": "false", "informationAvailableWithinTransaction": "false", "informationAvailableOutsideTheTransaction": "true", "informationAvailableForBics": "false", "bics": "", "applyForCrossBorderTraffic": "false", "currencies": [{"name": "SEK", "amount": "1"}, {"name": "CHF", "amount": "2"}], "zones": [{"name": "Perf_Zone"}], "blackLists": [{"name": "UN Iran"}], "fileName": "fatf_Tc14.txt", "encoding": "UTF8", "format": "Generic Text", "results": "Only blocked records", "expectedMessage": "", "action": "save"}, {"testCaseTitle": "Domestic message with Field57 &52  same Country Code- Field 59 & reference are not violated ", "fieldMinimumLineSize": "2", "enableFatfRecommendation": "true", "rank": "50", "europeanCodes": "SEK,CHF", "enableCheckOfUniqueTransactionReferenceNumber": "false", "informationAvailableWithinTransaction": "false", "informationAvailableOutsideTheTransaction": "true", "informationAvailableForBics": "false", "bics": "", "applyForCrossBorderTraffic": "false", "currencies": [{"name": "SEK", "amount": "1"}, {"name": "CHF", "amount": "2"}], "zones": [{"name": "Perf_Zone"}], "blackLists": [{"name": "UN Iran"}], "fileName": "fatf_Tc15.txt", "encoding": "UTF8", "format": "Generic Text", "results": "Only blocked records", "expectedMessage": "", "action": "save"}, {"testCaseTitle": "Domestic Messages _MT202COV__59 ben account  and refernce are violated  ", "fieldMinimumLineSize": "2", "enableFatfRecommendation": "true", "rank": "50", "europeanCodes": "SEK,CHF", "enableCheckOfUniqueTransactionReferenceNumber": "false", "informationAvailableWithinTransaction": "false", "informationAvailableOutsideTheTransaction": "true", "informationAvailableForBics": "false", "bics": "", "applyForCrossBorderTraffic": "false", "currencies": [{"name": "SEK", "amount": "1"}, {"name": "CHF", "amount": "2"}], "zones": [{"name": "Perf_Zone"}], "blackLists": [{"name": "UN Iran"}], "fileName": "fatf_Tc16.txt", "encoding": "UTF8", "format": "Generic Text", "results": "Only blocked records", "expectedMessage": "", "action": "save"}, {"testCaseTitle": "Cross boarder message _Sender and receiver 57A not in EU codes_field 50F is violated Refernce is not violated and checked ", "fieldMinimumLineSize": "2", "enableFatfRecommendation": "true", "rank": "50", "europeanCodes": "SEK,CHF", "enableCheckOfUniqueTransactionReferenceNumber": "false", "informationAvailableWithinTransaction": "false", "informationAvailableOutsideTheTransaction": "true", "informationAvailableForBics": "false", "bics": "", "applyForCrossBorderTraffic": "true", "currencies": [{"name": "SEK", "amount": "1"}, {"name": "CHF", "amount": "2"}], "zones": [{"name": "Perf_Zone"}], "blackLists": [{"name": "UN Iran"}], "fileName": "fatf_Tc17.txt", "encoding": "UTF8", "format": "Generic Text", "results": "Only blocked records", "expectedMessage": "", "action": "save"}, {"testCaseTitle": "Cross boarder message _Sender and receiver 57A not in EU codes_field 50F is violated Refernce is not violated but not checked ", "fieldMinimumLineSize": "2", "enableFatfRecommendation": "true", "rank": "50", "europeanCodes": "SEK,CHF", "enableCheckOfUniqueTransactionReferenceNumber": "false", "informationAvailableWithinTransaction": "false", "informationAvailableOutsideTheTransaction": "true", "informationAvailableForBics": "false", "bics": "", "applyForCrossBorderTraffic": "true", "currencies": [{"name": "SEK", "amount": "1"}, {"name": "CHF", "amount": "2"}], "zones": [{"name": "Perf_Zone"}], "blackLists": [{"name": "UN Iran"}], "fileName": "fatf_Tc18.txt", "encoding": "UTF8", "format": "Generic Text", "results": "Only blocked records", "expectedMessage": "", "action": "save"}, {"testCaseTitle": "Cross boarder message _Sender and receiver 57A not in EU codes_field 50F PartyIdentifier with valid code  ", "fieldMinimumLineSize": "2", "enableFatfRecommendation": "true", "rank": "50", "europeanCodes": "SEK,CHF", "enableCheckOfUniqueTransactionReferenceNumber": "false", "informationAvailableWithinTransaction": "false", "informationAvailableOutsideTheTransaction": "true", "informationAvailableForBics": "false", "bics": "", "applyForCrossBorderTraffic": "true", "currencies": [{"name": "SEK", "amount": "1"}, {"name": "CHF", "amount": "2"}], "zones": [{"name": "Perf_Zone"}], "blackLists": [{"name": "UN Iran"}], "fileName": "fatf_Tc19.txt", "encoding": "UTF8", "format": "Generic Text", "results": "Only blocked records", "expectedMessage": "", "action": "save"}, {"testCaseTitle": "Cross boarder message _Sender and receiver 57A not in EU codes_field 50F PartyIdentifier is invalid Refernce is not violated and checked ", "fieldMinimumLineSize": "2", "enableFatfRecommendation": "true", "rank": "50", "europeanCodes": "SEK,CHF", "enableCheckOfUniqueTransactionReferenceNumber": "false", "informationAvailableWithinTransaction": "false", "informationAvailableOutsideTheTransaction": "true", "informationAvailableForBics": "false", "bics": "", "applyForCrossBorderTraffic": "true", "currencies": [{"name": "SEK", "amount": "1"}, {"name": "CHF", "amount": "2"}], "zones": [{"name": "Perf_Zone"}], "blackLists": [{"name": "UN Iran"}], "fileName": "fatf_Tc20.txt", "encoding": "UTF8", "format": "Generic Text", "results": "Only blocked records", "expectedMessage": "", "action": "save"}, {"testCaseTitle": "Cross boarder message_Field50K_name is violated  ", "fieldMinimumLineSize": "2", "enableFatfRecommendation": "true", "rank": "50", "europeanCodes": "SEK,CHF", "enableCheckOfUniqueTransactionReferenceNumber": "false", "informationAvailableWithinTransaction": "false", "informationAvailableOutsideTheTransaction": "true", "informationAvailableForBics": "false", "bics": "", "applyForCrossBorderTraffic": "true", "currencies": [{"name": "SEK", "amount": "1"}, {"name": "CHF", "amount": "2"}], "zones": [{"name": "Perf_Zone"}], "blackLists": [{"name": "UN Iran"}], "fileName": "fatf_Tc21.txt", "encoding": "UTF8", "format": "Generic Text", "results": "Only blocked records", "expectedMessage": "", "action": "save"}, {"testCaseTitle": "Cross boarder message_Field 50K_address_only we have line 3 and its not violated ", "fieldMinimumLineSize": "2", "enableFatfRecommendation": "true", "rank": "50", "europeanCodes": "SEK,CHF", "enableCheckOfUniqueTransactionReferenceNumber": "false", "informationAvailableWithinTransaction": "false", "informationAvailableOutsideTheTransaction": "true", "informationAvailableForBics": "false", "bics": "", "applyForCrossBorderTraffic": "true", "currencies": [{"name": "SEK", "amount": "1"}, {"name": "CHF", "amount": "2"}], "zones": [{"name": "Perf_Zone"}], "blackLists": [{"name": "UN Iran"}], "fileName": "fatf_Tc22.txt", "encoding": "UTF8", "format": "Generic Text", "results": "Only blocked records", "expectedMessage": "", "action": "save"}, {"testCaseTitle": "Cross boarder message_Field 50K_address_Debit name is shifted and we have only on line address ", "fieldMinimumLineSize": "2", "enableFatfRecommendation": "true", "rank": "50", "europeanCodes": "SEK,CHF", "enableCheckOfUniqueTransactionReferenceNumber": "false", "informationAvailableWithinTransaction": "false", "informationAvailableOutsideTheTransaction": "true", "informationAvailableForBics": "false", "bics": "", "applyForCrossBorderTraffic": "true", "currencies": [{"name": "SEK", "amount": "1"}, {"name": "CHF", "amount": "2"}], "zones": [{"name": "Perf_Zone"}], "blackLists": [{"name": "UN Iran"}], "fileName": "fatf_Tc23.txt", "encoding": "UTF8", "format": "Generic Text", "results": "Only blocked records", "expectedMessage": "", "action": "save"}, {"testCaseTitle": "Cross boarder message_Field 50F_address_2nd and 3rd line are not violated", "fieldMinimumLineSize": "2", "enableFatfRecommendation": "true", "rank": "50", "europeanCodes": "SEK,CHF", "enableCheckOfUniqueTransactionReferenceNumber": "false", "informationAvailableWithinTransaction": "false", "informationAvailableOutsideTheTransaction": "true", "informationAvailableForBics": "false", "bics": "", "applyForCrossBorderTraffic": "true", "currencies": [{"name": "SEK", "amount": "1"}, {"name": "CHF", "amount": "2"}], "zones": [{"name": "Perf_Zone"}], "blackLists": [{"name": "UN Iran"}], "fileName": "fatf_Tc24.txt", "encoding": "UTF8", "format": "Generic Text", "results": "Only blocked records", "expectedMessage": "", "action": "save"}, {"testCaseTitle": "Cross boarder message_Field 50F_address 7th line is not violated ", "fieldMinimumLineSize": "2", "enableFatfRecommendation": "true", "rank": "50", "europeanCodes": "SEK,CHF", "enableCheckOfUniqueTransactionReferenceNumber": "false", "informationAvailableWithinTransaction": "false", "informationAvailableOutsideTheTransaction": "true", "informationAvailableForBics": "false", "bics": "", "applyForCrossBorderTraffic": "true", "currencies": [{"name": "SEK", "amount": "1"}, {"name": "CHF", "amount": "2"}], "zones": [{"name": "Perf_Zone"}], "blackLists": [{"name": "UN Iran"}], "fileName": "fatf_Tc25.txt", "encoding": "UTF8", "format": "Generic Text", "results": "Only blocked records", "expectedMessage": "", "action": "save"}, {"testCaseTitle": "Cross boarder message_Field 50F_address_2nd and 3rd line are violated", "fieldMinimumLineSize": "2", "enableFatfRecommendation": "true", "rank": "50", "europeanCodes": "SEK,CHF", "enableCheckOfUniqueTransactionReferenceNumber": "false", "informationAvailableWithinTransaction": "false", "informationAvailableOutsideTheTransaction": "true", "informationAvailableForBics": "false", "bics": "", "applyForCrossBorderTraffic": "true", "currencies": [{"name": "SEK", "amount": "1"}, {"name": "CHF", "amount": "2"}], "zones": [{"name": "Perf_Zone"}], "blackLists": [{"name": "UN Iran"}], "fileName": "fatf_Tc26.txt", "encoding": "UTF8", "format": "Generic Text", "results": "Only blocked records", "expectedMessage": "", "action": "save"}, {"testCaseTitle": "Cross boarder message_Filed59_Name_not violated", "fieldMinimumLineSize": "2", "enableFatfRecommendation": "true", "rank": "50", "europeanCodes": "SEK,CHF", "enableCheckOfUniqueTransactionReferenceNumber": "false", "informationAvailableWithinTransaction": "false", "informationAvailableOutsideTheTransaction": "true", "informationAvailableForBics": "false", "bics": "", "applyForCrossBorderTraffic": "true", "currencies": [{"name": "SEK", "amount": "1"}, {"name": "CHF", "amount": "2"}], "zones": [{"name": "Perf_Zone"}], "blackLists": [{"name": "UN Iran"}], "fileName": "fatf_Tc27.txt", "encoding": "UTF8", "format": "Generic Text", "results": "Only blocked records", "expectedMessage": "", "action": "save"}, {"testCaseTitle": "Cross boarder message_Filed59_Name_ violated", "fieldMinimumLineSize": "2", "enableFatfRecommendation": "true", "rank": "50", "europeanCodes": "SEK,CHF", "enableCheckOfUniqueTransactionReferenceNumber": "false", "informationAvailableWithinTransaction": "false", "informationAvailableOutsideTheTransaction": "true", "informationAvailableForBics": "false", "bics": "", "applyForCrossBorderTraffic": "true", "currencies": [{"name": "SEK", "amount": "1"}, {"name": "CHF", "amount": "2"}], "zones": [{"name": "Perf_Zone"}], "blackLists": [{"name": "UN Iran"}], "fileName": "fatf_Tc28.txt", "encoding": "UTF8", "format": "Generic Text", "results": "Only blocked records", "expectedMessage": "", "action": "save"}, {"testCaseTitle": "Cross boarder message_Field59A_BIC_withoutAccount_with_Reference violation", "fieldMinimumLineSize": "2", "enableFatfRecommendation": "true", "rank": "50", "europeanCodes": "SEK,CHF", "enableCheckOfUniqueTransactionReferenceNumber": "false", "informationAvailableWithinTransaction": "false", "informationAvailableOutsideTheTransaction": "true", "informationAvailableForBics": "false", "bics": "", "applyForCrossBorderTraffic": "true", "currencies": [{"name": "SEK", "amount": "1"}, {"name": "CHF", "amount": "2"}], "zones": [{"name": "Perf_Zone"}], "blackLists": [{"name": "UN Iran"}], "fileName": "fatf_Tc29.txt", "encoding": "UTF8", "format": "Generic Text", "results": "Only blocked records", "expectedMessage": "", "action": "save"}, {"testCaseTitle": "Domestic Messages _MT202COV__59F ben account not violated ", "fieldMinimumLineSize": "2", "enableFatfRecommendation": "true", "rank": "50", "europeanCodes": "SEK,CHF", "enableCheckOfUniqueTransactionReferenceNumber": "false", "informationAvailableWithinTransaction": "false", "informationAvailableOutsideTheTransaction": "true", "informationAvailableForBics": "false", "bics": "", "applyForCrossBorderTraffic": "false", "currencies": [{"name": "SEK", "amount": "1"}, {"name": "CHF", "amount": "2"}], "zones": [{"name": "Perf_Zone"}], "blackLists": [{"name": "UN Iran"}], "fileName": "fatf_Tc30.txt", "encoding": "UTF8", "format": "Generic Text", "results": "Only blocked records", "expectedMessage": "", "action": "save"}, {"testCaseTitle": "Domestic Messages _MT202COV_50K_accontOnlyIsViolated", "fieldMinimumLineSize": "2", "enableFatfRecommendation": "true", "rank": "50", "europeanCodes": "SEK,CHF", "enableCheckOfUniqueTransactionReferenceNumber": "false", "informationAvailableWithinTransaction": "false", "informationAvailableOutsideTheTransaction": "true", "informationAvailableForBics": "false", "bics": "", "applyForCrossBorderTraffic": "false", "currencies": [{"name": "SEK", "amount": "1"}, {"name": "CHF", "amount": "2"}], "zones": [{"name": "Perf_Zone"}], "blackLists": [{"name": "UN Iran"}], "fileName": "fatf_Tc31.txt", "encoding": "UTF8", "format": "Generic Text", "results": "Only blocked records", "expectedMessage": "", "action": "save"}, {"testCaseTitle": "Cross Borader _MT202COV_50K_NoAddress", "fieldMinimumLineSize": "2", "enableFatfRecommendation": "true", "rank": "50", "europeanCodes": "SEK,CHF", "enableCheckOfUniqueTransactionReferenceNumber": "false", "informationAvailableWithinTransaction": "false", "informationAvailableOutsideTheTransaction": "true", "informationAvailableForBics": "false", "bics": "", "applyForCrossBorderTraffic": "true", "currencies": [{"name": "SEK", "amount": "1"}, {"name": "CHF", "amount": "2"}], "zones": [{"name": "Perf_Zone"}], "blackLists": [{"name": "UN Iran"}], "fileName": "fatf_Tc32.txt", "encoding": "UTF8", "format": "Generic Text", "results": "Only blocked records", "expectedMessage": "", "action": "save"}, {"testCaseTitle": "Cross Boradre _MT205COV_59F_Benefeciary is violated", "fieldMinimumLineSize": "2", "enableFatfRecommendation": "true", "rank": "50", "europeanCodes": "SEK,CHF", "enableCheckOfUniqueTransactionReferenceNumber": "false", "informationAvailableWithinTransaction": "false", "informationAvailableOutsideTheTransaction": "true", "informationAvailableForBics": "false", "bics": "", "applyForCrossBorderTraffic": "true", "currencies": [{"name": "SEK", "amount": "1"}, {"name": "CHF", "amount": "2"}], "zones": [{"name": "Perf_Zone"}], "blackLists": [{"name": "UN Iran"}], "fileName": "fatf_Tc33.txt", "encoding": "UTF8", "format": "Generic Text", "results": "Only blocked records", "expectedMessage": "", "action": "save"}]