@echo off
SETLOCAL



REM =======================================
REM  !! PLEASE DO NOT MODIFY BELOW LINES !!
REM =======================================



set LOCAL_DIR=%~dp0



rem set JAVA_HOME=%LOCAL_DIR%\..\java\jre1.8.0_45
rem set PATH=%JAVA_HOME%\bin;%PATH%



rem java -jar dj-cli-5.0.3.jar %* connectString=localhost:2181 swlUri=Login_FileF8010.swl pfaUri="C:\Regression_5.x\DataFiles\AMLBOXLists\PFA2_201706302200_F.xml" loadGroupReferentialOnly=false loadReferentialOnly=true swMulticastPort=8400 swMulticastId=***************\SWF selectionSetName="BlackList_F8737" upgradeBlackListVersion=true lockPreviousBlackListVersions=false loadOriginalScriptName=true loadLowQualityAkaName=false
java -jar dj-cli-5.2.0.jar %* connectString=localhost:2181 swlUri=LoginFileName36892095.swl pfaUri=D:/SWF_Automation/SWS_selenium/src/test/resources/DataFiles/DowJonesDataFiles/PFA2_201912222200_I.xml loadGroupReferentialOnly=false loadReferentialOnly=true swMulticastPort=1433 swMulticastId=********** selectionSetName=ListName-52402931 upgradeBlackListVersion=true lockPreviousBlackListVersions=false loadOriginalScriptName=true loadLowQualityAkaName=false loadCoreFile=true extraAKAs=false



ENDLOCAL