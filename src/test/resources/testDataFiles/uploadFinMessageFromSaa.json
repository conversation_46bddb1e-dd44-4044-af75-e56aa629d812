[{"testCaseTitle": "Block action Click to block the detection from AMLUI", "testMethod": "uploadFile", "messageType": "MT103", "entityName": "<PERSON><PERSON>", "action": "realViolation", "detectionStatus": "RV", "expectedMessage": "1 detection(s) successfully modified!"}, {"testCaseTitle": "Release Click to Release the detection from AMLUI  ", "testMethod": "uploadFile", "messageType": "MT202", "entityName": "<PERSON><PERSON>", "action": "falsePositive", "detectionStatus": "FP", "expectedMessage": "1 detection(s) successfully modified!"}, {"testCaseTitle": "Don't Know Click on Don’t know button of the detection from AMLUI  ", "testMethod": "uploadFile", "messageType": "MT205", "entityName": "valid", "action": "dontKnow", "detectionStatus": "DK", "expectedMessage": "1 detection(s) successfully modified!"}, {"testCaseTitle": "Pending Click on Pending the detection from AMLUI", "testMethod": "uploadFile", "messageType": "MT103", "entityName": "valid", "action": "pending", "detectionStatus": "PEN", "expectedMessage": "1 detection(s) successfully modified!"}, {"testCaseTitle": "No Detection ", "testMethod": "uploadFile", "messageType": "MT205", "entityName": "clean", "action": "clean", "detectionStatus": "CLEAN", "expectedMessage": "CLEAN"}]