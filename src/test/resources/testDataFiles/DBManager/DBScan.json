[{"testCaseTitle": "Add DB flow - Verify that user can create new DB flow and save it 'with Input type'", "name": "Flow-%s", "type": "INPUT", "tableName": "DBScan", "expectedMessage": "", "dbFields": [{"name": "ID ", "primaryKeyFlag": true}, {"name": "FirstName ", "type": "FIRST_NAME", "scanFlag": true}, {"name": "LastName ", "type": "LAST_NAME", "scanFlag": true}]}, {"testCaseTitle": "Add DB flow - Verify that user can create new DB flow and save it 'with Output type'", "name": "Flow-%s", "type": "OUTPUT", "tableName": "DBScan", "expectedMessage": "", "dbFields": [{"name": "ID ", "primaryKeyFlag": true}, {"name": "FirstName ", "type": "FIRST_NAME"}, {"name": "LastName ", "type": "LAST_NAME"}]}, {"testCaseTitle": "Add DB flow - Verify that user can create new DB flow and save it 'INPUT type with output flow'", "name": "Flow-%s", "type": "INPUT", "outputFlow": 1, "tableName": "DBScan", "expectedMessage": "", "dbFields": [{"name": "ID ", "primaryKeyFlag": true}, {"name": "FirstName ", "type": "FIRST_NAME", "scanFlag": true}, {"name": "LastName ", "type": "LAST_NAME", "scanFlag": true}]}]