[{"testCaseTitle": "Verify that user is not able to Edit a User when 'Allow to modify all operators' permission is not granted.", "scriptName": "Regression_Admin_10", "permissionName": "Allow to modify all operators and enable some admin functions", "profileName": "selenium-full-right-profile ", "functionToCheck": "EditUser"}, {"testCaseTitle": "Verify that user is not able to Create a User when 'Allow to create new operators' permission is not granted.", "scriptName": "Regression_Admin_11", "permissionName": "Allow to create new operators", "profileName": "selenium-full-right-profile ", "functionToCheck": "CreateUser"}, {"testCaseTitle": "Verify that user is not able to Delete a User when 'Allow to delete' permission is not granted.", "scriptName": "Regression_Admin_12", "permissionName": "Allow to delete", "profileName": "selenium-full-right-profile ", "functionToCheck": "DeleteUser"}, {"testCaseTitle": "Verify that user is not able to Edit a User when 'Allow to modify own operator' permission is not granted.", "scriptName": "Regression_Admin_13", "permissionName": "Allow to modify own operator", "profileName": "selenium-full-right-profile ", "functionToCheck": "EditOwnOperator"}, {"testCaseTitle": "Verify that user is not able to Enable a User when 'Allow to enable operator' permission is not granted for Editing an operator not granted", "scriptName": "Regression_Admin_14", "permissionName": "Allow to enable operator", "profileName": "selenium-full-right-profile ", "functionToCheck": "EnableOperatorWhileEditing"}, {"testCaseTitle": "Verify that user is not able to Enable a User when 'Allow to enable operator' permission is not granted for creating an operator.", "scriptName": "Regression_Admin_15", "permissionName": "Allow to enable operator", "profileName": "selenium-full-right-profile ", "functionToCheck": "EnableOperatorWhileCreation"}]