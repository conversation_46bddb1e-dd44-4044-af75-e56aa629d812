[{"testCaseTitle": "Verify that password like \"aaa111\", \"aaaaaa\", \"111111\", \"9876543210\", \"qponmlk\", \"cba321\" should be accepted while the Prevent Consecutive is checked as it shouldn't be considered consecutive or sequential", "scriptName": "Regression_PasswordPolicy_15", "minimumUpperCaseCount": "0", "minimumLowerCaseCount": "0", "minimumDigitsCount": "0", "minimumSpecialCharactersCount": "0", "preventConsecutiveFlag": "true", "statusFlag": "true", "newPasswordList": ["aaa111", "aaaaaa", "111111", "9876543210", "qponmlk", "cba321"], "historyCount": "0", "expectedMessage": "The paswword has been successfully changed."}, {"testCaseTitle": "Validate that password is allowed to have 2 sequential numbers or alphabets or both if \"prevent consecutive\" is checked", "scriptName": "Regression_PasswordPolicy_16", "minimumUpperCaseCount": "0", "minimumLowerCaseCount": "0", "minimumDigitsCount": "0", "minimumSpecialCharactersCount": "0", "preventConsecutiveFlag": "true", "statusFlag": "true", "newPasswordList": ["121212", "ababab", "1245ab"], "historyCount": "0", "expectedMessage": "The paswword has been successfully changed."}, {"testCaseTitle": "Validate that password is allowed to have sequential numbers or alphabets or both if \"prevent consecutive\" is not checked", "scriptName": "Regression_PasswordPolicy_17", "minimumUpperCaseCount": "0", "minimumLowerCaseCount": "0", "minimumDigitsCount": "0", "minimumSpecialCharactersCount": "0", "preventConsecutiveFlag": "false", "statusFlag": "true", "newPasswordList": ["124124", "a<PERSON><PERSON><PERSON><PERSON>", "124abd"], "historyCount": "0", "expectedMessage": "The paswword has been successfully changed."}]