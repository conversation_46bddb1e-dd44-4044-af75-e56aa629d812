[{"testCaseTitle": "Verify Daily User Activity Report", "scriptName": "AdminReports_01", "reportName": "DailyUserActivity", "comment": "Comment%s", "userName": "UserName%s", "action": "Login_Logout", "validationString": "Daily User Activity|User [#UserName#] successfully logged out|User [#UserName#] successfully logged in"}, {"testCaseTitle": "Verify Delete Operators Report", "scriptName": "AdminReports_02", "reportName": "Deleted Operators", "comment": "Comment%s", "action": "deleteOperator", "validationString": "Deleted Operators|#DeletedOperatorName#"}, {"testCaseTitle": "Verify Event List Report", "scriptName": "AdminReports_03", "reportName": "Event List", "comment": "Comment%s", "validationString": "Events|User [sysadmin] successfully logged in"}, {"testCaseTitle": "Verify Failed Logins Report", "scriptName": "AdminReports_04", "reportName": "Failed<PERSON><PERSON><PERSON>", "comment": "Comment%s", "userName": "UserName%s", "action": "InvalidLogin", "validationString": "Failed Logins|Bad password given for user [#UserName#]"}, {"testCaseTitle": "Verify Operator Profiles and Rights Report", "scriptName": "AdminReports_05", "reportName": "Operator Profiles and Rights", "comment": "Comment%s", "operator": "sysadmin", "validationString": "Operator Profiles and Access rights|Operator name sysadmin|[Administrators Profile]"}, {"testCaseTitle": "Verify Operators List Report", "scriptName": "AdminReports_06", "reportName": "Operators List", "comment": "Comment%s", "operator": "sysadmin", "profile": "[Administrators Profile]", "zone": "[System Zone]", "validationString": "Operators List|Operator Name sysadmin|Zone [System Zone]|[Administrators Profile]"}, {"testCaseTitle": "Verify Profile Access Rights", "scriptName": "AdminReports_07", "reportName": "Profile Access Rights", "comment": "Comment%s", "zone": "[System Zone]", "profile": "[Administrators Profile]", "validationString": "Profile Access Rights|Zone [System Zone]|Profile [Administrators Profile]"}, {"testCaseTitle": "Verify Profiles Comparison", "scriptName": "AdminReports_08", "reportName": "Profiles Comparison", "comment": "Comment%s", "profile1": "[Administrators Profile]", "profile2": "[Default Profile]", "action": "removeAccess", "validationString": "Profiles Comparison|1st Profile #Profile1#|2nd Profile #Profile2#"}, {"testCaseTitle": "Verify Profile Lists", "scriptName": "AdminReports_09", "reportName": "Profiles List", "comment": "Comment%s", "profile": "[Administrators Profile]", "validationString": "Profiles List|[Administrators Profile]|[System Zone]"}, {"testCaseTitle": "Verify Daily User Activity Report", "scriptName": "AdminReports_10", "reportName": "DailyUserActivity", "comment": "Comment%s", "userName": "UserName%s", "action": "AddGoodGuy", "validationString": "Daily User Activity|New text has been added as a good guy"}]