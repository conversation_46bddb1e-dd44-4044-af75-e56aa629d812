package eastnets.screening.control;

import core.gui.Controls;
import core.util.TextFilesHandler;
import core.util.Wait;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.Navigation;
import eastnets.screening.entity.ISO20022FormatConfiguration;
import eastnets.screening.entity.ISO20022FormatDetailsConfiguration;
import eastnets.screening.entity.ISO20022SchemaConfiguration;
import eastnets.screening.gui.iso20022Manager.*;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;

public class ISO20022Control {

    private final ISO20022SchemaConfigurationManager iso20022SchemaConfigurationManager;
    private final ISO20022FormatConfigurationEditor iso20022FormatConfigurationEditor;
    private final ISO20022FormatConfigurationManager iso20022FormatConfigurationManager;
    private final ISO20022SchemaConfigurationEditor iso20022SchemaConfigurationEditor;
    private final CommonAction commonAction;
    private final Controls controls;
    private final Wait wait;
    private final TextFilesHandler textFilesHandler;

    public ISO20022Control() {
        this.iso20022SchemaConfigurationManager = new ISO20022SchemaConfigurationManager();
        this.iso20022FormatConfigurationEditor = new ISO20022FormatConfigurationEditor();
        this.iso20022FormatConfigurationManager = new ISO20022FormatConfigurationManager();
        this.iso20022SchemaConfigurationEditor = new ISO20022SchemaConfigurationEditor();
        this.commonAction = new CommonAction();
        this.controls = new Controls();
        this.wait = new Wait();
        this.textFilesHandler = new TextFilesHandler();
    }

    @Step("Navigate To ISO20022 Format Configuration")
    public void navigateToISO20022FormatManager(RemoteWebDriver driver) {
        Navigation.ISO_20022_MANAGER.navigate(driver);
        ISONavigation.FORMAT_CONFIGURATION.navigate(driver);
    }

    @Step("Navigate To ISO20022 Schema Configuration")
    public void navigateToISO20022SchemaManager(RemoteWebDriver driver) {
        Navigation.ISO_20022_MANAGER.navigate(driver);
        ISONavigation.ISO_20022_SCHEMA_CONFIGURATION.navigate(driver);
        ISONavigation.SCHEMA_CONFIGURATION.navigate(driver);
    }

    public void navigateToSchemaConfiguration(RemoteWebDriver driver) {
        ISONavigation.SCHEMA_CONFIGURATION.navigate(driver);
    }

    @Step("Navigate To Pre Mapped  Configuration")
    public void navigateToPreMappedFieldsConfiguration(RemoteWebDriver driver) {
        Navigation.ISO_20022_MANAGER.navigate(driver);
        ISONavigation.ISO_20022_SCHEMA_CONFIGURATION.navigate(driver);
        ISONavigation.PRE_MAPPED_FIELDS_CONFIGURATION.navigate(driver);
    }

    @Step("Delete schema")
    public String deleteSchema(RemoteWebDriver driver, String schemaName) throws InterruptedException {
        navigateToISO20022SchemaManager(driver);
        if (iso20022SchemaConfigurationManager.verifySchemaExist(driver, schemaName))
            return iso20022SchemaConfigurationManager.deleteSchemaVersion(driver, schemaName);
        return "No schema Found";
    }

    @Step("Delete ISO20022 message")
    public String deleteISO20022Message(RemoteWebDriver driver, String schemaName) throws InterruptedException {
        Allure.step("Delete schema with name = " + schemaName);
        return iso20022FormatConfigurationEditor.clickRemoveButton(driver);
    }

    @Step("Import schema")
    public String importSchema(RemoteWebDriver driver, String schemaPath) throws InterruptedException {
        iso20022SchemaConfigurationManager.browseSchema(driver, schemaPath);
        return iso20022SchemaConfigurationManager.clickImportButton(driver);
    }

    @Step("Create group")
    public String createGroup(RemoteWebDriver driver, ISO20022FormatConfiguration formatConfiguration) throws Exception {
        navigateToISO20022FormatManager(driver);
        iso20022FormatConfigurationManager.clickSearchButton(driver);
        iso20022FormatConfigurationManager.clickAddButton(driver);
        Allure.step("Create New Group.");
        iso20022FormatConfigurationManager.scrollToSaveGroupButton(driver);
        iso20022FormatConfigurationManager.selectGroupZone(driver, formatConfiguration);
        iso20022FormatConfigurationManager.setGroupName(driver, formatConfiguration);
        iso20022FormatConfigurationManager.clickEnableGroupButton(driver);
        return iso20022FormatConfigurationManager.clickSaveGroupButton(driver);

    }

    @Step("Click configure")
    public void clickConfigure(RemoteWebDriver driver) throws Exception {
        Allure.step("Sort table results by creation to click on the latest created group.");
        iso20022FormatConfigurationManager.clickSearchButton(driver);
        iso20022FormatConfigurationManager.sortByCreationDateButton(driver);
        iso20022FormatConfigurationManager.click_check_box(driver);
        iso20022FormatConfigurationManager.clickConfigureButton(driver);
    }

    @Step("Create new message")
    public String createNewMessage(RemoteWebDriver driver, ISO20022SchemaConfiguration ISO20022SchemaConfiguration, String SwiftHeader) throws Exception {
        iso20022FormatConfigurationEditor.clickAddButton(driver);
        controls.scrollDown(driver);
        wait.waitForJStoLoad(driver);
        String schemaName = ISO20022SchemaConfiguration.getSchemaName();
        String schemaVersion = ISO20022SchemaConfiguration.getSchemaVersion();
        String schemaWrapper = ISO20022SchemaConfiguration.getSchemaWrapper();
        iso20022FormatConfigurationEditor.selectSchemaName(driver, schemaName);
        iso20022FormatConfigurationEditor.selectSchemaVersion(driver, schemaName, schemaVersion);

        if (schemaWrapper != null) {
            iso20022FormatConfigurationEditor.selectSchemaWrapper(driver, schemaWrapper);
        }

        if (SwiftHeader != null) {
            iso20022FormatConfigurationEditor.selectMessageSwift(driver, SwiftHeader);
        }
        iso20022FormatConfigurationEditor.clickFormatCheckBox(driver);
        iso20022FormatConfigurationEditor.clickEnableButton(driver);
        iso20022FormatConfigurationEditor.clickSaveButton(driver);
        String message = commonAction.getAlertMessageString(driver);
        Allure.step("Actual result = " + message);
        return message;
    }

    @Step("Import GroupConfigurations")
    public String importConfigurations(RemoteWebDriver driver, String filepath) throws Exception {
        iso20022FormatConfigurationEditor.clickImportButton(driver);
        return iso20022FormatConfigurationEditor.importConfigurations(driver, filepath);
    }

    @Step("Add Body Field")
    public String addBodyField(RemoteWebDriver driver, ISO20022FormatDetailsConfiguration ISO20022FormatDetailsConfiguration) throws Exception {
        iso20022FormatConfigurationEditor.clickAddBodyFieldButton(driver);
        iso20022FormatConfigurationEditor.fillFieldNameTextBox(driver, ISO20022FormatDetailsConfiguration.getBodyField());
        iso20022FormatConfigurationEditor.fillXpathTextBox(driver, ISO20022FormatDetailsConfiguration.getXpath());
        iso20022FormatConfigurationEditor.checkScanCheckBox(driver);
        return iso20022FormatConfigurationEditor.saveField(driver);

    }

    @Step("Select Header Field")
    public void selectHeaderField(RemoteWebDriver driver, String headerFieldName, ISO20022FormatDetailsConfiguration ISO20022FormatDetailsConfiguration) throws Exception {
        iso20022FormatConfigurationEditor.searchHeaderField(driver, headerFieldName);
        iso20022FormatConfigurationEditor.selectField(driver, headerFieldName, ISO20022FormatDetailsConfiguration);
    }

    @Step("Select Body Field")
    public void selectBodyField(RemoteWebDriver driver, String bodyFieldName, ISO20022FormatDetailsConfiguration ISO20022FormatDetailsConfiguration) throws Exception {
        iso20022FormatConfigurationEditor.searchBodyField(driver, bodyFieldName);
        iso20022FormatConfigurationEditor.selectField(driver, bodyFieldName, ISO20022FormatDetailsConfiguration);
    }

    @Step("Export ISO message")
    public Boolean exportISOMessage(RemoteWebDriver driver, ISO20022SchemaConfiguration ISO20022Schema, String prefix, String hub) throws Exception {
        iso20022FormatConfigurationEditor.checkMessageNameCheckbox(driver, ISO20022Schema.getSchemaName());
        iso20022FormatConfigurationEditor.clickExportButton(driver);
        iso20022FormatConfigurationEditor.clickExportButton(driver);
        return textFilesHandler.checkExistOfFileWithDynamicName(driver, prefix, hub);
    }

    @Step("Merge ISO message")
    public String mergeISOMessage(RemoteWebDriver driver, String fileName) throws Exception {
        iso20022FormatConfigurationEditor.clickMergeCustomXSDButton(driver);

        return iso20022FormatConfigurationEditor.mergeCustomXSD(driver, fileName);
    }

    @Step("Update Category")
    public String updateCategory(RemoteWebDriver driver, String schemaName, String fieldname, String category) throws Exception {
        iso20022FormatConfigurationEditor.checkMessageNameCheckbox(driver, schemaName);
        iso20022FormatConfigurationEditor.clickDetailsButton(driver);
        iso20022FormatConfigurationEditor.searchBodyField(driver, fieldname);
        iso20022FormatConfigurationEditor.clickFirstItemInBodySearch(driver);
        iso20022FormatConfigurationEditor.selectCategory(driver, category);
        return iso20022FormatConfigurationEditor.clickUpdateButton(driver);

    }

    @Step("Update Field Type")
    public String updateFieldType(RemoteWebDriver driver, String schemaName, String fieldname, String fieldType) throws Exception {
        iso20022FormatConfigurationEditor.checkMessageNameCheckbox(driver, schemaName);
        iso20022FormatConfigurationEditor.clickDetailsButton(driver);
        iso20022FormatConfigurationEditor.searchBodyField(driver, fieldname);
        iso20022FormatConfigurationEditor.clickFirstItemInBodySearch(driver);
        iso20022FormatConfigurationEditor.selectFieldType(driver, fieldType);
        iso20022FormatConfigurationEditor.checkScanCheckBox(driver);
        return iso20022FormatConfigurationEditor.clickUpdateButton(driver);
    }

    @Step("Change Xsd element")
    public void addXSDElement(RemoteWebDriver driver, String xsdElementType, String fieldType) throws Exception {
        navigateToPreMappedFieldsConfiguration(driver);
        iso20022SchemaConfigurationEditor.clickAddButton(driver);
        iso20022SchemaConfigurationEditor.setXsdElementType(driver, xsdElementType);
        iso20022SchemaConfigurationEditor.chooseFieldType(driver, fieldType);
        iso20022SchemaConfigurationEditor.checkScanCheckbox(driver);
        iso20022SchemaConfigurationEditor.checkAddToContextCheckbox(driver);
        iso20022SchemaConfigurationEditor.clickSaveButton(driver);
    }

}
