package eastnets.screening.control.scanManger;

import eastnets.common.control.CommonAction;
import eastnets.common.gui.Navigation;
import eastnets.screening.entity.DBConfigurations;
import eastnets.screening.gui.scanManager.ScanManagerNavigation;
import eastnets.screening.gui.scanManager.databaseScan.DBScanManager;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;

public class DBScanControl {

    private final ScanManagerNavigation scanManagerNavigation;
    private final DBScanManager dbScanManager;
    private final CommonAction commonAction;

    public DBScanControl() {
        this.scanManagerNavigation = ScanManagerNavigation.DB_SCAN;
        this.dbScanManager = new DBScanManager();
        this.commonAction = new CommonAction();
    }

    @Step("Navigate to DB scan")
    private void navigateToDBScan(RemoteWebDriver driver) {
        Navigation.SCAN_MANAGER.navigate(driver);
        scanManagerNavigation.navigate(driver);
    }

    @Step("Search by Zone and List Set")
    public void search(RemoteWebDriver driver, DBConfigurations dbConfigurations) throws Exception {
        dbScanManager.selectZone(driver, dbConfigurations.getZoneName());
        dbScanManager.selectListSet(driver, dbConfigurations.getListSetName());
        dbScanManager.clickSearchButton(driver);

    }

    public String db_Scan(RemoteWebDriver driver, DBConfigurations dbConfigurations) throws Exception {
        String message = test_Connection(driver, dbConfigurations);
        if (message.contains("Successfully connected to Database")) {
            dbScanManager.set_password_for_input(driver, dbConfigurations.getPassword());
            dbScanManager.selectRunType(driver, "One Time");
            dbScanManager.setRunStartDate(driver);
            dbScanManager.startScan(driver);
            String validation_message = commonAction.getAlertMessageStringWithoutWaitForAjaxLoader(driver);
            System.out.println("Validation Message : " + validation_message);
            return validation_message;
        }
        Allure.step("Test Connection Failed");
        return message;

    }

    public String test_Connection(RemoteWebDriver driver, DBConfigurations dbConfigurations) throws Exception {
        navigateToDBScan(driver);
        search(driver, dbConfigurations);
        dbScanManager.selectDBFlow(driver, dbConfigurations.getName());
        dbScanManager.set_password_for_input(driver, dbConfigurations.getPassword());
        String test_input_validation_message = dbScanManager.click_test_connection_input_flow(driver);
        String test_output_validation_message = "";
        if (dbConfigurations.getType().equalsIgnoreCase("Input")) {
            dbScanManager.set_password_for_output(driver, dbConfigurations.getPassword());
             test_output_validation_message = dbScanManager.click_test_connection_output_flow(driver);
        }
        return test_input_validation_message + " " + test_output_validation_message;

    }

    @Step("Start set database login information")
    public String setLoginInformation(RemoteWebDriver driver, DBConfigurations dbConfigurations) {
        dbScanManager.setDatabaseName(driver, dbConfigurations.getDbName());
        dbScanManager.setDatabaseHost(driver, dbConfigurations.getHost());
        dbScanManager.setDatabasePort(driver, dbConfigurations.getPort());
        dbScanManager.setDatabaseUserName(driver, dbConfigurations.getUserName());
        dbScanManager.set_password_for_input(driver, dbConfigurations.getPassword());
        return dbScanManager.click_test_connection_input_flow(driver);
    }

    @Step("Start Set DB Flow Operator")
    public void setDBFlowsOperator(RemoteWebDriver driver, DBConfigurations dbConfigurations) throws Exception {
        dbScanManager.setFieldName(driver, dbConfigurations.getFieldName());
        dbScanManager.selectOperator(driver, dbConfigurations.getOperator());
        dbScanManager.setValue(driver, dbConfigurations.getValue());
        dbScanManager.selectDetectVesselsCheckBox(driver, dbConfigurations.getDetectVessels());
        dbScanManager.selectDetectCountriesCheckBox(driver, dbConfigurations.getDetectCountries());
        dbScanManager.clickSaveButton(driver);
    }

}
