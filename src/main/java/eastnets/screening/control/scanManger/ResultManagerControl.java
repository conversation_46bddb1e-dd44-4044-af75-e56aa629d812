package eastnets.screening.control.scanManger;

import core.util.TextFilesHandler;
import core.util.Wait;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.Navigation;
import eastnets.screening.gui.scanManager.ScanManagerNavigation;
import eastnets.screening.gui.scanManager.resultManager.ResultEditor;
import eastnets.screening.gui.scanManager.resultManager.ResultManager;
import eastnets.screening.gui.scanManager.scanName.NameScanEditor;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.net.MalformedURLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class ResultManagerControl {

    private final ScanManagerNavigation scanManagerNavigation;
    private final ResultManager resultManager;
    private final ResultEditor resultEditor;
    private final NameScanEditor nameScanEditor;
    private final CommonAction commonAction;
    private final Wait wait;
    private final TextFilesHandler textFilesHandler;
    private final NameScanControl nameScanControl;

    public ResultManagerControl() {
        this.scanManagerNavigation = ScanManagerNavigation.RESULT;
        this.resultManager = new ResultManager();
        this.resultEditor = new ResultEditor();
        this.nameScanEditor = new NameScanEditor();
        this.commonAction = new CommonAction();
        this.wait = new Wait();
        this.textFilesHandler = new TextFilesHandler();
        this.nameScanControl = new NameScanControl();
    }

    private void navigateToResultManager(RemoteWebDriver driver) {
        Allure.step("Navigate to Name Scan Tab.");
        Navigation.SCAN_MANAGER.navigate(driver);
        scanManagerNavigation.navigate(driver);
    }


    @Step("Add Context Conditions")
    public void add_context_conditions(RemoteWebDriver driver, String conditionName) {
        resultEditor.setConditionName(driver, conditionName);
        resultEditor.clickSearchConditionsButton(driver);
        resultEditor.clickAddConditionButton(driver);


    }

    public boolean export_alert(RemoteWebDriver driver, String printScope, String docType, String fileName, String hub) throws Exception {
        Allure.step(String.format("Start Exporting Violation With Print Scope %s And Document Type %s", printScope, docType));
        resultManager.clickOnDetection(driver);
        resultEditor.clickExportButton(driver);
        resultEditor.selectPrintScope(driver, printScope);
        resultEditor.selectDocType(driver, docType);
        resultEditor.clickPrintButton(driver);
        resultEditor.clickCloseDialogButton(driver);
        nameScanEditor.closeViolationDetection(driver);
        return textFilesHandler.exist(driver, fileName, hub);
    }

    @Step("Add Detection As Context Good Guy")
    public String add_detection_as_context_good_guy(RemoteWebDriver driver, String conditionName) throws Exception {
        resultManager.clickOnDetection(driver);
        nameScanEditor.clickGGButton(driver);
        add_context_conditions(driver, conditionName);
        nameScanEditor.clickAcceptButton(driver);
        String validationMessage = commonAction.getAlertMessageStringWithoutWaitForAjaxLoader(driver);
        Allure.step("Actual validation Message = " + validationMessage);
        nameScanEditor.closeGoodGuyEditor(driver);
        nameScanEditor.closeViolationDetection(driver);
        return validationMessage;
    }

    @Step("Add Good Guy")
    public String add_good_guy(RemoteWebDriver driver, String category) throws Exception {
        resultManager.clickOnDetection(driver);
        return nameScanControl.add_detection_as_GG(driver, category);
    }

    public String get_detection_rank(RemoteWebDriver driver) {
        String detectionRank = resultManager.getDetectionRank(driver);
        Allure.step("Detection rank = " + detectionRank);
        return detectionRank;
    }



    @Step("Check Scanned File Result")
    public String check_scanned_file_result(RemoteWebDriver driver) throws InterruptedException {
        driver.navigate().refresh();
        navigateToResultManager(driver);
        String status = "";
        int trial = 0;
        while (!status.equals("SUCCEEDED") && !status.equals("FAILED")) {
            wait.time(Wait.ONE_SECOND * 3);
            trial++;
            resultManager.clickSearchButton(driver);
            wait.waitUntilAjaxLoaderDisappear(driver);
            status = resultManager.getStatus(driver).toUpperCase();
            if (trial == 20) {
                break;
            }
        }
        Allure.step("Scan status = " + status);
        return status;
    }

    @Step("Get detection id")
    public String get_detection_id(RemoteWebDriver driver) throws InterruptedException {
        String detectionId = null;
        resultManager.clickOnResult(driver);
        try {
            detectionId = resultManager.getDetectionID(driver);
            Allure.step("Detection ID = " + detectionId);
        } catch (Exception e) {
            detectionId = commonAction.getAlertMessageString(driver);
        }
        return detectionId;
    }


    public String get_session_id(RemoteWebDriver driver) {
        return resultManager.getSessionId(driver);
    }

    //click on log button to download log file
    @Step("Download log file")
    public void download_log_file(RemoteWebDriver driver) {

        resultManager.click_log_Link(driver);
    }

    @Step("Get number of alert by click on detection on result page")
    public int get_number_of_alerts_for_detection(RemoteWebDriver driver) {
        resultManager.clickOnDetection(driver);
        return resultEditor.getNumOfAlerts(driver);
    }

    @Step("Get status list for alerts")
    public List<String> get_status_List_For_alerts(RemoteWebDriver driver) {
        resultManager.clickOnDetection(driver);
        List<String> list = resultEditor.getAlertStatusList(driver);
        resultEditor.closeViolationDetection(driver);
        return list;
    }

    @Step("Get unmatched roles")
    public String get_unmatched_roles(RemoteWebDriver driver) {
        resultManager.clickOnDetection(driver);
        String unmatchedRoles = resultEditor.getUnmatchedRoles(driver);
        resultManager.clickCloseButton(driver);
        return unmatchedRoles;
    }

    @Step("Get unmatched roles by status")
    public String get_unmatched_roles(RemoteWebDriver driver, String status) {
        resultManager.clickOnDetection(driver, status);
        String unmatchedRoles = resultEditor.getUnmatchedRoles(driver);
        resultManager.clickCloseButton(driver);
        return unmatchedRoles;
    }

    public ArrayList<String> get_status_list(RemoteWebDriver driver) {
        return resultManager.getStatusListFromResultPage(driver);
    }

    public String get_detection_status(RemoteWebDriver driver) {
        return resultManager.getStatusFromResultPage(driver);
    }

    @Step("Export detection details")
    public boolean export_detection_details(RemoteWebDriver driver, String fileName, String hub) throws InterruptedException, MalformedURLException {
        resultManager.clickExportDetectionDetailsButton(driver);
        return textFilesHandler.checkExistOfFileWithDynamicName(driver, fileName, hub);
    }

    public HashMap<String, String> get_all_detections_details(RemoteWebDriver driver) {
        resultManager.clickOnResult(driver);
        HashMap<String, String> detection_details = resultEditor.get_all_detections_details(driver);
        return detection_details;
    }

    public HashMap<String, String> get_scanned_record_and_rank(RemoteWebDriver driver) throws InterruptedException {
        resultManager.clickOnResult(driver);
        wait.time(5000);
        HashMap<String, String> detection_details = resultEditor.get_scanned_record_and_rank(driver);
        return detection_details;
    }
}
