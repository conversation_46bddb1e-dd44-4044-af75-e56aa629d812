package eastnets.screening.control.scanManger;

import core.util.TextFilesHandler;
import core.util.Wait;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.Navigation;
import eastnets.screening.entity.FileScan;
import eastnets.screening.gui.scanManager.ScanManagerNavigation;
import eastnets.screening.gui.scanManager.fileScan.FileScanManager;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.io.IOException;

public class FileScanControl {

    private final ScanManagerNavigation scanManagerNavigation;
    private final FileScanManager fileScanManager;
    private final CommonAction commonAction;
    private final Wait wait;
    private final TextFilesHandler textFilesHandler;

    public FileScanControl() {
        this.scanManagerNavigation = ScanManagerNavigation.FILE_SCAN;
        this.fileScanManager = new FileScanManager();
        this.commonAction = new CommonAction();
        this.wait = new Wait();
        this.textFilesHandler = new TextFilesHandler();
    }

    private void navigateToFileScan(RemoteWebDriver driver) {
        Allure.step("Navigate to Name Scan Tab.");
        Navigation.SCAN_MANAGER.navigate(driver);
        scanManagerNavigation.navigate(driver);
    }

    @Step("Start file Scanning")
    private String scanFile(RemoteWebDriver driver, FileScan fileScan) throws Exception {

        if (fileScan.getEnList() != null) {
            fileScanManager.selectZone(driver, fileScan.getEnList().getZoneName());
            if (fileScan.getEnList().getListSet() != null)
                fileScanManager.selectListSet(driver, fileScan.getEnList().getListSet().getName());
            wait.waitUntilAjaxLoaderDisappear(driver);
        }
        fileScanManager.setScannedFilePath(driver, fileScan.getFilePath());
        fileScanManager.selectFormat(driver, fileScan.getFormat());
        fileScanManager.selectEncoding(driver, fileScan.getEncoding());
        fileScanManager.selectResults(driver, fileScan.getResult());
        fileScanManager.selectRank(driver, fileScan.getRank());
        fileScanManager.selectDetectVessels(driver, fileScan.getDetectVessels());
        fileScanManager.selectDetectCountries(driver, fileScan.getDetectCountries());
        fileScanManager.selectCreateAlert(driver, fileScan.getCreateAlertsAutomatically());
        fileScanManager.clickStartScanButton(driver);

        String validationMessage = commonAction.getAlertMessageString(driver);
        Allure.step("Alert Message = " + validationMessage);
        return validationMessage;
    }

    public String scan_file(RemoteWebDriver driver, FileScan fileScan) throws Exception {
        navigateToFileScan(driver);
        fileScanManager.clickResetButton(driver);
        return scanFile(driver, fileScan);
    }

    public void edit_rje_file(String filePath, String newFilePath, String entryName) throws IOException {
        String fileContent = textFilesHandler.getTextFileContentAsString(filePath);

        fileContent = fileContent.replaceFirst("#ENTRY_NAME#", entryName);

        Allure.step("RJE File Content= " + fileContent);
        Allure.step("Start coping data from sample file to another file to be used.");
        textFilesHandler.writeToFile(newFilePath, fileContent);
    }
}
