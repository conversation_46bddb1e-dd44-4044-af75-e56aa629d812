package eastnets.screening.control.scanManger;

import eastnets.common.control.CommonAction;
import eastnets.common.gui.Navigation;
import eastnets.screening.gui.scanManager.ScanManagerNavigation;
import eastnets.screening.gui.scanManager.scanName.NameScanEditor;
import eastnets.screening.gui.scanManager.scanName.NameScanManager;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;

public class NameScanControl {

    private final ScanManagerNavigation scanManagerNavigation;
    private final NameScanManager nameScanManager;
    private final NameScanEditor nameScanEditor;
    private final CommonAction commonAction;

    public NameScanControl() {
        this.scanManagerNavigation = ScanManagerNavigation.NAME_CHECKER;
        this.nameScanManager = new NameScanManager();
        this.nameScanEditor = new NameScanEditor();
        this.commonAction = new CommonAction();
    }

    private void navigateToNameScan(RemoteWebDriver driver) {
        Allure.step("Navigate to Name Scan Tab.");
        Navigation.SCAN_MANAGER.navigate(driver);
        scanManagerNavigation.navigate(driver);
    }

    @Step("Start Name Scanning")
    private void nameScan(RemoteWebDriver driver, String name, String zone, String rank, boolean createAutomaticAlert, boolean detectCountriesFlag, boolean detectVesselsFlag) throws Exception {
        navigateToNameScan(driver);
        nameScanManager.clickFResetButton(driver);
        nameScanManager.selectZone(driver, zone);
        nameScanManager.selectRank(driver, rank);
        nameScanManager.setName(driver, name);
        nameScanManager.setAutomaticAlertCheckBox(driver, createAutomaticAlert);
        nameScanManager.setDetectCountriesCheckBox(driver, detectCountriesFlag);
        nameScanManager.setDetectVesselsCheckBox(driver, detectVesselsFlag);
        nameScanManager.clickScanButton(driver);
    }

    @Step("Start Name Scanning Then Get Detection Status")
    private String scan_Name_and_get_detection_status(RemoteWebDriver driver, String scannedName, String zone, String rank, boolean createAutomaticAlert, boolean detectCountriesFlag, boolean detectVesselsFlag)  {
        String massage = null;
        try {
            nameScan(driver, scannedName, zone, rank, createAutomaticAlert, detectCountriesFlag, detectVesselsFlag);
            massage = commonAction.getAlertMessageStringWithoutWaitForAjaxLoader(driver);
            String status = nameScanManager.getDetectionStatus(driver);
            Allure.step("Status for the scanned name = " + status);
            return status;
        } catch (Exception e) {
            return massage;
        }
    }

    @Step("Start Name Scanning Then Get Detection Status")
    private String scan_Name_and_get_detection_status_4eyes(RemoteWebDriver driver, String scannedName, String zone, String rank, boolean createAutomaticAlert, boolean detectCountriesFlag, boolean detectVesselsFlag)  {
        String massage = null;
        try {
            nameScan(driver, scannedName, zone, rank, createAutomaticAlert, detectCountriesFlag, detectVesselsFlag);
            massage = commonAction.getAlertMessageStringWithoutWaitForAjaxLoader(driver);
            String status = nameScanManager.getDetectionStatus_4eyes(driver);
            Allure.step("Status for the scanned name = " + status);
            return status;
        } catch (Exception e) {
            return massage;
        }
    }


    public String scan_Name(RemoteWebDriver driver, String name, String zone, String rank, boolean createAutomaticAlert, boolean detectCountriesFlag, boolean detectVesselsFlag, boolean fourEyes) throws Exception {
        if (fourEyes) {
            return scan_Name_and_get_detection_status_4eyes(driver, name, zone, rank, createAutomaticAlert, detectCountriesFlag, detectVesselsFlag);
        } else {
            return scan_Name_and_get_detection_status(driver, name, zone, rank, createAutomaticAlert, detectCountriesFlag, detectVesselsFlag);
        }
    }

    public String get_detection_rank(RemoteWebDriver driver) throws Exception {
        return nameScanManager.getDetectionRank(driver);
    }

    public String get_detection_rank_by_matched_entity(RemoteWebDriver driver,String scanned_name, String matched_entity) {
        nameScanManager.chooseScannedNameFromResultTable(driver, scanned_name);
        return nameScanManager.getRankByMatchedName(driver, matched_entity);
    }

    public String get_detection_id(RemoteWebDriver driver) throws InterruptedException {
        String detectionId ;
        try {
            detectionId = nameScanManager.getDetectionID(driver);
            Allure.step("Detection ID = " + detectionId);
        } catch (Exception e) {
            detectionId = commonAction.getAlertMessageString(driver);
        }
        return detectionId;
    }

    public String get_investigator(RemoteWebDriver driver) {
        String investigator = nameScanManager.getInvestigator(driver);
        Allure.step("Investigator = " + investigator);
        return investigator;
    }


    @Step("Add Detection As Good Guy")
    public String add_detection_as_GG(RemoteWebDriver driver, String category) throws Exception {
        nameScanEditor.clickGGButton(driver);
        nameScanEditor.selectGGCategory(driver, category);
        nameScanEditor.clickAcceptButton(driver);
        String validationMessage = commonAction.getAllAlertMessageString(driver);
        Allure.step("Actual validation Message = " + validationMessage);
        nameScanEditor.closeGoodGuyEditor(driver);
        nameScanEditor.closeViolationDetection(driver);

        return validationMessage;
    }

    @Step("Accept good-guy as shared")
    public String accept_gg_as_shared(RemoteWebDriver driver) throws InterruptedException {
        nameScanEditor.clickGGButton(driver);
        nameScanEditor.clickAcceptAsSharedButton(driver);
        String validationMessage = commonAction.getAlertMessageStringWithoutWaitForAjaxLoader(driver);
        Allure.step("Actual validation Message = " + validationMessage);
        nameScanEditor.closeGoodGuyEditor(driver);
        nameScanEditor.closeViolationDetection(driver);

        return validationMessage;
    }

    @Step("Get Matched Entity Name")
    public String get_matched_entity_name_GG(RemoteWebDriver driver, String name)  {
        nameScanManager.chooseScannedNameFromResultTable(driver, name);
        nameScanEditor.clickGGButton(driver);
        return nameScanEditor.getMatchedEntityName(driver);
    }

}

