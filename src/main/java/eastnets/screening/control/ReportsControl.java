package eastnets.screening.control;

import core.util.TextFilesHandler;
import core.util.Wait;
import eastnets.common.gui.Navigation;
import eastnets.screening.gui.reports.ReportEditor;
import eastnets.screening.gui.reports.ReportManager;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;

public class ReportsControl {

    private final ReportManager reportManager;
    private final ReportEditor reportEditor;
    private final Wait wait;
    private final TextFilesHandler textFilesHandler;

    public ReportsControl() {
        this.reportManager = new ReportManager();
        this.reportEditor = new ReportEditor();
        this.wait = new Wait();
        this.textFilesHandler = new TextFilesHandler();
    }

    @Step("Select name")
    public void selectName(RemoteWebDriver driver, String name) throws Exception {
        Allure.step("Selecting name: " + name);
        reportManager.selectName(driver, name);
    }

    @Step("Select Report Format")
    public void selectReportFormat(RemoteWebDriver driver, String reportFormat) throws Exception {
        Allure.step("Selecting report format: " + reportFormat);
        reportManager.selectReportFormat(driver, reportFormat);
    }

    @Step("Select status")
    public void selectStatus(RemoteWebDriver driver, String status) throws Exception {
        Allure.step("Selecting status: " + status);
        reportManager.selectStatus(driver, status);
    }

    @Step("Enter comment")
    public void enterComment(RemoteWebDriver driver, String comment) {
        Allure.step("Entering comment: " + comment);
        reportManager.enterComment(driver, comment);
    }

    @Step("Enter generated by")
    public void enterGeneratedBy(RemoteWebDriver driver, String generatedBy) {
        Allure.step("Entering generated by: " + generatedBy);
        reportManager.enterGeneratedBy(driver, generatedBy);
    }

    @Step("Click search")
    public void clickSearch(RemoteWebDriver driver) {
        Allure.step("Clicking search");
        reportManager.clickSearch(driver);
    }

    @Step("Click reset")
    public void clickReset(RemoteWebDriver driver) {
        Allure.step("Clicking reset");
        reportManager.clickReset(driver);
    }

    @Step("Click delete")
    public void clickDelete(RemoteWebDriver driver) {
        Allure.step("Clicking delete");
        reportManager.clickDelete(driver);
    }

    @Step("Click new")
    public void clickNew(RemoteWebDriver driver) {
        Allure.step("Clicking new");
        reportManager.clickNew(driver);
    }

    @Step("Select from date")
    public void selectFromDate(RemoteWebDriver driver, String fromDate) {
        Allure.step("Selecting from date: " + fromDate);
        reportManager.selectFromDate(driver, fromDate);
    }

    @Step("Search report")
    public void searchReport(RemoteWebDriver driver, String name, String reportFormat,
                                    String comment) throws Exception {
        reportManager.clickReset(driver);
        reportManager.selectName(driver, name);
        reportManager.enterComment(driver, comment);
        reportManager.clickSearch(driver);
    }

    @Step("Click new Button")
    public void clickNewButton(RemoteWebDriver driver, String reportName) throws Exception {
        Navigation.REPORT_MANAGER.navigate(driver);
        reportManager.selectReportList(driver, reportName);
        reportManager.clickNew(driver);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Create new report")
    public void createNewReport(RemoteWebDriver driver, String reportName, String comment, String format
            , String zone, String zoneInputType, String investigator, String listSetName) throws Exception {
        clickNewButton(driver, reportName);
        reportEditor.enterComment(driver, comment);
        reportEditor.selectFormat(driver, format);
        if (zoneInputType == "select")
            reportEditor.selectZone(driver, zone);
        else
            reportEditor.enterZone(driver, zone);

        reportEditor.selectInvestigator(driver, investigator);
        reportEditor.selectListSet(driver, listSetName);
        reportEditor.clickExecute(driver);
    }

    @Step("Create new report")
    public void createNewReport(RemoteWebDriver driver, String reportName, String comment, String format
            , String zone, String zoneInputType, String blackListName, String balckListVersion1, String balckListVersion2) throws Exception {
        clickNewButton(driver, reportName);
        reportEditor.enterComment(driver, comment);
        reportEditor.selectFormat(driver, format);
        if (zoneInputType == "select")
            reportEditor.selectZone(driver, zone);
        else
            reportEditor.enterZone(driver, zone);

        reportEditor.selectBlackListName(driver, blackListName);
        reportEditor.selectBlackListVersion1(driver, balckListVersion1);
        reportEditor.selectBlackListVersion2(driver, balckListVersion2);
        reportEditor.clickExecute(driver);
    }

    @Step("Create Detection Grouped By Investigator Report")
    public void createDetectionGroupedByInvestigatorReport(RemoteWebDriver driver, String reportName, String comment, String format
            , String startDate, String endDate) throws Exception {
        clickNewButton(driver, reportName);
        reportEditor.enterComment(driver, comment);
        reportEditor.selectFormat(driver, format);
        reportEditor.setStartData(driver, startDate);
        reportEditor.setEndData(driver, endDate);
        reportEditor.clickExecute(driver);
    }

    @Step("Create Detection Grouped By Status Report")
    public void createNewReportByZone(RemoteWebDriver driver, String reportName, String comment, String format
            , String zone) throws Exception {
        clickNewButton(driver, reportName);
        reportEditor.enterComment(driver, comment);
        reportEditor.selectFormat(driver, format);
        reportEditor.selectZone(driver, zone);
        reportEditor.clickExecute(driver);
    }

    public void createNewReportWithSessionData(RemoteWebDriver driver, String reportName, String comment, String format
            , String sessionId1, String sessionId2) throws Exception {
        clickNewButton(driver, reportName);
        if (sessionId1 != null)
            reportEditor.enterSessionID(driver, 0, sessionId1);
        if (sessionId2 != null)
            reportEditor.enterSessionID(driver, 1, sessionId2);
        reportEditor.enterComment(driver, comment);
        reportEditor.selectFormat(driver, format);
        reportEditor.clickExecute(driver);
    }

    @Step("Create Violations per list Report")
    public void createViolationsPerListReport(RemoteWebDriver driver, String reportName, String comment, String format
            , String blackList) throws Exception {
        clickNewButton(driver, reportName);
        reportEditor.enterComment(driver, comment);
        reportEditor.selectFormat(driver, format);
        reportEditor.selectBlackListName(driver, blackList);
        reportEditor.selectBlackListVersion(driver, "1");
        reportEditor.clickExecute(driver);
    }

    @Step("Verify report status")
    public String getReportStatus(RemoteWebDriver driver, String reportName, String reportFormat, String comment, String status) throws Exception {
        Navigation.REPORT_MANAGER.navigate(driver);
        searchReport(driver, reportName, reportFormat, comment);

        int i = 20;
        while (i > 0) {
            String current_status = reportManager.getReportStatus(driver);
            if (current_status.equalsIgnoreCase(status)
                    || current_status.equalsIgnoreCase("Failed") ) {
                return current_status;
            } else {
                wait.time(Wait.ONE_SECOND * 5);
                reportManager.clickSearch(driver);
                i--;
            }
        }
        return reportManager.getReportStatus(driver);
    }

    @Step("Download report")
    public boolean downloadReport(RemoteWebDriver driver, String reportName, String comment, String downloadedFilePath, String hub) throws Exception {
        Navigation.REPORT_MANAGER.navigate(driver);
        searchReport(driver, "", "", comment);
        reportManager.clickReportName(driver);
        return textFilesHandler.exist(driver, downloadedFilePath, hub);
    }


}
