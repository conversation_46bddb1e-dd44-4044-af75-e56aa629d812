package eastnets.screening.control.AdvancedSettingsControls;

import eastnets.common.control.CommonAction;
import eastnets.common.gui.Navigation;
import eastnets.screening.entity.FourEyes;
import eastnets.screening.gui.advancedSettings.FourEyesSettings.FourEyesSettingsEditor;
import eastnets.screening.gui.advancedSettings.FourEyesSettings.FourEyesSettingsManager;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;

public class FourEyesSettingsControl {

    private final AdvancedSettingsControl advancedSettingsControl;
    private final FourEyesSettingsManager fourEyesSettingsManager;
    private final FourEyesSettingsEditor fourEyesSettingsEditor;
    private final CommonAction commonAction;

    public FourEyesSettingsControl() {
        this.advancedSettingsControl = new AdvancedSettingsControl();
        this.fourEyesSettingsManager = new FourEyesSettingsManager();
        this.fourEyesSettingsEditor = new FourEyesSettingsEditor();
        this.commonAction = new CommonAction();
    }

    @Step("Navigate to '4 Eyes' tab")
    public void navigateTo4EyesTab(RemoteWebDriver driver) {
        Navigation.ADVANCED_SETTINGS.navigate(driver);
        advancedSettingsControl.navigateToFourEyesSettings(driver);
    }

    @Step("Create 4 Eyes Configuration")
    public void create4EyesConfiguration(RemoteWebDriver driver, FourEyes fourEyes) throws Exception {
        navigateTo4EyesTab(driver);
        fourEyesSettingsManager.clickNewButton(driver);
        fourEyesSettingsEditor.setName(driver, fourEyes.getName());
        fourEyesSettingsEditor.selectZone(driver, fourEyes.getZone());
        fourEyesSettingsEditor.selectListSet(driver, fourEyes.getListSet());
        fourEyesSettingsEditor.clickSaveButton(driver);
    }


    @Step("Create 4 Eyes Configuration and add context")
    public String create4EyesConfigurationAddContext(RemoteWebDriver driver, FourEyes fourEyes) throws Exception {
        navigateTo4EyesTab(driver);
        fourEyesSettingsManager.clickNewButton(driver);
        fourEyesSettingsEditor.setName(driver, fourEyes.getName());
        fourEyesSettingsEditor.selectZone(driver, fourEyes.getZone());
        fourEyesSettingsEditor.selectListSet(driver, fourEyes.getListSet());
        fourEyesSettingsEditor.setContextFilter(driver, fourEyes.getContextFilter());
        fourEyesSettingsEditor.clickValidateSyntaxButton(driver);
        String alertMessage = commonAction.getAlertMessageString(driver);
        Allure.step("Alert message: " + alertMessage);
        fourEyesSettingsEditor.clickSaveButton(driver);
        return alertMessage;
    }

    @Step("Enable Configuration ")
    public void enableConfiguration(RemoteWebDriver driver, String configName) throws InterruptedException {

        fourEyesSettingsManager.sortByIdDesc(driver);
        fourEyesSettingsManager.clickCheckboxButton(driver, configName);
        fourEyesSettingsManager.clickEnableButton(driver);

    }

}
