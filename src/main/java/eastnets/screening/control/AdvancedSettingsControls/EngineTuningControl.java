package eastnets.screening.control.AdvancedSettingsControls;


import eastnets.common.control.CommonAction;
import eastnets.common.gui.Navigation;
import eastnets.screening.entity.EngineTuning;
import eastnets.screening.gui.advancedSettings.EngineTuningManager;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;


public class EngineTuningControl {

    private final AdvancedSettingsControl advancedSettingsControl;
    private final EngineTuningManager engineTuningManager;
    private final CommonAction commonAction;

    public EngineTuningControl() {
        this.advancedSettingsControl = new AdvancedSettingsControl();
        this.engineTuningManager = new EngineTuningManager();
        this.commonAction = new CommonAction();
    }

    @Step("Add New Synonym")
    public String addNewSynonym(RemoteWebDriver driver, EngineTuning engineTuning) throws Exception {


        engineTuningManager.selectShowSettings(driver, engineTuning.getShowSettings(), engineTuning.getZone());
        engineTuningManager.clickAddNewSynonymButton(driver);
        //engineTuningManager.selectSynonymZone(driver, engineTuning.getZone());
        engineTuningManager.enterWord(driver, engineTuning.getWord());
        engineTuningManager.enterSynonym(driver, engineTuning.getSynonym());
        engineTuningManager.clickSaveSynonymButton(driver);
        engineTuningManager.clickOkButton(driver);
        String validationMessage = commonAction.getAlertMessageString(driver);
        Allure.step("Validation message is: " + validationMessage);
        return validationMessage;

    }

    @Step("Add New Neutral Word")
    public String addNewNeutralWord(RemoteWebDriver driver, EngineTuning engineTuning) throws Exception {
        advancedSettingsControl.navigateToEngineTuning(driver);
        engineTuningManager.selectShowSettings(driver, engineTuning.getShowSettings(), engineTuning.getZone());
        engineTuningManager.clickAddNewNeutralWordButton(driver);
        // engineTuningManager.selectNeutralWordZone(driver, engineTuning.getZone());
        engineTuningManager.enterNeutralWordName(driver, engineTuning.getWord());
        engineTuningManager.selectNeutralWordCategory(driver, engineTuning.getCategory());
        engineTuningManager.clickSaveNeutralWordButton(driver);
        engineTuningManager.clickOkButton(driver);
        String validationMessage = commonAction.getAlertMessageString(driver);
        Allure.step("Validation message is: " + validationMessage);
        return validationMessage;
    }

    @Step("Add Engine Setting")
    public String addEngineSetting(RemoteWebDriver driver, EngineTuning engineTuning) throws Exception {

        engineTuningManager.selectShowSettings(driver, engineTuning.getShowSettings(), engineTuning.getZone());
        engineTuningManager.setCacheThreshold(driver, engineTuning.getCacheThreshold());
        engineTuningManager.setCacheExpiration(driver, engineTuning.getCacheExpiration());
        engineTuningManager.selectIgnoreMatchShorter(driver, engineTuning.getIgnoreMatches());
        engineTuningManager.clickIgnore(driver, engineTuning.isIgnoreFlag());
        engineTuningManager.selectDetectSwiftBic(driver, engineTuning.getDetectSwiftBic());
        engineTuningManager.clickEnableMatchOnNumber(driver, engineTuning.isEnableMatchOnNumber());
        engineTuningManager.clickIgnoreExtraWords(driver, engineTuning.isIgnoreExtraWords());
        engineTuningManager.clickEnhanceMatchRankLastName(driver, engineTuning.isEnhanceMatchRankLastName());
        engineTuningManager.clickLowerMatchRank(driver, engineTuning.isLowerMatchRank());
        engineTuningManager.clickEnhanceMatchRankFirstName(driver, engineTuning.isEnhanceMatchRankFirstName());
        engineTuningManager.clickDisableScanOneWordIndividualAKA(driver, engineTuning.isDisableScanOneWordIndividualAKA());
        engineTuningManager.clickEnableShortWords(driver, engineTuning.isEnableShortWords());
        engineTuningManager.clickIndividualNamePrefixNeutral(driver, engineTuning.isIndividualNamePrefixNeutral());
        engineTuningManager.clickEnableDoubleLetterEnhacement(driver, engineTuning.isEnableDoubleLettersEnhancement());
        engineTuningManager.clickSaveEngineSettingButton(driver);
        String alertText = driver.switchTo().alert().getText();
        Allure.step("Alert Message = " + alertText);
        if (alertText.contains("Nothing has been changed.")) {
            driver.switchTo().alert().accept();
            return alertText;
        }
        driver.switchTo().alert().accept();
        String validationMessage = commonAction.getAlertMessageString(driver);
        Allure.step("Validation message is: " + validationMessage);
        return validationMessage;
    }

    @Step("Engine Settings Ignore Matches")
    public String engineSettingsIgnoreMatches(RemoteWebDriver driver, EngineTuning engineTuning) throws Exception {
        Navigation.ADVANCED_SETTINGS.navigate(driver);
        advancedSettingsControl.navigateToEngineTuning(driver);
        engineTuningManager.selectShowSettings(driver, engineTuning.getShowSettings(), engineTuning.getZone());
        engineTuningManager.setCacheExpiration(driver, engineTuning.getCacheExpiration());
        engineTuningManager.selectIgnoreMatchShorter(driver, engineTuning.getIgnoreMatches());
        engineTuningManager.clickIgnore(driver, engineTuning.isIgnoreFlag());
        engineTuningManager.clickEnableMatchOnNumber(driver, engineTuning.isEnableMatchOnNumber());
        engineTuningManager.clickSaveEngineSettingButton(driver);
        String alertText = driver.switchTo().alert().getText();
        Allure.step("Alert Message = " + alertText);
        if (alertText.contains("Nothing has been changed.")) {
            driver.switchTo().alert().accept();
            return alertText;
        }
        driver.switchTo().alert().accept();
        String validationMessage = commonAction.getAlertMessageString(driver);
        Allure.step("Validation message is: " + validationMessage);
        return validationMessage;
    }

    @Step("Engine Setting")
    public String engineSettings(RemoteWebDriver driver, EngineTuning engineTuning) throws Exception {

        engineTuningManager.selectShowSettings(driver, engineTuning.getShowSettings(), engineTuning.getZone());
        engineTuningManager.setCacheThreshold(driver, engineTuning.getCacheThreshold());
        engineTuningManager.setCacheExpiration(driver, engineTuning.getCacheExpiration());
        engineTuningManager.selectDetectSwiftBic(driver, engineTuning.getDetectSwiftBic());
        engineTuningManager.clickIgnoreExtraWords(driver, engineTuning.isIgnoreExtraWords());
        engineTuningManager.clickEnhanceMatchRankLastName(driver, engineTuning.isEnhanceMatchRankLastName());
        engineTuningManager.clickLowerMatchRank(driver, engineTuning.isLowerMatchRank());
        engineTuningManager.clickEnhanceMatchRankFirstName(driver, engineTuning.isEnhanceMatchRankFirstName());
        engineTuningManager.clickDisableScanOneWordIndividualAKA(driver, engineTuning.isDisableScanOneWordIndividualAKA());
        engineTuningManager.clickSaveEngineSettingButton(driver);
        String alertText = driver.switchTo().alert().getText();
        Allure.step("Alert Message = " + alertText);
        if (alertText.contains("Nothing has been changed.")) {
            driver.switchTo().alert().accept();
            return alertText;
        }
        driver.switchTo().alert().accept();
        String validationMessage = commonAction.getAlertMessageString(driver);
        Allure.step("Validation message is: " + validationMessage);
        return validationMessage;
    }

    @Step("Enable Islamic Names")
    public void enableIslamicNames(RemoteWebDriver driver, EngineTuning engineTuning) throws Exception {
        advancedSettingsControl.navigateToEngineTuning(driver);
        engineTuningManager.selectShowSettings(driver, engineTuning.getShowSettings(), engineTuning.getZone());
        engineTuningManager.clickEnableIslamicNames(driver, engineTuning.isEnableIslamicNames());
    }
}