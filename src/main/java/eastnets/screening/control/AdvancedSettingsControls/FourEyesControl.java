package eastnets.screening.control.AdvancedSettingsControls;

import eastnets.common.gui.Navigation;
import eastnets.screening.gui.advancedSettings.FourEyesSettings.FourEyesSettingsManager;
import org.openqa.selenium.remote.RemoteWebDriver;

public class FourEyesControl {

    private final AdvancedSettingsControl advancedSettingsControl;
    private final FourEyesSettingsManager fourEyesSettingsManager;

    public FourEyesControl() {
        this.advancedSettingsControl = new AdvancedSettingsControl();
        this.fourEyesSettingsManager = new FourEyesSettingsManager();
    }

    //navigate to Four Eyes Settings page
    public String navigateToFourEyesSettings(RemoteWebDriver driver) {
        Navigation.ADVANCED_SETTINGS.navigate(driver);
        return advancedSettingsControl.navigateToFourEyesSettings(driver);
    }

    //enable Four Eyes
    public void enableFourEyes(RemoteWebDriver driver) throws Exception {
        navigateToFourEyesSettings(driver);
        fourEyesSettingsManager.enableFourEyes(driver);
    }

    //disable Four Eyes
    public void disableFourEyes(RemoteWebDriver driver) {
        navigateToFourEyesSettings(driver);
        fourEyesSettingsManager.disableFourEyes(driver);
    }

    public void enableDecisionModePessimistic(RemoteWebDriver driver) throws Exception {
        navigateToFourEyesSettings(driver);
        fourEyesSettingsManager.enableDecisionModePessimistic(driver);
    }

    public void enableDecisionModeLastInputPrevails(RemoteWebDriver driver) throws Exception {
        navigateToFourEyesSettings(driver);
        fourEyesSettingsManager.enableDecisionModeLastInputPrevails(driver);
    }
}
