package eastnets.screening.control.AdvancedSettingsControls;

import eastnets.common.gui.Navigation;
import eastnets.screening.gui.advancedSettings.AdvancedSettingNavigation;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;


public class AdvancedSettingsControl {

    @Step("Navigate to Business Logic page")
    public String navigateToBusinessLogic(RemoteWebDriver driver) {
        Navigation.ADVANCED_SETTINGS.navigate(driver);
        return AdvancedSettingNavigation.BUSINESS_LOGIC.navigate(driver);
    }

    @Step("Navigate to Engine Tuning page")
    public String navigateToEngineTuning(RemoteWebDriver driver) {
        Navigation.ADVANCED_SETTINGS.navigate(driver);
        return AdvancedSettingNavigation.ENGINE_TUNING.navigate(driver);
    }

    @Step("Navigate to Four Eyes Settings page")
    public String navigateToFourEyesSettings(RemoteWebDriver driver) {
        return AdvancedSettingNavigation.FOUR_EYES_SETTING.navigate(driver);
    }


    @Step("Navigate to Investigators page")
    public String navigateToInvestigators(RemoteWebDriver driver) {
        String navigationStatus = AdvancedSettingNavigation.INVESTIGATORS.navigate(driver);
        return navigationStatus;

    }

    @Step("Navigate to Notifications page")
    public String navigateToNotifications(RemoteWebDriver driver) {
        return AdvancedSettingNavigation.NOTIFICATIONS.navigate(driver);
    }

}