package eastnets.screening.control.AdvancedSettingsControls;

import core.constants.screening.GeneralConstants;
import core.util.Wait;
import eastnets.screening.entity.BlackList;
import eastnets.screening.entity.Currency;
import eastnets.screening.entity.FatfSettings;
import eastnets.screening.gui.advancedSettings.businessLogic.FatfSettingsPage;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.util.ArrayList;

public class FatfSettingsControl {

    private final FatfSettingsPage fatfSettingsPage;
    private final Wait wait;

    public FatfSettingsControl() {
        this.fatfSettingsPage = new FatfSettingsPage();
        this.wait = new Wait();
    }

    @Step("Select zone")
    public void selectZone(RemoteWebDriver driver, String zone) throws Exception {
        Allure.step("Selecting zone: " + zone);
        fatfSettingsPage.selectZone(driver, zone);
    }

    @Step("Enable Fatf Recommendation")
    public void enableFatfRecommendation(RemoteWebDriver driver, String condition) throws InterruptedException {
        if (condition.equalsIgnoreCase(GeneralConstants.TRUE)) {
            Allure.step("Enabling Fatf Recommendation");
            wait.time(Wait.ONE_SECOND * 2);
            fatfSettingsPage.clickEnableFatfRecommendation(driver);
        }
    }

    @Step("Set Black List")
    public void setBlackList(RemoteWebDriver driver, ArrayList<BlackList> blackList) throws InterruptedException {
        for (int i = 0; i < blackList.size(); i++) {
            Allure.step("Inserting Black list: " + blackList.get(i).getName());
            wait.time(Wait.ONE_SECOND * 2);
            fatfSettingsPage.insertBlackList(driver, blackList.get(i).getName());
        }
    }

    @Step("Select Field Minimum Line Size")
    public void selectFieldMinimumLineSize(RemoteWebDriver driver, String minimumLineSize) throws Exception {
        Allure.step("Inserting minimum line size: " + minimumLineSize);
        wait.time(Wait.ONE_SECOND * 2);
        fatfSettingsPage.selectFieldMinimumLineSize(driver, minimumLineSize);
    }

    @Step("Select Rank")
    public void selectRank(RemoteWebDriver driver, String rank) throws Exception {
        Allure.step("Inserting rank: " + rank);
        wait.time(Wait.ONE_SECOND * 2);
        fatfSettingsPage.selectRank(driver, rank);
    }

    //Insert european codes
    @Step("Insert European Codes")
    public void insertEuropeanCodes(RemoteWebDriver driver, String europeanCodes) throws Exception {
        Allure.step("Inserting european codes: " + europeanCodes);
        wait.time(Wait.ONE_SECOND * 2);
        fatfSettingsPage.insertEuropeanCodes(driver, europeanCodes);
    }

    @Step("Enable Check Of Unique Transaction RN")
    public void enableCheckOfUniqueTransactionRN(RemoteWebDriver driver, String condition) throws InterruptedException {
        wait.time(Wait.ONE_SECOND * 2);
        if (condition.equalsIgnoreCase(GeneralConstants.TRUE)) {
            Allure.step("Enabling  Check Of Unique Transaction RN");
            fatfSettingsPage.clickEnableCheckOfTransactionRN(driver);
        } else if (condition.equalsIgnoreCase(GeneralConstants.FALSE)) {
            Allure.step("Disabling Check Of Unique Transaction RN");
            fatfSettingsPage.clickDisableCheckOfTransactionRN(driver);
        }
    }

    @Step("Check information Availability")
    public void informationAvailabilityCheck(RemoteWebDriver driver, FatfSettings fatfSettings) throws InterruptedException {
        wait.time(Wait.ONE_SECOND * 2);
        if (fatfSettings.getInformationAvailableWithinTransaction().equalsIgnoreCase(GeneralConstants.TRUE)) {
            Allure.step("Enabling Information available within transaction/message");
            fatfSettingsPage.clickInformationAvailableWithinTransaction(driver);
        } else if (fatfSettings.getInformationAvailableOutsideTheTransaction().equalsIgnoreCase(GeneralConstants.TRUE)) {
            Allure.step("Enabling Information available by other means (outside the transaction/message)");
            fatfSettingsPage.clickInformationAvailableByOtherMeans(driver);
            if (fatfSettings.getApplyForCrossBorderTraffic().equalsIgnoreCase(GeneralConstants.TRUE)) {
                wait.time(Wait.ONE_SECOND * 2);
                fatfSettingsPage.clickApplyForCrossBorderTraffic(driver);
            }
        } else if (fatfSettings.getInformationAvailableForBics().equalsIgnoreCase(GeneralConstants.TRUE)) {
            Allure.step("Enabling Information available by other means (outside the transaction/message), for the following BICs:");
            fatfSettingsPage.clickInformationAvailableByBics(driver);
            wait.time(Wait.ONE_SECOND * 2);
            Allure.step("Inserting BICS: " + fatfSettings.getBics());
            fatfSettingsPage.insertBics(driver, fatfSettings.getBics());
            if (fatfSettings.getApplyForCrossBorderTraffic().equalsIgnoreCase(GeneralConstants.TRUE)) {
                wait.time(Wait.ONE_SECOND * 2);
                fatfSettingsPage.clickApplyForCrossBorderTraffic(driver);
            }
        }
    }

    @Step("Apply For Cross Border Traffic")
    public void applyForCrossBorderTraffic(RemoteWebDriver driver, String condition) throws InterruptedException {
        if (condition.equalsIgnoreCase(GeneralConstants.TRUE)) {
            wait.time(Wait.ONE_SECOND * 2);
            Allure.step("Enabling Apply for all border traffic");
            fatfSettingsPage.clickApplyForCrossBorderTraffic(driver);
        }
    }

    @Step("Insert Currencies")
    public void insertCurrencies(RemoteWebDriver driver, ArrayList<Currency> currencies) throws Exception {
        for (int i = 0; i < currencies.size(); i++) {
            Allure.step(String.format("Adding Currency = %s with amount = %s", currencies.get(i).getName(), currencies.get(i).getAmount()));
            wait.time(Wait.ONE_SECOND * 2);
            fatfSettingsPage.insertCurrencyAmount(driver, currencies.get(i).getAmount());
            fatfSettingsPage.selectCurrency(driver, currencies.get(i).getName());
            fatfSettingsPage.clickCurrencyAddButton(driver);
        }
    }

    @Step("Click Save")
    public void clickSave(RemoteWebDriver driver, String condition) throws InterruptedException {
        if (condition.equalsIgnoreCase(GeneralConstants.SAVE)) {
            wait.time(Wait.ONE_SECOND * 2);
            Allure.step("Saving ...");
            fatfSettingsPage.clickSaveBtn(driver);
        }
    }
}