package eastnets.screening.control.AdvancedSettingsControls;

import eastnets.common.control.CommonAction;
import eastnets.common.gui.Navigation;
import eastnets.screening.entity.GeneralSettings;
import eastnets.screening.gui.advancedSettings.businessLogic.BusinessLogicNavigation;
import eastnets.screening.gui.advancedSettings.businessLogic.GeneralSettingsManager;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;


public class BusinessLogicControl {

    private final CommonAction commonAction;
    private final AdvancedSettingsControl advancedSettingsControl;
    private final GeneralSettingsManager generalSettingsManager;

    public BusinessLogicControl() {
        this.commonAction = new CommonAction();
        this.advancedSettingsControl = new AdvancedSettingsControl();
        this.generalSettingsManager = new GeneralSettingsManager();
    }

    @Step("Navigate to FATF16 Settings page")
    public String navigateToFatfSettings(RemoteWebDriver driver){
        String navigationStatus =  BusinessLogicNavigation.FATF_SETTINGS.navigate(driver);
        return navigationStatus;
    }

    @Step("Set Appearance Settings Details")
    public void setAppearanceSettingsDetails(RemoteWebDriver driver, GeneralSettings generalSettings) {
        Allure.step("Setting appearance settings details.");
        generalSettingsManager.setMaxDataCharacters(driver, generalSettings.getMaxDataCharacters());
        generalSettingsManager.setMaxCommentCharacters(driver, generalSettings.getMaxCommentsCharacters());
    }

    @Step("Set Phonetic Settings Details")
    public void setPhoneticSettingsDetails(RemoteWebDriver driver, GeneralSettings generalSettings) {
        Allure.step("Setting phonetic settings details.");
        generalSettingsManager.clickEnableArabicPhonetic(driver ,generalSettings.isEnableArabicPhonetic());
        generalSettingsManager.clickEnableArabicPhoneticPlus(driver ,generalSettings.isEnableArabicPhoneticPlus());
        generalSettingsManager.clickEnableRussianPhonetic(driver ,generalSettings.isEnableRussianPhonetic());
        generalSettingsManager.clickEnableSlavicPhonetic(driver ,generalSettings.isEnableSlavicPhonetic());
        generalSettingsManager.clickEnableCustomPhonetic(driver ,generalSettings.isEnableCustomPhonetic());
    }

    @Step("Set Other Settings Details")
    public void setOtherSettingsDetails(RemoteWebDriver driver, GeneralSettings generalSettings) throws Exception {
        Allure.step("Setting other settings details.");
        generalSettingsManager.clickAllowDetectionDoNotKnow(driver ,generalSettings.isAllowDetectionDontKnow());
        generalSettingsManager.clickEnableAutomaticSearchForModifiedDetection(driver ,generalSettings.isEnableAutomaticSearchForModifiedDetections());
        generalSettingsManager.clickEnableDetectionButtonsUnderTheDetectionDetails(driver ,generalSettings.isEnableDetectionButton());
        generalSettingsManager.clickEnableAuditTrailForAllScanRequests(driver ,generalSettings.isEnableAuditTrail());
        generalSettingsManager.clickEnableGroupForBlockReleaseDontKnowButtons(driver ,generalSettings.isEnableGroupForBlockReleaseDonKnow());
        generalSettingsManager.clickEnableGroupForAssignPendingButtons(driver ,generalSettings.isEnableGroupForAssignPending());
        generalSettingsManager.clickEnableAlternativeWayToHandleTheVisibilityOfDetectionButtons(driver ,generalSettings.isEnableAlternativeWay());
        generalSettingsManager.clickEnableImprovedInterpretationOfSwiftFields(driver ,generalSettings.isEnableImprovedInterpretationOfSWIFTFields());
        generalSettingsManager.clickAutomaticallyResetAssignees(driver ,generalSettings.isAutomaticallyResetAssignees());
        generalSettingsManager.clickEnableReadColumn(driver ,generalSettings.isEnableReadColumnOnAlerts());
        generalSettingsManager.clickEnableReasonCode(driver ,generalSettings.isEnableReasonCodeWhenAssignReleaseOrBlockDetectionAlert());
        generalSettingsManager.clickShowOptionUseAdvancedChecksumCalculation(driver ,generalSettings.isShowOptionUseAdvancedChecksumCalculation());
        generalSettingsManager.clickSendAnEmailToAdminsAndShowAWarning(driver ,generalSettings.isEnableSendAnEmailToAdminsAndShowAWarning());
        generalSettingsManager.setSendAnEmailToAdminsAndShowAWarning(driver, generalSettings.getSendAnEmailToAdminsAndShowAWarningForAlerts());
        generalSettingsManager.clickEnableGluedWordsSymSpell(driver,generalSettings.isEnableGluedWordsSymSpell());
        generalSettingsManager.setSymspellWordSize(driver, generalSettings.getEnableGluedWordSymSpellValue());
        generalSettingsManager.setEnableAutomaticFirstDetectionCheckbox(driver, generalSettings.isEnableAutomaticFirstDetection());
        generalSettingsManager.setAlertsToBeCreatedWithCommentsMode(driver, generalSettings.getAlertsDetectionsToBeCreatedModifiedWithCommentsMode());
        generalSettingsManager.setNumberOfLastNotes(driver, generalSettings.getNumberOfLastNotes());
    }


    @Step("Set Smyspell word size")
    public String setSymspellWordSize(RemoteWebDriver driver, GeneralSettings SysSpellWordSize) throws Exception {
        Allure.step("Setting general settings details.");
        Navigation.ADVANCED_SETTINGS.navigate(driver);
        advancedSettingsControl.navigateToBusinessLogic(driver);
        generalSettingsManager.setZone(driver, SysSpellWordSize.getZone());
        generalSettingsManager.clickEnableGluedWordsSymSpell(driver, SysSpellWordSize.isEnableGluedWordsSymSpell());
        generalSettingsManager.setSymspellWordSize(driver, SysSpellWordSize.getEnableGluedWordSymSpellValue());
        clickSave(driver);
        String message = commonAction.getAlertMessageString(driver);
        Allure.step("Message: " + message);
        return message;

    }

    @Step("Set General Settings Details")
    public String setGeneralSettingsDetails(RemoteWebDriver driver , GeneralSettings generalSettings) throws Exception {
        Allure.step("Setting general settings details.");
        Navigation.ADVANCED_SETTINGS.navigate(driver);
        advancedSettingsControl.navigateToBusinessLogic(driver);
        generalSettingsManager.setZone(driver, generalSettings.getZone());
        setAppearanceSettingsDetails(driver, generalSettings);
        setPhoneticSettingsDetails(driver, generalSettings);
        setOtherSettingsDetails(driver, generalSettings);
        clickSave(driver);
        String message = commonAction.getAlertMessageString(driver);
        Allure.step("Message: " + message);
        return message;
    }

    @Step("Alerts/Detections comments Mode")
    public String alertsDetectionsCommentMode(RemoteWebDriver driver, GeneralSettings generalSettings) throws Exception
    {
        Allure.step("Setting general settings details.");
        Navigation.ADVANCED_SETTINGS.navigate(driver);
        advancedSettingsControl.navigateToBusinessLogic(driver);
        generalSettingsManager.setZone(driver, generalSettings.getZone());
        generalSettingsManager.setMaxDataCharacters(driver, generalSettings.getMaxDataCharacters());
        generalSettingsManager.setMaxCommentCharacters(driver,generalSettings.getMaxCommentsCharacters());
        generalSettingsManager.clickAllowDetectionDoNotKnow(driver, generalSettings.isAllowDetectionDontKnow());
        generalSettingsManager.clickEnableAutomaticSearchForModifiedDetection(driver, generalSettings.isAllowDetectionDontKnow());
        generalSettingsManager.clickEnableAuditTrailForAllScanRequests(driver, generalSettings.isEnableAuditTrail());
        generalSettingsManager.setAlertsToBeCreatedWithCommentsMode(driver, generalSettings.getAlertsDetectionsToBeCreatedModifiedWithCommentsMode());
        clickSave(driver);
        String message = commonAction.getAlertMessageString(driver);
        Allure.step("Message: " + message);
        return message;

    }


    @Step("Click Save button")
    public void clickSave(RemoteWebDriver driver) {
        generalSettingsManager.clickSave(driver);
    }

    @Step("Click Cancel button")
    public void clickCancel(RemoteWebDriver driver) {
        generalSettingsManager.clickCancel(driver);
    }

    @Step("Set Phonetic Settings Details")
    public String updatePhoneticSettingsDetails(RemoteWebDriver driver, GeneralSettings generalSettings) throws InterruptedException {
        advancedSettingsControl.navigateToBusinessLogic(driver);
        setPhoneticSettingsDetails(driver, generalSettings);
        clickSave(driver);
        return commonAction.getAlertMessageString(driver);
    }
}