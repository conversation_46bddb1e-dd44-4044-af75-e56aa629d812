package eastnets.screening.control.AdvancedSettingsControls;

import eastnets.common.control.CommonAction;
import eastnets.common.gui.Navigation;
import eastnets.screening.gui.advancedSettings.Investigators.InvestigatorsEditor;
import eastnets.screening.gui.advancedSettings.Investigators.InvestigatorsManager;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;

public class InvestigatorsControl {

    private final AdvancedSettingsControl advancedSettingsControl;
    private final InvestigatorsManager investigatorsManager;
    private final InvestigatorsEditor investigatorsEditor;
    private final CommonAction commonAction;

    public InvestigatorsControl() {
        this.advancedSettingsControl = new AdvancedSettingsControl();
        this.investigatorsManager = new InvestigatorsManager();
        this.investigatorsEditor = new InvestigatorsEditor();
        this.commonAction = new CommonAction();
    }

    @Step("Navigate to investigator tab")
    public void navigateToInvestigatorsTab(RemoteWebDriver driver) {
        Navigation.ADVANCED_SETTINGS.navigate(driver);
        advancedSettingsControl.navigateToInvestigators(driver);
    }

    @Step("Assign Investigator role as Checker only")
    public String assignInvestigatorRoleChecker(RemoteWebDriver driver, String name, boolean checkCheckers) throws InterruptedException {
        navigateToInvestigatorsTab(driver);
        investigatorsManager.setName(driver, name);
        investigatorsManager.clickSearchButton(driver);
        investigatorsManager.clickNameInTable(driver, name);
        investigatorsEditor.checkCheckerRoles(driver, checkCheckers);
        investigatorsEditor.checkHidden(driver, checkCheckers);
        investigatorsEditor.clickSave(driver);
        return commonAction.getAlertMessageString(driver);
    }


    @Step("Assign Investigator role as Maker only")
    public void assignInvestigatorRoleMaker(RemoteWebDriver driver, String name, String nameInTable, boolean checkCheckers, boolean checkMayActAsMaker) {
        navigateToInvestigatorsTab(driver);
        investigatorsManager.setName(driver, name);
        investigatorsManager.clickSearchButton(driver);
        investigatorsManager.clickNameInTable(driver, nameInTable);
        investigatorsEditor.checkCheckerRoles(driver, checkCheckers);
        investigatorsEditor.checkMayActAsMaker(driver, checkMayActAsMaker);
        investigatorsEditor.clickSave(driver);
    }


}
