package eastnets.screening.control.AdvancedSettingsControls;

import core.util.Wait;
import eastnets.common.gui.Navigation;
import eastnets.screening.gui.advancedSettings.AdvancedSettingNavigation;
import eastnets.screening.gui.advancedSettings.replaySettings.ReplaySettingEditor;
import eastnets.screening.gui.advancedSettings.replaySettings.ReplaySettingManager;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.util.List;

public class ReplaySettingControls {

    private final AdvancedSettingNavigation advancedSettingNavigation;
    private final ReplaySettingManager replaySettingManager;
    private final ReplaySettingEditor replaySettingEditor;
    private final Wait wait;

    public ReplaySettingControls() {
        this.advancedSettingNavigation = AdvancedSettingNavigation.REPLAY_SETTING;
        this.replaySettingManager = new ReplaySettingManager();
        this.replaySettingEditor = new ReplaySettingEditor();
        this.wait = new Wait();
    }

    @Step("Navigate to replay setting")
    public void navigate_to_replay_setting(RemoteWebDriver driver) {
        Navigation.ADVANCED_SETTINGS.navigate(driver);
        advancedSettingNavigation.navigate(driver);
    }

    @Step("Get available black list")
    public List<String> get_available_black_lists(RemoteWebDriver driver, String blackListName) throws InterruptedException {
        navigate_to_replay_setting(driver);
        replaySettingManager.click_add_button(driver);
        replaySettingEditor.set_black_list_filter(driver, blackListName);
        wait.time(Wait.ONE_SECOND * 5);
        List<String> balckList = replaySettingEditor.get_available_black_lists(driver);
        replaySettingEditor.click_cancel_button(driver);
        return balckList;
    }

}
