package eastnets.screening.control.AdvancedSettingsControls;

import eastnets.common.gui.Navigation;
import eastnets.screening.gui.advancedSettings.AdvancedSettingNavigation;
import eastnets.screening.gui.advancedSettings.GoodGuysCategoriesManager;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;

public class GoodGuysCategoriesControl {

    private final AdvancedSettingNavigation advancedSettingNavigation;
    private final GoodGuysCategoriesManager goodGuysCategoriesManager;

    public GoodGuysCategoriesControl() {
        this.advancedSettingNavigation = AdvancedSettingNavigation.GOOD_GUYS_CATEGORIES;
        this.goodGuysCategoriesManager = new GoodGuysCategoriesManager();
    }

    @Step("Navigate to Good Guys Categories")
    public void navigateToGoodGuysCategories(RemoteWebDriver driver) {
        Navigation.ADVANCED_SETTINGS.navigate(driver);
        advancedSettingNavigation.navigate(driver);
    }

    @Step("Add Good Guy Category")
    public void addGoodGuyCategory(RemoteWebDriver driver, String categoryName) {
        navigateToGoodGuysCategories(driver);
        goodGuysCategoriesManager.enterCategoryName(driver, categoryName);
        goodGuysCategoriesManager.clickAddCategoryButton(driver);
    }
}
