package eastnets.screening.control;

import core.gui.Controls;
import core.util.Wait;
import eastnets.common.control.CommonAction;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import eastnets.screening.control.listManager.ListExplorerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.gui.approvalConfiguration.*;
import eastnets.screening.gui.listManager.listExplorer.ListExplorerEditor;
import eastnets.screening.gui.listManager.listExplorer.ListExplorerManager;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.io.IOException;
import java.sql.SQLException;
import java.util.List;


public class ApprovalControl {

    private final static By NO_RECORDS_MSG = By.xpath("//*[.='No records found.']");
    private static final By CHECK_BOX_LOCATOR_LIST_MANAGER = By.xpath("//div[contains(@class,'ui-selectbooleancheckbox ui-chkbox ui-widget')]");

    private final ApprovalGroupsManager approvalGroupsManager;
    private final ApprovalGroupsEditor approvalGroupsEditor;
    private final ConfigurationsManager configurationsManager;
    private final ConfigurationsEditor configurationsEditor;
    private final WorkflowApprovals workflowApprovals;
    private final CommonAction commonAction;
    private final Controls controls;
    private final Wait wait;
    private final ScreeningServicesDelegate screeningServicesDelegate;
    private final ListExplorerControl listExplorerControl;
    private final ListExplorerManager listExplorerManager;
    private final ListExplorerEditor listExplorerEditor;

    public ApprovalControl() {
        this.approvalGroupsManager = new ApprovalGroupsManager();
        this.approvalGroupsEditor = new ApprovalGroupsEditor();
        this.configurationsManager = new ConfigurationsManager();
        this.configurationsEditor = new ConfigurationsEditor();
        this.workflowApprovals = new WorkflowApprovals();
        this.commonAction = new CommonAction();
        this.controls = new Controls();
        this.wait = new Wait();
        this.screeningServicesDelegate = new ScreeningServicesDelegate();
        this.listExplorerControl = new ListExplorerControl();
        this.listExplorerManager = new ListExplorerManager();
        this.listExplorerEditor = new ListExplorerEditor();
    }

    @Step("Click Approval Module Icon")
    public void clickApprovalModuleIcon(RemoteWebDriver driver) throws InterruptedException {
        Allure.step("Click Approval Module Icon");
        By icon = By.className("ApprovalModuleClass");
        controls.performClick(driver, icon);
//        Navigation.APPROVAL.navigate(driver);
    }

    @Step("Add group")
    public String createGroup(RemoteWebDriver driver, String groupName, String zoneName, String operatorName) throws Exception {

        approvalGroupsManager.clickApprovalGroupsTab(driver);
        approvalGroupsManager.clickAddButton(driver);
        approvalGroupsEditor.writeGroupName(driver, groupName);
//        approvalGroupsEditor.chooseZone(driver, zoneName);
        approvalGroupsEditor.search_Operator(driver, operatorName);
        approvalGroupsEditor.addAllOperators(driver);
        approvalGroupsEditor.enableToggleSwitch(driver);
        approvalGroupsEditor.addGroup(driver);
        return commonAction.getAlertMessageString(driver);
    }

    @Step("Add permission")
    public String addPermission(RemoteWebDriver driver, String groupName, ConfigurationsManager.PERMISSIONS permission, String zoneName) throws Exception {
        clickApprovalModuleIcon(driver);
        configurationsManager.clickConfigurationTab(driver);
        configurationsManager.clickID(driver, permission);
        configurationsEditor.searchZone(driver, zoneName);
        configurationsEditor.clickAddAll(driver);
        configurationsEditor.clickUpdateButtonUpdateTemplate(driver);
        configurationsManager.clickCheckBox(driver, permission);
        configurationsManager.clickViewStepsButton(driver);
        configurationsManager.clickStepID(driver, permission);
//        configurationsEditor.chooseZone(driver,zoneName);
        configurationsEditor.clickRemoveAll(driver);
        configurationsEditor.searchGroup(driver, groupName);
        configurationsEditor.clickAddAll(driver);
        configurationsEditor.clickUpdateButtonUpdateStep(driver);
        return commonAction.getAlertMessageString(driver);
    }

    @Step("Update Zone")
    public String updateZone(RemoteWebDriver driver, ConfigurationsManager.PERMISSIONS permission, String zoneName) throws Exception {
        Allure.step("Update Zone");
        clickApprovalModuleIcon(driver);
        configurationsManager.clickConfigurationTab(driver);
        configurationsManager.clickID(driver, permission);
        configurationsEditor.searchZone(driver, zoneName);
        configurationsEditor.clickAddAll(driver);
        return configurationsEditor.clickUpdateButtonUpdateTemplate(driver);
    }


    @Step("get approve request success message")
    public String getApproveRequestSuccessMessage(RemoteWebDriver driver) throws Exception {
        Allure.step("Get Approve request success message");
        String validationMessage = commonAction.getAlertMessageString(driver);
        return validationMessage;
    }

    @Step("Check Request Status")
    public String checkRequestStatus(RemoteWebDriver driver, String templateName) throws Exception {
        Allure.step("Check request status");
        wait.time(Wait.ONE_SECOND * 5);
        return workflowApprovals.getApprovalStatus(driver, templateName);
    }

    @Step("Verify Delete Request created successfully")
    public String getDeletedRequestStatus(RemoteWebDriver driver) throws Exception {
        clickApprovalModuleIcon(driver);
        workflowApprovals.clickWorkflowApprovalsTab(driver);
        return workflowApprovals.getApprovalStatus(driver, "Delete blacklist entry");
    }

    @Step("Verify Delete Request created successfully")
    public String getRequestStatus(RemoteWebDriver driver, WorkflowApprovals.TEMPLATE_NAME templateName) throws Exception {
        clickApprovalModuleIcon(driver);
        workflowApprovals.clickWorkflowApprovalsTab(driver);
        return workflowApprovals.getApprovalStatus(driver, templateName.getTemplateName());
    }


    @Step("Reject a Request")
    public String rejectRequest(RemoteWebDriver driver, WorkflowApprovals.TEMPLATE_NAME templateName, String rejectionReason) throws Exception {
        workflowApprovals.clickCheckBox(driver, templateName);
        workflowApprovals.clickReject(driver);
        workflowApprovals.chooseRejectionReason(driver, rejectionReason);
        workflowApprovals.writeRejectionRemarks(driver);
        workflowApprovals.clickConfirmButton(driver);
        return commonAction.getAlertMessageString(driver);

    }

    @Step("Get Enable public list message")
    public String getEnablePublicListMessage(RemoteWebDriver driver) throws Exception {
        Allure.step("get Enable public list message");
        driver.switchTo().alert().accept();
        String validationMessage = commonAction.getAlertMessageString(driver);
        return validationMessage;
    }

    @Step("Disable first entity")
    public String disableFirstEntity(RemoteWebDriver driver, String zone, String bList) throws Exception {
        listExplorerControl.navigate(driver);
        listExplorerControl.search(driver, zone, bList, "");
        listExplorerManager.click_check_box(driver);
        listExplorerManager.click_disable_button(driver);
        controls.acceptAlert(driver);
        return commonAction.getAlertMessageString(driver);
    }

    @Step("Disable first entity")
    public String editFirstEntity(RemoteWebDriver driver) throws Exception {
        listExplorerManager.select_first_entry(driver);
        listExplorerEditor.set_entry_name(driver, "test");
        listExplorerEditor.click_save_button(driver);
        return commonAction.getAlertMessageString(driver);
    }

    @Step("Enable first entity")
    public String enableFirstEntity(RemoteWebDriver driver) throws InterruptedException {
        listExplorerManager.click_check_box(driver);
        listExplorerManager.click_enable_button(driver);
        controls.acceptAlert(driver);
        wait.time(Wait.ONE_SECOND * 5);
        return commonAction.getAlertMessageString(driver);
    }

    @Step("Remove blacklist before import")
    public void deleteBlacklistBeforeImport(RemoteWebDriver driver, String zone, String blist) throws SQLException, IOException {
        Allure.step("Remove blacklist before import");
        screeningServicesDelegate.deleteLinkBtwBlackListAndListSet(zone
                , blist);
        screeningServicesDelegate.deleteLinkBtwBlackListAndGoodGuy(zone
                , blist);
        screeningServicesDelegate.deleteBlackList(blist);
    }

    @Step("Update Entry for Resubmit")
    public String updateEntryForResubmit(RemoteWebDriver driver, EnList list) throws Exception {
        Allure.step("Update Entry for Resubmit");
        list.getEntry().get(0).setName("Test");
        list.getEntry().get(0).setFirstName("Test");
        listExplorerControl.set_type_names_data(driver, list.getEntry().get(0));
        listExplorerEditor.click_save_button(driver);
        String validationMessage = commonAction.getAlertMessageString(driver);
        return validationMessage;
    }

    @Step("Filter Requests by Status")
    public void filterRequestsByStatus(RemoteWebDriver driver, String status) throws Exception {
        workflowApprovals.chooseStatus(driver, status);
        workflowApprovals.clickSearchButton(driver);
    }

    @Step("Filter Requests by Action")
    public void filterRequestsByAction(RemoteWebDriver driver, String action) throws Exception {
        workflowApprovals.chooseAction(driver, action);
        workflowApprovals.clickSearchButton(driver);
    }

    @Step("Check Status For All Requests")
    public boolean checkStatusForAllRequests(RemoteWebDriver driver, String status) {
        boolean flag = false;
        List<WebElement> rows = workflowApprovals.getRequestsRows(driver);

        for (int i = 1; i < rows.size(); i++) {
            List<WebElement> rowItems = rows.get(i).findElements(By.xpath("//td[@role ='gridcell']"));
            if (rowItems.get(5).getText().equalsIgnoreCase(status)) {
                flag = true;
                continue;
            } else {
                flag = false;
                break;
            }
        }
        return flag;
    }

    @Step("Check Action For All Requests")
    public boolean checkActionForAllRequests(RemoteWebDriver driver, String action) {
        boolean flag = false;
        List<WebElement> rows = workflowApprovals.getRequestsRows(driver);

        for (int i = 1; i < rows.size(); i++) {
            List<WebElement> rowItems = rows.get(i).findElements(By.xpath("//td[@role ='gridcell']"));
            if (rowItems.get(2).getText().equalsIgnoreCase(action)) {
                flag = true;
                continue;
            } else {
                flag = false;
                break;
            }
        }
        return flag;
    }

    public boolean verifyRequestCreated(RemoteWebDriver driver) throws InterruptedException {
        String verifyMessage = commonAction.getAlertMessageString(driver);
        String partialMessage = "Approval request(s) added successfully with id(s)";
        return verifyMessage.contains(partialMessage);
    }

    public String approveRequest(RemoteWebDriver driver, WorkflowApprovals.TEMPLATE_NAME templateName) throws Exception {
        workflowApprovals.clickCheckBox(driver, templateName);
        workflowApprovals.clickApprove(driver);
        return getApproveRequestSuccessMessage(driver);
    }

    public void selectRequest(RemoteWebDriver driver, WorkflowApprovals.TEMPLATE_NAME templateName) throws InterruptedException {
        clickApprovalModuleIcon(driver);
        workflowApprovals.clickWorkflowApprovalsTab(driver);
        workflowApprovals.clickCheckBox(driver, templateName);
    }

    public String deleteGroup(RemoteWebDriver driver, String groupName) throws InterruptedException {
        Allure.step("Delete approval group");
        clickApprovalModuleIcon(driver);
        approvalGroupsManager.clickApprovalGroupsTab(driver);
        approvalGroupsManager.clickNavigateToLastPageButton(driver);
        approvalGroupsManager.clickCheckBox(driver, groupName);
        approvalGroupsManager.clickDeleteButton(driver);
        controls.acceptAlert(driver);
        System.out.println("return alert message here");
        return commonAction.getAlertMessageString(driver);
    }

    public String updateGroup(RemoteWebDriver driver, String groupName) throws InterruptedException {
        Allure.step("Update approval group");
        clickApprovalModuleIcon(driver);
        approvalGroupsManager.clickApprovalGroupsTab(driver);
        approvalGroupsManager.clickNavigateToLastPageButton(driver);
        workflowApprovals.clickWorkflowApprovalsTab(driver);
        approvalGroupsManager.clickApprovalGroupsTab(driver);
        approvalGroupsManager.clickID(driver, groupName);
        approvalGroupsEditor.writeGroupName(driver, groupName + " updatedGroup");
        approvalGroupsEditor.addGroup(driver);
        return commonAction.getAlertMessageString(driver);
    }

    public boolean verifyGenderOptionExists(RemoteWebDriver driver, EnList list, String gender) throws Exception {

        return listExplorerEditor.select_gender(driver, gender);


    }


}
