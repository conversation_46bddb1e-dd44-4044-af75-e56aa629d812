package eastnets.screening.control.dbManager;

import core.util.Wait;
import eastnets.common.gui.Navigation;
import eastnets.screening.gui.dbManager.DBManagerNavigation;
import eastnets.screening.gui.dbManager.dbScanRequest.DBScanReqEditor;
import eastnets.screening.gui.dbManager.dbScanRequest.DBScanReqManager;
import org.openqa.selenium.remote.RemoteWebDriver;

public class DBScanReqControl {

    private final DBManagerNavigation dbManagerNavigation;
    private final DBScanReqManager dbScanReqManager;
    private final DBScanReqEditor dbScanReqEditor;
    private final Wait wait;

    public DBScanReqControl() {
        this.dbManagerNavigation = DBManagerNavigation.DB_SCAN_REQUEST;
        this.dbScanReqManager = new DBScanReqManager();
        this.dbScanReqEditor = new DBScanReqEditor();
        this.wait = new Wait();
    }

    public void navigate_to_db_scan_req(RemoteWebDriver driver) {
        Navigation.DB_MANAGER.navigate(driver);
        dbManagerNavigation.navigate(driver);
    }

    public String get_process_status(RemoteWebDriver driver) throws InterruptedException {
        navigate_to_db_scan_req(driver);
        int trials= 30;
        while (trials > 0) {
            dbScanReqManager.click_search_button(driver);
            if (dbScanReqManager.get_request_status(driver).contains("End")) {
                break;
            }
            if( trials == 1) {
                return "process was not completed";
            }
            wait.time(Wait.ONE_SECOND*3);
            trials--;
        }
        dbScanReqManager.click_on_first_request_checkbox(driver);
        dbScanReqManager.click_on_view_process_details_button(driver);
        return dbScanReqManager.get_process_status(driver);
    }

    public String get_scan_session_id(RemoteWebDriver driver) {
        dbScanReqManager.click_on_first_process_checkbox(driver);
        dbScanReqManager.click_on_view_running_process_details_button(driver);
        String session_id = dbScanReqEditor.get_scan_session_id(driver);
        return session_id;

    }

    public String get_process_status_from_details_tab(RemoteWebDriver driver) {
        DBScanReqEditor.DBScanReqNavigation.PROCESS_DETAILS.navigate(driver);
        return dbScanReqEditor.get_process_status(driver);
    }

    public String get_total_records_updated(RemoteWebDriver driver) {
        DBScanReqEditor.DBScanReqNavigation.STATISTICS.navigate(driver);
        return dbScanReqEditor.get_total_records_updated_value(driver);
    }

    public void close_process_details(RemoteWebDriver driver) {
        dbScanReqEditor.click_close_button(driver);
    }
}
