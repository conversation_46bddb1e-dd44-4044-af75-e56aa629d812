package eastnets.screening.control.dbManager;

import eastnets.common.control.CommonAction;
import eastnets.common.gui.Navigation;
import eastnets.screening.entity.DBConfigurations;
import eastnets.screening.gui.dbManager.dbFlowConfiguration.DBConfigEditor;
import eastnets.screening.gui.dbManager.dbFlowConfiguration.DBConfigManager;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.util.List;

public class DBFlowConfigControl {

    private final DBConfigManager dbConfigManager;
    private final DBConfigEditor dbConfigEditor;
    private final CommonAction commonAction;

    public DBFlowConfigControl() {
        this.dbConfigManager = new DBConfigManager();
        this.dbConfigEditor = new DBConfigEditor();
        this.commonAction = new CommonAction();
    }

    @Step("Navigate to DB Manager")
    public void navigateToDBManager(RemoteWebDriver driver)
    {
        Navigation.DB_MANAGER.navigate(driver);
    }


    @Step("Search for DB configuration")
    public boolean search(RemoteWebDriver driver, DBConfigurations dbConfigurations) throws Exception {
        navigateToDBManager(driver);
        dbConfigManager.select_zone(driver, dbConfigurations.getZoneName());
        dbConfigManager.set_name(driver, dbConfigurations.getName());
        dbConfigManager.select_database_type(driver, dbConfigurations.getDbType());
        dbConfigManager.select_flow_type(driver, dbConfigurations.getType());
        dbConfigManager.click_search_button(driver);
        return dbConfigManager.is_no_record_found_message_displayed(driver);
    }

    @Step("Search for DB configuration by name")
    public boolean search_by_name(RemoteWebDriver driver, String db_name) throws Exception {
        navigateToDBManager(driver);
        dbConfigManager.set_name(driver, db_name);
        dbConfigManager.click_search_button(driver);
        return dbConfigManager.is_no_record_found_message_displayed(driver);
    }

    @Step("Set DB Flow Details")
    public void set_db_flow_information(RemoteWebDriver driver, DBConfigurations dbConfigurations) throws Exception {
        Allure.step("Set Flow Information: ");
        dbConfigEditor.select_zone(driver, dbConfigurations.getZoneName());
        dbConfigEditor.select_type(driver, dbConfigurations.getType());
        dbConfigEditor.set_name(driver, dbConfigurations.getName());
        dbConfigEditor.select_database_type(driver, dbConfigurations.getDbType());
        dbConfigEditor.set_table_name(driver, dbConfigurations.getTableName());
        dbConfigEditor.select_output_flow(driver, dbConfigurations.getOutputFlow());
    }

    @Step("Set DB Login Information")
    public void set_db_login_info(RemoteWebDriver driver, DBConfigurations dbConfigurations)
    {
        Allure.step("Set Database Login Information: ");
        dbConfigEditor.set_db_name(driver, dbConfigurations.getDbName());
        dbConfigEditor.set_host(driver, dbConfigurations.getHost());
        dbConfigEditor.set_port(driver, dbConfigurations.getPort());
        dbConfigEditor.set_user_name(driver, dbConfigurations.getUserName());
        dbConfigEditor.set_password(driver, dbConfigurations.getPassword());
    }

    @Step("Add field details")
    public void add_field(RemoteWebDriver driver, DBConfigurations dbConfigurations) throws Exception {

        dbConfigEditor.click_add_all_button(driver);
        for (int i = 0; i < dbConfigurations.getDbFields().size(); i++) {
            dbConfigEditor.select_field(driver, dbConfigurations.getDbFields().get(i).getName());
            dbConfigEditor.click_show_button(driver);
            dbConfigEditor.select_field_type(driver, dbConfigurations.getDbFields().get(i).getType());
            dbConfigEditor.click_scan_check_box(driver, dbConfigurations.getDbFields().get(i).isScanFlag());
            dbConfigEditor.click_primary_key_check_box(driver, dbConfigurations.getDbFields().get(i).isPrimaryKeyFlag());

        }
        dbConfigEditor.click_save_field_button(driver);
    }

    @Step("Select field details")
    public String select_field_details(RemoteWebDriver driver, String db_name, String password, String filed_name) throws Exception {
        navigateToDBManager(driver);
        search_by_name(driver, db_name);
        dbConfigManager.select_flow_by_name_from_result(driver, db_name);
        dbConfigEditor.set_password(driver, password);
        dbConfigEditor.click_show_fields_button(driver);
        String result = commonAction.getAlertMessageString(driver);
        if (result == null) {
            dbConfigEditor.click_add_all_button(driver);
            dbConfigEditor.select_field(driver, filed_name);
            dbConfigEditor.click_show_button(driver);
            return commonAction.getAlertMessageString(driver);
        }
        return result;
    }

    @Step("Verify filed name is disabled")
    public boolean verify_field_name_disabled(RemoteWebDriver driver) {
        return dbConfigEditor.is_field_name_disabled(driver);
    }

    @Step("Get all values from field type list")
    public List<String> get_all_field_types(RemoteWebDriver driver) {

        return dbConfigEditor.get_all_field_types(driver);
    }

    @Step("Verify 'scan' checkbox is displayed")
    public boolean verify_scan_checkbox_displayed(RemoteWebDriver driver) {
        return dbConfigEditor.is_scan_check_box_displayed(driver);
    }

    @Step("Verify 'primary key' checkbox is displayed")
    public boolean verify_primary_key_checkbox_displayed(RemoteWebDriver driver) {
        return dbConfigEditor.is_primary_key_check_box_displayed(driver);
    }

    //the same as the above method but for add to context field
    @Step("Verify 'add to context' checkbox is displayed")
    public boolean verify_add_to_context_checkbox_displayed(RemoteWebDriver driver) {
        return dbConfigEditor.is_add_to_context_check_box_displayed(driver);
    }

    @Step("Verify 'output' checkbox is displayed")
    public boolean verify_output_checkbox_displayed(RemoteWebDriver driver) {
        return dbConfigEditor.is_output_check_box_displayed(driver);
    }

    @Step("Verify 'parameter' checkbox is displayed")
    public boolean verify_parameter_checkbox_displayed(RemoteWebDriver driver) {
        return dbConfigEditor.is_parameter_check_box_displayed(driver);
    }

    @Step("Verify when 'Foreign Key' checkbox is displayed")
    public boolean verify_foreign_key_checkbox_displayed(RemoteWebDriver driver) {
        return dbConfigEditor.is_foreign_key_check_box_displayed(driver);
    }


    @Step("Verify when 'scan this field' checkbox is checked")
    public boolean verify_scan_checkbox_checked(RemoteWebDriver driver) {
        dbConfigEditor.click_scan_check_box(driver, true);
        boolean flag =  verify_scan_checkbox_displayed(driver)
                && verify_primary_key_checkbox_displayed(driver)
                && verify_add_to_context_checkbox_displayed(driver)
                && !verify_output_checkbox_displayed(driver)
                && verify_parameter_checkbox_displayed(driver);
        dbConfigEditor.click_scan_check_box(driver, true);
        return flag;

    }

    @Step("Verify when 'primary key' checkbox is checked")
    public boolean verify_primary_key_checkbox_checked_input_flow(RemoteWebDriver driver) {
        dbConfigEditor.click_primary_key_check_box(driver, true);
        boolean flag =  !verify_scan_checkbox_displayed(driver)
                && verify_primary_key_checkbox_displayed(driver)
                && !verify_add_to_context_checkbox_displayed(driver)
                && !verify_output_checkbox_displayed(driver)
                && verify_parameter_checkbox_displayed(driver);
        dbConfigEditor.click_primary_key_check_box(driver, true);
        return flag;
    }

    @Step("Verify when 'primary key' checkbox is checked")
    public boolean verify_primary_key_checkbox_checked_output_flow(RemoteWebDriver driver) {
        dbConfigEditor.click_primary_key_check_box(driver, true);
        boolean flag = verify_primary_key_checkbox_displayed(driver)
                && !verify_output_checkbox_displayed(driver)
                && !verify_foreign_key_checkbox_displayed(driver);
        dbConfigEditor.click_primary_key_check_box(driver, true);
        return flag;
    }

    @Step("Verify when 'add to context' checkbox is checked")
    public boolean verify_add_to_context_checkbox_checked(RemoteWebDriver driver) {
        dbConfigEditor.click_add_to_context_check_box(driver, true);
        boolean flag =  verify_scan_checkbox_displayed(driver)
                && verify_primary_key_checkbox_displayed(driver)
                && verify_add_to_context_checkbox_displayed(driver)
                && verify_output_checkbox_displayed(driver)
                && verify_parameter_checkbox_displayed(driver);
        dbConfigEditor.click_add_to_context_check_box(driver, true);
        return flag;
    }

    @Step("Verify when 'output' checkbox is checked")
    public boolean verify_output_checkbox_checked_input_flow(RemoteWebDriver driver) {
        dbConfigEditor.click_output_check_box(driver, true);
        boolean flag = !verify_scan_checkbox_displayed(driver)
                && !verify_primary_key_checkbox_displayed(driver)
                && !verify_add_to_context_checkbox_displayed(driver)
                && verify_output_checkbox_displayed(driver)
                && verify_parameter_checkbox_displayed(driver);
        dbConfigEditor.click_output_check_box(driver, true);
        return flag;
    }

    @Step("Verify when 'output' checkbox is checked")
    public boolean verify_output_checkbox_checked_output_flow(RemoteWebDriver driver) {
        dbConfigEditor.click_output_check_box(driver, true);
        boolean flag =  !verify_primary_key_checkbox_displayed(driver)
                && verify_output_checkbox_displayed(driver)
                && !verify_foreign_key_checkbox_displayed(driver);
        dbConfigEditor.click_output_check_box(driver, true);
        return flag;
    }

    @Step("Verify when 'parameter' checkbox is checked")
    public boolean verify_parameter_checkbox_checked(RemoteWebDriver driver) {
        dbConfigEditor.click_parameter_check_box(driver, true);
        boolean flag =  verify_scan_checkbox_displayed(driver)
                && verify_primary_key_checkbox_displayed(driver)
                && verify_add_to_context_checkbox_displayed(driver)
                && verify_output_checkbox_displayed(driver)
                && verify_parameter_checkbox_displayed(driver);

        dbConfigEditor.click_parameter_check_box(driver, true);

        return flag;
    }

    @Step("Verify when 'Foreign Key' checkbox is checked")
    public boolean verify_foreign_key_checkbox_checked(RemoteWebDriver driver) {
        dbConfigEditor.click_foreign_key_check_box(driver, true);
        boolean flag = !verify_primary_key_checkbox_displayed(driver)
                && !verify_output_checkbox_displayed(driver)
                && verify_foreign_key_checkbox_displayed(driver);
        dbConfigEditor.click_foreign_key_check_box(driver, true);
        return flag;
    }


    @Step("Create New DB Configurations")
    public String create_new_db_configurations(RemoteWebDriver driver, DBConfigurations dbConfigurations) throws Exception {
        navigateToDBManager(driver);
        dbConfigManager.click_add_new_db_button(driver);
        set_db_flow_information(driver, dbConfigurations);
        set_db_login_info(driver, dbConfigurations);
        dbConfigEditor.click_test_connection_button(driver);
        return commonAction.getAllAlertMessageString(driver);

    }

    @Step("Save DB Configurations")
    public String save_db_configurations(RemoteWebDriver driver, DBConfigurations dbConfigurations) throws Exception {
        dbConfigEditor.set_password(driver, dbConfigurations.getPassword());
        dbConfigEditor.click_save_button(driver);
        return commonAction.getAlertMessageString(driver);
    }

    @Step("Save DB Configurations")
    public boolean click_show_filed_btn(RemoteWebDriver driver, DBConfigurations dbConfigurations) throws Exception {
        search(driver, dbConfigurations);
        dbConfigManager.select_flow_by_name_from_result(driver, dbConfigurations.getName());
        dbConfigEditor.set_password(driver, dbConfigurations.getPassword());
        dbConfigEditor.click_show_fields_button(driver);
        return dbConfigEditor.check_show_button_is_displayed(driver);
    }

    @Step("Add Field Details")
    public String add_field_details(RemoteWebDriver driver, DBConfigurations dbConfigurations) throws Exception {
        boolean result = click_show_filed_btn(driver, dbConfigurations);
        if (result) {
            add_field(driver, dbConfigurations);
            return commonAction.getAlertMessageString(driver);
        }
        return "Error in opening field details page";
    }

    @Step("Get all available zones")
    public List<String> get_all_zones(RemoteWebDriver driver) {
        navigateToDBManager(driver);
        dbConfigManager.click_add_new_db_button(driver);
        return dbConfigEditor.get_all_zones(driver);
    }

    @Step("Get all available output database types")
    public List<String> get_all_output_db_types(RemoteWebDriver driver) throws Exception {
        navigateToDBManager(driver);
        dbConfigManager.click_add_new_db_button(driver);
        dbConfigEditor.select_type(driver, "INPUT");
        return dbConfigEditor.get_all_output_flows(driver);
    }

    @Step("Create DB Confgurations without testing connection")
    public String create_db_configurations_without_testing_connection(RemoteWebDriver driver, DBConfigurations dbConfigurations) throws Exception {
        navigateToDBManager(driver);
        dbConfigManager.click_add_new_db_button(driver);
        set_db_flow_information(driver, dbConfigurations);
        set_db_login_info(driver, dbConfigurations);
        dbConfigEditor.click_save_button(driver);
        return commonAction.getAlertMessageString(driver);
    }

    @Step("Save DB Configurations without enter any data")
    public String save_db_configurations_without_entering_data(RemoteWebDriver driver, DBConfigurations dbConfigurations) throws Exception {
        navigateToDBManager(driver);
        dbConfigManager.click_add_new_db_button(driver);
        dbConfigEditor.click_save_button(driver);
        return commonAction.getAllAlertMessageString(driver);
    }

    @Step("Cancel DB Configurations")
    public boolean cancel_db_configurations(RemoteWebDriver driver) {
        navigateToDBManager(driver);
        dbConfigManager.click_add_new_db_button(driver);
        dbConfigEditor.click_cancel_button(driver);
        return dbConfigManager.is_add_new_db_button_displayed(driver);
    }

    @Step(" Verify reset search functionality")
    public boolean verify_reset_search(RemoteWebDriver driver, DBConfigurations dbConfigurations) throws Exception {
        navigateToDBManager(driver);
        search(driver, dbConfigurations);
        dbConfigManager.click_reset_button(driver);
        return dbConfigManager.is_no_record_found_message_displayed(driver);
    }
}