package eastnets.screening.control;

import eastnets.common.gui.Navigation;
import eastnets.screening.gui.LicenseManager;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class LicenseControl {

    private static final Logger LOG = LoggerFactory.getLogger(LicenseControl.class);

    private final LicenseManager licenseManager;

    public LicenseControl() {
        this.licenseManager = new LicenseManager();
    }

    @Step("Navigate to 'Licence Manger' module")
    private void navigateToLicenseManager(RemoteWebDriver driver) {
        Navigation.LICENCE_MANAGER.navigate(driver);
    }

    @Step("Import License")
    public void importLicense(RemoteWebDriver driver, String licenseFilePath) {
        navigateToLicenseManager(driver);
        licenseManager.setLicenceFile(driver, licenseFilePath);
        licenseManager.clickImportButtn(driver);
    }
}
