package eastnets.screening.control;

import core.util.Wait;
import eastnets.screening.gui.Notifications;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.remote.RemoteWebDriver;

public class NotificationsControl {

    private final Wait wait;
    private final Notifications notifications;

    public NotificationsControl() {
        this.wait = new Wait();
        this.notifications = new Notifications();
    }

    public boolean elementExists(WebDriver driver, By element) throws InterruptedException {
        wait.time(Wait.ONE_SECOND * 2);
        return driver.findElement(element).isDisplayed();
    }

    public boolean elementHasText(WebDriver driver, By element) {
        System.out.println("Text is " + driver.findElement(element).getAttribute("value"));
        System.out.println("Result is " + driver.findElement(element).getAttribute("value").isEmpty());
        return !(driver.findElement(element).getAttribute("value").isEmpty());
    }

    public String getNotificationsCount(RemoteWebDriver driver) throws InterruptedException {
//        notifications.clickNotificationsIcon(driver);
        return notifications.getNotificationsCount(driver);
    }

    public int getNumberOfNotificationsInList(RemoteWebDriver driver) {
        return notifications.getNumberOfNotificationsInList(driver);
    }

    public void clickFirstNotification(RemoteWebDriver driver) {
        notifications.clickSearchButton(driver);
        notifications.clickFirstNotification(driver);
    }

}
