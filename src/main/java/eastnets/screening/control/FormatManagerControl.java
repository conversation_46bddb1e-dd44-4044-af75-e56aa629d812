package eastnets.screening.control;

import core.util.Wait;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.Navigation;
import eastnets.screening.entity.Format;
import eastnets.screening.gui.formatManager.FormatEditor;
import eastnets.screening.gui.formatManager.FormatManager;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;

public class FormatManagerControl {

    private final FormatManager formatManager;
    private final FormatEditor formatEditor;
    private final CommonAction commonAction;
    private final Wait wait;

    public FormatManagerControl() {
        this.formatManager = new FormatManager();
        this.formatEditor = new FormatEditor();
        this.commonAction = new CommonAction();
        this.wait = new Wait();
    }

    @Step("Navigate to 'Format Manager' tab")
    public void navigateToFormatMangerTab(RemoteWebDriver driver) {
        Navigation.FORMAT_MANAGER.navigate(driver);
    }

    @Step("Add New Format")
    public String AddNewFormat(RemoteWebDriver driver, Format format) throws Exception {
        navigateToFormatMangerTab(driver);
        formatManager.selectZone(driver, format.getZone());
        formatManager.clickSearchButton(driver);
        formatManager.clickAddNewFormatButton(driver);
        formatEditor.setZone(driver, format.getZone());
        formatEditor.setFormatName(driver, format.getName());
        formatEditor.setFormatType(driver, format.getType());
        formatEditor.setRecordDelimiter(driver, format.getRecordDelimiter());
        formatEditor.setXpath(driver, format.getXpath());
        formatEditor.setFieldDelimiter(driver, format.getFieldDelimiter());
        formatEditor.setSeparator(driver, format.getSeparator());
        formatEditor.setEntryType(driver, format.getEntryType());

        for (int i = 0; i < format.getFields().size(); i++) {
            formatEditor.clickAddNewFieldButton(driver);
            formatEditor.setFiledName(driver, format.getFields().get(i).getName());
            formatEditor.setFiledXpath(driver, format.getFields().get(i).getXpath());
            formatEditor.setFiledType(driver, format.getFields().get(i).getType());
            if (format.getType().equals("Scan")) {
                formatEditor.setScanCheckBox(driver, format.getFields().get(i).isScan());
                formatEditor.setAddToContextCheckBox(driver, format.getFields().get(i).isAddToContext());
            }
            wait.time(Wait.ONE_SECOND * 3);
        }

        formatEditor.clickSaveButton(driver);
        return commonAction.getAlertMessageString(driver);
    }

    @Step("Check Format Exist")
    public boolean checkFormatExist(RemoteWebDriver driver, Format format) throws Exception {
        wait.waitUntilLoadingLogoDisappear(driver);
        return formatManager.verifyFormatExist(driver, format.getName());
    }

    @Step("Import Format")
    public String importFormat(RemoteWebDriver driver, String zone, String filePath) throws Exception {
        navigateToFormatMangerTab(driver);
        formatManager.setBrowserButton(driver, filePath);
        formatManager.selectZoneImport(driver, zone);
        formatManager.clickImportButton(driver);
        wait.waitUntilAjaxLoaderDisappear(driver);
        return commonAction.getAlertMessageString(driver);
    }

    @Step("Export Format")
    public void exportFormat(RemoteWebDriver driver, Format format) throws Exception {
        navigateToFormatMangerTab(driver);
        formatManager.selectZone(driver, format.getZone());
        formatManager.clickSearchButton(driver);
        formatManager.clickLastPageButton(driver);
        formatManager.clickCheckbox(driver, format.getName());
        formatManager.clickExportButton(driver);
    }

    @Step("Delete Format")
    public void deleteFormat(RemoteWebDriver driver, Format format) throws Exception {
        navigateToFormatMangerTab(driver);
        formatManager.selectZone(driver, format.getZone());
        formatManager.clickSearchButton(driver);
        formatManager.clickLastPageButton(driver);
        formatManager.clickCheckbox(driver, format.getName());
        formatManager.clickDeleteButton(driver);
    }
}