package eastnets.screening.control;

import core.constants.saa.GeneralConstants;
import core.util.TextFilesHandler;
import core.util.Wait;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.Navigation;
import eastnets.screening.gui.detectionManager.DetectionEditor;
import eastnets.screening.gui.detectionManager.DetectionManager;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.util.ArrayList;


public class DetectionManagerControl {

    private final DetectionManager detectionManager;
    private final DetectionEditor detectionEditor;
    private final CommonAction commonAction;
    private final Wait wait;
    private final TextFilesHandler textFilesHandler;

    public DetectionManagerControl() {
        this.detectionManager = new DetectionManager();
        this.detectionEditor = new DetectionEditor();
        this.commonAction = new CommonAction();
        this.wait = new Wait();
        this.textFilesHandler = new TextFilesHandler();
    }

    @Step("Navigate to detection manager page")
    public void navigate(RemoteWebDriver driver) {
        Navigation.DETECTION_MANAGER.navigate(driver);
    }


    /************** Search Related Methods **************/
    @Step("Search")
    public void search(RemoteWebDriver driver, String uetr, String servTypeId, String formatFilterBy, String format) throws Exception {
        navigate(driver);
        detectionManager.click_reset_button(driver);
        detectionManager.select_format(driver, format);
        detectionManager.select_format_filter_by(driver, formatFilterBy);
        detectionManager.set_UETR(driver, uetr);
        detectionManager.set_service_type_id(driver, servTypeId);
        detectionManager.click_search_button(driver);
    }

    @Step("Search by Session ID")
    public void search_by_session_id(RemoteWebDriver driver, String sessionId)  {
        navigate(driver);
        detectionManager.click_reset_button(driver);
        detectionManager.set_session_id(driver, sessionId);
        detectionManager.click_search_button(driver);
    }


    @Step("Search by today date and get detection status")
    public String search_by_today_date(RemoteWebDriver driver) {
        detectionManager.click_reset_button(driver);
        detectionManager.select_today_date(driver);
        detectionManager.click_search_button(driver);
        String status = detectionManager.get_detection_status(driver);
        Allure.step("Detection Status = " + status);
        return status;
    }

    @Step("Search By Id and get detection status")
    public String search_by_id(RemoteWebDriver driver, String detectionId) throws InterruptedException {
        navigate(driver);
        detectionManager.click_reset_button(driver);
        detectionManager.set_id(driver, detectionId);
        detectionManager.click_search_button(driver);
        String status = detectionManager.get_detection_status(driver);
        Allure.step("Detection Status = " + status);
        return status;
    }

    @Step("Search by 'contains open alerts' flag")
    public void search_by_contains_open_alerts(RemoteWebDriver driver, boolean flag) {
        navigate(driver);
        detectionManager.click_reset_button(driver);
        detectionManager.set_contains_open_alert_checkbox(driver, flag);
        detectionManager.click_search_button(driver);
    }

    @Step("Select 4eyes option")
    public void select_four_eyes_option(RemoteWebDriver driver, String value) throws Exception {
        navigate(driver);
        detectionManager.click_reset_button(driver);
        detectionManager.select_4_eyes_option(driver, value);
        detectionManager.click_search_button(driver);
    }

    @Step("Select column name to appear in search result table")
    public void showGPITableField(RemoteWebDriver driver, String columnName) {
        navigate(driver);
        detectionManager.click_search_button(driver);
        detectionManager.click_on_options_button_in_result_table(driver);
        detectionManager.select_column_name(driver, columnName);
        detectionManager.click_on_options_button_in_result_table(driver);
    }

    @Step("Customize search columns")
    public void Customize_search_columns(RemoteWebDriver driver, String optionName) {
        navigate(driver);
        detectionManager.click_on_search_options_icon(driver);
        detectionManager.select_option_from_search_options(driver, optionName);
        detectionManager.click_on_add_option_to_active_list(driver);
        detectionManager.click_on_apply_button(driver);
    }

    @Step("Get row number for detection id")
    public int get_row_number_for_detection_id(RemoteWebDriver driver, String detectionId, String format_filter_by, String format) throws Exception {
        search(driver, null, null, format_filter_by, format);
        return detectionManager.get_row_number_for_detection_id(driver, detectionId);
    }

    @Step("Get number of detections")
    public int get_number_of_detections(RemoteWebDriver driver, String session_id) {
        search_by_session_id(driver, session_id);
        return detectionManager.get_number_of_detections_in_table(driver);
    }


    /************** Release Related Methods **************/
    @Step("Block Detection")
    public String block_detection(RemoteWebDriver driver, String detectionId, String message) throws Exception {
        search_by_id(driver, detectionId);
        detectionManager.click_on_detection(driver, detectionId);
        detectionEditor.click_block_detection_button(driver);
        detectionEditor.set_reason_message(driver, message);
        detectionEditor.click_save_button(driver);
        detectionEditor.click_ok_button(driver);
        return commonAction.getAlertMessageStringWithoutWaitForAjaxLoader(driver);
    }

    @Step("Block Detection")
    public String block_detection_without_click_on_detection(RemoteWebDriver driver, String detectionId, String message) throws Exception {
        search_by_id(driver, detectionId);
        detectionEditor.click_block_detection_button(driver);
        detectionEditor.set_reason_message(driver, message);
        detectionEditor.click_save_button(driver);
        detectionEditor.click_ok_button(driver);
        return commonAction.getAlertMessageStringWithoutWaitForAjaxLoader(driver);
    }

    @Step("Block Detection")
    public String block_detection_4_eyes(RemoteWebDriver driver, String detectionId, String message, String checkerName) throws Exception {
        search_by_id(driver, detectionId);
        detectionManager.click_on_detection(driver, detectionId);
        detectionEditor.click_block_detection_button(driver);
        detectionEditor.select_checker(driver, checkerName);
        detectionEditor.set_reason_message(driver, message);
        detectionEditor.click_save_button(driver);
        detectionEditor.click_ok_button(driver);
        String alertMessage = commonAction.getAlertMessageStringWithoutWaitForAjaxLoader(driver);
        Allure.step("Alert message = " + alertMessage);
        return alertMessage;
    }

    @Step("Block alert")
    public String block_alert(RemoteWebDriver driver, String detectionId, String message) throws Exception {
        search_by_id(driver, detectionId);
        detectionManager.click_on_detection(driver, detectionId);
        detectionEditor.click_block_alert_button(driver);
        detectionEditor.set_reason_message(driver, message);
        detectionEditor.click_save_button(driver);
        return commonAction.getAlertMessageStringWithoutWaitForAjaxLoader(driver);
    }


    /************** Release Related Methods **************/
    @Step("Release Detection")
    public String release_detection_4_eyes(RemoteWebDriver driver, String detectionId, String message, String checkerName) throws Exception {
        search_by_id(driver, detectionId);
        detectionManager.click_on_detection(driver, detectionId);
        detectionEditor.click_release_detection_button(driver);
        detectionEditor.select_checker(driver, checkerName);
        detectionEditor.set_reason_message(driver, message);
        detectionEditor.click_save_button(driver);
        detectionEditor.click_ok_button(driver);
        return commonAction.getAlertMessageStringWithoutWaitForAjaxLoader(driver);
    }

    @Step("Release detection")
    public String release_detection(RemoteWebDriver driver, String detectionId, String message) throws InterruptedException {
        search_by_id(driver, detectionId);
        detectionManager.click_on_detection(driver, detectionId);
        detectionEditor.click_release_detection_button(driver);
        detectionEditor.set_reason_message(driver, message);
        detectionEditor.click_save_button(driver);
        detectionEditor.click_ok_button(driver);
        return commonAction.getAlertMessageStringWithoutWaitForAjaxLoader(driver);
    }

    @Step("Release alert")
    public String release_alert(RemoteWebDriver driver, String detectionId, String message) throws InterruptedException {
        detectionManager.click_on_detection(driver, detectionId);
        detectionEditor.click_release_alert_button(driver);
        detectionEditor.set_reason_message(driver, message);
        detectionEditor.click_save_button(driver);
        return commonAction.getAlertMessageStringWithoutWaitForAjaxLoader(driver);
    }


    /************** Repeat Related Methods **************/
    @Step("Repeat Detection")
    public String repeat_detection(RemoteWebDriver driver, String detectionId, String message) throws InterruptedException {
        detectionManager.click_on_detection(driver, detectionId);
        detectionEditor.click_stripping_detection_button(driver);
        detectionEditor.set_repeat_message(driver, message);
        detectionEditor.click_save_repeat_button(driver);
        return commonAction.getAlertMessageStringWithoutWaitForAjaxLoader(driver);

    }


    /************** Don't Know Related Methods **************/
    @Step("Perform Don't Know Action")
    public String perform_dont_Know(RemoteWebDriver driver, String detectionId, String message) throws InterruptedException {
        search_by_id(driver, detectionId);
        detectionManager.click_on_detection(driver, detectionId);
        detectionEditor.click_dont_know_detection_button(driver);
        detectionEditor.set_reason_message(driver, message);
        detectionEditor.click_save_button(driver);
        detectionEditor.click_ok_button(driver);
        return commonAction.getAlertMessageStringWithoutWaitForAjaxLoader(driver);
    }


    /************** Pending Related Methods **************/
    @Step("Perform pending action from detections section")
    public String pending_detection(RemoteWebDriver driver, String detectionId, String message) throws Exception {
        search_by_id(driver, detectionId);
        detectionManager.click_on_detection(driver, detectionId);
        detectionEditor.click_pending_detection_button(driver);
        detectionEditor.set_reason_message(driver, message);
        detectionEditor.click_save_button(driver);
        detectionEditor.click_ok_button(driver);
        return commonAction.getAlertMessageStringWithoutWaitForAjaxLoader(driver);

    }

    @Step("Perform pending alert action")
    public String pending_alert(RemoteWebDriver driver, String detectionId, String message) throws Exception {
        search_by_id(driver, detectionId);
        detectionManager.click_on_detection(driver, detectionId);
        detectionEditor.click_pending_alert_button(driver);
        detectionEditor.set_reason_message(driver, message);
        detectionEditor.click_save_button(driver);
        return commonAction.getAlertMessageStringWithoutWaitForAjaxLoader(driver);
    }


    /************** Assign Related Methods **************/
    @Step("Assign Detection")
    public String assignDetection(RemoteWebDriver driver, String detectionId, String assignee, String message) throws Exception {
        search_by_id(driver, detectionId);
        detectionManager.click_on_detection(driver, detectionId);
        detectionEditor.select_assign_to(driver, assignee);
        detectionEditor.click_assign_detection_button(driver);
        detectionEditor.set_reason_message(driver, message);
        detectionEditor.click_save_button(driver);
        return commonAction.getAlertMessageString(driver);
    }

    @Step("Verify Assignee Exists")
    public boolean verify_assignee_exists(RemoteWebDriver driver, String detectionId, String assignee) throws Exception {
        search_by_id(driver, detectionId);
        detectionManager.click_on_detection(driver, detectionId);
        return detectionEditor.select_assign_to(driver, assignee);
    }

    @Step("Get Assignee Value")
    public String get_assignee_value(RemoteWebDriver driver, String detectionId) throws Exception {
        search_by_id(driver, detectionId);
        detectionManager.click_on_detection(driver, detectionId);
        return detectionEditor.get_assignee_value(driver);

    }

    /************** Add Alert Related Methods **************/
    @Step("Create Alert")
    public String create_alert(RemoteWebDriver driver, String detectionId, String message) throws Exception {
        search_by_id(driver, detectionId);
        detectionManager.click_on_detection(driver, detectionId);
        detectionEditor.click_add_alert_button(driver);
        detectionEditor.set_reason_message(driver, message);
        detectionEditor.click_save_button(driver);
        wait.waitUntilAjaxLoaderDisappear(driver);
        return commonAction.getAlertMessageString(driver);
    }


    /************** Add Good Guy Related Methods **************/
    @Step("Add Detection As Good Guy")
    public String add_good_guy(RemoteWebDriver driver, String detectionId) throws InterruptedException {
        search_by_id(driver, detectionId);
        detectionManager.click_on_detection(driver, detectionId);
        detectionEditor.click_good_guy_button(driver);
        detectionEditor.click_scan_good_guy_button(driver);
        detectionEditor.click_accept_good_guy_button(driver);
        detectionEditor.close_form(driver);
        return commonAction.getAlertMessageString(driver);
    }


    /************** Upload Attachment Related Methods **************/
    @Step("Add attachment to detection")
    public boolean upload_attachment(RemoteWebDriver driver, String detectionId, String attachmentPath, String description) throws InterruptedException {
        search_by_id(driver, detectionId);
        detectionManager.click_on_detection(driver, detectionId);
        detectionEditor.click_attachment_tab(driver);
        detectionEditor.click_upload_button(driver);
        detectionEditor.set_browse_file_path(driver, attachmentPath);
        detectionEditor.set_attachment_description(driver, description);
        detectionEditor.click_save_button_for_attachment(driver);
        return detectionEditor.verify_attachment_exists(driver, description);
    }

    /************** Add Comment Related Methods **************/
    @Step("Add comment to alert.")
    public String add_comment_to_alert(RemoteWebDriver driver, String detectionId, String comment) throws InterruptedException {
        search_by_id(driver, detectionId);
        detectionManager.click_on_detection(driver, detectionId);
        detectionEditor.click_add_comment_button(driver);
        detectionEditor.set_reason_message(driver, comment);
        detectionEditor.click_save_button(driver);
        return commonAction.getAlertMessageString(driver);
    }

    @Step("Add comment to alert.")
    public String add_comment(RemoteWebDriver driver, String comment) throws InterruptedException {
        detectionEditor.click_add_comment_button(driver);
        detectionEditor.set_reason_message(driver, comment);
        detectionEditor.click_save_button(driver);
        return commonAction.getAlertMessageString(driver);
    }


    /************** Export Related Methods **************/
    @Step("Export detection alerts")
    public boolean export_detection_alerts(RemoteWebDriver driver, String detectionId, String printScope, String printType, String filePath, String hub) throws Exception {
        search_by_id(driver, detectionId);
        detectionManager.click_on_detection(driver, detectionId);
        detectionEditor.click_export_detection_alerts_button(driver);
        detectionEditor.select_print_scope(driver, printScope);
        detectionEditor.select_export_type(driver, printType);
        detectionEditor.click_print_button(driver);
        boolean flag = textFilesHandler.exist(driver, filePath, hub);
        detectionEditor.closeExportViolation(driver);
        return flag;
    }

    @Step("Export detection")
    public boolean export_detection(RemoteWebDriver driver, String detectionId, String printScope, String printType, String filePath, String hub) throws Exception {
        search_by_id(driver, detectionId);
        detectionManager.click_on_detection(driver, detectionId);
        detectionEditor.click_export_detection_button(driver);
        detectionEditor.select_print_scope_for_detection(driver, printScope);
        detectionEditor.select_print_type_for_detection(driver, printType);
        detectionEditor.click_print_button_for_detection(driver);
        boolean flag = textFilesHandler.exist(driver, filePath, hub);
        detectionEditor.closeExportViolation(driver);
        return flag;
    }


    @Step("Get Matched Entity")
    public String get_matched_entity(RemoteWebDriver driver) {
        detectionManager.click_on_first_listed_detection(driver);
        return detectionEditor.get_matched_entity(driver);
    }

    @Step("Get Related Message Detection ID")
    public String get_related_message_detection_id(RemoteWebDriver driver) {
        detectionManager.click_on_first_listed_detection(driver);
        detectionEditor.click_on_related_record(driver);
        return detectionEditor.get_related_message_detection_ID(driver);
    }

    @Step("Get Related Message Detection Status")
    public String get_related_message_detection_status(RemoteWebDriver driver) {
        detectionEditor.click_on_related_record(driver);
        return detectionEditor.get_related_message_detection_status(driver);
    }

    @Step("Get Structured Record Data")
    public ArrayList<ArrayList<String>> get_structured_record_data(RemoteWebDriver driver, String detectionId) throws Exception {
        search_by_id(driver, detectionId);
        detectionManager.click_on_detection(driver, detectionId);
        return detectionEditor.get_structured_records_data(driver);
    }

    @Step("Check if Checker name exists")
    public boolean verify_if_checker_exists(RemoteWebDriver driver, String detectionID, String checkerName) throws Exception {
        search_by_id(driver, detectionID);
        detectionManager.click_on_detection(driver, detectionID);
        detectionEditor.click_release_detection_button(driver);
        return detectionEditor.select_checker(driver, checkerName);
    }

    @Step("Check Permission")
    public String check_permission(RemoteWebDriver driver, String functionToCheck, String detectionId, String message) throws Exception {
        String result = GeneralConstants.FAILED;
        detectionManager.click_on_detection(driver, detectionId);
        switch (functionToCheck) {
            case "Block":
                Allure.step("Check if user can block detection. ");
                if (detectionEditor.verify_block_button_exists(driver))
                    result = block_detection(driver, detectionId, message);
                break;
            case "Release":
                Allure.step("Check if user can Release detection. ");
                if (detectionEditor.verify_release_button_exists(driver))
                    result = release_detection_4_eyes(driver, detectionId, message, "");
                break;
            case "Repeat":
                Allure.step("Check if user can Repeat detection. ");
                if (detectionEditor.verify_repeat_button_exists(driver))
                    result = repeat_detection(driver, detectionId, message);
                break;

        }
        return result;
    }


    @Step("Get First Listed Detection Status")
    public String get_first_listed_detection_status(RemoteWebDriver driver) {
        String status = detectionManager.get_detection_status(driver);
        return status;
    }

    @Step("Click On First Listed Detection")
    public void click_on_first_listed_detection(RemoteWebDriver driver) {
        detectionManager.click_on_first_listed_detection(driver);
    }

    @Step("Verify if comment exist")
    public boolean verify_comment_exist(RemoteWebDriver driver, String detectionId, String comment) throws InterruptedException {
        search_by_id(driver, detectionId);
        detectionManager.click_on_detection(driver, detectionId);
        return detectionEditor.verify_comment_exist(driver, comment);
    }

    @Step("Get Scanned Record Data")
    public String get_scanned_record_data(RemoteWebDriver driver, String detectionId, String dataStr) throws Exception {
        search_by_id(driver, detectionId);
        detectionManager.click_on_detection(driver, detectionId);
        detectionEditor.select_detection_record_by_data(driver, dataStr);
        detectionEditor.click_scanned_record_tab(driver);
        return detectionEditor.get_highlighted_data_scanned_record(driver);
    }

    @Step("Bulk Assignment for Detections")
    public void bulk_assign_detections(RemoteWebDriver driver) {
        detectionEditor.click_on_bulk_assign(driver);
        detectionEditor.select_4_eyes_detection_type(driver);
        detectionEditor.click_from_checker_radio_button(driver);
        detectionEditor.click_search_button(driver);
    }

    @Step("Get field value by column number")
    public String get_field_value_by_column_number(RemoteWebDriver driver, String detectionId, int columnNumber) throws InterruptedException {
        return detectionManager.get_field_value_by_column_number(driver, detectionId, columnNumber);
    }

    @Step("Verify Repeat Detection Button Exists")
    public boolean verify_repeat_button_exists(RemoteWebDriver driver) {
        return detectionEditor.verify_repeat_button_exists(driver);
    }

    @Step("Check violation exit.")
    public boolean verify_violation_exist(RemoteWebDriver driver, String detectionId, String dataStr) throws Exception {
        search_by_id(driver, detectionId);
        detectionManager.click_on_detection(driver, detectionId);
        return detectionEditor.verify_violation_exist(driver, dataStr);

    }
    public String get_violation_text(RemoteWebDriver driver, String detectionId) throws InterruptedException {
        search_by_id(driver , detectionId);
        detectionManager.click_on_detection(driver, detectionId);
        return driver.findElement(By.xpath("//*[@id='auditManagerForm:homepage_business:Detections_list:detectionResultsTable_data']/tr/td[6]/span")).getText();
    }


    @Step("Pessimistic User Releases Detection")
    public void pessimistic_user_releases_detection(RemoteWebDriver driver, String detectionId) throws InterruptedException {
        search_by_id(driver, detectionId);
        detectionManager.click_on_detection(driver, detectionId);
        detectionEditor.click_release_detection_button(driver);
    }

    @Step("Check if don't know option exist in 3 dots menu")
    public boolean check_if_dontKnow_option_exist_3dots_Menu(RemoteWebDriver driver, String detection_id) throws InterruptedException {
        search_by_id(driver, detection_id);
        detectionManager.click_on_3_dots_menu_button(driver);
        boolean is_exist = detectionManager.is_dontKnow_exist_in_3dots_menu(driver);
        Allure.step(String.format("Don't Know option appear in 3 dots menu = ", is_exist));
        return is_exist;
    }

    @Step("Check if block option appear without click on detection")
    public boolean check_if_block_option_appear_without_click_on_detection(RemoteWebDriver driver, String detection_id) throws InterruptedException {
        search_by_id(driver, detection_id);
        boolean is_exist = detectionEditor.verify_block_button_exists(driver);
        Allure.step(String.format("Is block option appear = %s", is_exist));
        return is_exist;
    }

    @Step("Check if block option enabled")
    public boolean check_if_block_option_enabled(RemoteWebDriver driver, String detection_id) throws InterruptedException {
        search_by_id(driver, detection_id);
        detectionManager.click_on_detection(driver, detection_id);
        boolean is_exist = detectionEditor.verify_block_button_enabled(driver);
        Allure.step(String.format("Is block option appear = %s", is_exist));
        return is_exist;
    }

    @Step("Perform bulk assignment of detections")
    public String bulk_assignment_of_detections(RemoteWebDriver driver, String new_investigator, String new_assignment) throws Exception {
        click_on_first_listed_detection(driver);
        detectionEditor.click_on_bulk_assign(driver);
        detectionEditor.click_on_bulk_assign(driver);
        detectionEditor.select_4_eyes_detection_type(driver);
        detectionEditor.click_from_checker_radio_button(driver);
        detectionEditor.click_search_button(driver);
        detectionEditor.select_new_investigator(driver, new_investigator);
        detectionEditor.set_new_assignment(driver, new_assignment);
        detectionEditor.click_bulk_assign_button(driver);
        detectionEditor.click_ok_button(driver);
        String num_od_open_detection = detectionEditor.get_open_detection_count(driver);
        detectionEditor.click_cancel_bulk_assign_button(driver);
        return num_od_open_detection;
    }

    @Step("Search by user name in bulk assign")
    public boolean search_user_in_bulk_assign(RemoteWebDriver driver, String assign) throws Exception {
        detectionEditor.click_on_bulk_assign(driver);
        return detectionEditor.select_regular_detection_type(driver, assign);

    }

    @Step("Verify detection id existence")
    public boolean verify_detection_id_exist(RemoteWebDriver driver, String detection_id) {
        return detectionEditor.verify_detection_id_exist(driver, detection_id);
    }

    @Step("Check if table rows appear")
    public boolean verify_table_rows_appear(RemoteWebDriver driver) {
        Navigation.DETECTION_MANAGER.navigate(driver);
        return detectionManager.verify_table_rows_appear(driver);
    }

    @Step("Get Field type")
    public String get_Field_Type(RemoteWebDriver driver, String fieldName, String detectionID) throws Exception {
        String fieldType = "";
        ArrayList<ArrayList<String>> fields = get_structured_record_data(driver, detectionID);
        for (int i = 0; i < fields.size(); i++) {
            System.out.println("Field type is " + fields.get(i).get(2));
            if (fields.get(i).get(1).equals(fieldName)) {
                fieldType = fields.get(i).get(2);
                break;
            }
        }
        return fieldType;
    }

}
