package eastnets.screening.control.listManager;

import core.gui.Controls;
import core.util.TextFilesHandler;
import core.util.Wait;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.Navigation;
import eastnets.screening.entity.AKA;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.ListEntry;
import eastnets.screening.gui.listManager.ListManagerNavigation;
import eastnets.screening.gui.listManager.listExplorer.ListExplorerEditor;
import eastnets.screening.gui.listManager.listExplorer.ListExplorerManager;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class ListExplorerControl {

    private final ListManagerNavigation listManagerNavigation;
    private final ListExplorerManager listExplorerManager;
    private final ListExplorerEditor listExplorerEditor;
    private final CommonAction commonAction;
    private final Controls controls;
    private final Wait wait;

    public ListExplorerControl() {
        this.listManagerNavigation = ListManagerNavigation.LIST_EXPLORER;
        this.listExplorerManager = new ListExplorerManager();
        this.listExplorerEditor = new ListExplorerEditor();
        this.commonAction = new CommonAction();
        this.controls = new Controls();
        this.wait = new Wait();
    }

    @Step("Navigate To List Explorer Tab")
    public void navigate(RemoteWebDriver driver) {

        Navigation.LIST_MANAGER.navigate(driver);
        listManagerNavigation.navigate(driver);
    }

    @Step("Start search")
    public void search(RemoteWebDriver driver, EnList list) throws Exception {
        navigate(driver);
        listExplorerManager.click_reset(driver);
        listExplorerManager.select_zone(driver, list.getZoneName());

        if (list.getListSet() != null) {
            listExplorerManager.select_list_set(driver, list.getListSet().getName());
        }

        listExplorerManager.select_black_list(driver, list.getName());

        if (list.getEntry() != null) {
            ListEntry entry = list.getEntry().get(0);
            listExplorerManager.select_entry_category(driver, entry.getType());

            String name = (entry.getFirstName() == null)
                    ? entry.getName()
                    : entry.getName() + ", " + entry.getFirstName();

            listExplorerManager.set_entry_name(driver, name);

            if (entry.getAddress() != null) {
                listExplorerManager.set_address(driver, entry.getAddress().get(0).getAddress());
            }

            listExplorerManager.set_program(driver, entry.getPrograms());

            if (entry.getAkas() != null) {
                listExplorerManager.set_aka(driver, entry.getAkas().get(0).getFirstName());
            }
        }
        listExplorerManager.click_search_button(driver);
    }

    @Step("Search by zone and black-list")
    public void search_by_zone_and_blackList(RemoteWebDriver driver, EnList list) throws Exception {
        navigate(driver);
        listExplorerManager.click_reset(driver);
        listExplorerManager.select_zone(driver, list.getZoneName());
        listExplorerManager.select_black_list(driver, list.getName());
        listExplorerManager.click_search_button(driver);
    }

    @Step("Search by name")
    public void search_by_entry_name(RemoteWebDriver driver, String name) throws Exception {
        listExplorerManager.click_reset(driver);
        listExplorerManager.set_entry_name(driver, name);
        listExplorerManager.click_search_button(driver);
    }

    @Step("Search list explorer")
    public void search(RemoteWebDriver driver, String zone, String listName, String name) throws Exception {
        listExplorerManager.click_reset(driver);
        listExplorerManager.select_zone(driver, zone);
        listExplorerManager.select_black_list(driver, listName);
        listExplorerManager.set_entry_name(driver, name);
        listExplorerManager.click_search_button(driver);
    }

    @Step("Export entry")
    public void export_entry(RemoteWebDriver driver, String exportType) throws Exception {
        listExplorerManager.set_export_type(driver, exportType);
        listExplorerManager.click_export_button(driver);
    }


    @Step("Verify black list linked to list set")
    public boolean verify_black_list_linked_to_list_set(RemoteWebDriver driver, String zone, String blackList) throws Exception {
        navigate(driver);
        listExplorerManager.click_reset(driver);
        listExplorerManager.select_zone(driver, zone);
        listExplorerManager.select_list_set_by_index(driver, "1");
        return listExplorerManager.select_black_list(driver, blackList);
    }


    @Step("Fill entry data in Type&Names Tab")
    public void set_type_names_data(RemoteWebDriver driver, ListEntry entry) throws Exception {
        listExplorerEditor.select_type(driver, entry.getType());
        listExplorerEditor.set_name(driver, entry.getName());
        listExplorerEditor.set_first_name(driver, entry.getFirstName());
        listExplorerEditor.set_title(driver, entry.getTitle());
    }


    @Step("Add entry as good guy")
    public String set_good_guy_data(RemoteWebDriver driver, String name, String rank) throws Exception {
        listExplorerEditor.click_good_guy_tab(driver);
        listExplorerEditor.click_add_good_guy_btn(driver);
        listExplorerEditor.set_accepted_text(driver, name);
        listExplorerEditor.select_rank(driver, rank);
        listExplorerEditor.click_scan_btn(driver);
        listExplorerEditor.select_first_matched_entity(driver);
        listExplorerEditor.click_accept_btn(driver);

        String alertMessage = commonAction.getAlertMessageString(driver);
        Allure.step("Alert message = " + alertMessage);
        return alertMessage;
    }


    @Step("Add Aka Data")
    public boolean add_aka(RemoteWebDriver driver, AKA aka) throws Exception {
        listExplorerEditor.click_add_aka_btn(driver);
        listExplorerEditor.select_aka_type(driver, aka.getType());
        listExplorerEditor.set_aka_first_name(driver, aka.getFirstName());
        listExplorerEditor.set_aka_last_name(driver, aka.getLastName());
        listExplorerEditor.click_save_aka_button(driver);
        return true;

    }

    @Step("Fill entry data in Details Tab")
    public void set_details_data(RemoteWebDriver driver, ListEntry entry) {
        listExplorerEditor.click_details_tab(driver);
        listExplorerEditor.set_birthday(driver, entry.getBirthDate());
        listExplorerEditor.set_program(driver, entry.getPrograms());
    }


    @Step("Fill entry data in Address Tab")
    public boolean set_address_data(RemoteWebDriver driver, ListEntry entry) {
        listExplorerEditor.click_address_tab(driver);
        listExplorerEditor.click_add_address_button(driver);
        listExplorerEditor.set_address(driver, entry.getAddress().get(0).getAddress());
        listExplorerEditor.set_city(driver, entry.getAddress().get(0).getCity());
        listExplorerEditor.set_country(driver, entry.getAddress().get(0).getCountry());
        listExplorerEditor.click_ok(driver);
        return true;

    }

    @Step("Create new entry with filling Type and names")
    public boolean create_entry_with_type_and_names(RemoteWebDriver driver, ListEntry entry) throws Exception {
        listExplorerManager.click_add_button(driver);
        set_type_names_data(driver, entry);

        if (entry.getAkas() != null && entry.getAkas().size() != 0)
            for (int i = 0; i < entry.getAkas().size(); i++)
                add_aka(driver, entry.getAkas().get(i));

        listExplorerEditor.click_save_button(driver);

        return listExplorerManager.verify_entry_exist(driver, entry);
    }


    @Step("Create new entry with filling Type,names and Address")
    public boolean create_entry_with_type_and_names_and_address(RemoteWebDriver driver, EnList list, ListEntry entry) throws Exception {
        search(driver, list);
        listExplorerManager.click_add_button(driver);
        set_type_names_data(driver, entry);

        if (entry.getAddress() != null && entry.getAddress().size() != 0)
            set_address_data(driver, entry);
        listExplorerEditor.click_save_button(driver);

        return listExplorerManager.verify_entry_exist(driver, entry);
    }

    @Step("Create new entry with filling Type&Names and Details ")
    public boolean create_entry_with_details_info(RemoteWebDriver driver, ListEntry entry) throws Exception {
        listExplorerManager.click_add_button(driver);
        set_type_names_data(driver, entry);
        set_details_data(driver, entry);
        listExplorerEditor.click_save_button(driver);
        return listExplorerManager.verify_entry_exist(driver, entry);
    }

    @Step("Delete Entry")
    public String delete_entry(RemoteWebDriver driver, String zone, String blackListName, String entryName) throws Exception {
        navigate(driver);
        search(driver, zone, blackListName, entryName);
        listExplorerManager.click_check_box(driver);
        listExplorerManager.click_delete_button(driver);
        return commonAction.getAlertMessageString(driver);
    }

    @Step("Add good guy")
    public String add_good_guy(RemoteWebDriver driver, EnList list, String name, String rank) throws Exception {
        navigate(driver);
        search(driver, list);
        listExplorerManager.select_entry_by_name(driver, list.getEntry().get(0));
        String alertMessage = set_good_guy_data(driver, name, rank);
        listExplorerEditor.click_cancel_button(driver);
        return alertMessage;
    }

    @Step("Verify if entry exist.")
    public boolean is_entry_exist(RemoteWebDriver driver, String zone, ListEntry entry) throws Exception {
        listExplorerManager.select_zone(driver, zone);
        listExplorerManager.set_entry_name(driver, entry.getName());
        listExplorerManager.click_search_button(driver);
        wait.time(Wait.ONE_SECOND * 5);
        return controls.exists(driver, By.xpath(String.format("//a[.='%s']", entry.getName() + ", " + entry.getFirstName())));
    }

    @Step("Verify if entry exist.")
    public boolean is_entry_exist(RemoteWebDriver driver, String zone, String name) throws Exception {
        navigate(driver);
        listExplorerManager.select_zone(driver, zone);
        listExplorerManager.set_entry_name(driver, name);
        listExplorerManager.click_search_button(driver);
        return controls.exists(driver, By.xpath(String.format("//a[.='%s']", name)));
    }

    @Step("Verify if entry exist.")
    public boolean is_entry_exist(RemoteWebDriver driver, String zone, String black_list, String name) throws Exception {
        navigate(driver);
        listExplorerManager.select_zone(driver, zone);
        listExplorerManager.select_black_list(driver, black_list);
        listExplorerManager.set_entry_name(driver, name);
        listExplorerManager.click_search_button(driver);
        return controls.exists(driver, By.xpath(String.format("//a[.='%s']", name)));
    }

    @Step("Edit entry")
    public boolean edit_entry(RemoteWebDriver driver, String zone, ListEntry entry) throws Exception {
        listExplorerManager.select_zone(driver, zone);
        listExplorerManager.set_entry_name(driver, entry.getName());
        listExplorerManager.click_search_button(driver);
        listExplorerManager.select_entry_by_name(driver, entry);
        entry.setName(entry.getName() + "update");
        set_type_names_data(driver, entry);
        listExplorerEditor.click_save_button(driver);

        return is_entry_exist(driver, zone, entry);
    }

    @Step("Edit external ID")
    public String edit_external_id(RemoteWebDriver driver, String zone, ListEntry entry, String externalID) throws Exception {
        listExplorerManager.select_zone(driver, zone);
        listExplorerManager.set_entry_name(driver, entry.getName());
        listExplorerManager.click_search_button(driver);
        listExplorerManager.select_entry_by_name(driver, entry);
        listExplorerEditor.click_details_tab(driver);
        listExplorerEditor.set_external_id(driver, externalID);
        listExplorerEditor.click_save_button(driver);

        return commonAction.getAlertMessageString(driver);
    }

    @Step("Get rows count")
    public String get_Rows_Count(RemoteWebDriver driver, EnList list) throws Exception {
        search_by_zone_and_blackList(driver, list);
        return listExplorerManager.get_rows_number(driver);
    }

    @Step("Verify if entry exist.")
    public boolean is_entry_details_contains_aKA(RemoteWebDriver driver, String zone, String blackList, String entryName) throws Exception {
        listExplorerManager.select_zone(driver, zone);
        listExplorerManager.select_black_list(driver, blackList);
        listExplorerManager.set_entry_name(driver, entryName);
        listExplorerManager.click_search_button(driver);
        listExplorerManager.select_first_entry(driver);
        wait.waitUntilAjaxLoaderDisappear(driver);
        return controls.exists(driver, By.xpath(String.format("//td[.='%s']", entryName)));
    }

    @Step("Export entries")
    public boolean export_entries(RemoteWebDriver driver, String zone, String filePrefix, String type, String hub) throws Exception {
        navigate(driver);
        listExplorerManager.click_reset(driver);
        listExplorerManager.select_zone(driver, zone);
        listExplorerManager.click_search_button(driver);
        export_entry(driver, type);
        return new TextFilesHandler().checkExistOfFileWithDynamicName(driver, filePrefix, hub);
    }

    @Step("Import entries")
    public String import_entries(RemoteWebDriver driver, String zone, String blackList, String filePath, String formatName, String encoding, boolean isLock, boolean isUpgrade, boolean isMigrate, boolean isAppend, boolean isAKA) throws Exception {
        navigate(driver);
        listExplorerManager.click_import_button(driver);
        listExplorerEditor.select_zone(driver, zone);
        listExplorerEditor.select_black_list(driver, blackList);
        listExplorerEditor.select_format(driver, formatName);
        listExplorerEditor.select_version(driver);
        listExplorerEditor.select_encoding(driver, encoding);
        listExplorerEditor.set_file_to_import(driver, filePath);
        listExplorerEditor.click_lock_check_box(driver, isLock);
        listExplorerEditor.click_upgrade_check_box(driver, isUpgrade);
        listExplorerEditor.click_migrate_check_box(driver, isMigrate);
        listExplorerEditor.click_append_check_box(driver, isAppend);
        listExplorerEditor.click_extra_aka_check_box(driver, isAKA);
        listExplorerEditor.click_import_file(driver);
        return commonAction.getAlertMessageString(driver);
    }
}
