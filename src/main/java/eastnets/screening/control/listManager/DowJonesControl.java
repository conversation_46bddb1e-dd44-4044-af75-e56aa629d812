package eastnets.screening.control.listManager;

import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.SftpException;
import core.constants.screening.GeneralConstants;
import core.util.*;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.Navigation;
import eastnets.screening.entity.EnList;
import eastnets.screening.gui.listManager.ListManagerNavigation;
import eastnets.screening.gui.listManager.dowJones.DowJonesEditor;
import eastnets.screening.gui.listManager.dowJones.DowJonesManager;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.io.IOException;

public class DowJonesControl {

    private final static Property property = new Property();
    public static String loginSampleFilePath = System.getProperty("user.dir") + property.fromFile(GeneralConstants.SCREENING_TEST_DATA_CONFIG_FILE_PATH).getProperty("loginSampleFile");
    public static String loginNewFilePath = System.getProperty("user.dir") + property.fromFile(GeneralConstants.SCREENING_TEST_DATA_CONFIG_FILE_PATH).getProperty("loginNewFile");
    public static String djSFilePath = System.getProperty("user.dir") + property.fromFile(GeneralConstants.SCREENING_TEST_DATA_CONFIG_FILE_PATH).getProperty("DJSampleFile");
    public static String djNewFilePath = System.getProperty("user.dir") + property.fromFile(GeneralConstants.SCREENING_TEST_DATA_CONFIG_FILE_PATH).getProperty("DJNewFile");

    private final ListManagerNavigation listManagerNavigation;
    private final DowJonesManager dowJonesManager;
    private final DowJonesEditor dowJonesEditor;
    private final CommonAction commonAction;
    private final Wait wait;
    private final TextFilesHandler textFilesHandler;
    private final ServerUtil serverUtil;
    private final Log log;

    public DowJonesControl() {
        this.listManagerNavigation = ListManagerNavigation.DOW_GONES;
        this.dowJonesManager = new DowJonesManager();
        this.dowJonesEditor = new DowJonesEditor();
        this.commonAction = new CommonAction();
        this.wait = new Wait();
        this.textFilesHandler = new TextFilesHandler();
        this.serverUtil = new ServerUtil();
        this.log = new Log();
    }

    @Step("Navigate To Dow Jones Manager Tab")
    public void navigate_to_dj(RemoteWebDriver driver) {
        Navigation.LIST_MANAGER.navigate(driver);
        listManagerNavigation.navigate(driver);
    }

    @Step("Create DJ List")
    public boolean create_dj_list(RemoteWebDriver driver, EnList list, String mode, boolean new_ver_on_update_flag) throws Exception {
        navigate_to_dj(driver);
        dowJonesManager.click_search_button(driver);
        dowJonesManager.click_add_dj_button(driver);
        dowJonesEditor.set_name(driver, list);
        dowJonesEditor.select_container_name(driver, list);
        dowJonesEditor.select_mode(driver, mode);
        dowJonesEditor.select_new_version(driver, new_ver_on_update_flag);
        dowJonesEditor.click_save_button(driver);
        navigate_to_dj(driver);
        return dowJonesManager.verify_dj_exist(driver, list.getName());
    }

    @Step("Create DJ List with Advanced Setting")
    public boolean create_dj_list_with_advanced_setting(RemoteWebDriver driver, EnList list, String mode, String advancedSetting) throws Exception {
        navigate_to_dj(driver);
        dowJonesManager.click_search_button(driver);
        dowJonesManager.click_add_dj_button(driver);
        dowJonesEditor.set_name(driver, list);
        dowJonesEditor.select_container_name(driver, list);
        dowJonesEditor.select_mode(driver, mode);
        dowJonesEditor.select_advanced_setting(driver, advancedSetting);
        dowJonesEditor.click_save_button(driver);
        navigate_to_dj(driver);
        return dowJonesManager.verify_dj_exist(driver, list.getName());
    }

    @Step("Enable DJ List")
    public String enable_dj_list(RemoteWebDriver driver) throws InterruptedException {
        navigate_to_dj(driver);
        dowJonesManager.click_search_button(driver);
        dowJonesManager.sort_descending_by_creation_date(driver);
        dowJonesManager.click_checkbox_button(driver);
        wait.time(Wait.ONE_SECOND * 2);
        dowJonesManager.click_enable_utton(driver);
        return commonAction.getAlertMessageStringWithoutWaitForAjaxLoader(driver);
    }

    @Step("Disable DJ List")
    public String disable_dj_list(RemoteWebDriver driver) throws InterruptedException {
        navigate_to_dj(driver);
        dowJonesManager.click_search_button(driver);
        dowJonesManager.sort_descending_by_creation_date(driver);
        wait.time(Wait.ONE_SECOND * 2);
        dowJonesManager.click_checkbox_button(driver);
        wait.time(Wait.ONE_SECOND * 2);
        dowJonesManager.click_disable_button(driver);
        return commonAction.getAlertMessageString(driver);
    }

    @Step("Delete DJ List")
    public boolean delete_dj_list(RemoteWebDriver driver, String listName) throws InterruptedException {
        navigate_to_dj(driver);
        dowJonesManager.click_search_button(driver);
        dowJonesManager.sort_descending_by_creation_date(driver);
        wait.time(Wait.ONE_SECOND * 2);
        dowJonesManager.click_checkbox_button(driver);
        wait.time(Wait.ONE_SECOND * 2);
        dowJonesManager.click_delete_button(driver);
        dowJonesManager.click_search_button(driver);
        return dowJonesManager.verify_dj_exist(driver, listName);
    }

    @Step("Select Countries For DJ List")
    public void select_countries(RemoteWebDriver driver, EnList list) throws InterruptedException {
        dowJonesManager.select_first_list_from_result(driver, list);
        dowJonesEditor.click_edit_button(driver);
        dowJonesEditor.click_add_all_button(driver);
        dowJonesEditor.click_done_button(driver);
        dowJonesEditor.click_save_button(driver);
        wait.time(Wait.ONE_SECOND * 2);
    }

    @Step("Add Sanction List")
    public void add_sanction_list(RemoteWebDriver driver, EnList list) {
        dowJonesManager.select_first_list_from_result(driver, list);
        dowJonesEditor.add_sanction_list(driver);
    }


    @Step("Create Login File")
    public void create_login_file(String userName, String password, String zoneId) throws IOException {
        String fileContent = textFilesHandler.getTextFileContentAsString(loginSampleFilePath);

        fileContent = fileContent.replaceFirst("#", GeneralConstants.LOGIN_FILE_NAME);
        fileContent = fileContent.replaceFirst("#", userName);
        fileContent = fileContent.replaceFirst("#", password);
        fileContent = fileContent.replaceFirst("#", zoneId);
        Allure.step("Dow jones Login File content = " + fileContent);
        Allure.step("Start coping data from sample file to another file to be used on server.");
        textFilesHandler.writeToFile(loginNewFilePath, fileContent);
    }

    @Step("Create DJ File")
    public void create_dj_file(EnList list, boolean loadReferentialOnly, boolean loadGroupReferentialOnly, String listName) throws IOException {
        String fileContent = textFilesHandler.getTextFileContentAsString(djSFilePath);
        fileContent = fileContent.replaceFirst("#JAR_FILE_NAME#", GeneralConstants.DJ_JARFILE_NAME);
        fileContent = fileContent.replaceFirst("#LOGIN_FILE#", GeneralConstants.LOGIN_FILE_NAME);
        String filePath = System.getProperty("user.dir").replace("\\","/")+"/src/test/resources/DataFiles/DowJonesDataFiles/"+listName;
        fileContent = fileContent.replaceFirst("#LIST_TO_BE_LOADED#", filePath);
        if (!loadReferentialOnly) {
            Allure.step("Set loadReferentialOnly = false");
            fileContent = fileContent.replace("loadReferentialOnly=true", "loadReferentialOnly=false");
        }
        if (loadGroupReferentialOnly) {
            Allure.step("Set loadGroupReferentialOnly=true");
            fileContent = fileContent.replace("loadGroupReferentialOnly=false", "loadGroupReferentialOnly=true");
        }
        fileContent = fileContent.replaceFirst("#LIST_NAME#", list.getName());
        log.info("Dow jones File content = " + fileContent);
        textFilesHandler.writeToFile(djNewFilePath, fileContent);
    }

    @Step("Create DJ Files And Run CMD")
    public String create_dj_files(EnList list, boolean loadReferentialOnly, boolean loadGroupReferentialOnly
            , String userName, String password, String zoneId, String listName, boolean fullDjList) throws IOException, SftpException, JSchException, InterruptedException {
        Allure.step("Create Dow-Jones login file.");
        create_login_file(userName, password, zoneId);

        log.info("Transfer Dow-Jones login file to server under location = " + GeneralConstants.DJ_LOGIN_FILE_CREATOR_PATH);
        serverUtil.transferFileToServer(loginNewFilePath, "/" + GeneralConstants.DJ_LOGIN_FILE_CREATOR_PATH, GeneralConstants.SWF_APP_NAME);

        serverUtil.runCommand("cd /d " + GeneralConstants.DJ_LOGIN_FILE_CREATOR_PATH + "&& LoginFileAutomation.cmd", GeneralConstants.SWF_APP_NAME, false);

        Allure.step("Transfer Dow-Jones " + GeneralConstants.LOGIN_FILE_NAME + " file from ---> " + GeneralConstants.DJ_LOGIN_FILE_CREATOR_PATH + "/" + GeneralConstants.LOGIN_FILE_NAME +
                " To --->" + GeneralConstants.UTILS_FOLDER_PATH + "/" + GeneralConstants.LOGIN_FILE_NAME);
        serverUtil.moveFileInServer("/" + GeneralConstants.DJ_LOGIN_FILE_CREATOR_PATH + "/" + GeneralConstants.LOGIN_FILE_NAME
                , GeneralConstants.UTILS_FOLDER_PATH + "/" + GeneralConstants.LOGIN_FILE_NAME, GeneralConstants.SWF_APP_NAME);

        Allure.step("Create Dow-Jones file.");
        create_dj_file(list, loadReferentialOnly, loadGroupReferentialOnly, listName);

        Allure.step("Transfer Dow-Jones file to server under location = " + GeneralConstants.UTILS_FOLDER_PATH);
        serverUtil.transferFileToServer(djNewFilePath, "/" + GeneralConstants.UTILS_FOLDER_PATH, GeneralConstants.SWF_APP_NAME);

        return serverUtil.runCommand("cd /d  " + GeneralConstants.UTILS_FOLDER_PATH/*.substring(1)*/ + " && " + "DJAutomation.bat", GeneralConstants.SWF_APP_NAME, fullDjList);
    }

    @Step("Create DJ List With Load Referential False")
    public String load_dj_list_load_referential_only(EnList list, boolean loadReferentialOnly, boolean loadGroupReferentialOnly, String listName, boolean fullDjList) throws IOException, JSchException, InterruptedException {

        Allure.step("Edit Dow-Jones file.");
        create_dj_file(list, loadReferentialOnly, loadGroupReferentialOnly, listName);

        Allure.step("Transfer Dow-Jones file to server under location = " + GeneralConstants.UTILS_FOLDER_PATH);
        serverUtil.transferFileToServer(djNewFilePath, "/" + GeneralConstants.UTILS_FOLDER_PATH, GeneralConstants.SWF_APP_NAME);

        return serverUtil.runCommand("cd /d  " + GeneralConstants.UTILS_FOLDER_PATH + " && " + "DJAutomation.bat", GeneralConstants.SWF_APP_NAME, fullDjList);
    }

}
