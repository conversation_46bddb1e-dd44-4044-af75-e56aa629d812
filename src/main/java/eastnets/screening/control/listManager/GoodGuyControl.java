package eastnets.screening.control.listManager;

import core.util.Log;
import core.util.TextFilesHandler;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.Navigation;
import eastnets.screening.control.DetectionManagerControl;
import eastnets.screening.entity.EnList;
import eastnets.screening.gui.detectionManager.DetectionEditor;
import eastnets.screening.gui.listManager.ListManagerNavigation;
import eastnets.screening.gui.listManager.goodGuy.GoodGuyEditor;
import eastnets.screening.gui.listManager.goodGuy.GoodGuyManager;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;


public class GoodGuyControl {

    private final ListManagerNavigation listManagerNavigation;
    private final GoodGuyManager goodGuyManager;
    private final GoodGuyEditor goodGuyEditor;
    private final CommonAction commonAction;
    private final DetectionManagerControl detectionManagerControl;
    private final DetectionEditor detectionEditor;
    private final TextFilesHandler textFilesHandler;
    private final Log log;

    public GoodGuyControl() {
        this.listManagerNavigation = ListManagerNavigation.GOOD_GUYS_EXPLORER;
        this.goodGuyManager = new GoodGuyManager();
        this.goodGuyEditor = new GoodGuyEditor();
        this.commonAction = new CommonAction();
        this.detectionManagerControl = new DetectionManagerControl();
        this.detectionEditor = new DetectionEditor();
        this.textFilesHandler = new TextFilesHandler();
        this.log = new Log();
    }

    @Step("Navigate to GoodGuy Explorer")
    public void navigate_to_gg(RemoteWebDriver driver) {
        Navigation.LIST_MANAGER.navigate(driver);
        listManagerNavigation.navigate(driver);
    }

    @Step("Search in Good Guy Explorer")
    public void search(RemoteWebDriver driver, EnList enList) throws Exception {
        navigate_to_gg(driver);
        goodGuyManager.select_zone(driver, enList.getZoneName());
        goodGuyManager.select_black_list(driver, enList.getName());
        goodGuyManager.select_list_set(driver, enList.getListSet().getName());
        goodGuyManager.click_search_btn(driver);
    }

    @Step("Search Good-Guy by accepted text")
    public void search_by_accepted_text(RemoteWebDriver driver, String accepted_text) {
        goodGuyManager.set_accepted_text(driver, accepted_text);
        goodGuyManager.click_search_btn(driver);
    }

    @Step("Set good guy form details")
    public void set_accepted_text_and_rank(RemoteWebDriver driver, String acceptedText, String rank) throws Exception {
        goodGuyEditor.set_accepted_text(driver, acceptedText);
        goodGuyEditor.set_rank(driver, rank);
    }

    @Step("Search Good Guy Explorer")
    public boolean search_by_name(RemoteWebDriver driver, String name) {
        navigate_to_gg(driver);
        search_by_accepted_text(driver, name);
        return goodGuyManager.verify_gg_exist(driver, name);
    }

    @Step("Search Good Guy Explorer")
    public boolean verify_gg_exist(RemoteWebDriver driver, String name) {
        navigate_to_gg(driver);
        search_by_accepted_text(driver, name);
        return goodGuyManager.verify_gg_exist(driver, name);
    }

    @Step("Set import good guy details")
    public void set_import_gg_details(RemoteWebDriver driver, String zone, String listSet, String blackList, String file_path) throws Exception {
        goodGuyEditor.select_zone(driver, zone);
        goodGuyEditor.select_list_set(driver, listSet);
        goodGuyEditor.select_black_list(driver, blackList);
        goodGuyEditor.set_file_path(driver, file_path);
    }

    @Step("Add new Good Guy")
    public String add_good_guy(RemoteWebDriver driver, String acceptedText, String rank) throws Exception {
        goodGuyManager.click_add_button(driver);
        set_accepted_text_and_rank(driver, acceptedText, rank);
        goodGuyEditor.click_scan_btn(driver);
        goodGuyEditor.select_matched_name(driver);
        goodGuyEditor.click_accept_button(driver);
        String validationMessage = commonAction.getAlertMessageString(driver);
        Allure.step(String.format("Validation message = %s .", validationMessage));
        return validationMessage;
    }

    @Step("Add new Good Guy")
    public String add_gg_by_select_all_matched_names(RemoteWebDriver driver, String acceptedText, String rank) throws Exception {
        goodGuyManager.click_add_button(driver);
        set_accepted_text_and_rank(driver, acceptedText, rank);
        goodGuyEditor.click_scan_btn(driver);
        goodGuyEditor.select_all_matched_names(driver);
        goodGuyEditor.click_accept_button(driver);
        String validationMessage = commonAction.getAlertMessageString(driver);
        Allure.step(String.format("Validation message = %s .", validationMessage));
        return validationMessage;
    }

    @Step("Add new Good Guy")
    public String add_gg_with_context_condition(RemoteWebDriver driver, EnList list, String acceptedText, String rank, String conditionName) throws Exception {
        search(driver, list);
        goodGuyManager.click_add_button(driver);
        set_accepted_text_and_rank(driver, acceptedText, rank);
        goodGuyEditor.click_scan_btn(driver);
        goodGuyEditor.select_matched_name(driver);
        goodGuyEditor.add_context_conditions(driver, conditionName);
        goodGuyEditor.click_accept_button(driver);
        String validationMessage = commonAction.getAlertMessageString(driver);
        Allure.step(String.format("Validation message = %s .", validationMessage));
        return validationMessage;
    }

    @Step("Delete Good Guy")
    public boolean delete_good_guy(RemoteWebDriver driver, String acceptedText) {
        search_by_accepted_text(driver, acceptedText);
        goodGuyManager.click_checkbox(driver);
        goodGuyManager.click_delete_button(driver);
        goodGuyManager.click_search_btn(driver);
        return goodGuyManager.verify_gg_exist(driver, acceptedText);
    }


    @Step("Export Good Guy")
    public boolean export_good_guy(RemoteWebDriver driver, String fileName, String exportType, String hub) throws Exception {
        navigate_to_gg(driver);
        goodGuyManager.click_reset_button(driver);
        goodGuyManager.click_search_button(driver);
        goodGuyManager.set_export_type(driver, exportType);
        goodGuyManager.click_export_button(driver);
        return textFilesHandler.checkExistOfFileWithDynamicName(driver, fileName, hub);
    }

    @Step("Import non shared good guy")
    public String import_non_shared_gg(RemoteWebDriver driver, String zone, String listSet, String blackList, String filePath) throws Exception {
        navigate_to_gg(driver);
        goodGuyManager.click_import_button(driver);
        set_import_gg_details(driver, zone, listSet, blackList, filePath);
        goodGuyEditor.click_import_btn(driver);

        String validationMessage = commonAction.getAlertMessageString(driver);
        Allure.step(String.format("Validation message = %s .", validationMessage));
        return validationMessage;
    }

    @Step("Import good guy as shared")
    public String import_shared_gg(RemoteWebDriver driver, String zone, String listSet, String blackList, String filePath) throws Exception {
        navigate_to_gg(driver);
        goodGuyManager.click_import_button(driver);
        set_import_gg_details(driver, zone, listSet, blackList, filePath);
        goodGuyEditor.click_import_as_shared_btn(driver);
        String validationMessage = commonAction.getAlertMessageString(driver);
        log.info(String.format("Validation message = %s .", validationMessage));
        return validationMessage;
    }

    @Step("Edit good guy")
    public String edit_good_guy(RemoteWebDriver driver, EnList list, String acceptedText) throws Exception {
        navigate_to_gg(driver);
        search(driver, list);
        goodGuyManager.click_checkbox(driver);
        goodGuyManager.click_edit_button(driver);
        set_accepted_text_and_rank(driver, acceptedText + "update", "");
        goodGuyEditor.click_scan_btn(driver);
        goodGuyEditor.click_accept_button(driver);
        String validationMessage = commonAction.getAlertMessageString(driver);
        Allure.step(String.format("Validation message = %s .", validationMessage));
        return validationMessage;
    }

    public void sort_by_rank_then_click_gg_button(RemoteWebDriver driver, String detectionID) throws InterruptedException {
        detectionManagerControl.search_by_id(driver, detectionID);
        detectionManagerControl.click_on_first_listed_detection(driver);
        detectionEditor.click_sort_by_rank(driver);
        detectionEditor.click_first_row_in_violations_list(driver);
        detectionEditor.click_good_guy_button(driver);
    }


    public boolean check_if_matched_entity_checked(RemoteWebDriver driver, String matched_entity) {
        goodGuyEditor.select_all_option(driver);
        goodGuyEditor.click_on_matched_entity(driver, matched_entity);
        return goodGuyEditor.check_if_Matched_entity_checkbox_checked(driver, matched_entity);
    }

}



