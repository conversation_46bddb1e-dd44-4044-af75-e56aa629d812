package eastnets.screening.control.listManager;

import core.gui.Controls;
import core.util.Log;
import core.util.Wait;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.Navigation;
import eastnets.screening.entity.EnList;
import eastnets.screening.entity.ListSet;
import eastnets.screening.entity.SwiftTemplate;
import eastnets.screening.gui.listManager.ListManagerNavigation;
import eastnets.screening.gui.listManager.listSet.ListSetEditor;
import eastnets.screening.gui.listManager.listSet.ListSetManager;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class ListSetControl {

    private final ListManagerNavigation listManagerNavigation;
    private final ListSetManager listSetManager;
    private final ListSetEditor listSetEditor;
    private final Controls controls;
    private final CommonAction commonAction;
    private final Wait wait;
    private final ListExplorerControl listExplorerControl;
    private final Log log;

    public ListSetControl() {
        this.listManagerNavigation = ListManagerNavigation.LIST_SET_MANAGER;
        this.listSetManager = new ListSetManager();
        this.listSetEditor = new ListSetEditor();
        this.controls = new Controls();
        this.commonAction = new CommonAction();
        this.wait = new Wait();
        this.listExplorerControl = new ListExplorerControl();
        this.log = new Log();
    }

    @Step("Navigate To List Set Manager Tab")
    public void navigate_to_listSet(RemoteWebDriver driver) {
        Navigation.LIST_MANAGER.navigate(driver);
        listManagerNavigation.navigate(driver);
    }

    @Step("Create new list set")
    public String create_new_list_set(RemoteWebDriver driver, ListSet listSet) throws Exception {
        controls.scrollUp(driver);
        listSetEditor.set_name(driver, listSet.getName());
        listSetEditor.select_owner(driver, listSet.getOwner());
        SwiftTemplate swiftTemplate = listSet.getSwiftTemplate();
        if (swiftTemplate != null)
            listSetEditor.select_swift(driver, swiftTemplate.getTemplateName());
        else
            listSetEditor.select_iso(driver, listSet.getIsoGroup());

        listSetEditor.select_detect_countries(driver, Boolean.parseBoolean(listSet.getDetectCountriesFlag()));
        listSetEditor.select_detect_vessels(driver, Boolean.parseBoolean(listSet.getDetectVesselsFlag()));
        listSetEditor.click_save_button(driver);

        String validationMessage = commonAction.getAllAlertMessageString(driver);
        log.info("Validation message = " + validationMessage);
        return validationMessage;
    }

    @Step("Create new list-set.")
    public String create_list_set(RemoteWebDriver driver, EnList list) throws Exception {
        navigate_to_listSet(driver);
        listSetManager.select_zone(driver, list.getZoneName());
        listSetManager.click_search(driver);
        listSetManager.click_create_new_list_set_button(driver);
        return create_new_list_set(driver, list.getListSet());
    }

    @Step("Link black list to list-set")
    public String link_black_list_to_list_set(RemoteWebDriver driver, EnList list) throws Exception {
        select_list_set(driver);
        listSetEditor.click_add_new_black_list(driver);
        listSetEditor.select_black_list(driver, list.getName());
        listSetEditor.click_add_button(driver);
        listSetEditor.click_save_button(driver);
        String validationMessage = commonAction.getAllAlertMessageString(driver);
        return validationMessage;
    }

    @Step("Link black list to list set without alert")
    public String link_black_list_to_list_set_without_alert(RemoteWebDriver driver, EnList list) throws Exception {
        select_list_set(driver);
        listSetEditor.click_add_new_black_list(driver);
        listSetEditor.select_black_list(driver, list.getName());
        wait.time(Wait.ONE_SECOND * 2);
        listSetEditor.click_add_button(driver);
        listSetEditor.click_save_button(driver);
        return commonAction.getAllAlertMessageString(driver);
    }

    @Step("Select list set from table")
    public void select_list_set(RemoteWebDriver driver) throws InterruptedException {
        navigate_to_listSet(driver);
        listSetManager.click_search(driver);
        listSetManager.sort_by_id_desc(driver);
        listSetManager.select_first_result(driver);
    }

    @Step("Link Black List To Exist List Set")
    public String link_black_list_to_exist_listSet(RemoteWebDriver driver, EnList list) throws Exception {

        if (!listExplorerControl.verify_black_list_linked_to_list_set(driver, list.getZoneName(), list.getName())) {
            select_list_set(driver);
            String result = link_black_list_to_list_set_without_alert(driver, list);
            //listSetEditor.click_close_button(driver);
            return result;
        }
        return "Black LIst already linked to list set.";
    }

    @Step("Select user profiles.")
    public String select_profiles(RemoteWebDriver driver, String profileName) throws Exception {

        select_list_set(driver);
        ListSetEditor.ListSetEditorNavigation.PROFILES_LIST_SET_ASSOCIATION.navigate(driver);
        listSetEditor.set_profile_filter(driver, profileName);
        wait.time(Wait.ONE_SECOND * 3);
        listSetEditor.click_select_all_btn(driver);
        wait.time(Wait.ONE_SECOND * 3);
        listSetEditor.click_save_button(driver);
        listSetEditor.wait_until_validation_message_appear(driver);
        String validationMessage = commonAction.getAllAlertMessageString(driver);
       // listSetEditor.click_close_button(driver);
        Allure.step("Validation Message = " + validationMessage);
        log.info("Validation Message = " + validationMessage);
        return validationMessage;
    }

    @Step("Get black list version from ListSet manager tab")
    public String get_blackList_version(RemoteWebDriver driver, String zone, String blackListName) throws Exception {
        navigate_to_listSet(driver);
        listSetManager.select_zone(driver, zone);
        listSetManager.click_search(driver);
        listSetManager.sort_by_id_desc(driver);
        listSetManager.select_first_result(driver);
        return listSetEditor.get_black_list_version(driver, blackListName);
    }

    @Step("Delete List Set")
    public boolean delete_list_set(RemoteWebDriver driver, String zone, String listSetName) throws Exception {
        navigate_to_listSet(driver);
        listSetManager.select_zone(driver, zone);
        listSetManager.click_search(driver);
        listSetManager.sort_by_id_desc(driver);
        listSetManager.click_check_box(driver, listSetName);
        listSetManager.click_delete_list_set_button(driver);
        listSetManager.select_zone(driver, zone);
        listSetManager.click_search(driver);
        listSetManager.sort_by_id_desc(driver);
        return controls.exists(driver, By.xpath(String.format("//td[.='%s']", listSetName)));
    }

    @Step("Delete List Set Associated with Detections")
    public String delete_list_Set(RemoteWebDriver driver, String zone, String listSetName) throws Exception {
        navigate_to_listSet(driver);
        listSetManager.select_zone(driver, zone);
        listSetManager.click_search(driver);
        listSetManager.sort_by_id_desc(driver);
        listSetManager.click_check_box(driver, listSetName);
        listSetManager.click_delete_list_set_button(driver);
        return commonAction.getAllAlertMessageString(driver);
    }


    @Step("Validate syntax for violation filter")
    public String validate_post_violation_filter(RemoteWebDriver driver, String exclusionRule) {
        listSetEditor.set_post_violation_filter(driver, exclusionRule);
        listSetEditor.click_post_validate_syntax_button(driver);

        String alertMessage = commonAction.getAllAlertMessageString(driver);
        Allure.step("Validation message = " + alertMessage);
        log.info("Validation message = " + alertMessage);

        return alertMessage;

    }

    @Step("Add violation Pre-filter")
    public String add_post_violation_filter(RemoteWebDriver driver, String zone, String listSetName, String blackListName, String exclusionRule) throws Exception {
        navigate_to_listSet(driver);
        listSetManager.select_zone(driver, zone);
        listSetManager.click_search(driver);
        listSetManager.sort_by_id_desc(driver);
        listSetManager.select_list_set_by_name(driver, listSetName);
        listSetEditor.select_black_list_by_name(driver, blackListName);
        return validate_post_violation_filter(driver, exclusionRule);
    }

    @Step("Validate syntax for violation filter")
    public String validate_pre_violation_filter(RemoteWebDriver driver, String exclusionRule) {
        listSetEditor.set_pre_violation_filter(driver, exclusionRule);
        listSetEditor.click_pre_validate_syntax_button(driver);

        String alertMessage = commonAction.getAllAlertMessageString(driver);
        Allure.step("Validation message = " + alertMessage);
        log.info("Validation message = " + alertMessage);

        return alertMessage;

    }

    @Step("Add violation Post-filter")
    public String add_pre_violation_filter(RemoteWebDriver driver, String zone, String listSetName, String blackListName, String exclusionRule) throws Exception {
        navigate_to_listSet(driver);
        listSetManager.select_zone(driver, zone);
        listSetManager.click_search(driver);
        listSetManager.sort_by_id_desc(driver);
        listSetManager.select_list_set_by_name(driver, listSetName);
        listSetEditor.select_black_list_by_name(driver, blackListName);
        return validate_pre_violation_filter(driver, exclusionRule);
    }

    @Step("Save violation filter")
    public String save_violation_filter(RemoteWebDriver driver, boolean reportExternalViolation) throws Exception {

        listSetEditor.click_report_external_violations(driver, reportExternalViolation);
        listSetEditor.click_save_button(driver);
        String validationMessage = commonAction.getAlertMessageString(driver);
        Allure.step("Validation Message = " + validationMessage);
       // listSetEditor.click_close_button(driver);
        return validationMessage;
    }

    @Step("Assign Clean Record")
    public String assign_clean_record(RemoteWebDriver driver, String zone, String listSetName, String assignee) throws Exception {
        navigate_to_listSet(driver);
        listSetManager.select_zone(driver, zone);
        listSetManager.click_search(driver);
        listSetManager.sort_by_id_desc(driver);
        listSetManager.select_list_set_by_name(driver, listSetName);
        listSetEditor.select_assign_clean_records(driver, assignee);
        listSetEditor.click_save_button(driver);
        return commonAction.getAlertMessageString(driver);
    }

    @Step("Check if table rows appear")
    public boolean verify_table_rows_appear(RemoteWebDriver driver) {
        navigate_to_listSet(driver);
        return listSetManager.verify_table_rows_appear(driver);
    }
}
