package eastnets.screening.control.listManager;

import core.util.Wait;
import eastnets.screening.gui.listManager.ListManagerNavigation;
import eastnets.screening.gui.listManager.activity.ActivityManager;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;

public class ActivityControl {

    private final ListManagerNavigation listManagerNavigation;
    private final ActivityManager activityManager;
    private final Wait wait;

    public ActivityControl() {
        this.listManagerNavigation = ListManagerNavigation.ACTIVITY;
        this.activityManager = new ActivityManager();
        this.wait = new Wait();
    }

    @Step("Get import list status")
    public String get_status(RemoteWebDriver driver) throws InterruptedException {
        listManagerNavigation.navigate(driver);
        int trials = 60;
        String status = null;
        while (trials != 0) {
            wait.time(Wait.ONE_SECOND * 5);
            activityManager.click_search(driver);
            status = activityManager.get_import_list_status(driver);
            if (status.equalsIgnoreCase("SUCCEEDED") || status.equalsIgnoreCase("FAILED")) {
                return status;
            }
            trials--;
        }
        Allure.step("Import list status = " + status);
        return status;

    }
}
