package eastnets.screening.control.listManager;

import core.constants.screening.GeneralConstants;
import core.util.Wait;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.Navigation;
import eastnets.screening.entity.EnList;
import eastnets.screening.gui.listManager.ListManagerNavigation;
import eastnets.screening.gui.listManager.activity.ActivityManager;
import eastnets.screening.gui.listManager.blackList.BlackListEditor;
import eastnets.screening.gui.listManager.blackList.BlackListManager;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;

public class BlackListControl {

    private final ListManagerNavigation listManagerNavigation;
    private final BlackListManager blackListManager;
    private final BlackListEditor blackListEditor;
    private final ActivityManager activityManager;
    private final CommonAction commonAction;
    private final Wait wait;
    private final ActivityControl activityControl;

    public BlackListControl() {
        this.listManagerNavigation = ListManagerNavigation.LIST_MANAGER;
        this.blackListManager = new BlackListManager();
        this.blackListEditor = new BlackListEditor();
        this.activityManager = new ActivityManager();
        this.commonAction = new CommonAction();
        this.wait = new Wait();
        this.activityControl = new ActivityControl();
    }

    @Step("Navigate To List Manager Tab")
    public void navigate(RemoteWebDriver driver) {

        Navigation.LIST_MANAGER.navigate(driver);
        listManagerNavigation.navigate(driver);
    }

    public boolean verify_reset_button_visibility(RemoteWebDriver driver) {
        return blackListManager.is_reset_button_visible(driver);
    }

    public boolean verify_search_button_visibility(RemoteWebDriver driver) {
        return blackListManager.is_search_button_visible(driver);
    }

    public boolean verify_create_new_list_button_visibility(RemoteWebDriver driver) {
        return blackListManager.is_create_new_list_button_visible(driver);
    }

    public boolean verify_delete_list_button_visibility(RemoteWebDriver driver) {
        return blackListManager.is_delete_list_button_visible(driver);
    }

    public boolean verify_share_list_button_visibility(RemoteWebDriver driver) {
        return blackListManager.is_share_list_button_visible(driver);
    }

    public boolean verify_import_list_button_visibility(RemoteWebDriver driver) {
        return blackListManager.is_import_list_button_visible(driver);
    }

    @Step("Search black list by name")
    public void search_black_list_by_name(RemoteWebDriver driver, String listName) {
        blackListManager.set_name(driver, listName);
        blackListManager.click_search(driver);
    }

    @Step("Search black list by zone")
    public void search_black_list_by_zone(RemoteWebDriver driver, String zone) throws Exception {
        blackListManager.set_zone(driver, zone);
        blackListManager.click_search(driver);
    }

    @Step("Search black list.")
    public void search(RemoteWebDriver driver, String zone, String listName, String privateFlag) throws Exception {
        blackListManager.reset_search(driver);
        blackListManager.set_zone(driver, zone);
        blackListManager.select_private_flag(driver, privateFlag);
        blackListManager.set_name(driver, listName);
        blackListManager.click_search(driver);
    }


    @Step("Verify if black list exist.")
    public boolean verify_list_exist(RemoteWebDriver driver, String zone, String listName) throws Exception {
        navigate(driver);
        blackListManager.reset_search(driver);
        search(driver, zone, listName, null);
        return blackListManager.verify_list_exist(driver, listName);
    }


    @Step("Create new black list.")
    public boolean create_Black_List(RemoteWebDriver driver, EnList list) throws Exception {
        if (!verify_list_exist(driver, list.getZoneName(), list.getName())) {
            blackListManager.click_add_new_list_button(driver);
            blackListEditor.set_name(driver, list.getName());
            blackListEditor.set_official_date(driver, list.getOfficialDate());
            blackListEditor.click_save(driver);
            try {
                blackListEditor.wait_until_list_name_appear(driver);
            } catch (Exception e) {
                blackListEditor.click_save(driver);
            }
            Allure.step("Search for list by listName = " + list.getName() + " to verify the existence of the list.");
            return verify_list_exist(driver, list.getZoneName(), list.getName());
        }
        Allure.step("Black list already exist.");
        return true;
    }

    @Step("Import List")
    public String import_list(RemoteWebDriver driver, String zone, String listPath, boolean lockFlag, boolean upgradeFlag) throws Exception {
        navigate(driver);
        blackListManager.click_search(driver);
        blackListManager.clickImportButton(driver);
        blackListEditor.select_zone(driver, zone);
        blackListEditor.set_file_path(driver, listPath);
        blackListEditor.set_lock_flag(driver, lockFlag);
        blackListEditor.set_upgrade_flag(driver, upgradeFlag);
        blackListEditor.click_import_button(driver);
        return activityControl.get_status(driver);
    }

    @Step("Lock Black List")
    public boolean lock_list(RemoteWebDriver driver) throws InterruptedException {
        blackListEditor.select_ver_history_checkbox(driver);
        blackListEditor.click_lock_button(driver);
        boolean flag = blackListEditor.verify_if_list_locked(driver);
        blackListEditor.click_Save_button_editor(driver);
        if (blackListEditor.verify_cancel_button_editor_exist(driver))
            blackListEditor.click_cancel_button_editor(driver);
        return flag;
    }

    @Step("Add new Black list Version")
    public void add_black_list_version(RemoteWebDriver driver, String version, String listName) {
        navigate(driver);
        search_black_list_by_name(driver, listName);
        blackListManager.select_first_result_from_table(driver);
        blackListEditor.click_add_version(driver);
        blackListEditor.set_ver_date(driver, version);
        blackListEditor.click_save_version(driver);
    }

    @Step("Share Black list")
    public String share_black_list(RemoteWebDriver driver, String blackListName) throws InterruptedException {
        navigate(driver);
        search_black_list_by_name(driver, blackListName);
        blackListManager.select_checkbox(driver);
        blackListManager.click_share_button(driver);

        String validationMessage = commonAction.getAlertMessageString(driver);
        Allure.step("Validation Message = " + validationMessage);

        return validationMessage;
    }

    @Step("Delete Black list")
    public boolean delete_black_list(RemoteWebDriver driver, String blackListName) throws Exception {
        navigate(driver);
        search_black_list_by_name(driver, blackListName);
        blackListManager.select_checkbox(driver);
        blackListManager.click_delete_list_button(driver);

        return verify_list_exist(driver, "", blackListName);
    }

    @Step("Export Black list")
    public String export_black_list(RemoteWebDriver driver, String zone, String privateFlag, String exportType) throws Exception {
        navigate(driver);
        search(driver, zone, "", privateFlag);
        if (blackListManager.select_first_result_from_table(driver) == true) {
            blackListEditor.select_ver_history_checkbox(driver);
            blackListEditor.click_export_button(driver);
            blackListEditor.select_export_type(driver, exportType);
            blackListEditor.click_export_submit_button(driver);
            wait.time(Wait.ONE_SECOND * 5);
            activityManager.click_search(driver);
            return activityControl.get_status(driver);

        }
        return GeneralConstants.FAILED;
    }


    public void select_auto_block(RemoteWebDriver driver, String zone, String listName, boolean flag) throws Exception {
        navigate(driver);
        search(driver, zone, listName, null);
        if (blackListManager.select_first_result_from_table(driver)) {
            blackListEditor.select_auto_block_option(driver, flag);
            blackListEditor.click_Save_button_editor(driver);

        }
    }

    @Step("Lock black list")
    public boolean Lock_black_list(RemoteWebDriver driver, String zone, String listName) throws Exception {
        navigate(driver);
        search(driver, zone, listName, "");
        if (blackListManager.select_first_result_from_table(driver)) {
            return lock_list(driver);
        }
        return false;
    }

    @Step("Upgrade black list")
    public String upgrade_black_list(RemoteWebDriver driver, String zone, String listName) throws Exception {
        navigate(driver);
        search(driver, zone, listName, "");
        if (blackListManager.select_first_result_from_table(driver)) {
            blackListEditor.select_ver_history_checkbox(driver);
            blackListEditor.click_upgrade_button_editor(driver);
            return "Successfully upgraded.";
        }
        return GeneralConstants.FAILED;
    }

    @Step("Delete black list Edition")
    public String delete_black_list_edition(RemoteWebDriver driver, String zone, String listName) throws Exception {
        navigate(driver);
        search(driver, zone, listName, "");
        if (blackListManager.select_first_result_from_table(driver)) {
            blackListEditor.select_ver_history_checkbox(driver);
            blackListEditor.click_delete_button_editor(driver);
            blackListEditor.click_cancel_button_editor(driver);
            return "Successfully deleted.";
        }
        return GeneralConstants.FAILED;

    }

    @Step("Update Black List")
    public boolean update_black_list_name(RemoteWebDriver driver, String zone, String listName, String updatedName) throws Exception {
        navigate(driver);
        search(driver, zone, listName, "");
        if (blackListManager.select_first_result_from_table(driver)) {
            blackListEditor.set_name_editor(driver, updatedName);
            blackListEditor.click_Save_button_editor(driver);
            return verify_list_exist(driver, zone, updatedName);
        }
        return false;
    }
}
