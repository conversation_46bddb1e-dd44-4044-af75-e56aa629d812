package eastnets.screening.control.listManager;

import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.SftpException;
import core.constants.screening.GeneralConstants;
import core.util.*;
import eastnets.common.gui.Navigation;
import eastnets.screening.gui.listManager.ListManagerNavigation;
import eastnets.screening.gui.listManager.worldCheck.WorldCheckEditor;
import eastnets.screening.gui.listManager.worldCheck.WorldCheckManager;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.io.IOException;

public class WorldCheckControl {

    private final ListManagerNavigation listManagerNavigation;
    private final WorldCheckManager worldCheckManager;
    private final WorldCheckEditor worldCheckEditor;
    private final Wait wait;
    private final TextFilesHandler textFilesHandler;
    private final ServerUtil serverUtil;
    private final Log log;
    private final Property property;

    public WorldCheckControl() {
        this.listManagerNavigation = ListManagerNavigation.WORLD_CHECK;
        this.worldCheckManager = new WorldCheckManager();
        this.worldCheckEditor = new WorldCheckEditor();
        this.wait = new Wait();
        this.textFilesHandler = new TextFilesHandler();
        this.serverUtil = new ServerUtil();
        this.log = new Log();
        this.property = new Property();
    }

    @Step("Navigate To List Set Manager Tab")
    public void navigate_to_world_check(RemoteWebDriver driver) {
        Navigation.LIST_MANAGER.navigate(driver);
        listManagerNavigation.navigate(driver);
    }

    @Step("Search world check")
    public void search(RemoteWebDriver driver, String zone) throws Exception {
        worldCheckManager.select_zone(driver, zone);
        worldCheckManager.click_search(driver);

    }

    @Step("Delete world check")
    private void delete_world_check(RemoteWebDriver driver, String wc_name) throws InterruptedException {
        worldCheckManager.select_wc_checkbox_by_name(driver, wc_name);
        worldCheckManager.click_delete_btn(driver);
        wait.time(Wait.ONE_SECOND * 2);
    }

    @Step("Set world check creation form details")
    public void set_wc_details(RemoteWebDriver driver, String name, String black_list, String mode, boolean isEnabled, boolean isNewVersionOnUpdate) throws Exception {
        worldCheckEditor.set_name(driver, name);
        worldCheckEditor.select_black_list(driver, black_list);
        worldCheckEditor.select_mode(driver, mode);
        worldCheckEditor.is_enabled(driver, isEnabled);
        worldCheckEditor.select_new_ver_on_update(driver, isNewVersionOnUpdate);

        if (mode == "PEP") {
            worldCheckEditor.click_all_countries(driver);
        }
        worldCheckEditor.click_all_categories(driver);
        worldCheckEditor.click_save_btn(driver);

    }

    @Step("Create new world Check ")
    public boolean createNewWorldCheck(RemoteWebDriver driver, String zone, String worldCheckName, String bListName, String mode, boolean isEnabled, boolean isNewVersionOnUpdate) throws Exception {
        navigate_to_world_check(driver);
        search(driver, zone);
        worldCheckManager.click_add_btn(driver);
        set_wc_details(driver, worldCheckName, bListName, mode, isEnabled, isNewVersionOnUpdate);
        search(driver, zone);
        ;
        worldCheckManager.sort_descending_by_creation_date(driver);
        return worldCheckManager.is_wc_exist(driver, worldCheckName);
    }


    @Step("Enable world Check list by editing.")
    public String enableWorldCheckByEditing(RemoteWebDriver driver, String zone, String worldCheckName) throws Exception {
        navigate_to_world_check(driver);
        search(driver, zone);
        worldCheckManager.select_wc_by_name(driver, worldCheckName);
        worldCheckEditor.clickEnableButton_EditorForm(driver, true);
        worldCheckEditor.clickSaveButton_EditorForm(driver);
        worldCheckManager.select_zone(driver, zone);
        worldCheckManager.click_search(driver);
        return worldCheckManager.is_wc_enabled(driver, worldCheckName);
    }

    @Step("Enable 'New Version On Update' flag for world Check list by editing.")
    public void enable_new_version_on_update_editing(RemoteWebDriver driver, String zone, String worldCheckName) throws Exception {
        navigate_to_world_check(driver);
        search(driver, zone);
        worldCheckManager.select_wc_by_name(driver, worldCheckName);
        worldCheckEditor.set_New_ver_on_update_Edit(driver, true);
        worldCheckEditor.clickSaveButton_EditorForm(driver);
    }


    @Step("Delete world check list.")
    public boolean delete_world_check(RemoteWebDriver driver, String zone, String worldCheckName) throws Exception {
        navigate_to_world_check(driver);
        search(driver, zone);
        delete_world_check(driver, worldCheckName);
        worldCheckManager.select_zone(driver, zone);
        worldCheckManager.click_search(driver);
        return worldCheckManager.is_wc_exist(driver, worldCheckName);
    }

    @Step("Get avail country list size")
    public String get_avail_country_list_size(RemoteWebDriver driver, String zone, String mode) throws Exception {
        navigate_to_world_check(driver);
        search(driver, zone);
        worldCheckManager.click_add_btn(driver);
        worldCheckEditor.select_mode(driver, mode);
        return worldCheckEditor.get_avail_country_list_size(driver);
    }

    @Step("Get avail umbrella list size")
    public int get_avail_umbrella_list_size(RemoteWebDriver driver, String zone, String mode) throws Exception {
        navigate_to_world_check(driver);
        search(driver, zone);
        worldCheckManager.click_add_btn(driver);
        worldCheckEditor.select_mode(driver, mode);
        return worldCheckEditor.get_avail_umbrella_list_size(driver);
    }


    public static String loginNewFilePath = System.getProperty("user.dir") + new Property().fromFile(GeneralConstants.SCREENING_TEST_DATA_CONFIG_FILE_PATH).getProperty("loginNewFile");
    public static String loginSampleFilePath = System.getProperty("user.dir") + new Property().fromFile(GeneralConstants.SCREENING_TEST_DATA_CONFIG_FILE_PATH).getProperty("loginSampleFile");

    @Step("Create DJ Login File")
    public void create_login_file(String userName, String password, String zoneId) throws IOException {
        String fileContent = textFilesHandler.getTextFileContentAsString(loginSampleFilePath);

        fileContent = fileContent.replaceFirst("#", GeneralConstants.LOGIN_FILE_NAME);
        fileContent = fileContent.replaceFirst("#", userName);
        fileContent = fileContent.replaceFirst("#", password);
        fileContent = fileContent.replaceFirst("#", zoneId);
        Allure.step("Dow jones Login File content = " + fileContent);
        Allure.step("Start coping data from sample file to another file to be used on server.");
        textFilesHandler.writeToFile(loginNewFilePath, fileContent);
    }

    @Step("Create World Check Files And Run CMD")
    public String create_wc_files_and_run_cmd(String wCheckCommand, String userName, String password, String zoneId, String dataFileName, String wCheckListName, String nativeFile, String refFile) throws IOException, SftpException, JSchException, InterruptedException {
        Allure.step("Create login file.");
        create_login_file(userName, password, zoneId);

        log.info("Transfer login file to server under location = " + GeneralConstants.DJ_LOGIN_FILE_CREATOR_PATH);
        serverUtil.transferFileToServer(loginNewFilePath, "/" + GeneralConstants.DJ_LOGIN_FILE_CREATOR_PATH, GeneralConstants.SWF_APP_NAME);

        serverUtil.runCommand("cd /d " + GeneralConstants.DJ_LOGIN_FILE_CREATOR_PATH + "&& LoginFileAutomation.cmd", GeneralConstants.SWF_APP_NAME, false);

        Allure.step("Transfer Login File " + GeneralConstants.LOGIN_FILE_NAME + " file from ---> " + GeneralConstants.DJ_LOGIN_FILE_CREATOR_PATH + "/" + GeneralConstants.LOGIN_FILE_NAME +
                " To --->" + GeneralConstants.UTILS_FOLDER_PATH + "/" + GeneralConstants.LOGIN_FILE_NAME);
        serverUtil.moveFileInServer("/" + GeneralConstants.DJ_LOGIN_FILE_CREATOR_PATH + "/" + GeneralConstants.LOGIN_FILE_NAME
                , GeneralConstants.UTILS_FOLDER_PATH + "/" + GeneralConstants.LOGIN_FILE_NAME, GeneralConstants.SWF_APP_NAME);

        Allure.step("Create World file.");
        create_wc_file(wCheckCommand, dataFileName, wCheckListName, nativeFile, refFile);

        Allure.step("Transfer World Check file to server under location = " + GeneralConstants.UTILS_FOLDER_PATH);
        serverUtil.transferFileToServer(GeneralConstants.WC_FILE_PATH, "/" + GeneralConstants.UTILS_FOLDER_PATH, GeneralConstants.SWF_APP_NAME);

        return serverUtil.runCommand("cd /d  " + GeneralConstants.UTILS_FOLDER_PATH/*.substring(1)*/ + " && " + "WorldCheckAutomation.bat", GeneralConstants.SWF_APP_NAME, false);
    }


    @Step("Create World Check File")
    public void create_wc_file(String wCheckCommand, String dataFileName, String wCheckListName, String nativeFile, String refFile) throws IOException {
        wCheckCommand = wCheckCommand.replaceFirst("#JAR_FILE_NAME#", GeneralConstants.WC_JARFILE_NAME);
        wCheckCommand = wCheckCommand.replaceFirst("#LOGIN_FILE#", GeneralConstants.LOGIN_FILE_NAME);
        wCheckCommand = wCheckCommand.replaceFirst("#DATA_FILE#", dataFileName);
        wCheckCommand = wCheckCommand.replaceFirst("#WORLD_CHECK_LIST#", wCheckListName);
        wCheckCommand = wCheckCommand.replaceFirst("#NATIVE_FILE#", nativeFile);
        wCheckCommand = wCheckCommand.replaceFirst("#REF_FILE#", refFile);


        log.info("World check File content = " + wCheckCommand);
        textFilesHandler.writeToFile(GeneralConstants.WC_FILE_PATH, wCheckCommand);
    }

}
