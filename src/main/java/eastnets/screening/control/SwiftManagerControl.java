package eastnets.screening.control;

import eastnets.common.gui.Navigation;
import eastnets.screening.entity.SwiftTemplate;
import eastnets.screening.gui.SwiftManager.SwiftTempEditor;
import eastnets.screening.gui.SwiftManager.SwiftTempManager;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;

public class SwiftManagerControl {

    private final SwiftTempManager swiftTempManager;
    private final SwiftTempEditor swiftTempEditor;

    public SwiftManagerControl() {
        this.swiftTempManager = new SwiftTempManager();
        this.swiftTempEditor = new SwiftTempEditor();
    }

    @Step("Navigate To Swift Manger Module")
    private void navigateToSwiftMangerModule(RemoteWebDriver driver) {
        Navigation.SWIFT_MANAGER.navigate(driver);
    }

    @Step("Create new swift template")
    public boolean CreateSwiftTemplate(RemoteWebDriver driver, SwiftTemplate swift, String zone) throws Exception {
        navigateToSwiftMangerModule(driver);
        swiftTempManager.setZone(driver, zone);
        swiftTempManager.clickSearchButton(driver);
        swiftTempManager.clickAddButton(driver);
        swiftTempEditor.setTemplateName(driver, swift.getTemplateName());
        swiftTempEditor.selectAllMessageTypes(driver);
        swiftTempEditor.selectAllMessageFields(driver);
        swiftTempEditor.clickSaveButton(driver);
        return swiftTempEditor.checkIfSwiftTemplateExist(driver);
    }
}