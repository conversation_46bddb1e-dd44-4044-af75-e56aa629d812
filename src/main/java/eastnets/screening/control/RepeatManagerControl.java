package eastnets.screening.control;

import core.util.Wait;
import eastnets.common.control.CommonAction;
import eastnets.common.gui.Navigation;
import eastnets.screening.entity.Repeat;
import eastnets.screening.entity.RepeatedData;
import eastnets.screening.gui.repeatManager.*;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.util.List;

public class RepeatManagerControl {

    private final ConfigurationManager configurationManager;
    private final ConfigurationEditor configurationEditor;
    private final DataManager dataManager;
    private final DataEditor dataEditor;
    private final CommonAction commonAction;
    private final Wait wait;

    public RepeatManagerControl() {
        this.configurationManager = new ConfigurationManager();
        this.configurationEditor = new ConfigurationEditor();
        this.dataManager = new DataManager();
        this.dataEditor = new DataEditor();
        this.commonAction = new CommonAction();
        this.wait = new Wait();
    }

    @Step("Navigate to 'Configuration' tab")
    public void navigateToConfigurationTab(RemoteWebDriver driver) {
        Navigation.REPEAT_MANAGER.navigate(driver);
        RepeatManagerNavigation.Configuration.navigate(driver);
    }

    @Step("Navigate to 'Data' tab")
    public void navigateToDataTab(RemoteWebDriver driver) {
        Navigation.REPEAT_MANAGER.navigate(driver);
        RepeatManagerNavigation.Data.navigate(driver);
    }

    @Step("Create Repeat Config")
    public String createRepeatConfig(RemoteWebDriver driver, Repeat repeat) throws Exception {
        navigateToConfigurationTab(driver);
        configurationManager.checkConfigurationEnableCheckbox(driver, repeat);
        configurationManager.selectListName(driver, repeat);
        configurationManager.setRetentionTime(driver, repeat);
        configurationManager.clickSaveButton(driver);
        configurationManager.selectZone(driver, repeat);
        configurationManager.clickAddButton(driver);
        configurationEditor.setConfigName(driver, repeat);
        configurationEditor.setType(driver, repeat);
        configurationEditor.setSubType(driver, repeat);
        configurationEditor.setRule1(driver, repeat);
        configurationEditor.clickSaveButton(driver);
        String alertMessage = commonAction.getAlertMessageString(driver);
        Allure.step("Alert message = " + alertMessage);
        return alertMessage;
    }

    @Step("Delete all repeat configurations")
    public void deleteAllRepeatConfigurations(RemoteWebDriver driver) {
        navigateToConfigurationTab(driver);
        configurationManager.clickConfigurationOptionButton(driver);
        configurationManager.clickConfigurationSelectAllButton(driver);
        configurationManager.clickDeleteButton(driver);
        driver.switchTo().alert().accept();
    }

    @Step("Check Repeat Config Exist")
    public boolean checkRepeatConfigExist(RemoteWebDriver driver, Repeat repeat) {
        return configurationManager.verifyRepeatConfigExist(driver, repeat.getConfigName());
    }

    @Step("Create Repeat Config Duplicate")
    public String createRepeatConfigDuplicate(RemoteWebDriver driver, Repeat repeat) throws Exception {
        navigateToConfigurationTab(driver);
        createRepeatConfig(driver, repeat);
        checkRepeatConfigExist(driver, repeat);
        String alertMessage = commonAction.getAlertMessageString(driver);
        Allure.step("Alert message = " + alertMessage);
        return alertMessage;
    }

    @Step("Verify Duplicated Repeated MT")
    public void verifyDuplicatedRepeatedMT(RemoteWebDriver driver, Repeat repeat) throws Exception {
        navigateToConfigurationTab(driver);
        createRepeatConfig(driver, repeat);
        createRepeatConfigDuplicate(driver, repeat);
        createRepeatConfig(driver, repeat);
    }

    @Step("Verify Added Repeat Data")
    public List<RepeatedData> getRepeatData(RemoteWebDriver driver) throws Exception {
        wait.waitUntilAjaxLoaderDisappear(driver);
        navigateToDataTab(driver);
        dataManager.clickResetButton(driver);
        dataManager.clickSearchButton(driver);
        return dataEditor.getRepeatedDataFromResultTable(driver);
        //return Controls.getTableValue(driver, TABLE_ROWS_LOCATOR, 0, 7);
        //return Controls.exists(driver , By.xpath(String.format("//tbody//tr//td[.='%s']", remarks)));
    }
}