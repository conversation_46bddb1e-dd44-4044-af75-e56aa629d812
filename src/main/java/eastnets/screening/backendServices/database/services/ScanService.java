package eastnets.screening.backendServices.database.services;

import com.opencsv.exceptions.CsvValidationException;
import core.database.DatabaseDriver;
import eastnets.common.control.Application;
import eastnets.screening.backendServices.database.daos.ScanDAO;

import java.io.IOException;
import java.sql.Connection;
import java.sql.SQLException;

public class ScanService {

    private final DatabaseDriver databaseDriver;

    public ScanService() {
        this.databaseDriver = new DatabaseDriver();
    }

    public int CreateDBScanTable() throws SQLException {
        //Open connection to users database
        Connection connection = databaseDriver.getConnection(Application.SFP);
        ScanDAO scanDAO = new ScanDAO();
        int effectedRowsNumber = scanDAO.CreateDBScanTable(connection);
        //close db connection
        databaseDriver.closeDBConnection(connection);
        return effectedRowsNumber;
    }

    public int insertDataInDBScanTable(String filePath) throws SQLException, IOException, CsvValidationException {
        //Open connection to users database
        Connection connection = databaseDriver.getConnection(Application.SFP);
        ScanDAO scanDAO = new ScanDAO();
        int effectedRowsNumber = scanDAO.insertDataInDBScanTable(connection, filePath);
        //close db connection
        databaseDriver.closeDBConnection(connection);
        return effectedRowsNumber;
    }
}
