package eastnets.screening.backendServices.database.services;

import core.database.DatabaseDriver;
import eastnets.common.control.Application;
import eastnets.screening.backendServices.database.daos.DBManagerDAO;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;

public class DBManagerService {

    private final DatabaseDriver databaseDriver;

    public DBManagerService() {
        this.databaseDriver = new DatabaseDriver();
    }

    public List<String> get_db_flows_list(String zone_name, String flow_type) throws SQLException {
        //Open connection to users database
        Connection connection = databaseDriver.getConnection(Application.SFP);
        DBManagerDAO db_manager_dao = new DBManagerDAO();
        List<String> db_flow_list = db_manager_dao.get_db_flows_list(connection, zone_name, flow_type);
        //close db connection
        databaseDriver.closeDBConnection(connection);
        return db_flow_list;
    }
}
