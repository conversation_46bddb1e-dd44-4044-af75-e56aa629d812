package eastnets.screening.backendServices.database.services;

import core.database.DatabaseDriver;
import eastnets.common.control.Application;
import eastnets.screening.backendServices.database.daos.ConfigurationDAO;

import java.io.IOException;
import java.sql.Connection;
import java.sql.SQLException;

public class ConfigurationService {

    private final DatabaseDriver databaseDriver;

    public ConfigurationService() {
        this.databaseDriver = new DatabaseDriver();
    }

    public int updatetConfiguration(String variableName) throws SQLException, IOException {
        //Open connection to users database
        Connection connection = databaseDriver.getConnection(Application.SFP);
        ConfigurationDAO configurationDAO = new ConfigurationDAO();
        int effectedRowsNumber = configurationDAO.updatetConfiguration(connection, variableName);
        //close db connection
        databaseDriver.closeDBConnection(connection);
        return effectedRowsNumber;
    }
}