package eastnets.screening.backendServices.database.services;

import core.database.DatabaseDriver;
import eastnets.common.control.Application;
import eastnets.screening.backendServices.database.daos.FormatDao;

import java.sql.Connection;
import java.sql.SQLException;

public class FormatService {

    private final DatabaseDriver databaseDriver;

    public FormatService() {
        this.databaseDriver = new DatabaseDriver();
    }

    public int cleanFormatTable(String zone) throws SQLException {
        //Open connection to users database
        Connection connection = databaseDriver.getConnection(Application.ADMIN);
        FormatDao formatDao = new FormatDao();
        int detetedRowsNumber = formatDao.cleanFormatTable(connection, zone);
        //close db connection
        databaseDriver.closeDBConnection(connection);
        return detetedRowsNumber;
    }
}
