package eastnets.screening.backendServices.database.services;

import core.database.DatabaseDriver;
import eastnets.common.control.Application;
import eastnets.screening.backendServices.database.daos.EntityDAO;

import java.sql.Connection;
import java.sql.SQLException;

public class EntityService {

    private final DatabaseDriver databaseDriver;

    public EntityService() {
        this.databaseDriver = new DatabaseDriver();
    }

    public String getEntityNameFromList(String listName) throws SQLException {
        //Open connection to users database
        Connection connection = databaseDriver.getConnection(Application.SFP);
        EntityDAO entityDAO = new EntityDAO();
        String entityName = entityDAO.getEntityName(connection, listName);
        //close db connection
        databaseDriver.closeDBConnection(connection);
        return entityName;
    }

    public String getEntitiesCount(String listName) throws SQLException {
        //Open connection to users database
        Connection connection = databaseDriver.getConnection(Application.SFP);
        EntityDAO entityDAO = new EntityDAO();
        String count = entityDAO.getEntitiesCount(connection, listName);
        //close db connection
        databaseDriver.closeDBConnection(connection);
        return count;
    }
}
