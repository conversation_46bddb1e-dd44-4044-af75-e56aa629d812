package eastnets.screening.backendServices.database.services;

import core.database.DatabaseDriver;
import eastnets.common.control.Application;
import eastnets.screening.backendServices.database.daos.BicsDAO;

import java.sql.Connection;
import java.sql.SQLException;

public class BicsService {

    private final DatabaseDriver databaseDriver;

    public BicsService() {
        this.databaseDriver = new DatabaseDriver();
    }

    public int excuteQuerySFP(String queryString) throws SQLException {
        //Open connection to users database
        Connection connection = databaseDriver.getConnection(Application.SFP);
        BicsDAO bicsDAO = new BicsDAO();
        int effectedRowsNumber = bicsDAO.excuteQuery(connection, queryString);
        //close db connection
        databaseDriver.closeDBConnection(connection);
        return effectedRowsNumber;
    }

    public int excuteQueryAdmin(String queryString) throws SQLException {
        //Open connection to users database
        Connection connection = databaseDriver.getConnection(Application.ADMIN);
        BicsDAO bicsDAO = new BicsDAO();
        int effectedRowsNumber = bicsDAO.excuteQuery(connection, queryString);
        //close db connection
        databaseDriver.closeDBConnection(connection);
        return effectedRowsNumber;
    }

    public int deleteBics() throws SQLException {
        //Open connection to users database
        Connection connection = databaseDriver.getConnection(Application.SFP);
        BicsDAO bicsDAO = new BicsDAO();
        int effectedRowsNumber = bicsDAO.deleteBics(connection);
        //close db connection
        databaseDriver.closeDBConnection(connection);
        return effectedRowsNumber;
    }
}
