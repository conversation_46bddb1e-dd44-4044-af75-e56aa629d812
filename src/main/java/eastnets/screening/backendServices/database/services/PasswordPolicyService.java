package eastnets.screening.backendServices.database.services;

import core.database.DatabaseDriver;
import eastnets.common.control.Application;
import eastnets.screening.backendServices.database.daos.PasswordPolicyDAO;

import java.sql.Connection;
import java.sql.SQLException;

public class PasswordPolicyService {

    private final DatabaseDriver databaseDriver;

    public PasswordPolicyService() {
        this.databaseDriver = new DatabaseDriver();
    }

    public int resetPasswordPolicyData() throws SQLException {
        //Open connection to users database
        Connection connection = databaseDriver.getConnection(Application.ADMIN);
        PasswordPolicyDAO passwordPolicyDAO = new PasswordPolicyDAO();
        int effectedRowsNumber = passwordPolicyDAO.resetPasswordPolicyData(connection);
        //close db connection
        databaseDriver.closeDBConnection(connection);
        return effectedRowsNumber;
    }

    public int resetOperatorPasswordByManager(String operatorName) throws SQLException {
        //Open connection to users database
        Connection connection = databaseDriver.getConnection(Application.ADMIN);
        PasswordPolicyDAO passwordPolicyDAO = new PasswordPolicyDAO();
        int effectedRowsNumber = passwordPolicyDAO.resetOperatorPasswordByManager(connection, operatorName);
        //close db connection
        databaseDriver.closeDBConnection(connection);
        return effectedRowsNumber;
    }

    public int resetOperatorPassword(String operatorName) throws SQLException {
        //Open connection to users database
        Connection connection = databaseDriver.getConnection(Application.ADMIN);
        PasswordPolicyDAO passwordPolicyDAO = new PasswordPolicyDAO();
        int effectedRowsNumber = passwordPolicyDAO.resetOperatorPassword(connection, operatorName);
        //close db connection
        databaseDriver.closeDBConnection(connection);
        return effectedRowsNumber;
    }

    public int unlockOperator(String operatorName) throws SQLException {
        //Open connection to users database
        Connection connection = databaseDriver.getConnection(Application.ADMIN);
        PasswordPolicyDAO passwordPolicyDAO = new PasswordPolicyDAO();
        int effectedRowsNumber = passwordPolicyDAO.unlockOperator(connection, operatorName);
        //close db connection
        databaseDriver.closeDBConnection(connection);
        return effectedRowsNumber;
    }
}
