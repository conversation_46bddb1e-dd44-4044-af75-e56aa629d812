package eastnets.screening.backendServices.database.services;

import core.database.DatabaseDriver;
import eastnets.admin.backendServices.AdminServicesDelegate;
import eastnets.admin.backendServices.database.services.ProfileService;
import eastnets.common.control.Application;
import eastnets.screening.backendServices.database.daos.ListSetDAO;

import java.io.IOException;
import java.sql.Connection;
import java.sql.SQLException;

public class ListSetService {

    private final DatabaseDriver databaseDriver;
    private final AdminServicesDelegate adminServicesDelegate;

    public ListSetService() {
        this.databaseDriver = new DatabaseDriver();
        this.adminServicesDelegate = new AdminServicesDelegate();
    }

    public int deleteProfileFromListSet(String profileName) throws SQLException, IOException {
        //Open connection to users database
        Connection connection = databaseDriver.getConnection(Application.ADMIN);
        ProfileService profileService = new ProfileService();
        int profileID = profileService.getProfileID(profileName);
        connection = databaseDriver.getConnection(Application.SFP);
        ListSetDAO listSetDAO = new ListSetDAO();
        int effectedRowsNumber = listSetDAO.deleteProfileFromListSet(connection, profileID);
        //close db connection
        databaseDriver.closeDBConnection(connection);
        return effectedRowsNumber;
    }

    public boolean checkIfListSetLinkedToProfile(String profileName) throws SQLException, IOException {
        //Open connection to users database
        Connection connection = databaseDriver.getConnection(Application.ADMIN);
        ListSetDAO listSetDAO = new ListSetDAO();
        boolean flag = listSetDAO.checkIfListSetLinkedToProfile(connection, profileName);
        //close db connection
        databaseDriver.closeDBConnection(connection);
        return flag;
    }

    public int deleteLinkBtwBlackListAndListSet(String zone, String bList) throws SQLException {
        //Open connection to users database
        String zoneId = adminServicesDelegate.getZoneId(zone);
        Connection connection = databaseDriver.getConnection(Application.SFP);
        ListSetDAO listSetDAO = new ListSetDAO();
        int numOfEffectedRows = listSetDAO.deleteLinkBtwBlackListAndListSet(connection, zoneId, bList);
        //close db connection
        databaseDriver.closeDBConnection(connection);
        return numOfEffectedRows;
    }

    public int deleteLinkBtwBlackListAndGoodGuy(String zone, String bList) throws SQLException {
        //Open connection to users database
        Connection connection = databaseDriver.getConnection(Application.SFP);
        ListSetDAO listSetDAO = new ListSetDAO();
        int numOfEffectedRows = listSetDAO.deleteLinkBtwBlackListAndGoodGuy(connection, zone, bList);
        //close db connection
        databaseDriver.closeDBConnection(connection);
        return numOfEffectedRows;
    }

    public int deleteBlackList(String bList) throws SQLException {
        //Open connection to users database
        Connection connection = databaseDriver.getConnection(Application.SFP);
        ListSetDAO listSetDAO = new ListSetDAO();
        int numOfEffectedRows = listSetDAO.deleteBlackList(connection, bList);
        //close db connection
        databaseDriver.closeDBConnection(connection);
        return numOfEffectedRows;
    }

}
