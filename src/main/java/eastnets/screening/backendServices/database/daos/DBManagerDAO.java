package eastnets.screening.backendServices.database.daos;

import core.database.DatabaseDriver;
import eastnets.admin.backendServices.AdminServicesDelegate;
import eastnets.screening.backendServices.ScreeningServicesDelegate;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class DBManagerDAO {

    private final DatabaseDriver databaseDriver;
    private final AdminServicesDelegate adminServicesDelegate;

    public DBManagerDAO() {
        this.databaseDriver = new DatabaseDriver();
        this.adminServicesDelegate = new AdminServicesDelegate();
    }

    public List<String> get_db_flows_list(Connection dbConn, String zone_name, String flow_type) throws SQLException {
        StringBuilder query = new StringBuilder();
        String zone_id = adminServicesDelegate.getZoneId(zone_name);
        query.append(String.format("select flowname from tDBFlows td where zone_id = '%s' and flowtype = '%s'", zone_id, flow_type));
        ResultSet rs = databaseDriver.executeQueryAndGetRS(dbConn, query.toString());
        List<String> db_flow_names = new ArrayList<>();
        while (rs.next()) {
            db_flow_names.add(rs.getString("flowname") == null ? "" : rs.getString("flowname"));
        }
        return db_flow_names;
    }

}
