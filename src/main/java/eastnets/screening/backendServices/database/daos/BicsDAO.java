package eastnets.screening.backendServices.database.daos;

import core.database.DatabaseDriver;

import java.sql.Connection;
import java.sql.SQLException;

public class BicsDAO {

    private final DatabaseDriver databaseDriver;

    public BicsDAO() {
        this.databaseDriver = new DatabaseDriver();
    }

    public int excuteQuery(Connection dbConn, String queryString) throws SQLException {
        //Create DB Query to delete profile from listSet
        StringBuilder query = new StringBuilder();
        query.append(queryString);
        // Execute query
        return databaseDriver.executeUpdate(dbConn, query.toString());
    }

    public int deleteBics(Connection dbConn) throws SQLException {
        //Create DB Query to delete profile from listSet
        StringBuilder query = new StringBuilder();
        query.append("DELETE FROM tSwiftBics");
        // Execute query
        return databaseDriver.executeUpdate(dbConn, query.toString());
    }
}
