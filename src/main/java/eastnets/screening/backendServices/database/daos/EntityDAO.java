package eastnets.screening.backendServices.database.daos;

import core.database.DatabaseDriver;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;

public class EntityDAO {

    private final DatabaseDriver databaseDriver;

    public EntityDAO() {
        this.databaseDriver = new DatabaseDriver();
    }

    public String getEntityName(Connection dbConn, String listName) throws SQLException {
        //Create DB Query to delete profile from listSet
        StringBuilder query = new StringBuilder();
        query.append("select\n" +
                "tblen.name as entityName\n" +
                "from tBlackListEntryNames tblen\n" +
                "left join tBlackListEntries tble on tblen.entry_id = tble.id\n" +
                "left join tBlackListVersions tblv on tble.black_list_id = tblv.id\n" +
                "left join tBlackLists tbl on tblv.black_list_id = tbl.id\n" +
                "where \n" +
                "tbl.display_name like '%" + listName + "%'");
        // Execute query
        ResultSet rs = databaseDriver.executeQueryAndGetRS(dbConn, query.toString());
        String entityName = "";
        if (rs.next()) {
            entityName = rs.getString("entityName") == null ? "" : rs.getString("entityName");
        }
        return entityName;
    }

    public String getEntitiesCount(Connection dbConn, String listName) throws SQLException {
        //Create DB Query to delete profile from listSet
        StringBuilder query = new StringBuilder();
        query.append("select count(*) as count from tBlackListEntries tble " +
                " where tble.id in " +
                "(select id from tBlackListVersions tblv  where tble.id  in " +
                "(select id from tBlackLists tbl where tbl.display_name ='" + listName + "' ))");
        // Execute query
        ResultSet rs = databaseDriver.executeQueryAndGetRS(dbConn, query.toString());
        rs.next();
        return rs.getString("count");
    }
}
