package eastnets.screening.backendServices.database.daos;

import core.database.DatabaseDriver;
import io.qameta.allure.Step;

import java.sql.Connection;
import java.sql.SQLException;

public class ConfigurationDAO {

    private final DatabaseDriver databaseDriver;

    public ConfigurationDAO() {
        this.databaseDriver = new DatabaseDriver();
    }

    @Step("Update configuration")
    public int updatetConfiguration(Connection dbConn, String variableName) throws SQLException {
        //Create DB Query to update configuration
        StringBuilder query = new StringBuilder();
        query.append("UPDATE tConfiguration SET variable_value = 'FALSE' Where variable_name ='" + variableName + "'");
        System.out.println(query.toString());
        // Execute query
        return databaseDriver.executeUpdate(dbConn, query.toString());
    }
}