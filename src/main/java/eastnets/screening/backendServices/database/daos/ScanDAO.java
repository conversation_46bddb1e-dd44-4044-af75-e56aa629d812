package eastnets.screening.backendServices.database.daos;

import com.opencsv.exceptions.CsvValidationException;
import core.database.DatabaseDriver;
import core.util.CSVHandler;
import org.apache.commons.lang.StringUtils;

import java.io.IOException;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

public class ScanDAO {

    private final DatabaseDriver databaseDriver;
    private final CSVHandler csvHandler;

    public ScanDAO() {
        this.databaseDriver = new DatabaseDriver();
        this.csvHandler = new CSVHandler();
    }

    public void drop_db_scan_table(Connection conn) throws SQLException {

        StringBuilder query = new StringBuilder();
        if (conn.getMetaData().getDriverName().contains("SQL Server"))
            query.append("DROP TABLE IF EXISTS DBScan");
        else
            query.append("BEGIN\n" +
                    "   EXECUTE IMMEDIATE 'DROP TABLE DBScan';\n" +
                    "EXCEPTION\n" +
                    "   WHEN OTHERS THEN\n" +
                    "      IF SQLCODE != -942 THEN\n" +
                    "         RAISE;\n" +
                    "      END IF;\n" +
                    "END;");
        databaseDriver.executeUpdate(conn, query.toString());

    }

    public int CreateDBScanTable(Connection dbConn) throws SQLException {
        //Create DB Query to Create DBScan Table
        drop_db_scan_table(dbConn);
        StringBuilder query = new StringBuilder();
        if (dbConn.getMetaData().getDriverName().contains("SQL Server"))
            query.append("IF OBJECT_ID('DBScan') IS NULL BEGIN  CREATE TABLE DBScan (ID int NOT NULL IDENTITY PRIMARY KEY ,FirstName varchar(50),LastName varchar(50),BirthYear varchar(50)) END");
        else
            query.append(" BEGIN\n" +
                    "   EXECUTE IMMEDIATE 'CREATE TABLE DBScan (ID int NOT NULL PRIMARY KEY ,FirstName varchar(50),LastName varchar(50),BirthYear varchar(50))';\n" +
                    "EXCEPTION\n" +
                    "   WHEN OTHERS THEN\n" +
                    "      IF SQLCODE = -955 THEN\n" +
                    "         NULL; -- suppresses the error if the table already exists\n" +
                    "      ELSE\n" +
                    "         RAISE;\n" +
                    "      END IF;\n" +
                    "END;");
        return databaseDriver.executeUpdate(dbConn, query.toString());
    }


    public int insertDataInDBScanTable(Connection dbConn, String filePath) throws SQLException, IOException, CsvValidationException {
        //Create DB Query to insert Data In DBScan Table
        filePath = System.getProperty("user.dir") + filePath;
        List<List<String>> data = csvHandler.readFromFile(filePath);
        StringBuilder query = new StringBuilder();
        // query.append("IF OBJECT_ID('DBScan') IS NOT NULL BEGIN  DELETE FROM DBScan   END");
        // DatabaseDriver.executeUpdate(dbConn, query.toString());
        if (dbConn.getMetaData().getDriverName().contains("SQL Server"))
            for (int i = 1; i < data.size(); i++) {
                query = new StringBuilder();
                query.append("INSERT INTO DBScan (" + StringUtils.join(data.get(0), ',') + ") values (" + StringUtils.join(data.get(i), ',') + ")");
                databaseDriver.executeUpdate(dbConn, query.toString());
            }
        else {
            query.append("SELECT COALESCE(MAX(id), 0) + 1 AS next_id FROM DBScan");
            ResultSet resultSet = databaseDriver.executeQueryAndGetRS(dbConn, query.toString());
            resultSet.next();
            int next_id = resultSet.getInt("next_id");
            for (int i = 1; i < data.size(); i++) {
                String insertSQL = "INSERT INTO DBScan (ID, " + StringUtils.join(data.get(0), ',') + ") values (?, ?, ?, ?)";
                PreparedStatement preparedStatement = dbConn.prepareStatement(insertSQL);
                preparedStatement.setInt(1, next_id);
                preparedStatement.setString(2, data.get(i).get(0));
                preparedStatement.setString(3, data.get(i).get(1));
                preparedStatement.setString(4, data.get(i).get(2));
                System.out.println(preparedStatement);
                int rowsInserted = preparedStatement.executeUpdate();
                if (rowsInserted > 0) {
                    continue;
                }
                preparedStatement.close();
                next_id++;
            }

        }
        return 0;
    }
}
