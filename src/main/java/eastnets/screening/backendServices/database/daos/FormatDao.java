package eastnets.screening.backendServices.database.daos;

import core.database.DatabaseDriver;

import java.sql.Connection;
import java.sql.SQLException;

public class FormatDao {

    private final DatabaseDriver databaseDriver;

    public FormatDao() {
        this.databaseDriver = new DatabaseDriver();
    }

    public int cleanFormatTable(Connection dbConn, String zone) throws SQLException {
        //Create DB Query to delete profile from listSet
        StringBuilder query = new StringBuilder();
        query.append("DELETE FROM tRecordFormatFields  where tRecordFormatFields.format_id  in( Select trf.id from tRecordFormats trf Left join tZones tz on tz.ID = trf.zone_id where tz.DISPLAY_NAME = '" + zone + "')");

        databaseDriver.executeUpdate(dbConn, query.toString());
        query = new StringBuilder();
        query.append("DELETE FROM tRecordFormats where tRecordFormats.id in( Select trf.id from tRecordFormats trf Left join tZones tz on tz.ID = trf.zone_id where tz.DISPLAY_NAME = '" + zone + "')");
        // Execute query
        return databaseDriver.executeUpdate(dbConn, query.toString());
    }
}
