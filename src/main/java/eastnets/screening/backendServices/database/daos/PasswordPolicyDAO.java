package eastnets.screening.backendServices.database.daos;

import core.database.DatabaseDriver;

import java.sql.Connection;
import java.sql.SQLException;

public class PasswordPolicyDAO {

    private final DatabaseDriver databaseDriver;

    public PasswordPolicyDAO() {
        this.databaseDriver = new DatabaseDriver();
    }

    public int resetPasswordPolicyData(Connection dbConn) throws SQLException {
        //Create DB Query to Create DBScan Table
        StringBuilder query = new StringBuilder();
        query.append("UPDATE tPasswordPolicy SET  minimum_length = 6 , history_count =3 , min_upper_chars = 0, min_lower_chars = 0 ,min_numerics = 0 , min_special_chars = 0 ,enabled = 1 WHERE id = 1");
        return databaseDriver.executeUpdate(dbConn, query.toString());
    }

    public int resetOperatorPasswordByManager(Connection dbConn, String operatorName) throws SQLException {
        //Create DB Query to Create DBScan Table
        StringBuilder query = new StringBuilder();
        query.append("UPDATE tOperators  set PASSWORD ='329dwJn6m0N2isHkrjdfwtWFEeWqC+QAWg6/EDo+N8HG7AY=' , LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='" + operatorName + "'");
        return databaseDriver.executeUpdate(dbConn, query.toString());
    }

    public int resetOperatorPassword(Connection dbConn, String operatorName) throws SQLException {
        //Create DB Query to Create DBScan Table
        StringBuilder query = new StringBuilder();
        query.append("UPDATE tOperators  set PASSWORD ='9b7es5QR8BSqMxm4c4qMGhDdf9QJ0xRJ+sM2UWkOGVjluWY=' , LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='" + operatorName + "'");
        return databaseDriver.executeUpdate(dbConn, query.toString());
    }

    public int unlockOperator(Connection dbConn, String operatorName) throws SQLException {
        //Create DB Query to Create DBScan Table
        StringBuilder query = new StringBuilder();
        query.append("UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='" + operatorName + "'");
        return databaseDriver.executeUpdate(dbConn, query.toString());
    }

}
