package eastnets.screening.backendServices.database.daos;

import core.database.DatabaseDriver;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;

public class ListSetDAO {

    private final DatabaseDriver databaseDriver;

    public ListSetDAO() {
        this.databaseDriver = new DatabaseDriver();
    }

    public int deleteProfileFromListSet(Connection dbConn, int profileID) throws SQLException {
        //Create DB Query to delete profile from listSet
        StringBuilder query = new StringBuilder();
        query.append("Delete From tListSetProfile where profile_id in (" + profileID + ")");
        // Execute query
        return databaseDriver.executeUpdate(dbConn, query.toString());
    }

    public boolean checkIfListSetLinkedToProfile(Connection dbConn, String profileName) throws SQLException {
        //Create DB Query to delete profile from listSet
        StringBuilder query = new StringBuilder();
        query.append("SELECT ID FROM tProfiles Where NAME ='" + profileName + "'");
        // Execute query
        ResultSet rs = databaseDriver.executeQueryAndGetRS(dbConn, query.toString());

        if (rs.next()) {
            return true;
        }
        return false;
    }

    public int deleteLinkBtwBlackListAndListSet(Connection dbConn, String zoneID, String BList) throws SQLException {
        //Create DB Query to delete profile from listSet
        StringBuilder query = new StringBuilder();
        query.append("DELETE " +
                " FROM tSetContent  " +
                " WHERE id IN (" +
                " SELECT tsc.id FROM tSetContent tsc " +
                " LEFT JOIN tBlackListVersions tblv on tblv.id =tsc.black_list_id " +
                " LEFT JOIN tListSets tls on tls.id = tsc.list_set_id " +
                " LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id " +
                " WHERE tbl.zone_id ='" + zoneID + "' " +
                " AND tbl.display_name ='" + BList + "')");
        // Execute query
        return databaseDriver.executeUpdate(dbConn, query.toString());

    }

    public int deleteLinkBtwBlackListAndGoodGuy(Connection dbConn, String zone, String bList) throws SQLException {
        //Create DB Query to delete profile from listSet
        StringBuilder query = new StringBuilder();
        query.append("DELETE  FROM tGoodGuys " +
                " WHERE id IN (" +
                " SELECT tgg.id " +
                " FROM tGoodGuys tgg" +
                " LEFT JOIN tBlackListVersions tblv on tblv.id =tgg.black_list_id" +
                " LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id" +
                " WHERE tbl.display_name ='" + bList + "')");
        // Execute query
        return databaseDriver.executeUpdate(dbConn, query.toString());

    }

    public int deleteBlackList(Connection dbConn, String BList) throws SQLException {
        //Create DB Query to delete profile from listSet
        StringBuilder query = new StringBuilder();
        query.append("DELETE FROM tBlackLists WHERE display_name ='" + BList + "'");
        // Execute query
        return databaseDriver.executeUpdate(dbConn, query.toString());

    }

}
