package eastnets.screening.backendServices;

import com.opencsv.exceptions.CsvValidationException;
import core.util.Log;
import eastnets.screening.backendServices.database.services.*;
import io.qameta.allure.Step;

import java.io.IOException;
import java.sql.SQLException;
import java.util.List;

public class ScreeningServicesDelegate {

    private final Log log;

    public ScreeningServicesDelegate() {
        this.log = new Log();
        log.info(" *******   Create an instance of ServiceDelegate to access backend DB    ******** ");
    }

    public int deleteProfileFromListSet(String profileName) throws IOException, SQLException {
        ListSetService listSetService = new ListSetService();
        return listSetService.deleteProfileFromListSet(profileName);

    }

    public boolean checkIfListSetLinkedToProfile(String profileName) throws IOException, SQLException {
        ListSetService listSetService = new ListSetService();
        return listSetService.checkIfListSetLinkedToProfile(profileName);
    }

    public int CreateDBScanTable() throws SQLException {
        ScanService scanService = new ScanService();
        return scanService.CreateDBScanTable();
    }

    public int insertDataInDBScanTable(String filePath) throws SQLException, IOException, CsvValidationException {
        ScanService scanService = new ScanService();
        return scanService.insertDataInDBScanTable(filePath);
    }

    public String getEntityNameFromSwf(String listName) throws IOException, SQLException {
        EntityService entityService = new EntityService();
        String entityName = entityService.getEntityNameFromList(listName);
        return entityName;
    }

    public int resetPasswordPolicyData() throws SQLException {
        PasswordPolicyService passwordPolicyService = new PasswordPolicyService();
        return passwordPolicyService.resetPasswordPolicyData();
    }

    public int resetOperatorPasswordByManager(String operatorName) throws SQLException {
        PasswordPolicyService passwordPolicyService = new PasswordPolicyService();
        return passwordPolicyService.resetOperatorPasswordByManager(operatorName);
    }

    public int resetOperatorPassword(String operatorName) throws SQLException {
        PasswordPolicyService passwordPolicyService = new PasswordPolicyService();
        return passwordPolicyService.resetOperatorPassword(operatorName);
    }

    public int unlockOperator(String operatorName) throws SQLException {
        PasswordPolicyService passwordPolicyService = new PasswordPolicyService();
        return passwordPolicyService.unlockOperator(operatorName);
    }

    public int deleteLinkBtwBlackListAndListSet(String zone, String bList) throws SQLException, IOException {
        ListSetService listSetService = new ListSetService();
        return listSetService.deleteLinkBtwBlackListAndListSet(zone, bList);
    }

    public int deleteLinkBtwBlackListAndGoodGuy(String zone, String bList) throws SQLException {
        ListSetService listSetService = new ListSetService();
        return listSetService.deleteLinkBtwBlackListAndGoodGuy(zone, bList);
    }

    public int deleteBlackList(String bList) throws SQLException {
        ListSetService listSetService = new ListSetService();
        return listSetService.deleteBlackList(bList);
    }

    public int excuteQueryAdmin(String queryString) throws SQLException, IOException {
        BicsService bicsService = new BicsService();
        return bicsService.excuteQueryAdmin(queryString);
    }

    public int excuteQuerSFP(String queryString) throws SQLException, IOException {
        BicsService bicsService = new BicsService();
        return bicsService.excuteQuerySFP(queryString);
    }

    public int deleteBics() throws SQLException {
        BicsService bicsService = new BicsService();
        return bicsService.deleteBics();
    }

    @Step("Update configuration")
    public int updateConfiguration(String variableName) throws SQLException, IOException {
        ConfigurationService configurationService = new ConfigurationService();
        return configurationService.updatetConfiguration(variableName);
    }


    public String getEntitiesCount(String listName) throws SQLException {
        EntityService entityService = new EntityService();
        return entityService.getEntitiesCount(listName);
    }

    public int cleanFormatTable(String zone) throws SQLException {
        FormatService formatService = new FormatService();
        return formatService.cleanFormatTable(zone);
    }


    public List<String> get_db_flows_list(String zone_name, String flow_type) throws SQLException {
        DBManagerService db_manager_service = new DBManagerService();
        return db_manager_service.get_db_flows_list(zone_name, flow_type);
    }
}