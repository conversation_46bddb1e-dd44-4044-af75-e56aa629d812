package eastnets.screening.entity;

import java.util.List;

public class EnList {

    private String testCaseTitle;
    private ListSet listSet;
    private String zoneName;
    private String privateFlag;
    private String name;
    private String officialDate;
    private List<ListEntry> entry;
    private String expectedMessage;

    public ListSet getListSet() {
        return listSet;
    }

    public void setListSet(ListSet listSet) {
        this.listSet = listSet;
    }

    public String getZoneName() {
        return zoneName;
    }

    public void setZoneName(String zoneName) {
        this.zoneName = zoneName;
    }

    public String getPrivateFlag() {
        return privateFlag;
    }

    public void setPrivateFlag(String privateFlag) {
        this.privateFlag = privateFlag;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getOfficialDate() {
        return officialDate;
    }

    public void setOfficialDate(String officialDate) {
        this.officialDate = officialDate;
    }

    public List<ListEntry> getEntry() {
        return entry;
    }

    public void setEntry(List<ListEntry> entry) {
        this.entry = entry;
    }

    public String getExpectedMessage() {
        return expectedMessage;
    }

    public void setExpectedMessage(String expectedMessage) {
        this.expectedMessage = expectedMessage;
    }

    public String getTestCaseTitle() {
        return testCaseTitle;
    }

    public void setTestCaseTitle(String testCaseTitle) {
        this.testCaseTitle = testCaseTitle;
    }


    @Override
    public String toString() {
        return "EnList{" +
                "testCaseTitle='" + testCaseTitle + '\'' +
                ", listSet=" + listSet +
                ", zoneName='" + zoneName + '\'' +
                ", privateFlag='" + privateFlag + '\'' +
                ", name='" + name + '\'' +
                ", officialDate='" + officialDate + '\'' +
                ", entry=" + entry +
                ", expectedMessage='" + expectedMessage + '\'' +
                '}';
    }
}
