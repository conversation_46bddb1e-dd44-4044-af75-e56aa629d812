package eastnets.screening.entity;


public class Violation {

    private String status;
    private String matchedEntity;
    private String violationFilter;


    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getMatchedEntity() {
        return matchedEntity;
    }

    public void setMatchedEntity(String matchedEntity) {
        this.matchedEntity = matchedEntity;
    }

    public String getViolationFilter() {
        return violationFilter;
    }

    public void setViolationFilter(String violationFilter) {
        this.violationFilter = violationFilter;
    }
}
