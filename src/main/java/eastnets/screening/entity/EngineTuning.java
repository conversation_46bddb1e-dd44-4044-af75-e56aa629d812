package eastnets.screening.entity;

public class EngineTuning {

    private String testCaseTitle;
    private String scriptName;
    private String showSettings;
    private String zone;
    private String word;
    private String synonym;
    private String category;
    private String scannedName;
    private String[] scannedNameList;
    private String[] detectionStatusList;
    private String detectionStatus;
    private String rank;

    //Engine Settings
    private  String cacheThreshold;
    private  String cacheExpiration;
    private  String ignoreMatches;
    private  boolean ignoreFlag;
    private  String detectSwiftBic;
    private  boolean enableMatchOnNumber;
    private  boolean ignoreExtraWords;
    private  boolean enhanceMatchRankLastName;
    private  boolean lowerMatchRank;
    private  boolean enhanceMatchRankFirstName;
    private  boolean disableScanOneWordIndividualAKA;
    private  boolean enableShortWords;
    private  boolean individualNamePrefixNeutral;
    private  boolean enableIslamicNames;
    private  boolean enableDoubleLettersEnhancement;

    public  EngineTuning setAllToTrueOrFalse( String showSettings, String zone, boolean flag) {

        EngineTuning engineTuning = new EngineTuning();

        engineTuning.showSettings = showSettings;
        engineTuning.zone = zone;
        engineTuning.ignoreFlag = flag;
        engineTuning.enableMatchOnNumber = flag;
        engineTuning.ignoreExtraWords = flag;
        engineTuning.enhanceMatchRankLastName = flag;
        engineTuning.lowerMatchRank = flag;
        engineTuning.enhanceMatchRankFirstName = flag;
        engineTuning.disableScanOneWordIndividualAKA = flag;
        engineTuning.enableShortWords = flag;
        engineTuning.individualNamePrefixNeutral = flag;
        engineTuning.enableIslamicNames = flag;
        engineTuning.enableDoubleLettersEnhancement = flag;

        return engineTuning;
    }

    public String getTestCaseTitle() {
        return testCaseTitle;
    }

    public void setTestCaseTitle(String testCaseTitle) {
        this.testCaseTitle = testCaseTitle;
    }

    public String getScriptName() {
        return scriptName;
    }

    public void setScriptName(String scriptName) {
        this.scriptName = scriptName;
    }

    public String getShowSettings() {
        return showSettings;
    }

    public void setShowSettings(String showSettings) {
        this.showSettings = showSettings;
    }

    public String getZone() {
        return zone;
    }

    public void setZone(String zone) {
        this.zone = zone;
    }

    public String getWord() {
        return word;
    }

    public void setWord(String word) {
        this.word = word;
    }

    public String getSynonym() {
        return synonym;
    }

    public void setSynonym(String synonym) {
        this.synonym = synonym;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getScannedName() {
        return scannedName;
    }

    public void setScannedName(String scannedName) {
        this.scannedName = scannedName;
    }

    public String[] getScannedNameList() {
        return scannedNameList;
    }

    public void setScannedNameList(String[] scannedNameList) {
        this.scannedNameList = scannedNameList;
    }

    public String getDetectionStatus() {
        return detectionStatus;
    }

    public void setDetectionStatus(String detectionStatus) {
        this.detectionStatus = detectionStatus;
    }

    public String getRank() {
        return rank;
    }

    public void setRank(String rank) {
        this.rank = rank;
    }

    //Engine Settings

    public String getCacheThreshold() {
        return cacheThreshold;
    }

    public void setCacheThreshold(String cacheThreshold) {
        this.cacheThreshold = cacheThreshold;
    }

    public String getCacheExpiration() {
        return cacheExpiration;
    }

    public void setCacheExpiration(String cacheExpiration) {
        this.cacheExpiration = cacheExpiration;
    }

    public String getIgnoreMatches() {
        return ignoreMatches;
    }

    public void setIgnoreMatches(String ignoreMatches) {
        this.ignoreMatches = ignoreMatches;
    }

    public boolean isIgnoreFlag() {
        return ignoreFlag;
    }

    public void setIgnoreFlag(boolean ignoreFlag) {
        this.ignoreFlag = ignoreFlag;
    }

    public String getDetectSwiftBic() {
        return detectSwiftBic;
    }

    public void setDetectSwiftBic(String detectSwiftBic) {
        this.detectSwiftBic = detectSwiftBic;
    }

    public boolean isEnableMatchOnNumber() {
        return enableMatchOnNumber;
    }

    public void setEnableMatchOnNumber(boolean enableMatchOnNumber) {
        this.enableMatchOnNumber = enableMatchOnNumber;
    }

    public boolean isIgnoreExtraWords() {
        return ignoreExtraWords;
    }

    public void setIgnoreExtraWords(boolean ignoreExtraWords) {
        this.ignoreExtraWords = ignoreExtraWords;
    }

    public boolean isEnhanceMatchRankLastName() {
        return enhanceMatchRankLastName;
    }

    public void setEnhanceMatchRankLastName(boolean enhanceMatchRankLastName) {
        this.enhanceMatchRankLastName = enhanceMatchRankLastName;
    }

    public boolean isLowerMatchRank() {
        return lowerMatchRank;
    }

    public void setLowerMatchRank(boolean lowerMatchRank) {
        this.lowerMatchRank = lowerMatchRank;
    }

    public boolean isEnhanceMatchRankFirstName() {
        return enhanceMatchRankFirstName;
    }

    public void setEnhanceMatchRankFirstName(boolean enhanceMatchRankFirstName) {
        this.enhanceMatchRankFirstName = enhanceMatchRankFirstName;
    }

    public boolean isDisableScanOneWordIndividualAKA() {
        return disableScanOneWordIndividualAKA;
    }

    public void setDisableScanOneWordIndividualAKA(boolean disableScanOneWordIndividualAKA) {
        this.disableScanOneWordIndividualAKA = disableScanOneWordIndividualAKA;
    }

    public boolean isEnableShortWords() {
        return enableShortWords;
    }

    public void setEnableShortWords(boolean enableShortWords) {
        this.enableShortWords = enableShortWords;
    }

    public boolean isIndividualNamePrefixNeutral() {
        return individualNamePrefixNeutral;
    }

    public void setIndividualNamePrefixNeutral(boolean individualNamePrefixNeutral) {
        this.individualNamePrefixNeutral = individualNamePrefixNeutral;
    }

    public boolean isEnableIslamicNames() {
        return enableIslamicNames;
    }

    public void setEnableIslamicNames(boolean enableIslamicNames) {
        this.enableIslamicNames = enableIslamicNames;
    }

    public boolean isEnableDoubleLettersEnhancement(){
        return this.enableDoubleLettersEnhancement;
    }
    public void setEnableDoubleLettersEnhancement (boolean enableDoubleLettersEnhancement){
        this.enableDoubleLettersEnhancement = enableDoubleLettersEnhancement;
    }
    public String[] getDetectionStatusList() {
        return detectionStatusList;
    }

    public void setDetectionStatusList(String[] detectionStatusList) {
        this.detectionStatusList = detectionStatusList;
    }
}
