package eastnets.screening.entity;

public class FileScan extends Main {


    private String scriptName;
    private String filePath;
    private String format;
    private String encoding;
    private EnList enList;
    private String result;
    private String rank;
    private boolean detectVessels;
    private boolean detectCountries;
    private boolean createAlertsAutomatically;
    private String takeAction;
    private String expectedResults;
    private String entryName;

    public String getAssignee() {
        return assignee;
    }

    public void setAssignee(String assignee) {
        this.assignee = assignee;
    }

    private String assignee;

    public String getScriptName() {
        return scriptName;
    }

    public void setScriptName(String scriptName) {
        this.scriptName = scriptName;
    }

    public boolean isDetectVessels() {
        return detectVessels;
    }

    public boolean isDetectCountries() {
        return detectCountries;
    }

    public boolean isCreateAlertsAutomatically() {
        return createAlertsAutomatically;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getFormat() {
        return format;
    }

    public void setFormat(String format) {
        this.format = format;
    }

    public String getEncoding() {
        return encoding;
    }

    public void setEncoding(String encoding) {
        this.encoding = encoding;
    }

    public EnList getEnList() {
        return enList;
    }

    public void setEnList(EnList enList) {
        this.enList = enList;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public String getRank() {
        return rank;
    }

    public void setRank(String rank) {
        this.rank = rank;
    }

    public boolean getDetectVessels() {
        return detectVessels;
    }

    public void setDetectVessels(boolean detectVessels) {
        this.detectVessels = detectVessels;
    }

    public boolean getDetectCountries() {
        return detectCountries;
    }

    public void setDetectCountries(boolean detectCountries) {
        this.detectCountries = detectCountries;
    }

    public boolean getCreateAlertsAutomatically() {
        return createAlertsAutomatically;
    }

    public void setCreateAlertsAutomatically(boolean createAlertsAutomatically) {
        this.createAlertsAutomatically = createAlertsAutomatically;
    }

    public String getTakeAction() {
        return takeAction;
    }

    public void setTakeAction(String takeAction) {
        this.takeAction = takeAction;
    }

    public String getExpectedResults() {
        return expectedResults;
    }

    public void setExpectedResults(String expectedResults) {
        this.expectedResults = expectedResults;
    }

    public String getEntryName() {
        return entryName;
    }

    public void setEntryName(String entryName) {
        this.entryName = entryName;
    }

    @Override
    public String toString() {
        return "FileScan{" +
                "filePath='" + filePath + '\'' +
                ", format='" + format + '\'' +
                ", encoding='" + encoding + '\'' +
                ", enList=" + enList +
                ", result='" + result + '\'' +
                ", rank='" + rank + '\'' +
                ", detectVessels='" + detectVessels + '\'' +
                ", detectCountries='" + detectCountries + '\'' +
                ", createAlertsAutomatically='" + createAlertsAutomatically + '\'' +
                '}';
    }
}
