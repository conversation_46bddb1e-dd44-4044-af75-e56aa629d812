package eastnets.screening.entity;

public class ISO20022FormatConfiguration {

    private String zone;
    private String groupName;
    protected ISO20022SchemaConfiguration iso20022SchemaConfiguration;
    private String expectedResults;

    public String getZone() {
        return zone;
    }

    public void setZone(String zone) {
        this.zone = zone;
    }

    public String getGroupName() {
        return groupName;
    }

    public void setGroupName(String groupName) {
        this.groupName = groupName;
    }

    public ISO20022SchemaConfiguration getIso20022SchemaConfiguration() {
        return iso20022SchemaConfiguration;
    }

    public void setIso20022SchemaConfiguration(ISO20022SchemaConfiguration iso20022SchemaConfiguration) {
        this.iso20022SchemaConfiguration = iso20022SchemaConfiguration;
    }

    public String getExpectedResults() {
        return expectedResults;
    }

    public void setExpectedResults(String expectedResults) {
        this.expectedResults = expectedResults;
    }

    @Override
    public String toString() {
        return "ISO20022FormatConfiguration{" +
                "zone='" + zone + '\'' +
                ", groupName='" + groupName + '\'' +
                ", iso20022SchemaConfiguration=" + iso20022SchemaConfiguration +
                ", expectedResults='" + expectedResults + '\'' +
                '}';
    }
}