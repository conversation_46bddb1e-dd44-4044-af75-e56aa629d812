package eastnets.screening.entity;

public class FourEyes {
    private String TestCaseTitle;
    private String name;
    private String zone;
    private String listSet;
    private String rank;
    private String amount;
    private String country;
    private String application;
    private String contextFilter;
    private String scannedName;
    private String status;

    public String getTestCaseTitle() {
        return TestCaseTitle;
    }

    public void setTestCaseTitle(String testCaseTitle) {
        TestCaseTitle = testCaseTitle;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getZone() {
        return zone;
    }

    public void setZone(String zone) {
        this.zone = zone;
    }

    public String getListSet() {
        return listSet;
    }

    public void setListSet(String listSet) {
        this.listSet = listSet;
    }

    public String getRank() {
        return rank;
    }

    public void setRank(String rank) {
        this.rank = rank;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getCountry() {
        return country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getApplication() {
        return application;
    }

    public void setApplication(String application) {
        this.application = application;
    }

    public String getContextFilter() {
        return contextFilter;
    }

    public void setContextFilter(String contextFilter) {
        this.contextFilter = contextFilter;
    }

    public String getScannedName() {
        return scannedName;
    }

    public void setScannedName(String scannedName) {
        this.scannedName = scannedName;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Override
    public String toString() {
        return "FourEyes{" +
                "TestCaseTitle='" + TestCaseTitle + '\'' +
                ", name='" + name + '\'' +
                ", zone='" + zone + '\'' +
                ", listSet='" + listSet + '\'' +
                ", rank='" + rank + '\'' +
                ", amount='" + amount + '\'' +
                ", country='" + country + '\'' +
                ", application='" + application + '\'' +
                ", contextFilter='" + contextFilter + '\'' +
                ", scannedName='" + scannedName + '\'' +
                ", status='" + status + '\'' +
                '}';
    }
}
