package eastnets.screening.entity;

public class DB<PERSON>ield {
    private String name;
    private String type;
    private boolean scanFlag;
    private boolean primaryKeyFlag;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public boolean isScanFlag() {

        return scanFlag;
    }

    public void setScanFlag(boolean scanFlag) {
        this.scanFlag = scanFlag;
    }

    public boolean isPrimaryKeyFlag() {
        return primaryKeyFlag;
    }

    public void setPrimaryKeyFlag(boolean primaryKeyFlag) {
        this.primaryKeyFlag = primaryKeyFlag;
    }
}
