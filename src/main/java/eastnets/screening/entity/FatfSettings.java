package eastnets.screening.entity;

import io.qameta.allure.internal.shadowed.jackson.annotation.JsonIgnoreProperties;
import saa.entity.Main;

import java.util.ArrayList;

@JsonIgnoreProperties(ignoreUnknown = true)
public class FatfSettings extends Main {

    private String fieldMinimumLineSize;
    private String rank;
    private String europeanCodes;
    private String enableCheckOfUniqueTransactionReferenceNumber;
    private String informationAvailableWithinTransaction;
    private String informationAvailableOutsideTheTransaction;
    private String informationAvailableForBics;
    private String applyForCrossBorderTraffic;
    private ArrayList<Currency> currencies;
    private ArrayList<Zone> zones;
    private String encoding;

    public String getFormat() {
        return format;
    }

    public void setFormat(String format) {
        this.format = format;
    }

    private String format;
    private boolean detectVessels;
    private boolean detectCountries;

    public String getResults() {
        return results;
    }

    public void setResults(String results) {
        this.results = results;
    }

    private String results;

    public String getEncoding() {
        return encoding;
    }

    public void setEncoding(String encoding) {
        this.encoding = encoding;
    }

    public boolean isDetectVessels() {
        return detectVessels;
    }

    public void setDetectVessels(boolean detectVessels) {
        this.detectVessels = detectVessels;
    }

    public boolean isDetectCountries() {
        return detectCountries;
    }

    public void setDetectCountries(boolean detectCountries) {
        this.detectCountries = detectCountries;
    }

    public boolean isCreateAlertsAutomatically() {
        return createAlertsAutomatically;
    }

    public void setCreateAlertsAutomatically(boolean createAlertsAutomatically) {
        this.createAlertsAutomatically = createAlertsAutomatically;
    }

    private boolean createAlertsAutomatically;

    public ListSet getListSet() {
        return listSet;
    }

    public void setListSet(ListSet listSet) {
        this.listSet = listSet;
    }

    private ListSet listSet;

    public String getBics() {
        return bics;
    }

    public void setBics(String bics) {
        this.bics = bics;
    }

    private String bics;


    public String getFieldMinimumLineSize() {
        return fieldMinimumLineSize;
    }

    public void setFieldMinimumLineSize(String fieldMinimumLineSize) {
        this.fieldMinimumLineSize = fieldMinimumLineSize;
    }

    public String getRank() {
        return rank;
    }

    public void setRank(String rank) {
        this.rank = rank;
    }

    public String getEuropeanCodes() {
        return europeanCodes;
    }

    public void setEuropeanCodes(String europeanCodes) {
        this.europeanCodes = europeanCodes;
    }

    public String getEnableCheckOfUniqueTransactionReferenceNumber() {
        return enableCheckOfUniqueTransactionReferenceNumber;
    }

    public void setEnableCheckOfUniqueTransactionReferenceNumber(String enableCheckOfUniqueTransactionReferenceNumber) {
        this.enableCheckOfUniqueTransactionReferenceNumber = enableCheckOfUniqueTransactionReferenceNumber;
    }

    public String getInformationAvailableWithinTransaction() {
        return informationAvailableWithinTransaction;
    }

    public void setInformationAvailableWithinTransaction(String informationAvailableWithinTransaction) {
        this.informationAvailableWithinTransaction = informationAvailableWithinTransaction;
    }

    public String getInformationAvailableOutsideTheTransaction() {
        return informationAvailableOutsideTheTransaction;
    }

    public void setInformationAvailableOutsideTheTransaction(String informationAvailableOutsideTheTransaction) {
        this.informationAvailableOutsideTheTransaction = informationAvailableOutsideTheTransaction;
    }

    public String getInformationAvailableForBics() {
        return informationAvailableForBics;
    }

    public void setInformationAvailableForBics(String informationAvailableForBics) {
        this.informationAvailableForBics = informationAvailableForBics;
    }

    public String getApplyForCrossBorderTraffic() {
        return applyForCrossBorderTraffic;
    }

    public void setApplyForCrossBorderTraffic(String applyForCrossBorderTraffic) {
        this.applyForCrossBorderTraffic = applyForCrossBorderTraffic;
    }

    public ArrayList<Currency> getCurrencies() {
        return currencies;
    }

    public void setCurrencies(ArrayList<Currency> currencies) {
        this.currencies = currencies;
    }

    public ArrayList<Zone> getZones() {
        return zones;
    }

    public void setZones(ArrayList<Zone> zones) {
        this.zones = zones;
    }

    public ArrayList<BlackList> getBlackLists() {
        return blackLists;
    }

    public void setBlackLists(ArrayList<BlackList> blackLists) {
        this.blackLists = blackLists;
    }

    private ArrayList<BlackList> blackLists;

    public String getEnableFatfRecommendation() {
        return enableFatfRecommendation;
    }

    public void setEnableFatfRecommendation(String enableFatfRecommendation) {
        this.enableFatfRecommendation = enableFatfRecommendation;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    private String fileName;
    private String enableFatfRecommendation;


}
