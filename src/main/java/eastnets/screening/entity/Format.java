package eastnets.screening.entity;

import java.util.List;

public class Format {
    private String testCaseTitle;
    private String name;
    private String zone;
    private String type;
    private String recordDelimiter;
    private String fieldDelimiter;
    private String separator;
    private String entryType;
    private String xpath;
    private List<FormatField> fields;

    public Format() {
        super();

    }

    public Format(String name, String type, String fieldDelimiter, String separator, String entryType) {
        this.name = name;
        this.type = type;
        this.fieldDelimiter = fieldDelimiter;
        this.separator = separator;
        this.entryType = entryType;
    }

    public Format(String name, String type, String recordDelimiter, String xpath) {
        this.name = name;
        this.type = type;
        this.recordDelimiter = recordDelimiter;
        this.xpath = xpath;
    }

    public String getTestCaseTitle() {
        return testCaseTitle;
    }

    public void setTestCaseTitle(String testCaseTitle) {
        this.testCaseTitle = testCaseTitle;
    }

    public String getZone() {
        return zone;
    }

    public void setZone(String zone) {
        this.zone = zone;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getFieldDelimiter() {
        return fieldDelimiter;
    }

    public void setFieldDelimiter(String fieldDelimiter) {
        this.fieldDelimiter = fieldDelimiter;
    }

    public String getSeparator() {
        return separator;
    }

    public void setSeparator(String separator) {
        this.separator = separator;
    }

    public String getEntryType() {
        return entryType;
    }

    public void setEntryType(String entryType) {
        this.entryType = entryType;
    }

    public List<FormatField> getFields() {
        return fields;
    }

    public void setFields(List<FormatField> fields) {
        this.fields = fields;
    }

    public String getXpath() {
        return xpath;
    }

    public void setXpath(String xpath) {
        this.xpath = xpath;
    }

    public String getRecordDelimiter() {
        return recordDelimiter;
    }

    public void setRecordDelimiter(String recordDelimiter) {
        this.recordDelimiter = recordDelimiter;
    }

    @Override
    public String toString() {
        return "Format{" +
                "testCaseTitle='" + testCaseTitle + '\'' +
                ", name='" + name + '\'' +
                ", zone='" + zone + '\'' +
                ", type='" + type + '\'' +
                ", fieldDelimiter='" + fieldDelimiter + '\'' +
                ", separator='" + separator + '\'' +
                ", entryType='" + entryType + '\'' +
                ", xpath='" + xpath + '\'' +
                ", fields=" + fields +
                '}';
    }
}
