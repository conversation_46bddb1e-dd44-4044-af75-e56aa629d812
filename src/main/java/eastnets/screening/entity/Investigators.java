package eastnets.screening.entity;

public class Investigators {
    private String name;
    private String hidden;
    private String type;
    private String roles;
    private boolean checkCheckers;
    private boolean checkMayActAsMaker;
    private boolean checkHidden;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getHidden() {
        return hidden;
    }

    public void setHidden(String hidden) {
        this.hidden = hidden;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRoles() {
        return roles;
    }

    public void setRoles(String roles) {
        this.roles = roles;
    }

    public boolean isCheckCheckers() {
        return checkCheckers;
    }

    public void setCheckCheckers(boolean checkCheckers) {
        this.checkCheckers = checkCheckers;
    }

    public boolean isCheckMayActAsMaker() {
        return checkMayActAsMaker;
    }

    public void setCheckMayActAsMaker(boolean checkMayActAsMaker) {
        this.checkMayActAsMaker = checkMayActAsMaker;
    }

    public boolean isCheckHidden() {
        return checkHidden;
    }

    public void setCheckHidden(boolean checkHidden) {
        this.checkHidden = checkHidden;
    }

    @Override
    public String toString() {
        return "Investigators{" +
                "name='" + name + '\'' +
                ", hidden='" + hidden + '\'' +
                ", type='" + type + '\'' +
                ", roles='" + roles + '\'' +
                ", checkCheckers=" + checkCheckers +
                ", checkMayActAsMaker=" + checkMayActAsMaker +
                ", checkHidden=" + checkHidden +
                '}';
    }
}
