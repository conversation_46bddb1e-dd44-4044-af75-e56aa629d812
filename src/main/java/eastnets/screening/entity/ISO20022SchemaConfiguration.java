package eastnets.screening.entity;

public class ISO20022SchemaConfiguration {
    private String schemaName;
    private String schemaVersion;
    private String headerSwift;
    private String expectedResults;
    private String wrapper;

    public String getSchemaName() {
        return schemaName;
    }

    public void setSchemaName(String schemaName) {
        this.schemaName = schemaName;
    }

    public String getSchemaVersion() {
        return schemaVersion;
    }

    public void setSchemaVersion(String schemaVersion) {
        this.schemaVersion = schemaVersion;
    }

    public String getSchemaWrapper() {
        return wrapper;
    }

    public void setWrapper(String wrapper) {
        this.wrapper = wrapper;
    }

    public String getExpectedResults() {
        return expectedResults;
    }

    public void setExpectedResults(String expectedResults) {
        this.expectedResults = expectedResults;
    }

    public String getHeaderSwift() {
        return headerSwift;
    }

    public void setHeaderSwift(String headerSwift) {
        this.headerSwift = headerSwift;
    }

    @Override
    public String toString() {
        return "ISO20022SchemaConfiguration{" +
                "schemaName='" + schemaName + '\'' +
                ", schemaVersion='" + schemaVersion + '\'' +
                ", headerSwift='" + headerSwift + '\'' +
                ", expectedResults='" + expectedResults + '\'' +
                '}';
    }
}
