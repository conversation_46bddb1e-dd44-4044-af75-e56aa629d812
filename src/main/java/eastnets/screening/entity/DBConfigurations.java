package eastnets.screening.entity;

import java.util.List;

public class DBConfigurations  {

    private String testCaseTitle;

    private String zoneName;
    private String listSetName;

    private String name;
    private String type;
    private String dbType;
    private String tableName;
    private String outputFlow;

    private String dbName;
    private String host;
    private String port;
    private String userName;
    private String password;
    private boolean monitoring;
    private String fieldName;
    private String operator;
    private String value;
    private String detectVessels;
    private String detectCountries;
    private String expectedMessage;
    private List<DBField> dbFields;

    public String getTestCaseTitle() {
        return testCaseTitle;
    }

    public void setTestCaseTitle(String testCaseTitle) {
        this.testCaseTitle = testCaseTitle;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    public String getZoneName() {
        return zoneName;
    }

    public void setZoneName(String zoneName) {
        this.zoneName = zoneName;
    }

    public String getListSetName() {
        return listSetName;
    }

    public void setListSetName(String listSetName) {
        this.listSetName = listSetName;
    }

    public String getDbName() {
        return dbName;
    }

    public void setDbName(String dbName) {
        this.dbName = dbName;
    }

    public String getDbType() {
        return dbType;
    }

    public void setDbType(String dbType) {
        this.dbType = dbType;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getOutputFlow() {
        return outputFlow;
    }

    public void setOutputFlow(String outputFlow) {
        this.outputFlow = outputFlow;
    }

    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public String getPort() {
        return port;
    }

    public void setPort(String port) {
        this.port = port;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public boolean isMonitoring() {
        return monitoring;
    }

    public void setMonitoring(boolean monitoring) {
        this.monitoring = monitoring;
    }

    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getDetectVessels() {
        return detectVessels;
    }

    public void setDetectVessels(String detectVessels) {
        this.detectVessels = detectVessels;
    }

    public String getDetectCountries() {
        return detectCountries;
    }

    public void setDetectCountries(String detectCountries) {
        this.detectCountries = detectCountries;
    }

    public String getExpectedMessage() {
        return expectedMessage;
    }

    public void setExpectedMessage(String ExpectedMessage) {
        this.expectedMessage = ExpectedMessage;
    }

    public List<DBField> getDbFields() {
        return dbFields;
    }

    public void setDbFields(List<DBField> dbFields) {
        this.dbFields = dbFields;
    }

    @Override
    public String toString() {
        return "DBScan{" +
                "zoneName='" + zoneName + '\'' +
                ", listSetName='" + listSetName + '\'' +
                ", dbName='" + dbName + '\'' +
                ", flowName='" + name + '\'' +
                ", flowType='" + type + '\'' +
                ", outputFlow='" + outputFlow + '\'' +
                ", dbType='" + dbType + '\'' +
                ", host='" + host + '\'' +
                ", port='" + port + '\'' +
                ", userName='" + userName + '\'' +
                ", password='" + password + '\'' +
                ", monitoring='" + monitoring + '\'' +
                ", fieldName='" + fieldName + '\'' +
                ", operator='" + operator + '\'' +
                ", value='" + value + '\'' +
                ", detectVessels='" + detectVessels + '\'' +
                ", detectCountries='" + detectCountries + '\'' +
                ", ExpectedMessage='" + expectedMessage + '\'' +
                '}';
    }
}
