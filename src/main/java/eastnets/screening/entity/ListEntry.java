package eastnets.screening.entity;

import java.util.List;

public class ListEntry {

    private String type;
    private String name;
    private String firstName;
    private String title;
    private List<AKA> akas;
    private List<EntryAddress> address;
    private String programs;
    private String lastOccupation;
    private String birthDate;
    private String birthCountry;
    private String residenceCountry;
    private String nationality;
    private String externalId;
    private String internalId;
    private String gender;
    private String deceased;
    private String remarks;
    private String dataSources;
    private String relatedTo;

    private String Gender;

    private String country;

    public String getCountry() {
        return country;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public List<AKA> getAkas() {
        return akas;
    }

    public void setAkas(List<AKA> akas) {
        this.akas = akas;
    }

    public List<EntryAddress> getAddress() {
        return address;
    }

    public void setAddress(List<EntryAddress> address) {
        this.address = address;
    }

    public String getPrograms() {
        return programs;
    }

    public void setPrograms(String programs) {
        this.programs = programs;
    }

    public String getLastOccupation() {
        return lastOccupation;
    }

    public void setLastOccupation(String lastOccupation) {
        this.lastOccupation = lastOccupation;
    }

    public String getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(String birthDate) {
        this.birthDate = birthDate;
    }

    public String getBirthCountry() {
        return birthCountry;
    }

    public void setBirthCountry(String birthCountry) {
        this.birthCountry = birthCountry;
    }

    public String getResidenceCountry() {
        return residenceCountry;
    }

    public void setResidenceCountry(String residenceCountry) {
        this.residenceCountry = residenceCountry;
    }

    public String getNationality() {
        return nationality;
    }

    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    public String getExternalId() {
        return externalId;
    }

    public void setExternalId(String externalId) {
        this.externalId = externalId;
    }

    public String getInternalId() {
        return internalId;
    }

    public void setInternalId(String internalId) {
        this.internalId = internalId;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getDeceased() {
        return deceased;
    }

    public void setDeceased(String deceased) {
        this.deceased = deceased;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getDataSources() {
        return dataSources;
    }

    public void setDataSources(String dataSources) {
        this.dataSources = dataSources;
    }

    public String getRelatedTo() {
        return relatedTo;
    }

    public void setRelatedTo(String relatedTo) {
        this.relatedTo = relatedTo;
    }


    @Override
    public String toString() {
        return "ListEntry{" +
                "type='" + type + '\'' +
                ", name='" + name + '\'' +
                ", firstName='" + firstName + '\'' +
                ", title='" + title + '\'' +
                ", alias=" + akas +
                ", address=" + address +
                ", programs='" + programs + '\'' +
                ", lastOccupation='" + lastOccupation + '\'' +
                ", birthDate='" + birthDate + '\'' +
                ", birthCountry='" + birthCountry + '\'' +
                ", residenceCountry='" + residenceCountry + '\'' +
                ", nationality='" + nationality + '\'' +
                ", externalId='" + externalId + '\'' +
                ", internalId='" + internalId + '\'' +
                ", gender='" + gender + '\'' +
                ", deceased='" + deceased + '\'' +
                ", remarks='" + remarks + '\'' +
                ", dataSources='" + dataSources + '\'' +
                ", relatedTo='" + relatedTo + '\'' +
                '}';
    }
}
