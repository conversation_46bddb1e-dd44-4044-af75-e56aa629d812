package eastnets.screening.entity;

import java.util.List;
import java.util.stream.Collectors;

public class Common {

    public enum FIELD {
        FIRST_NAME(new FormatField("FIRST_NAME", "FIRST_NAME")),
        LAST_NAME(new FormatField("LAST_NAME", "LAST_NAME")),
        FUll_NAME(new FormatField("FULL_NAME", "FULL_NAME")),
        FUll_NAME_XML(new FormatField("FULL_NAME", "XML/BIC", "FULL_NAME")),
        ADDRESS(new FormatField("CN", "ADDRESS")),
        TYPE(new FormatField("CATEGORY", "CATEGORY")),
        AKA(new FormatField("AKA_FULL_NAME", "AKA_FULL_NAME")),
        BIC_CODE(new FormatField("BIC_CODE", "BIC_CODE")),
        BIC_CODE_XML(new FormatField("BIC_CODE", "XML/BIC", "BIC_CODE"));


        private FormatField field;

        FIELD(FormatField field) {
            this.field = field;
        }

        public FormatField getFilField() {
            return field;
        }
    }

    public enum FORMAT {
        SCAN_FORMAT(new Format("format%s", "Scan", "Separator", ",", null)
                , List.of(FIELD.FIRST_NAME, FIELD.LAST_NAME)),
        SCAN_XML_WITH_FULLNAME_FORMAT(new Format("format%s", "Scan", "XML", "XML")
                , List.of(FIELD.FUll_NAME_XML)),
        SCAN_XML_WITH_BIC_FORMAT(new Format("format%s", "Scan", "XML", "XML")
                , List.of(FIELD.BIC_CODE_XML)),
        SCAN_CSV_WITH_BIC_FORMAT(new Format("format%s", "Scan", "Separator", ",", null)
                , List.of(FIELD.BIC_CODE)),
        SCAN_CSV_WITH_FULLNAME_FORMAT(new Format("format%s", "Scan", "Separator", ",", null)
                , List.of(FIELD.FUll_NAME)),
        SCAN_FORMAT2(new Format("format%s", "Scan", "Separator", "|", null)
                , List.of(FIELD.FUll_NAME, FIELD.ADDRESS)),
        IMPORT_ENTRY_INDIVIDUAL_FORMAT(new Format("format%s", "List", "Separator", ",", null)
                , List.of(FIELD.FUll_NAME, FIELD.AKA)),
        IMPORT_ENTRY_INDIVIDUAL_FORMAT_2(new Format("format%s", "List", "Separator", ",", null)
                , List.of(FIELD.LAST_NAME, FIELD.FIRST_NAME)),
        IMPORT_ENTRY_NAMES_AKA(new Format("format%s", "List", "Separator", ",", null)
                , List.of(FIELD.FUll_NAME, FIELD.LAST_NAME, FIELD.AKA)),
        IMPORT_ENTRY_GROUP_FORMAT(new Format("format%s", "List", "Separator", ",", "Group")
                , List.of(FIELD.FUll_NAME, FIELD.AKA)),
        IMPORT_ENTRY_NAME_TYPE(new Format("format%s", "List", "Separator", ",", null)
                , List.of(FIELD.FUll_NAME, FIELD.TYPE)),
        IMPORT_ENTRY_NAMES_TYPE(new Format("format%s", "List", "Separator", ",", null)
                , List.of(FIELD.LAST_NAME, FIELD.FIRST_NAME, FIELD.AKA, FIELD.TYPE));

        private Format format;

        FORMAT(Format format, List<FIELD> formatFields) {
            this.format = format;
            this.format.setFields(formatFields.stream().map(FIELD::getFilField).collect(Collectors.toList()));
        }

        public Format get() {
            return format;
        }
    }


}
