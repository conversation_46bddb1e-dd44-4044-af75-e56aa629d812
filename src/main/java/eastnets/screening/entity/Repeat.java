package eastnets.screening.entity;

public class Repeat {
    private String testCaseName;
    private boolean configurationEnable;
    private String listName;
    private String retentionTime;
    private String zone;
    private String configName;
    private String type;
    private String subType;
    private String rule1;
    private String rule2;
    private String rule3;
    private String rule4;
    private String rule5;
    private String expectedMessage;
    private String remarks;
    private String status;
    private String violation;
    private String matchedEntity;
    private String massage;
    private String relatedMessageDetection;

    public String getTestCaseName() {
        return testCaseName;
    }

    public void setTestCaseName(String testCaseName) {
        this.testCaseName = testCaseName;
    }

    public boolean isConfigurationEnable() {
        return configurationEnable;
    }

    public String getRetentionTime() {
        return retentionTime;
    }

    public void setRetentionTime(String retentionTime) {
        this.retentionTime = retentionTime;
    }

    public boolean getConfigurationEnable() {
        return configurationEnable;
    }

    public void setConfigurationEnable(boolean configurationEnable) {
        this.configurationEnable = configurationEnable;
    }

    public String getListName() {
        return listName;
    }

    public void setListName(String listName) {
        this.listName = listName;
    }

    public String getZone() {
        return zone;
    }

    public void setZone(String zone) {
        this.zone = zone;
    }

    public String getConfigName() {
        return configName;
    }

    public void setConfigName(String configName) {
        this.configName = configName;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSubType() {
        return subType;
    }

    public void setSubType(String subType) {
        this.subType = subType;
    }

    public String getRule1() {
        return rule1;
    }

    public void setRule1(String rule1) {
        this.rule1 = rule1;
    }

    public String getRule2() {
        return rule2;
    }

    public void setRule2(String rule2) {
        this.rule2 = rule2;
    }

    public String getRule3() {
        return rule3;
    }

    public void setRule3(String rule3) {
        this.rule3 = rule3;
    }

    public String getRule4() {
        return rule4;
    }

    public void setRule4(String rule4) {
        this.rule4 = rule4;
    }

    public String getRule5() {
        return rule5;
    }

    public void setRule5(String rule5) {
        this.rule5 = rule5;
    }

    public String getExpectedMessage() {
        return expectedMessage;
    }

    public void setExpectedMessage(String expectedMessage) {
        this.expectedMessage = expectedMessage;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getViolation() {
        return violation;
    }

    public void setViolation(String violation) {
        this.violation = violation;
    }

    public String getMatchedEntity() {
        return matchedEntity;
    }

    public void setMatchedEntity(String matchedEntity) {
        this.matchedEntity = matchedEntity;
    }

    public String getMassage() {
        return massage;
    }

    public void setMassage(String massage) {
        this.massage = massage;
    }

    @Override
    public String toString() {
        return "Repeat{" +
                "testCaseName='" + testCaseName + '\'' +
                ", configurationEnable=" + configurationEnable +
                ", listName='" + listName + '\'' +
                ", retentionTime='" + retentionTime + '\'' +
                ", zone='" + zone + '\'' +
                ", configName='" + configName + '\'' +
                ", type='" + type + '\'' +
                ", subType='" + subType + '\'' +
                ", rule1='" + rule1 + '\'' +
                ", rule2='" + rule2 + '\'' +
                ", rule3='" + rule3 + '\'' +
                ", rule4='" + rule4 + '\'' +
                ", rule5='" + rule5 + '\'' +
                ", expectedMessage='" + expectedMessage + '\'' +
                ", remarks='" + remarks + '\'' +
                ", status='" + status + '\'' +
                ", violation='" + violation + '\'' +
                ", matchedEntity='" + matchedEntity + '\'' +
                ", massage='" + massage + '\'' +
                '}';
    }
}
