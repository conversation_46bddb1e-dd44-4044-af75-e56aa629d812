package eastnets.screening.entity;

public class AKA {

    private String id;
    private String type;
    private String firstName;
    private String lastName;

    public AKA(String id, String type, String name, String description) {
        this.id = id;
        this.type = type;
        this.firstName = name;
        this.lastName = description;
    }

    public AKA() {
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    @Override
    public String toString() {
        return "Alias{" +
                "id='" + id + '\'' +
                ", type='" + type + '\'' +
                ", name='" + firstName + '\'' +
                ", description='" + lastName + '\'' +
                '}';
    }
}
