package eastnets.screening.entity;

import java.util.List;

public class ListSet {

    private String zone;
    private String name;
    private String rank;
    private String owner;
    private SwiftTemplate swiftTemplate;
    private String isoGroup;
    private String detectCountriesFlag;
    private String detectVesselsFlag;
    private List<EnList> enLists;

    public String getZone() {
        return zone;
    }

    public void setZone(String zone) {
        this.zone = zone;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRank() {
        return rank;
    }

    public void setRank(String rank) {
        this.rank = rank;
    }

    public String getOwner() {
        return owner;
    }

    public void setOwner(String owner) {
        this.owner = owner;
    }

    public SwiftTemplate getSwiftTemplate() {
        return swiftTemplate;
    }

    public void setSwiftTemplate(SwiftTemplate swiftTemplate) {
        this.swiftTemplate = swiftTemplate;
    }

    public String getIsoGroup() {
        return isoGroup;
    }

    public void setIsoGroup(String isoGroup) {
        this.isoGroup = isoGroup;
    }

    public String getDetectCountriesFlag() {
        return detectCountriesFlag;
    }

    public void setDetectCountriesFlag(String detectCountriesFlag) {
        this.detectCountriesFlag = detectCountriesFlag;
    }

    public String getDetectVesselsFlag() {
        return detectVesselsFlag;
    }

    public void setDetectVesselsFlag(String detectVesselsFlag) {
        this.detectVesselsFlag = detectVesselsFlag;
    }

    public List<EnList> getEnLists() {
        return enLists;
    }

    public void setEnLists(List<EnList> enLists) {
        this.enLists = enLists;
    }

    @Override
    public String toString() {
        return "ListSet{" +
                "zone='" + zone + '\'' +
                ", name='" + name + '\'' +
                ", rank='" + rank + '\'' +
                ", owner='" + owner + '\'' +
                ", swiftTemplate=" + swiftTemplate +
                ", isoGroup='" + isoGroup + '\'' +
                ", detectCountriesFlag='" + detectCountriesFlag + '\'' +
                ", detectVesselsFlag='" + detectVesselsFlag + '\'' +
                ", enLists=" + enLists +
                '}';
    }
}
