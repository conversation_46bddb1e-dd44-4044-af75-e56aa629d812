package eastnets.screening.entity;

public class GeneralSettings {

    private String testCaseTitle;
    private String scriptName;
    private String zone;
    private String maxDataCharacters;
    private String maxCommentsCharacters;
    private boolean enableArabicPhonetic;
    private boolean enableArabicPhoneticPlus;
    private boolean enableRussianPhonetic;
    private boolean enableSlavicPhonetic;
    private boolean enableCustomPhonetic;
    private boolean allowDetectionDontKnow;
    private boolean enableAutomaticSearchForModifiedDetections;
    private boolean enableDetectionButton;
    private boolean enableAuditTrail;
    private boolean enableGroupForBlockReleaseDonKnow;
    private boolean enableGroupForAssignPending;
    private boolean enableAlternativeWay;
    private boolean enableImprovedInterpretationOfSWIFTFields;
    private boolean enableReadColumnOnAlerts;
    private boolean enableReasonCodeWhenAssignReleaseOrBlockDetectionAlert;
    private boolean showOptionUseAdvancedChecksumCalculation;
    private boolean enableSendAnEmailToAdminsAndShowAWarning;
    private String sendAnEmailToAdminsAndShowAWarningForAlerts;
    private boolean automaticallyResetAssignees;
    private boolean enableGluedWordsSymSpell;
    private boolean enableAutomaticFirstDetection;
    private String alertsDetectionsToBeCreatedModifiedWithCommentsMode;
    private String numberOfLastNotes;
    private String rank;
    private String scannedName;
    private String detectionStatus;
     private String enableGluedWordSymSpellValue;

    public String getEnableGluedWordSymSpellValue() {
        return enableGluedWordSymSpellValue;
    }
    public void setEnableGluedWordSymSpellValue(String enableGluedWordSymSpellValue) {
        this.enableGluedWordSymSpellValue = enableGluedWordSymSpellValue;
    }

    public String getTestCaseTitle() {
        return testCaseTitle;
    }

    public void setTestCaseTitle(String testCaseTitle) {
        this.testCaseTitle = testCaseTitle;
    }

    public String getScriptName() {
        return scriptName;
    }

    public void setScriptName(String scriptName) {
        this.scriptName = scriptName;
    }

    public String getZone() {
        return zone;
    }

    public void setZone(String zone) {
        this.zone = zone;
    }

    public String getMaxDataCharacters() {
        return maxDataCharacters;
    }

    public void setMaxDataCharacters(String maxDataCharacters) {
        this.maxDataCharacters = maxDataCharacters;
    }

    public String getMaxCommentsCharacters() {
        return maxCommentsCharacters;
    }

    public void setMaxCommentsCharacters(String maxCommentsCharacters) {
        this.maxCommentsCharacters = maxCommentsCharacters;
    }

    public boolean isEnableArabicPhonetic() {
        return enableArabicPhonetic;
    }

    public void setEnableArabicPhonetic(boolean enableArabicPhonetic) {
        this.enableArabicPhonetic = enableArabicPhonetic;
    }

    public boolean isEnableArabicPhoneticPlus() {
        return enableArabicPhoneticPlus;
    }

    public void setEnableArabicPhoneticPlus(boolean enableArabicPhoneticPlus) {
        this.enableArabicPhoneticPlus = enableArabicPhoneticPlus;
    }

    public boolean isEnableRussianPhonetic() {
        return enableRussianPhonetic;
    }

    public void setEnableRussianPhonetic(boolean enableRussianPhonetic) {
        this.enableRussianPhonetic = enableRussianPhonetic;
    }

    public boolean isEnableSlavicPhonetic() {
        return enableSlavicPhonetic;
    }

    public void setEnableSlavicPhonetic(boolean enableSlavicPhonetic) {
        this.enableSlavicPhonetic = enableSlavicPhonetic;
    }

    public boolean isEnableCustomPhonetic() {
        return enableCustomPhonetic;
    }

    public void setEnableCustomPhonetic(boolean enableCustomPhonetic) {
        this.enableCustomPhonetic = enableCustomPhonetic;
    }

    public boolean isAllowDetectionDontKnow() {
        return allowDetectionDontKnow;
    }

    public void setAllowDetectionDontKnow(boolean allowDetectionDontKnow) {
        this.allowDetectionDontKnow = allowDetectionDontKnow;
    }

    public boolean isEnableAutomaticSearchForModifiedDetections() {
        return enableAutomaticSearchForModifiedDetections;
    }

    public void setEnableAutomaticSearchForModifiedDetections(boolean enableAutomaticSearchForModifiedDetections) {
        this.enableAutomaticSearchForModifiedDetections = enableAutomaticSearchForModifiedDetections;
    }

    public boolean isEnableDetectionButton() {
        return enableDetectionButton;
    }

    public void setEnableDetectionButton(boolean enableDetectionButton) {
        this.enableDetectionButton = enableDetectionButton;
    }

    public boolean isEnableAuditTrail() {
        return enableAuditTrail;
    }

    public void setEnableAuditTrail(boolean enableAuditTrail) {
        this.enableAuditTrail = enableAuditTrail;
    }

    public boolean isEnableGroupForBlockReleaseDonKnow() {
        return enableGroupForBlockReleaseDonKnow;
    }

    public void setEnableGroupForBlockReleaseDonKnow(boolean enableGroupForBlockReleaseDonKnow) {
        this.enableGroupForBlockReleaseDonKnow = enableGroupForBlockReleaseDonKnow;
    }

    public boolean isEnableGroupForAssignPending() {
        return enableGroupForAssignPending;
    }

    public void setEnableGroupForAssignPending(boolean enableGroupForAssignPending) {
        this.enableGroupForAssignPending = enableGroupForAssignPending;
    }

    public boolean isEnableAlternativeWay() {
        return enableAlternativeWay;

    }

    public void setEnableAlternativeWay(boolean enableAlternativeWay) {
        this.enableAlternativeWay = enableAlternativeWay;
    }

    public boolean isEnableImprovedInterpretationOfSWIFTFields() {
        return enableImprovedInterpretationOfSWIFTFields;
    }

    public void setEnableImprovedInterpretationOfSWIFTFields(boolean enableImprovedInterpretationOfSWIFTFields) {
        this.enableImprovedInterpretationOfSWIFTFields = enableImprovedInterpretationOfSWIFTFields;
    }

    public boolean isEnableReadColumnOnAlerts() {
        return enableReadColumnOnAlerts;
    }

    public void setEnableReadColumnOnAlerts(boolean enableReadColumnOnAlerts) {
        this.enableReadColumnOnAlerts = enableReadColumnOnAlerts;
    }

    public boolean isEnableReasonCodeWhenAssignReleaseOrBlockDetectionAlert() {
        return enableReasonCodeWhenAssignReleaseOrBlockDetectionAlert;
    }

    public void setEnableReasonCodeWhenAssignReleaseOrBlockDetectionAlert(boolean enableReasonCodeWhenAssignReleaseOrBlockDetectionAlert) {
        this.enableReasonCodeWhenAssignReleaseOrBlockDetectionAlert = enableReasonCodeWhenAssignReleaseOrBlockDetectionAlert;
    }

    public boolean isShowOptionUseAdvancedChecksumCalculation() {
        return showOptionUseAdvancedChecksumCalculation;
    }

    public void setShowOptionUseAdvancedChecksumCalculation(boolean showOptionUseAdvancedChecksumCalculation) {
        this.showOptionUseAdvancedChecksumCalculation = showOptionUseAdvancedChecksumCalculation;
    }

    public boolean isEnableSendAnEmailToAdminsAndShowAWarning() {
        return enableSendAnEmailToAdminsAndShowAWarning;
    }

    public void setEnableSendAnEmailToAdminsAndShowAWarning(boolean enableSendAnEmailToAdminsAndShowAWarning) {
        this.enableSendAnEmailToAdminsAndShowAWarning = enableSendAnEmailToAdminsAndShowAWarning;
    }

    public String getSendAnEmailToAdminsAndShowAWarningForAlerts() {
        return sendAnEmailToAdminsAndShowAWarningForAlerts;
    }

    public void setSendAnEmailToAdminsAndShowAWarningForAlerts(String sendAnEmailToAdminsAndShowAWarningForAlerts) {
        this.sendAnEmailToAdminsAndShowAWarningForAlerts = sendAnEmailToAdminsAndShowAWarningForAlerts;
    }

    public boolean isAutomaticallyResetAssignees() {
        return automaticallyResetAssignees;
    }
    public void setAutomaticallyResetAssignees(boolean automaticallyResetAssignees) {
        this.automaticallyResetAssignees = automaticallyResetAssignees;
    }

    public boolean isEnableGluedWordsSymSpell() {
        return enableGluedWordsSymSpell;
    }

    public void setEnableGluedWordsSymSpell(boolean enableGluedWordsSymSpell, String enableGluedWordSymSpellValue) {
        this.enableGluedWordsSymSpell = enableGluedWordsSymSpell;
        this.enableGluedWordSymSpellValue = enableGluedWordSymSpellValue;
    }

    public String getAlertsDetectionsToBeCreatedModifiedWithCommentsMode() {
        return alertsDetectionsToBeCreatedModifiedWithCommentsMode;
    }

    public void setAlertsDetectionsToBeCreatedModifiedWithCommentsMode(String alertsDetectionsToBeCreatedModifiedWithCommentsMode) {
        this.alertsDetectionsToBeCreatedModifiedWithCommentsMode = alertsDetectionsToBeCreatedModifiedWithCommentsMode;
    }

    public String getNumberOfLastNotes() {
        return numberOfLastNotes;
    }

    public void setNumberOfLastNotes(String numberOfLastNotes) {
        this.numberOfLastNotes = numberOfLastNotes;
    }

    public String getRank() {
        return rank;
    }

    public void setRank(String rank) {
        this.rank = rank;
    }

    public String getScannedName() {
        return scannedName;
    }

    public void setScannedName(String scannedName) {
        this.scannedName = scannedName;
    }

    public String getDetectionStatus() {
        return detectionStatus;
    }

    public void setDetectionStatus(String detectionStatus) {
        this.detectionStatus = detectionStatus;
    }

    public boolean isEnableAutomaticFirstDetection() {
        return enableAutomaticFirstDetection;
    }

    public void setEnableAutomaticFirstDetection(boolean enableAutomaticFirstDetection) {
        this.enableAutomaticFirstDetection = enableAutomaticFirstDetection;
    }
}