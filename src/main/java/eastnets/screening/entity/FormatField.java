package eastnets.screening.entity;

public class FormatField {
    private String name;
    private String type;
    private String xpath;
    private boolean scan;
    private boolean addToContext;

    public FormatField() {
    }

    public FormatField(String name, String type) {
        this.name = name;
        this.type = type;
        this.scan = true;
        this.addToContext = true;
    }

    public FormatField(String name, String xpath, String type) {
        this.name = name;
        this.type = type;
        this.xpath = xpath;
        this.scan = true;
        this.addToContext = true;

    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getXpath() {
        return xpath;
    }

    public void setXpath(String xpath) {
        this.xpath = xpath;
    }

    public boolean isScan() {
        return scan;
    }

    public void setScan(boolean scan) {
        this.scan = scan;
    }

    public boolean isAddToContext() {
        return addToContext;
    }

    public void setAddToContext(boolean addToContext) {
        this.addToContext = addToContext;
    }

    @Override
    public String toString() {
        return "FormatField{" +
                "name='" + name + '\'' +
                ", type='" + type + '\'' +
                ", xpath='" + xpath + '\'' +
                ", scan=" + scan +
                ", addToContext=" + addToContext +
                '}';
    }
}
