package eastnets.screening.entity;

public class RepeatedData {
    private String zone;
    private String type;
    private String subType;
    private String created;
    private String expirationDate;
    private String remarks;

    public String getZone() {
        return zone;
    }

    public void setZone(String zone) {
        this.zone = zone;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getSubType() {
        return subType;
    }

    public void setSubType(String subType) {
        this.subType = subType;
    }

    public String getCreated() {
        return created;
    }

    public void setCreated(String created) {
        this.created = created;
    }

    public String getExpirationDate() {
        return expirationDate;
    }

    public void setExpirationDate(String expirationDate) {
        this.expirationDate = expirationDate;
    }

    public String getRemarks() {
        return remarks;
    }

    public void setRemarks(String remarks) {
        this.remarks = remarks;
    }

    @Override
    public String toString() {
        return "RepeatedData{" +
                ", zone='" + zone + '\'' +
                ", type='" + type + '\'' +
                ", subType='" + subType + '\'' +
                ", created='" + created + '\'' +
                ", expirationDate='" + expirationDate + '\'' +
                ", remarks='" + remarks + '\'' +
                '}';
    }
}
