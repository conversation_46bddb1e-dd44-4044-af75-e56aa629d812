package eastnets.screening.entity;

public class ISO20022FormatDetailsConfiguration {

    private String headerField;
    private String bodyField;
    private String Xpath;
    private String bodyFieldType;
    private String SEPA;
    private String category;
    private String quickLink;
    private String fieldLinkWith;
    private ISO20022FormatConfiguration iso20022FormatConfiguration;

    public String getHeaderField() {
        return headerField;
    }

    public void setHeaderField(String header) {
        headerField = headerField;
    }

    public String getBodyField() {
        return bodyField;
    }

    public void setBodyField(String body) {
        bodyField = body;
    }

    public String getBodyFieldType() {
        return bodyFieldType;
    }

    public void setBodyFieldType(String bodyType) {
        bodyFieldType = bodyType;
    }

    public String getSEPA() {
        return SEPA;
    }

    public void setSEPA(String sepa) {
        SEPA = sepa;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String cat) {
        category = cat;
    }

    public String getQuickLink() {
        return quickLink;
    }

    public void setQuickLink(String link) {
        quickLink = link;
    }

    public String getFieldLinkWith() {
        return fieldLinkWith;
    }

    public void setFieldLinkWith(String linkWith) {
        fieldLinkWith = linkWith;
    }

    public String getXpath() {
        return Xpath;
    }

    public void setXpath(String xpath) {
        Xpath = xpath;
    }

    public ISO20022FormatConfiguration getIso20022FormatConfiguration() {
        return iso20022FormatConfiguration;
    }

    public void setIso20022FormatConfiguration(ISO20022FormatConfiguration iso20022FormatConfiguration) {
        this.iso20022FormatConfiguration = iso20022FormatConfiguration;
    }

    @Override
    public String toString() {
        return "ISO20022FormatDetailsConfiguration{" +
                "headerField='" + headerField + '\'' +
                ", bodyField='" + bodyField + '\'' +
                ", Xpath='" + Xpath + '\'' +
                ", bodyFieldType='" + bodyFieldType + '\'' +
                ", SEPA='" + SEPA + '\'' +
                ", category='" + category + '\'' +
                ", quickLink='" + quickLink + '\'' +
                ", fieldLinkWith='" + fieldLinkWith + '\'' +
                ", iso20022FormatConfiguration$=" + iso20022FormatConfiguration +
                '}';
    }
}
