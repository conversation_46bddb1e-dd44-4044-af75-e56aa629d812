package eastnets.screening.gui;

import core.gui.Controls;
import io.qameta.allure.Allure;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class LicenseManager {
    private static final Logger LOG = LoggerFactory.getLogger(LicenseManager.class);

    private static final By IMPORT_LICENSE_BUTTON_BY_ID = By.id("licenseForm:Homepage_business:_btnImportLicense");
    private static final String BROWSE_LICENSE_ID = "licenseForm:Homepage_business:file";
    private static final By BROWSE_LICENSE_BUTTON_BY_ID = By.id(BROWSE_LICENSE_ID);
    private static final By BROWSE_LICENSE_FILE_INPUT_BY_ID = By.id(BROWSE_LICENSE_ID + "_input");
    private static final By IMPORT_LICENSE_WARNING_MESSAGE_BY_XPATH = By.xpath("//div[@id=\"licenseForm:swaf_error_message:growl_message_container\"]/div/div/div[2]/span");

    private final Controls controls;

    public LicenseManager() {
        this.controls = new Controls();
    }

    public void setLicenceFile(RemoteWebDriver driver, String licenseFilePath) {
        Allure.step(String.format("Set Browse Licence file = %s", licenseFilePath));
        controls.setTextBoxValue(driver, BROWSE_LICENSE_FILE_INPUT_BY_ID, licenseFilePath);
    }

    public void clickImportButtn(RemoteWebDriver driver) {
        Allure.step("Click import button.");
        controls.performClickByJS(driver, IMPORT_LICENSE_BUTTON_BY_ID);
    }


}