package eastnets.screening.gui.approvalConfiguration;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class ApprovalGroupsEditor {

    private static final By GROUP_NAME = By.id("approvalForm:homepage_business:tabViewApprovalConfig:homepage_business_approval_groups:groupName");
    private static final By ZONE_FOR_NEW_GROUP = By.id("approvalForm:homepage_business:tabViewApprovalConfig:homepage_business_approval_groups:zoneGroupConfigCbx_label");
    private static final By ADD_ALL_BUTTON = By.xpath("/html/body/div[1]/div[2]/div/form[3]/table/tbody/tr/td/table/tbody/tr/td/div[2]/div/div/div/div[1]/div/div/div[2]/div[3]/div/div[2]/div[3]/div/div/div[2]/div/button[2]");
    private static final By ENABLE_TOGGLER = By.id("approvalForm:homepage_business:tabViewApprovalConfig:homepage_business_approval_groups:togBtn1");
    private static final By ADD_GROUP_BUTTON = By.id("approvalForm:homepage_business:tabViewApprovalConfig:homepage_business_approval_groups:_btnGrp");
    private static final By SEARCH_OPERATOR = By.id("approvalForm:homepage_business:tabViewApprovalConfig:homepage_business_approval_groups:InvestigatorPickList_source_filter");

    private final Controls controls;
    private final Wait wait;

    public ApprovalGroupsEditor() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    @Step("Enter new group name")
    public void writeGroupName(RemoteWebDriver driver, String text) {
        Allure.step("Write group name");
        controls.setTextBoxValue(driver, GROUP_NAME, text);
    }

    @Step("Choose a Zone")
    public void chooseZone(RemoteWebDriver driver, String zoneName) throws Exception {
        Allure.step("Choose Zone");
        controls.selectOptionByDisplayedText(driver, ZONE_FOR_NEW_GROUP, zoneName);
        wait.time(Wait.ONE_SECOND * 5);
    }

    @Step("Add all operators")
    public void addAllOperators(RemoteWebDriver driver) throws InterruptedException {
        Allure.step("Click Add all button");
        controls.performClick(driver, ADD_ALL_BUTTON);
        wait.time(Wait.ONE_SECOND * 5);
    }

    @Step("Enable the toggle switch")
    public void enableToggleSwitch(RemoteWebDriver driver) throws InterruptedException {
        Allure.step("Enable Toggle switch");
        controls.performClickByJS(driver, ENABLE_TOGGLER);
        wait.time(Wait.ONE_SECOND * 2);
    }

    @Step("Click Add Button")
    public void addGroup(RemoteWebDriver driver) throws InterruptedException {
        Allure.step("Click Add button");
        controls.performClick(driver, ADD_GROUP_BUTTON);
        wait.time(Wait.ONE_SECOND * 2);
        controls.acceptAlert(driver);
    }


    @Step("Search Operator")
    public void search_Operator(RemoteWebDriver driver, String operator) {
        Allure.step("Search for Operator", () -> {
            controls.setTextBoxValue(driver, SEARCH_OPERATOR, operator);
            wait.time(Wait.ONE_SECOND * 5);

        });
    }

}
