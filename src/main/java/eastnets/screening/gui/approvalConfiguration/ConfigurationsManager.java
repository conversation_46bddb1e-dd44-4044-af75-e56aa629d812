package eastnets.screening.gui.approvalConfiguration;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class ConfigurationsManager {

    private final static By CONFIGURATION_TAB = By.xpath("//a[@href='#approvalForm:homepage_business:tabViewApprovalConfig:workflowApprovalConfigurationTab']");

    private final static String CHECK_BOX_LOCATOR = "//*[.='%s']//ancestor::tr[1]//td[1]/div";
    private final static String ID_LOCATOR = "//*[.='%s']//ancestor::tr[1]//td[2]/a";
    private final static String STEP_NAME_ID_LOCATOR = "//*[.='%s']//ancestor::tr[1]//td[2]//a";
    private final static By VIEW_STEPS_BUTTON = By.id("approvalForm:homepage_business:tabViewApprovalConfig:homepage_business_wrokflow_configuration:_tblResults:_btnStepsBtn");
    private final static By STATUS_TOGGLE_LOCATOR = By.xpath("//div[@title ='Status']");

    private final Controls controls;
    private final Wait wait;

    public ConfigurationsManager() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    public static enum PERMISSIONS {
        GOOD_GUYS("Good Guys", "Approve on Good Guys Data"),
        ADD_ENTRY("Add new blacklist entry", "Approve Add New Entry"),
        UPDATE_ENTRY("Update blacklist entry", "Approve Update Entry"),
        DELETE_ENTRY("Delete blacklist entry", "Approve Delete Entry"),
        ENABLE_ENTRY("Enable blacklist entry", "Approve Enable Entry"),
        DISABLE_ENTRY("Disable Blacklist Entry", "Approve Disable Entry");

        public String permissionName;
        public String approveStepDescription;

        PERMISSIONS(String permissionName, String approveStepDescription) {
            this.permissionName = permissionName;
            this.approveStepDescription = approveStepDescription;
        }

        public String getText() {
            return this.permissionName;
        }

        public String getApproveStepDescription() {
            return approveStepDescription;
        }
    }

    ;


    @Step("Click Checkbox")
    public void clickCheckBox(RemoteWebDriver driver, PERMISSIONS permission) {
        Allure.step("Choose Permission checkbox");
        controls.performClick(driver, By.xpath(String.format(CHECK_BOX_LOCATOR, permission.getText())));
    }

    @Step("Click ID")
    public void clickID(RemoteWebDriver driver, PERMISSIONS permission) throws InterruptedException {
        Allure.step("Click Permission ID");
        controls.performClickByJS(driver, By.xpath(String.format(ID_LOCATOR, permission.getText())));
    }

    @Step("Click Configuration tab")
    public void clickConfigurationTab(RemoteWebDriver driver) throws InterruptedException {
        Allure.step("Click Configuration tab");
        wait.time(Wait.ONE_SECOND * 10);
        controls.performClick(driver, CONFIGURATION_TAB);
    }

    @Step("Click View Steps button")
    public void clickViewStepsButton(RemoteWebDriver driver) {
        Allure.step("Click View Steps button");
        controls.performClickByJS(driver, VIEW_STEPS_BUTTON);
    }

    @Step("Click Step ID")
    public void clickStepID(RemoteWebDriver driver, PERMISSIONS permission) throws InterruptedException {
        Allure.step("Click Step ID");
        controls.performClickByJS(driver, By.xpath(String.format(STEP_NAME_ID_LOCATOR, permission.getApproveStepDescription())));

        wait.time(Wait.ONE_SECOND * 4);
    }

    @Step("Click Status toggle switch")
    public void clickStatusToggleSwitch(RemoteWebDriver driver) throws InterruptedException {
        Allure.step("Click Status toggle button");
        controls.performClickByJS(driver, STATUS_TOGGLE_LOCATOR);
        wait.time(Wait.ONE_SECOND * 4);
    }


}
