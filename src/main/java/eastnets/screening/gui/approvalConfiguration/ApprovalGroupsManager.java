package eastnets.screening.gui.approvalConfiguration;

import core.gui.Controls;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class ApprovalGroupsManager {
    private static final By APPROVAL_TAB = By.xpath("//a[@href='#approvalForm:homepage_business:tabViewApprovalConfig:approvalGroupsTab']");
    private static final By ADD_BUTTON = By.id("approvalForm:homepage_business:tabViewApprovalConfig:homepage_business_approval_groups:_tblResults:btnAdd");
    private static final By SEARCH_BUTTON = By.id("approvalForm:homepage_business:tabViewApprovalConfig:homepage_business_approval_groups:btnSearchApprovalGroups");
    private static final By DELETE_BUTTON = By.id("approvalForm:homepage_business:tabViewApprovalConfig:homepage_business_approval_groups:_tblResults:btnDelete");
    private static final By NAVIGATE_TO_LAST_PAGE_BUTTON = By.id("approvalForm:homepage_business:tabViewApprovalConfig:homepage_business_approval_groups:_tblResults:j_idt359");
    private final static String CHECK_BOX_LOCATOR = "//*[.='%s']//ancestor::tr[1]//td[1]/div";
    private final static String ID_LOCATOR = "//*[.='%s']//ancestor::tr[1]//td[2]/a";

    private final Controls controls;

    public ApprovalGroupsManager() {
        this.controls = new Controls();
    }


    @Step("Click Search button")
    public void clickSearchButton(RemoteWebDriver driver) {
        Allure.step("Click Search button");
        controls.performClickByJS(driver, SEARCH_BUTTON);
    }

    @Step("Click Approval tab")
    public void clickApprovalGroupsTab(RemoteWebDriver driver) {
        Allure.step("Click Approval Groups tab");
        controls.performClick(driver, APPROVAL_TAB);
    }

    @Step("Click Add Button")
    public void clickAddButton(RemoteWebDriver driver) {
        Allure.step("Click Add button");
        controls.performClickByJS(driver, ADD_BUTTON);
    }

    @Step("Click Delete Button")
    public void clickDeleteButton(RemoteWebDriver driver) {
        Allure.step("Click Delete Button");
        controls.performClickByJS(driver, DELETE_BUTTON);

    }

    @Step("Click Navigate to last page Button")
    public void clickNavigateToLastPageButton(RemoteWebDriver driver) {
        Allure.step("Click Navigate to last page Button");
        controls.performClickByJS(driver, NAVIGATE_TO_LAST_PAGE_BUTTON);

    }

    @Step("Click CheckBox")
    public void clickCheckBox(RemoteWebDriver driver, String groupName) {
        Allure.step("Click Navigate to last page Button");
        controls.performClick(driver, By.xpath(String.format(CHECK_BOX_LOCATOR, groupName)));
    }

    @Step("Click group ID")
    public void clickID(RemoteWebDriver driver, String groupName) {
        Allure.step("Click group ID");
        controls.performClickByJS(driver, By.xpath(String.format(ID_LOCATOR, groupName)));
    }
}
