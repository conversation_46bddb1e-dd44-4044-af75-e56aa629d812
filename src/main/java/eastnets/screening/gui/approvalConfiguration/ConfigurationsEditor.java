package eastnets.screening.gui.approvalConfiguration;

import core.gui.Controls;
import core.util.Wait;
import eastnets.common.control.CommonAction;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.remote.RemoteWebDriver;

public class ConfigurationsEditor {
    private final static By ADD_ALL_BUTTON = By.xpath("//button[contains(@class,'add-all')]");
    private final static By REMOVE_ALL_BUTTON = By.xpath("//button[contains(@class,'remove-all')]");
    private final static By UPDATE_BUTTON_UPDATE_TEMPLATE = By.id("approvalForm:homepage_business:tabViewApprovalConfig:homepage_business_wrokflow_configuration:_btnUpdateTemplate");
    private final static By UPDATE_BUTTON_UPDATE_STEP = By.id("approvalForm:homepage_business:tabViewApprovalConfig:homepage_business_wrokflow_configuration:_btnUpdateStep");

    private final static By SEARCH_ZONE_TEXTBOX = By.id("approvalForm:homepage_business:tabViewApprovalConfig:homepage_business_wrokflow_configuration:PojoPickList_source_filter");
    private final static By SEARCH_GROUP_TEXTBOX = By.id("approvalForm:homepage_business:tabViewApprovalConfig:homepage_business_wrokflow_configuration:InvestigatorPickList_source_filter");
    private final static By ZONE_DDL = By.id("approvalForm:homepage_business:tabViewApprovalConfig:homepage_business_wrokflow_configuration:zoneCbx_label");

    private final Controls controls;
    private final Wait wait;
    private final CommonAction commonAction;

    public ConfigurationsEditor() {
        this.controls = new Controls();
        this.wait = new Wait();
        this.commonAction = new CommonAction();
    }

    @Step("Click Add all button")
    public void clickAddAll(RemoteWebDriver driver) throws InterruptedException {
        Allure.step("Click Add all button");
        wait.time(Wait.ONE_SECOND * 5);
        WebElement addButton = driver.findElement(ADD_ALL_BUTTON);

        if (addButton.isEnabled()) {
            controls.performClick(driver, ADD_ALL_BUTTON);
            wait.time(Wait.ONE_SECOND * 5);
        }
    }

    @Step("Click Remove all button")
    public void clickRemoveAll(RemoteWebDriver driver) throws InterruptedException {
        WebElement removeButton = driver.findElement(REMOVE_ALL_BUTTON);
        if (removeButton.isEnabled()) {
            controls.performClick(driver, REMOVE_ALL_BUTTON);
            wait.time(Wait.ONE_SECOND * 5);
        }
    }

    @Step("Click Update button Update Template")
    public String clickUpdateButtonUpdateTemplate(RemoteWebDriver driver) throws InterruptedException {
        Allure.step("Click Update button");
        controls.performClickByJS(driver, UPDATE_BUTTON_UPDATE_TEMPLATE);
        wait.time(Wait.ONE_SECOND * 3);
        return commonAction.getAlertMessageString(driver);
    }

    @Step("Click Update button Update Step")
    public void clickUpdateButtonUpdateStep(RemoteWebDriver driver) throws InterruptedException {
        Allure.step("Click Update button");
        controls.performClickByJS(driver, UPDATE_BUTTON_UPDATE_STEP);
        wait.time(Wait.ONE_SECOND * 3);
    }

    @Step("Search Zone")
    public void searchZone(RemoteWebDriver driver, String zoneName) {
        Allure.step("Write Zone name");
        controls.setTextBoxValue(driver, SEARCH_ZONE_TEXTBOX, zoneName);
    }

    @Step("Search Group")
    public void searchGroup(RemoteWebDriver driver, String groupName) throws InterruptedException {
        Allure.step("Write Group name");
        controls.setTextBoxValue(driver, SEARCH_GROUP_TEXTBOX, groupName);

    }

    @Step("Choose a Zone")
    public void chooseZone(RemoteWebDriver driver, String zoneName) throws Exception {
        Allure.step("Choose Zone");
        controls.selectOptionByDisplayedText(driver, ZONE_DDL, zoneName);
        wait.time(Wait.ONE_SECOND * 5);

    }

}
