package eastnets.screening.gui.approvalConfiguration;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.util.List;

public class WorkflowApprovals {

    private final static By WORKFLOW_APPROVALS_TAB = By.xpath("//a[@href = '#approvalForm:homepage_business:tabViewApprovalConfig:workflowApprovalsTab']");
    private final static By SEARCH_BUTTON = By.xpath("//*[@id='approvalForm:homepage_business:tabViewApprovalConfig:homepage_business_approvals:btnSearchApprovals']");
    private final static By APPROVE_BUTTON = By.id("approvalForm:homepage_business:tabViewApprovalConfig:homepage_business_approvals:_tblResults:btnApprove");
    private final static By REJECT_BUTTON = By.id("approvalForm:homepage_business:tabViewApprovalConfig:homepage_business_approvals:_tblResults:btnViewRejectPanel");
    private final static By REJECTION_REASON_DDL = By.id("approvalForm:homepage_business:tabViewApprovalConfig:homepage_business_approvals:rejectionReasonCbx_label");
    private final static By REJECTION_REMARKS_TEXTBOX = By.id("approvalForm:homepage_business:tabViewApprovalConfig:homepage_business_approvals:rejectionRemarks");
    private final static By CONFIRM_BUTTON = By.id("approvalForm:homepage_business:tabViewApprovalConfig:homepage_business_approvals:btnReject");
    private final static String CHECK_BOX_LOCATOR = "//*[.='%s']//ancestor::tr[1]//td[1]/input";
    private final static String APPROVAL_STATUS_LOCATOR = "//*[.='%s']//ancestor::tr[1]//td[6]";
    private final static String ACTION_LOCATOR = "//*[.='%s']//ancestor::tr[1]//td[7]";
    private static final By RESUBMIT_BUTTON = By.id("approvalForm:homepage_business:tabViewApprovalConfig:homepage_business_approvals:_tblResults:btnReSubmitApproval");
    private static final By STATUS_DDL_LOCATOR = By.id("approvalForm:homepage_business:tabViewApprovalConfig:homepage_business_approvals:statusCbx_label");
    private static final By ACTION_DDL_LOCATOR = By.id("approvalForm:homepage_business:tabViewApprovalConfig:homepage_business_approvals:actionCbx_label");
    private static final By ROWS_LOCATOR = By.xpath("//tr[@role = 'row']");
    private static final By FROM_DATE_LOCATOR = By.id("approvalForm:homepage_business:tabViewApprovalConfig:homepage_business_approvals:dateFromId_input");

    private final Controls controls;
    private final Wait wait;

    public WorkflowApprovals() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    public static enum TEMPLATE_NAME {
        ENABLE_BLACKLIST_ENTRY("Enable blacklist entry"),
        DISABLE_BLACKLIST_ENTRY("Disable Blacklist Entry"),
        DELETE_BLACKLIST_ENTRY("Delete blacklist entry"),
        UPDATE_BLACKLIST_ENTRY("Update blacklist entry"),
        ADD_BLACKLIST_ENTRY("Add new blacklist entry"),
        ;
        String templateName;

        TEMPLATE_NAME(String templateName) {
            this.templateName = templateName;

        }

        public String getTemplateName() {
            return templateName;
        }
    }

    public void clickWorkflowApprovalsTab(RemoteWebDriver driver) {
        Allure.step("Click Workflow Approvals tab");
        controls.performClick(driver, WORKFLOW_APPROVALS_TAB);
    }

    @Step("Click Checkbox")
    public void clickCheckBox(RemoteWebDriver driver, TEMPLATE_NAME templateName) throws InterruptedException {
        Allure.step("Choose request to be approved");
        wait.time(Wait.ONE_SECOND * 5);
        controls.performClick(driver, By.xpath(String.format(CHECK_BOX_LOCATOR, templateName.getTemplateName())));
    }

    @Step("Get Approval Status")
    public String getApprovalStatus(RemoteWebDriver driver, String templateName) throws InterruptedException {
        Allure.step("Get Approval Status");
        wait.time(Wait.ONE_SECOND * 7);
        WebElement status = driver.findElement(By.xpath(String.format(APPROVAL_STATUS_LOCATOR, templateName)));
        return status.getText();
    }

    @Step("Get Action")
    public String getAction(RemoteWebDriver driver) {
        Allure.step("Get Action");
        WebElement status = driver.findElement(By.xpath(ACTION_LOCATOR));
        return status.getText();
    }


    @Step("Click Approve")
    public void clickApprove(RemoteWebDriver driver) {
        Allure.step("Click Approve button");
        controls.performClickByJS(driver, APPROVE_BUTTON);
    }

    @Step("Click Reject")
    public void clickReject(RemoteWebDriver driver) {
        Allure.step("Click Reject button");
        controls.performClickByJS(driver, REJECT_BUTTON);
    }

    @Step("Choose Rejection Reason")
    public boolean chooseRejectionReason(RemoteWebDriver driver, String rejectionReason) throws Exception {
        Allure.step("Choose Rejection Reason");
        wait.time(Wait.ONE_SECOND * 3);
        return controls.selectOptionByDisplayedText(driver, REJECTION_REASON_DDL, rejectionReason);
    }

    @Step("Write Rejection Remarks")
    public void writeRejectionRemarks(RemoteWebDriver driver) throws InterruptedException {
        Allure.step("Choose Rejection Remarks");
        controls.setTextBoxValue(driver, REJECTION_REMARKS_TEXTBOX, "Test");
        wait.time(Wait.ONE_SECOND * 3);
    }

    @Step("Click Confirm button")
    public void clickConfirmButton(RemoteWebDriver driver) throws InterruptedException {
        Allure.step("Click Confirm button");
        controls.performClickByJS(driver, CONFIRM_BUTTON);
        wait.time(Wait.ONE_SECOND * 3);
    }

    @Step("Click Resubmit button")
    public void clickResubmitButton(RemoteWebDriver driver) {
        Allure.step("Click Resubmit button");
        controls.performClickByJS(driver, RESUBMIT_BUTTON);
    }

    @Step("Check Resubmit button")
    public boolean isResubmitButtonDisplayed(RemoteWebDriver driver) throws InterruptedException {
        Allure.step("Check Resubmit button");
        wait.time(Wait.ONE_SECOND * 5);
        return driver.findElement(RESUBMIT_BUTTON).isDisplayed();
    }

    @Step("Choose Status")
    public void chooseStatus(RemoteWebDriver driver, String status) throws Exception {
        Allure.step("Choose Status");
        controls.selectOptionByDisplayedText(driver, STATUS_DDL_LOCATOR, status);
        wait.time(Wait.ONE_SECOND * 2);

    }

    @Step("Choose Action")
    public void chooseAction(RemoteWebDriver driver, String action) throws Exception {
        Allure.step("Choose Status");
        controls.selectOptionByDisplayedText(driver, ACTION_DDL_LOCATOR, action);
        wait.time(Wait.ONE_SECOND * 2);

    }

    @Step("Click Search button")
    public void clickSearchButton(RemoteWebDriver driver) {
        Allure.step("Click Search Button");
        controls.performClickByJS(driver, SEARCH_BUTTON);

    }

    @Step("Get Rows")
    public List<WebElement> getRequestsRows(RemoteWebDriver driver) {
        return driver.findElements(ROWS_LOCATOR);
    }

    @Step("Choose From Date")
    public void chooseFromDate(RemoteWebDriver driver, String date) {
        driver.findElement(FROM_DATE_LOCATOR).clear();
        driver.findElement(FROM_DATE_LOCATOR).sendKeys(date);
    }


}
