package eastnets.screening.gui.scanManager.resultManager;

import core.gui.Controls;
import core.util.Wait;
import eastnets.screening.entity.Violation;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.util.ArrayList;
import java.util.List;

public class ResultManager {

    private static final By SEARCH_BUTTON_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:Tab_results:btnSearch");
    private static final By RESET_BUTTON_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:Tab_results:btnReset");
    private static final By STATUS_RESULT_LOCATOR = By.xpath(
            "//*[@id='scanManagerForm:homepage_business:tabViewScanManager:Tab_results:_tblResults_data']" +
                    "//td[count(//*[@id='scanManagerForm:homepage_business:tabViewScanManager:Tab_results:_tblResults_head']" +
                    "//span[.='Status']/preceding::th" +
                    "[contains(@id,'scanManagerForm:homepage_business:tabViewScanManager:Tab_results:_tblResults')])+1]");
    private static final By REFRESH_BUTTON_LOCATOR = By.xpath("scanManagerForm:homepage_business:tabViewScanManager:Tab_results:_tblResults:_btnRefresh");
    private static final By DETECTION_ID_LOCATOR = By.xpath("//table/tbody[@id='scanManagerForm:homepage_business:tabViewScanManager:Tab_results:ScanResultDetails:_tblResultDetails_data']/tr/td[count(//span[.='Detection ID']/ancestor::th/preceding-sibling::th)+1]");
    private static final By RANK_LOCATOR = By.xpath("//tbody[@id='scanManagerForm:homepage_business:tabViewScanManager:Tab_results:ScanResultDetails:_tblResultDetails_data']//tr//td[7]");
    private static final By PRINT_DETAILS_BUTTON_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:Tab_results:ScanResultDetails:_tblResultDetails:btnPrintDetail");
    private static final By STATUS_LOCATOR = By.cssSelector(".ui-widget .customer-badge");
    private static final By CLOSE_BUTTON_LOCATOR = By.xpath("//*[@id='scanManagerForm:violationDetailDLG']//*[@aria-label='Close']");
    private static final By DETECTION_RECORDS_ROWS_LOCATOR = By.xpath("//*[@id='scanManagerForm:violation_list:violationList_ResultsTable_data']//tr");
    private static final By NEXT_PAGE_BUTTON_LOCATOR = By.xpath("//span[text()='>']//ancestor::button[contains(@id,'scanManagerForm:violation_list:j')]");
    private static final By SESSION_ID_LOCATOR = By.xpath("//*[@id = 'scanManagerForm:homepage_business:tabViewScanManager:Tab_results:_tblResults_data']//tr[1]//td[2]");
    private static final String STATUS_STR_LOCATOR = "(//tbody[@id='scanManagerForm:homepage_business:tabViewScanManager:Tab_results:ScanResultDetails:_tblResultDetails_data']//span[.='%s'])[1]";
    private static final By LOG_FILE_LOCATOR = By.linkText("Log");

    private final Controls controls;
    private final Wait wait;

    public ResultManager() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    @Step("Click Search Button")
    public void clickSearchButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SEARCH_BUTTON_LOCATOR);
    }

    @Step("Click Reset Button")
    public void clickResetButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, RESET_BUTTON_LOCATOR);
    }

    @Step("Get Status")
    public String getStatus(RemoteWebDriver driver) throws InterruptedException {
        wait.time(Wait.ONE_SECOND);
        return controls.getElementText(driver, STATUS_RESULT_LOCATOR);
    }

    @Step("Click on Result")
    public void clickOnResult(RemoteWebDriver driver) {
        controls.performClickByJS(driver, STATUS_RESULT_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Click on Detection")
    public void clickOnDetection(RemoteWebDriver driver) {
        controls.performClick(driver, DETECTION_ID_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Click on Detection by detection status")
    public void clickOnDetection(RemoteWebDriver driver, String status) {
        controls.performClick(driver, By.xpath(String.format(STATUS_STR_LOCATOR, status)));
    }

    @Step("Click Refresh Button")
    public void clickRefreshButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, REFRESH_BUTTON_LOCATOR);
    }

    @Step("Get Detection ID")
    public String getDetectionID(RemoteWebDriver driver) {
        return controls.getElementText(driver, DETECTION_ID_LOCATOR);
    }

    @Step("Get Detection Rank")
    public String getDetectionRank(RemoteWebDriver driver) {
        return controls.getElementText(driver, RANK_LOCATOR);
    }

    @Step("Click export detection details report button")
    public void clickExportDetectionDetailsButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, PRINT_DETAILS_BUTTON_LOCATOR);
    }

    @Step("Get status detection from result page")
    public String getStatusFromResultPage(RemoteWebDriver driver) {
        String status = controls.getElementText(driver, STATUS_LOCATOR);
        Allure.step("Status detection = " + status);
        return status;
    }

    @Step("Get status List detection from result page")
    public ArrayList<String> getStatusListFromResultPage(RemoteWebDriver driver) {
        ArrayList<String> statusList = new ArrayList<>();
        List<WebElement> statusElementsList = driver.findElements(STATUS_LOCATOR);
        for (WebElement element : statusElementsList) {
            Allure.step("Status detection = " + element.getText());
            statusList.add(element.getText());
        }
        return statusList;
    }

    @Step("Click close button")
    public void clickCloseButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, CLOSE_BUTTON_LOCATOR);
    }

    @Step("Get violation list data [status report, matched entity name]")
    public ArrayList<Violation> getViolationList(RemoteWebDriver driver) {
        clickOnDetection(driver);
        wait.waitUntilAjaxLoaderDisappear(driver);
        ArrayList<Violation> violations = new ArrayList<>();
        Violation violation;

        while (true) {
            wait.waitUntilAjaxLoaderDisappear(driver);
            List<WebElement> rowsWebElements = driver.findElements(DETECTION_RECORDS_ROWS_LOCATOR);
            System.out.println("Rows " + rowsWebElements.size());
            for (WebElement webElement : rowsWebElements) {
                List<WebElement> cellsWebElements = webElement.findElements(By.tagName("td"));
                System.out.println(cellsWebElements.size());
                violation = new Violation();

                violation.setStatus(cellsWebElements.get(2).getText());
                violation.setMatchedEntity(cellsWebElements.get(4).getText());

                violations.add(violation);

            }
            if (driver.findElement(NEXT_PAGE_BUTTON_LOCATOR).getAttribute("aria-disabled").contains("false")) {
                controls.performClickByJS(driver, NEXT_PAGE_BUTTON_LOCATOR);
            } else
                break;
        }
        clickCloseButton(driver);
        return violations;
    }

    @Step("Get Session ID")
    public String getSessionId(RemoteWebDriver driver) {
        return controls.getElementText(driver, SESSION_ID_LOCATOR);
    }

    @Step("Click on 'Log'")
    public void click_log_Link(RemoteWebDriver driver) {
        controls.performClick(driver, LOG_FILE_LOCATOR);
    }

}
