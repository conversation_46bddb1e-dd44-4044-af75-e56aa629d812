package eastnets.screening.gui.scanManager.resultManager;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

public class ResultEditor {
    private static final By ALERTS_LIST_LOCATOR = By.xpath("//*[@id = 'scanManagerForm:violation_list:violationList_ResultsTable_data']//tr");
    private static final By UNMATCHED_ROLES_LOCATOR = By.xpath("//*[.='Unmatched Rule:']//ancestor::div[2]//div[2]");
    private static final By EXPORT_VIOLATION_BUTTON_LOCATOR = By.id("scanManagerForm:violation_list:exportViolationBtn");
    private static final By PRINT_SCOPE_ALL_LOCATOR = By.xpath("//input[@id='scanManagerForm:exportViolation:printMode:1']//ancestor::div[1]");
    private static final By DOC_TYPE_LOCATOR = By.id("scanManagerForm:exportViolation:exportType");
    private static final By PRINT_BUTTON_LOCATOR = By.id("scanManagerForm:exportViolation:btnPrint");
    private static final By CLOSE_DIALOG_LOCATOR = By.xpath("//*[@id='scanManagerForm:exportViolationDlg']//*[@aria-label='Close']");
    private static final By CLOSE_VIOLATION_DETECTION_BUTTON_LOCATOR = By.xpath("//*[@id='scanManagerForm:violationDetailDLG']//a[@aria-label='Close']");
    private static final By CONDITIONS_NAME_TEXT_BOX_LOCATOR = By.id("scanManagerForm:Good_guy_management:searchInputText");
    private static final By SEARCH_CONDITIONS_BUTTON_LOCATOR = By.id("scanManagerForm:Good_guy_management:searchCondtion");
    private static final By ADD_CONTEXT_CONDITIONS_LOCATOR = By.linkText("Add");
    private static final By ALERT_STATUS_LIST_LOCATOR = By.xpath("//*[@id= 'scanManagerForm:violation_list:violationList_ResultsTable_data']//tr//td[3]");
    private static final By SCANNED_RESULT_ROWS_LOCATOR = By.xpath("//*[@id = 'scanManagerForm:homepage_business:tabViewScanManager:Tab_results:ScanResultDetails:_tblResultDetails_data']//tr");
    private static final By NEXT_PAGE_LOCATOR = By.xpath("//*[contains(@id , 'scanManagerForm:homepage_business:tabViewScanManager:Tab_results:ScanResultDetails:_tblResultDetails')]//button[.='>']");

    private final Controls controls;
    private final Wait wait;

    public ResultEditor() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    @Step("Get Unmatched Roles")
    public String getUnmatchedRoles(RemoteWebDriver driver) {
        String unmatchedRoles = controls.getElementText(driver, UNMATCHED_ROLES_LOCATOR);
        Allure.step("Unmatched roles = " + unmatchedRoles);
        return unmatchedRoles;
    }


    @Step("Get Number of alerts")
    public int getNumOfAlerts(RemoteWebDriver driver) {
        return driver.findElements(ALERTS_LIST_LOCATOR).size();
    }


    @Step("Click export button.")
    public void clickExportButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, EXPORT_VIOLATION_BUTTON_LOCATOR);
    }

    @Step("Select print Scope.")
    public void selectPrintScope(RemoteWebDriver driver, String printScope) {
        if (printScope.equalsIgnoreCase("all")) {
            controls.performClick(driver, PRINT_SCOPE_ALL_LOCATOR);
        }
    }

    @Step("Select document tye.")
    public void selectDocType(RemoteWebDriver driver, String docType) throws Exception {
        controls.selectOptionByDisplayedText(driver, DOC_TYPE_LOCATOR, docType);
    }

    @Step("Click print button.")
    public void clickPrintButton(RemoteWebDriver driver) {
        controls.performClick(driver, PRINT_BUTTON_LOCATOR);
        wait.waitForJStoLoad(driver);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Click on close dialog button.")
    public void clickCloseDialogButton(RemoteWebDriver driver) {
        Allure.step("Click on close dialog button.");
        controls.performClick(driver, CLOSE_DIALOG_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Click close violation detection button")
    public void closeViolationDetection(RemoteWebDriver driver) {
        controls.performClickByJS(driver, CLOSE_VIOLATION_DETECTION_BUTTON_LOCATOR);
    }

    @Step("Set conditions name")
    public void setConditionName(RemoteWebDriver driver, String conditionName) {
        controls.setTextBoxValue(driver, CONDITIONS_NAME_TEXT_BOX_LOCATOR, conditionName);
    }

    @Step("Click on search button.")
    public void clickSearchConditionsButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SEARCH_CONDITIONS_BUTTON_LOCATOR);
        wait.waitForJStoLoad(driver);
    }

    @Step("Click on add context conditions button")
    public void clickAddConditionButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, ADD_CONTEXT_CONDITIONS_LOCATOR);
    }

    @Step("Get Alert's status list")
    public List<String> getAlertStatusList(RemoteWebDriver driver) {
        List<WebElement> status_locators = driver.findElements(ALERT_STATUS_LIST_LOCATOR);
        List<String> status_list = new ArrayList<>();
        for (WebElement webElement : status_locators) {
            status_list.add(webElement.getText());
        }
        return status_list;
    }

    @Step("Get Detections Details(data and Rank)")
    public HashMap<String, String> get_all_detections_details(RemoteWebDriver driver) {
        HashMap<String, String> detection_details = new HashMap<>();
        while (true) {
            List<WebElement> detection_element_list = driver.findElements(SCANNED_RESULT_ROWS_LOCATOR);
            for (WebElement webElement : detection_element_list) {
                List<WebElement> detection_td = webElement.findElements(By.tagName("td"));
                detection_details.put(detection_td.get(4).getText(), detection_td.get(6).getText());

            }
            try {
                controls.performClick(driver, NEXT_PAGE_LOCATOR);
                wait.waitUntilAjaxLoaderDisappear(driver);
            } catch (Exception e) {
                break;
            }
        }
        return detection_details;
    }

    @Step("Get Detections Details(Scanned Name and Rank)")
    public HashMap<String, String> get_scanned_record_and_rank(RemoteWebDriver driver) {
        HashMap<String, String> detection_details = new HashMap<>();
        while (true) {
            List<WebElement> detection_element_list = driver.findElements(SCANNED_RESULT_ROWS_LOCATOR);
            for (WebElement webElement : detection_element_list) {
                List<WebElement> detection_td = webElement.findElements(By.tagName("td"));
                detection_details.put(detection_td.get(7).getText(), detection_td.get(6).getText());
            }
            try {
                controls.performClick(driver, NEXT_PAGE_LOCATOR);
                wait.waitUntilAjaxLoaderDisappear(driver);
            } catch (Exception e) {
                break;
            }
        }
        return detection_details;
    }

}
