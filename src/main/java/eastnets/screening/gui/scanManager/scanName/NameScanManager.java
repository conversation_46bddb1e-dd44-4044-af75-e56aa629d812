package eastnets.screening.gui.scanManager.scanName;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.remote.RemoteWebDriver;

public class NameScanManager {


    private static final By ZONE_SELECT_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:NameChecker_business:ncZoneId");
    private static final By NAME_FIELD_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:NameChecker_business:name");
    private static final By RANK_SELECT_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:NameChecker_business:rankCbx");
    private static final String CREATE_AUTOMATIC_ALERT_CHECKBOX_LOCATOR = "scanManagerForm:homepage_business:tabViewScanManager:NameChecker_business:auto_create_alert";
    private static final String DETECT_VESSELS_CHECKBOX_LOCATOR = "scanManagerForm:homepage_business:tabViewScanManager:NameChecker_business:vessels";
    private static final String DETECT_COUNTRIES_CHECKBOX_LOCATOR = "scanManagerForm:homepage_business:tabViewScanManager:NameChecker_business:countries";

    private static final By SCAN_BUTTON_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:NameChecker_business:btnScan");
    private static final By RESET_BUTTON_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:NameChecker_business:btnReset");
    private static final By TABLE_LOCATOR = By.xpath("//tbody[@id='scanManagerForm:homepage_business:tabViewScanManager:NameChecker_business:detectionItem_data']");
    private static final By DETECTION_STATUS_LOCATOR = By.xpath("//*[contains(@id , 'scanManagerForm:homepage_business:tabViewScanManager:NameChecker_business:detectionItem:0:tagDet')]//ancestor::td[1]");
    private static final By DETECTION_STATUS_4_EYES_LOCATOR = By.xpath("//*[@id = 'scanManagerForm:homepage_business:tabViewScanManager:NameChecker_business:detectionItem:0:tagDet4Eyes']//ancestor::td[1]");
    private static final By RANK_LOCATOR = By.xpath("//tbody/tr[1]/td[5]");
    private static final By DETECTION_ID_LOCATOR = By.xpath("//table/tbody[@id='scanManagerForm:homepage_business:tabViewScanManager:NameChecker_business:detectionItem_data']/tr/td[count(//span[.='Detection ID']/ancestor::th/preceding-sibling::th)+1]");
    private static final By INVESTIGATOR_LOCATOR = By.xpath("//tbody[@id='scanManagerForm:homepage_business:tabViewScanManager:NameChecker_business:detectionItem_data']/tr[1]/td[6]");
    private static final String RANK_BY_MATCHED_NAME_LOCATOR = "//*[@id='scanManagerForm:violation_list:violationList_ResultsTable_data']//td[5]//*[.='%s']//ancestor::tr[1]//td[9]";

    private final Controls controls;
    private final Wait wait;

    public NameScanManager() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    @Step("Click reset button")
    public void clickFResetButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, RESET_BUTTON_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Select zone")
    public void selectZone(RemoteWebDriver driver, String zone) throws Exception {
        controls.selectOptionByDisplayedText(driver, ZONE_SELECT_LOCATOR, zone);
    }

    @Step("Select rank")
    public void selectRank(RemoteWebDriver driver, String rank) throws Exception {
        controls.selectOptionByDisplayedText(driver, RANK_SELECT_LOCATOR, rank);
    }

    @Step("Set scanned name")
    public void setName(RemoteWebDriver driver, String name) {
        controls.setTextBoxValue(driver, NAME_FIELD_LOCATOR, name);
    }

    @Step("Set automatic alert checkbox")
    public void setAutomaticAlertCheckBox(RemoteWebDriver driver, boolean flag) {
        controls.setCheckboxValueById(driver, CREATE_AUTOMATIC_ALERT_CHECKBOX_LOCATOR, flag);
    }

    @Step("Set detect countries checkbox")
    public void setDetectCountriesCheckBox(RemoteWebDriver driver, boolean flag) {
        controls.setCheckboxValueById(driver, DETECT_COUNTRIES_CHECKBOX_LOCATOR, flag);
    }

    @Step("Set detect vessels checkbox")
    public void setDetectVesselsCheckBox(RemoteWebDriver driver, boolean flag) {
        controls.setCheckboxValueById(driver, DETECT_VESSELS_CHECKBOX_LOCATOR, flag);
    }

    @Step("Click scan button")
    public void clickScanButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SCAN_BUTTON_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Choose scanned name from result table")
    public void chooseScannedNameFromResultTable(RemoteWebDriver driver, String scannedName) {
        Allure.step("Choose scanned name from result table " + scannedName);
        WebElement scannedNameElement = controls.getWebElement(driver, TABLE_LOCATOR).findElement(By.xpath("//span[.='" + scannedName + "']"));
        controls.performClick(scannedNameElement);
    }

    @Step("Get detection status")
    public String getDetectionStatus(RemoteWebDriver driver) {
        wait.waitUntilAjaxLoaderDisappear(driver);
        return controls.getElementText(driver, DETECTION_STATUS_LOCATOR);
    }

    @Step("Get detection status")
    public String getDetectionStatus_4eyes(RemoteWebDriver driver) {
        return controls.getElementText(driver, DETECTION_STATUS_4_EYES_LOCATOR);
    }

    @Step("Get detection rank for scanned name")
    public String getDetectionRank(RemoteWebDriver driver) {
        String rank = controls.getElementText(driver, RANK_LOCATOR);
        Allure.step("Rank for the scanned name = " + rank);
        return rank;
    }

    @Step("Get detection ID for scanned name")
    public String getDetectionID(RemoteWebDriver driver) {
        String detectionID = controls.getElementText(driver, DETECTION_ID_LOCATOR);
        Allure.step("Detection ID for the scanned name = " + detectionID);
        return detectionID;
    }

    @Step("Get investigator for scanned name")
    public String getInvestigator(RemoteWebDriver driver) {
        String investigator = controls.getElementText(driver, INVESTIGATOR_LOCATOR);
        Allure.step("Investigator for the scanned name = " + investigator);
        return investigator;
    }

    @Step("Get rank by matched name")
    public String getRankByMatchedName(RemoteWebDriver driver, String matchedName) {
        String rank = controls.getElementText(driver, By.xpath(String.format(RANK_BY_MATCHED_NAME_LOCATOR, matchedName)));
        Allure.step("Rank for the matched name '" + matchedName + "' = " + rank);
        return rank;
    }
}
