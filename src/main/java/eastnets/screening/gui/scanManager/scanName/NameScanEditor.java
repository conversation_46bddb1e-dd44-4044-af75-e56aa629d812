package eastnets.screening.gui.scanManager.scanName;

import core.gui.Controls;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class NameScanEditor {

    private static final By GOOD_GUY_BUTTON_LOCATOR = By.id("scanManagerForm:violation_list:btnAddGG");
    private static final By ACCEPT_BUTTON_LOCATOR = By.id("scanManagerForm:Good_guy_management:btnAcceptGG");
    private static final By ACCEPT_AS_SHARED_BUTTON_LOCATOR = By.id("scanManagerForm:Good_guy_management:btnSharedAcceptGG");
    private static final By CLOSE_GOOD_GUY_BUTTON_LOCATOR = By.xpath("//*[@id='scanManagerForm:createGGDlg']//a[@aria-label='Close']");
    private static final By CLOSE_VIOLATION_DETECTION_BUTTON_LOCATOR = By.xpath("//*[@id='scanManagerForm:violationDetailDLG']//a[@aria-label='Close']");
    private static final By GG_CATEGORY_SELECT_LOCATOR = By.id("scanManagerForm:Good_guy_management:categoryCbx");
    private static final By MATCHED_ENTITY_LOCATOR = By.xpath("//*[@id = 'scanManagerForm:Good_guy_management:goodGuysTable_data']//td[4]");

    private final Controls controls;

    public NameScanEditor() {
        this.controls = new Controls();
    }

    @Step("Click Good Guy Button")
    public void clickGGButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, GOOD_GUY_BUTTON_LOCATOR);
    }

    @Step("Select Goodguy Category")
    public void selectGGCategory(RemoteWebDriver driver, String category) throws Exception {
        controls.selectOptionByDisplayedText(driver, GG_CATEGORY_SELECT_LOCATOR, category);
    }

    @Step("Click Accept As Good Guy Button")
    public void clickAcceptAsSharedButton(RemoteWebDriver driver) {
        controls.scrollDown(driver);
        controls.performClickByJS(driver, ACCEPT_AS_SHARED_BUTTON_LOCATOR);
    }

    @Step("Click Accept button")
    public void clickAcceptButton(RemoteWebDriver driver) {
        controls.scrollDown(driver);
        controls.performClickByJS(driver, ACCEPT_BUTTON_LOCATOR);
    }

    @Step("Click close Good Guy button")
    public void closeGoodGuyEditor(RemoteWebDriver driver) {
        controls.performClickByJS(driver, CLOSE_GOOD_GUY_BUTTON_LOCATOR);
    }

    @Step("Click close violation detection button")
    public void closeViolationDetection(RemoteWebDriver driver) {
        controls.performClickByJS(driver, CLOSE_VIOLATION_DETECTION_BUTTON_LOCATOR);
    }

    @Step("Get Matched Entity Name")
    public String getMatchedEntityName(RemoteWebDriver driver) {
        return controls.getElementText(driver, MATCHED_ENTITY_LOCATOR);
    }

}