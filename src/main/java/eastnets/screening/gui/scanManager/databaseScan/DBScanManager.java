package eastnets.screening.gui.scanManager.databaseScan;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;


public class DBScanManager {

    private static final By ZONE_SELECT_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:Tab_DBScan:zoneCbx");
    private static final By LIST_SET_SELECT_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:Tab_DBScan:listContainerCbx");
    private static final By SEARCH_BUTTON_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:Tab_DBScan:btnDBScanSearch");

    private static final By DB_NAME_INPUT_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:Tab_DBScan:dbName");
    private static final By HOST_INPUT_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:Tab_DBScan:hostName");
    private static final By PORT_INPUT_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:Tab_DBScan:port");
    private static final By USER_NAME_INPUT_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:Tab_DBScan:userName");
    private static final By PASSWORD_INPUT_LOGIN_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:Tab_DBScan:password");
    private static final By PASSWORD_OUTPUT_LOGIN_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:Tab_DBScan:outputPassword");

    private static final By TEST_CONNECTION_INPUT_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:Tab_DBScan:btnInputLogin");
    private static final By TEST_CONNECTION_OUTPUT_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:Tab_DBScan:btnOutputLogin");

    private static final By FIELD_NAME_INPUT_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:Tab_DBScan:fieldName");
    private static final By OPERATOR_SELECT_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:Tab_DBScan:operator_input");
    private static final By VALUE_INPUT_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:Tab_DBScan:fieldValue");
    private static final By DETECT_VESSELS_CHECK_BOX_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:Tab_DBScan:vessels");
    private static final By DETECT_COUNTRIES_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:Tab_DBScan:countries");
    private static final By SAVE_VALUES_INPUT_LOCATORS = By.id("scanManagerForm:homepage_business:tabViewScanManager:Tab_DBScan:btnOutputAdd");
    private static final By START_SCAN_BUTTON_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:Tab_DBScan:btnStartScan");
    private static final By TEST_CONNECTION_VALIDATION_MASSAGE_LOCATOR = By.xpath("//*[@class='ui-outputlabel ui-widget basicLabel infoLabel infoMessage']");
    private static final By RUN_TYPE_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:Tab_DBScan:dbScanRunType");
    private static final By RUN_START_DATE_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:Tab_DBScan:startRunDate");
    private static final By MINUTE_PICKER_UP_LOCATOR = By.cssSelector(".ui-minute-picker .ui-picker-up");

    private final Controls controls;
    private final Wait wait;

    public DBScanManager() {
        this.controls = new Controls();
        this.wait = new Wait();
    }


    @Step("Click Search Button")
    public void clickSearchButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SEARCH_BUTTON_LOCATOR);
    }

    @Step("Click Test Connection Button")
    public String click_test_connection_input_flow(RemoteWebDriver driver) {
        controls.performClickByJS(driver, TEST_CONNECTION_INPUT_LOCATOR);
        String validationMessage = controls.getLabelValue(driver, TEST_CONNECTION_VALIDATION_MASSAGE_LOCATOR);
        Allure.step(String.format("Validation Message That Appear After click Test Connection Button = %s.", validationMessage));
        return validationMessage;
    }

    @Step("Click Test Connection Output Button")
    public String click_test_connection_output_flow(RemoteWebDriver driver) {
        controls.performClickByJS(driver, TEST_CONNECTION_OUTPUT_LOCATOR);
        String validationMessage = controls.getLabelValue(driver, TEST_CONNECTION_VALIDATION_MASSAGE_LOCATOR);
        Allure.step(String.format("Validation Message That Appear After click Test Connection Button = %s.", validationMessage));
        return validationMessage;
    }

    @Step("Select Zone")
    public void selectZone(RemoteWebDriver driver, String zone) throws Exception {
        controls.selectOptionByDisplayedText(driver, ZONE_SELECT_LOCATOR, zone);

    }


    @Step("Select List-Set")
    public void selectListSet(RemoteWebDriver driver, String listSet) throws Exception {
        controls.selectOptionByDisplayedText(driver, LIST_SET_SELECT_LOCATOR, listSet);
    }

    @Step("Set database name")
    public void setDatabaseName(RemoteWebDriver driver, String dbName) {
        controls.setTextBoxValue(driver, DB_NAME_INPUT_LOCATOR, dbName);
    }

    @Step("Set database host")
    public void setDatabaseHost(RemoteWebDriver driver, String host) {
        controls.setTextBoxValue(driver, HOST_INPUT_LOCATOR, host);
    }

    @Step("Set database port")
    public void setDatabasePort(RemoteWebDriver driver, String port) {
        controls.setTextBoxValue(driver, PORT_INPUT_LOCATOR, port);
    }

    @Step("Set database user name")
    public void setDatabaseUserName(RemoteWebDriver driver, String userName) {
        controls.setTextBoxValue(driver, USER_NAME_INPUT_LOCATOR, userName);
    }


    @Step("Set database password")
    public void set_password_for_input(RemoteWebDriver driver, String password) {
        controls.setTextBoxValue(driver, PASSWORD_INPUT_LOGIN_LOCATOR, password);
    }

    @Step("Set database password for output login information")
    public void set_password_for_output(RemoteWebDriver driver, String password) {
        controls.setTextBoxValue(driver, PASSWORD_OUTPUT_LOGIN_LOCATOR, password);
    }


    @Step("Set field name")
    public void setFieldName(RemoteWebDriver driver, String fieldName) {
        controls.setTextBoxValue(driver, FIELD_NAME_INPUT_LOCATOR, fieldName);
    }

    @Step("Select Operator")
    public void selectOperator(RemoteWebDriver driver, String operator) throws Exception {
        controls.selectOptionByDisplayedText(driver, OPERATOR_SELECT_LOCATOR, operator);
    }

    @Step("Set value")
    public void setValue(RemoteWebDriver driver, String value) {
        controls.setTextBoxValue(driver, VALUE_INPUT_LOCATOR, value);
    }

    @Step("Select detect vessels button")
    public void selectDetectVesselsCheckBox(RemoteWebDriver driver, String value) {

        if (value != "" && value != null) {
            controls.performClick(driver, DETECT_VESSELS_CHECK_BOX_LOCATOR);
        }
    }

    @Step("Select detect countries button")
    public void selectDetectCountriesCheckBox(RemoteWebDriver driver, String value) {

        if (value != "" && value != null) {
            controls.performClick(driver, DETECT_COUNTRIES_LOCATOR);
        }
    }

    @Step("Click save button")
    public void clickSaveButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SAVE_VALUES_INPUT_LOCATORS);
    }

    @Step("Select Run Type")
    public void selectRunType(RemoteWebDriver driver, String run_type) throws Exception {
        controls.selectOptionByDisplayedText(driver, RUN_TYPE_LOCATOR, run_type);
    }

    @Step("Set Run Start Date")
    public void setRunStartDate(RemoteWebDriver driver)  {
        controls.performClick(driver, RUN_START_DATE_LOCATOR);
        controls.performClick(driver, MINUTE_PICKER_UP_LOCATOR);
    }


    @Step("Click On Start Scan Button")
    public void startScan(RemoteWebDriver driver) {
        controls.performClickByJS(driver, START_SCAN_BUTTON_LOCATOR);
    }

    public void selectDBFlow(RemoteWebDriver driver, String name) {
        Allure.step(String.format("Select DB Flow = '%s' from the avilable list ", name));
        controls.performClickByJS(driver, By.xpath(String.format("//a[.='%s']", name)));
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

}