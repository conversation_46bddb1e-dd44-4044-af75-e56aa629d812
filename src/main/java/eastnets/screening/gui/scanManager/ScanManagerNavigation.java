package eastnets.screening.gui.scanManager;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public enum ScanManagerNavigation {


    NAME_CHECKER("//a[contains(text(),'Name Checker')]",
            "scanManagerForm:homepage_business:tabViewScanManager:NameChecker_business:ncZoneId_label"),
    FILE_SCAN("//a[contains(text(),'File Scan')]",
            "scanManagerForm:homepage_business:tabViewScanManager:Tab_FileScan:scanFile_label"),
    DB_SCAN("//a[contains(text(),'DB Scan')]",
            "scanManagerForm:homepage_business:tabViewScanManager:Tab_DBScan:zoneCbx"),
    EMW_SCAN("//a[contains(text(),'EMW Scan')]",
            "scanManagerForm:homepage_business:tabViewScanManager:ReportingScanner_business:rsMsgType"),
    RESULT("//a[contains(text(),'Results')]",
            "scanManagerForm:homepage_business:tabViewScanManager:Tab_results:statusCbx_label");

    private final By navigationElement;
    private final By checkLabel;
    private final Controls controls;
    private final Wait wait;

    ScanManagerNavigation(String navigationElement, String checkLabel) {
        this.navigationElement = By.xpath(navigationElement);
        this.checkLabel = By.id(checkLabel);
        this.controls = new Controls();
        this.wait = new Wait();
    }


    @Step("Navigation")
    public boolean navigate(RemoteWebDriver driver) {
        controls.waitAndClick(driver, navigationElement, Wait.TEN_SECOND_DURATION);
        return wait.waitUntilElementToBeVisible(driver, checkLabel, Wait.ONE_MINUTE_DURATION).isDisplayed();
    }
}
