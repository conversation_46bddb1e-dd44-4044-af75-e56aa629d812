package eastnets.screening.gui.scanManager.fileScan;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class FileScanManager {

    private static final By BROWSE_BUTTON_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:Tab_FileScan:scanFile_input");
    private static final By FORMAT_INPUT_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:Tab_FileScan:formatCbx_label");
    private static final By ZONE_INPUT_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:Tab_FileScan:zoneId_label");
    private static final By ENCODING_INPUT_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:Tab_FileScan:encodingCbx_label");
    private static final By LIST_SET_INPUT_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:Tab_FileScan:listSetId_label");
    private static final By RESULTS_INPUT_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:Tab_FileScan:resultsCbx_label");
    private static final By RANK_INPUT_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:Tab_FileScan:fsRankCbx_label");
    private static final String DETECT_VESSELS_CHECK_BOX_LOCATOR = "scanManagerForm:homepage_business:tabViewScanManager:Tab_FileScan:vessels";
    private static final String DETECT_COUNTRIES_CHECK_BOX_LOCATOR = "scanManagerForm:homepage_business:tabViewScanManager:Tab_FileScan:countries";
    private static final String CREATE_ALERT_CHECK_BOX_LOCATOR = "scanManagerForm:homepage_business:tabViewScanManager:Tab_FileScan:autoAlert";
    private static final By START_SCAN_BUTTON_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:Tab_FileScan:btnFileScan");
    private static final By RESET_BUTTON_LOCATOR = By.id("scanManagerForm:homepage_business:tabViewScanManager:Tab_FileScan:btnResetFileScan");

    private final Controls controls;
    private final Wait wait;

    public FileScanManager() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    @Step("Click On Start Scan Button")
    public void clickStartScanButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, START_SCAN_BUTTON_LOCATOR);
    }

    @Step("Click On Reset Button")
    public void clickResetButton(RemoteWebDriver driver) throws InterruptedException {
        controls.performClickByJS(driver, RESET_BUTTON_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }


    @Step("Select zone")
    public void selectZone(RemoteWebDriver driver, String zone) throws Exception {
        controls.selectOptionByDisplayedText(driver, ZONE_INPUT_LOCATOR, zone);
    }

    @Step("Select list-set")
    public void selectListSet(RemoteWebDriver driver, String listSet) throws Exception {
        controls.selectOptionByDisplayedText(driver, LIST_SET_INPUT_LOCATOR, listSet);
    }


    @Step("Set scanned file path")
    public void setScannedFilePath(RemoteWebDriver driver, String filePath) {
        controls.setTextBoxValue(driver, BROWSE_BUTTON_LOCATOR, filePath);
    }

    @Step("Select format")
    public void selectFormat(RemoteWebDriver driver, String format) throws Exception {
        controls.selectOptionByDisplayedText(driver, FORMAT_INPUT_LOCATOR, format);
    }

    @Step("Select encoding")
    public void selectEncoding(RemoteWebDriver driver, String encoding) throws Exception {
        controls.selectOptionByDisplayedText(driver, ENCODING_INPUT_LOCATOR, encoding);
    }

    @Step("Select results")
    public void selectResults(RemoteWebDriver driver, String results) throws Exception {
        controls.selectOptionByDisplayedText(driver, RESULTS_INPUT_LOCATOR, results);
    }

    @Step("Select rank")
    public void selectRank(RemoteWebDriver driver, String rank) throws Exception {
        controls.selectOptionByDisplayedText(driver, RANK_INPUT_LOCATOR, rank);
    }


    @Step("Select detect vessels option")
    public void selectDetectVessels(RemoteWebDriver driver, boolean flag) {
        controls.setCheckboxValueById(driver, DETECT_VESSELS_CHECK_BOX_LOCATOR, flag);
    }


    @Step("Select detect countries option")
    public void selectDetectCountries(RemoteWebDriver driver, boolean flag) {
        controls.setCheckboxValueById(driver, DETECT_COUNTRIES_CHECK_BOX_LOCATOR, flag);
    }


    @Step("Select create alert  option")
    public void selectCreateAlert(RemoteWebDriver driver, boolean flag) {
        controls.setCheckboxValueById(driver, CREATE_ALERT_CHECK_BOX_LOCATOR, flag);
    }


}
