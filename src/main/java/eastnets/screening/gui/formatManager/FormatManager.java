package eastnets.screening.gui.formatManager;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class FormatManager {

    private static final By ZONE_SELECT_LOCATOR = By.id("FormatManagerForm:Homepage_business:zoneCbx");
    private static final By SEARCH_BUTTON_LOCATOR = By.id("FormatManagerForm:Homepage_business:btnSearch");
    private static final By ADD_NEW_FORMAT_BUTTON_LOCATOR = By.id("FormatManagerForm:Homepage_business:Formats:_btnAddFormat");
    private static final By DELETE_FORMAT_BUTTON_LOCATOR = By.id("FormatManagerForm:Homepage_business:Formats:_btnDelFormat");
    private static final By EXPORT_BUTTON_LOCATOR = By.id("FormatManagerForm:Homepage_business:Formats:_btnExportFormat");
    private static final By BROWSE_BUTTON_LOCATOR = By.id("FormatManagerForm:Homepage_business:Formats:uploadFile_input");
    private static final By ZONE_IMPORT_SELECT_LOCATOR = By.id("FormatManagerForm:Homepage_business:Formats:ZoneImport");
    private static final By IMPORT_BUTTON_LOCATOR = By.id("FormatManagerForm:Homepage_business:Formats:_btnImportFormat");
    private static final By LAST_PAGE_LOCATOR = By.xpath("//*[contains(@class,'navigation-btn')][4]");
    private static final String CHECKBOX_LOCATOR = "//*[.='%s']//ancestor::tr[1]//td//div[@class='ui-selectbooleancheckbox ui-chkbox ui-widget']";

    private final Controls controls;
    private final Wait wait;

    public FormatManager() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    @Step("Select Zone")
    public void selectZone(RemoteWebDriver driver, String zoneName) throws Exception {
        controls.selectOptionByDisplayedText(driver, ZONE_SELECT_LOCATOR, zoneName);
    }

    @Step("Click Search Button")
    public void clickSearchButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SEARCH_BUTTON_LOCATOR);
    }

    @Step("Click Add New Format Button")
    public void clickAddNewFormatButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, ADD_NEW_FORMAT_BUTTON_LOCATOR);

    }

    @Step("Click Last Page Button")
    public void clickLastPageButton(RemoteWebDriver driver) {
        if (controls.getWebElement(driver, LAST_PAGE_LOCATOR).isDisplayed())
            controls.performClickByJS(driver, LAST_PAGE_LOCATOR);
    }

    @Step("Verify Format Exist")
    public boolean verifyFormatExist(RemoteWebDriver driver, String formatName) {
        wait.waitUntilAjaxLoaderDisappear(driver);
        return controls.exists(driver, By.xpath(String.format("//tbody//tr//td[.='%s']", formatName)));
    }

    @Step("Set browser button to upload a file")
    public void setBrowserButton(RemoteWebDriver driver, String filePath) {
        controls.setTextBoxValue(driver, BROWSE_BUTTON_LOCATOR, filePath);
    }

    @Step("Select Zone to Import a Format")
    public void selectZoneImport(RemoteWebDriver driver, String zoneName) throws Exception {
        controls.selectOptionByDisplayedText(driver, ZONE_IMPORT_SELECT_LOCATOR, zoneName);
    }

    @Step("Click 'Import' button")
    public void clickImportButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, IMPORT_BUTTON_LOCATOR);
    }

    @Step("Click Checkbox")
    public void clickCheckbox(RemoteWebDriver driver, String formatName) throws Exception {
        controls.performClick(driver, By.xpath(String.format(CHECKBOX_LOCATOR, formatName)));
    }

    @Step("Click 'Export' button")
    public void clickExportButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, EXPORT_BUTTON_LOCATOR);
    }

    @Step("Click 'Delete' button")
    public void clickDeleteButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, DELETE_FORMAT_BUTTON_LOCATOR);
        driver.switchTo().alert().accept();
    }
}