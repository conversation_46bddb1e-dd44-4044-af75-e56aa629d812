package eastnets.screening.gui.formatManager;

import core.gui.Controls;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class FormatEditor {

    private static final By ZONE_INPUT_LOCATOR = By.id("FormatManagerForm:Homepage_business:FormatDetails:ZoneCbx");
    private static final By FORMAT_NAME_LOCATOR = By.id("FormatManagerForm:Homepage_business:FormatDetails:format");
    private static final By FORMAT_TYPE_LOCATOR = By.id("FormatManagerForm:Homepage_business:FormatDetails:FormatTypeCbx_label");
    private static final By RECORD_DELIMITER_LOCATOR = By.id("FormatManagerForm:Homepage_business:FormatDetails:RecordDelimCbx");
    private static final By FIELD_DELIMITER_LOCATOR = By.id("FormatManagerForm:Homepage_business:FormatDetails:FieldDelimCbx_label");
    private static final By SEPARATOR_LOCATOR = By.id("FormatManagerForm:Homepage_business:FormatDetails:fieldSeparatorValue");
    private static final By ENTRY_TYPE_SELECT_LOCATOR = By.id("FormatManagerForm:Homepage_business:FormatDetails:EntityTypeCbx");
    private static final By ADD_NEW_FIELD_BUTTON_LOCATOR = By.id("FormatManagerForm:Homepage_business:FormatDetails:FieldDetails:Fields:_btnAddField");
    private static final By DELETE_FIELD_BUTTON_LOCATOR = By.id("FormatManagerForm:Homepage_business:FormatDetails:FieldDetails:Fields:_btnDelField");
    private static final By MOVE_UP_FIELD_BUTTON_LOCATOR = By.id("FormatManagerForm:Homepage_business:FormatDetails:FieldDetails:Fields:_btnUp");
    private static final By MOVE_DOWN_FIELD_BUTTON_LOCATOR = By.id("FormatManagerForm:Homepage_business:FormatDetails:FieldDetails:Fields:_btnDown");
    private static final By XPATH_INPUT_LOCATOR = By.id("FormatManagerForm:Homepage_business:FormatDetails:txtRecordXPathInputValue");
    private static final By FILED_XPATH_INPUT_LOCATOR = By.id("FormatManagerForm:Homepage_business:FormatDetails:FieldDetails:fieldXpathInput");
    private static final By FIELD_NAME_LOCATOR = By.id("FormatManagerForm:Homepage_business:FormatDetails:FieldDetails:fieldLabelInput");
    private static final By FIELD_TYPE_LOCATOR = By.id("FormatManagerForm:Homepage_business:FormatDetails:FieldDetails:fieldTypeInput_label");
    private static final String SCAN_CHECK_BOX_LOCATOR = "FormatManagerForm:Homepage_business:FormatDetails:FieldDetails:ScanChkBx";
    private static final String ADD_TO_CONTEXT_CHECK_BOX_LOCATOR = "FormatManagerForm:Homepage_business:FormatDetails:FieldDetails:ContextChkBx";
    private static final By SAVE_BUTTON_LOCATOR = By.id("FormatManagerForm:Homepage_business:FormatDetails:btnSave");

    private final Controls controls;

    public FormatEditor() {
        this.controls = new Controls();
    }

    @Step("Click Add New Field Button")
    public void clickAddNewFieldButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, ADD_NEW_FIELD_BUTTON_LOCATOR);
    }

    @Step("Click Delete Field Button")
    public void clickDeleteFieldButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, DELETE_FIELD_BUTTON_LOCATOR);
    }

    @Step("Click Move Up Field Button")
    public void clickMoveUpFieldButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, MOVE_UP_FIELD_BUTTON_LOCATOR);
    }

    @Step("Click Move Down Field Button")
    public void clickMoveDownFieldButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, MOVE_DOWN_FIELD_BUTTON_LOCATOR);
    }

    @Step("Set Zone")
    public void setZone(RemoteWebDriver driver, String zone) throws Exception {
        controls.selectOptionByDisplayedText(driver, ZONE_INPUT_LOCATOR, zone);
    }

    @Step("Set Format Name")
    public void setFormatName(RemoteWebDriver driver, String formatName) {
        controls.clearTextBoxValue(driver, FORMAT_NAME_LOCATOR);
        controls.setTextBoxValue(driver, FORMAT_NAME_LOCATOR, formatName);
    }

    @Step("Set Format Type")
    public void setFormatType(RemoteWebDriver driver, String formatType) throws Exception {
        controls.selectOptionByDisplayedText(driver, FORMAT_TYPE_LOCATOR, formatType);
    }

    @Step("Set Record Delimiter")
    public void setRecordDelimiter(RemoteWebDriver driver, String recordDelimiter) throws Exception {
        controls.selectOptionByDisplayedText(driver, RECORD_DELIMITER_LOCATOR, recordDelimiter);
    }

    @Step("Set Field Delimiter")
    public void setFieldDelimiter(RemoteWebDriver driver, String fieldDelimiter) throws Exception {
        controls.selectOptionByDisplayedText(driver, FIELD_DELIMITER_LOCATOR, fieldDelimiter);
    }

    @Step("Set Xpath")
    public void setXpath(RemoteWebDriver driver, String xpath) {
        controls.setTextBoxValue(driver, XPATH_INPUT_LOCATOR, xpath);
    }

    @Step("Set Separator")
    public void setSeparator(RemoteWebDriver driver, String separator) {
        controls.setTextBoxValue(driver, SEPARATOR_LOCATOR, separator);
    }

    @Step("Set Entry Type")
    public void setEntryType(RemoteWebDriver driver, String entryType) throws Exception {
        controls.selectOptionByDisplayedText(driver, ENTRY_TYPE_SELECT_LOCATOR, entryType);
    }

    @Step("Set Filed Name")
    public void setFiledName(RemoteWebDriver driver, String fieldName) {
        controls.setTextBoxValue(driver, FIELD_NAME_LOCATOR, fieldName);
    }

    @Step("Set Filed Xpath")
    public void setFiledXpath(RemoteWebDriver driver, String filedXpath) {
        controls.setTextBoxValue(driver, FILED_XPATH_INPUT_LOCATOR, filedXpath);
    }

    @Step("Set Filed Type")
    public void setFiledType(RemoteWebDriver driver, String fieldType) throws Exception {
        controls.scrollIntoViewJS(driver, FIELD_TYPE_LOCATOR);
        controls.selectOptionByDisplayedText(driver, FIELD_TYPE_LOCATOR, fieldType);
    }

    @Step("Set Scan Check Box")
    public void setScanCheckBox(RemoteWebDriver driver, boolean scan) {
        controls.setCheckboxValueById(driver, SCAN_CHECK_BOX_LOCATOR, scan);
    }

    @Step("Set Add To Context Check Box")
    public void setAddToContextCheckBox(RemoteWebDriver driver, boolean addToContext) {
        controls.setCheckboxValueById(driver, ADD_TO_CONTEXT_CHECK_BOX_LOCATOR, addToContext);
    }

    @Step("Click Save Button")
    public void clickSaveButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SAVE_BUTTON_LOCATOR);
        driver.switchTo().alert().accept();
    }
}