package eastnets.screening.gui.detectionManager;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.util.ArrayList;
import java.util.List;


public class DetectionEditor {

    private static final By RELEASE_DETECTION_BUTTON_LOCATOR = By.xpath("//*[@id='auditManagerForm:homepage_business:Detections_list:actionBar']//span[text() = 'Release']");
    private static final By RELEASE_ALERT_BUTTON_LOCATOR = By.xpath("//*[@id='auditManagerForm:homepage_business:Detections_list:violation_list:violation_list_panel']//span[text() = 'Release']");
    private static final By PENDING_DETECTION_BUTTON_LOCATOR = By.xpath("//*[@id='auditManagerForm:homepage_business:Detections_list:actionBar']//span[text()= 'Pending']");
    private static final By BLOCK_BUTTON_LOCATOR = By.xpath("//*[@id='auditManagerForm:homepage_business:Detections_list:actionBar']//button[. = 'Block']");
    private static final By BLOCK_ALERT_BUTTON_LOCATOR = By.xpath("//*[@id='auditManagerForm:homepage_business:Detections_list:violation_list:actionsPanel']//span[text() = 'Block']");
    private static final By STRIPPING_DETECTION_BUTTON_LOCATOR = By.xpath("//*[@id='auditManagerForm:homepage_business:Detections_list:actionBar']//span[text() = 'Stripping']");
    private static final By REASON_LOCATOR = By.id("auditManagerForm:homepage_business:Detections_list:detectionActionDialog:text");
    private static final By ADD_REPEAT_COMMENT_LOCATOR = By.id("auditManagerForm:homepage_business:Detections_list:repeatView:comment");
    private static final By SAVE_BUTTON_LOCATOR = By.id("auditManagerForm:homepage_business:Detections_list:detectionActionDialog:btnNoteEditorSave");
    private static final By ADD_REPEAT_DATA_SAVE_BUTTON_LOCATOR = By.id("auditManagerForm:homepage_business:Detections_list:repeatView:expiryDateEnabled");
    private static final By SAVE_REPEAT_BUTTON_LOCATOR = By.xpath("//*[@id = 'auditManagerForm:homepage_business:Detections_list:repeatDlg']//button[.='Save']");
    private static final By OK_BUTTON_LOCATOR = By.xpath("//button[.='OK']");
    private static final By DONT_KNOW_DETECTION_BTN = By.xpath("//*[@id='auditManagerForm:homepage_business:Detections_list:actionBar']//span[text() = \"Don't Know\"]");
    private static final By ASSIGN_TO_SELECT_LOCATOR = By.xpath("//div//div[contains(@id,'auditManagerForm:homepage_business:Detections_list:DetectionButtons')]");
    private static final By ASSIGN_DETECTION_BTN_LOCATOR = By.xpath("//*[.='Assign']//ancestor::button[contains(@id,'auditManagerForm:homepage_business:Detections_list:DetectionButtons')]");
    private static final By ALERT_BUTTON = By.id("auditManagerForm:homepage_business:Detections_list:violation_list:btnAlert");
    private static final By ADD_GG_BUTTON_LOCATOR = By.id("auditManagerForm:homepage_business:Detections_list:violation_list:btnAddGG");
    private static final By SCAN_GG_BUTTON_LOCATOR = By.id("auditManagerForm:homepage_business:Detections_list:Good_guy_management:btnScanGG");
    private static final By ACCEPT_GG_BUTTON_LOCATOR = By.id("auditManagerForm:homepage_business:Detections_list:Good_guy_management:btnAcceptGG");
    private static final By CLOSE_FORM_BUTTON_LOCATOR = By.xpath("//*[@id='auditManagerForm:homepage_business:Detections_list:createGGDlg']//*[contains(@class,'ui-icon-closethick')]");
    private static final By FIRST_LISTED_DETECTION_ID_BTN = By.xpath("//td[@role='gridcell'][2]");
    private static final By FIRST_ROW_FIRST_TABLE_LOCATOR = By.id("auditManagerForm:homepage_business:Detections_list:detectionResultsTable_data");
    private static final By RELATED_RECORD_LOCATOR = By.linkText("Related Record");
    private static final By RELATED_MESSAGE_DETECTION_ID_LOCATOR = By.xpath("//span[contains(text(),'Related message detection ID:')]");
    private static final By RELATED_MESSAGE_DETECTION_STATUS_LOCATOR = By.xpath("//span[contains(text(),'Related message detection status:')]");
    private static final By RELATED_RECORD_DETAIL_LOCATOR = By.xpath("//span[contains(text(),'Related record:')]");
    private static final By STRUCTURED_RECORDS_TAB_LOCATOR = By.linkText("Structured Record");
    private static final By SCANNED_RECORDS_TAB_LOCATOR = By.linkText("Scanned Record");
    private static final By SCANNED_RECORDS_FILED_LOCATOR = By.xpath("//*[@id='auditManagerForm:homepage_business:Detections_list:violation_list:view:scannedStructuredTab:ScannedRecordTab']//span");
    private static final By HIGHLIGHTED_DATA_SCANNED_RECORDS_FILED_LOCATOR = By.xpath("//*[@id='auditManagerForm:homepage_business:Detections_list:violation_list:view:scannedStructuredTab:ScannedRecordTab']//span[@class ='highlight']");
    private static final By STRUCTURED_RECORDS_ROWS_LOCATOR = By.xpath("//*[@id='auditManagerForm:homepage_business:Detections_list:violation_list:view:scannedStructuredTab:Tab_structured_record:_tblStructuredRecords_data']//tr");
    public static final By PENDING_ALERT_BUTTON_LOCATOR = By.id("auditManagerForm:homepage_business:Detections_list:violation_list:btnPending");
    private static final By ATTACHMENT_TAB_LOCATOR = By.linkText("Attachments");
    private static final By ATTACHMENT_UPLOAD_BTN_LOCATOR = By.id("auditManagerForm:homepage_business:Detections_list:violation_list:view:alertTab:Tab_attachments:table:upload");
    private static final By BROWSE_BTN_LOCATOR = By.xpath("//input[@type='file']");
    private static final By DESCRIPTION_UPLOAD_INPUT_LOCATOR = By.id("auditManagerForm:homepage_business:Detections_list:uploadFile:description");
    private static final By SAVE_BTN_UPLOAD_LOCATOR = By.xpath("//*[@class = 'uploadFileBtnsWrapper']//span[.='Save']");
    private static final String UPLOAD_ATTACHMENT_VALIDATION_LOCATOR = "//*[@id = 'auditManagerForm:homepage_business:Detections_list:violation_list:view:alertTab:Tab_attachments:table_data']//*[.='%s']";
    private static final By COMMENT_BUTTON_LOCATOR = By.id("auditManagerForm:homepage_business:Detections_list:violation_list:btnComment");
    private static final String ADD_COMMENT_VALIDATION_LOCATOR = "//*[@id = 'auditManagerForm:homepage_business:Detections_list:violation_list:view:alertTab:Tab_notes:_tblAlertNotes_data']//*[.='%s']";
    private static final By EXPORT_ALERTS_BUTTON_LOCATOR = By.id("auditManagerForm:homepage_business:Detections_list:violation_list:exportViolationBtn");
    private static final String PRINT_SCOPE_LOCATOR = "//*[@id= 'auditManagerForm:homepage_business:Detections_list:exportViolation:printMode']//input[@value='%s']//ancestor::div[1]";
    private static final By PRINT_BUTTON_LOCATOR = By.id("auditManagerForm:homepage_business:Detections_list:exportViolation:btnPrint");
    private static final By PRINT_TYPE_SELECT_LOCATOR = By.id("auditManagerForm:homepage_business:Detections_list:exportViolation:exportType");
    private static final By CLOSE_EXPORT_VIOLATION_BTN_LOCATOR = By.xpath("//*[@id ='auditManagerForm:homepage_business:Detections_list:exportViolationDlg']//*[@class = 'ui-icon ui-icon-closethick']");
    private static final By EXPORT_DETECTIONS_BTN_LOCATOR = By.id("auditManagerForm:homepage_business:Detections_list:detectionResultsTable:exportDetectionBtn");
    private static final String PRINT_SCOPE_FOR_DETECTION_LOCATOR = "//*[@id= 'auditManagerForm:homepage_business:Detections_list:detectionResultsTable:printMode']//input[@value='%s']//ancestor::div[1]";
    private static final By PRINT_TYPE_DETECTION_SELECT_LOCATOR = By.id("auditManagerForm:homepage_business:Detections_list:detectionResultsTable:exportType");
    private static final By PRINT_DETECTION_BUTTON_LOCATOR = By.id("auditManagerForm:homepage_business:Detections_list:detectionResultsTable:btnPrint");
    private static final By CHECKER_SELECT_LOCATOR = By.id("auditManagerForm:homepage_business:Detections_list:detectionActionDialog:investigator");
    private static final By BULK_ASSIGN_BUTTON_LOCATOR = By.xpath(" //*[.='Bulk Assign']//ancestor::button[contains(@id,'auditManagerForm:homepage_business:Detections_list:DetectionButtons')]");
    private static final By DETECTION_TYPE_REGULAR_LOCATOR = By.xpath("//*[@id='auditManagerForm:homepage_business:Detections_list:violation_list:violation_list_panel']//span[text() = 'Regular']");
    private static final By DETECTION_TYPE_4EYES_LOCATOR = By.xpath("//label[@for='auditManagerForm:homepage_business:Detections_list:BulkAssignDetections:detectionTypeRadio:1']");
    private static final By FROM_INVESTIGATOR_DROPDOWN_LOCATOR = By.xpath("//input[@type='text' and contains(@class, 'ui-inputfield ui-inputtext ui-widget ui-state-default ui-corner-all bulk-assign-input-text-new-assignments ui-state-filled')]");
    private static final By SEARCH_BULK_ASSIGN_DETECTION_BUTTON_LOCATOR = By.id("auditManagerForm:homepage_business:Detections_list:BulkAssignDetections:btnBASearch");
    private static final By FROM_CHECKER_RADIO_BUTTON = By.xpath("//label[@for='auditManagerForm:homepage_business:Detections_list:BulkAssignDetections:typeSOR:1']");
    private static final By NEW_INVESTIGATOR_DROPDOWN = By.xpath("//*[@id='auditManagerForm:homepage_business:Detections_list:BulkAssignDetections:_tblBulkDetectionsResult_data']//label[@class='ui-selectonemenu-label ui-inputfield ui-corner-all']");
    private static final By ASSIGN_BULK_DETECTION_BUTTON_LOCATOR = By.id("auditManagerForm:homepage_business:Detections_list:BulkAssignDetections:btnBAConfirm");
    private static final By OPEN_DETECTIONS_COUNT_LOCATOR = By.xpath("//tbody[@id='auditManagerForm:homepage_business:Detections_list:BulkAssignDetections:_tblBulkDetectionsResult_data']//td[2]");
    private static final By NEW_ASSIGNMENT_TEXTBOX_LOCATOR = By.xpath("//tbody[@id='auditManagerForm:homepage_business:Detections_list:BulkAssignDetections:_tblBulkDetectionsResult_data']//td[4]//input");
    private static final By CANCEL_BULK_ASSIGN_BUTTON_LOCATOR = By.xpath("//div[@id ='auditManagerForm:homepage_business:Detections_list:bulkAssignDlg']//button[.='Cancel']");
    private static final By DETECTION_LIST_INVESTIGATOR_LOCATOR = By.xpath("//tbody[@id='auditManagerForm:homepage_business:Detections_list:violation_list:violationList_ResultsTable_data']/tr/td[8]");
    private static final By SORT_BY_RANK_LOCATOR = By.xpath("//thead[@id='auditManagerForm:homepage_business:Detections_list:violation_list:violationList_ResultsTable_head']//th[contains(@aria-label,'Rank: undefined')]");
    private static final By FIRST_ALERTS_ROW_LOCATOR = By.xpath("//tbody[@id='auditManagerForm:homepage_business:Detections_list:violation_list:violationList_ResultsTable_data']/tr[1]");
    public static final By REGULAR_DETECTION_TYPE_INVESTIGATOR = By.xpath("//*[@id='auditManagerForm:homepage_business:Detections_list:BulkAssignDetections:invCbx_label']");
    private static final By MATCHED_ENTITY_LOCATOR = By.xpath("//*[@id='auditManagerForm:homepage_business:Detections_list:violation_list:violationList_ResultsTable_data']//tr[1]//td[6]");

    private final Controls controls;
    private final Wait wait;

    public DetectionEditor() {
        this.controls = new Controls();
        this.wait = new Wait();
    }


    @Step("Click first row in violations list")
    public void click_first_row_in_violations_list(RemoteWebDriver driver) {
        controls.performClick(driver, FIRST_ALERTS_ROW_LOCATOR);
    }

    @Step("Click sort by rank")
    public void click_sort_by_rank(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SORT_BY_RANK_LOCATOR);
    }

    @Step("Click on Good Guy button")
    public void click_good_guy_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, ADD_GG_BUTTON_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Click Block Button ")
    public void click_block_detection_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, BLOCK_BUTTON_LOCATOR);
    }

    @Step("Click cancel Bulk Assignment button")
    public void click_cancel_bulk_assign_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, CANCEL_BULK_ASSIGN_BUTTON_LOCATOR);
    }

    @Step("Click block alert button")
    public void click_block_alert_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, BLOCK_ALERT_BUTTON_LOCATOR);
    }

    @Step("Click release detection button")
    public void click_release_detection_button(RemoteWebDriver driver) {
        controls.performClick(driver, RELEASE_DETECTION_BUTTON_LOCATOR);
    }

    @Step("Click release alert button")
    public void click_release_alert_button(RemoteWebDriver driver) {
        controls.performClick(driver, RELEASE_ALERT_BUTTON_LOCATOR);
    }

    @Step("Click stripping button for detection")
    public void click_stripping_detection_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, STRIPPING_DETECTION_BUTTON_LOCATOR);
    }

    @Step("Click pending detection button")
    public void click_pending_detection_button(RemoteWebDriver driver) {
        controls.performClick(driver, PENDING_DETECTION_BUTTON_LOCATOR);
    }

    @Step("Click Don't Know Button")
    public void click_dont_know_detection_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, DONT_KNOW_DETECTION_BTN);
    }

    @Step("Select Checker")
    public boolean select_checker(RemoteWebDriver driver, String checkerName) throws Exception {
        return controls.selectOptionByPartialDisplayedText(driver, CHECKER_SELECT_LOCATOR, checkerName);

    }

    @Step("Set Reason Message")
    public void set_reason_message(RemoteWebDriver driver, String message) {
        controls.setTextBoxValueByJS(driver, REASON_LOCATOR, message);

    }

    @Step("Click save button")
    public void click_save_button(RemoteWebDriver driver) throws InterruptedException {
        controls.waitAndClick(driver, SAVE_BUTTON_LOCATOR, Wait.TEN_SECOND_DURATION);
    }

    @Step("Click Ok button")
    public void click_ok_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, OK_BUTTON_LOCATOR);
    }

    @Step("Set repeat message")
    public void set_repeat_message(RemoteWebDriver driver, String message) {
        controls.setTextBoxValueByJS(driver, ADD_REPEAT_COMMENT_LOCATOR, message);
    }

    @Step("Click save repeat button")
    public void click_save_repeat_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SAVE_REPEAT_BUTTON_LOCATOR);
    }


    @Step("Verify Block Button Exists")
    public boolean verify_block_button_exists(RemoteWebDriver driver) {
        return controls.exists(driver, BLOCK_BUTTON_LOCATOR);
    }

    @Step("Verify Block Button Enabled")
    public boolean verify_block_button_enabled(RemoteWebDriver driver) {
        return controls.isButtonClickable(driver, BLOCK_BUTTON_LOCATOR);
    }

    @Step("Verify Release Button Exists")
    public boolean verify_release_button_exists(RemoteWebDriver driver) {
        return controls.exists(driver, RELEASE_DETECTION_BUTTON_LOCATOR);
    }

    @Step("Verify Repeat Button Exists")
    public boolean verify_repeat_button_exists(RemoteWebDriver driver) {
        return controls.exists(driver, STRIPPING_DETECTION_BUTTON_LOCATOR);
    }

    @Step("Click pending alert button")
    public void click_pending_alert_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, PENDING_ALERT_BUTTON_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Select assign to")
    public boolean select_assign_to(RemoteWebDriver driver, String assignee) throws Exception {
        return controls.selectOptionByPartialDisplayedText(driver, ASSIGN_TO_SELECT_LOCATOR, assignee);

    }

    @Step("Get assignee value")
    public String get_assignee_value(RemoteWebDriver driver) {
        return controls.getLabelValue(driver, ASSIGN_TO_SELECT_LOCATOR);
    }

    @Step("Click assign detection button")
    public void click_assign_detection_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, ASSIGN_DETECTION_BTN_LOCATOR);
    }

    @Step("Click add alert button")
    public void click_add_alert_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, ALERT_BUTTON);
    }

    @Step("Click scan good guy button")
    public void click_scan_good_guy_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SCAN_GG_BUTTON_LOCATOR);
    }

    @Step("Click accept good guy button")
    public void click_accept_good_guy_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, ACCEPT_GG_BUTTON_LOCATOR);
    }

    @Step("Close form")
    public void close_form(RemoteWebDriver driver) {
        controls.performClickByJS(driver, CLOSE_FORM_BUTTON_LOCATOR);
    }

    @Step("Click on attachment tab")
    public void click_attachment_tab(RemoteWebDriver driver) {
        controls.performClickByJS(driver, ATTACHMENT_TAB_LOCATOR);
    }

    @Step("Click upload button")
    public void click_upload_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, ATTACHMENT_UPLOAD_BTN_LOCATOR);
    }

    @Step("Set browse file path")
    public void set_browse_file_path(RemoteWebDriver driver, String filePath) {
        controls.setTextBoxValue(driver, BROWSE_BTN_LOCATOR, filePath);
    }

    @Step("Set description")
    public void set_attachment_description(RemoteWebDriver driver, String description) {
        controls.setTextBoxValueByJS(driver, DESCRIPTION_UPLOAD_INPUT_LOCATOR, description);
    }

    @Step("Click Save Button For Attachment")
    public void click_save_button_for_attachment(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SAVE_BTN_UPLOAD_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("verify attachment exists")
    public boolean verify_attachment_exists(RemoteWebDriver driver, String description) {
        return controls.exists(driver, By.xpath(String.format(UPLOAD_ATTACHMENT_VALIDATION_LOCATOR, description)));
    }

    @Step("Click comment button")
    public void click_add_comment_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, COMMENT_BUTTON_LOCATOR);
    }

    @Step("Verify comment exist")
    public boolean verify_comment_exist(RemoteWebDriver driver, String comment) {
        wait.waitUntilAjaxLoaderDisappear(driver);
        return controls.exists(driver, By.xpath(String.format(ADD_COMMENT_VALIDATION_LOCATOR, comment)));
    }

    @Step("Click export detection alerts button")
    public void click_export_detection_alerts_button(RemoteWebDriver driver) {
        controls.scrollDown(driver);
        controls.performClickByJS(driver, EXPORT_ALERTS_BUTTON_LOCATOR);
    }

    @Step("select print scope")
    public void select_print_scope(RemoteWebDriver driver, String printScope) {
        WebElement webElement = controls.getWebElement(driver, By.xpath(String.format(PRINT_SCOPE_LOCATOR, printScope)));

        if (webElement.getAttribute("class").contains("ui-state-active")) {
            controls.performClick(driver, By.xpath(String.format(PRINT_SCOPE_LOCATOR, printScope)));
            controls.performClick(driver, By.xpath(String.format(PRINT_SCOPE_LOCATOR, printScope)));
        } else
            controls.performClick(driver, By.xpath(String.format(PRINT_SCOPE_LOCATOR, printScope)));
    }

    @Step("Select export type")
    public void select_export_type(RemoteWebDriver driver, String printType) throws Exception {
        controls.selectOptionByDisplayedText(driver, PRINT_TYPE_SELECT_LOCATOR, printType);
    }

    @Step("Click print button")
    public void click_print_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, PRINT_BUTTON_LOCATOR);
    }

    @Step("Close Export Violation")
    public void closeExportViolation(RemoteWebDriver driver) {
        controls.performClickByJS(driver, CLOSE_EXPORT_VIOLATION_BTN_LOCATOR);
    }

    @Step("Click export detection button")
    public void click_export_detection_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, EXPORT_DETECTIONS_BTN_LOCATOR);
    }

    @Step("Select print scope")
    public void select_print_scope_for_detection(RemoteWebDriver driver, String printScope) {
        WebElement webElement = controls.getWebElement(driver, By.xpath(String.format(PRINT_SCOPE_FOR_DETECTION_LOCATOR, printScope)));

        if (webElement.getAttribute("class").contains("ui-state-active")) {
            controls.performClick(driver, By.xpath(String.format(PRINT_SCOPE_FOR_DETECTION_LOCATOR, printScope)));
            controls.performClick(driver, By.xpath(String.format(PRINT_SCOPE_FOR_DETECTION_LOCATOR, printScope)));
        } else
            controls.performClick(driver, By.xpath(String.format(PRINT_SCOPE_FOR_DETECTION_LOCATOR, printScope)));

    }

    @Step("Select print type")
    public void select_print_type_for_detection(RemoteWebDriver driver, String printType) throws Exception {
        controls.selectOptionByDisplayedText(driver, PRINT_TYPE_DETECTION_SELECT_LOCATOR, printType);
    }

    @Step("Click print button")
    public void click_print_button_for_detection(RemoteWebDriver driver) {
        controls.performClickByJS(driver, PRINT_DETECTION_BUTTON_LOCATOR);
    }

    @Step("Get Matched Entity")
    public String get_matched_entity(RemoteWebDriver driver) {
        return controls.getElementText(driver, MATCHED_ENTITY_LOCATOR);
    }

    @Step("Click On Related Record")
    public void click_on_related_record(RemoteWebDriver driver) {
        controls.performClick(driver, RELATED_RECORD_LOCATOR);
    }

    @Step("Get Related Message Detection ID")
    public String get_related_message_detection_ID(RemoteWebDriver driver) {
        return controls.getElementText(driver, RELATED_MESSAGE_DETECTION_ID_LOCATOR);
    }

    @Step("Get Related Message Detection Status")
    public String get_related_message_detection_status(RemoteWebDriver driver) {
        return controls.getElementText(driver, RELATED_MESSAGE_DETECTION_STATUS_LOCATOR);
    }

    @Step("Select Detection Record By Data")
    public void select_detection_record_by_data(RemoteWebDriver driver, String dataString) {

        controls.performClick(driver, By.xpath(String.format("//*[@id='auditManagerForm:homepage_business:Detections_list:violation_list:violationList_ResultsTable_data']//td[.='%s' or @title='%s']", dataString, dataString)));
    }

    @Step("Check violation exit by Data")
    public boolean verify_violation_exist(RemoteWebDriver driver, String dataString) {

        return controls.exists(driver, By.xpath(String.format("//*[@title='%s']", dataString)));
    }

    @Step("Click scanned record tab")
    public void click_scanned_record_tab(RemoteWebDriver driver) {

        controls.scrollIntoViewJS(driver, SCANNED_RECORDS_TAB_LOCATOR);
        controls.performClickByJS(driver, SCANNED_RECORDS_TAB_LOCATOR);
    }

    @Step("Get scanned records data")
    public String get_scanned_record_data(RemoteWebDriver driver) {
        return controls.getElementText(driver, SCANNED_RECORDS_FILED_LOCATOR);
    }

    @Step("Get highlighted data from scanned records section")
    public String get_highlighted_data_scanned_record(RemoteWebDriver driver) {
        return controls.getElementText(driver, HIGHLIGHTED_DATA_SCANNED_RECORDS_FILED_LOCATOR);
    }

    @Step("Bulk assign Detections")
    public void click_on_bulk_assign(RemoteWebDriver driver) {
        controls.performClickByJS(driver, BULK_ASSIGN_BUTTON_LOCATOR);
    }

    @Step("Select 4eyes option as detection type")
    public void select_4_eyes_detection_type(RemoteWebDriver driver) {
        controls.performClickByJS(driver, DETECTION_TYPE_4EYES_LOCATOR);
    }

    @Step("Select 'From Checker' radio button")
    public void click_from_checker_radio_button(RemoteWebDriver driver) {
        controls.performClick(driver, FROM_CHECKER_RADIO_BUTTON);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Click Search Button")
    public void click_search_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SEARCH_BULK_ASSIGN_DETECTION_BUTTON_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }


    @Step("Select new investigator")
    public void select_new_investigator(RemoteWebDriver driver, String newInvestigator) throws Exception {
        controls.selectOptionByPartialDisplayedText(driver, NEW_INVESTIGATOR_DROPDOWN, newInvestigator);
    }

    @Step("Set new assignment")
    public void set_new_assignment(RemoteWebDriver driver, String new_assignment) {
        controls.setTextBoxValue(driver, NEW_ASSIGNMENT_TEXTBOX_LOCATOR, new_assignment);
    }

    @Step("Click bulk assign button")
    public void click_bulk_assign_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, ASSIGN_BULK_DETECTION_BUTTON_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Get investigator for scanned name from detection list")
    public String get_investigator(RemoteWebDriver driver) {
        return controls.getElementText(driver, DETECTION_LIST_INVESTIGATOR_LOCATOR);
    }


    @Step("Get Open detection  after assigning to diffrent operator")
    public String get_open_detection_count(RemoteWebDriver driver) {
        return controls.getElementText(driver, OPEN_DETECTIONS_COUNT_LOCATOR);

    }


    @Step("Verify detection id existence")
    public boolean verify_detection_id_exist(RemoteWebDriver driver, String detection_id) {
        return controls.exists(driver, By.xpath(String.format("//*[.='%s']", detection_id)));
    }

    @Step("Click on structured records tab")
    public void click_on_structured_records_tab(RemoteWebDriver driver) {
        controls.scrollIntoViewJS(driver, STRUCTURED_RECORDS_TAB_LOCATOR);
        controls.performClickByJS(driver, STRUCTURED_RECORDS_TAB_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Get Structured Records Data")
    public ArrayList<ArrayList<String>> get_structured_records_data(RemoteWebDriver driver) {

        click_on_structured_records_tab(driver);

        ArrayList<ArrayList<String>> structuredRecordsData = new ArrayList<>();
        ArrayList<String> structuredRecordRowData;
        List<WebElement> rowsElements = driver.findElements(STRUCTURED_RECORDS_ROWS_LOCATOR);
        for (WebElement rowElement : rowsElements) {
            structuredRecordRowData = new ArrayList<String>();
            List<WebElement> cellsElements = rowElement.findElements(By.tagName("td"));
            for (WebElement cellElement : cellsElements) {
                structuredRecordRowData.add(cellElement.getText());
            }
            structuredRecordsData.add(structuredRecordRowData);
        }
        Allure.step("Structured Records Data = " + structuredRecordsData);
        return structuredRecordsData;
    }

    @Step("Select Regular Detection Type")
    public static boolean select_regular_detection_type(RemoteWebDriver driver, String assign) throws Exception {
        return new Controls().selectOptionByDisplayedText(driver, REGULAR_DETECTION_TYPE_INVESTIGATOR, assign);
    }
}








