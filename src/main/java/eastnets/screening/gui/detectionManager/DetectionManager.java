package eastnets.screening.gui.detectionManager;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.openqa.selenium.support.ui.WebDriverWait;

import java.util.List;

public class DetectionManager {

    private static final By DETECTION_ID_INPUT_LOCATOR = By.id("auditManagerForm:homepage_business:detectionSearch:col2:0:detectionSearchFields:numberExl_input");
    private static final By SESSION_ID_LOCATOR = By.id("auditManagerForm:homepage_business:detectionSearch:col1:2:detectionSearchFields:sessionFinderComp:selectSession_input");
    private static final By SEARCH_BUTTON_LOCATOR = By.id("auditManagerForm:homepage_business:detectionSearch:btnSearch");
    private static final By RESET_BUTTON_LOCATOR = By.id("auditManagerForm:homepage_business:detectionSearch:btnReset");
    private static final By DETECTION_STATUS_LOCATOR = By.xpath("//*[contains(@id , 'auditManagerForm:homepage_business:Detections_list:detectionResultsTable:0:tagDet')]//ancestor::td[1]");
    private static final By DETECTION_CALENDER_IMAGE = By.xpath("//i[contains(@class,'fa fa-calendar')]");
    private static final By DETECTION_CALENDER_TODAY_BTN = By.xpath("//a[contains(text(),'Today')]");
    private static final String CONTAINS_OPEN_ALERT_CHECK_BOX_LOCATOR = "//*[@title ='When checked, detections that contain at least one alert without a final status will be displayed']";
    private static final By DETECTION_CALENDER_CLOSE_BTN = By.xpath("//button[@id='auditManagerForm:homepage_business:detectionSearch:col1:0:detectionSearchFields:dateRangeComp:closebtn']");
    private static final By FIRST_LISTED_DETECTION_ID_BTN = By.xpath("//td[@role='gridcell'][2]");
    private static final By SEARCH_OPTIONS_ICON_LOCATOR = By.xpath("//*[@id= 'auditManagerForm:homepage_business:detectionSearch:custom_header']//*[@title='Options']");
    private static final String SEARCH_OPTIONS_LOCATOR = "//*[@data-item-label='%s']";
    private static final By REMOVE_BUTTON_SEARCH_COLUMNS_DLG_LOCATOR2 = By.xpath("//*[@id='auditManagerForm:homepage_business:detectionSearch:tabSearchColumnDlg:column1Tab']//*[@title='Remove']");
    private static final By APPLY_BUTTON_SEARCH_COLUMNS_DLG_LOCATOR1 = By.xpath("//button[.='Apply']");
    private static final By CANCEL_BUTTON_SEARCH_COLUMNS_DLG_LOCATOR1 = By.xpath("//button[.='Cancel']");
    private static final By CURRENT_EXPANDED_MESSAGE_TEXT_ID_TXT = By.id("auditManagerForm:homepage_business:Detections_list:violation_list:view:scannedStructuredTab:Tab_scan_record:j_idt1203");
    private static final By DETECTION_ID_RESULT_TABLE_LOCATORS = By.xpath("//*[contains(@id,'detectionId')]");
    private static final String ROW_DATA_LOCATOR = "//*[@id='auditManagerForm:homepage_business:Detections_list:detectionResultsTable_data']//tr[%s]//td";
    private static final By OPTION_BUTTON_RESULT_TABLE_LOCATOR = By.id("auditManagerForm:homepage_business:Detections_list:detectionResultsTable:toggler");
    private static final String OPTION_CHECKBOX_LOCATOR = "//*[contains(@id,'auditManagerForm:homepage_business:Detections_list:detectionResultsTable_columnTogglerChkbx') and text()='%s']";
    private static final By UETR_INPUT_LOCATOR = By.xpath("//*[.='UETR:']//ancestor::div[1]//input[@role='textbox']");
    private static final By SERV_TYPE_ID_INPUT_LOCATOR = By.xpath("//*[.='Serv. Type Id:']//ancestor::div[1]//input[@role='textbox']");
    private static final By FORMAT_FILTER_BY_SELECT_LOCATOR = By.xpath("//div[contains(@id,':detectionSearchFields:selectOneFormatFilter') and  @role= 'combobox']");
    private static final By FORMAT_SELECT_LOCATOR = By.xpath("//*[.='Format:']//ancestor::div[1]//div[contains(@id,'detectionSearchFields:selectOneFormat')]");
    private static final By FOUR_EYES_OPTION_YES_NO = By.xpath("//*[contains(@id, 'detectionSearchFields:selectOneYesNo') and @role= 'combobox']");
    private static final By THREE_DOTS_MENU_LOCATOR = By.xpath("//*[contains(@id,'menuOnRow_button')]");
    private static final By DONT_KNOW_3_Dots_Menu_LOCATOR = By.xpath("//a[.=\"Don't Know\"]");
    private static final By TABLE_ROWS_LOCATOR = By.xpath("//*[@id = 'auditManagerForm:homepage_business:Detections_list:detectionResultsTable_data']//tr");
    private static final By VIOLATION_RESULT_TABLE_LOCATOR = By.xpath("//*[@id ='auditManagerForm:homepage_business:Detections_list:detectionResultsTable_data']//tr[1]//td[6]");

    private final Controls controls;
    private final Wait wait;

    public DetectionManager() {
        this.controls = new Controls();
        this.wait = new Wait();
    }


    @Step("Click search button")
    public void click_search_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SEARCH_BUTTON_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Click reset button")
    public void click_reset_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, RESET_BUTTON_LOCATOR);
        driver.switchTo().alert().accept();
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Set Id")
    public void set_id(RemoteWebDriver driver, String detectionId) {
        controls.setTextBoxValue(driver, DETECTION_ID_INPUT_LOCATOR, detectionId);
    }

    @Step("Set session id")
    public void set_session_id(RemoteWebDriver driver, String sessionId) {
        controls.setTextBoxValue(driver, SESSION_ID_LOCATOR, sessionId);
    }

    @Step("Set contains open alerts checkbox")
    public void set_contains_open_alert_checkbox(RemoteWebDriver driver, boolean flag) {
        controls.setCheckboxValueByXpath(driver, CONTAINS_OPEN_ALERT_CHECK_BOX_LOCATOR, flag);
    }

    @Step("Select format")
    public void select_format(RemoteWebDriver driver, String format) throws Exception {
        controls.selectOptionByDisplayedText(driver, FORMAT_SELECT_LOCATOR, format);
    }

    @Step("Select format filter by")
    public void select_format_filter_by(RemoteWebDriver driver, String formatFilterBy) throws Exception {
        new WebDriverWait(driver, Wait.TEN_SECOND_DURATION)
                .until(d -> controls.getWebElement(driver, FORMAT_FILTER_BY_SELECT_LOCATOR).findElement(By.tagName("input")).isEnabled());
        controls.selectOptionByDisplayedText(driver, FORMAT_FILTER_BY_SELECT_LOCATOR, formatFilterBy);
    }

    @Step("Set UETR")
    public void set_UETR(RemoteWebDriver driver, String uetr) {
        controls.setTextBoxValue(driver, UETR_INPUT_LOCATOR, uetr);
    }

    @Step("Set service type id")
    public void set_service_type_id(RemoteWebDriver driver, String servTypeId) {
        controls.setTextBoxValue(driver, SERV_TYPE_ID_INPUT_LOCATOR, servTypeId);
    }

    @Step("Get detection status")
    public String get_detection_status(RemoteWebDriver driver) {
        return controls.getElementText(driver, DETECTION_STATUS_LOCATOR);
    }

    @Step("Click on detection by id")
    public void click_on_detection(RemoteWebDriver driver, String detectionId) {
        controls.performClickByJS(driver, By.xpath("//*[.='" + detectionId + "']"));
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Click on first listed detection")
    public void click_on_first_listed_detection(RemoteWebDriver driver) {
        controls.performClick(driver, FIRST_LISTED_DETECTION_ID_BTN);
    }

    @Step("Select today date")
    public void select_today_date(RemoteWebDriver driver) {
        controls.performJsClick(driver, driver.findElement(DETECTION_CALENDER_IMAGE));
        controls.performJsClick(driver, driver.findElement(DETECTION_CALENDER_TODAY_BTN));
        controls.performJsClick(driver, driver.findElement(DETECTION_CALENDER_CLOSE_BTN));
    }

    @Step("Get first listed detection id")
    public String get_first_listed_detection_id(RemoteWebDriver driver) {
        return controls.getElementText(driver, FIRST_LISTED_DETECTION_ID_BTN);
    }

    @Step("Get current expanded message text")
    public String get_current_expanded_message_text(RemoteWebDriver driver) {
        return controls.getElementText(driver, CURRENT_EXPANDED_MESSAGE_TEXT_ID_TXT);
    }

    @Step("Click on options button in result table")
    public void click_on_options_button_in_result_table(RemoteWebDriver driver) {
        controls.performClickByJS(driver, OPTION_BUTTON_RESULT_TABLE_LOCATOR);
    }

    @Step("Select column name")
    public void select_column_name(RemoteWebDriver driver, String columnName) {
        if (controls.getWebElement(driver,
                By.xpath(String.format(OPTION_CHECKBOX_LOCATOR + "//ancestor::li[1]//*[@role='checkbox']", columnName))).getAttribute("aria-checked").contains("false"))
            controls.performClick(driver, By.xpath(String.format(OPTION_CHECKBOX_LOCATOR, columnName)));
    }

    @Step("Click on search options icon")
    public void click_on_search_options_icon(RemoteWebDriver driver) {
        controls.performClick(driver, SEARCH_OPTIONS_ICON_LOCATOR);
    }

    @Step("Select option from search options")
    public void select_option_from_search_options(RemoteWebDriver driver, String optionName) {
        controls.performClick(driver, By.xpath(String.format(SEARCH_OPTIONS_LOCATOR, optionName)));
    }

    @Step("Click on '<<' button to add option to active list")
    public void click_on_add_option_to_active_list(RemoteWebDriver driver) {
        if (controls.getWebElement(driver, REMOVE_BUTTON_SEARCH_COLUMNS_DLG_LOCATOR2).getAttribute("disabled") == null)
            controls.performClick(driver, REMOVE_BUTTON_SEARCH_COLUMNS_DLG_LOCATOR2);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Click on apply button")
    public void click_on_apply_button(RemoteWebDriver driver) {
        controls.performClick(driver, APPLY_BUTTON_SEARCH_COLUMNS_DLG_LOCATOR1);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Get detection column number")
    public int get_row_number_for_detection_id(RemoteWebDriver driver, String detectionId) {
        List<WebElement> detectionIds = driver.findElements(DETECTION_ID_RESULT_TABLE_LOCATORS);
        for (WebElement element : detectionIds) {
            if (element.getText().equals(detectionId))
                return detectionIds.indexOf(element) + 1;
        }
        return -1;
    }

    @Step("Get field value by column number")
    public String get_field_value_by_column_number(RemoteWebDriver driver, String detectionId, int columnNumber) throws InterruptedException {
        int rowNumber = get_row_number_for_detection_id(driver, detectionId);
        List<WebElement> data = driver.findElements(By.xpath(String.format(ROW_DATA_LOCATOR, rowNumber)));
        return data.get(columnNumber - 1).getText();
    }

    @Step("Get violation value from result table")
    public String get_violation(RemoteWebDriver driver) {
        return controls.getElementText(driver, VIOLATION_RESULT_TABLE_LOCATOR);
    }

    @Step("Select 4eyes option")
    public void select_4_eyes_option(RemoteWebDriver driver, String value) throws Exception {
        controls.selectOptionByDisplayedText(driver, FOUR_EYES_OPTION_YES_NO, value);
    }


    @Step("Click on 3 dots menu button")
    public void click_on_3_dots_menu_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, THREE_DOTS_MENU_LOCATOR);
    }

    @Step("Check if Don't Know button exist in the three dots menu")
    public boolean is_dontKnow_exist_in_3dots_menu(RemoteWebDriver driver) {
        return controls.exists(driver, DONT_KNOW_3_Dots_Menu_LOCATOR);
    }

    @Step("Check if table rows appear")
    public boolean verify_table_rows_appear(RemoteWebDriver driver) {
        return controls.exists(driver, TABLE_ROWS_LOCATOR);
    }

    @Step("Get number of detections in the table")
    public int get_number_of_detections_in_table(RemoteWebDriver driver) {
        return driver.findElements(TABLE_ROWS_LOCATOR).size();
    }
}