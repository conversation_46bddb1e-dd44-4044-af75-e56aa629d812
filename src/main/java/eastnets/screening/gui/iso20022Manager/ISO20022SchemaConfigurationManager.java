package eastnets.screening.gui.iso20022Manager;

import core.gui.Controls;
import eastnets.common.control.CommonAction;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;


public class ISO20022SchemaConfigurationManager {

    private static final By SEARCH_BUTTON_LOCATOR = By.id("iso20022:homepage_business:tabViewListManager:tabView:homepage_business_ISO20022:btnSearchIso20022");
    private static final String BROWSE_BUTTON_LOCATOR = "iso20022:homepage_business:tabViewListManager:tabView:homepage_business_ISO20022:uploadFile_input";
    private static final By IMPORT_BUTTON_LOCATOR = By.id("iso20022:homepage_business:tabViewListManager:tabView:homepage_business_ISO20022:_btnImportFormat");
    private static final By DELETE_VERSION_BUTTON_LOCATOR = By.xpath("//span[contains(text(),'Delete Version')]");

    private final Controls controls;
    private final CommonAction commonAction;

    public ISO20022SchemaConfigurationManager() {
        this.controls = new Controls();
        this.commonAction = new CommonAction();
    }

    @Step("Click search button")
    public void clickSearchButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SEARCH_BUTTON_LOCATOR);
    }

    @Step("Click specific Message")
    public void clickSpecificSchema(RemoteWebDriver driver, String schemaName) {
        controls.performClickByJS(driver, By.xpath(String.format("//a[.='%s']", schemaName)));
    }

    @Step("Browse schema")
    public void browseSchema(RemoteWebDriver driver, String schemaPath) {
        controls.setAttributeValueByJS(driver, BROWSE_BUTTON_LOCATOR, "aria-hidden", "false");
        controls.setTextBoxValue(driver, By.id(BROWSE_BUTTON_LOCATOR), schemaPath);
    }

    @Step("Click import button")
    public String clickImportButton(RemoteWebDriver driver) throws InterruptedException {
        controls.performClickByJS(driver, IMPORT_BUTTON_LOCATOR);
        return commonAction.getAlertMessageString(driver);

    }

    @Step("Verify schema exist")
    public boolean verifySchemaExist(RemoteWebDriver driver, String schemaName) {
        Allure.step(String.format("Check if schema with Name = %s", schemaName));
        return controls.exists(driver, By.xpath(String.format("//a[.='%s']", schemaName)));
    }

    @Step("Click delete version button")
    public void clickDeleteVersionButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, DELETE_VERSION_BUTTON_LOCATOR);
    }

    @Step("Delete schema version")
    public String deleteSchemaVersion(RemoteWebDriver driver, String schemaName) throws InterruptedException {
        Allure.step("Delete schema with name = " + schemaName);
        clickSearchButton(driver);
        controls.performClickByJS(driver, By.xpath(String.format("//a[.='%s']", schemaName)));
        clickDeleteVersionButton(driver);
        driver.switchTo().alert().accept();
        return commonAction.getAlertMessageString(driver);
    }
}