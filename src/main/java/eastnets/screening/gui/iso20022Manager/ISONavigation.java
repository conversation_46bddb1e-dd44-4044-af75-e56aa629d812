package eastnets.screening.gui.iso20022Manager;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public enum ISONavigation {


    ISO_20022_SCHEMA_CONFIGURATION("//a[contains(text(),'ISO20022 Schema Configuration')]",
            "iso20022:homepage_business:tabViewListManager:homepage_business_ISO20022:btnSearchIso20022"),
    FORMAT_CONFIGURATION("//a[contains(text(),'ISO20022 Format Configuration')]",
            "iso20022:homepage_business:tabViewListManager:Tab_format_ISO20022:zoneCbx_label"),
    SCHEMA_CONFIGURATION("//a[@href='#iso20022:homepage_business:tabViewListManager:tabView:schemaConfiguration']",
            "iso20022:homepage_business:tabViewListManager:homepage_business_ISO20022:btnSearchIso20022"),
    PRE_MAPPED_FIELDS_CONFIGURATION("//a[contains(text(),'Pre Mapped Fields Configuration')]",
            "iso20022:homepage_business:tabViewListManager:tabView:pre_mapped_fields_configuration:j_idt252");


    private final By navigationElement;
    private final By checkLabel;
    private final Controls controls;
    private final Wait wait;

    ISONavigation(String navigationElement, String checkLabel) {
        this.navigationElement = By.xpath(navigationElement);
        this.checkLabel = By.id(checkLabel);
        this.controls = new Controls();
        this.wait = new Wait();
    }

    @Step("Navigate")
    public boolean navigate(RemoteWebDriver driver) {
        Allure.step("Navigate to " + navigationElement);
        wait.waitUntilElementToBeClickable(driver, navigationElement, Wait.TEN_SECOND_DURATION);
        controls.performClickByJS(driver, navigationElement);
        return controls.exists(driver, checkLabel);
    }
}
