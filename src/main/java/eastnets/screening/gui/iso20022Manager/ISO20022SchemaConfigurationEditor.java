package eastnets.screening.gui.iso20022Manager;

import core.gui.Controls;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class ISO20022SchemaConfigurationEditor {

    private static final By PRE_MAPPED_ADD_BUTTON_LOCATOR = By.xpath("//*[.='Add' and @title='Add New Field']");
    private static final By XSD_ELEMENT_TYPE_LOCATOR = By.id("iso20022:homepage_business:tabViewListManager:tabView:pre_mapped_fields_configuration:XSDElementTypeLabelInput");
    private static final By FIELD_TYPE_DDL_LOCATOR = By.id("iso20022:homepage_business:tabViewListManager:tabView:pre_mapped_fields_configuration:fieldTypeInput_label");
    private static final By SCAN_CHECKBOX_LOCATOR = By.xpath("//div[@id='iso20022:homepage_business:tabViewListManager:tabView:pre_mapped_fields_configuration:ScanChkBx']/div[2]");
    private static final By ADD_TO_CONTEXT_CHECKBOX_LOCATOR = By.xpath("//div[@id='iso20022:homepage_business:tabViewListManager:tabView:pre_mapped_fields_configuration:ContextChkBx']/div[2]");
    private static final By SAVE_BUTTON_LOCATOR = By.id("iso20022:homepage_business:tabViewListManager:tabView:pre_mapped_fields_configuration:btnSave");

    private final Controls controls;

    public ISO20022SchemaConfigurationEditor() {
        this.controls = new Controls();
    }

    public void clickAddButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, PRE_MAPPED_ADD_BUTTON_LOCATOR);
    }

    public void setXsdElementType(RemoteWebDriver driver, String xsdElementType) {
        controls.setTextBoxValue(driver, XSD_ELEMENT_TYPE_LOCATOR, xsdElementType);
    }

    public void chooseFieldType(RemoteWebDriver driver, String fieldType) throws Exception {
        controls.selectOptionByDisplayedText(driver, FIELD_TYPE_DDL_LOCATOR, fieldType);
    }

    public void checkScanCheckbox(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SCAN_CHECKBOX_LOCATOR);
    }

    public void checkAddToContextCheckbox(RemoteWebDriver driver) {
        controls.performClickByJS(driver, ADD_TO_CONTEXT_CHECKBOX_LOCATOR);
    }

    public void clickSaveButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SAVE_BUTTON_LOCATOR);
    }
}
