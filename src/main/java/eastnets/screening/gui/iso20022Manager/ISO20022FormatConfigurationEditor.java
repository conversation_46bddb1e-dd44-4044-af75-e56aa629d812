package eastnets.screening.gui.iso20022Manager;

import core.gui.Controls;
import core.util.Wait;
import eastnets.common.control.CommonAction;
import eastnets.screening.entity.ISO20022FormatDetailsConfiguration;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.Keys;
import org.openqa.selenium.interactions.Actions;
import org.openqa.selenium.remote.RemoteWebDriver;


public class ISO20022FormatConfigurationEditor {

    private static final By ADD_BUTTON_LOCATOR = By.id("iso20022FormatConfigurationEditor:iso20022_format_configuration_editor_business:_tblResults:btnAdd");
    private static final By REMOVE_BUTTON_LOCATOR = By.id("iso20022FormatConfigurationEditor:iso20022_format_configuration_editor_business:_tblResults:_btnRemoveMessage");
    private static final By CREATE_FORMAT_BUTTON_LOCATOR = By.id("iso20022FormatConfigurationEditor:iso20022_format_configuration_editor_business:_tblResults:_btnCreateFormat");
    private static final By DETAILS_BUTTON_LOCATOR = By.id("iso20022FormatConfigurationEditor:iso20022_format_configuration_editor_business:_tblResults:_btnDetailsMessage");
    private static final By MERGE_CUSTOM_XSD_BUTTON_LOCATOR = By.id("iso20022FormatConfigurationEditor:iso20022_format_configuration_editor_business:_tblResults:mergeCustomXsd");
    private static final By VIEW_MERGED_CUSTOM_XSD_BUTTON_LOCATOR = By.id("iso20022FormatConfigurationEditor:iso20022_format_configuration_editor_business:_tblResults:viewMergeHistory");
    private static final By EXPORT_BUTTON_LOCATOR = By.id("iso20022FormatConfigurationEditor:iso20022_format_configuration_editor_business:_tblResults:exportMessages");
    private static final By IMPORT_BUTTON_LOCATOR = By.id("iso20022FormatConfigurationEditor:iso20022_format_configuration_editor_business:_tblResults:_btnImport");
    private static final By IMPORT_INPUT_LOCATOR = By.xpath("//*[@type='file']");
    private static final By IMPORT_BUTTON_LOCATOR_IMPORT_CONFIG_FORM = By.xpath("//button[contains(@id,'iso20022FormatConfigurationEditor:iso20022_format_configuration_editor_business:importConfiguration') and (. = 'Import')]");
    private static final By IMPORT_BUTTON_LOCATOR_IMPORT_MERGE_FORM = By.xpath("//button[contains(@id,'iso20022FormatConfigurationEditor:iso20022_format_configuration_editor_business:uploadFileISO') and (. = 'Import')]");
    private static final By MESSAGE_TYPE_LOCATOR = By.id("iso20022FormatConfigurationEditor:iso20022_format_configuration_editor_business:messageTypeCbx_label");
    private static final By MESSAGE_VERSION_LOCATOR = By.id("iso20022FormatConfigurationEditor:iso20022_format_configuration_editor_business:messageVersionCbx_label");
    private static final By Message_WRAPPER_LOCATOR = By.id("iso20022FormatConfigurationEditor:iso20022_format_configuration_editor_business:messageWrapperTxt");
    private static final By MESSAGE_SWIFT_LOCATOR = By.id("iso20022FormatConfigurationEditor:iso20022_format_configuration_editor_business:messageHeaderCbx_label");
    private static final By SAVE_BUTTON_LOCATOR = By.id("iso20022FormatConfigurationEditor:iso20022_format_configuration_editor_business:_btnSaveNewMessage");
    private static final By CREATE_FORMAT_CHECK_BOX_LOCATOR = By.xpath("//div[@id = 'iso20022FormatConfigurationEditor:iso20022_format_configuration_editor_business:createFormatCbx']//div[2]");
    private static final By ENABLE_BUTTON_LOCATOR = By.xpath("//div[@title='To Enable/Disable Message Format']");
    private static final String MESSAGE_NAME_CHECKBOX_LOCATOR = "//*[.='%s']//ancestor::tr[1]//td[1]//*[@type='checkbox']";
    private static final By ADD_HEADER_FIELD_BUTTON_LOCATOR = By.id("iso20022FormatConfigurationEditor:iso20022_format_configuration_editor_business:FormatDetails:_tblHeaderResults:btnAddHeaderField");
    private static final By ADD_BODY_FIELD_BUTTON_LOCATOR = By.id("iso20022FormatConfigurationEditor:iso20022_format_configuration_editor_business:FormatDetails:_tblResults:btnAddBodyField");
    private static final String FIELD_LOCATOR = "//tr/td/a/span[text() = '%s']";
    private static final By FIELD_TYPE_DDL_LOCATOR = By.id("iso20022FormatConfigurationEditor:iso20022_format_configuration_editor_business:FormatDetails:fieldTypeInput_label");
    private static final By FIELD_NAME_TEXT_BOX_LOCATOR = By.id("iso20022FormatConfigurationEditor:iso20022_format_configuration_editor_business:FormatDetails:fieldLabelInput");
    private static final By XPATH_TEXT_BOX_LOCATOR = By.xpath("//*[@id='iso20022FormatConfigurationEditor:iso20022_format_configuration_editor_business:FormatDetails:fieldXpath']//following::*[@type='text']");
    private static final String SCAN_CHECK_BOX_LOCATOR = "iso20022FormatConfigurationEditor:iso20022_format_configuration_editor_business:FormatDetails:ScanChkBx";
    private static final By SEARCH_HEADERFIELD_TEXTBOX_LOCATOR = By.id("iso20022FormatConfigurationEditor:iso20022_format_configuration_editor_business:FormatDetails:globalFilterHeader");
    private static final By SEARCH_BODYFIELD_TEXTBOX_LOCATOR = By.id("iso20022FormatConfigurationEditor:iso20022_format_configuration_editor_business:FormatDetails:globalFilter");
    private static final By SAVE_FIELD_BUTTON_LOCATOR = By.id("iso20022FormatConfigurationEditor:iso20022_format_configuration_editor_business:FormatDetails:btnSave");
    private static final By UPDATE_BUTTON_LOCATOR = By.id("iso20022FormatConfigurationEditor:iso20022_format_configuration_editor_business:FormatDetails:btnUpdate");
    private static final By CATEGORY_DDL_LOCATOR = By.id("iso20022FormatConfigurationEditor:iso20022_format_configuration_editor_business:FormatDetails:categoryCbx_label");

    private final Controls controls;
    private final Wait wait;
    private final CommonAction commonAction;

    public ISO20022FormatConfigurationEditor() {
        this.controls = new Controls();
        this.wait = new Wait();
        this.commonAction = new CommonAction();
    }

    @Step("Click add button")
    public void clickAddButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, ADD_BUTTON_LOCATOR);
    }

    @Step("Click remove button")
    public String clickRemoveButton(RemoteWebDriver driver) throws InterruptedException {
        controls.performClickByJS(driver, REMOVE_BUTTON_LOCATOR);
        driver.switchTo().alert().accept();
        return commonAction.getAlertMessageString(driver);
    }

    @Step("Click create format button")
    public void clickCreateFormatButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, CREATE_FORMAT_BUTTON_LOCATOR);
    }

    @Step("Click details button")
    public void clickDetailsButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, DETAILS_BUTTON_LOCATOR);
    }

    @Step("Click view merged Custom XSD button")
    public void clickViewMergedCustomXSDsButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, VIEW_MERGED_CUSTOM_XSD_BUTTON_LOCATOR);
    }

    @Step("Click export button")
    public void clickExportButton(RemoteWebDriver driver) throws InterruptedException {
        controls.performClickByJS(driver, EXPORT_BUTTON_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Click import button")
    public void clickImportButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, IMPORT_BUTTON_LOCATOR);
    }

    @Step("Choose Category")
    public void selectCategory(RemoteWebDriver driver, String category) throws Exception {
        controls.selectOptionByDisplayedText(driver, CATEGORY_DDL_LOCATOR, category);
    }


    @Step("Select Schema name")
    public void selectSchemaName(RemoteWebDriver driver, String schemaName) throws Exception {
        controls.selectOptionByDisplayedText(driver, MESSAGE_TYPE_LOCATOR, schemaName);
    }

    @Step("Select Schema Version")
    public void selectSchemaVersion(RemoteWebDriver driver, String schemaName, String schemaVersion) throws Exception {
        controls.selectOptionByDisplayedText(driver, MESSAGE_VERSION_LOCATOR, schemaName + "." + schemaVersion);
    }

    @Step("Select Schema Wrapper")
    public void selectSchemaWrapper(RemoteWebDriver driver, String schemaWrapper) throws Exception {
        controls.setTextBoxValue(driver, Message_WRAPPER_LOCATOR, schemaWrapper);
    }

    @Step("Select Message Swift")
    public void selectMessageSwift(RemoteWebDriver driver, String SwiftHeader) throws Exception {
        controls.selectOptionByDisplayedText(driver, MESSAGE_SWIFT_LOCATOR, SwiftHeader);
    }

    @Step("Click Format Checkbox")
    public void clickFormatCheckBox(RemoteWebDriver driver) {
        controls.performClickByJS(driver, CREATE_FORMAT_CHECK_BOX_LOCATOR);
    }

    @Step("Click EnableButton")
    public void clickEnableButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, ENABLE_BUTTON_LOCATOR);
    }

    @Step("Click Save button")
    public void clickSaveButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SAVE_BUTTON_LOCATOR);
    }


    @Step("Check message Name checkbox")
    public void checkMessageNameCheckbox(RemoteWebDriver driver, String schemaName) throws Exception {
        Allure.step("Check message Name checkbox.");
        String xpath = String.format(MESSAGE_NAME_CHECKBOX_LOCATOR, schemaName);
        controls.setCheckboxValueById(driver, By.xpath(xpath), By.xpath(xpath), true);
    }

    @Step("Click Add Header Field Button")
    public void clickAddHeaderFieldButton(RemoteWebDriver driver) {
        Allure.step("Click Add Header Field Button");
        controls.performClickByJS(driver, ADD_HEADER_FIELD_BUTTON_LOCATOR);
    }

    @Step("Click Add Body Field Button")
    public void clickAddBodyFieldButton(RemoteWebDriver driver) {
        Allure.step("Click Add Body Field Button");
        controls.performClickByJS(driver, ADD_BODY_FIELD_BUTTON_LOCATOR);
    }

    @Step("Select Field")
    public void selectField(RemoteWebDriver driver, String fieldName, ISO20022FormatDetailsConfiguration ISO20022FormatDetailsConfiguration) throws Exception {
        controls.setTextBoxValue(driver, SEARCH_HEADERFIELD_TEXTBOX_LOCATOR, fieldName);
        By xpath = By.xpath(String.format(FIELD_LOCATOR, ISO20022FormatDetailsConfiguration.getHeaderField()));
        controls.performClickByJS(driver, xpath);
    }

    @Step("Select Field Type")
    public void selectFieldType(RemoteWebDriver driver, String fieldType) throws Exception {
        controls.selectOptionByDisplayedText(driver, FIELD_TYPE_DDL_LOCATOR, fieldType);

    }

    @Step("Search Header Field")
    public void searchHeaderField(RemoteWebDriver driver, String headerFieldName) throws Exception {
        Allure.step("Search Header Field = " + headerFieldName);
        controls.setTextBoxValue(driver, SEARCH_HEADERFIELD_TEXTBOX_LOCATOR, headerFieldName);
    }

    @Step("Search Body Field")
    public void searchBodyField(RemoteWebDriver driver, String bodyFieldName) throws Exception {
        controls.setTextBoxValue(driver, SEARCH_BODYFIELD_TEXTBOX_LOCATOR, bodyFieldName);
        Actions action = new Actions(driver);
        action.sendKeys(Keys.ENTER).build().perform();
        wait.time(Wait.ONE_SECOND * 1);
    }

    @Step("Click on first item in Search")
    public void clickFirstItemInBodySearch(RemoteWebDriver driver) {
        driver.findElement(By.xpath("//a[@id = 'iso20022FormatConfigurationEditor:iso20022_format_configuration_editor_business:FormatDetails:_tblResults:0:linkId']")).click();
    }

    @Step("Fill Field Name Text Box")
    public void fillFieldNameTextBox(RemoteWebDriver driver, String fieldName) throws Exception {
        Allure.step("Fill Field Name Text Box = " + fieldName);
        controls.setTextBoxValue(driver, FIELD_NAME_TEXT_BOX_LOCATOR, fieldName);
    }

    @Step("Fill Xpath Text Box")
    public void fillXpathTextBox(RemoteWebDriver driver, String xpath) throws Exception {
        Allure.step("Fill Xpath Text Box = " + xpath);
        controls.setTextBoxValue(driver, XPATH_TEXT_BOX_LOCATOR, xpath);
    }

    @Step("Check Scan CheckBox")
    public void checkScanCheckBox(RemoteWebDriver driver) throws Exception {
        Allure.step("Check Scan CheckBox");
        controls.setCheckboxValueById(driver, SCAN_CHECK_BOX_LOCATOR, true);
    }

    @Step("Save Field")
    public String saveField(RemoteWebDriver driver) throws Exception {
        controls.performClickByJS(driver, SAVE_FIELD_BUTTON_LOCATOR);
        return commonAction.getAlertMessageString(driver);
    }

    @Step("Import Configrations")
    public String importConfigurations(RemoteWebDriver driver, String filePath) throws InterruptedException {
        controls.setTextBoxValue(driver, IMPORT_INPUT_LOCATOR, filePath);
        controls.performClickByJS(driver, IMPORT_BUTTON_LOCATOR_IMPORT_CONFIG_FORM);
        return commonAction.getAlertMessageString(driver);

    }

    @Step("Click merge Custom XSD button")
    public void clickMergeCustomXSDButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, MERGE_CUSTOM_XSD_BUTTON_LOCATOR);
    }

    @Step("Merge Custom XSD")
    public String mergeCustomXSD(RemoteWebDriver driver, String filePath) throws InterruptedException {
        controls.setTextBoxValue(driver, IMPORT_INPUT_LOCATOR, filePath);
        controls.performClickByJS(driver, IMPORT_BUTTON_LOCATOR_IMPORT_MERGE_FORM);
        return commonAction.getAlertMessageString(driver);

    }

    @Step("Click Update button")
    public String clickUpdateButton(RemoteWebDriver driver) throws InterruptedException {
        controls.performClickByJS(driver, UPDATE_BUTTON_LOCATOR);
        return commonAction.getAlertMessageString(driver);
    }
}
