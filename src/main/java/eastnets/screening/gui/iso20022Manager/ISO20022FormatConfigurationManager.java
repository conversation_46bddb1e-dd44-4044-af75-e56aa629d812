package eastnets.screening.gui.iso20022Manager;

import core.gui.Controls;
import core.util.Wait;
import eastnets.common.control.CommonAction;
import eastnets.screening.entity.ISO20022FormatConfiguration;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class ISO20022FormatConfigurationManager {

    private static final By SEARCH_BUTTON_LOCATOR = By.id("iso20022:homepage_business:tabViewListManager:Tab_format_ISO20022:btnSearchIso20022");
    private static final By ADD_BUTTON_LOCATOR = By.id("iso20022:homepage_business:tabViewListManager:Tab_format_ISO20022:_tblResults:btnAdd");
    private static final By DELETE_BUTTON_LOCATOR = By.id("iso20022:homepage_business:tabViewListManager:Tab_format_ISO20022:_tblResults:_btnDeleteGroup");
    private static final By CONFIGURE_BUTTON_LOCATOR = By.id("iso20022:homepage_business:tabViewListManager:Tab_format_ISO20022:_tblResults:_btnConfigureGroup");
    private static final By GROUP_ZONE_LOCATOR = By.id("iso20022:homepage_business:tabViewListManager:Tab_format_ISO20022:zoneCbx1_label");
    private static final By GROUP_NAME_LOCATOR = By.id("iso20022:homepage_business:tabViewListManager:Tab_format_ISO20022:groupName");
    private static final By ENABLE_GROUP_BUTTON = By.xpath("//div[@title='To Enable/Disable Group']");
    private static final By SAVE_GROUP_BUTTON_LOCATOR = By.id("iso20022:homepage_business:tabViewListManager:Tab_format_ISO20022:_btnSaveNewGroup");
    private static final By SORT_BY_CREATION_DATE_LOCATOR = By.id("iso20022:homepage_business:tabViewListManager:Tab_format_ISO20022:_tblResults:creationDateColumn");


    private static final By CHECKBOX_LOCATOR = By.xpath("//*[@id='iso20022:homepage_business:tabViewListManager:Tab_format_ISO20022:_tblResults_data']//*[@class='ui-selectbooleancheckbox ui-chkbox ui-widget']");
    private static final By LAST_PAGE_BUTTON_LOCATOR = By.xpath("//*[@id='iso20022:homepage_business:tabViewListManager:Tab_format_ISO20022:_tblResults']//*[text()='>>']//ancestor::button");

    private final Controls controls;
    private final Wait wait;
    private final CommonAction commonAction;

    public ISO20022FormatConfigurationManager() {
        this.controls = new Controls();
        this.wait = new Wait();
        this.commonAction = new CommonAction();
    }

    @Step("Click search button")
    public void clickSearchButton(RemoteWebDriver driver) {
        controls.scrollUp(driver);
        controls.performClickByJS(driver, SEARCH_BUTTON_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }


    @Step("Click add button")
    public void clickAddButton(RemoteWebDriver driver) throws InterruptedException {
        wait.time(Wait.ONE_SECOND * 2);
        controls.performClickByJS(driver, ADD_BUTTON_LOCATOR);
    }

    @Step("Click delete button")
    public void clickDeleteButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, DELETE_BUTTON_LOCATOR);
    }

    @Step("Click configure button")
    public void clickConfigureButton(RemoteWebDriver driver) {
        controls.scrollDown(driver);
        controls.performClickByJS(driver, CONFIGURE_BUTTON_LOCATOR);
        wait.waitUntilLoadersDisappear(driver);
    }

    @Step("Scroll to Save Group button")
    public void scrollToSaveGroupButton(RemoteWebDriver driver) {
        controls.scrollIntoViewJS(driver, SAVE_GROUP_BUTTON_LOCATOR);
    }

    @Step("Select Group Zone")
    public void selectGroupZone(RemoteWebDriver driver, ISO20022FormatConfiguration formatConfiguration) throws Exception {
        controls.selectOptionByDisplayedText(driver, GROUP_ZONE_LOCATOR, formatConfiguration.getZone());

    }

    @Step("Set Group Name")
    public void setGroupName(RemoteWebDriver driver, ISO20022FormatConfiguration formatConfiguration) throws Exception {
        controls.setTextBoxValue(driver, GROUP_NAME_LOCATOR, formatConfiguration.getGroupName());

    }

    @Step("Click Enable Group button")
    public void clickEnableGroupButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, ENABLE_GROUP_BUTTON);

    }

    @Step("Click Save Group Button")
    public String clickSaveGroupButton(RemoteWebDriver driver) throws InterruptedException {
        controls.performClickByJS(driver, SAVE_GROUP_BUTTON_LOCATOR);
        return commonAction.getAlertMessageStringWithoutWaitForAjaxLoader(driver);
    }


    @Step("Click Last Page Button")
    public void clickLastPageButton(RemoteWebDriver driver) throws InterruptedException {
        Allure.step("Click on last page button on result table.");
        controls.scrollDown(driver);
        if (controls.getWebElement(driver, LAST_PAGE_BUTTON_LOCATOR).getAttribute("aria-disabled").contains("false"))
            controls.performClickByJS(driver, LAST_PAGE_BUTTON_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);

    }

    @Step("Click checkbox button")
    public void click_check_box(RemoteWebDriver driver)  {
        driver.findElement(CHECKBOX_LOCATOR).click();

    }


    @Step("Click Sort By Creation Date Button")
    public void sortByCreationDateButton(RemoteWebDriver driver) throws InterruptedException {
            controls.performClick(driver, SORT_BY_CREATION_DATE_LOCATOR);
            wait.time(Wait.ONE_SECOND * 5);
            controls.performClick(driver, SORT_BY_CREATION_DATE_LOCATOR);
            wait.time(Wait.ONE_SECOND * 5);
    }

   
}