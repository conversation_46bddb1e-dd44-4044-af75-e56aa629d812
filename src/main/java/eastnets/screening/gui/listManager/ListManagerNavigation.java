package eastnets.screening.gui.listManager;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public enum ListManagerNavigation {

    LIST_MANAGER("//a[contains(text(),'List Manager')]",
            "listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:Sub_black_list_viewer:zoneCbx"),
    LIST_SET_MANAGER("//a[contains(text(),'List Set Manager')]",
            "listManagerForm:homepage_business:tabViewListManager:tab_list_set_manager:zoneCbx"),
    LIST_EXPLORER("//a[contains(text(),'List Explorer')]",
            "listManagerForm:homepage_business:tabViewListManager:tab_new_list_explorer:zoneCbx"),
    GOOD_GUYS_EXPLORER("//a[contains(text(),'Good Guys Explorer')]",
            "listManagerForm:homepage_business:tabViewListManager:Tab_good_guys_explorer:ZonesMulti"),
    WORLD_CHECK("//a[contains(text(),'World Check')]",
            "listManagerForm:homepage_business:tabViewListManager:tab_world_check:zoneCbx_label"),
    DOW_GONES_GG_MIGRATION("//a[contains(text(),'Dow Jones - GG Migration')]",
            "listManagerForm:homepage_business:tabViewListManager:tab_GoodGuys_Migration:tab_GoodGuys_Migration_Jobs:zoneCbx"),
    DOW_GONES("//a[text()='Dow Jones']",
            "listManagerForm:homepage_business:tabViewListManager:tab_Factiva:zoneCbx_label"),
    SAFE_TRADE("//a[text()='SafeTrade']",
            "listManagerForm:homepage_business:tabViewListManager:tab_trade_finance:zoneCbx"),
    ACTIVITY("//a[contains(text(),'Activity')]",
            "listManagerForm:homepage_business:tabViewListManager:Tab_activity:searchDateFrom");

    private final By navigationElement;
    private final By checkLabel;
    private final Controls controls;
    private final Wait wait;

    ListManagerNavigation(String navigationElement, String checkLabel) {
        this.navigationElement = By.xpath(navigationElement);
        this.checkLabel = By.id(checkLabel);
        this.controls = new Controls();
        this.wait = new Wait();
    }

    @Step("Navigate")
    public boolean navigate(RemoteWebDriver driver) {
        wait.waitUntilElementToBeClickable(driver, navigationElement, Wait.ONE_MINUTE_DURATION);
        controls.performClick(driver, navigationElement);
        return wait.waitUntilElementToBeVisible(driver, checkLabel, Wait.ONE_MINUTE_DURATION).isDisplayed();
    }
}
