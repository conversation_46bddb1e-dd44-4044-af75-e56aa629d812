package eastnets.screening.gui.listManager.goodGuy;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.util.List;

public class GoodGuyEditor {

    private static final By ACCEPTED_TEXT_INPUT_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_good_guys_creator:Good_guy_management:name");
    private static final By RANK_SELECT_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_good_guys_creator:Good_guy_management:rank");
    private static final By SCAN_BTN_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_good_guys_creator:Good_guy_management:btnScanGG_1");
    private static final By MATCHED_NAME_CHECK_BOX_LOCATOR = By.cssSelector("[id='listManagerForm:homepage_business:tabViewListManager:Tab_good_guys_creator:Good_guy_management:goodGuysTable_data'] div.ui-chkbox-box");
    private static final By ACCEPT_BTN_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_good_guys_creator:Good_guy_management:btnAcceptGG_1");

    //Good Guy Import Locators
    private static final By ZONE_SELECT_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_good_guys_importer:Good_guy_importer:zoneCbx");
    private static final By LIST_SET_SELECT_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_good_guys_importer:Good_guy_importer:listSetCbx");
    private static final By BLACK_LIST_SELECT_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_good_guys_importer:Good_guy_importer:blackListsCbx");
    private static final By FILE_TO_IMPORT_INPUT_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_good_guys_importer:Good_guy_importer:importFile_input");
    private static final By IMPORT_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_good_guys_importer:Good_guy_importer:btnImport");
    private static final By IMPORT_AS_SHARED_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_good_guys_importer:Good_guy_importer:btnSharedAcceptGG");
    private static final By GG_CONDITIONS_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_good_guys_creator:Good_guy_management:conditions");
    private static final By SELECT_ALL_OPTION = By.xpath("//*[@id='auditManagerForm:homepage_business:Detections_list:Good_guy_management:goodGuysTable:overlaySelect']//span[.='Select all']");

    private static final String MATCHED_ENTITY_LOCATOR = "//*[@id='auditManagerForm:homepage_business:Detections_list:Good_guy_management:goodGuysTable']//td[4]//a[.='%s']";
    private static final String MATCHED_ENTITY_CHECK_BOX_LOCATOR = "//*[@id='auditManagerForm:homepage_business:Detections_list:Good_guy_management:goodGuysTable']//td[4]//a[.='%s']//ancestor::tr[1]//td[1]/div";

    private final Controls controls;
    private final Wait wait;

    public GoodGuyEditor() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    @Step("click matched entities")
    public void click_matched_entities(RemoteWebDriver driver, String MATCHED_ENTITY) {
        controls.performClickByJS(driver, By.id(MATCHED_ENTITY));

    }

    @Step("Select All Option")
    public void select_all_option(RemoteWebDriver driver) {
        Allure.step("Select All Option");
        controls.performClickByJS(driver, SELECT_ALL_OPTION);
    }


    @Step("Set Accepted Text ")
    public void set_accepted_text(RemoteWebDriver driver, String acceptedText) {
        controls.setTextBoxValue(driver, ACCEPTED_TEXT_INPUT_LOCATOR, acceptedText);

    }

    @Step("Set Rank")
    public void set_rank(RemoteWebDriver driver, String rank) throws Exception {
        controls.selectOptionByDisplayedText(driver, RANK_SELECT_LOCATOR, rank);
    }

    @Step("Click scan button")
    public void click_scan_btn(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SCAN_BTN_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Select matched name from grid")
    public void select_matched_name(RemoteWebDriver driver) {
        controls.performClick(driver, MATCHED_NAME_CHECK_BOX_LOCATOR);
    }

    @Step("Select all matched name from grid")
    public void select_all_matched_names(RemoteWebDriver driver) {
        List<WebElement> checkBoxs = driver.findElements(MATCHED_NAME_CHECK_BOX_LOCATOR);
        for (WebElement element : checkBoxs)
            controls.performClick(element);
    }

    @Step("Click accept button")
    public void click_accept_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, ACCEPT_BTN_LOCATOR);

    }

    @Step("Select Zone")
    public void select_zone(RemoteWebDriver driver, String zone) throws Exception {
        controls.selectOptionByDisplayedText(driver, ZONE_SELECT_LOCATOR, zone);
    }

    @Step("Select List Set")
    public void select_list_set(RemoteWebDriver driver, String list_set) throws Exception {
        controls.selectOptionByDisplayedText(driver, LIST_SET_SELECT_LOCATOR, list_set);
    }

    @Step("Select Black List")
    public void select_black_list(RemoteWebDriver driver, String black_list) throws Exception {
        controls.selectOptionByPartialDisplayedText(driver, BLACK_LIST_SELECT_LOCATOR, black_list);
    }

    @Step("set File Path")
    public void set_file_path(RemoteWebDriver driver, String file_path) throws Exception {
        controls.setTextBoxValue(driver, FILE_TO_IMPORT_INPUT_LOCATOR, file_path);
    }

    @Step("Click Import Button")
    public void click_import_btn(RemoteWebDriver driver) {
        controls.performClickByJS(driver, IMPORT_BUTTON_LOCATOR);
    }

    @Step("Click Import As Shared Button")
    public void click_import_as_shared_btn(RemoteWebDriver driver) {
        controls.performClickByJS(driver, IMPORT_AS_SHARED_BUTTON_LOCATOR);
    }

    @Step("Set Add Context Conditions")
    public void add_context_conditions(RemoteWebDriver driver, String conditionName) {
        controls.setTextBoxValue(driver, GG_CONDITIONS_LOCATOR, conditionName);
    }

    @Step("Click on matched entity")
    public void click_on_matched_entity(RemoteWebDriver driver, String matched_entity) {
        controls.performClickByJS(driver, By.xpath(String.format(MATCHED_ENTITY_LOCATOR, matched_entity)));

    }

    @Step("Check if Checkbox related to matched entity is checked")
    public boolean check_if_Matched_entity_checkbox_checked(RemoteWebDriver driver, String matched_entity) {
        return controls.isChecked(driver, String.format(MATCHED_ENTITY_CHECK_BOX_LOCATOR, matched_entity));
    }
}
