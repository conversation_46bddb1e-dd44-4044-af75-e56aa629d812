package eastnets.screening.gui.listManager.goodGuy;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.util.List;

public class GoodGuyManager {

    private static final By ZONE_SELECT_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_good_guys_explorer:ZonesMulti");
    //  listManagerForm:homepage_business:tabViewListManager:Tab_good_guys_explorer:ZonesMulti
    private static final By BLACK_LIST_SELECT_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_good_guys_explorer:blackList");
    private static final By LIST_SET_SELECT_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_good_guys_explorer:listSet");
    private static final By ACCEPTED_TEXT_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_good_guys_explorer:acceptedText");
    private static final By SEARCH_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_good_guys_explorer:_btnSearchGoodGuys");
    private static final By RESET_BUTTON_LOCATOR = By.xpath("//span[text()='Reset']");
    private static final By TABLE_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_good_guys_explorer:_tblResults_data");
    private static final By CHECK_BOX_LOCATOR = By.cssSelector(".ui-selectbooleancheckbox");
    private static final By ADD_GG_BTN_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_good_guys_explorer:_tblResults:_btnNewGoodGuy");
    private static final By DELETE_GG_BTN_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_good_guys_explorer:_tblResults:_btnDelete");
    private static final By EXPORT_TYPE_SELECT_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_good_guys_explorer:_tblResults:exportTypeCbx");
    private static final By EXPORT_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_good_guys_explorer:_tblResults:_btnExport");
    private static final By IMPORT_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_good_guys_explorer:_tblResults:btnImport");
    private static final By EDIT_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_good_guys_explorer:_tblResults:_btnEditGoodGuy");

    private final Controls controls;
    private final Wait wait;

    public GoodGuyManager() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    @Step("Select Zone")
    public void select_zone(RemoteWebDriver driver, String zone) throws Exception {
        controls.selectOptionByDisplayedText(driver, ZONE_SELECT_LOCATOR, zone);
    }


    @Step("Select Black List")
    public void select_black_list(RemoteWebDriver driver, String black_list) throws Exception {
        controls.selectOptionByDisplayedText(driver, BLACK_LIST_SELECT_LOCATOR, black_list);
    }

    @Step("Select accepted text")
    public void set_accepted_text(RemoteWebDriver driver, String accepted_text) {
        controls.setTextBoxValue(driver, ACCEPTED_TEXT_LOCATOR, accepted_text);
    }

    @Step("Select List Set")
    public void select_list_set(RemoteWebDriver driver, String list_set) throws Exception {
        controls.selectOptionByDisplayedText(driver, LIST_SET_SELECT_LOCATOR, list_set);
    }

    @Step("Click Search Button")
    public void click_search_btn(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SEARCH_BUTTON_LOCATOR);
    }

    @Step("Good Guy Exist")
    public boolean verify_gg_exist(RemoteWebDriver driver, String acceptedText) {
        List<WebElement> scannedNameElement = controls.getWebElement(driver, TABLE_LOCATOR).findElements(By.xpath("//a[.='" + acceptedText + "']"));
        return scannedNameElement.size() > 0;
    }

    @Step("Click search button")
    public void click_search_button(RemoteWebDriver driver) {
        Allure.step("Click Search Button");
        controls.performClick(driver, SEARCH_BUTTON_LOCATOR);
    }

    @Step("Click Reset Button")
    public void click_reset_button(RemoteWebDriver driver) {
        Allure.step("Click Reset Button");
        controls.performClickByJS(driver, RESET_BUTTON_LOCATOR);
        driver.switchTo().alert().accept();
    }

    @Step("Click on check box")
    public void click_checkbox(RemoteWebDriver driver) {
        controls.performClick(driver, CHECK_BOX_LOCATOR);
    }

    @Step("Click Add GG Button")
    public void click_add_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, ADD_GG_BTN_LOCATOR);
    }

    @Step("Click Delete GG Button")
    public void click_delete_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, DELETE_GG_BTN_LOCATOR);
        driver.switchTo().alert().accept();
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Click Edit GG Button")
    public void click_edit_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, EDIT_BUTTON_LOCATOR);
    }

    @Step("Set Export Type")
    public void set_export_type(RemoteWebDriver driver, String exportType) throws Exception {
        controls.selectOptionByDisplayedText(driver, EXPORT_TYPE_SELECT_LOCATOR, exportType);
    }

    @Step("Click Export Button")
    public void click_export_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, EXPORT_BUTTON_LOCATOR);
        driver.switchTo().alert().accept();
    }

    @Step("Click Import Button")
    public void click_import_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, IMPORT_BUTTON_LOCATOR);
    }


}
