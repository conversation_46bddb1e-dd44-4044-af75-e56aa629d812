package eastnets.screening.gui.listManager.listExplorer;

import core.gui.Controls;
import core.util.Wait;
import eastnets.screening.entity.ListEntry;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.time.Duration;

public class ListExplorerManager {

    private static final By ZONE_INPUT_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_new_list_explorer:zoneCbx");
    private static final By LIST_SET_INPUT_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_new_list_explorer:listSetCbx");
    private static final By BLACK_LIST_INPUT_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_new_list_explorer:blackListCbx");
    private static final By VERSION_INPUT_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_new_list_explorer:versionCbx_label");
    private static final By CATEGORY_INPUT_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_new_list_explorer:categoryCbx");
    private static final By NAME_INPUT_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_new_list_explorer:name");
    private static final By ADDRESS_INPUT_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_new_list_explorer:address");
    private static final By PROGRAMS_INPUT_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_new_list_explorer:program");
    private static final By OTHER_INPUT_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_new_list_explorer:other");
    private static final By SEARCH_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_new_list_explorer:btnLESearch");
    private static final By RESET_BUTTON_LOCATOR = By.xpath("//button[.='Reset']");
    private static final By ADD_ENTRY_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_new_list_explorer:_tblResults:_btnNewEntry");
    private static final By DELETE_ENTRY_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_new_list_explorer:_tblResults:_btnDeleteEntry");
    private static final By ENABLE_ENTRY_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_new_list_explorer:_tblResults:_btnEnableEntry");
    private static final By DISABLE_ENTRY_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_new_list_explorer:_tblResults:_btnDisableEntry");
    private static final By IMPORT_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_new_list_explorer:_tblResults:_btnImport");
    private static final By EXPORT_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_new_list_explorer:_tblResults:_btnLEExport");
    private static final By EXPORT_TYPE_INPUT_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_new_list_explorer:_tblResults:leExportTypeCbx_label");
    private static final By COPY_TO_LIST_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_new_list_explorer:_tblResults:copyToBlackListCbx_label");
    private static final By COPY_ENTRY_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_new_list_explorer:_tblResults:_btnCopyEntry");
    private static final By PRINT_ENTRY_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_new_list_explorer:_tblResults:_btnPrintEntry");
    private static final By NAMES_LIST_LOCATOR = By.xpath("//tbody//tr//td[2]");
    private static final By CHECK_BOX_LOCATOR = By.xpath("//div[contains(@class,'ui-chkbox-box ui-widget ui-corner-all ui-state-default')]");
    private static final By ROWS_NUMBER_LOCATOR = By.xpath("//*[@id='listManagerForm:homepage_business:tabViewListManager:tab_new_list_explorer:_tblResults:lessThanMaximum' or @title='Number of rows displayed in the grid is limited to 100 000.']");

    private static final By FIRST_ENTRY_IN_TABLE_LOCATOR = By.xpath("//*[@id ='listManagerForm:homepage_business:tabViewListManager:tab_new_list_explorer:_tblResults_data']//tr[1]//a");
    public static By NO_RECORDS_MSG_LOCATOR = By.xpath("//*[.='No records found.']");
    public static final By CHECK_BOX_LOCATOR_LIST_MANAGER = By.xpath("//div[contains(@class,'ui-selectbooleancheckbox ui-chkbox ui-widget')]");
    public static final String IS_DISABLED = "//*[.='%s']//ancestor::tr[1]//td[6]/span";

    private final Controls controls;
    private final Wait wait;

    public ListExplorerManager() {
        this.controls = new Controls();
        this.wait = new Wait();
    }


    @Step("Click reset")
    public void click_reset(RemoteWebDriver driver) throws InterruptedException {
        controls.performClickByJS(driver, RESET_BUTTON_LOCATOR);
        controls.acceptAlert(driver);
        wait.time(Wait.ONE_SECOND * 2);
    }

    @Step("Click search button")
    public void click_search_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SEARCH_BUTTON_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);


    }

    @Step("Select zone")
    public void select_zone(RemoteWebDriver driver, String zone) throws Exception {
        controls.selectOptionByDisplayedText(driver, ZONE_INPUT_LOCATOR, zone);
    }


    @Step("Set entry name")
    public void set_entry_name(RemoteWebDriver driver, String name) {
        controls.setTextBoxValue(driver, NAME_INPUT_LOCATOR, name);
    }

    @Step("Select list set")
    public void select_list_set(RemoteWebDriver driver, String listSet) throws Exception {
        controls.selectOptionByDisplayedText(driver, LIST_SET_INPUT_LOCATOR, listSet);
    }

    @Step("Select list set")
    public void select_list_set_by_index(RemoteWebDriver driver, String listSet) throws Exception {
        controls.selectOptionByIndex(driver, LIST_SET_INPUT_LOCATOR, listSet);
    }

    @Step("Select black list")
    public boolean select_black_list(RemoteWebDriver driver, String blackListName) throws Exception {
        return controls.selectOptionByDisplayedText(driver, BLACK_LIST_INPUT_LOCATOR, blackListName);
    }

    @Step("Select entry category")
    public void select_entry_category(RemoteWebDriver driver, String entryType) throws Exception {
        controls.selectOptionByDisplayedText(driver, CATEGORY_INPUT_LOCATOR, entryType);
    }

    @Step("Set address")
    public void set_address(RemoteWebDriver driver, String address) {
        controls.setTextBoxValue(driver, ADDRESS_INPUT_LOCATOR, address);
    }

    @Step("Set program")
    public void set_program(RemoteWebDriver driver, String program) {
        controls.setTextBoxValue(driver, PROGRAMS_INPUT_LOCATOR, program);
    }

    @Step("Set aka")
    public void set_aka(RemoteWebDriver driver, String aka) {
        controls.setTextBoxValue(driver, NAME_INPUT_LOCATOR, aka);
    }

    @Step("Click Check Box")
    public void click_check_box(RemoteWebDriver driver) {
        controls.performClick(driver, CHECK_BOX_LOCATOR);
    }

    @Step("Click Add Button")
    public void click_add_button(RemoteWebDriver driver) {
        Allure.step("Click add new entry button.");
        controls.waitAndClick(driver, ADD_ENTRY_BUTTON_LOCATOR, Duration.ofSeconds(2));
    }

    @Step("Click Delete Button")
    public void click_delete_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, DELETE_ENTRY_BUTTON_LOCATOR);
        driver.switchTo().alert().accept();
    }

    @Step("Click Enable Button")
    public void click_enable_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, ENABLE_ENTRY_BUTTON_LOCATOR);
    }

    @Step("Click Disable Button")
    public void click_disable_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, DISABLE_ENTRY_BUTTON_LOCATOR);
    }

    @Step("Click Import Button")
    public void click_import_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, IMPORT_BUTTON_LOCATOR);
    }

    @Step("CSet export type")
    public void set_export_type(RemoteWebDriver driver, String type) throws Exception {
        controls.selectOptionByDisplayedText(driver, EXPORT_TYPE_INPUT_LOCATOR, type);
    }

    @Step("Click Export Button")
    public void click_export_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, EXPORT_BUTTON_LOCATOR);
        driver.switchTo().alert().accept();
        wait.waitForJStoLoad(driver);
    }

    @Step("Copy entry")
    public void copy_entry(RemoteWebDriver driver, String copyToListName) {
        controls.setSelectByVisibleText(driver, COPY_TO_LIST_LOCATOR, copyToListName);
        controls.performClickByJS(driver, COPY_ENTRY_BUTTON_LOCATOR);
    }

    @Step("Print entry")
    public void print_entry(RemoteWebDriver driver) {
        controls.performClickByJS(driver, PRINT_ENTRY_BUTTON_LOCATOR);
    }

    @Step("Verify entry exist")
    public boolean verify_entry_exist(RemoteWebDriver driver, ListEntry entry) {
        if (entry.getFirstName() == null)
            return controls.exists(driver, By.xpath("//tbody//tr//td[.='" + entry.getName() + "']"));
        return controls.exists(driver, By.xpath("//tbody//tr//td[.='" + entry.getName() + ", " + entry.getFirstName() + "']"));
    }

    @Step("Select entry by name")
    public void select_entry_by_name(RemoteWebDriver driver, ListEntry entry) {
        Allure.step("Select list set by name.");
        if (entry.getFirstName() == null)
            controls.performClickByJS(driver, By.xpath(String.format("//a[.='%s']", entry.getName())));
        else
            controls.performClick(driver, By.xpath(String.format("//a[.='%s']", entry.getName() + ", " + entry.getFirstName())));
    }

    @Step("Select first entry")
    public void select_first_entry(RemoteWebDriver driver) {
        controls.performClickByJS(driver, FIRST_ENTRY_IN_TABLE_LOCATOR);
    }

    @Step("Get rows number")
    public String get_rows_number(RemoteWebDriver driver) {
        return controls.getElementText(driver, ROWS_NUMBER_LOCATOR);
    }

    @Step("Get disabled status")
    public String get_disabled_status(RemoteWebDriver driver, String name) {
        return driver.findElement(By.xpath(String.format(IS_DISABLED, name))).getText();

    }

    @Step("Get is Disabled status")
    public Boolean is_disabled(RemoteWebDriver driver, String entityName) throws InterruptedException {
        Allure.step("Get is Disabled status");
        wait.time(Wait.ONE_SECOND * 3);
        if (get_disabled_status(driver, entityName).equalsIgnoreCase("true"))
            return true;
        else
            return false;
    }

    @Step("Get First Entry Name")
    public String get_first_entry_name(RemoteWebDriver driver) {
        return driver.findElement(FIRST_ENTRY_IN_TABLE_LOCATOR).getText();

    }

}