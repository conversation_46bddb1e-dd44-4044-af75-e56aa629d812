package eastnets.screening.gui.listManager.listExplorer;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.text.SimpleDateFormat;
import java.util.Date;

public class ListExplorerEditor {

    //---------------------Add Entry Locators---------------------//

    //Tabs Locators
    private static final By DETAILS_TAB_LOCATOR = By.linkText("Details");
    private static final By ADDRESS_TAB_LOCATOR = By.linkText("Address");
    private static final By GOOD_GUY_TAB_LOCATOR = By.linkText("Good Guy");

    //Types & Names Tab Locators
    private static final By TYPE_INPUT_LOCATOR = By.id("listExplorerEditorForm:viewDetails:ListEditorTabs:view_details:entryEditorTabs:listExplorerTypeAndNames:typeCbx");
    private static final By NAME_INPUT_LOCATOR = By.id("listExplorerEditorForm:viewDetails:ListEditorTabs:view_details:entryEditorTabs:listExplorerTypeAndNames:name");
    private static final By FIRST_NAME_INPUT_LOCATOR = By.id("listExplorerEditorForm:viewDetails:ListEditorTabs:view_details:entryEditorTabs:listExplorerTypeAndNames:firstNameField");
    private static final By TITLE_INPUT_LOCATOR = By.id("listExplorerEditorForm:viewDetails:ListEditorTabs:view_details:entryEditorTabs:listExplorerTypeAndNames:title");
    private static final By ADD_AKA_BUTTON_LOCATOR = By.id("listExplorerEditorForm:viewDetails:ListEditorTabs:view_details:entryEditorTabs:listExplorerTypeAndNames:_btnAddAka");
    private static final By AKA_TYPE_SELECT_LOCATOR = By.id("listExplorerEditorForm:viewDetails:ListEditorTabs:view_details:entryEditorTabs:listExplorerTypeAndNames:akaType");
    private static final By AKA_FIRST_NAME_INPUT_LOCATOR = By.id("listExplorerEditorForm:viewDetails:ListEditorTabs:view_details:entryEditorTabs:listExplorerTypeAndNames:akaFirstName");
    private static final By AKA_LAST_NAME_INPUT_LOCATOR = By.id("listExplorerEditorForm:viewDetails:ListEditorTabs:view_details:entryEditorTabs:listExplorerTypeAndNames:akaLastName");
    private static final By AKA_SAVE_BUTTON_LOCATOR = By.id("listExplorerEditorForm:viewDetails:ListEditorTabs:view_details:entryEditorTabs:listExplorerTypeAndNames:_btnAkaOk");
    private static final By SAVE_BUTTON_LOCATOR = By.id("listExplorerEditorForm:viewDetails:ListEditorTabs:listExplorerDetails:btnSave");
    private static final By CANCEL_BUTTON_LOCATOR = By.id("listExplorerEditorForm:viewDetails:ListEditorTabs:listExplorerDetails:btnCancel");


    //Details Tab Locators
    private static final By BIRTHDAY_INPUT_LOCATOR = By.id("listExplorerEditorForm:viewDetails:ListEditorTabs:view_details:entryEditorTabs:listExplorerDetails:birthDate");
    private static final By EXTERNAL_ID_INPUT_LOCATOR = By.id("listExplorerEditorForm:viewDetails:ListEditorTabs:view_details:entryEditorTabs:listExplorerDetails:externalId");

    private static final By GENDER_DDL_LOCATOR = By.id("listExplorerEditorForm:viewDetails:ListEditorTabs:view_details:entryEditorTabs:listExplorerDetails:gender_label");
    //Good Guy Tab Locators
    private static final By ADD_GG_BUTTON_LOCATOR = By.id("listExplorerEditorForm:viewDetails:ListEditorTabs:listExplorerGoodGuys:_tblListExplorerGoodGuys:_btnAddGoodGuy");
    private static final By ACCEPTED_TEXT_INPUT_LOCATOR = By.id("goodGuyEditorForm:Good_guy_management:name");
    private static final By RANK_SELECT_LOCATOR = By.id("goodGuyEditorForm:Good_guy_management:rank");

    //Address Tab Locators
    private static final By ADD_ADDRESS_BUTTON_LOCATOR = By.id("listExplorerEditorForm:viewDetails:ListEditorTabs:view_details:entryEditorTabs:listExplorerAddress:_tblEntityAddresses:_btnAddAddress");
    private static final By ADDRESS_TEXT_INPUT_LOCATOR = By.id("listExplorerEditorForm:viewDetails:ListEditorTabs:view_details:entryEditorTabs:listExplorerAddress:address");
    private static final By CITY_TEXT_INPUT_LOCATOR = By.id("listExplorerEditorForm:viewDetails:ListEditorTabs:view_details:entryEditorTabs:listExplorerAddress:city");
    private static final By COUNTRY_TEXT_INPUT_LOCATOR = By.id("listExplorerEditorForm:viewDetails:ListEditorTabs:view_details:entryEditorTabs:listExplorerAddress:country");
    private static final By OK_BUTTON_LOCATOR = By.id("listExplorerEditorForm:viewDetails:ListEditorTabs:view_details:entryEditorTabs:listExplorerAddress:_btnAkaOk");


    private static final By SCAN_GG_BUTTON_LOCATOR = By.id("goodGuyEditorForm:Good_guy_management:btnScanGG");
    private static final By MATCHED_NAME_CHECK_BOX_LOCATOR = By.cssSelector("[id='goodGuyEditorForm:Good_guy_management:goodGuysTable_data'] div.ui-chkbox-box");
    private static final By ACCEPT_BTN_LOCATOR = By.id("goodGuyEditorForm:Good_guy_management:btnAcceptGG");


    //Import Entry Locators
    private static final By FILE_TO_IMPORT_BUTTON_LOCATOR = By.id("ListManagerForm:ListExplorer_import_business:file_input");
    private static final By ZONE_SELECT_LOCATOR = By.id("ListManagerForm:ListExplorer_import_business:zoneCbx");
    private static final By BLACK_LIST_SELECT_LOCATOR = By.id("ListManagerForm:ListExplorer_import_business:blacklistCbx");
    private static final By VERSION_SELECT_LOCATOR = By.id("ListManagerForm:ListExplorer_import_business:blackListVersionCbx");
    private static final By FORMAT_SELECT_LOCATOR = By.id("ListManagerForm:ListExplorer_import_business:formatCbx");
    private static final By ENCODING_SELECT_LOCATOR = By.id("ListManagerForm:ListExplorer_import_business:encodingCbx");
    private static final By IMPORT_BUTTON_LOCATOR = By.id("ListManagerForm:ListExplorer_import_business:_btnImport");
    private static final String APPEND_CHECKBOX_LOCATOR = "ListManagerForm:ListExplorer_import_business:appendFlag";
    private static final String LOCK_CHECKBOX_LOCATOR = "ListManagerForm:ListExplorer_import_business:lockFlag";
    private static final String UPGRADE_CHECKBOX_LOCATOR = "ListManagerForm:ListExplorer_import_business:upgradeFlag";
    private static final String MIGRATE_CHECKBOX_LOCATOR = "ListManagerForm:ListExplorer_import_business:migrateFlag";
    private static final String EXTRA_AKAS_CHECKBOX_LOCATOR = "ListManagerForm:ListExplorer_import_business:extraAKAsFlag";


    //Details Tab Locators
    private static final By PROGRAMS_INPUT_LOCATOR = By.id("listExplorerEditorForm:viewDetails:ListEditorTabs:view_details:entryEditorTabs:listExplorerDetails:programs");
    private static final By LAST_OCCUPATION_INPUT_LOCATOR = By.id("listExplorerEditorForm:viewDetails:ListEditorTabs:view_details:entryEditorTabs:listExplorerDetails:lastOccupation");
    private static final By BRITH_DATE_INPUT_LOCATOR = By.id("listExplorerEditorForm:viewDetails:ListEditorTabs:view_details:entryEditorTabs:listExplorerDetails:birthDate");

    private static final By BIRTH_COUNTRY_INPUT_LOCATOR = By.id("listExplorerEditorForm:viewDetails:ListEditorTabs:view_details:entryEditorTabs:listExplorerDetails:birthCountry");
    private static final By COUNTRY_INPUT_LOCATOR = By.id("listExplorerEditorForm:viewDetails:ListEditorTabs:view_details:entryEditorTabs:listExplorerDetails:birthDate");
    private static final By RESIDENCE_COUNTRY_INPUT_LOCATOR = By.id("listExplorerEditorForm:viewDetails:ListEditorTabs:view_details:entryEditorTabs:listExplorerDetails:residenceCountry");
    private static final By NATIONLITY_INPUT_LOCATOR = By.id("listExplorerEditorForm:viewDetails:ListEditorTabs:view_details:entryEditorTabs:listExplorerDetails:nationality");
    private static final By EXTERNAL_ID_COUNTRY_INPUT_LOCATOR = By.id("listExplorerEditorForm:viewDetails:ListEditorTabs:view_details:entryEditorTabs:listExplorerDetails:externalId");
    private static final By INTERNAL_ID_COUNTRY_INPUT_LOCATOR = By.id("listExplorerEditorForm:viewDetails:ListEditorTabs:view_details:entryEditorTabs:listExplorerDetails:id");
    private static final By GENDER_INPUT_LOCATOR = By.id("listExplorerEditorForm:viewDetails:ListEditorTabs:view_details:entryEditorTabs:listExplorerDetails:birthDate");

    private final Controls controls;
    private final Wait wait;

    public ListExplorerEditor() {
        this.controls = new Controls();
        this.wait = new Wait();
    }


    @Step("Select type")
    public void select_type(RemoteWebDriver driver, String type) throws Exception {
        controls.selectOptionByDisplayedText(driver, TYPE_INPUT_LOCATOR, type);
    }

    @Step("set name")
    public void set_name(RemoteWebDriver driver, String name) {
        controls.setTextBoxValue(driver, NAME_INPUT_LOCATOR, name);
    }

    @Step("set first name")
    public void set_first_name(RemoteWebDriver driver, String first_name) {
        controls.setTextBoxValue(driver, FIRST_NAME_INPUT_LOCATOR, first_name);
    }

    @Step("Set title")
    public void set_title(RemoteWebDriver driver, String title) {
        controls.setTextBoxValue(driver, TITLE_INPUT_LOCATOR, title);
    }


    @Step("Click details tab")
    public void click_details_tab(RemoteWebDriver driver) {
        controls.performClick(driver, DETAILS_TAB_LOCATOR);

    }

    @Step("Click address tab")
    public void click_address_tab(RemoteWebDriver driver) {
        controls.performClick(driver, ADDRESS_TAB_LOCATOR);

    }

    @Step("Select gender")
    public boolean select_gender(RemoteWebDriver driver, String gender) throws Exception {
        return controls.selectOptionByDisplayedText(driver, GENDER_DDL_LOCATOR, gender);
    }

    @Step("Click good-guy tab")
    public void click_good_guy_tab(RemoteWebDriver driver) {
        controls.performClick(driver, GOOD_GUY_TAB_LOCATOR);
    }

    @Step("Click add good-guy button")
    public void click_add_good_guy_btn(RemoteWebDriver driver) {
        controls.performClickByJS(driver, ADD_GG_BUTTON_LOCATOR);
    }

    @Step("Set accepted text")
    public void set_accepted_text(RemoteWebDriver driver, String accepted_text) {
        controls.setTextBoxValue(driver, ACCEPTED_TEXT_INPUT_LOCATOR, accepted_text);
    }

    @Step("Select rank")
    public void select_rank(RemoteWebDriver driver, String rank) throws Exception {
        controls.selectOptionByDisplayedText(driver, RANK_SELECT_LOCATOR, rank);
    }

    @Step("Click scan button")
    public void click_scan_btn(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SCAN_GG_BUTTON_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Select first matched entity")
    public void select_first_matched_entity(RemoteWebDriver driver) {
        controls.performClick(driver, MATCHED_NAME_CHECK_BOX_LOCATOR);
    }

    @Step("Click accept good guy button")
    public void click_accept_btn(RemoteWebDriver driver) {
        controls.performClick(driver, ACCEPT_BTN_LOCATOR);
    }

    @Step("Click add aka button")
    public void click_add_aka_btn(RemoteWebDriver driver) {
        controls.performClickByJS(driver, ADD_AKA_BUTTON_LOCATOR);
    }

    @Step("Select aka type")
    public void select_aka_type(RemoteWebDriver driver, String aka_type) throws Exception {
        controls.selectOptionByDisplayedText(driver, AKA_TYPE_SELECT_LOCATOR, aka_type);
    }

    @Step("Set aka first name")
    public void set_aka_first_name(RemoteWebDriver driver, String aka_first_name) {
        controls.setTextBoxValue(driver, AKA_FIRST_NAME_INPUT_LOCATOR, aka_first_name);
    }

    @Step("Set aka last name")
    public void set_aka_last_name(RemoteWebDriver driver, String aka_last_name) {
        controls.setTextBoxValue(driver, AKA_LAST_NAME_INPUT_LOCATOR, aka_last_name);
    }

    @Step("Click save aka button")
    public void click_save_aka_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, AKA_SAVE_BUTTON_LOCATOR);
    }

    @Step("Set birthday")
    public void set_birthday(RemoteWebDriver driver, String birthday) {
        controls.setTextBoxValue(driver, BIRTHDAY_INPUT_LOCATOR, birthday);
    }

    @Step("Set program")
    public void set_program(RemoteWebDriver driver, String program) {
        controls.setTextBoxValue(driver, PROGRAMS_INPUT_LOCATOR, program);
    }

    @Step("set external id")
    public void set_external_id(RemoteWebDriver driver, String external_id) {
        controls.setTextBoxValue(driver, EXTERNAL_ID_INPUT_LOCATOR, external_id);
    }

    @Step("Click add address button")
    public void click_add_address_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, ADD_ADDRESS_BUTTON_LOCATOR);
    }

    @Step("set address")
    public void set_address(RemoteWebDriver driver, String address) {
        controls.setTextBoxValue(driver, ADDRESS_TEXT_INPUT_LOCATOR, address);
    }

    @Step("set city")
    public void set_city(RemoteWebDriver driver, String city) {
        controls.setTextBoxValue(driver, CITY_TEXT_INPUT_LOCATOR, city);
    }

    @Step("set country")
    public void set_country(RemoteWebDriver driver, String country) {
        controls.setTextBoxValue(driver, COUNTRY_TEXT_INPUT_LOCATOR, country);
    }

    @Step("Click ok button")
    public void click_ok(RemoteWebDriver driver) {
        controls.performClickByJS(driver, OK_BUTTON_LOCATOR);
    }


    @Step("Click save button")
    public void click_save_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SAVE_BUTTON_LOCATOR);
        driver.switchTo().alert().accept();
        wait.waitUntilAjaxLoaderDisappear(driver);
    }


    @Step("Click cancel button")
    public void click_cancel_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, CANCEL_BUTTON_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Set file to import")
    public void set_file_to_import(RemoteWebDriver driver, String filePath) throws Exception {
        controls.setTextBoxValue(driver, FILE_TO_IMPORT_BUTTON_LOCATOR, filePath);
        wait.time(Wait.ONE_SECOND);
    }

    @Step("Select zone")
    public void select_zone(RemoteWebDriver driver, String zoneName) throws Exception {
        controls.selectOptionByDisplayedText(driver, ZONE_SELECT_LOCATOR, zoneName);
    }

    @Step("Select black list")
    public void select_black_list(RemoteWebDriver driver, String blackListName) throws Exception {
        controls.selectOptionByDisplayedText(driver, BLACK_LIST_SELECT_LOCATOR, blackListName);
        wait.time(Wait.ONE_SECOND*3);
    }

    @Step("Select version")
    public void select_version(RemoteWebDriver driver) throws Exception {
        String version = "v1 - " + new SimpleDateFormat("yyyy/mm/dd").format(new Date());
        Allure.step("Select version : " + version);
        controls.selectOptionByIndex(driver, VERSION_SELECT_LOCATOR, "1");
        controls.getWebElement(driver,By.xpath("//*[.='Custom list Import']")).click();
        if (controls.getElementText(driver, VERSION_SELECT_LOCATOR).equals("< None >")) {
           // controls.performClick(driver, VERSION_SELECT_LOCATOR);
            controls.selectOptionByIndex(driver, VERSION_SELECT_LOCATOR, "1");
        }

    }

    @Step("Select format")
    public void select_format(RemoteWebDriver driver, String format) throws Exception {
        controls.selectOptionByDisplayedText(driver, FORMAT_SELECT_LOCATOR, format);

    }

    @Step("Select encoding")
    public void select_encoding(RemoteWebDriver driver, String encoding) throws Exception {
        controls.selectOptionByDisplayedText(driver, ENCODING_SELECT_LOCATOR, encoding);
    }

    @Step("Click import file")
    public void click_import_file(RemoteWebDriver driver) {
        controls.performClickByJS(driver, IMPORT_BUTTON_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Click on lock checkbox")
    public void click_lock_check_box(RemoteWebDriver driver, boolean flag) {
        controls.setCheckboxValueById(driver, LOCK_CHECKBOX_LOCATOR, flag);
    }

    @Step("Click on upgrade checkbox")
    public void click_upgrade_check_box(RemoteWebDriver driver, boolean flag) {
        controls.setCheckboxValueById(driver, UPGRADE_CHECKBOX_LOCATOR, flag);
    }

    @Step("Click on migrate checkbox")
    public void click_migrate_check_box(RemoteWebDriver driver, boolean flag) {
        Allure.step(String.format("Click on migrate checkbox", flag));
        controls.setCheckboxValueById(driver, MIGRATE_CHECKBOX_LOCATOR, flag);
    }

    @Step("Click on append checkbox")
    public void click_append_check_box(RemoteWebDriver driver, boolean flag) {
        controls.setCheckboxValueById(driver, APPEND_CHECKBOX_LOCATOR, flag);
    }

    @Step("Click on extra AKAs checkbox")
    public void click_extra_aka_check_box(RemoteWebDriver driver, boolean flag) {
        Allure.step(String.format("Click on Extra AKAs checkbox", flag));
        controls.setCheckboxValueById(driver, EXTRA_AKAS_CHECKBOX_LOCATOR, flag);
    }

    @Step("Set Name of entry")
    public void set_entry_name(RemoteWebDriver driver, String entryName) {
        controls.setTextBoxValue(driver, NAME_INPUT_LOCATOR, entryName);
    }
}
