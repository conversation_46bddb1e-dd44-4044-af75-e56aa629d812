package eastnets.screening.gui.listManager.blackList;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class BlackListEditor {

    private static final By LIST_NAME_INPUT_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:Sub_black_list_creation_viewer:name");
    private static final By OFFICIAL_DATE_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:Sub_black_list_creation_viewer:officialDate_input");
    private static final By SAVE_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:Sub_black_list_creation_viewer:btnBLCSave");
    private static final By ZONE_SELECT_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:sub_black_list_import_viewer:zoneCbx");
    private static final By FILE_TO_IMPORT_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:sub_black_list_import_viewer:file_input");
    private static final String UPGRADE_LIST_CHECK_BOX_LOCATOR = "listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:sub_black_list_import_viewer:upgradeFlag";
    private static final String LOCK_LIST_CHECK_BOX_LABEL_LOCATOR = "listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:sub_black_list_import_viewer:lockFlag";
    private static final By IMPORT_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:sub_black_list_import_viewer:_btnImport");


    /******************************* Black List Editor Locators *******************************/
    private static final By ADD_VERSION_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:Sub_black_list_edition:Sub_black_list_detail_viewer:_tblResults:_btnAdd");
    private static final By LOCK_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:Sub_black_list_edition:Sub_black_list_detail_viewer:_tblResults:_btnLock");
    private static final By UPGRADE_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:Sub_black_list_edition:Sub_black_list_detail_viewer:_tblResults:_btnUpgrade");
    private static final By DELETE_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:Sub_black_list_edition:Sub_black_list_detail_viewer:_tblResults:_btnDelete");
    private static final By EXPORT_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:Sub_black_list_edition:Sub_black_list_detail_viewer:_tblResults:_btnExport");

    /***** Version Creation Locators *****/
    private static final By OFFICIAL_DATE_Ver_INPUT_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:Sub_black_list_edition:Sub_bl_historyCreation_viewer:officialDate_input");
    private static final By SAVE_VERSION_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:Sub_black_list_edition:Sub_bl_historyCreation_viewer:btnBHCSave");

    private static final By BL_NAME_INPUT_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:Sub_black_list_edition:Sub_black_list_detail_viewer:name");
    private static final By VERSION_HISTORY_CHECKBOX_LOCATOR = By.xpath("//div[contains(@id,'listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:Sub_black_list_edition:Sub_black_list_detail_viewer:_tblResults:0:')]");
    private static final By LOCK_LIST_VERIFICATION_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:Sub_black_list_edition:Sub_black_list_detail_viewer:_tblResults:0:_lockedImg");
    private static final By BL_EDITOR_SAVE_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:Sub_black_list_edition:Sub_black_list_detail_viewer:btnBLDSave");
    private static final By BL_EDITOR_CANCEL_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:Sub_black_list_edition:Sub_black_list_detail_viewer:btnCancel");
    private static final String AUTO_BLOCK_CHECKBOX_LOCATOR = "listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:Sub_black_list_edition:Sub_black_list_detail_viewer:autoBlock";

    //Export Form Locators
    private static final By EXPORT_SUBMIT_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:sub_black_list_export_viewer:_btnExport");

    private final Controls controls;
    private final Wait wait;

    public BlackListEditor() {
        this.controls = new Controls();
        this.wait = new Wait();
    }


    @Step("Set black list name")
    public void set_name(RemoteWebDriver driver, String name) {
        controls.setTextBoxValue(driver, LIST_NAME_INPUT_LOCATOR, name);
    }

    @Step("Set official date")
    public void set_official_date(RemoteWebDriver driver, String official_date) {
        controls.setTextBoxValue(driver, OFFICIAL_DATE_LOCATOR, official_date);
    }

    @Step("Click save button")
    public void click_save(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SAVE_BUTTON_LOCATOR);
        driver.switchTo().alert().accept();
        wait.waitUntilAjaxLoaderDisappear(driver);

    }

    public void wait_until_list_name_appear(RemoteWebDriver driver) {
        wait.waitUntilElementToDisappear(driver, LIST_NAME_INPUT_LOCATOR, Wait.TEN_SECOND_DURATION);
    }

    @Step("Select zone")
    public void select_zone(RemoteWebDriver driver, String zone) throws Exception {
        controls.selectOptionByDisplayedText(driver, ZONE_SELECT_LOCATOR, zone);
    }

    @Step("Set file path")
    public void set_file_path(RemoteWebDriver driver, String path) {
        controls.setTextBoxValue(driver, FILE_TO_IMPORT_BUTTON_LOCATOR, path);
    }

    @Step("Set lock flag")
    public void set_lock_flag(RemoteWebDriver driver, boolean flag) {
        controls.setCheckboxValueById(driver, LOCK_LIST_CHECK_BOX_LABEL_LOCATOR, flag);
    }

    @Step("Set upgrade flag")
    public void set_upgrade_flag(RemoteWebDriver driver, boolean flag) {
        controls.setCheckboxValueById(driver, UPGRADE_LIST_CHECK_BOX_LOCATOR, flag);
    }

    @Step("Click import button")
    public void click_import_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, IMPORT_BUTTON_LOCATOR);
    }

    @Step("Click export button")
    public void click_export_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, EXPORT_BUTTON_LOCATOR);
    }

    @Step("Select version history checkbox")
    public void select_ver_history_checkbox(RemoteWebDriver driver) {
        controls.performClick(driver, VERSION_HISTORY_CHECKBOX_LOCATOR);
    }

    @Step("Select export type")
    public void select_export_type(RemoteWebDriver driver, String type) {
        controls.performClick(driver, By.xpath(String.format("//td[.='%s']//div", type)));
    }

    @Step("Click export button")
    public void click_export_submit_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, EXPORT_SUBMIT_BUTTON_LOCATOR);
    }

    @Step("Click lock button")
    public void click_lock_button(RemoteWebDriver driver) throws InterruptedException {
        controls.performClickByJS(driver, LOCK_BUTTON_LOCATOR);
        driver.switchTo().alert().accept();
        wait.waitUntilAjaxLoaderDisappear(driver);
        wait.time(Wait.ONE_SECOND * 2);

    }

    @Step("Verify if list locked")
    public boolean verify_if_list_locked(RemoteWebDriver driver) {
        return controls.exists(driver, LOCK_LIST_VERIFICATION_LOCATOR);
    }

    @Step("Click Save Button")
    public void click_Save_button_editor(RemoteWebDriver driver) {
        controls.performClickByJS(driver, BL_EDITOR_SAVE_BUTTON_LOCATOR);
        driver.switchTo().alert().accept();
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Verify Cancel Button Exist")
    public boolean verify_cancel_button_editor_exist(RemoteWebDriver driver) {
        return controls.exists(driver, BL_EDITOR_CANCEL_BUTTON_LOCATOR);
    }

    @Step("Click Cancel Button")
    public void click_cancel_button_editor(RemoteWebDriver driver) {
        controls.performClickByJS(driver, BL_EDITOR_CANCEL_BUTTON_LOCATOR);
    }

    @Step("Click Cancel button")
    public void select_auto_block_option(RemoteWebDriver driver, boolean flag) {
        controls.setCheckboxValueById(driver, AUTO_BLOCK_CHECKBOX_LOCATOR, flag);
    }

    @Step("Click upgrade button")
    public void click_upgrade_button_editor(RemoteWebDriver driver) {
        controls.performClickByJS(driver, UPGRADE_BUTTON_LOCATOR);
        driver.switchTo().alert().accept();
        wait.waitUntilAjaxLoaderDisappear(driver);
    }


    @Step("Click delete button")
    public void click_delete_button_editor(RemoteWebDriver driver) {
        controls.performClickByJS(driver, DELETE_BUTTON_LOCATOR);
        driver.switchTo().alert().accept();
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("set name")
    public void set_name_editor(RemoteWebDriver driver, String updatedName) {
        controls.setTextBoxValue(driver, BL_NAME_INPUT_LOCATOR, updatedName);

    }

    @Step("Click Add version button")
    public void click_add_version(RemoteWebDriver driver) {
        controls.performClickByJS(driver, ADD_VERSION_BUTTON_LOCATOR);
    }

    @Step("Set official date")
    public void set_ver_date(RemoteWebDriver driver, String version) {
        controls.setTextBoxValue(driver, OFFICIAL_DATE_Ver_INPUT_LOCATOR, version);
    }

    @Step("Click save button")
    public void click_save_version(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SAVE_VERSION_BUTTON_LOCATOR);
    }


}
