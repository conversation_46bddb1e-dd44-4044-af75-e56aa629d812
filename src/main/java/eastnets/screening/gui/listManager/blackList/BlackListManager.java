package eastnets.screening.gui.listManager.blackList;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class BlackListManager {

    private static final By RESET_BUTTON_LOCATOR = By.xpath("//span[contains(text(),'Reset')]");
    private static final By SEARCH_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:Sub_black_list_viewer:btnBLVSearch");
    private static final By CREATE_NEW_LIST_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:Sub_black_list_viewer:_tblResults:_btnNew");
    private static final By SEARCH_VALIDATION_LOCATOR = By.xpath("//tbody[@id='listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:Sub_black_list_viewer:_tblResults_data']//tr");
    private static final By ZONE_NAME_SEARCH_INPUT_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:Sub_black_list_viewer:zoneCbx_label");
    private static final By PRIVATE_FLAG_SEARCH_INPUT_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:Sub_black_list_viewer:privateCbx");
    private static final By LIST_NAME_SEARCH_INPUT_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:Sub_black_list_viewer:nameField");
    private static final By OFFICIAL_ID_SEARCH_INPUT_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:Sub_black_list_viewer:LatestOfficialDateCbx_label");
    private static final By DELETE_LIST_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:Sub_black_list_viewer:_tblResults:_btnDelete");
    private static final By SHARE_LIST_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:Sub_black_list_viewer:_tblResults:_btnShare");
    private static final By IMPORT_LIST_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:Sub_black_list_viewer:_tblResults:_btnImport");
    private static final By CHECK_BOX_LOCATOR = By.xpath("//div[contains(@class,'ui-selectbooleancheckbox ui-chkbox ui-widget')]");
    private static final By FIRST_RESULT_LOCATOR = By.xpath("//a[contains(@id,':0:linkId')]");

    private final Controls controls;
    private final Wait wait;

    public BlackListManager() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    @Step("Verify if reset button visible.")
    public boolean is_reset_button_visible(RemoteWebDriver driver) {
        return controls.exists(driver, RESET_BUTTON_LOCATOR);
    }

    @Step("Verify if search button visible.")
    public boolean is_search_button_visible(RemoteWebDriver driver) {
        return controls.exists(driver, SEARCH_BUTTON_LOCATOR);
    }

    @Step("Verify if create new list button visible.")
    public boolean is_create_new_list_button_visible(RemoteWebDriver driver) {
        return controls.exists(driver, CREATE_NEW_LIST_BUTTON_LOCATOR);
    }

    @Step("Verify if delete visible.")
    public boolean is_delete_list_button_visible(RemoteWebDriver driver) {
        return controls.exists(driver, DELETE_LIST_BUTTON_LOCATOR);
    }

    @Step("Verify if share visible.")
    public boolean is_share_list_button_visible(RemoteWebDriver driver) {
        return controls.exists(driver, SHARE_LIST_BUTTON_LOCATOR);
    }

    @Step("Verify if import visible.")
    public boolean is_import_list_button_visible(RemoteWebDriver driver) {
        return controls.exists(driver, IMPORT_LIST_BUTTON_LOCATOR);
    }

    @Step("Reset search")
    public void reset_search(RemoteWebDriver driver) throws InterruptedException {
        controls.performClickByJS(driver, RESET_BUTTON_LOCATOR);
        driver.switchTo().alert().accept();
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Click search button")
    public void click_search(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SEARCH_BUTTON_LOCATOR);
        wait.waitUntilElementToBeVisible(driver, CREATE_NEW_LIST_BUTTON_LOCATOR, Wait.TEN_SECOND_DURATION);
        wait.waitUntilAjaxLoaderDisappear(driver);

    }

    @Step("Set Black List Name")
    public void set_name(RemoteWebDriver driver, String listName) {
        controls.setTextBoxValue(driver, LIST_NAME_SEARCH_INPUT_LOCATOR, listName);
    }

    @Step("Set zone")
    public void set_zone(RemoteWebDriver driver, String zone) throws Exception {
        controls.selectOptionByDisplayedText(driver, ZONE_NAME_SEARCH_INPUT_LOCATOR, zone);
    }

    @Step("Select private flag")
    public void select_private_flag(RemoteWebDriver driver, String privateFlag) throws Exception {
        controls.scrollUp(driver);
        controls.selectOptionByDisplayedText(driver, PRIVATE_FLAG_SEARCH_INPUT_LOCATOR, privateFlag);
    }

    public void select_checkbox(RemoteWebDriver driver) {
        controls.performClick(driver, CHECK_BOX_LOCATOR);
    }

    @Step("Click delete button")
    public void click_delete_list_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, DELETE_LIST_BUTTON_LOCATOR);
        driver.switchTo().alert().accept();
    }

    @Step("Click share button")
    public void click_share_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SHARE_LIST_BUTTON_LOCATOR);
        driver.switchTo().alert().accept();
    }

    @Step("Click add new list button")
    public void click_add_new_list_button(RemoteWebDriver driver) {
        wait.waitUntilElementToBeClickable(driver, CREATE_NEW_LIST_BUTTON_LOCATOR, Wait.TEN_SECOND_DURATION);
        controls.performClickByJS(driver, CREATE_NEW_LIST_BUTTON_LOCATOR);
    }

    @Step("Click import new list button")
    public void clickImportButton(RemoteWebDriver driver) {
        wait.waitUntilElementToBeClickable(driver, IMPORT_LIST_BUTTON_LOCATOR, Wait.TEN_SECOND_DURATION);
        controls.performClickByJS(driver, IMPORT_LIST_BUTTON_LOCATOR);
    }

    @Step("Verify if black list exist.")
    public boolean verify_list_exist(RemoteWebDriver driver, String listName) throws Exception {
        return controls.exists(driver, By.xpath(String.format("//tbody//tr//td[.='%s']", listName)));
    }

    @Step("Select first result from result table")
    public boolean select_first_result_from_table(RemoteWebDriver driver) {
        controls.performClick(driver, FIRST_RESULT_LOCATOR);
        wait.waitUntilElementToBeVisible(driver, By.xpath("//*[.='Black List Edition']"), Wait.TEN_SECOND_DURATION);
        return controls.exists(driver, By.xpath("//*[.='Black List Edition']"));
    }
}