package eastnets.screening.gui.listManager.listSet;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class ListSetManager {

    private static final By ZONE_SEARCH_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_list_set_manager:zoneCbx_label");
    private static final By SEARCH_BUTTON_LOCATOR = By.xpath("//*[contains(@id,'listManagerForm:homepage_business:tabViewListManager:tab_list_set_manager')]//span[contains(text(),'Search')]");
    private static final By CREATE_NEW_SET_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_list_set_manager:_listSetsTbl:_btnNew");
    private static final By CREATE_NEW_LIST_BUTTON_VALIDATION_LOCATOR = By.xpath("//div[text()='List Set Details']");
    private static final By DELETE_LIST_SET_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_list_set_manager:_listSetsTbl:_btnDelete");
    private static final By SORT_BY_ID_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_list_set_manager:_listSetsTbl:_colId");
    private static final By FIRST_RESULT_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_list_set_manager:_listSetsTbl:0:lnkCode");
    private static final String CHECKBOX_BUTTON_LOCATOR = "//td[.='%s']//ancestor::tr[1]//td//div[contains(@class,'ui-selectbooleancheckbox ui-chkbox')]";
    private static final By TABLE_ROWS_LOCATOR = By.xpath("//*[@id = 'listManagerForm:homepage_business:tabViewListManager:tab_list_set_manager:_listSetsTbl_data']//tr");

    private final Controls controls;
    private final Wait wait;

    public ListSetManager() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    @Step("Click search")
    public void click_search(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SEARCH_BUTTON_LOCATOR);
        wait.waitUntilElementToBeVisible(driver, CREATE_NEW_SET_BUTTON_LOCATOR, Wait.TEN_SECOND_DURATION);
    }

    @Step("Click create new set button")
    public void click_create_new_list_set_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, CREATE_NEW_SET_BUTTON_LOCATOR);
        wait.waitUntilElementToBeVisible(driver, CREATE_NEW_LIST_BUTTON_VALIDATION_LOCATOR, Wait.ONE_MINUTE_DURATION);
    }

    @Step("Click checkbox button")
    public void click_check_box(RemoteWebDriver driver, String listSetName) {
        controls.performClick(driver, By.xpath(String.format(CHECKBOX_BUTTON_LOCATOR, listSetName)));
    }

    @Step("Click delete set button")
    public void click_delete_list_set_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, DELETE_LIST_SET_LOCATOR);
        driver.switchTo().alert().accept();
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Select zone")
    public void select_zone(RemoteWebDriver driver, String zone) throws Exception {
        controls.selectOptionByDisplayedText(driver, ZONE_SEARCH_LOCATOR, zone);
    }

    @Step("Sort by ID Desc")
    public void sort_by_id_desc(RemoteWebDriver driver) throws InterruptedException {
        if (!controls.getWebElement(driver, SORT_BY_ID_LOCATOR).getAttribute("aria-sort").equalsIgnoreCase("descending")) {
            controls.performClick(driver, SORT_BY_ID_LOCATOR);
            wait.time(Wait.ONE_SECOND * 5);
            controls.performClick(driver, SORT_BY_ID_LOCATOR);
            wait.time(Wait.ONE_SECOND * 5);

        }
    }

    @Step("Select first result")
    public void select_first_result(RemoteWebDriver driver) {
        controls.performClick(driver, FIRST_RESULT_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Select list set by name")
    public void select_list_set_by_name(RemoteWebDriver driver, String listSetName) {
        controls.performClickByJS(driver, By.xpath(String.format("//a[.='%s']", listSetName)));
    }

    @Step("Check if table rows appear")
    public boolean verify_table_rows_appear(RemoteWebDriver driver) {
        return controls.exists(driver, TABLE_ROWS_LOCATOR);
    }

}
