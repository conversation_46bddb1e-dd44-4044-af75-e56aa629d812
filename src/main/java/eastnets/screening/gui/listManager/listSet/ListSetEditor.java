package eastnets.screening.gui.listManager.listSet;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class ListSetEditor {

    private static final By NAME_INPUT_LOCATOR = By.id("listSetDetailForm:listSet_details:tabViewListSet:listset_tab_details:name");
    private static final By OWNER_INPUT_LOCATOR = By.id("listSetDetailForm:listSet_details:tabViewListSet:listset_tab_details:investCbx_label");
    private static final By SWIFT_TEMPLATE_INPUT_LOCATOR = By.id("listSetDetailForm:listSet_details:tabViewListSet:listset_tab_details:swiftCbx_label");
    private static final By ISO_TEMPLATE_INPUT_LOCATOR = By.id("listSetDetailForm:listSet_details:tabViewListSet:listset_tab_details:isoCbx_label");
    private static final String DETECT_COUNTRIES_CHECKBOX_LOCATOR = "listSetDetailForm:listSet_details:tabViewListSet:listset_tab_details:listSetCountries";
    private static final String DETECT_VESSELS_CHECKBOX_INPUT = "listSetDetailForm:listSet_details:tabViewListSet:listset_tab_details:listSetVessels";
    private static final By ISO_GROUP_SELECT_INPUT_LOCATOR = By.id("listSetDetailForm:listSet_details:tabViewListSet:listset_tab_details:isoGroupCbx_label");
    private static final By ASSIGN_CLEAN_RECORDS_SELECT_LOCATOR = By.id("listSetDetailForm:listSet_details:tabViewListSet:listset_tab_details:assignCleanRecordToCbx");
    private static final By SAVE_BUTTON_LOCATOR = By.id("listSetDetailForm:listSet_details:btnSave");
    private static final By ADD_BLACK_LIST_BUTTON_LOCATOR = By.id("listSetDetailForm:listSet_details:tabViewListSet:listset_tab_details:BlButton");
    private static final By BLACK_LIST_NAME_LOCATOR = By.id("listSetDetailForm:listSet_details:tabViewListSet:listset_tab_details:ListSet_AddList:blackListName_label");
    private static final By ADD_BUTTON_LOCATOR = By.id("listSetDetailForm:listSet_details:tabViewListSet:listset_tab_details:ListSet_AddList:saveButton");
    private static final By SELECT_ALL_BUTTON_LOCATOR = By.xpath("//*[@title='Add All']");
    private static final By REMOVE_ALL_BUTTON_LOCATOR = By.xpath("//*[@title='Remove All']");
    private static final By VALIDATION_MESSAGE_LOCATOR = By.xpath("//*[@class='ui-growl-message']");
    private static final By PROFILE_NAME_LOCATOR = By.xpath("//*[@aria-label='Available Profiles']//tr");
    private static final By PROFILE_FILTER_INPUT_LOCATOR = By.id("listSetDetailForm:listSet_details:tabViewListSet:listset_tab_profiles:profilePickList_source_filter");
    private static final By POST_FILTER_INPUT_LOCATOR = By.id("listSetDetailForm:listSet_details:tabViewListSet:listset_tab_details:blvFilter");
    private static final By PRE_FILTER_INPUT_LOCATOR = By.id("listSetDetailForm:listSet_details:tabViewListSet:listset_tab_details:prefilter");
    private static final By VALIDATE_SYNTAX_PRE_FILTER_BUTTON_LOCATOR = By.id("listSetDetailForm:listSet_details:tabViewListSet:listset_tab_details:btnValidatePrefilter");
    private static final By VALIDATE_SYNTAX_POST_FILTER_BUTTON_LOCATOR = By.id("listSetDetailForm:listSet_details:tabViewListSet:listset_tab_details:btnValidateFilter");
    private static final String REPORT_EXTERNAL_VIOLATIONS_BUTTON_LOCATORS = "listSetDetailForm:listSet_details:tabViewListSet:listset_tab_details:reportExternal";
    private static final By CLOSE_BUTTON_LOCATOR = By.id("listSetDetailForm:listSet_details:btnClose");

    private final Controls controls;
    private final Wait wait;

    public ListSetEditor() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    @Step("Set list name")
    public void set_name(RemoteWebDriver driver, String name) {
        controls.setTextBoxValue(driver, NAME_INPUT_LOCATOR, name);
    }

    @Step("Select owner")
    public void select_owner(RemoteWebDriver driver, String owner) throws Exception {
        controls.selectOptionByPartialDisplayedText(driver, OWNER_INPUT_LOCATOR, owner);
    }

    @Step("Select swift template")
    public void select_swift(RemoteWebDriver driver, String swift) throws Exception {
        controls.selectOptionByDisplayedText(driver, SWIFT_TEMPLATE_INPUT_LOCATOR, swift);
    }

    @Step("Select ISO group")
    public void select_iso(RemoteWebDriver driver, String iso) throws Exception {
        controls.selectOptionByDisplayedText(driver, ISO_GROUP_SELECT_INPUT_LOCATOR, iso);
    }

    @Step("Select detect countries")
    public void select_detect_countries(RemoteWebDriver driver, boolean flag) {
        controls.setCheckboxValueById(driver, DETECT_COUNTRIES_CHECKBOX_LOCATOR, flag);
    }

    @Step("Select detect Vessels")
    public void select_detect_vessels(RemoteWebDriver driver, boolean flag) {
        controls.setCheckboxValueById(driver, DETECT_VESSELS_CHECKBOX_INPUT, flag);
    }

    @Step("Click add new black list")
    public void click_add_new_black_list(RemoteWebDriver driver) {
        controls.performClickByJS(driver, ADD_BLACK_LIST_BUTTON_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Click save button")
    public void click_save_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SAVE_BUTTON_LOCATOR);
        driver.switchTo().alert().accept();
        driver.switchTo().defaultContent();
    }

    @Step("Select black list")
    public void select_black_list(RemoteWebDriver driver, String name) throws Exception {
        controls.selectOptionByDisplayedText(driver, BLACK_LIST_NAME_LOCATOR, name);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Click add button")
    public void click_add_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, ADD_BUTTON_LOCATOR);

    }

    @Step("Get profile name")
    public String getProfileName(RemoteWebDriver driver) {
        return controls.getElementText(driver, PROFILE_NAME_LOCATOR);
    }


    @Step("Set pre-filter input")
    public void set_profile_filter(RemoteWebDriver driver, String profileName) {
        controls.setTextBoxValue(driver, PROFILE_FILTER_INPUT_LOCATOR, profileName);
    }

    @Step("Click select all button")
    public void click_select_all_btn(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SELECT_ALL_BUTTON_LOCATOR);
    }

    public void wait_until_validation_message_appear(RemoteWebDriver driver) {
        wait.waitUntilElementToBeVisible(driver, VALIDATION_MESSAGE_LOCATOR, Wait.ONE_MINUTE_DURATION);
    }

    @Step("Select black set by name")
    public void select_black_list_by_name(RemoteWebDriver driver, String blackListName) {
        controls.performClickByJS(driver, By.xpath(String.format("//a[.='%s']", blackListName)));
    }

    @Step("Click close button")
    public void click_close_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, CLOSE_BUTTON_LOCATOR);
    }

    @Step("Set violation filter")
    public void set_post_violation_filter(RemoteWebDriver driver, String exclusionRule) {
        controls.setTextBoxValue(driver, POST_FILTER_INPUT_LOCATOR, exclusionRule);
    }

    @Step("Click validate syntax button")
    public void click_post_validate_syntax_button(RemoteWebDriver driver) {
        controls.performClick(driver, VALIDATE_SYNTAX_POST_FILTER_BUTTON_LOCATOR);
    }

    @Step("Set violation filter")
    public void set_pre_violation_filter(RemoteWebDriver driver, String exclusionRule) {
        controls.setTextBoxValue(driver, PRE_FILTER_INPUT_LOCATOR, exclusionRule);
    }

    @Step("Click validate syntax button")
    public void click_pre_validate_syntax_button(RemoteWebDriver driver) {
        controls.performClick(driver, VALIDATE_SYNTAX_PRE_FILTER_BUTTON_LOCATOR);
    }


    @Step("Click Report External Violations")
    public void click_report_external_violations(RemoteWebDriver driver, boolean reportExternalViolations) {
        controls.setCheckboxValueById(driver, REPORT_EXTERNAL_VIOLATIONS_BUTTON_LOCATORS, reportExternalViolations);
    }

    @Step("Get black list version")
    public String get_black_list_version(RemoteWebDriver driver, String blackListName) {
        String version = controls.getElementText(driver, By.xpath(String.format("//td[.='%s']//ancestor::tr[1]//td[2]", blackListName)));
        return version;
    }

    @Step("Select assign clean records")

    public void select_assign_clean_records(RemoteWebDriver driver, String assignCleanRecords) throws Exception {
        controls.selectOptionByDisplayedText(driver, ASSIGN_CLEAN_RECORDS_SELECT_LOCATOR, assignCleanRecords);
    }

    public enum ListSetEditorNavigation {

        DETAILS("//a[contains(text(),'Details')]",
                "listSetDetailForm:listSet_details:tabViewListSet:listset_tab_details:detailPanel"),
        PROFILES_LIST_SET_ASSOCIATION("//a[contains(text(),'Profiles-ListSet Association')]",
                "listSetDetailForm:listSet_details:tabViewListSet:listset_tab_profiles:profilePickList"),
        HISTORY("//a[contains(text(),'History')]",
                "listSetDetailForm:listSet_details:tabViewListSet:tab_history:_tblResults");


        private final By navigationElement;
        private final By checkLabel;
        private final Controls controls;
        private final Wait wait;

        ListSetEditorNavigation(String navigationElement, String checkLabel) {
            this.navigationElement = By.xpath(navigationElement);
            this.checkLabel = By.id(checkLabel);
            this.controls = new Controls();
            this.wait = new Wait();
        }

        @Step("Navigate")
        public boolean navigate(RemoteWebDriver driver) {
            wait.waitUntilElementToBeClickable(driver, navigationElement, Wait.TEN_SECOND_DURATION);
            controls.scrollUp(driver);
            controls.performClickByJS(driver, navigationElement);
            return wait.waitUntilElementToBeVisible(driver, checkLabel, Wait.TEN_SECOND_DURATION).isDisplayed();
        }

    }

}