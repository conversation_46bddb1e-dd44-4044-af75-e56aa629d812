package eastnets.screening.gui.listManager.worldCheck;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class WorldCheckManager {

    private static final By ZONE_SEARCH_SELECT_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_world_check:zoneCbx");
    private static final By SEARCH_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_world_check:btnWCSearch");
    private static final By ADD_NEW_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_world_check:_tblResults:_btnNew");
    private static final By DELETE_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_world_check:_tblResults:_btnDelete");
    private static final By CHECK_BOX_LOCATOR = By.cssSelector(".ui-selectbooleancheckbox");
    private static final String NAME_CELL_TABLE_LOCATOR = "//a[.='%s']";
    private static final By ENABLE_STATUS_RESULT_TABLE_LOCATOR = By.xpath("//*[@id='listManagerForm:homepage_business:tabViewListManager:tab_world_check:_tblResults_data']//td[count(//th[@aria-label='Enabled: undefined']/preceding-sibling::th)+1]");
    private static final By CREATION_DATE_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_world_check:_tblResults:creationDate");

    private final Controls controls;
    private final Wait wait;

    public WorldCheckManager() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    @Step("Select zone")
    public void select_zone(RemoteWebDriver driver, String zone) throws Exception {
        controls.selectOptionByDisplayedText(driver, ZONE_SEARCH_SELECT_LOCATOR, zone);
    }

    @Step("Click search")
    public void click_search(RemoteWebDriver driver) throws Exception {
        controls.performClickByJS(driver, SEARCH_BUTTON_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Click add new world check button")
    public void click_add_btn(RemoteWebDriver driver) {
        controls.performClickByJS(driver, ADD_NEW_BUTTON_LOCATOR);
    }

    @Step("Click delete button")
    public void click_delete_btn(RemoteWebDriver driver) {
        controls.performClickByJS(driver, DELETE_BUTTON_LOCATOR);
        driver.switchTo().alert().accept();
        wait.waitUntilAjaxLoaderDisappear(driver);

    }

    @Step("Select world check row")
    public void select_wc_checkbox_by_name(RemoteWebDriver driver, String wc_name) {
        controls.performClick(
                driver, By.xpath(
                        String.format("//*[.='%s']//ancestor::tr[1]//*[@class = 'ui-selectbooleancheckbox ui-chkbox ui-widget']", wc_name)));


    }


    @Step("Select world check by name")
    public void select_wc_by_name(RemoteWebDriver driver, String name) {
        controls.performClick(driver, By.xpath(String.format(NAME_CELL_TABLE_LOCATOR, name)));
    }


    @Step("Check if world check exist")
    public boolean is_wc_exist(RemoteWebDriver driver, String name) {
        return controls.exists(driver, By.xpath(String.format(NAME_CELL_TABLE_LOCATOR, name)));

    }

    @Step("Check if world check is enabled")
    public String is_wc_enabled(RemoteWebDriver driver, String wcheckListName) {
        return controls.getElementText(driver, By.xpath(String.format("//*[.='%s']//ancestor::tr[1]//td[4]", wcheckListName)));

    }

    @Step("Click on creation date to sort descending")
    public void sort_descending_by_creation_date(RemoteWebDriver driver) {
        controls.performClick(driver, CREATION_DATE_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
        if (controls.getWebElement(driver, CREATION_DATE_LOCATOR).getAttribute("aria-sort").contains("ascending")) {
            controls.performClick(driver, CREATION_DATE_LOCATOR);
            wait.waitUntilAjaxLoaderDisappear(driver);

        }
    }
}
