package eastnets.screening.gui.listManager.worldCheck;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.remote.RemoteWebDriver;

public class WorldCheckEditor {

    //WORLD CHECK CREATION FORM LOCATORS
    private static final By WORLD_CHECK_NAME_INPUT_LOCATOR = By.id("WorldCheckCreateForm:world_check_details_business:Name");
    private static final By BLACK_LIST_SELECT_LOCATOR = By.id("WorldCheckCreateForm:world_check_details_business:listContainerCbx");
    private static final String MODE_RADIO_BUTTONS_LOCATOR = "//label[.='%s']//ancestor::td[1]//div[@class='ui-radiobutton ui-widget']";
    private static final String ENABLE_CHECK_BOX_LOCATOR = "WorldCheckCreateForm:world_check_details_business:enabledValue";
    private static final String NEW_VER_ON_UPDATE_CHECK_BOX_LOCATOR = "WorldCheckCreateForm:world_check_details_business:versionedValue";
    private static final By ADD_ALL_COUNTRIES_BUTTON_LOCATOR = By.xpath("//div[@id='WorldCheckCreateForm:world_check_details_business:countriesPickListId']//*[@title='Add All']");
    private static final By ADD_ALL_CATEGORIES_BUTTON_LOCATOR = By.xpath("//div[@id='WorldCheckCreateForm:world_check_details_business:categoriesPickListId']//*[@title='Add All']");
    private static final By SAVE_BUTTON_LOCATOR = By.id("WorldCheckCreateForm:world_check_details_business:btnWCCSave");

    //WORLD CHECK DETAILED/EDITOR FORM LOCATORS
    private static final String ENABLE_EDITOR_CHECK_BOX_LOCATOR = "WorldCheckDetailForm:world_check_details_business:enabledValue";
    private static final String NEW_VER_ON_UPDATE_EDITOR_CHECK_BOX_LOCATOR = "WorldCheckDetailForm:world_check_details_business:versionedValue";
    private static final By SAVE_EDITOR_BUTTON_LOCATOR = By.id("WorldCheckDetailForm:world_check_details_business:btnWCDSave");
    private static final By AVAILABLE_COUNTRIES_LIST_LOCATOR = By.xpath("//*[@id ='WorldCheckCreateForm:world_check_details_business:listsPickListId']//li");
    private static final By AVAILABLE_UMBRELLA_LIST_LOCATOR = By.xpath("//*[@id ='WorldCheckCreateForm:world_check_details_business:umbrellaPickList']//li");

    private final Controls controls;
    private final Wait wait;

    public WorldCheckEditor() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    @Step("Select Mode")
    public void select_mode(RemoteWebDriver driver, String mode) {
        controls.disable_healing(driver);
        controls.performClick(driver, By.xpath(String.format(MODE_RADIO_BUTTONS_LOCATOR, mode)));
        controls.enable_healing(driver);
    }

    @Step("Set name")
    public void set_name(RemoteWebDriver driver, String name) {
        controls.setTextBoxValue(driver, WORLD_CHECK_NAME_INPUT_LOCATOR, name);
    }

    @Step("Select black list")
    public void select_black_list(RemoteWebDriver driver, String black_list) throws Exception {
        controls.selectOptionByDisplayedText(driver, BLACK_LIST_SELECT_LOCATOR, black_list);
    }

    @Step("Click all countries")
    public void click_all_countries(RemoteWebDriver driver) {
        controls.performClick(driver, ADD_ALL_COUNTRIES_BUTTON_LOCATOR);
    }

    @Step("Click all countries")
    public void click_all_categories(RemoteWebDriver driver) throws InterruptedException {
        controls.performClick(driver, ADD_ALL_CATEGORIES_BUTTON_LOCATOR);
        wait.time(Wait.ONE_SECOND * 2);

    }

    @Step("Click save btn")
    public void click_save_btn(RemoteWebDriver driver) {
        controls.performClick(driver, SAVE_BUTTON_LOCATOR);
        driver.switchTo().alert().accept();
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Set enabled checkbox ")
    public void is_enabled(RemoteWebDriver driver, boolean flag) {
        controls.setCheckboxValueById(driver, ENABLE_CHECK_BOX_LOCATOR, flag);
    }

    @Step("Set 'new version on update' checkbx ")
    public void select_new_ver_on_update(RemoteWebDriver driver, boolean flag) {
        controls.setCheckboxValueById(driver, NEW_VER_ON_UPDATE_CHECK_BOX_LOCATOR, flag);
    }

    @Step("Set enable button Editor Form")
    public void clickEnableButton_EditorForm(RemoteWebDriver driver, boolean flag) {
        controls.setCheckboxValueById(driver, ENABLE_EDITOR_CHECK_BOX_LOCATOR, flag);
    }

    @Step("Set 'New Version On Update' button")
    public void set_New_ver_on_update_Edit(RemoteWebDriver driver, boolean flag) {
        controls.setCheckboxValueById(driver, NEW_VER_ON_UPDATE_EDITOR_CHECK_BOX_LOCATOR, flag);
    }

    @Step("Click save button Editor Form")
    public void clickSaveButton_EditorForm(RemoteWebDriver driver) {
        controls.performClick(driver, SAVE_EDITOR_BUTTON_LOCATOR);
        driver.switchTo().alert().accept();
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Get available countries list size")
    public String get_avail_country_list_size(RemoteWebDriver driver) throws InterruptedException {
        wait.waitUntilAjaxLoaderDisappear(driver);
        String size = String.valueOf(driver.findElements(AVAILABLE_COUNTRIES_LIST_LOCATOR).size());
        return size;
    }

    @Step("Get available umbrella list size")
    public int get_avail_umbrella_list_size(RemoteWebDriver driver) throws InterruptedException {
        wait.waitUntilAjaxLoaderDisappear(driver);
        return driver.findElements(AVAILABLE_UMBRELLA_LIST_LOCATOR).size();
    }

}
