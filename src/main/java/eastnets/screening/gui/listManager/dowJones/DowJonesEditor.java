package eastnets.screening.gui.listManager.dowJones;

import core.gui.Controls;
import core.util.Wait;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.util.List;

public class DowJonesEditor {

    private static final By NAME_LOCATOR = By.id("DowJonesEditorForm:DowJ<PERSON>_editor_business:nameValue");
    private static final By CONTAINER_NAME_LOCATOR = By.id("DowJonesEditorForm:DowJones_editor_business:listContainerCbx_label");
    private static final String MODE_LOCATOR = "//tr[.='%s']//div[contains(@class,'ui-radiobutton-box')]";
    private static final By EDIT_BUTTON_LOCATOR = By.id("DowJonesEditorForm:DowJones_editor_business:DowJones_editor_pep_advs:btnEditCountries");
    private static final By ADD_ALL_BTN_LOCATOR = By.xpath("//*[@title='Add All']");
    private static final By SAVE_BUTTON_LOCATOR = By.id("DowJonesEditorForm:DowJones_editor_business:btnSave");
    private static final By ADD_ALL_CATEGORY_BUTTON_LOCATOR = By.xpath("//a[ .='All']");
    private static final By OK_BUTTON_LOCATOR = By.xpath("//button[ .='OK']");
    private static final By DONE_BUTTON_LOCATOR = By.xpath("//button[.='Done']");
    private static final String ADVANCED_SETTINGS_CHECKBOX_LOCATOR = "//*[.='%s']//ancestor::div[1]/div[1]/div[2]";
    private static final By NEW_VER_CHECKBOX_LOCATOR = By.xpath("//*[.='New Version on Update']//div");

    private final Controls controls;
    private final Wait wait;

    public DowJonesEditor() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    @Step("Set DJ List Name")
    public void set_name(RemoteWebDriver driver, EnList list) {
        controls.setTextBoxValue(driver, NAME_LOCATOR, list.getName());
    }

    @Step("Select DJ List Container Name")
    public void select_container_name(RemoteWebDriver driver, EnList list) throws Exception {
        controls.selectOptionByDisplayedText(driver, CONTAINER_NAME_LOCATOR, list.getName());
    }

    @Step("Select DJ List Mode")
    public void select_mode(RemoteWebDriver driver, String mode) {
        controls.performClick(driver, By.xpath(String.format(MODE_LOCATOR, mode)));
    }

    @Step("Choose 'New Version on Update' Option")
    public void select_new_version(RemoteWebDriver driver, boolean flag) {
        if (flag)
            controls.performClick(driver, NEW_VER_CHECKBOX_LOCATOR);
    }

    @Step("Choose Advanced Setting")
    public void select_advanced_setting(RemoteWebDriver driver, String advancedSetting) {
        controls.performClick(driver, By.xpath(String.format(ADVANCED_SETTINGS_CHECKBOX_LOCATOR, advancedSetting)));
    }

    @Step("Click Dj Save Button")
    public void click_save_button(RemoteWebDriver driver) {
        controls.scrollDown(driver);
        controls.performClickByJS(driver, SAVE_BUTTON_LOCATOR);
        driver.switchTo().alert().accept();
    }

    @Step("Click 'edit' button.")
    public void click_edit_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, EDIT_BUTTON_LOCATOR);
    }

    @Step("Click 'add-all' button.")
    public void click_add_all_button(RemoteWebDriver driver) throws InterruptedException {
        controls.performClickByJS(driver, ADD_ALL_BTN_LOCATOR);
        wait.waitUntilLoadersDisappear(driver);
        wait.time(Wait.ONE_SECOND * 2);
    }

    @Step("Click 'done' button.")
    public void click_done_button(RemoteWebDriver driver) throws InterruptedException {
        controls.performClickByJS(driver, DONE_BUTTON_LOCATOR);
        wait.waitUntilLoadersDisappear(driver);
        wait.time(Wait.ONE_SECOND * 2);
    }

    @Step("Click 'ok' button.")
    public void click_ok_button(RemoteWebDriver driver) {
        controls.performClick(driver, OK_BUTTON_LOCATOR);
        wait.waitUntilLoadersDisappear(driver);
    }

    @Step("Add Sanction List")
    public void add_sanction_list(RemoteWebDriver driver) {
        wait.waitUntilElementToBeVisible(driver, ADD_ALL_CATEGORY_BUTTON_LOCATOR, Wait.TEN_SECOND_DURATION);
        List<WebElement> editButtonList = driver.findElements(ADD_ALL_CATEGORY_BUTTON_LOCATOR);
        int size = editButtonList.size();
        for (int i = 0; i < size; i++) {
            editButtonList = driver.findElements(ADD_ALL_CATEGORY_BUTTON_LOCATOR);
            controls.performClick(editButtonList.get(i));
            click_ok_button(driver);
        }
        controls.performClickByJS(driver, SAVE_BUTTON_LOCATOR);
    }
}
