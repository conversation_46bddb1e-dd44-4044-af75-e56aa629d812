package eastnets.screening.gui.listManager.dowJones;

import core.gui.Controls;
import core.util.Wait;
import eastnets.screening.entity.EnList;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.time.Duration;

public class DowJonesManager {

    private static final By SEARCH_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_Factiva:btnSearch");
    private static final By ADD_NEW_DJ_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_Factiva:_tblResults:_btnNew");
    private static final By DELETE_DJ_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_Factiva:_tblResults:_btnDelete");
    private static final By ENABLE_DJ_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_Factiva:_tblResults:_btnEnableEntry");
    private static final By DISABLE_DJ_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_Factiva:_tblResults:_btnDisableEntry");
    private static final By PRINT_DJ_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_Factiva:_tblResults:_btnPrintEntry");
    private static final By CHECKBOX_LOCATOR = By.xpath("//*[@class='ui-selectbooleancheckbox ui-chkbox ui-widget']");
    private static final By SORT_BY_CREATION_DATE_LOCATOR = By.xpath("//*[contains(@aria-label,'Creation Date')]");

    private final Controls controls;
    private final Wait wait;

    public DowJonesManager() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    @Step("Click search button")
    public void click_search_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SEARCH_BUTTON_LOCATOR);
    }

    @Step("Click add new DJ button")
    public void click_add_dj_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, ADD_NEW_DJ_BUTTON_LOCATOR);
    }

    @Step("Click delete DJ button")
    public void click_delete_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, DELETE_DJ_BUTTON_LOCATOR);
        driver.switchTo().alert().accept();
    }

    @Step("Click enable DJ button")
    public void click_enable_utton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, ENABLE_DJ_BUTTON_LOCATOR);
        driver.switchTo().alert().accept();
    }

    @Step("Click disable DJ button")
    public void click_disable_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, DISABLE_DJ_BUTTON_LOCATOR);
        driver.switchTo().alert().accept();

    }

    @Step("Click print DJ button")
    public void click_print_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, PRINT_DJ_BUTTON_LOCATOR);
    }

    @Step("Click checkbox button")
    public void click_checkbox_button(RemoteWebDriver driver) {
        controls.waitAndClick(driver, CHECKBOX_LOCATOR, Duration.ofSeconds(1));
    }

    @Step("Sort Descending by creation date")
    public void sort_descending_by_creation_date(RemoteWebDriver driver) throws InterruptedException {
        wait.time(Wait.ONE_SECOND * 2);
        controls.performClickByJS(driver, SORT_BY_CREATION_DATE_LOCATOR);
        wait.time(Wait.ONE_SECOND * 2);
        controls.performClickByJS(driver, SORT_BY_CREATION_DATE_LOCATOR);
    }

    @Step("Verify DJ exist")
    public boolean verify_dj_exist(RemoteWebDriver driver, String listName) throws InterruptedException {
        sort_descending_by_creation_date(driver);
        wait.time(Wait.ONE_SECOND * 2);
        return controls.exists(driver, By.xpath("//tbody//tr//td[.='" + listName + "']"));
    }

    @Step("Choose DJ name from search results")
    public void select_first_list_from_result(RemoteWebDriver driver, EnList list) {
        controls.waitAndClick(driver, By.xpath("//tbody//tr//td[.='" + list.getName() + "']"), Duration.ofSeconds(2));
    }
}
