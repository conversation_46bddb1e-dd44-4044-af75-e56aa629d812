package eastnets.screening.gui.listManager.activity;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;


public class ActivityManager {

    private static final By SEARCH_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:Tab_activity:btnASearch");
    private static final By STATUS_FIELD_LOCATOR = By.xpath("//tbody[@id='listManagerForm:homepage_business:tabViewListManager:Tab_activity:_tblResults_data']//tr[1]//td[5]");

    private final Controls controls;
    private final Wait wait;

    public ActivityManager() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    @Step("Click search button")
    public void click_search(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SEARCH_BUTTON_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Get import list status")
    public String get_import_list_status(RemoteWebDriver driver) {
        return driver.findElement(STATUS_FIELD_LOCATOR).getText();
    }


}
