package eastnets.screening.gui.listManager.safeTrade;

import core.gui.Controls;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class SafeTradeManager {

    private static final By ZONE_SELECT_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_trade_finance:zoneCbx");
    private static final By SEARCH_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_trade_finance:btnSearch");
    private static final By NEW_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_trade_finance:_tblResults:_btnNew");
    private static final By DELETE_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_trade_finance:_tblResults:_btnDelete");
    private static final By ENABLE_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_trade_finance:_tblResults:_btnEnableEntry");
    private static final By DISABLE_BUTTON_LOCATOR = By.id("listManagerForm:homepage_business:tabViewListManager:tab_trade_finance:_tblResults:_btnDisableEntry");

    private final Controls controls;

    public SafeTradeManager() {
        this.controls = new Controls();
    }

    @Step("Select zone")
    public void selectZone(RemoteWebDriver driver, String zone) throws Exception {
        controls.selectOptionByDisplayedText(driver, ZONE_SELECT_LOCATOR, zone);
    }

    @Step("Click search")
    public void clickSearch(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SEARCH_BUTTON_LOCATOR);
    }

    @Step("Click new")
    public void clickNew(RemoteWebDriver driver) {
        controls.performClickByJS(driver, NEW_BUTTON_LOCATOR);
    }

    @Step("Click delete")
    public void clickDelete(RemoteWebDriver driver) {
        controls.performClickByJS(driver, DELETE_BUTTON_LOCATOR);
    }

    @Step("Click enable")
    public void clickEnable(RemoteWebDriver driver) {
        controls.performClickByJS(driver, ENABLE_BUTTON_LOCATOR);
    }

    @Step("Click disable")
    public void clickDisable(RemoteWebDriver driver) {
        controls.performClickByJS(driver, DISABLE_BUTTON_LOCATOR);
    }

}
