package eastnets.screening.gui.SwiftManager;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Allure;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.time.Duration;

public class SwiftTempEditor {

    private static final By templateNameLocator = By.id("swiftConfiguratoinDetail:detail_business:_templateName");
    private static final By messageTypeOptionButtonLocator = By.id("swiftConfiguratoinDetail:detail_business:_tblResults:imgSelect");
    private static final By messageTypeSelectAllButtonLocator = By.xpath("//*[@id='swiftConfiguratoinDetail:detail_business:_tblResults:overlaySelect']//span[.='Select all']");
    private static final By messageFieldOptionButtonLocator = By.id("swiftConfiguratoinDetail:detail_business:_tblResultsField:imgSelect");
    private static final By messageFieldSelectAllButtonLocator = By.xpath("//*[@id='swiftConfiguratoinDetail:detail_business:_tblResultsField:overlaySelect']//span[.='Select all']");
    private static final By saveButtonLocator = By.id("swiftConfiguratoinDetail:detail_business:btnSave");
    private static final By VALIDATION_LOCATOR = By.id("swiftConfigurationForm:homepage_business:searchGroup1");

    private final Controls controls;
    private final Wait wait;

    public SwiftTempEditor() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    public void setTemplateName(RemoteWebDriver driver, String templateName) {
        Allure.step(String.format("Set template name = %s", templateName));
        controls.setTextBoxValue(driver, templateNameLocator, templateName);
    }

    public void selectAllMessageTypes(RemoteWebDriver driver) {
        Allure.step("Select all message type");
        controls.performClickByJS(driver, messageTypeOptionButtonLocator);
        controls.performClickByJS(driver, messageTypeSelectAllButtonLocator);
        //wait.waitUntilAjaxLoaderDisappear(driver);
    }


    public void selectAllMessageFields(RemoteWebDriver driver) {
        Allure.step("Select all message fields");
        controls.performClickByJS(driver, messageFieldOptionButtonLocator);
        controls.performClickByJS(driver, messageFieldSelectAllButtonLocator);
        //wait.waitUntilAjaxLoaderDisappear(driver);
    }

    public void clickSaveButton(RemoteWebDriver driver) {
        Allure.step("Click save button");
        controls.performClickByJS(driver, saveButtonLocator);
        driver.switchTo().alert().accept();
    }


    public boolean checkIfSwiftTemplateExist(RemoteWebDriver driver) {
       // wait.waitUntilElementToBeVisible(driver, VALIDATION_LOCATOR, Duration.ofSeconds(20));
        Allure.step("Check if swift template added successfully");
        return controls.exists(driver, VALIDATION_LOCATOR);
    }
}
