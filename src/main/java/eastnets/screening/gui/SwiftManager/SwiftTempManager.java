package eastnets.screening.gui.SwiftManager;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Allure;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class SwiftTempManager {

    private static final By ZONE_SEARCH_FIELD_LOCATOR = By.id("swiftConfigurationForm:homepage_business:zoneCbx_label");
    private static final By searchButtonLocator = By.id("swiftConfigurationForm:homepage_business:btnSearch");
    private static final By addButtonLocator = By.id("swiftConfigurationForm:homepage_business:_tblResults:_btnAddConfig");
    private static final By modifyButtonLocator = By.id("swiftConfigurationForm:homepage_business:_tblResults:_btnModifyConfig");
    private static final By deleteButtonLocator = By.id("swiftConfigurationForm:homepage_business:_tblResults:_btnDeleteConfig");
    private static final By printButtonLocator = By.id("swiftConfigurationForm:homepage_business:_tblResults:_btnPrintEntry");

    private final Controls controls;
    private final Wait wait;

    public SwiftTempManager() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    public void setZone(RemoteWebDriver driver, String zone) throws Exception {
        Allure.step(String.format("Set zone : %s", zone));
        controls.selectOptionByDisplayedText(driver, ZONE_SEARCH_FIELD_LOCATOR, zone);
        clickSearchButton(driver);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    public void clickSearchButton(RemoteWebDriver driver) {
        Allure.step("Click Search Button");
        controls.performClickByJS(driver, searchButtonLocator);
        wait.waitUntilAjaxLoaderDisappear(driver);

    }

    public void clickAddButton(RemoteWebDriver driver) {
        Allure.step("Click Add Button");
        controls.performClickByJS(driver, addButtonLocator);
    }

    public void clickModifyButton(RemoteWebDriver driver) {
        Allure.step("Click Modify Button");
        wait.waitUntilElementToBeClickable(driver, modifyButtonLocator, Wait.TEN_SECOND_DURATION);
        controls.performClickByJS(driver, modifyButtonLocator);
    }

    public void clickDeleteButton(RemoteWebDriver driver) {
        Allure.step("Click Delete Button");
        wait.waitUntilElementToBeClickable(driver, deleteButtonLocator, Wait.TEN_SECOND_DURATION);
        controls.performClickByJS(driver, deleteButtonLocator);
    }

    public void clickPrintButton(RemoteWebDriver driver) {
        Allure.step("Click Print Button");
        wait.waitUntilElementToBeClickable(driver, printButtonLocator, Wait.TEN_SECOND_DURATION);
        controls.performClickByJS(driver, printButtonLocator);
    }
}
