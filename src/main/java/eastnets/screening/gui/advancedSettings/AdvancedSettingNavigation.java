package eastnets.screening.gui.advancedSettings;

import core.constants.screening.GeneralConstants;
import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;


public enum AdvancedSettingNavigation {

    MAIL("//a[contains(text(),'Email')]",
            "//*[@id = 'settingsForm:Homepage_business:tabViewSettings:tab_email_ldap:mailPatternHeader']"),
    SYSTEM_STATUS("//a[contains(text(),'System Status')]",
            "//div[text() = 'Application Sessions']"),
    BUSINESS_LOGIC("//a[contains(text(),'Business Logic')]",
            "//a[contains(text(),'General Settings')]"),
    ENGINE_TUNING("//a[contains(text(),'Engine Tuning')]",
            "//*[@id = 'settingsForm:Homepage_business:tabViewSettings:tab_engine_tuning:Settings']"),
    GOOD_GUYS_CATEGORIES("//a[contains(text(),'Good Guys Categories')]",
            "//*[@id = 'settingsForm:Homepage_business:tabViewSettings:tab_goodGuyCategories:_tblResults:btnSaveGGCategory']"),
    FOUR_EYES_SETTING("//a[contains(text(),'4 Eyes Settings')]",
            "//*[@id = 'settingsForm:Homepage_business:tabViewSettings:Tab_secu4eyes:sub_4eyesSettings_list:_switchAllBtn']"),
    INVESTIGATORS("//a[contains(text(),'Investigators')]",
            "//*[@id = 'settingsForm:Homepage_business:tabViewSettings:investigators_view:btnSearchInvestigator']"),
    DETECTION_REASON_CODE("//a[contains(text(),'Detection Reason Code')]",
            "//*[@id = 'settingsForm:Homepage_business:tabViewSettings:Tab_Reason_Code:sub_reason_code_viewer:_tblResults:btnAdd']"),
    SAFE_TRADE("//a[contains(text(),'SafeTrade')]",
            "//*[text() = 'SafeTrade Settings']"),
    NOTIFICATIONS("//a[contains(text(),'Notifications')]",
            "//*[text() = 'Notification Message']"),
    REPLAY_SETTING("//a[contains(text(),'Replay Settings')]",
            "//*[contains(text() , 'Replay Config Editor')]");

    private final By navigationElement;
    private final By checkLabel;
    private final Wait wait;
    private final Controls controls;

    AdvancedSettingNavigation(String navigationElement, String checkLabel) {
        this.navigationElement = By.xpath(navigationElement);
        this.checkLabel = By.xpath(checkLabel);
        this.wait = new Wait();
        this.controls = new Controls();
    }

    @Step("Navigation")
    public String navigate(RemoteWebDriver driver) {

        wait.waitUntilElementToBeClickable(driver, navigationElement, Wait.ONE_MINUTE_DURATION);
        controls.scrollIntoViewJS(driver, navigationElement);
        controls.performClickByJS(driver, navigationElement);

        wait.waitUntilAjaxLoaderDisappear(driver);
        if (!controls.exists(driver, checkLabel))
            return GeneralConstants.FAILED;

        return GeneralConstants.SUCCESS;
    }
}
