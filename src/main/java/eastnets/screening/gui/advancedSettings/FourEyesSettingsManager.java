package eastnets.screening.gui.advancedSettings;

import core.gui.Controls;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class FourEyesSettingsManager {
    private static final By SWITCH_4_EYES_ALL_DETECTIONS_BUTTON_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:Tab_secu4eyes:sub_4eyesSettings_list:_switchAllBtn");
    private static final By FOUR_EYES_ALL_DETECTIONS_RADIOBUTTON_YES_LOCATOR = By.xpath("//*[@id='settingsForm:Homepage_business:tabViewSettings:Tab_secu4eyes:sub_4eyesSettings_list:forceFeature']//*[contains(@class,'ui-radiobutton-box')]");

    private final Controls controls;

    public FourEyesSettingsManager() {
        this.controls = new Controls();
    }

    @Step("Disable 4 eyes")
    public void disableFourEyes(RemoteWebDriver driver) {
        if (controls.getWebElement(driver, FOUR_EYES_ALL_DETECTIONS_RADIOBUTTON_YES_LOCATOR).getAttribute("class").contains("ui-state-active")) {
            controls.performClickByJS(driver, SWITCH_4_EYES_ALL_DETECTIONS_BUTTON_LOCATOR);
        }
    }


    @Step("Enable 4 eyes")
    public void enableFourEyes(RemoteWebDriver driver) {
        if (!controls.getWebElement(driver, FOUR_EYES_ALL_DETECTIONS_RADIOBUTTON_YES_LOCATOR).getAttribute("class").contains("ui-state-active")) {
            controls.performClickByJS(driver, SWITCH_4_EYES_ALL_DETECTIONS_BUTTON_LOCATOR);
        }
    }
}
