package eastnets.screening.gui.advancedSettings.replaySettings;

import core.gui.Controls;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class ReplaySettingManager {

    private static final By ADD_BUTTON_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:Tab_replay:sub_replay_viewer:_tblResults:btnAdd");

    private final Controls controls;

    public ReplaySettingManager() {
        this.controls = new Controls();
    }

    @Step("Click on the Add button")
    public void click_add_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, ADD_BUTTON_LOCATOR);
    }


}
