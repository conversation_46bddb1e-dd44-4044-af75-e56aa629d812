package eastnets.screening.gui.advancedSettings.replaySettings;

import core.gui.Controls;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.util.ArrayList;
import java.util.List;

public class ReplaySettingEditor {

    private static final By BLACK_LIST_FILTER_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:Tab_replay:sub_replay_config_details:blistPickList_source_filter");
    private static final By AVAILABLE_BLACK_LIST_LOCATOR = By.xpath("//*[@aria-label='Available blacklists']//li[not(@style) or @style='']");
    private static final By CANCEL_BUTTON_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:Tab_replay:sub_replay_config_details:cancelButton");

    private final Controls controls;

    public ReplaySettingEditor() {
        this.controls = new Controls();
    }

    @Step("Set black list")
    public void set_black_list_filter(RemoteWebDriver driver, String blackListName) {
        controls.setTextBoxValue(driver, BLACK_LIST_FILTER_LOCATOR, blackListName);
    }

    @Step("Get available black list")
    public List<String> get_available_black_lists(RemoteWebDriver driver) {
        List<String> blackList = new ArrayList<>();
        List<WebElement> webElements = driver.findElements(AVAILABLE_BLACK_LIST_LOCATOR);
        for (WebElement webElement : webElements) {
            blackList.add(webElement.getText());
        }
        return blackList;
    }

    @Step("Click on the Cancel button")
    public void click_cancel_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, CANCEL_BUTTON_LOCATOR);
    }


}
