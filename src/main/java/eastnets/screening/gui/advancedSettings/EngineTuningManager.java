package eastnets.screening.gui.advancedSettings;

import core.gui.Controls;
import core.util.Wait;
import eastnets.screening.entity.EngineTuning;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.remote.RemoteWebDriver;


import java.util.List;

public class EngineTuningManager {

    private static final By SHOW_SETTINGS_SELECT_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:tab_engine_tuning:SettingsCbx");
    private static final By ZONE_SELECT_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:tab_engine_tuning:zoneCbx");
    private static final By ADD_BUTTON_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:tab_engine_tuning:Homepage_synonym:Synonyms_:_btnSynomAdd");
    private static final By DELETE_BUTTON_LOCATOR = By.xpath("//span[contains(text(),'Delete')]");

    //Synonym Locators
    private static final By SYNONYM_ZONE_SELECT_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:tab_engine_tuning:Homepage_synonym:SynonymWordsDetails:zoneCbx_synonym_words");
    private static final By WORD_INPUT_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:tab_engine_tuning:Homepage_synonym:SynonymWordsDetails:word1");
    private static final By SYNONYM_INPUT_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:tab_engine_tuning:Homepage_synonym:SynonymWordsDetails:word2");
    private static final By Save_BUTTON_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:tab_engine_tuning:Homepage_synonym:SynonymWordsDetails:btnSave");
    private static final By TABLE_ROWS_LOCATOR = By.xpath("//tbody[contains(@id,'settingsForm:Homepage_business:tabViewSettings:tab_engine_tuning')]//tr");
    private static final By Check_Box_LOCATOR = By.xpath("//tbody[contains(@id,'settingsForm:Homepage_business:tabViewSettings:tab_engine_tuning')]//tr//td[1]/div");

    //neutral words Locators
    private static final By ADD_NEUTRAL_WORD_BUTTON_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:tab_engine_tuning:Homepage_business:NeutralWords:_btnNwAdd");
    private static final By NEUTRAL_WORD_ZONE_SELECT_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:tab_engine_tuning:Homepage_business:NeutralWordsDetails:zoneCbx_neutral_words");
    private static final By NEUTRAL_WORD_NAME_INPUT_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:tab_engine_tuning:Homepage_business:NeutralWordsDetails:neutralWordName");
    private static final By NEUTRAL_WORD_CATEGORY_SELECT_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:tab_engine_tuning:Homepage_business:NeutralWordsDetails:neutralWordCategory");
    private static final By NEUTRAL_WORD_SAVE_BUTTON_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:tab_engine_tuning:Homepage_business:NeutralWordsDetails:btnSave");
    private static final By OK_BUTTON_LOCATOR = By.xpath("//button[.='OK']");

    //Engine Settings Locators
    private static final By CACHE_THRESHOLD_INPUT_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:tab_engine_tuning:cachethreshold");
    private static final By CACHE_EXPIRATION_INPUT_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:tab_engine_tuning:cacheexpiration");
    private static final By IGNORE_SELECT_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:tab_engine_tuning:IgnoreCbx");
    private static final String IGNORE_CHECKBOX_LOCATOR = "settingsForm:Homepage_business:tabViewSettings:tab_engine_tuning:IgnoreInCountryList";
    private static final By DETECT_SWIFT_BIC_SELECT_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:tab_engine_tuning:SwiftBicCbx2");
    private static final String ENABLE_MATCH_ON_NUMBERS_CHECKBOX_LOCATOR = "settingsForm:Homepage_business:tabViewSettings:tab_engine_tuning:autoFlag";
    private static final String IGNORE_EXTRA_WORDS_CHECKBOX_LOCATOR = "settingsForm:Homepage_business:tabViewSettings:tab_engine_tuning:chxIgnoreExtraWords";
    private static final String ENHANCE_RANK_LAST_NAME_CHECKBOX_LOCATOR = "settingsForm:Homepage_business:tabViewSettings:tab_engine_tuning:chxCompoundLastName";
    private static final String LOWER_RANK_CHECKBOX_LOCATOR = "settingsForm:Homepage_business:tabViewSettings:tab_engine_tuning:chxMissingInformation";
    private static final String ENHANCE_RANK_First_NAME_CHECKBOX_LOCATOR = "settingsForm:Homepage_business:tabViewSettings:tab_engine_tuning:chxFirstThreeNames";
    private static final String DISABLE_SCAN_AKA_CHECKBOX_LOCATOR = "settingsForm:Homepage_business:tabViewSettings:tab_engine_tuning:chxScanOneWordAKAs";
    private static final String ENABLE_SHORT_WORDS_CHECKBOX_LOCATOR = "settingsForm:Homepage_business:tabViewSettings:tab_engine_tuning:EnableShortWords";
    private static final String INDIVIDUAL_NAME_PREFIX_CHECKBOX_LOCATOR = "settingsForm:Homepage_business:tabViewSettings:tab_engine_tuning:enableInvividualNamePrefixNeutral";
    private static final String ENABLE_DOUBLE_LETTERS_ENHANCEMENT_LOCATOR = "settingsForm:Homepage_business:tabViewSettings:tab_engine_tuning:enableDoubleLettersEnhancement";
    private static final By SAVE_ENGINE_SETTINGS_BUTTON_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:tab_engine_tuning:btnSave");

    //Islamic Names Locators
    private static final String ISLAMIC_NAMES_CHECKBOX_LOCATOR = "settingsForm:Homepage_business:tabViewSettings:tab_engine_tuning:Homepage_islamicNames:enableIslamicNames";

    private final Controls controls;
    private final Wait wait;

    public EngineTuningManager() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    /* Methods For Filtering Section */
    @Step("Select show settings")
    public void selectShowSettings(RemoteWebDriver driver ,String showSettings, String zone) throws Exception {
        Allure.step("Select show settings = "+showSettings);
        controls.selectOptionByDisplayedText(driver ,SHOW_SETTINGS_SELECT_LOCATOR, showSettings);
        Allure.step("Select zone = "+zone);
        controls.selectOptionByDisplayedText(driver ,ZONE_SELECT_LOCATOR, zone);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    /* Methods For Synonym Section */
    @Step("Click add new synonym button")
    public void clickAddNewSynonymButton(RemoteWebDriver driver)  {
        Allure.step("Click add button");
        controls.performClickByJS(driver ,ADD_BUTTON_LOCATOR);
    }
    @Step("Click delete synonym button")
    public void clickDeleteSynonymButton(RemoteWebDriver driver)  {
        Allure.step("Click delete button");
        controls.performClickByJS(driver ,DELETE_BUTTON_LOCATOR);
    }
    @Step("Select synonym zone")
    public void selectSynonymZone(RemoteWebDriver driver ,String zone) throws Exception {
        Allure.step("Select synonym zone = "+zone);
        controls.selectOptionByDisplayedText(driver ,SYNONYM_ZONE_SELECT_LOCATOR, zone);
    }
    @Step("Enter word")
    public void enterWord(RemoteWebDriver driver ,String word)  {
        Allure.step("Enter word = "+word);
        controls.setTextBoxValue(driver ,WORD_INPUT_LOCATOR, word);
    }
    @Step("Enter synonym")
    public void enterSynonym(RemoteWebDriver driver ,String synonym)  {
        Allure.step("Enter synonym = "+synonym);
        controls.setTextBoxValue(driver ,SYNONYM_INPUT_LOCATOR, synonym);
    }
    @Step("Click save button")
    public void clickSaveSynonymButton(RemoteWebDriver driver)  {
        controls.performClickByJS(driver ,Save_BUTTON_LOCATOR);
    }


    /* Methods For Neutral Word Section */
    @Step("Click add new neutral word button")
    public void clickAddNewNeutralWordButton(RemoteWebDriver driver)  {
        controls.performClickByJS(driver ,ADD_NEUTRAL_WORD_BUTTON_LOCATOR);
    }
    @Step("Select neutral word zone")
    public void selectNeutralWordZone(RemoteWebDriver driver ,String zone) throws Exception {
        Allure.step("Select neutral word zone = "+zone);
        controls.selectOptionByDisplayedText(driver ,NEUTRAL_WORD_ZONE_SELECT_LOCATOR, zone);
    }
    @Step("Enter neutral word name")
    public void enterNeutralWordName(RemoteWebDriver driver ,String neutralWordName)  {
        Allure.step("Enter neutral word name = "+neutralWordName);
        controls.setTextBoxValue(driver ,NEUTRAL_WORD_NAME_INPUT_LOCATOR, neutralWordName);
    }
    @Step("Select neutral word category")
    public void selectNeutralWordCategory(RemoteWebDriver driver ,String neutralWordCategory) throws Exception {
        Allure.step("Select neutral word category = "+neutralWordCategory);
        controls.selectOptionByDisplayedText(driver ,NEUTRAL_WORD_CATEGORY_SELECT_LOCATOR, neutralWordCategory);
    }
    @Step("Click save neutral word button")
    public void clickSaveNeutralWordButton(RemoteWebDriver driver)  {
        Allure.step("Click save neutral word button");
        controls.performClickByJS(driver ,NEUTRAL_WORD_SAVE_BUTTON_LOCATOR);
    }

    /* Methods For Engine Settings Section */
    @Step("Set cache threshold")
    public void setCacheThreshold(RemoteWebDriver driver ,String cacheThreshold)  {
        Allure.step("Set cache threshold = "+cacheThreshold);
        controls.setTextBoxValue(driver ,CACHE_THRESHOLD_INPUT_LOCATOR, cacheThreshold);
    }
    @Step("Set cache expiration")
    public void setCacheExpiration(RemoteWebDriver driver ,String cacheExpiration)  {
        Allure.step("Set cache expiration = "+cacheExpiration);
        controls.setTextBoxValue(driver ,CACHE_EXPIRATION_INPUT_LOCATOR, cacheExpiration);
    }
    @Step("Select ignore match shorter")
    public void selectIgnoreMatchShorter(RemoteWebDriver driver , String ignoreMatches) throws Exception {
        Allure.step("Select ignore = "+ignoreMatches);
        controls.selectOptionByDisplayedText(driver ,IGNORE_SELECT_LOCATOR, ignoreMatches);
    }
    @Step("Click ignore")
    public void clickIgnore(RemoteWebDriver driver , boolean ignoreFlag)   {
        Allure.step("set ignore = "+ignoreFlag);
        controls.setCheckboxValueById(driver ,IGNORE_CHECKBOX_LOCATOR ,ignoreFlag );
    }
    @Step("Select detect swift bic")
    public void selectDetectSwiftBic(RemoteWebDriver driver ,String detectSwiftBic) throws Exception {
        Allure.step("Select detect swift bic = "+detectSwiftBic);
        controls.selectOptionByDisplayedText(driver ,DETECT_SWIFT_BIC_SELECT_LOCATOR, detectSwiftBic);
    }
    @Step("Click enable match on number")
    public void clickEnableMatchOnNumber(RemoteWebDriver driver , boolean enableMatchOnNumber)  {
        Allure.step("set enable match on number = "+enableMatchOnNumber);
        controls.setCheckboxValueById(driver ,ENABLE_MATCH_ON_NUMBERS_CHECKBOX_LOCATOR ,enableMatchOnNumber );
    }
    @Step("Click ignore extra words")
    public void clickIgnoreExtraWords(RemoteWebDriver driver , boolean ignoreExtraWords)  {
        Allure.step("set ignore Extra Words in Scanned Names = "+ignoreExtraWords);
        controls.setCheckboxValueById(driver ,IGNORE_EXTRA_WORDS_CHECKBOX_LOCATOR ,ignoreExtraWords );
    }
    @Step("Click enhance match rank last name")
    public void clickEnhanceMatchRankLastName(RemoteWebDriver driver , boolean enhanceMatchRankLastName)  {
        Allure.step("set enhanced match rank in case of compound last name and one of its words is matched = "+enhanceMatchRankLastName);
        controls.setCheckboxValueById(driver ,ENHANCE_RANK_LAST_NAME_CHECKBOX_LOCATOR ,enhanceMatchRankLastName );
    }
    @Step("Click lower match rank")
    public void clickLowerMatchRank(RemoteWebDriver driver , boolean lowerMatchRank)  {
        Allure.step("set lower match rank penalty for differences in middle names = "+lowerMatchRank);
        controls.setCheckboxValueById(driver ,LOWER_RANK_CHECKBOX_LOCATOR ,lowerMatchRank );
    }
    @Step("Click enhance match rank first name")
    public void clickEnhanceMatchRankFirstName(RemoteWebDriver driver , boolean enhanceMatchRankFirstName)  {
        Allure.step("set enhanced match rank in case of compound first names, and last name along with one of the first name’s words are matched = "+enhanceMatchRankFirstName);
        controls.setCheckboxValueById(driver ,ENHANCE_RANK_First_NAME_CHECKBOX_LOCATOR ,enhanceMatchRankFirstName );
    }
    @Step("Click disable scan one-word Individual AKA")
    public void clickDisableScanOneWordIndividualAKA(RemoteWebDriver driver , boolean disableScanOneWordIndividualAKA)  {
        Allure.step("set disable scan one-word Individual AKA = "+disableScanOneWordIndividualAKA);
        controls.setCheckboxValueById(driver ,DISABLE_SCAN_AKA_CHECKBOX_LOCATOR ,disableScanOneWordIndividualAKA );
    }
    @Step("Click enable short words")
    public void clickEnableShortWords(RemoteWebDriver driver , boolean enableShortWords)  {
        Allure.step("set enable short words = "+enableShortWords);
        controls.setCheckboxValueById(driver ,ENABLE_SHORT_WORDS_CHECKBOX_LOCATOR ,enableShortWords );
    }
    @Step("Click individual name prefix neutral")
    public void clickIndividualNamePrefixNeutral(RemoteWebDriver driver , boolean individualNamePrefixNeutral)  {
        Allure.step("set individual name prefix neutral = "+individualNamePrefixNeutral);
        controls.setCheckboxValueById(driver ,INDIVIDUAL_NAME_PREFIX_CHECKBOX_LOCATOR ,individualNamePrefixNeutral );
    }
    @Step("Click Enable double letter enhacement")
    public void clickEnableDoubleLetterEnhacement(RemoteWebDriver driver, boolean enableDoubleLetterEnhacement){
        Allure.step("set enable double letter enhacement = " +enableDoubleLetterEnhacement);
        controls.setCheckboxValueById(driver, ENABLE_DOUBLE_LETTERS_ENHANCEMENT_LOCATOR, enableDoubleLetterEnhacement);

    }
    @Step("Click save engine setting button")
    public void clickSaveEngineSettingButton(RemoteWebDriver driver)  {
        Allure.step("Click save engine setting button");
        controls.performClickByJS(driver ,SAVE_ENGINE_SETTINGS_BUTTON_LOCATOR);
    }
    /* Methods For Engine Settings Section */
    @Step("Set Islamic Names")
    public void clickEnableIslamicNames(RemoteWebDriver driver ,boolean enableIslamicNames)  {
        Allure.step("set enable islamic names = "+enableIslamicNames);
        controls.setCheckboxValueById(driver ,ISLAMIC_NAMES_CHECKBOX_LOCATOR, enableIslamicNames);
    }

    /* Common Methods */
    @Step("Verify if exist")
    public boolean verifyIfExist(RemoteWebDriver driver , EngineTuning engineTuning) throws Exception {
        Allure.step("Verify = "+engineTuning.getWord()+" exist");
        selectShowSettings(driver ,engineTuning.getShowSettings() , engineTuning.getZone());
        return controls.exists(driver ,By.xpath(String.format("//*[.='%s']", engineTuning.getWord())));
    }
    @Step("Delete")
    public void delete(RemoteWebDriver driver ,String element)  {
        Allure.step("Delete = "+element);
        List<WebElement> rows = driver.findElements(TABLE_ROWS_LOCATOR);
        List<WebElement> checkbox = driver.findElements(Check_Box_LOCATOR);
        for (WebElement row : rows) {
            if (row.findElement(By.xpath("//td[2]//span")).getText().contains(element)) {
                controls.performClick(checkbox.get(rows.indexOf(row)));
                break;
            }
        }
        clickDeleteSynonymButton(driver);

    }
    @Step("Click ok button")
    public void clickOkButton(RemoteWebDriver driver)  {
        controls.performClickByJS(driver ,OK_BUTTON_LOCATOR);
    }
}