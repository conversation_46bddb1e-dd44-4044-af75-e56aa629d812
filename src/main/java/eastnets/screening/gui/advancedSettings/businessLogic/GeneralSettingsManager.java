package eastnets.screening.gui.advancedSettings.businessLogic;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class GeneralSettingsManager {

    private static final By ZONE_SELECT_LOCATOR
            = By.id("settingsForm:Homepage_business:tabViewSettings:tabView:mainLogic:zoneCbx");
    private static final By MAX_DATA_CHARACTERS_INPUT_LOCATOR
            = By.id("settingsForm:Homepage_business:tabViewSettings:tabView:mainLogic:MaxDataCharacters");
    private static final By MAX_COMMENT_CHARACTERS_INPUT_LOCATOR
            = By.id("settingsForm:Homepage_business:tabViewSettings:tabView:mainLogic:Max<PERSON>lertCommentCharacters");
    private static final String ENABLE_ARABIC_PHONETIC_CHECKBOX_LOCATOR
            = "settingsForm:Homepage_business:tabViewSettings:tabView:mainLogic:chxArabicPhoneticsOn";
    private static final String ENABLE_ARABIC_PHONETIC_PLUS_CHECKBOX_LOCATOR
            = "settingsForm:Homepage_business:tabViewSettings:tabView:mainLogic:chxArabicPhoneticsPlusOn";
    private static final String ENABLE_RUSSIAN_PHONETIC_CHECKBOX_LOCATOR
            = "settingsForm:Homepage_business:tabViewSettings:tabView:mainLogic:chxRussianPhoneticsOn";
    private static final String ENABLE_SLAVIC_PHONETIC_CHECKBOX_LOCATOR
            = "settingsForm:Homepage_business:tabViewSettings:tabView:mainLogic:chxSlavicPhoneticsOn";
    private static final String ENABLE_CUSTOM_PHONETIC_CHECKBOX_LOCATOR
            = "settingsForm:Homepage_business:tabViewSettings:tabView:mainLogic:chxCustomPhoneticsOn";
    private static final String ALLOW_DETECTION_DO_NOT_KNOW_CHECKBOX_LOCATOR
            = "settingsForm:Homepage_business:tabViewSettings:tabView:mainLogic:enableDontKnow";
    private static final String ENABLE_AUTOMATIC_SEARCH_FOR_MODIFIED_DETECTION_CHECKBOX_LOCATOR
            = "settingsForm:Homepage_business:tabViewSettings:tabView:mainLogic:chxEnableAutoSearch";
    private static final String ENABLE_DETECTION_BUTTONS_UNDER_THE_DETECTION_DETAILS_CHECKBOX_LOCATOR
            = "settingsForm:Homepage_business:tabViewSettings:tabView:mainLogic:chxEnableDetectionActionButtons";
    private static final String ENABLE_AUDIT_TRAIL_FOR_ALL_SCAN_REQUESTS_CHECKBOX_LOCATOR
            = "settingsForm:Homepage_business:tabViewSettings:tabView:mainLogic:chxAuditTrail";
    private static final String ENABLE_GROUP_FOR_BLOCK_RELEASE_DONT_KNOW_BUTTONS_CHECKBOX_LOCATOR
            = "settingsForm:Homepage_business:tabViewSettings:tabView:mainLogic:chxEnableGroupFilterBlockRelease";
    private static final String ENABLE_GROUP_FOR_ASSIGN_PENDING_BUTTONS_CHECKBOX_LOCATOR
            = "settingsForm:Homepage_business:tabViewSettings:tabView:mainLogic:chxGroupFilterAssignPending";
    private static final String ENABLE_ALTERNATIVE_WAY_TO_HANDLE_THE_VISIBILITY_OF_DETECTIONS_CHECKBOX_LOCATOR
            = "settingsForm:Homepage_business:tabViewSettings:tabView:mainLogic:chxAlternateDetectionVisibility";
    private static final String ENABLE_IMPROVED_INTERPRETATION_OF_SWIFT_FIELDS_CHECKBOX_LOCATOR
            = "settingsForm:Homepage_business:tabViewSettings:tabView:mainLogic:chxImporvedSwift";
    private static final String AUTOMATICALLY_RESET_ASSIGNEES_CHECKBOX_LOCATOR
            = "settingsForm:Homepage_business:tabViewSettings:tabView:mainLogic:chxAutoResetValue";
    private static final String ENABLE_READ_COLUMN_ON_ALERTS_CHECKBOX_LOCATOR
            = "settingsForm:Homepage_business:tabViewSettings:tabView:mainLogic:chxEnableReadColumn";
    private static final String ENABLE_REASON_CODE_CHECKBOX_LOCATOR
            = "settingsForm:Homepage_business:tabViewSettings:tabView:mainLogic:chxReasonCode";
    private static final String SHOW_OPTION_USE_ADVANCED_CHECKSUM_CALCULATION_CHECKBOX_LOCATOR
            = "settingsForm:Homepage_business:tabViewSettings:tabView:mainLogic:chxAdvancedChecksumCalculation";
    private static final String SEND_AN_EMAIL_TO_ADMINS_AND_SHOW_A_WARNING_CHECKBOX_LOCATOR
            = "settingsForm:Homepage_business:tabViewSettings:tabView:mainLogic:chxEnableExpirDateMail";
    private static final By SEND_AN_EMAIL_TO_ADMINS_AND_SHOW_A_WARNING_TEXT_LOCATOR
            = By.id("settingsForm:Homepage_business:tabViewSettings:tabView:mainLogic:txtDaysBeforeMail");
    private static final String ENABLE_GLUED_WORDS_SYMSPELL_CHECKBOX_LOCATOR
            = "settingsForm:Homepage_business:tabViewSettings:tabView:mainLogic:chxGluedwordsSymSpell";
    private static final By ALERTS_TO_BE_CREATED_WITH_COMMENTS_MODE_SELECT_LOCATOR
            = By.id("settingsForm:Homepage_business:tabViewSettings:tabView:mainLogic:preventAlertCbx");
    private static final By NUMBER_OF_LAST_NOTES_SELECT_LOCATOR
            = By.id("settingsForm:Homepage_business:tabViewSettings:tabView:mainLogic:lastNotesCbx");
    private static final By SAVE_BUTTON_LOCATOR
            = By.id("settingsForm:Homepage_business:tabViewSettings:tabView:mainLogic:btnSave");
    private static final By CANCEL_BUTTON_LOCATOR
            = By.id("settingsForm:Homepage_business:tabViewSettings:tabView:mainLogic:btnCancelIn");
    private static final String ENABLE_AUTOMATIC_SELECT_FIRST_DETECTION_LOCATOR
    ="//*[.='Enable automatic select:']//ancestor::div[1]/div";

    private static final By SYMSPELL_WORD_SIZE
            =By.id ("settingsForm:Homepage_business:tabViewSettings:tabView:mainLogic:SymSpellWordSize");
    private static final String ENNABLE_GLUED_WORDS_SYMSPELL_CHECKBOX_LOCATOR
            = ("settingsForm:Homepage_business:tabViewSettings:tabView:mainLogic:chxGluedwordsSymSpell");

    private final Controls controls;
    private final Wait wait;

    public GeneralSettingsManager() {
        this.controls = new Controls();
        this.wait = new Wait();
    }
    @Step("Set Zone")
    public void setZone(RemoteWebDriver driver , String zone) throws Exception {
        Allure.step("Set Zone to: " + zone);
        controls.selectOptionByDisplayedText(driver ,ZONE_SELECT_LOCATOR, zone);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }
    @Step("Set Max Data Characters")
    public void setMaxDataCharacters(RemoteWebDriver driver , String maxDataCharacters)  {
        Allure.step("Set Max Data Characters to: " + maxDataCharacters);
        controls.setTextBoxValue(driver ,MAX_DATA_CHARACTERS_INPUT_LOCATOR, maxDataCharacters);
    }
    @Step("Set Max Comment Characters")
    public void setMaxCommentCharacters(RemoteWebDriver driver , String maxCommentCharacters)  {
        Allure.step("Set Max Comment Characters to: " + maxCommentCharacters);
        controls.setTextBoxValue(driver ,MAX_COMMENT_CHARACTERS_INPUT_LOCATOR, maxCommentCharacters);
    }
    @Step("Click Enable Arabic Phonetic")
    public void clickEnableArabicPhonetic(RemoteWebDriver driver , boolean flag )  {
        Allure.step("Enable Arabic Phonetic = " + flag);
        controls.setCheckboxValueById(driver ,ENABLE_ARABIC_PHONETIC_CHECKBOX_LOCATOR , flag);
    }
    @Step("Click Enable Arabic Phonetic Plus")
    public void clickEnableArabicPhoneticPlus(RemoteWebDriver driver , boolean flag)  {
        Allure.step("Enable Arabic Phonetic Plus = " + flag);
        controls.setCheckboxValueById(driver ,ENABLE_ARABIC_PHONETIC_PLUS_CHECKBOX_LOCATOR , flag);
    }
    @Step("Click Enable Russian Phonetic")
    public void clickEnableRussianPhonetic(RemoteWebDriver driver , boolean flag)  {
        Allure.step("Enable Russian Phonetic = " + flag);
        controls.setCheckboxValueById(driver ,ENABLE_RUSSIAN_PHONETIC_CHECKBOX_LOCATOR , flag);
    }
    @Step("Click Enable Slavic Phonetic")
    public void clickEnableSlavicPhonetic(RemoteWebDriver driver  , boolean flag)  {
        Allure.step("Enable Slavic Phonetic = " + flag);
        controls.setCheckboxValueById(driver ,ENABLE_SLAVIC_PHONETIC_CHECKBOX_LOCATOR , flag);
    }

    @Step("Click Enable Custom Phonetic")
    public void clickEnableCustomPhonetic(RemoteWebDriver driver  , boolean flag)  {
        Allure.step("Enable Slavic Phonetic = " + flag);
        controls.setCheckboxValueById(driver ,ENABLE_CUSTOM_PHONETIC_CHECKBOX_LOCATOR , flag);
    }
    @Step("Click Allow Detection Do Not Know")
    public void clickAllowDetectionDoNotKnow(RemoteWebDriver driver , boolean flag)  {
        Allure.step("Allow Detection Do Not Know");
        controls.setCheckboxValueById(driver ,ALLOW_DETECTION_DO_NOT_KNOW_CHECKBOX_LOCATOR ,flag);
    }
    @Step("Click Enable Automatic Search For Modified Detection")
    public void clickEnableAutomaticSearchForModifiedDetection(RemoteWebDriver driver, boolean flag)  {
        Allure.step("Enable Automatic Search For Modified Detection = " + flag);
        controls.setCheckboxValueById(driver ,ENABLE_AUTOMATIC_SEARCH_FOR_MODIFIED_DETECTION_CHECKBOX_LOCATOR , flag);
    }
    @Step("Click Enable Detection Buttons Under The Detection Details")
    public void clickEnableDetectionButtonsUnderTheDetectionDetails(RemoteWebDriver driver, boolean flag)  {
        Allure.step("Enable Detection Buttons Under The Detection Details = " + flag);
        controls.setCheckboxValueById(driver ,ENABLE_DETECTION_BUTTONS_UNDER_THE_DETECTION_DETAILS_CHECKBOX_LOCATOR , flag);
    }
    @Step("Click Enable Audit Trail For All Scan Requests")
    public void clickEnableAuditTrailForAllScanRequests(RemoteWebDriver driver , boolean flag)  {
        Allure.step("Enable Audit Trail For All Scan Requests = " + flag);
        controls.setCheckboxValueById(driver ,ENABLE_AUDIT_TRAIL_FOR_ALL_SCAN_REQUESTS_CHECKBOX_LOCATOR , flag);
    }
    @Step("Click Enable Group For Block Release Dont Know Buttons")
    public void clickEnableGroupForBlockReleaseDontKnowButtons(RemoteWebDriver driver, boolean flag)  {
        Allure.step("Enable Group For Block Release Dont Know Buttons = " + flag);
        controls.setCheckboxValueById(driver ,ENABLE_GROUP_FOR_BLOCK_RELEASE_DONT_KNOW_BUTTONS_CHECKBOX_LOCATOR , flag);
    }
    @Step("Click Enable Group For Assign Pending Buttons")
    public void clickEnableGroupForAssignPendingButtons(RemoteWebDriver driver, boolean flag)  {
        Allure.step("Enable Group For Block Release Pending Buttons = " + flag);
        controls.setCheckboxValueById(driver ,ENABLE_GROUP_FOR_ASSIGN_PENDING_BUTTONS_CHECKBOX_LOCATOR , flag);
    }
    @Step("Click Enable Alternative Way To Handle The Visibility Of Detection Buttons")
    public void clickEnableAlternativeWayToHandleTheVisibilityOfDetectionButtons(RemoteWebDriver driver, boolean flag)  {
        Allure.step("Enable Group For Block Release Pending Buttons = " + flag);
        controls.setCheckboxValueById(driver ,ENABLE_ALTERNATIVE_WAY_TO_HANDLE_THE_VISIBILITY_OF_DETECTIONS_CHECKBOX_LOCATOR , flag);
    }
    @Step("Click Enable Improved Interpretation Of Swift Fields")
    public void clickEnableImprovedInterpretationOfSwiftFields(RemoteWebDriver driver, boolean flag)  {
        Allure.step("Enable Improved Interpretation Of Swift Fields = " + flag);
        controls.setCheckboxValueById(driver ,ENABLE_IMPROVED_INTERPRETATION_OF_SWIFT_FIELDS_CHECKBOX_LOCATOR , flag);
    }
    @Step("Click Automatically Reset Assignees")
    public void clickAutomaticallyResetAssignees(RemoteWebDriver driver, boolean flag)  {
        Allure.step("Automatically Reset Assignees = " + flag);
        controls.setCheckboxValueById(driver ,AUTOMATICALLY_RESET_ASSIGNEES_CHECKBOX_LOCATOR , flag);
    }
    @Step("Click Enable Read Column")
    public void clickEnableReadColumn(RemoteWebDriver driver, boolean flag)  {
        Allure.step("Enable Read Column = " + flag);
        controls.setCheckboxValueById(driver ,ENABLE_READ_COLUMN_ON_ALERTS_CHECKBOX_LOCATOR , flag);
    }
    @Step("Click Enable Reason Code")
    public void clickEnableReasonCode(RemoteWebDriver driver, boolean flag)  {
        Allure.step("Enable Reason Code = " + flag);
        controls.setCheckboxValueById(driver ,ENABLE_REASON_CODE_CHECKBOX_LOCATOR , flag);
    }
    @Step("Click Show Option Use Advanced Checksum Calculation")
    public void clickShowOptionUseAdvancedChecksumCalculation(RemoteWebDriver driver, boolean flag)  {
        Allure.step("Show Option Use Advanced Checksum Calculation = " + flag);
        controls.setCheckboxValueById(driver ,SHOW_OPTION_USE_ADVANCED_CHECKSUM_CALCULATION_CHECKBOX_LOCATOR, flag);
    }
    @Step("Click Send An Email To Admins And Show A Warning")
    public void clickSendAnEmailToAdminsAndShowAWarning(RemoteWebDriver driver, boolean flag)  {
        Allure.step("Send An Email To Admins And Show A Warning = " + flag);
        controls.setCheckboxValueById(driver ,SEND_AN_EMAIL_TO_ADMINS_AND_SHOW_A_WARNING_CHECKBOX_LOCATOR , flag);
    }
    @Step("Set Send An Email To Admins And Show A Warning")
    public void setSendAnEmailToAdminsAndShowAWarning(RemoteWebDriver driver , String text)  {
        Allure.step("Set Text Send An Email To Admins And Show A Warning to: " + text);
        controls.setTextBoxValue(driver ,SEND_AN_EMAIL_TO_ADMINS_AND_SHOW_A_WARNING_TEXT_LOCATOR, text);
    }
    @Step("Click Enable Glued Words SymSpell")
    public void clickEnableGluedWordsSymSpell(RemoteWebDriver driver , boolean flag)  {
        Allure.step("Enable Glued Words SymSpell   = " + flag);
        controls.setCheckboxValueById(driver ,ENABLE_GLUED_WORDS_SYMSPELL_CHECKBOX_LOCATOR , flag);

    }
    @Step("Set Symspell word size")
    public void setSymspellWordSize(RemoteWebDriver driver, String size){
        Allure.step(" Symspell value    = " + size);
        controls.setTextBoxValue(driver ,SYMSPELL_WORD_SIZE, size);
    }

    @Step("Set enable automatic first detection option")
    public void setEnableAutomaticFirstDetectionCheckbox(RemoteWebDriver driver , boolean flag)  {
        controls.setCheckboxValueByXpath(driver ,ENABLE_AUTOMATIC_SELECT_FIRST_DETECTION_LOCATOR , flag);
    }
    @Step("Set Alerts To Be Created With Comments Mode")
    public void setAlertsToBeCreatedWithCommentsMode(RemoteWebDriver driver , String mode) throws Exception {
        Allure.step("Set Alerts To Be Created With Comments Mode to: " + mode);
        controls.selectOptionByDisplayedText(driver ,ALERTS_TO_BE_CREATED_WITH_COMMENTS_MODE_SELECT_LOCATOR, mode);
    }
    @Step("Set Number Of Last Notes")
    public void setNumberOfLastNotes(RemoteWebDriver driver , String number) throws Exception {
        Allure.step("Set Number Of Last Notes to: " + number);
        controls.selectOptionByDisplayedText(driver ,NUMBER_OF_LAST_NOTES_SELECT_LOCATOR, number);
    }
    @Step("Click Save")
    public void clickSave(RemoteWebDriver driver)  {
        controls.performClick(driver ,SAVE_BUTTON_LOCATOR);
    }
    @Step("Click Cancel")
    public void clickCancel(RemoteWebDriver driver)  {
        controls.performClick(driver ,CANCEL_BUTTON_LOCATOR);
    }
}