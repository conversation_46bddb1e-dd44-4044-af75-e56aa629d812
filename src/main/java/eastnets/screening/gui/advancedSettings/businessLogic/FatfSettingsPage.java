package eastnets.screening.gui.advancedSettings.businessLogic;

import core.gui.Controls;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class FatfSettingsPage {

    private static final By ZONE_LIST_SLCT = By.xpath("//label[@id='settingsForm:Homepage_business:tabViewSettings:tabView:fatf:zoneCbx_label']");
    private static final By ENABLE_FATF_RECOMMENDATION_BTN = By.xpath("//div[@id='settingsForm:Homepage_business:tabViewSettings:tabView:fatf:enableSr7']");
    private static final By BLACK_LIST_TXT = By.xpath("//input[@id='settingsForm:Homepage_business:tabViewSettings:tabView:fatf:listName']");
    private static final By FIELD_MINIMUM_LINE_SIZE_SLCT = By.xpath("//label[@id='settingsForm:Homepage_business:tabViewSettings:tabView:fatf:minLineSizeCbx_label']");
    private static final By SAVE_BTN = By.xpath("//button[@id='settingsForm:Homepage_business:tabViewSettings:tabView:fatf:btnSave']//span[@class='ui-button-text ui-c'][normalize-space()='Save']");
    private static final By RANK_SLCT = By.xpath("//label[@id='settingsForm:Homepage_business:tabViewSettings:tabView:fatf:sr7rank_label']");
    private static final By EUROPEAN_CODES_TXT = By.xpath("//textarea[@id='settingsForm:Homepage_business:tabViewSettings:tabView:fatf:codes']");
    private static final By ENABLE_CHECK_OF_UNIQUE_TRANSACTION_RN_BTN = By.xpath("//div[@id='settingsForm:Homepage_business:tabViewSettings:tabView:fatf:checkEnableCheckReferenceNumber']");
    private static final By INFORMATION_AVAILABLE_WITHIN_TRANSACTION_BTN = By.xpath("//label[@for='settingsForm:Homepage_business:tabViewSettings:tabView:fatf:validationOptions:0']");
    private static final By INFORMATION_AVAILABLE_BY_OTHER_MEANS_BTN = By.xpath("//label[@for='settingsForm:Homepage_business:tabViewSettings:tabView:fatf:validationOptions:1']");
    private static final By INFORMATION_AVAILABLE_BY_BICS_BTN = By.xpath("//label[@for='settingsForm:Homepage_business:tabViewSettings:tabView:fatf:validationOptions:2']");

    private static final By BICS_TXT = By.xpath("//textarea[@id='settingsForm:Homepage_business:tabViewSettings:tabView:fatf:bics']");
    private static final By APPLY_FOR_CROSS_BORDER_TRAFFIC_BTN = By.xpath("//div[@id='settingsForm:Homepage_business:tabViewSettings:tabView:fatf:checkApplyForCrossBorder']//div[@class='ui-chkbox-box ui-widget ui-corner-all ui-state-default']");
    private static final By CURRENCY_AMOUNT_TXT = By.xpath("//input[@id='settingsForm:Homepage_business:tabViewSettings:tabView:fatf:currencyAmountTable:newAmount_input']");
    private static final By CURRENCY_SLCT = By.xpath("//label[@id='settingsForm:Homepage_business:tabViewSettings:tabView:fatf:currencyAmountTable:newCurrency_label']");
    private static final By CURRENCY_ADD_BTN = By.xpath("//span[@class='ui-button-text ui-c'][normalize-space()='Add']");

    private final Controls controls;

    public FatfSettingsPage() {
        this.controls = new Controls();
    }

    @Step("Select zone")
    public void selectZone(RemoteWebDriver driver, String zoneName) throws Exception {
        controls.selectOptionByDisplayedText(driver, ZONE_LIST_SLCT, zoneName);
    }

    @Step("Select Field Minimum Line Size")
    public void selectFieldMinimumLineSize(RemoteWebDriver driver, String option) throws Exception {
        controls.selectOptionByDisplayedText(driver, FIELD_MINIMUM_LINE_SIZE_SLCT, option);
    }

    @Step("Select Rank")
    public void selectRank(RemoteWebDriver driver, String option) throws Exception {
        controls.selectOptionByDisplayedText(driver, RANK_SLCT, option);
    }

    @Step("Click Enable Fatf Recommendation")
    public void clickEnableFatfRecommendation(RemoteWebDriver driver) {
        controls.performClick(driver, ENABLE_FATF_RECOMMENDATION_BTN);
    }

    @Step("Click Save Button")
    public void clickSaveBtn(RemoteWebDriver driver) {
        controls.performClick(driver, SAVE_BTN);
        controls.acceptAlert(driver);
    }

    @Step("Click Enable Check Of Unique Transaction RN")
    public void clickEnableCheckOfTransactionRN(RemoteWebDriver driver) {
        if (!controls.checkIfButtonSelected(driver, ENABLE_CHECK_OF_UNIQUE_TRANSACTION_RN_BTN)) {
            controls.performClick(driver, ENABLE_CHECK_OF_UNIQUE_TRANSACTION_RN_BTN);
        }
    }

    @Step("Click Disable Check Of Unique Transaction RN")
    public void clickDisableCheckOfTransactionRN(RemoteWebDriver driver) {
        if (controls.checkIfButtonSelected(driver, ENABLE_CHECK_OF_UNIQUE_TRANSACTION_RN_BTN)) {
            controls.performClick(driver, ENABLE_CHECK_OF_UNIQUE_TRANSACTION_RN_BTN);
        }
    }

    @Step("Click Information Available Within Transaction")
    public void clickInformationAvailableWithinTransaction(RemoteWebDriver driver) {
        controls.performClick(driver, INFORMATION_AVAILABLE_WITHIN_TRANSACTION_BTN);
    }

    @Step("Click Information Available By Other Means")
    public void clickInformationAvailableByOtherMeans(RemoteWebDriver driver) {
        controls.performClick(driver, INFORMATION_AVAILABLE_BY_OTHER_MEANS_BTN);
    }

    @Step("Click Information Available By Bics")
    public void clickInformationAvailableByBics(RemoteWebDriver driver) {
        controls.performClick(driver, INFORMATION_AVAILABLE_BY_BICS_BTN);
    }

    @Step("Insert Black List")
    public void insertBlackList(RemoteWebDriver driver, String text) {
        controls.clearTextBoxValue(driver, BLACK_LIST_TXT);
        controls.setTextBoxValue(driver, BLACK_LIST_TXT, text);
    }

    @Step("Insert Bics")
    public void insertBics(RemoteWebDriver driver, String text) {
        controls.setTextBoxValue(driver, BICS_TXT, text);
    }

    @Step("Insert European Codes")
    public void insertEuropeanCodes(RemoteWebDriver driver, String text) {
        controls.clearTextBoxValue(driver, EUROPEAN_CODES_TXT);
        controls.setTextBoxValue(driver, EUROPEAN_CODES_TXT, text);
    }

    @Step("Click Apply For Cross Border Traffic")
    public void clickApplyForCrossBorderTraffic(RemoteWebDriver driver) {
        controls.performClick(driver, APPLY_FOR_CROSS_BORDER_TRAFFIC_BTN);
    }

    @Step("Insert Currency Amount")
    public void insertCurrencyAmount(RemoteWebDriver driver, String text) {
        controls.setTextBoxValue(driver, CURRENCY_AMOUNT_TXT, text);
    }

    @Step("Select Currency")
    public void selectCurrency(RemoteWebDriver driver, String option) throws Exception {
        controls.selectOptionByDisplayedText(driver, CURRENCY_SLCT, option);
    }

    @Step("Click Currency Add Button")
    public void clickCurrencyAddButton(RemoteWebDriver driver) {
        controls.performClick(driver, CURRENCY_ADD_BTN);
    }

}
