package eastnets.screening.gui.advancedSettings.businessLogic;

import core.constants.screening.GeneralConstants;
import core.util.Wait;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;


public enum BusinessLogicNavigation {
    FATF_SETTINGS("//a[normalize-space()='FATF16 Settings']",
            "//a[normalize-space()='FATF16 Settings']");

    private final By navigationElement;
    private final By checkLabel;
    private final Wait wait;

    BusinessLogicNavigation(String navigationElement, String checkLabel) {
        this.navigationElement = By.xpath(navigationElement);
        this.checkLabel = By.xpath(checkLabel);
        this.wait = new Wait();
    }

    @Step("Navigate")
    public String navigate(RemoteWebDriver driver) {

        wait.waitUntilElementToBeClickable(driver, navigationElement, Wait.ONE_MINUTE_DURATION);
        driver.findElement(navigationElement).click();

        if (driver.findElements(checkLabel).size() == 0)
            return GeneralConstants.FAILED;

        return GeneralConstants.SUCCESS;
    }
}
