package eastnets.screening.gui.advancedSettings.Investigators;

import core.gui.Controls;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class InvestigatorsEditor {
    private static final String CHECKERS_CHECKBOX_LOCATOR = "settingsForm:Homepage_business:tabViewSettings:investigators_view:investigatorsDetail:selectedRole";
    private static final String MAY_ACT_AS_MAKER_CHECKBOX_LOCATOR = "settingsForm:Homepage_business:tabViewSettings:investigators_view:investigatorsDetail:mayActAsMaker";
    private static final String HIDDEN_CHECKBOX_LOCATOR = "settingsForm:Homepage_business:tabViewSettings:investigators_view:investigatorsDetail:hiddenInvestigator";
    private static final By SAVE_BUTTON_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:investigators_view:investigatorsDetail:btnSaveInvestigator");
    private static final By CANCEL_BUTTON_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:investigators_view:investigatorsDetail:btnCancelInvestigator");

    private final Controls controls;

    public InvestigatorsEditor() {
        this.controls = new Controls();
    }

    @Step("Check checker role")
    public void checkCheckerRoles(RemoteWebDriver driver, boolean checkCheckers) {
        controls.setCheckboxValueById(driver, CHECKERS_CHECKBOX_LOCATOR, checkCheckers);
    }

    @Step("Check May act as maker")
    public void checkMayActAsMaker(RemoteWebDriver driver, boolean checkMayActAsMaker) {
        controls.setCheckboxValueById(driver, MAY_ACT_AS_MAKER_CHECKBOX_LOCATOR, checkMayActAsMaker);
    }

    @Step("Check Hidden check box")
    public void checkHidden(RemoteWebDriver driver, boolean checkHidden) {
        controls.setCheckboxValueById(driver, HIDDEN_CHECKBOX_LOCATOR, checkHidden);
    }

    @Step("Click save button")
    public void clickSave(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SAVE_BUTTON_LOCATOR);
        driver.switchTo().alert().accept();
    }

    @Step("Click cancel button")
    public void clickCancel(RemoteWebDriver driver) {
        controls.performClickByJS(driver, CANCEL_BUTTON_LOCATOR);
    }


}


