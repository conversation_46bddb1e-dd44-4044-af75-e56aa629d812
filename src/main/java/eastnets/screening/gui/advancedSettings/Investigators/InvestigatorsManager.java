package eastnets.screening.gui.advancedSettings.Investigators;

import core.gui.Controls;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class InvestigatorsManager {

    private static final By NAME_INPUT_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:investigators_view:investName");
    private static final By HIDDEN_SELECT_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:investigators_view:investHiddenCbx_label");
    private static final By TYPE_SELECT_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:investigators_view:investTypeCbx_label");
    private static final By ROLES_SELECT_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:investigators_view:investRoleCbx_label");
    private static final By RESET_BUTTON_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:investigators_view:btnResetInvestigator");
    private static final By SEARCH_BUTTON_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:investigators_view:btnSearchInvestigator");
    private static final By NAME_IN_TABLE_LOCATOR = By.xpath("//tbody[contains(@id,'settingsForm:Homepage_business:tabViewSettings:investigators_view:_tblResults_data')]//tr//td[2]");

    private final Controls controls;

    public InvestigatorsManager() {
        this.controls = new Controls();
    }

    @Step("Set Name")
    public void setName(RemoteWebDriver driver, String name) {
        controls.setTextBoxValue(driver, NAME_INPUT_LOCATOR, name);
    }

    @Step("Select Hidden Value")
    public void selectHiddenValue(RemoteWebDriver driver, String hidden) throws Exception {
        controls.selectOptionByDisplayedText(driver, HIDDEN_SELECT_LOCATOR, hidden);
    }

    @Step("Select Investigator Type")
    public void selectInvestigatorType(RemoteWebDriver driver, String type) throws Exception {
        controls.selectOptionByDisplayedText(driver, TYPE_SELECT_LOCATOR, type);
    }

    @Step("Select 4eyes roles")
    public void select4eyesRoles(RemoteWebDriver driver, String roles) throws Exception {
        controls.selectOptionByDisplayedText(driver, ROLES_SELECT_LOCATOR, roles);
    }

    @Step("Click reset button")
    public void clickResetButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, RESET_BUTTON_LOCATOR);
    }

    @Step("Click search button")
    public void clickSearchButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SEARCH_BUTTON_LOCATOR);
    }

    @Step("Click Name in table")
    public void clickNameInTable(RemoteWebDriver driver, String name) {
        //Controls.performClickByJS(driver,NAME_IN_TABLE_LOCATOR);
        controls.performClick(driver, By.xpath("//a[.= '" + name + "']"));
    }
}