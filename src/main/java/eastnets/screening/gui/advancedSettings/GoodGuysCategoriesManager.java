package eastnets.screening.gui.advancedSettings;

import core.gui.Controls;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class GoodGuysCategoriesManager {

    private static final By CATEGORY_NAME_INPUT_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:tab_goodGuyCategories:_tblResults:newCategory");
    private static final By ADD_CATEGORY_BUTTON_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:tab_goodGuyCategories:_tblResults:btnSaveGGCategory");

    private final Controls controls;

    public GoodGuysCategoriesManager() {
        this.controls = new Controls();
    }

    @Step("Enter Category Name")
    public void enterCategoryName(RemoteWebDriver driver, String categoryName) {
        controls.setTextBoxValue(driver, CATEGORY_NAME_INPUT_LOCATOR, categoryName);
    }

    @Step("Click Add Category Button")
    public void clickAddCategoryButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, ADD_CATEGORY_BUTTON_LOCATOR);
    }

}
