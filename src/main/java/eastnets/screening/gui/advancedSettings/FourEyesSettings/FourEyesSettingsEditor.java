package eastnets.screening.gui.advancedSettings.FourEyesSettings;

import core.gui.Controls;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class FourEyesSettingsEditor {
    private static final By NAME_INPUT_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:Tab_secu4eyes:Sub_secu4eyes_create:configName");
    private static final By ZONE_SELECT_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:Tab_secu4eyes:Sub_secu4eyes_create:zoneId_label");
    private static final By LISTSET_SELECT_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:Tab_secu4eyes:Sub_secu4eyes_create:listSetId_label");
    private static final By RANK_SELECT_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:Tab_secu4eyes:Sub_secu4eyes_create:rank_label");
    private static final By AMOUNT_INPUT_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:Tab_secu4eyes:Sub_secu4eyes_create:amount");
    private static final By COUNTRY_INPUT_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:Tab_secu4eyes:Sub_secu4eyes_create:country");
    private static final By APPLICATION_SELECT_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:Tab_secu4eyes:Sub_secu4eyes_create:applicationMenuIdCreate_label");
    private static final By CONTEXT_FILTER_INPUT_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:Tab_secu4eyes:Sub_secu4eyes_create:contextFilter");
    private static final By SAVE_BUTTON_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:Tab_secu4eyes:Sub_secu4eyes_create:_btnSave");
    private static final By CANCEL_BUTTON_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:Tab_secu4eyes:Sub_secu4eyes_create:btnCancel");
    private static final By VALIDATE_SYNTAX_BUTTON_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:Tab_secu4eyes:Sub_secu4eyes_create:btnValidateFilter");

    private final Controls controls;

    public FourEyesSettingsEditor() {
        this.controls = new Controls();
    }

    @Step("Set Name")
    public void setName(RemoteWebDriver driver, String name) {
        controls.setTextBoxValue(driver, NAME_INPUT_LOCATOR, name);
    }

    @Step("Select Zone")
    public void selectZone(RemoteWebDriver driver, String zone) throws Exception {
        controls.selectOptionByDisplayedText(driver, ZONE_SELECT_LOCATOR, zone);
    }

    @Step("Select ListSet")
    public void selectListSet(RemoteWebDriver driver, String listSet) throws Exception {
        controls.selectOptionByDisplayedText(driver, LISTSET_SELECT_LOCATOR, listSet);
    }

    @Step("Select Rank")
    public void selectRank(RemoteWebDriver driver, String rank) throws Exception {
        controls.selectOptionByDisplayedText(driver, RANK_SELECT_LOCATOR, rank);
    }

    @Step("Set Amount")
    public void setAmount(RemoteWebDriver driver, String amount) {
        controls.setTextBoxValue(driver, AMOUNT_INPUT_LOCATOR, amount);
    }

    @Step("Set Country")
    public void setCountry(RemoteWebDriver driver, String country) {
        controls.setTextBoxValue(driver, COUNTRY_INPUT_LOCATOR, country);
    }

    @Step("Select Application")
    public void selectApplication(RemoteWebDriver driver, String application) throws Exception {
        controls.selectOptionByDisplayedText(driver, APPLICATION_SELECT_LOCATOR, application);
    }

    @Step("Set Context Filter")
    public void setContextFilter(RemoteWebDriver driver, String contextFilter) {
        controls.setTextBoxValue(driver, CONTEXT_FILTER_INPUT_LOCATOR, contextFilter);
    }

    @Step("Click Validate Syntax Button")
    public void clickValidateSyntaxButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, VALIDATE_SYNTAX_BUTTON_LOCATOR);
    }

    @Step("Click Save Button")
    public void clickSaveButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SAVE_BUTTON_LOCATOR);
        driver.switchTo().alert().accept();
    }

    @Step("Click Cancel Button")
    public void clickCancelButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, CANCEL_BUTTON_LOCATOR);
    }
}