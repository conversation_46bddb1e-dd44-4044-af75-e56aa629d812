package eastnets.screening.gui.advancedSettings.FourEyesSettings;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class FourEyesSettingsManager {
    private static final By SWITCH_4_EYES_ALL_DETECTIONS_BUTTON_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:Tab_secu4eyes:sub_4eyesSettings_list:_switchAllBtn");
    private static final By FOUR_EYES_ALL_DETECTIONS_RADIOBUTTON_YES_LOCATOR = By.xpath("//*[@id='settingsForm:Homepage_business:tabViewSettings:Tab_secu4eyes:sub_4eyesSettings_list:forceFeature']//*[contains(@class,'ui-radiobutton-box')]");
    private static final By NEW_BUTTON_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:Tab_secu4eyes:sub_4eyesSettings_list:_tblResults:_btnNew");
    private static final By DELETE_BUTTON_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:Tab_secu4eyes:sub_4eyesSettings_list:_tblResults:_btnDelete");
    private static final By ENABLE_BUTTON_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:Tab_secu4eyes:sub_4eyesSettings_list:_tblResults:_btnEnable");
    private static final By DISABLE_BUTTON_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:Tab_secu4eyes:sub_4eyesSettings_list:_tblResults:_btnDisable");
    private static final By DECISION_MODE_PESSIMISTIC_RADIOBUTTON_LOCATOR = By.xpath("//*[@id='settingsForm:Homepage_business:tabViewSettings:Tab_secu4eyes:sub_4eyesSettings_list:_4eyesMode']//*[contains(@class,'ui-radiobutton-box')]");
    private static final By DECISION_MODE_LAST_INPUT_PREVAILS_RADIOBUTTON_LOCATOR = By.xpath("//*[@id='settingsForm:Homepage_business:tabViewSettings:Tab_secu4eyes:sub_4eyesSettings_list:_4eyesMode']//*[contains(@class,'ui-radiobutton-box')]");
    private static final By DECISION_MODE_SWITCH_BUTTON = By.id("settingsForm:Homepage_business:tabViewSettings:Tab_secu4eyes:sub_4eyesSettings_list:_switchModeBtn");
    private static final By SORT_BY_ID_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:Tab_secu4eyes:sub_4eyesSettings_list:_tblResults:_colId");

    private static final String CHECKBOX_BUTTON_LOCATOR = "//td[.='%s']//ancestor::tr[1]//td//div[contains(@class,'ui-selectbooleancheckbox ui-chkbox ui-widget')]";
    private static final By CONFIRMNATION_MESSAGE = By.xpath("//span[@class='ui-message-error-detail']");

    private final Controls controls;
    private final Wait wait;

    public FourEyesSettingsManager() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    @Step("Sort by ID Desc")
    public void sortByIdDesc(RemoteWebDriver driver) throws InterruptedException {
        Allure.step("Sort result table by ID Desc.");
        if (!controls.getWebElement(driver, SORT_BY_ID_LOCATOR).getAttribute("aria-sort").equalsIgnoreCase("descending")) {
            controls.performClick(driver, SORT_BY_ID_LOCATOR);
            wait.time(Wait.ONE_SECOND * 5);
            controls.performClick(driver, SORT_BY_ID_LOCATOR);
            wait.time(Wait.ONE_SECOND * 5);

        }
    }


    @Step("Click checkbox button")
    public void clickCheckboxButton(RemoteWebDriver driver, String confName) {
        controls.performClick(driver, By.xpath(String.format(CHECKBOX_BUTTON_LOCATOR, confName)));
    }


    @Step("Disable 4 eyes")
    public void disableFourEyes(RemoteWebDriver driver) {
        if (controls.getWebElement(driver, FOUR_EYES_ALL_DETECTIONS_RADIOBUTTON_YES_LOCATOR).getAttribute("class").contains("ui-state-active")) {
            controls.performClickByJS(driver, SWITCH_4_EYES_ALL_DETECTIONS_BUTTON_LOCATOR);
        }
    }

    @Step("Enable 4 eyes")
    public void enableFourEyes(RemoteWebDriver driver) throws Exception {

        if (!controls.getWebElement(driver, FOUR_EYES_ALL_DETECTIONS_RADIOBUTTON_YES_LOCATOR).getAttribute("class").contains("ui-state-active")) {
            controls.performClickByJS(driver, SWITCH_4_EYES_ALL_DETECTIONS_BUTTON_LOCATOR);
            wait.waitUntilAjaxLoaderDisappear(driver);

        }
    }

    @Step("Enable Decision Mode Pessimistic")
    public void enableDecisionModePessimistic(RemoteWebDriver driver) throws Exception {
        if (!controls.getWebElement(driver, DECISION_MODE_PESSIMISTIC_RADIOBUTTON_LOCATOR).getAttribute("class").contains("ui-state-active")) {
            controls.performClickByJS(driver, DECISION_MODE_SWITCH_BUTTON);
        }
    }

    @Step("Enable Decision Mode Last Input Prevails")
    public void enableDecisionModeLastInputPrevails(RemoteWebDriver driver) throws Exception {
        if (controls.getWebElement(driver, DECISION_MODE_PESSIMISTIC_RADIOBUTTON_LOCATOR).getAttribute("class").contains("ui-state-active")) {
            controls.performClickByJS(driver, DECISION_MODE_SWITCH_BUTTON);
        }
    }

    @Step("Click New button")
    public void clickNewButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, NEW_BUTTON_LOCATOR);
    }

    @Step("Click Delete button")
    public void clickDeleteButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, DELETE_BUTTON_LOCATOR);
    }

    @Step("Click Enable button")
    public void clickEnableButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, ENABLE_BUTTON_LOCATOR);
        driver.switchTo().alert().accept();
    }

    @Step("Click Disable button")
    public void clickDisableButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, DISABLE_BUTTON_LOCATOR);
    }


}





