package eastnets.screening.gui;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Allure;
import org.openqa.selenium.By;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.util.List;

public class Notifications {

    private static final By NOTIFICATIONS_ICON = By.xpath("//i[contains(@class,'ringBell')]");
    private static final By NOTIFICATIONS_COUNT_LOCATOR = By.id("topbar-right:countNotification");
    private static final By NOTIFICATIONS_LIST_LOCATOR = By.className("notification-item");
    public static final By ID_TEXTBOX_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:tab_notification:id");
    public static final By MODULE_DDL_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:tab_notification:categoryCbx_label");
    public static final By RESET_BUTTON = By.id("settingsForm:Homepage_business:tabViewSettings:tab_notification:btnReset");
    public static final By SEARCH_BUTTON = By.id("settingsForm:Homepage_business:tabViewSettings:tab_notification:btnSearchNotification");
    public static final By ID_LOCATOR = By.xpath("//span[contains(text(),'Id')]");
    public static final By NOTIFICATION_MESSAGE_LOCATOR = By.xpath("//span[contains(text(),'Notification Message')]");
    public static final By MODULE_LOCATOR = By.xpath("//span[contains(text(),'Module')]");
    public static final By TYPE_LOCATOR = By.xpath("//span[contains(text(),'Type')]");
    public static final By CREATION_DATE_LOCATOR = By.xpath("//span[contains(text(),'Creation Date')]");
    public static final By POPUP_ID_LOCATOR = By.xpath("//label[contains(text(),'Id:')]");
    public static final By POPUP_ID_TEXTBOX_LOCATOR = By.name("settingsForm:Homepage_business:tabViewSettings:tab_notification:notificationDetailsDialog:id");
    public static final By POPUP_MODULE_LOCATOR = By.xpath("//label[contains(text(),'Module:')]");
    public static final By POPUP_MODULE_TEXTBOX_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:tab_notification:notificationDetailsDialog:module");
    public static final By POPUP_TYPE_LOCATOR = By.xpath("//label[contains(text(),'Type:')]");
    public static final By POPUP_TYPE_TEXTBOX_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:tab_notification:notificationDetailsDialog:notificationTypeDescription");
    public static final By POPUP_CREATED_BY_LOCATOR = By.xpath("//label[contains(text(),'Created By:')]");
    public static final By POPUP_CREATED_BY_TEXTBOX_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:tab_notification:notificationDetailsDialog:createdBy");
    public static final By POPUP_CREATION_DATE_LOCATOR = By.xpath("//label[contains(text(),'Creation Date:')]");
    public static final By POPUP_CREATION_DATE_TEXTBOX_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:tab_notification:notificationDetailsDialog:createdDate");
    public static final By POPUP_NOTIFICATION_MESSAGE_LOCATOR = By.xpath("//label[contains(text(),'Notification Message:')]");
    public static final By POPUP_NOTIFICATION_MESSAGE_TEXTBOX_LOCATOR = By.id("settingsForm:Homepage_business:tabViewSettings:tab_notification:notificationDetailsDialog:notification");
    public static final By POPUP_CANCEL_BUTTON_LOCATOR = By.xpath("//span[contains(text(),'Cancel')]");
    public static final By FIRST_NOTIFICATION_LOCATOR = By.xpath("//span[contains(text(),'Approval No.')]");

    private final Controls controls;
    private final Wait wait;

    public Notifications() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    public void clickNotificationsIcon(RemoteWebDriver driver) throws InterruptedException {
        Allure.step("Click Notifications Icon");
        controls.performClick(driver, NOTIFICATIONS_ICON);
        wait.time(Wait.ONE_SECOND * 5);
    }

    public String getNotificationsCount(WebDriver driver) {
        Allure.step("Get Notifications Count");
        return driver.findElement(NOTIFICATIONS_COUNT_LOCATOR).getText();
    }

    public List<WebElement> getNotificationsList(WebDriver driver) {
        Allure.step("Get Notifications List");
        return driver.findElements(NOTIFICATIONS_LIST_LOCATOR);
    }

    public int getNumberOfNotificationsInList(WebDriver driver) {
        Allure.step("Get Notifications List");
        return driver.findElements(NOTIFICATIONS_LIST_LOCATOR).size();
    }

    public void clickResetButton(RemoteWebDriver driver) {
        Allure.step("Click Reset button");
        controls.performClickByJS(driver, RESET_BUTTON);
    }

    public void clickSearchButton(RemoteWebDriver driver) {
        Allure.step("Click Search button");
        controls.performClickByJS(driver, SEARCH_BUTTON);
    }

    public void selectModule(RemoteWebDriver driver, String module) throws Exception {
        Allure.step("Select Module");
        controls.selectOptionByDisplayedText(driver, MODULE_DDL_LOCATOR, module);
    }

    public void clickFirstNotification(RemoteWebDriver driver) {
        Allure.step("Click First Notification");
        controls.performClickByJS(driver, FIRST_NOTIFICATION_LOCATOR);
    }
}
