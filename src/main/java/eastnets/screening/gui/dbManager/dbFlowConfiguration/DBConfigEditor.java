package eastnets.screening.gui.dbManager.dbFlowConfiguration;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.util.List;


public class DBConfigEditor {

    /** Flow Information Locators */
    private static final By ZONE_SELECT_LOCATOR = By.id("dbConnectorConfiguratoinEditor:db_connector_editor:zoneCbx");
    private static final By NAME_INPUT_LOCATOR = By.id("dbConnectorConfiguratoinEditor:db_connector_editor:dbFlowName");
    private static final By TYPE_SELECT_LOCATOR = By.id("dbConnectorConfiguratoinEditor:db_connector_editor:TypeCbx");
    private static final By DB_TYPE_SELECT_LOCATOR = By.id("dbConnectorConfiguratoinEditor:db_connector_editor:dataBaseType");
    private static final By TABLE_NAME_INPUT_LOCATOR = By.id("dbConnectorConfiguratoinEditor:db_connector_editor:tableName");
    private static final By OUTPUT_FLOW_SELECT_LOCATOR = By.id("dbConnectorConfiguratoinEditor:db_connector_editor:outputFlowCbx");
    private static final By OUTPUT_FLOW_OPTIONS_LOCATOR = By.xpath("//*[@id='dbConnectorConfiguratoinEditor:db_connector_editor:outputFlowCbx_items']//li");
    private static final By ZONE_OPTIONS_LOCATORS = By.xpath("//*[@id='dbConnectorConfiguratoinEditor:db_connector_editor:zoneCbx_items']//li");

    /** Database Login Information Locators */
    private static final By DB_NAME_INPUT_LOCATOR = By.id("dbConnectorConfiguratoinEditor:db_connector_editor:dbName");
    private static final By HOST_INPUT_LOCATOR = By.id("dbConnectorConfiguratoinEditor:db_connector_editor:hostName");
    private static final By PORT_INPUT_LOCATOR = By.id("dbConnectorConfiguratoinEditor:db_connector_editor:port_input");
    private static final By USER_NAME_INPUT_LOCATOR = By.id("dbConnectorConfiguratoinEditor:db_connector_editor:dbConnUserName");
    private static final By PASSWORD_INPUT_LOCATOR = By.id("dbConnectorConfiguratoinEditor:db_connector_editor:dbConnPassword");


    /** Buttons Locators */
    private static final By SAVE_BUTTON_LOCATOR = By.id("dbConnectorConfiguratoinEditor:db_connector_editor:btnSave");
    private static final By CANCEL_BUTTON_LOCATOR = By.id("dbConnectorConfiguratoinEditor:db_connector_editor:btnCancel");
    private static final By TEST_CONNECTION_BUTTON_LOCATOR = By.id("dbConnectorConfiguratoinEditor:db_connector_editor:btnConnect");
    private static final By SHOW_FIELDS_BUTTON_LOCATOR = By.id("dbConnectorConfiguratoinEditor:db_connector_editor:btnGetFields");

    /** Field Details Locators */
    private static final By ADD_ALL_BUTTON_LOCATOR = By.xpath("//button[@title='Add All']");
    private static final String FILED_LOCATOR = "//*[contains(@data-item-label,'%s')]";
    private static final By SHOW_BUTTON_LOCATOR = By.id("dbConnectionFormatField:db_connector_field_details:showButton");
    private static final By FIELD_NAME_INPUT_LOCATOR = By.id("dbConnectionFormatField:db_connector_field_details:fieldLabelInput");
    private static final By FIELD_TYPE_SELECT_LOCATOR = By.id("dbConnectionFormatField:db_connector_field_details:fieldTypeInput");
    private static final By FIELD_TYPE_OPTIONS_LOCATOR = By.xpath("//li[contains(@id,'dbConnectionFormatField:db_connector_field_details:fieldTypeInput')]");
    private static final By SCAN_CHECK_BOX_LOCATOR = By.id("dbConnectionFormatField:db_connector_field_details:ScanChkBx");
    private static final By ADD_TO_CONTEXT_CHECK_BOX_LOCATOR = By.id("dbConnectionFormatField:db_connector_field_details:ContextChkBx");
    private static final By PRIMARY_KEY_CHECK_BOX_LOCATOR = By.id("dbConnectionFormatField:db_connector_field_details:PrimaryKeyChkBx");
    private static final By OUTPUT_CHECK_BOX_LOCATOR = By.id("dbConnectionFormatField:db_connector_field_details:OutputChkBx");
    private static final By PARAMETER_CHECK_BOX_LOCATOR = By.id("dbConnectionFormatField:db_connector_field_details:ParameterChkBx");
    private static final By FOREIGN_KEY_CHECK_BOX_LOCATOR= By.id("dbConnectionFormatField:db_connector_field_details:ForeignKeyChkBx");
    private static final By SAVE_FIELD_BUTTON_LOCATOR = By.id("dbConnectionFormatField:db_connector_field_details:btnSave");

    private final Controls controls;
    private final Wait wait;

    public DBConfigEditor() {
        this.controls = new Controls();
        this.wait = new Wait();
    }


    @Step("Select zone")
    public void select_zone(RemoteWebDriver driver, String zoneName) throws Exception {
        controls.selectOptionByDisplayedText(driver, ZONE_SELECT_LOCATOR, zoneName);
    }

    @Step("Set name")
    public void set_name(RemoteWebDriver driver, String name) {
        controls.setTextBoxValue(driver, NAME_INPUT_LOCATOR, name);
    }

    @Step("Select database type")
    public void select_database_type(RemoteWebDriver driver, String dbType) throws Exception {
        controls.selectOptionByDisplayedText(driver, DB_TYPE_SELECT_LOCATOR, dbType);
    }

    @Step("Select type")
    public void select_type(RemoteWebDriver driver, String flowType) throws Exception {
        controls.selectOptionByDisplayedText(driver, TYPE_SELECT_LOCATOR, flowType);
    }

    @Step("Select output flow")
    public void select_output_flow(RemoteWebDriver driver, String outputFlow) throws Exception {
        controls.selectOptionByDisplayedText(driver, OUTPUT_FLOW_SELECT_LOCATOR, outputFlow);
    }

    @Step("Set table name")
    public void set_table_name(RemoteWebDriver driver, String tableName) {
        controls.setTextBoxValue(driver, TABLE_NAME_INPUT_LOCATOR, tableName);
    }

    @Step("Set db name")
    public void set_db_name(RemoteWebDriver driver, String dbName) {
        controls.setTextBoxValue(driver, DB_NAME_INPUT_LOCATOR, dbName);
    }

    @Step("Set host")
    public void set_host(RemoteWebDriver driver, String host) {
        controls.setTextBoxValue(driver, HOST_INPUT_LOCATOR, host);
    }

    @Step("Set port")
    public void set_port(RemoteWebDriver driver, String port) {
        controls.setTextBoxValue(driver, PORT_INPUT_LOCATOR, port);
    }

    @Step("Set user name")
    public void set_user_name(RemoteWebDriver driver, String userName) {
        controls.setTextBoxValue(driver, USER_NAME_INPUT_LOCATOR, userName);
    }

    @Step("Set password")
    public void set_password(RemoteWebDriver driver, String password) {
        controls.setTextBoxValue(driver, PASSWORD_INPUT_LOCATOR, password);
    }


    @Step("Click save button")
    public void click_save_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SAVE_BUTTON_LOCATOR);
    }

    @Step("Click cancel button")
    public void click_cancel_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, CANCEL_BUTTON_LOCATOR);
    }

    @Step("Click test connection button")
    public void click_test_connection_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, TEST_CONNECTION_BUTTON_LOCATOR);
    }

    @Step("Click show fields button")
    public void click_show_fields_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SHOW_FIELDS_BUTTON_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Select field")
    public void select_field(RemoteWebDriver driver, String fieldName) throws InterruptedException {
        try
        {
        controls.performClick(driver, By.xpath(String.format(FILED_LOCATOR, fieldName)));
        wait.time(Wait.ONE_SECOND*2);
        }
        catch (Exception e)
        {
            controls.performClick(driver, By.xpath(String.format(FILED_LOCATOR, fieldName.toUpperCase())));
        }
    }

    @Step("Click add all button")
    public void click_add_all_button(RemoteWebDriver driver) {
        controls.performClick(driver, ADD_ALL_BUTTON_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Click show button")
    public void click_show_button(RemoteWebDriver driver) {
        controls.performClick(driver, SHOW_BUTTON_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("check show button is displayed")
    public boolean check_show_button_is_displayed(RemoteWebDriver driver) {
        return controls.exists(driver, SHOW_BUTTON_LOCATOR);
    }
    @Step("Verify field name is disabled")
    public boolean is_field_name_disabled(RemoteWebDriver driver) {
        return driver.findElement(FIELD_NAME_INPUT_LOCATOR).getAttribute("aria-readonly").equals("true");
    }

    @Step("Get all field types list")
    public List<String> get_all_field_types(RemoteWebDriver driver) {
        return controls.getDropDownOptionsList(driver, FIELD_TYPE_SELECT_LOCATOR,FIELD_TYPE_OPTIONS_LOCATOR);

    }

    @Step("Verify that 'Scan this field' checkbox is displayed")
    public boolean is_scan_check_box_displayed(RemoteWebDriver driver) {
        return controls.exists(driver, SCAN_CHECK_BOX_LOCATOR);
    }

    @Step("Verify that 'Add to context' checkbox is displayed")
    public boolean is_add_to_context_check_box_displayed(RemoteWebDriver driver) {
        return controls.exists(driver, ADD_TO_CONTEXT_CHECK_BOX_LOCATOR);
    }

    @Step("Verify that 'Primary key' checkbox is displayed")
    public boolean is_primary_key_check_box_displayed(RemoteWebDriver driver) {
        return controls.exists(driver, PRIMARY_KEY_CHECK_BOX_LOCATOR);
    }

    @Step("Verify that 'Output' checkbox is displayed")
    public boolean is_output_check_box_displayed(RemoteWebDriver driver) {
        return controls.exists(driver, OUTPUT_CHECK_BOX_LOCATOR);
    }

    @Step("Verify that 'Parameter' checkbox is displayed")
    public boolean is_parameter_check_box_displayed(RemoteWebDriver driver) {
        return controls.exists(driver, PARAMETER_CHECK_BOX_LOCATOR);
    }

    @Step("Verify That 'Foreign Key' checkbox is displayed")
    public boolean is_foreign_key_check_box_displayed(RemoteWebDriver driver) {
        return controls.exists(driver, FOREIGN_KEY_CHECK_BOX_LOCATOR);
    }
    @Step("Select field type")
    public void select_field_type(RemoteWebDriver driver, String fieldType) throws Exception {
        controls.selectOptionByDisplayedText(driver, FIELD_TYPE_SELECT_LOCATOR, fieldType);
    }

    @Step("select scan this field button")
    public void click_scan_check_box(RemoteWebDriver driver, boolean flag) {
        if (flag)
            controls.performClick(driver, SCAN_CHECK_BOX_LOCATOR);
    }
    @Step("select primary key button")
    public void click_primary_key_check_box(RemoteWebDriver driver, boolean flag) {
        if (flag)
            controls.performClick(driver, PRIMARY_KEY_CHECK_BOX_LOCATOR);
    }

    @Step("select add to context button")
    public void click_add_to_context_check_box(RemoteWebDriver driver, boolean flag) {
        if (flag)
            controls.performClick(driver, ADD_TO_CONTEXT_CHECK_BOX_LOCATOR);
    }

    @Step("select output check box")
    public void click_output_check_box(RemoteWebDriver driver, boolean flag) {
        if (flag)
            controls.performClick(driver, OUTPUT_CHECK_BOX_LOCATOR);
    }

    @Step("select parameter check box")
    public void click_parameter_check_box(RemoteWebDriver driver, boolean flag) {
        if (flag)
            controls.performClick(driver, PARAMETER_CHECK_BOX_LOCATOR);
    }

    @Step("Click save field button")
    public void click_save_field_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SAVE_FIELD_BUTTON_LOCATOR);
    }

    @Step("Click foreign key check box")
    public void click_foreign_key_check_box(RemoteWebDriver driver, boolean flag) {
        if (flag)
            controls.performClick(driver, FOREIGN_KEY_CHECK_BOX_LOCATOR);
    }

    @Step("Get all available zones")
    public List<String> get_all_zones(RemoteWebDriver driver) {
       return controls.getDropDownOptionsList(driver, ZONE_SELECT_LOCATOR, ZONE_OPTIONS_LOCATORS);

    }

    @Step("Get all available output flows")
    public List<String> get_all_output_flows(RemoteWebDriver driver) {

       return controls.getDropDownOptionsList(driver, OUTPUT_FLOW_SELECT_LOCATOR, OUTPUT_FLOW_OPTIONS_LOCATOR);

    }

}
