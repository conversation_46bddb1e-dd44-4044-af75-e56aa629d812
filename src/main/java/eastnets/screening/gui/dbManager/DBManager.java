package eastnets.screening.gui.dbManager;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class DBManager {

    private static final By ZONE_DROP_DOWN_LOCATOR = By.id("dbConnectionForm:Homepage_tabs_business:tabDbConfig:Homepage_business:zoneCbx");
    private static final By NAME_INPUT_LOCATOR = By.id("dbConnectionForm:Homepage_tabs_business:tabDbConfig:Homepage_business:flowNameField");
    private static final By DATABASE_TYPE_LOCATOR = By.id("dbConnectionForm:Homepage_tabs_business:tabDbConfig:Homepage_business:dbTypeCbx");
    private static final By FLOW_TYPE_LOCATOR = By.id("dbConnectionForm:Homepage_tabs_business:tabDbConfig:Homepage_business:flowTypeCbx");
    private static final By RESET_BUTTON_LOCATOR = By.xpath("//button[.='Reset']");
    private static final By SEARCH_BUTTON_LOCATOR = By.id("dbConnectionForm:Homepage_tabs_business:tabDbConfig:Homepage_business:btnLESearch");
    private static final By ADD_NEW_DB_BUTTON_LOCATOR = By.id("dbConnectionForm:Homepage_tabs_business:tabDbConfig:Homepage_business:dbConnectorConfigurationResults:_tblResults:_btnNew");
    private static final By DELETE_BUTTON_LOCATOR = By.id("dbConnectionForm:Homepage_tabs_business:tabDbConfig:Homepage_business:dbConnectorConfigurationResults:_tblResults:_btnDelete");
    private static final By NO_RECORD_FOUND_LOCATOR = By.xpath("//*[.='No records found.']");

    private final Controls controls;
    private final Wait wait;

    public DBManager() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    @Step("Click search button")
    public void click_search_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SEARCH_BUTTON_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Click reset button")
    public void click_reset_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, RESET_BUTTON_LOCATOR);
        driver.switchTo().alert().accept();
    }

    @Step("Select zone")
    public void select_zone(RemoteWebDriver driver, String zone) throws Exception {
        controls.selectOptionByDisplayedText(driver, ZONE_DROP_DOWN_LOCATOR, zone);
    }

    @Step("Set name")
    public void set_name(RemoteWebDriver driver, String name) {
        controls.setTextBoxValue(driver, NAME_INPUT_LOCATOR, name);
    }

    @Step("Select database type")
    public void select_database_type(RemoteWebDriver driver, String dbType) throws Exception {
        controls.selectOptionByDisplayedText(driver, DATABASE_TYPE_LOCATOR, dbType);
    }

    @Step("Select flow type")
    public void select_flow_type(RemoteWebDriver driver, String flowType) throws Exception {
        controls.selectOptionByDisplayedText(driver, FLOW_TYPE_LOCATOR, flowType);
    }

    @Step("Click on select")
    public boolean select_flow_by_name_from_result(RemoteWebDriver driver, String name) {
        wait.waitUntilAjaxLoaderDisappear(driver);
        controls.performClickByJS(driver, By.xpath(String.format("//a[.='%s']", name)));
        return true;
    }


    @Step("Click add new DB button")
    public void click_add_new_db_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, ADD_NEW_DB_BUTTON_LOCATOR);
    }

    @Step("Click delete DB button")
    public void click_delete_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, DELETE_BUTTON_LOCATOR);
    }

    @Step("check Add new DB button is displayed")
    public boolean is_add_new_db_button_displayed(RemoteWebDriver driver) {
        return controls.exists(driver, ADD_NEW_DB_BUTTON_LOCATOR);
    }

    @Step("Verify no record found message is displayed")
    public boolean is_no_record_found_message_displayed(RemoteWebDriver driver) {
        return controls.exists(driver, NO_RECORD_FOUND_LOCATOR);
    }
}