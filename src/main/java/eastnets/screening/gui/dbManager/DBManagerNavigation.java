package eastnets.screening.gui.dbManager;

import core.gui.Controls;
import core.util.Wait;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public enum DBManagerNavigation {

    DB_FLOW_CONFIGURATION("Database Flow Configuration",
            "dbConnectionForm:Homepage_tabs_business:tabDbConfig:Homepage_business:zoneCbx"),
    DB_SCAN_REQUEST("Database Scan Request",
            "dbConnectionForm:Homepage_tabs_business:tabDbConfig:db-scan-request:requestId");

    private final By element;
    private final By validation_element;
    private final Controls controls;
    private final Wait wait;

    DBManagerNavigation(String element, String validation_element) {
        this.element = By.linkText(element);
        this.validation_element = By.id(validation_element);
        this.controls = new Controls();
        this.wait = new Wait();
    }

    public boolean navigate(RemoteWebDriver driver) {
        controls.performClick(driver, element);
        return wait.waitUntilElementToBeVisible(driver, validation_element, Wait.TEN_SECOND_DURATION).isDisplayed();
    }
}
