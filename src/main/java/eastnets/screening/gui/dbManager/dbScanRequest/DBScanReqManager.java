package eastnets.screening.gui.dbManager.dbScanRequest;

import core.gui.Controls;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class DBScanReqManager {

    private static final By ID_LOCATOR = By.id("dbConnectionForm:Homepage_tabs_business:tabDbConfig:db-scan-request:requestId_input");
    private static final By SEARCH_BTN_LOCATOR = By.id("dbConnectionForm:Homepage_tabs_business:tabDbConfig:db-scan-request:btnLESearch");
    private static final By REQUEST_CHECKBOX_LOCATORS = By.xpath(
            "//*[@id= 'dbConnectionForm:Homepage_tabs_business:tabDbConfig:db-scan-request:db-scan-request" +
                    "-table-result:_tblDbScanResult_data']//div[contains(@class,'ui-selectbooleancheckbox')]");
    private static final By VIEW_PROCESS_DETAILS_BTN_LOCATOR = By.id(
            "dbConnectionForm:Homepage_tabs_business:tabDbConfig:db-scan-request:db-scan-request-table-result" +
                    ":_tblDbScanResult:btnViewProcessesDetails");
    private static final By PROCESSES_CHECKBOX_LOCATORS = By.xpath(
            "//*[@id= 'dbConnectionForm:Homepage_tabs_business:tabDbConfig:db-scan-request:db-scan-processes-request-table-result" +
                    ":_tblDbScanRequestProcessDetailsResult_data']//div[contains(@class,'ui-selectbooleancheckbox')]");
    private static final By VIEW_RUNNING_PROCESSES_DETAILS_BTN_LOCATOR = By.id("dbConnectionForm:Homepage_tabs_business:tabDbConfig:db-scan-request:db-scan-processes-request-table-result" +
            ":_tblDbScanRequestProcessDetailsResult:btnViewRunningProcessesDetails");
    private static final By PROCESS_STATUS_LOCATOR = By.xpath(
            "//*[@id= 'dbConnectionForm:Homepage_tabs_business:tabDbConfig:db-scan-request" +
                    ":db-scan-processes-request-table-result:_tblDbScanRequestProcessDetailsResult_data']//tr[1]//td[3]");
    private static final By REQUEST_STATUS_LOCATOR = By.xpath(
            "//*[@id = 'dbConnectionForm:Homepage_tabs_business:tabDbConfig:db-scan-request" +
            ":db-scan-request-table-result:_tblDbScanResult_data']//tr[1]//td[8]");

    private final Controls controls;

    public DBScanReqManager() {
        this.controls = new Controls();
    }

    @Step("Click on search button")
    public void click_search_button(RemoteWebDriver driver) {
        controls.performClick(driver, SEARCH_BTN_LOCATOR);
    }

    @Step("Click on the checkbox of the first request")
    public void click_on_first_request_checkbox(RemoteWebDriver driver) {
        controls.performClick(driver, REQUEST_CHECKBOX_LOCATORS);
    }

    @Step("Click on view process details button")
    public void click_on_view_process_details_button(RemoteWebDriver driver) {
        controls.performClick(driver, VIEW_PROCESS_DETAILS_BTN_LOCATOR);
    }

    @Step("Click on the checkbox of the first process")
    public void click_on_first_process_checkbox(RemoteWebDriver driver) {
        controls.performClick(driver, PROCESSES_CHECKBOX_LOCATORS);
    }

    @Step("Click on view running process details button")
    public void click_on_view_running_process_details_button(RemoteWebDriver driver) {
        controls.performClick(driver, VIEW_RUNNING_PROCESSES_DETAILS_BTN_LOCATOR);
    }

    @Step("Get the process status")
    public String get_process_status(RemoteWebDriver driver) {
        return controls.getElementText(driver, PROCESS_STATUS_LOCATOR);
    }

    @Step("Get the request status")
    public String get_request_status(RemoteWebDriver driver) {
        return controls.getElementText(driver, REQUEST_STATUS_LOCATOR);
    }
}
