package eastnets.screening.gui.dbManager.dbScanRequest;

import core.gui.Controls;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class DBScanReqEditor {

    private static final By SCAN_SESSION_ID = By.xpath("//*[.='Scan Session Id:']//ancestor::div[1]//div//label");
    private static final By SESSION_ID_VALUE_LOCATOR = By.xpath("//*[.='Scan Session Id:']//ancestor::div[1]//div//label//following-sibling::div");
    private static final By PROCESS_STATUS_VALUE_LOCATOR = By.xpath("//*[.='Process Status:']//ancestor::div[1]//div//label");
    private static final By TOTAL_RECORDS_UPDATED_VALUE_LOCATOR = By.xpath("//*[.='Total Records Updated:']//ancestor::div[1]//div//label");
    private static final By CLOSE_LOCATOR = By.xpath("//*[contains(@id , 'dbConnectionForm:Homepage_tabs_business:tabDbConfig:db-scan-request:db-scan-processes-request-table-result:')]" +
            "//*[@aria-label='Close']");

    private final Controls controls;

    public DBScanReqEditor() {
        this.controls = new Controls();
    }

    @Step("Get the scan session id")
    public String get_scan_session_id(RemoteWebDriver driver) {
        return controls.getElementText(driver, SCAN_SESSION_ID);
    }

    @Step("Get the session id value")
    public String get_session_id_value(RemoteWebDriver driver) {
        return controls.getElementText(driver, SESSION_ID_VALUE_LOCATOR);
    }

    @Step("Get the process status")
    public String get_process_status(RemoteWebDriver driver) {
        return controls.getElementText(driver, PROCESS_STATUS_VALUE_LOCATOR);
    }

    @Step("Get the total records updated value")
    public String get_total_records_updated_value(RemoteWebDriver driver) {
        return controls.getElementText(driver, TOTAL_RECORDS_UPDATED_VALUE_LOCATOR);
    }

    @Step("Click on close button")
    public void click_close_button(RemoteWebDriver driver) {
        controls.performClick(driver, CLOSE_LOCATOR);
    }


    public enum DBScanReqNavigation {
        PROCESS_DETAILS("Process Details"),
        STATISTICS("Statistics");

        private final By locator;
        private final Controls controls;

        DBScanReqNavigation(String locator) {
            this.locator = By.linkText(locator);
            this.controls = new Controls();
        }

        public void navigate(RemoteWebDriver driver) {
            controls.performClick(driver, locator);
            }
    }
}
