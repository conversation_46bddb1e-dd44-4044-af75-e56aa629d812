package eastnets.screening.gui.repeatManager;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public enum RepeatManagerNavigation {
    Configuration("//a[contains(text(),'Configuration')]",
            "repeatManagerForm:homepage_business:tabViewRepeat:tab_repeatConfig:repeatConfigEnabled"),

    Data("//a[contains(text(),'Data')]",
            "repeatManagerForm:homepage_business:tabViewRepeat:tab_repeatData:Zone"),
    EXACT_MATCH("//a[contains(text(),'Exact Match')]",
            "repeatManagerForm:homepage_business:tabViewRepeat:tab_exactMatchConfig:exactMatchConfigEnabled");

    private final By navigationElement;
    private final By checkLabel;
    private final Controls controls;
    private final Wait wait;

    RepeatManagerNavigation(String navigationElement, String checkLabel) {
        this.navigationElement = By.xpath(navigationElement);
        this.checkLabel = By.id(checkLabel);
        this.controls = new Controls();
        this.wait = new Wait();
    }

    @Step("Navigation")
    public boolean navigate(RemoteWebDriver driver) {
        controls.waitAndClick(driver, navigationElement, Wait.TEN_SECOND_DURATION);
        return wait.waitUntilElementToBeVisible(driver, checkLabel, Wait.ONE_MINUTE_DURATION).isDisplayed();
    }
}