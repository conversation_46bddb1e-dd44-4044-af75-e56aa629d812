package eastnets.screening.gui.repeatManager;

import core.gui.Controls;
import core.util.Wait;
import eastnets.screening.entity.Repeat;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class ConfigurationManager {
    private static final String CONFIGURATION_ENABLE_FIELD_LOCATOR = "repeatManagerForm:homepage_business:tabViewRepeat:tab_repeatConfig:repeatConfigEnabled";
    private static final By SAVE_BUTTON_LOCATOR = By.id("repeatManagerForm:homepage_business:tabViewRepeat:tab_repeatConfig:generalSaveButton");
    private static final By LIST_DROP_DOWN_LOCATOR = By.id("repeatManagerForm:homepage_business:tabViewRepeat:tab_repeatConfig:selectedRepeat");
    private static final By RETENTION_TIME_FIELD_LOCATOR = By.id("repeatManagerForm:homepage_business:tabViewRepeat:tab_repeatConfig:retention_input");
    private static final By ZONE_FIELD_LOCATOR = By.id("repeatManagerForm:homepage_business:tabViewRepeat:tab_repeatConfig:zoneCbx_label");
    private static final By ADD_BUTTON_LOCATOR = By.id("repeatManagerForm:homepage_business:tabViewRepeat:tab_repeatConfig:_tblResults:addButton");
    private static final By DELETE_BUTTON_LOCATOR = By.id("repeatManagerForm:homepage_business:tabViewRepeat:tab_repeatConfig:_tblResults:deleteButton");
    private static final By CONFIGURATIONS_OPTION_BUTTON_LOCATOR = By.id("repeatManagerForm:homepage_business:tabViewRepeat:tab_repeatConfig:_tblResults:imgSelect");
    private static final By CONFIGURATIONS_SELECT_ALL_BUTTON_LOCATOR = By.xpath("//*[@id='repeatManagerForm:homepage_business:tabViewRepeat:tab_repeatConfig:_tblResults:overlaySelect']//span[.='Select all']");

    private final Controls controls;
    private final Wait wait;

    public ConfigurationManager() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    @Step("Check configuration enable checkbox")
    public void checkConfigurationEnableCheckbox(RemoteWebDriver driver, Repeat repeat) {
        System.out.println("Check configuration enable check box to be " + repeat.getConfigurationEnable());
        controls.setCheckboxValueById(driver, CONFIGURATION_ENABLE_FIELD_LOCATOR, repeat.getConfigurationEnable());
    }

    @Step("Select list name")
    public void selectListName(RemoteWebDriver driver, Repeat repeat) throws Exception {
        Allure.step("Set list name = " + repeat.getListName());
        controls.selectOptionByDisplayedText(driver, LIST_DROP_DOWN_LOCATOR, repeat.getListName());
    }

    @Step("Set retention time")
    public void setRetentionTime(RemoteWebDriver driver, Repeat repeat) throws Exception {
        Allure.step("Set retention time = " + repeat.getRetentionTime());
        controls.setTextBoxValue(driver, RETENTION_TIME_FIELD_LOCATOR, repeat.getRetentionTime());
    }

    @Step("Click Save Button")
    public void clickSaveButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SAVE_BUTTON_LOCATOR);
        driver.switchTo().alert().accept();
    }

    @Step("Select zone")
    public void selectZone(RemoteWebDriver driver, Repeat repeat) throws Exception {
        Allure.step("Set zone = " + repeat.getZone());
        controls.selectOptionByDisplayedText(driver, ZONE_FIELD_LOCATOR, repeat.getZone());
    }

    @Step("Click Add Button")
    public void clickAddButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, ADD_BUTTON_LOCATOR);
    }

    @Step("Click Delete Button")
    public void clickDeleteButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, DELETE_BUTTON_LOCATOR);
    }

    @Step("Click Configurations option button")
    public void clickConfigurationOptionButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, CONFIGURATIONS_OPTION_BUTTON_LOCATOR);

    }

    @Step("Click Configurations select all button")
    public void clickConfigurationSelectAllButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, CONFIGURATIONS_SELECT_ALL_BUTTON_LOCATOR);

    }

    @Step("Verify Repeat Config Exist")
    public boolean verifyRepeatConfigExist(RemoteWebDriver driver, String repeatConfigName) {
        wait.waitUntilAjaxLoaderDisappear(driver);
        return controls.exists(driver, By.xpath(String.format("//tbody//tr//td[.='%s']", repeatConfigName)));
    }
}