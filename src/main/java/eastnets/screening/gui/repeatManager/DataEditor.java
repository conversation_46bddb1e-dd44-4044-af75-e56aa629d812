package eastnets.screening.gui.repeatManager;

import core.util.Wait;
import eastnets.screening.entity.RepeatedData;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.util.ArrayList;
import java.util.List;

public class DataEditor {
    private static final By TABLE_ROWS_LOCATOR = By.xpath("//tbody[contains(@id,'repeatManagerForm:homepage_business:tabViewRepeat:tab_repeatData')]//tr");

    private final Wait wait;

    public DataEditor() {
        this.wait = new Wait();
    }

    @Step("Get Repeated Data From Result Table")
    public List<RepeatedData> getRepeatedDataFromResultTable(RemoteWebDriver driver) throws InterruptedException {

        wait.time(Wait.ONE_SECOND * 3);
        List<WebElement> tableRows = driver.findElements(TABLE_ROWS_LOCATOR);
        List<RepeatedData> repeatedDataResult = new ArrayList<>();
        RepeatedData repeatedData;
        for (WebElement row : tableRows) {
            wait.time(Wait.ONE_SECOND);
            repeatedData = new RepeatedData();
            List<WebElement> rowCells = row.findElements(By.tagName("td"));
            repeatedData = new RepeatedData();
            repeatedData.setZone(rowCells.get(2).getText());
            repeatedData.setType(rowCells.get(3).getText());
            repeatedData.setRemarks(rowCells.get(7).getText());

            Allure.step(repeatedData.toString());
            repeatedDataResult.add(repeatedData);

        }
        return repeatedDataResult;
    }
}
