package eastnets.screening.gui.repeatManager;

import core.gui.Controls;
import core.util.Wait;
import eastnets.screening.entity.Repeat;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class ConfigurationEditor {
    private static final By CONFIG_NAME_FIELD_LOCATOR = By.id("repeatManagerForm:homepage_business:tabViewRepeat:tab_repeatConfig:repeatConfigDetail:name");
    private static final By ZONE_DROP_DOWN_LOCATOR = By.id("repeatManagerForm:homepage_business:tabViewRepeat:tab_repeatConfig:repeatConfigDetail:zoneId");
    private static final By TYPE_DROP_DOWN_LOCATOR = By.id("repeatManagerForm:homepage_business:tabViewRepeat:tab_repeatConfig:repeatConfigDetail:type");
    private static final By SUB_TYPE_DROP_DOWN_LOCATOR = By.id("repeatManagerForm:homepage_business:tabViewRepeat:tab_repeatConfig:repeatConfigDetail:subType");
    private static final By RULE1_LOCATOR = By.id("repeatManagerForm:homepage_business:tabViewRepeat:tab_repeatConfig:repeatConfigDetail:rule1");
    private static final By RULE2_LOCATOR = By.id("repeatManagerForm:homepage_business:tabViewRepeat:tab_repeatConfig:repeatConfigDetail:rule2");
    private static final By RULE3_LOCATOR = By.id("repeatManagerForm:homepage_business:tabViewRepeat:tab_repeatConfig:repeatConfigDetail:rule3");
    private static final By RULE4_LOCATOR = By.id("repeatManagerForm:homepage_business:tabViewRepeat:tab_repeatConfig:repeatConfigDetail:rule4");
    private static final By RULE5_LOCATOR = By.id("repeatManagerForm:homepage_business:tabViewRepeat:tab_repeatConfig:repeatConfigDetail:rule5");
    private static final By SAVE_BUTTON_LOCATOR = By.id("repeatManagerForm:homepage_business:tabViewRepeat:tab_repeatConfig:repeatConfigDetail:saveButton");
    private static final By CANCEL_BUTTON_LOCATOR = By.id("repeatManagerForm:homepage_business:tabViewRepeat:tab_repeatConfig:repeatConfigDetail:cancelButton");

    private final Controls controls;
    private final Wait wait;

    public ConfigurationEditor() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    @Step("Set Configuration Name")
    public void setConfigName(RemoteWebDriver driver, Repeat repeat) {
        Allure.step("Set configuration name = " + repeat.getConfigName());
        wait.waitUntilAjaxLoaderDisappear(driver);
        controls.setTextBoxValue(driver, CONFIG_NAME_FIELD_LOCATOR, repeat.getConfigName());
    }

    @Step("Set Zone")
    public void setZone(RemoteWebDriver driver, Repeat repeat) throws Exception {
        Allure.step("Set Zone = " + repeat.getZone());
        controls.selectOptionByDisplayedText(driver, ZONE_DROP_DOWN_LOCATOR, repeat.getZone());
    }

    @Step("Set Type")
    public void setType(RemoteWebDriver driver, Repeat repeat) throws Exception {
        Allure.step("Set Type = " + repeat.getType());
        controls.selectOptionByDisplayedText(driver, TYPE_DROP_DOWN_LOCATOR, repeat.getType());
    }

    @Step("Set Sub Type")
    public void setSubType(RemoteWebDriver driver, Repeat repeat) throws Exception {
        Allure.step("Set Sub Type = " + repeat.getSubType());
        if (controls.checkIfElementExist(driver, SUB_TYPE_DROP_DOWN_LOCATOR)) {
            controls.selectOptionByDisplayedText(driver, SUB_TYPE_DROP_DOWN_LOCATOR, repeat.getSubType());
        }
    }

    @Step("Set Rule 1")
    public void setRule1(RemoteWebDriver driver, Repeat repeat) {
        Allure.step("Set Rule 1 = " + repeat.getRule1());
        controls.setTextBoxValue(driver, RULE1_LOCATOR, repeat.getRule1());
    }

    @Step("Set Rule 2")
    public void setRule2(RemoteWebDriver driver, Repeat repeat) {
        Allure.step("Set Rule 2 = " + repeat.getRule2());
        controls.setTextBoxValue(driver, RULE2_LOCATOR, repeat.getRule2());
    }

    @Step("Set Rule 3")
    public void setRule3(RemoteWebDriver driver, Repeat repeat) {
        Allure.step("Set Rule 3 = " + repeat.getRule3());
        controls.setTextBoxValue(driver, RULE3_LOCATOR, repeat.getRule3());
    }

    @Step("Set Rule 4")
    public void setRule4(RemoteWebDriver driver, Repeat repeat) {
        Allure.step("Set Rule 4 = " + repeat.getRule4());
        controls.setTextBoxValue(driver, RULE4_LOCATOR, repeat.getRule4());
    }

    @Step("Set Rule 5")
    public void setRule5(RemoteWebDriver driver, Repeat repeat) {
        Allure.step("Set Rule 5 = " + repeat.getRule5());
        controls.setTextBoxValue(driver, RULE5_LOCATOR, repeat.getRule5());
    }

    @Step("Click Save Button")
    public void clickSaveButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SAVE_BUTTON_LOCATOR);
        driver.switchTo().alert().accept();
        wait.waitUntilAjaxLoaderDisappear(driver);
    }


    @Step("Click Cancel Button")
    public void clickCancelButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, CANCEL_BUTTON_LOCATOR);
    }
}