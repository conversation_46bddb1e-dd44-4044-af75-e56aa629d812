package eastnets.screening.gui.repeatManager;

import core.gui.Controls;
import core.util.Wait;
import eastnets.common.gui.Navigation;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class DataManager {
    private static final By ZONE_FIELD_LOCATOR = By.id("repeatManagerForm:homepage_business:tabViewRepeat:tab_repeatData:Zone_input");
    private static final By CONFIG_NAME_FIELD_LOCATOR = By.id("repeatManagerForm:homepage_business:tabViewRepeat:tab_repeatData:ConfigName_input");
    private static final By SEARCH_BUTTON_LOCATOR = By.id("repeatManagerForm:homepage_business:tabViewRepeat:tab_repeatData:_btnSearch");
    private static final By RESET_BUTTON_LOCATOR = By.id("repeatManagerForm:homepage_business:tabViewRepeat:tab_repeatData:_btnReset");
    private static final By VIEW_BUTTON_LOCATOR = By.id("repeatManagerForm:homepage_business:tabViewRepeat:tab_repeatData:_tblResults:_btnOpen");
    private static final By DELETE_BUTTON_LOCATOR = By.id("repeatManagerForm:homepage_business:tabViewRepeat:tab_repeatData:_tblResults:_btnDelete");

    private final Controls controls;
    private final Wait wait;
    private final RepeatManagerNavigation repeatManagerNavigation;

    public DataManager() {
        this.controls = new Controls();
        this.wait = new Wait();
        this.repeatManagerNavigation = RepeatManagerNavigation.Data;
    }

    @Step("Navigate to 'Data' tab")
    public void navigateToDataTab(RemoteWebDriver driver) {
        Navigation.REPEAT_MANAGER.navigate(driver);
        repeatManagerNavigation.navigate(driver);
    }

    @Step("Click Search Button")
    public void clickSearchButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SEARCH_BUTTON_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Click Reset Button")
    public void clickResetButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, RESET_BUTTON_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Search by zone and config name")
    public void searchByZoneAndConfigName(RemoteWebDriver driver, String zone, String configName) throws Exception {
        navigateToDataTab(driver);
        clickResetButton(driver);
        controls.selectOptionByDisplayedText(driver, ZONE_FIELD_LOCATOR, zone);
        controls.setTextBoxValue(driver, CONFIG_NAME_FIELD_LOCATOR, configName);
        clickSearchButton(driver);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Click View Button")
    public void clickViewButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, VIEW_BUTTON_LOCATOR);
    }

    @Step("Click Delete Button")
    public void clickDeleteButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, DELETE_BUTTON_LOCATOR);
    }

}