package eastnets.screening.gui.reports;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class ReportEditor {

    private static final By COMMENT_INPUT_LOCATOR = By.xpath("//*[text()='Comment:']//following::textarea");
    private static final By FORMAT_SELECT_LOCATOR = By.id("reportViewerForm:viewer_business:reportType");
    private static final By ZONE_INPUT_LOCATOR = By.xpath("//*[text()='Zone:']//ancestor::tr[1]//input");
    private static final By ZONE_SELECT_LOCATOR = By.xpath("//*[text() = 'zone' or text() = 'Zone:']//ancestor::tr[1]/td/div");
    private static final By INVESTIGATOR_SELECT_LOCATOR = By.xpath("//*[text()='Investigator:']//ancestor::tr[1]/td/div");
    private static final By LIST_SET_SELECT_LOCATOR = By.xpath("//*[contains(text() , 'List Set')]//ancestor::tr[1]/td/div[@role='combobox']");
    private static final By BLACK_LIST_NAME__SELECT_LOCATOR = By.xpath("//*[text() = 'BlackListName' or text() = 'Black List']//ancestor::tr[1]/td/div");
    private static final By BLACK_LIST_VERSION__SELECT_LOCATOR = By.xpath("//*[text() = 'BlackListVersion']//ancestor::tr[1]/td/div");
    private static final By BLACK_LIST_VERSION1_SELECT_LOCATOR = By.xpath("//*[text() = 'BlackListVersion1']//ancestor::tr[1]/td/div");
    private static final By BLACK_LIST_VERSION2_SELECT_LOCATOR = By.xpath("//*[text() = 'BlackListVersion2']//ancestor::tr[1]/td/div");
    private static final By EXECUTE_BTN_LOCATOR = By.id("reportViewerForm:viewer_business:btnExecute");
    private static final By LAST_PAGE_BUTTON_LOCATOR = By.xpath("//*[text() ='>>']//ancestor::button");
    private static final By PREVIOUS_PAGE_BUTTON_LOCATOR = By.xpath("//*[text() ='<']//ancestor::button");
    private static final By FIND_SESSION_BTN_LOCATOR = By.xpath("//*[text() = 'Find Session']");
    private static final By SEARCH_SESSION_BTN_LOCATOR = By.id("scanSessionForm:btnSearch");
    private static final String SESSION_CHECKBOX_LOCATOR = "//td[text() ='%s']//ancestor::tr[1]//div[contains(@class,'ui-selectbooleancheckbox')]";
    private static final By SELECT_BTN_LOCATOR = By.id("scanSessionForm:ScanSessionsTable:btnSelect");
    private static final By START_DATE_TEXT_LOCATOR = By.id("reportViewerForm:viewer_business:_tblResults:0:dateTimeInput_input");
    private static final By END_DATE_TEXT_LOCATOR = By.id("reportViewerForm:viewer_business:_tblResults:1:dateTimeInput_input");

    private final Controls controls;
    private final Wait wait;

    public ReportEditor() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    @Step("Enter comment")
    public void enterComment(RemoteWebDriver driver, String comment) {
        controls.setTextBoxValue(driver, COMMENT_INPUT_LOCATOR, comment);
    }

    @Step("Select format")
    public void selectFormat(RemoteWebDriver driver, String format) throws Exception {
        controls.selectOptionByDisplayedText(driver, FORMAT_SELECT_LOCATOR, format);
    }

    @Step("Enter zone")
    public void enterZone(RemoteWebDriver driver, String zone) {
        controls.setTextBoxValue(driver, ZONE_INPUT_LOCATOR, zone);
    }

    @Step("Select zone")
    public void selectZone(RemoteWebDriver driver, String zone) throws Exception {
        controls.selectOptionByDisplayedText(driver, ZONE_SELECT_LOCATOR, zone);
    }

    @Step("Select investigator")
    public void selectInvestigator(RemoteWebDriver driver, String investigator) throws Exception {
        controls.selectOptionByDisplayedText(driver, INVESTIGATOR_SELECT_LOCATOR, investigator);
    }

    @Step("Select ListSet")
    public void selectListSet(RemoteWebDriver driver, String listSet) throws Exception {
        controls.selectOptionByDisplayedText(driver, LIST_SET_SELECT_LOCATOR, listSet);
    }

    @Step("Select BlackList Name")
    public void selectBlackListName(RemoteWebDriver driver, String blackListName) throws Exception {
        controls.selectOptionByDisplayedText(driver, BLACK_LIST_NAME__SELECT_LOCATOR, blackListName);
    }

    @Step("Select BlackList Version")
    public void selectBlackListVersion(RemoteWebDriver driver, String blackListVersion) throws Exception {
        controls.selectOptionByIndex(driver, BLACK_LIST_VERSION__SELECT_LOCATOR, blackListVersion);
    }

    @Step("Select BlackList Version1")
    public void selectBlackListVersion1(RemoteWebDriver driver, String blackListVersion1) throws Exception {
        controls.selectOptionByPartialDisplayedText(driver, BLACK_LIST_VERSION1_SELECT_LOCATOR, blackListVersion1);
    }

    @Step("Select BlackList Version2")
    public void selectBlackListVersion2(RemoteWebDriver driver, String blackListVersion2) throws Exception {
        controls.selectOptionByPartialDisplayedText(driver, BLACK_LIST_VERSION2_SELECT_LOCATOR, blackListVersion2);
    }

    @Step("Click execute")
    public void clickExecute(RemoteWebDriver driver) {
        controls.performClick(driver, EXECUTE_BTN_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Enter Session1 Id")
    public void enterSessionID(RemoteWebDriver driver, int sessionNumber, String sessionId) {

        driver.findElements(FIND_SESSION_BTN_LOCATOR).get(sessionNumber).click();
        controls.performClickByJS(driver, SEARCH_SESSION_BTN_LOCATOR);
        if (controls.getWebElement(driver, LAST_PAGE_BUTTON_LOCATOR).getAttribute("aria-disabled").contains("false"))
            controls.performClick(driver, LAST_PAGE_BUTTON_LOCATOR);
        if (!driver.findElements(By.xpath(String.format(SESSION_CHECKBOX_LOCATOR, sessionId))).isEmpty())
            controls.performClick(driver, By.xpath(String.format(SESSION_CHECKBOX_LOCATOR, sessionId)));
        else
        {
            controls.performClick(driver, PREVIOUS_PAGE_BUTTON_LOCATOR);
            controls.performClick(driver, By.xpath(String.format(SESSION_CHECKBOX_LOCATOR, sessionId)));
        }
        controls.performClick(driver, SELECT_BTN_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }


    @Step("Set Start Date")
    public void setStartData(RemoteWebDriver driver, String startDate) {
        controls.setTextBoxValueByJS(driver, START_DATE_TEXT_LOCATOR, startDate);
    }

    @Step("Set End Date")
    public void setEndData(RemoteWebDriver driver, String endDate) {
        controls.setTextBoxValueByJS(driver, END_DATE_TEXT_LOCATOR, endDate);
    }

}
