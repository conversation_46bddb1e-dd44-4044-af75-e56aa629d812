package eastnets.screening.gui.reports;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class ReportManager {
    private static final By NAME_SLCT = By.id("reportForm:homepage_business:reportListCbx_label");
    private static final By COMMENT_TXT = By.id("reportForm:homepage_business:descField");
    private static final By REPORT_FORMAT_SLCT = By.id("reportForm:homepage_business:reportFormatCbx_label");
    private static final By GENERATED_BY_TXT = By.id("reportForm:homepage_business:reportNameField");
    private static final By STATUS_SLCT = By.id("reportForm:homepage_business:reportNameField");
    private static final By FROM_DATE_SLCT = By.id("reportForm:homepage_business:startDate_input");
    private static final By TO_DATE_SLCT = By.id("reportForm:homepage_business:endDate_input");
    private static final By NEW_BTN = By.id("reportForm:homepage_business:_tblResults:btnShow");
    private static final By SEARCH_BTN = By.id("reportForm:homepage_business:btnSearch");
    private static final By RESET_BTN = By.id("reportForm:homepage_business:btnReset");
    private static final By DELETE_BTN = By.id("reportForm:homepage_business:_tblResults:_btnDeleteGroup");
    private static final By REPORT_LIST_SLCT = By.id("reportForm:homepage_business:_tblResults:reportListSelectCbx");
    private static final By REPORT_STATUS_TD_LOCATOR = By.xpath("//*[@id = 'reportForm:homepage_business:_tblResults_data']//tr[1]//td[8]");
    private static final By REPORT_NAME_TD_LOCATOR = By.id("reportForm:homepage_business:_tblResults:0:linkEdit");

    private final Controls controls;
    private final Wait wait;

    public ReportManager() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    @Step("Click search")
    public void clickSearch(RemoteWebDriver driver) {
        controls.performClick(driver, SEARCH_BTN);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Click reset")
    public void clickReset(RemoteWebDriver driver) {
       /* controls.performClickByJS(driver, RESET_BTN);
        driver.switchTo().alert().accept();
        wait.waitUntilAjaxLoaderDisappear(driver);*/
    }

    @Step("Click delete")
    public void clickDelete(RemoteWebDriver driver) {
        controls.performClick(driver, DELETE_BTN);
    }

    @Step("Click new")
    public void clickNew(RemoteWebDriver driver) {
        controls.performClick(driver, NEW_BTN);
    }

    @Step("Select name")
    public void selectName(RemoteWebDriver driver, String name) throws Exception {
        controls.selectOptionByDisplayedText(driver, NAME_SLCT, name);
    }

    @Step("Select Report Format")
    public void selectReportFormat(RemoteWebDriver driver, String reportFormat) throws Exception {
        controls.selectOptionByDisplayedText(driver, REPORT_FORMAT_SLCT, reportFormat);
    }

    @Step("Select status")
    public void selectStatus(RemoteWebDriver driver, String status) throws Exception {
        controls.selectOptionByDisplayedText(driver, STATUS_SLCT, status);
    }

    @Step("Enter comment")
    public void enterComment(RemoteWebDriver driver, String comment) {
        controls.setTextBoxValue(driver, COMMENT_TXT, comment);
    }

    @Step("Enter generated by")
    public void enterGeneratedBy(RemoteWebDriver driver, String generatedBy) {
        controls.setTextBoxValue(driver, GENERATED_BY_TXT, generatedBy);
    }

    @Step("Select from date")
    public void selectFromDate(RemoteWebDriver driver, String fromDate) {
        controls.setTextBoxValue(driver, FROM_DATE_SLCT, fromDate);
    }

    @Step("Select from report list")
    public void selectReportList(RemoteWebDriver driver, String reportName) throws Exception {
        controls.selectOptionByDisplayedText(driver, REPORT_LIST_SLCT, reportName);
    }

    @Step("Get report status")
    public String getReportStatus(RemoteWebDriver driver) {
        Allure.step("Report status is: " + controls.getElementText(driver, REPORT_STATUS_TD_LOCATOR));
        return controls.getElementText(driver, REPORT_STATUS_TD_LOCATOR);
    }

    @Step("Click report name")
    public void clickReportName(RemoteWebDriver driver) {
        controls.performClick(driver, REPORT_NAME_TD_LOCATOR);
    }


}