package eastnets.common.control;

import core.constants.screening.GeneralConstants;
import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.time.Duration;
import java.util.List;

public class CommonAction {
    private static final int WAIT_COEFFICIENT = 9;

    private final Controls controls;
    private final Wait wait;

    public CommonAction() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    @Step("Start Search")
    public void startSearch(RemoteWebDriver driver, By searchButton) throws InterruptedException {
        controls.performClickByJS(driver, searchButton);
        wait.time(Wait.ONE_SECOND / WAIT_COEFFICIENT);
    }

    @Step("Reset Search")
    public void resetSearch(RemoteWebDriver driver, By resetButton) throws InterruptedException {
        controls.performClickByJS(driver, resetButton);
        driver.switchTo().alert().accept();
        wait.time(Wait.ONE_SECOND * 5);
    }

    @Step("Is User Logged")
    public boolean isUserLogged(RemoteWebDriver driver) {
        try {
            By loginButton = By.cssSelector(".fa-street-view");
            return controls.exists(driver, loginButton);
        } catch (Exception e) {
            return false;
        }
    }

    @Step("Logout")
    public void logout(RemoteWebDriver driver) {
        driver.navigate().refresh();
        wait.elementVisible(driver, By.xpath("//*[@title='Exit']"), Duration.ofSeconds(5));
        controls.performClickByJS(driver, By.xpath("//*[@title='Exit' or @title ='Quitter']"));
        controls.performClickByJS(driver, By.xpath("//button[.='Yes']"));
        wait.elementVisible(driver, By.id("pageLoginForm:login_business:UserName"), Duration.ofSeconds(3));

    }

    @Step("Wait Page Visible")
    public void waitPageVisible(RemoteWebDriver driver, By validator) {
        wait.elementVisible(driver, validator, Duration.ofSeconds(2));
    }

    private static final By ALERT_MESSAGE_LOCATOR = By.xpath("//*[@class='ui-growl-title']");

    @Step("Get Alert Message String")
    public String getAlertMessageString(RemoteWebDriver driver) throws InterruptedException {
        wait.waitUntilAjaxLoaderDisappear(driver);
        if (driver.findElements(ALERT_MESSAGE_LOCATOR).size() == 0)
            return null;

        String validationMessage = controls.getLabelValue(driver, ALERT_MESSAGE_LOCATOR);
        Allure.step("Validation message = " + validationMessage);
        return validationMessage;
    }

    private static final By LEGAL_NOTICE_MESSAGE_LOCATOR = By.xpath("//*[@class='ui-growl-message']/p");

    @Step("Get Alert Message String")
    public String getLegalNoticeMessageString(RemoteWebDriver driver) throws InterruptedException {
        wait.waitUntilAjaxLoaderDisappear(driver);
        if (driver.findElements(LEGAL_NOTICE_MESSAGE_LOCATOR).size() == 0)
            return null;

        return controls.getLabelValue(driver, LEGAL_NOTICE_MESSAGE_LOCATOR);
    }

    @Step("Get All Alert Messages String")
    public String getAllAlertMessageString(RemoteWebDriver driver) {
        wait.waitUntilAjaxLoaderDisappear(driver);
        String message = "";
        List<WebElement> massages = driver.findElements(ALERT_MESSAGE_LOCATOR);
        if (massages.size() != 0) {
            message += massages.get(0).getText();
            for (int i = 1; i < massages.size(); i++) {
                message += " , " + massages.get(i).getText();
            }
            return message;
        }

        return "";
    }

    @Step("Check Existing Alert Message")
    public boolean checkExistingAlertMessage(RemoteWebDriver driver) {

        return controls.exists(driver, ALERT_MESSAGE_LOCATOR);
    }

    @Step("Get Alert Message String Without Wait For Ajax Loader")
    public String getAlertMessageStringWithoutWaitForAjaxLoader(RemoteWebDriver driver) throws InterruptedException {
        wait.time(Wait.ONE_SECOND * 2);
        if (driver.findElements(ALERT_MESSAGE_LOCATOR).size() == 0)
            return GeneralConstants.FAILED;
        return controls.getLabelValue(driver, ALERT_MESSAGE_LOCATOR);
    }
}