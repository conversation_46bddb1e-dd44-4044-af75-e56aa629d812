package eastnets.common.control;

import core.database.DatabaseDriver;
import core.exception.AlreadyExistsException;
import core.util.Log;
import eastnets.admin.control.GroupControl;
import eastnets.admin.control.OperatorControl;
import eastnets.admin.control.ProfileControl;
import eastnets.admin.control.ZoneControl;
import eastnets.admin.entity.*;
import eastnets.common.gui.LoginPage;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.sql.Connection;
import java.sql.SQLException;

public class CommonOperatorControl {
    private static final Logger LOG = LoggerFactory.getLogger(CommonOperatorControl.class);

    private final CommonAction commonAction;
    private final LoginPage loginPage;
    private final ZoneControl zoneControl;
    private final GroupControl groupControl;
    private final OperatorControl operatorControl;
    private final ProfileControl profileControl;
    private final Log log;
    private final DatabaseDriver databaseDriver;

    public CommonOperatorControl() {
        this.commonAction = new CommonAction();
        this.loginPage = new LoginPage();
        this.zoneControl = new ZoneControl();
        this.groupControl = new GroupControl();
        this.operatorControl = new OperatorControl();
        this.profileControl = new ProfileControl();
        this.log = new Log();
        this.databaseDriver = new DatabaseDriver();
    }

    @Step("Login as sysadmin if not logged in")
    private void loginAsSysadminIfNotLogged(RemoteWebDriver driver) throws SQLException, InterruptedException {
        Allure.step("Login as admin if not logged in as 'sysadmin'");
        Allure.step("Check if user logged in as 'sysadmin'");
        if (!commonAction.isUserLogged(driver)) {
            Allure.step("Logging in as 'sysadmin'");
            loginPage.login(driver, LoginPage.SYSADMIN_LOGIN, LoginPage.SYSADMIN_PASSWORD);
        }
        Allure.step("User already logged in as 'sysadmin'");
    }

    @Step("Create Common Zone If Not Exists")
    private void createZoneIfNotExists(RemoteWebDriver driver, Connection connection, Zone zone) throws SQLException, AlreadyExistsException, InterruptedException, IOException {
        Allure.step("Create zone if not exist.");
        Allure.step("Check is zone exist in DB.");
        if (databaseDriver.getCountResult(connection, Zone.COUNT_ON_NAME, zone.getName()) > 0) {
            Allure.step("Zone exist in database.");
            return;
        }
        Allure.step("Zone not exist in database.");
        Allure.step("Start creating zone.");
        loginAsSysadminIfNotLogged(driver);
        Allure.step(String.format("Creating zone '%s'", zone.getDisplayName()));
        zoneControl.createZone(driver, zone);
    }

    @Step("Create Group ")
    private void createGroup(RemoteWebDriver driver, Connection connection, Group group) throws Exception {
        int numberOfRowsInDB = databaseDriver.getCountResult(connection, Group.COUNT_ON_NAME, group.getName());
        if (numberOfRowsInDB > 0) {
            Allure.step("GroupOperator already exist in DB.");
            if (group.isSystemGroup()) {
                for (Operator operator : group.getGroupMembers()) {
                    driver.navigate().refresh();
                    loginAsSysadminIfNotLogged(driver);
                    Allure.step(String.format("Updating group '%s'", group.getName()));
                    groupControl.add_operator_to_group(driver, group);
                }
            }
            return;
        }
        loginAsSysadminIfNotLogged(driver);
        Allure.step(String.format("Creating group '%s'", group.getName()));
        groupControl.createGroup(driver, group);
    }

    @Step("Create Operator If Not Exists")
    private void createOperatorIfNotExists(RemoteWebDriver driver, Connection connection, Operator operator) throws Exception {
        Allure.step(String.format("Check if operator with name %s already exist in DB or no.", operator.getLoginName()));
        int numberOfRowsInDB = databaseDriver.getCountResult(connection, Operator.COUNT_ON_LOGIN_NAME, operator.getLoginName());
        Allure.step(String.format("Number of rows related to operator %s in DB that found = %s", operator.getLoginName(), numberOfRowsInDB));
        if (numberOfRowsInDB > 0) {
            Allure.step("Operator already exist in DB and not deleted.");
            return;
        }
        loginAsSysadminIfNotLogged(driver);
        Allure.step(String.format("Creating operator '%s'", operator.getLoginName()));
        operatorControl.createOperator(driver, operator);
    }

    @Step("Create Profile If Not Exists")
    private void createProfileIfNotExists(RemoteWebDriver driver, Connection connection, Profile profile) throws Exception {
        Allure.step(String.format("Check if profile with name %s already exist in DB or no.", profile.getName()));
        int numberOfRowsInDB = databaseDriver.getCountResult(connection, Profile.COUNT_ON_NAME, profile.getName());
        Allure.step(String.format("Number of rows related to profile %s in DB that found = %s", profile.getName(), numberOfRowsInDB));
        if (numberOfRowsInDB > 0) {
            log.info("Profile already exist in DB and not deleted.");
            return;
        }
        if (profile.isClone())
        {
            loginAsSysadminIfNotLogged(driver);
            Allure.step(String.format("Cloning profile '%s'", profile.getName()));
            profileControl.cloneProfile(driver, profile);
        }
        else {
        loginAsSysadminIfNotLogged(driver);
        Allure.step(String.format("Creating profile '%s'", profile.getName()));
        profileControl.createProfile(driver, profile);
        }
    }

    @Step("Prepare Operator Environment")
    public void prepareOperatorEnvironment(RemoteWebDriver driver, Connection connection) throws Exception {
        Common.GROUP[] groups = Common.GROUP.values();
        for (Common.GROUP group : groups) {
            Group groupDetails = group.get();
            Allure.step(String.format("Operator Data --> %s", group.get().toString()));
            createZoneIfNotExists(driver, connection, groupDetails.getProfile().getZone());
            for (Operator groupMember : groupDetails.getGroupMembers()) {
                Allure.step(String.format("Start add %s with all Details.", groupMember.getLoginName()));
                createOperatorIfNotExists(driver, connection, groupMember);
            }
            createProfileIfNotExists(driver, connection, groupDetails.getProfile());
            createGroup(driver, connection, groupDetails);
            Allure.step("Common investigators are created with their zone, group and profile");
        }

    }
}
