package eastnets.common.control;

import core.util.DirectoryUtil;
import core.util.Property;
import io.qameta.allure.Step;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;

public class ScreeningPropertiesChecker {

    private final Property property;
    private final DirectoryUtil directoryUtil;

    public ScreeningPropertiesChecker() {
        this.property = new Property();
        this.directoryUtil = new DirectoryUtil();
    }

    @Step("Check test version")
    public boolean checkTestVersion(Connection connection) throws SQLException {
        String[] version = property.fromFile(DirectoryUtil.TEST_PROPERTY_FILE)
                .getProperty("test.version")
                .split("\\.");

        Statement statement = connection.createStatement();
        try (ResultSet rs = statement.executeQuery("SELECT major, minor, patch FROM tVersion WHERE install_date = (SELECT MAX(install_date) FROM tVersion)")) {
            if (!rs.next()) {
                System.out.println("Empty result set");
                return false;
            }
            return Integer.parseInt(version[0]) == rs.getInt("major")
                    && Integer.parseInt(version[1]) == rs.getInt("minor")
                    && Integer.parseInt(version[2]) == rs.getInt("patch");
        }
    }

    @Step("Get Env Path")
    private String getEnvPath(String key) {
        return System.getenv(key);
    }

    @Step("Check If Environment Variable Exist")
    public boolean checkIfEnvironmentVariableExist(String key) {
        String path = getEnvPath(key);
        return path != null && Files.exists(Paths.get(path));
    }

    @Step("Verify Aml Folder Content")
    private boolean verifyAmlFolderContent(File amlFolder) {
        String[] amlFolderContent = {
                "cayenne.xml",
                "en-config.xml",
                "en-domain-node.user.sxml",
                "en-license.sxml",
                "SasDomainNode.driver.xml",
                "SfpDomainNode.driver.xml",
                "images",
                "tmp"
        };

        for (String fileToCheck : amlFolderContent) {
            if (!directoryUtil.verifyFolderContains(amlFolder, fileToCheck))
                return false;
        }

        return true;
    }

    @Step("Check East Nets Config Home Content")
    public boolean checkEastNetsConfigHomeContent(String key) {
        String envPath = getEnvPath(key);
        File amlFolder = new File(envPath, "aml");
        if (!amlFolder.exists()) return false;
        return verifyAmlFolderContent(amlFolder);
    }

    @Step("Verify Scdb External Tool Paths")
    public boolean verifyScdbExternalToolPaths(Connection connection) throws SQLException {
        Statement statement = connection.createStatement();
        try (ResultSet rs = statement.executeQuery("SELECT name, filename FROM tExternalTool")) {
            while (rs.next()) {
                String filename = rs.getString("filename");
                if (!new File(filename).exists() && (filename.toLowerCase().contains(".jar") || filename.toLowerCase().contains(".exe")))
                    return false;
            }
        }
        return true;
    }
}