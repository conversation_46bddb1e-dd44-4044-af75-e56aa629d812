package eastnets.common.gui;

import core.gui.Controls;
import core.util.Log;
import core.util.Wait;
import eastnets.admin.entity.Operator;
import eastnets.common.control.Application;
import eastnets.common.control.CommonAction;
import eastnets.screening.backendServices.ScreeningServicesDelegate;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.sql.SQLException;
import java.time.Duration;

public class LoginPage {
    public static final String SYSADMIN_LOGIN = "sysadmin";
    public static final String SYSADMIN_PASSWORD = "manager";

    private static final By USERNAME_INPUT_ID = By.id("pageLoginForm:login_business:UserName");
    private static final By PASSWORD_INPUT_ID = By.id("pageLoginForm:login_business:Password");
    private static final By LOGIN_BUTTON_ID = By.id("pageLoginForm:login_business:goButton");
    private static final By BACK_TO_LOGIN_LOCATOR = By.xpath("//span[contains(text(),'Back to login')]");
    private static final By USER_PROFILE_ICON_LOCATOR = By.cssSelector(".fa-street-view");
    private static final By SWITCH_TO_ADMIN_LOCATOR = By.linkText("Switch to Admin");
    private static final By ABOUT_YOUR_SESSION_LINK_LOCATOR = By.linkText("About Your Session");
    private static final By CHANGE_PASSWORD_LOCATOR = By.id("pageLoginForm:login_business:changePasswordId");
    private static final By CHANGE_PASSWORD_USER_NAME_LOCATOR = By.id("pageChangePasswordForm:change_password_business:userName");
    private static final By OLD_PASSWORD_LOCATOR = By.id("pageChangePasswordForm:change_password_business:oldPassword");
    private static final By NEW_PASSWORD_LOCATOR = By.id("pageChangePasswordForm:change_password_business:newPassword");
    private static final By CONFIRM_NEW_PASSWORD_LOCATOR = By.id("pageChangePasswordForm:change_password_business:confirmNewPassword");
    private static final By OK_BUTTON_LOCATOR = By.id("pageChangePasswordForm:change_password_business:okButton");
    private static final By CLOSE_BUTTON_LOCATOR = By.xpath("//*[@id='topbar-right:aboutDlg']//button[.='Close']");
    private static final By LOGIN_VALIDATION_ICON = By.xpath("//label[.='Scan Manager']");
    private static final By LOGGED_USER_NAME_LOCATOR = By.xpath("//label[.='User name:']//following::input[1]");

    private static final By SAA_LOGIN_OPTION_XPATH = By.xpath("//div[@class='auth_OL']");
    private static final By SAA_USERNAME_INPUT_ID = By.id("gwt-debug-platform_login-username");
    private static final By SAA_PASSWORD_INPUT_ID = By.id("gwt-debug-platform_login-password");
    private static final By SAA_LOGIN_INSTANCE_LIST_XPATH = By.xpath("//input[@class='auth_ACC']");
    private static final By SAA_MULTIPLE_SESSION_OK_BUTTON_ID = By.id("gwt-debug-dialog-ask-0-ok");
    private static final By SAA_SWIFT_LOGO_XPATH = By.xpath("//div[@class='swp_LP']");

    private static final By SAA_LOGIN_BUTTON_ID = By.id("gwt-debug-platform_login-logon");

    private final Controls controls;
    private final Log log;
    private final Wait wait;
    private final CommonAction commonAction;
    private final ScreeningServicesDelegate screeningServicesDelegate;

    public LoginPage() {
        this.controls = new Controls();
        this.log = new Log();
        this.wait = new Wait();
        this.commonAction = new CommonAction();
        this.screeningServicesDelegate = new ScreeningServicesDelegate();
    }

    @Step("Click Back To Login button")
    public boolean clickBackToLoginButton(RemoteWebDriver driver) {
        driver.navigate().refresh();
        if (controls.exists(driver, BACK_TO_LOGIN_LOCATOR)) {
            controls.performClick(driver, BACK_TO_LOGIN_LOCATOR);
            return true;
        }
        return false;
    }

    @Step("Login")
    public boolean login(RemoteWebDriver driver, String username, String password, Application intentedApplicationLogin) throws InterruptedException {
        log.info(String.format("Login with User Name = %s and Password = %s", username, password));
        try {
            clickBackToLoginButton(driver);
            driver.get(driver.getCurrentUrl());
            controls.setTextBoxValue(driver, USERNAME_INPUT_ID, username);
            controls.setTextBoxValue(driver, PASSWORD_INPUT_ID, password);
            controls.performClickByJS(driver, LOGIN_BUTTON_ID);
        } catch (Exception e) {
            if (commonAction.isUserLogged(driver))
                commonAction.logout(driver);
            login(driver, username, password, intentedApplicationLogin);
            return true;
        }


        // Assert login of login
        return commonAction.isUserLogged(driver);

    }

    @Step("Login in the system")
    public String login(RemoteWebDriver driver, String username, String password) throws InterruptedException, SQLException {
        clickBackToLoginButton(driver);
        screeningServicesDelegate.unlockOperator(username);
        if (commonAction.isUserLogged(driver)) {
            commonAction.logout(driver);
        }
        Allure.step(String.format("Login with User Name = %s and Password = %s", username, password));
        controls.clearTextBoxValue(driver, USERNAME_INPUT_ID);
        controls.clearTextBoxValue(driver, PASSWORD_INPUT_ID);
        controls.setTextBoxValue(driver, USERNAME_INPUT_ID, username);
        controls.setTextBoxValue(driver, PASSWORD_INPUT_ID, password);
        controls.performClickByJS(driver, LOGIN_BUTTON_ID);
        //Wait.waitUntilElementToBeVisible(driver, LOGIN_VALIDATION_ICON, Duration.ofSeconds(10));
        // Assert login of login
        return commonAction.getAlertMessageStringWithoutWaitForAjaxLoader(driver);
    }

    @Step("Login in the system")
    public boolean login(RemoteWebDriver driver, String username, String password, String instance) throws Exception {
        Allure.step(String.format("Login with User Name = %s and Password = %s using instance = %s", username, password, instance));
        controls.performJsClick(driver, driver.findElement(SAA_LOGIN_OPTION_XPATH));
        //driver.findElement(SAA_LOGIN_OPTION_XPATH).click();
        controls.clearTextBoxValue(driver, SAA_USERNAME_INPUT_ID);
        controls.setTextBoxValue(driver, SAA_USERNAME_INPUT_ID, username);
        controls.clearTextBoxValue(driver, SAA_PASSWORD_INPUT_ID);
        controls.setTextBoxValue(driver, SAA_PASSWORD_INPUT_ID, password);
        controls.setOptionInDropDownTable(driver, SAA_LOGIN_INSTANCE_LIST_XPATH, instance);
        controls.performClick(driver, SAA_LOGIN_BUTTON_ID);
        if (controls.checkIfElementExist(driver, SAA_MULTIPLE_SESSION_OK_BUTTON_ID)) {
            controls.performClick(driver, SAA_MULTIPLE_SESSION_OK_BUTTON_ID);
        }
        Allure.step("Logged in Successfully");
        // Assert login of login
        return controls.checkIfElementExist(driver, SAA_SWIFT_LOGO_XPATH);
    }

    @Step("Switch to admin")
    public void switchToAdmin(RemoteWebDriver driver) throws InterruptedException {
        wait.waitForJStoLoad(driver);
        controls.waitAndClick(driver, USER_PROFILE_ICON_LOCATOR, Duration.ofSeconds(0));
        wait.time(Wait.ONE_SECOND * 3);
        controls.waitAndClick(driver, SWITCH_TO_ADMIN_LOCATOR, Duration.ofSeconds(0));
    }

    @Step("Click Change Password Link Text")
    public void clickChangePasswordLinkText(RemoteWebDriver driver) {
        controls.performClickByJS(driver, CHANGE_PASSWORD_LOCATOR);
    }

    @Step("Change Password")
    public String changePassword(RemoteWebDriver driver, Operator operator, String newPassword) throws InterruptedException {
        Allure.step("Setting change password detailed data.");
        Allure.step(String.format("Set User Name = %s .", operator.getLoginName()));
        controls.setTextBoxValue(driver, CHANGE_PASSWORD_USER_NAME_LOCATOR, operator.getLoginName());

        Allure.step(String.format("Set Old Password = %s .", operator.getPassword()));
        controls.setTextBoxValue(driver, OLD_PASSWORD_LOCATOR, operator.getPassword());

        Allure.step(String.format("Set New Password = %s .", newPassword));
        controls.setTextBoxValue(driver, NEW_PASSWORD_LOCATOR, newPassword);

        Allure.step(String.format("Set Confirm New Password = %s .", newPassword));
        controls.setTextBoxValue(driver, CONFIRM_NEW_PASSWORD_LOCATOR, newPassword);

        Allure.step("click ok button.");
        controls.performClickByJS(driver, OK_BUTTON_LOCATOR);

        String validationMessage = commonAction.getAlertMessageStringWithoutWaitForAjaxLoader(driver);
        Allure.step(String.format("Validation message = %s .", validationMessage));
        return validationMessage;
    }

    @Step("Get logged user name")
    public String getLoggedUserName(RemoteWebDriver driver) {
        Allure.step("Click on user profile icon.");
        driver.get(driver.getCurrentUrl());
        controls.performClick(driver, USER_PROFILE_ICON_LOCATOR);
        Allure.step("Click on About Your Session link.");
        controls.performClick(driver, ABOUT_YOUR_SESSION_LINK_LOCATOR);
        Allure.step("Get logged user name.");
        String loggedUserName = controls.getWebElement(driver, LOGGED_USER_NAME_LOCATOR).getAttribute("value");
        Allure.step("Logged user name = " + loggedUserName);
        Allure.step("Click on close button.");
        controls.performClick(driver, CLOSE_BUTTON_LOCATOR);
        return loggedUserName;
    }

    @Step("Check if user is logged in with correct credentials")
    public void checkIfUserLoginWithCorrectCredentials(RemoteWebDriver driver, String loginName, String password) throws InterruptedException, SQLException {

        if (commonAction.isUserLogged(driver)) {
            if (getLoggedUserName(driver).equals(loginName)) {
                driver.get(driver.getCurrentUrl());
                Navigation.SCAN_MANAGER.navigate(driver);
                return;
            } else {
                commonAction.logout(driver);
                login(driver
                        , loginName
                        , password
                        );
            }
        } else {
            login(driver
                    , loginName
                    , password
            );
        }


    }
}
