package eastnets.common.gui;

import core.gui.Controls;
import core.util.Wait;
import eastnets.common.control.Application;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.testng.Assert;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public enum Navigation {
    EVENT("menuform:om_admin_HomeEventViewer", "Event Viewer", Application.ADMIN),
    OPERATOR("menuform:om_admin_HomeOperatorManager", "Operator Manager", Application.ADMIN),
    GROUP("menuform:om_admin_HomeOperatorGroupManager", "Group Manager", Application.ADMIN),
    PROFILE("menuform:om_admin_HomeAuthorizationProfileManager", "Profile Manager", Application.ADMIN),
    ZONE("menuform:om_admin_HomeZoneManager", "Zone Manager", Application.ADMIN),
    AUDIT("menuform:om_admin_HomeAdminAuditManager", "Audit Manager", Application.ADMIN),
    REPORT("menuform:om_admin_HomeReportManager", "Report Manager", Application.ADMIN),
    ARCHIVE("menuform:om_admin_HomeArchiveViewerManager", "Archive Viewer", Application.ADMIN),
    SCAN_MANAGER("menuform:om_HomeScanManager", "Scan Manager", Application.SFP),
    DETECTION_MANAGER("menuform:om_HomeFilteringDetectionManager", "Detection Manager", Application.SFP),
    LIST_MANAGER("menuform:om_HomeListManager", "List Manager", Application.SFP),
    ADVANCED_SETTINGS("menuform:om_HomeAdvancedSettings", "Advanced Settings", Application.SFP),
    FORMAT_MANAGER("menuform:om_HomeFormatManager", "Format Manager", Application.SFP),
    DB_MANAGER("menuform:om_HomeDBConnector", "DB Manager", Application.SFP),
    SWIFT_MANAGER("menuform:om_HomeSWIFTConfiguration", "SWIFT Manager", Application.SFP),
    APPROVAL("menuform:om_HomeApproval-Module", "Approval", Application.SFP),
    MQ_MANAGER("menuform:om_HomeMQConfiguration", "MQ Manager", Application.SFP),
    ARCHIVE_VIEWER("menuform:om_HomeArchivingViewer", "Archive Viewer", Application.SFP),
    EVENT_VIEWER("menuform:om_HomeEventViewer", "Event Viewer", Application.SFP),
    REPORT_MANAGER("menuform:om_HomeReportManager", "Report Manager", Application.SFP),
    REPEAT_MANAGER("menuform:om_HomeRepeatManager", "Repeat Manager", Application.SFP),
    ISO_20022_MANAGER("menuform:om_HomeISO20022-Module", "ISO20022-Module", Application.SFP),
    LICENCE_MANAGER("menuform:om_HomeLicenseManager", "License Manager", Application.SFP);


    private final By navigationElement;
    private final Application application;
    private final String checkLabel;
    private final Controls controls;

    Navigation(String navigationElement, String checkLabel, Application application) {
        this.navigationElement = By.id(navigationElement);
        this.checkLabel = checkLabel;
        this.application = application;
        this.controls = new Controls();
    }

    @Step("Get Navigation By Application")
    public List<Navigation> getNavigationByApplication(Application application) {
        return Arrays.stream(Navigation.values())
                .filter(navigation -> navigation.application.equals(application))
                .collect(Collectors.toList());
    }

    @Step("Navigate")
    public void navigate(RemoteWebDriver driver) {

        controls.waitAndClick(driver, this.navigationElement, Duration.ofSeconds(Wait.ONE_SECOND * 5));
        String label = controls.getLabelValue(driver, By.xpath("//form[@id='topbar-right']/ul/label"));
        Assert.assertEquals(label, this.checkLabel, "Navigation to " + this.checkLabel + " failed");
    }

    @Step("Verify Repeat Manager Exists")
    public static void verifyRepeatManagerExists(RemoteWebDriver driver, boolean isExists) {
        new Controls().exists(driver, By.id("menuform:om_HomeRepeatManager"));
    }
}