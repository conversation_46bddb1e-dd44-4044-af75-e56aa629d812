package eastnets.admin.entity;

public class PasswordPolicy {

    private String testCaseTitle;
    private String scriptName;
    private String minimumLength;
    private String historyCount;
    private String minimumUpperCaseCount;
    private String minimumLowerCaseCount;
    private String minimumDigitsCount;
    private String minimumSpecialCharactersCount;
    private String preventConsecutiveFlag;
    private String statusFlag;
    private String newPassword;
    private String[] newPasswordList;
    private String expectedMessage;

    public PasswordPolicy() {
    }

    public PasswordPolicy(String minimumLength, String historyCount, String minimumUpperCaseCount
            , String minimumLowerCaseCount, String minimumDigitsCount, String minimumSpecialCharactersCount, String statusFlag) {
        this.minimumLength = minimumLength;
        this.historyCount = historyCount;
        this.minimumUpperCaseCount = minimumUpperCaseCount;
        this.minimumLowerCaseCount = minimumLowerCaseCount;
        this.minimumDigitsCount = minimumDigitsCount;
        this.minimumSpecialCharactersCount = minimumSpecialCharactersCount;
        this.statusFlag = statusFlag;
    }

    public String getScriptName() {
        return scriptName;
    }

    public void setScriptName(String scriptName) {
        this.scriptName = scriptName;
    }

    public String getMinimumLength() {
        return minimumLength;
    }

    public void setMinimumLength(String minimumLength) {
        this.minimumLength = minimumLength;
    }

    public String getHistoryCount() {
        return historyCount;
    }

    public void setHistoryCount(String historyCount) {
        this.historyCount = historyCount;
    }

    public String getMinimumUpperCaseCount() {
        return minimumUpperCaseCount;
    }

    public void setMinimumUpperCaseCount(String minimumUpperCaseCount) {
        this.minimumUpperCaseCount = minimumUpperCaseCount;
    }

    public String getMinimumLowerCaseCount() {
        return minimumLowerCaseCount;
    }

    public void setMinimumLowerCaseCount(String minimumLowerCaseCount) {
        this.minimumLowerCaseCount = minimumLowerCaseCount;
    }

    public String getMinimumDigitsCount() {
        return minimumDigitsCount;
    }

    public void setMinimumDigitsCount(String minimumDigitsCount) {
        this.minimumDigitsCount = minimumDigitsCount;
    }

    public String getMinimumSpecialCharactersCount() {
        return minimumSpecialCharactersCount;
    }

    public void setMinimumSpecialCharactersCount(String minimumSpecialCharactersCount) {
        this.minimumSpecialCharactersCount = minimumSpecialCharactersCount;
    }

    public String getPreventConsecutiveFlag() {
        return preventConsecutiveFlag;
    }

    public void setPreventConsecutiveFlag(String preventConsecutiveFlag) {
        this.preventConsecutiveFlag = preventConsecutiveFlag;
    }

    public String getStatusFlag() {
        return statusFlag;
    }

    public void setStatusFlag(String statusFlag) {
        this.statusFlag = statusFlag;
    }

    public String getTestCaseTitle() {
        return testCaseTitle;
    }

    public void setTestCaseTitle(String testCaseTitle) {
        this.testCaseTitle = testCaseTitle;
    }

    public String getNewPassword() {
        return newPassword;
    }

    public void setNewPassword(String newPassword) {
        this.newPassword = newPassword;
    }

    public String[] getNewPasswordList() {
        return newPasswordList;
    }

    public void setNewPasswordList(String[] newPasswordList) {
        this.newPasswordList = newPasswordList;
    }

    public String getExpectedMessage() {
        return expectedMessage;
    }

    public void setExpectedMessage(String expectedMessage) {
        this.expectedMessage = expectedMessage;
    }

    @Override
    public String toString() {
        return "PasswordPolicy{" +
                "minimumLength='" + minimumLength + '\'' +
                ", historyCount='" + historyCount + '\'' +
                ", minimumUpperCaseCount='" + minimumUpperCaseCount + '\'' +
                ", minimumLowerCaseCount='" + minimumLowerCaseCount + '\'' +
                ", minimumDigitsCount='" + minimumDigitsCount + '\'' +
                ", minimumSpecialCharactersCount='" + minimumSpecialCharactersCount + '\'' +
                ", preventConsecutiveFlag='" + preventConsecutiveFlag + '\'' +
                ", statusFlag='" + statusFlag + '\'' +
                '}';
    }


}
