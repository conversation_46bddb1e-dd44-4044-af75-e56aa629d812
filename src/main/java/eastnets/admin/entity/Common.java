package eastnets.admin.entity;

import com.opencsv.exceptions.CsvValidationException;
import eastnets.admin.control.EnCsvExtractor;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

public class Common {

    public enum ZONE {
        COMMON_TEST_ZONE_001(new Zone(0, "Common_Zone_01", "Common Zone 01", "Zone for most of the tests")),
        COMMON_TEST_ZONE_002(new Zone(0, "Common_Zone_02", "Common Zone 02", "Zone for most of the tests")),
        COMMON_TEST_ZONE_003(new Zone(0, "Common_Zone_03", "Common Zone 03", "Zone for most of the tests")),
        COMMON_TEST_ZONE_004(new Zone(0, "Common_Zone_04", "Common Zone 04", "Zone for most of the tests")),
        COMMON_TEST_ZONE_005(new Zone(0, "Common_Zone_05", "Common Zone 05", "Zone for most of the tests")),
        COMMON_TEST_ZONE_006(new Zone(0, "Common_Zone_06", "Common Zone 06", "Zone for most of the tests")),
        COMMON_TEST_ZONE_007(new Zone(0, "Common_Zone_07", "Common Zone 07", "Zone for most of the tests")),
        COMMON_TEST_ZONE_008(new Zone(0, "Common_Zone_08", "Common Zone 08", "Zone for most of the tests")),
        COMMON_TEST_ZONE_009(new Zone(0, "Common_Zone_09", "Common Zone 09", "Zone for most of the tests")),
        COMMON_TEST_ZONE_010(new Zone(0, "Common_Zone_10", "Common Zone 10", "Zone for most of the tests")),
        COMMON_TEST_ZONE_011(new Zone(0, "Common_Zone_11", "Common Zone 11", "Zone for most of the tests")),
        DEFAULT_ZONE(new Zone(0, "[Default Zone]", "Default Zone", "Default Zone")),
        SYSTEM_ZONE(new Zone(0, "[System Zone]", "[System Zone]", "System Zone")),
        FOUR_EYES_ZONE(new Zone(0, "Four_Eyes_Zone", "Four Eyes", "Four Eyes")),
        REPEAT_ZONE(new Zone(0, "Repeat_Zone", "Repeat Eyes", "Repeat Eyes")),

        APPROVAL_ZONE(new Zone(0, "[Approval Zone]", "Approval Zone", "Approval Zone"));
        private final Zone zone;

        ZONE(Zone zone) {
            this.zone = zone;
        }

        public Zone getZone() {
            return zone;
        }
    }

    public enum PROFILE {
        ADMIN_FULL_RIGHT(new Profile(0, "full-admin-right", true, true,false
                , "Profile with Admin full Rights", ZONE.COMMON_TEST_ZONE_001.getZone())),
        FULL_RIGHT_001(new Profile(0, "full-right-profile", true, true,false
                , "Profile with full Rights", ZONE.COMMON_TEST_ZONE_001.getZone())),
        FULL_RIGHT_002(new Profile(0, "full-right-profile_002", true, true,true
                , "full-right-profile", ZONE.COMMON_TEST_ZONE_002.getZone())),
        FULL_RIGHT_003(new Profile(0, "full-right-profile_03", true, true,true
                , "full-right-profile", ZONE.COMMON_TEST_ZONE_003.getZone())),
        FULL_RIGHT_004(new Profile(0, "full-right-profile_04", true, true,true
                , "full-right-profile", ZONE.COMMON_TEST_ZONE_004.getZone())),
        FULL_RIGHT_005(new Profile(0, "full-right-profile_05", true, true,true
                , "full-right-profile", ZONE.COMMON_TEST_ZONE_005.getZone())),
        FULL_RIGHT_006(new Profile(0, "full-right-profile_06", true, true,true
                , "full-right-profile", ZONE.COMMON_TEST_ZONE_006.getZone())),
        FULL_RIGHT_007(new Profile(0, "full-right-profile_07", true, true,true
                , "full-right-profile", ZONE.COMMON_TEST_ZONE_007.getZone())),
        FULL_RIGHT_008(new Profile(0, "full-right-profile_08", true, true,true
                , "full-right-profile", ZONE.COMMON_TEST_ZONE_008.getZone())),
        FULL_RIGHT_009(new Profile(0, "full-right-profile_09", true, true,true
                , "full-right-profile", ZONE.COMMON_TEST_ZONE_009.getZone())),
        FULL_RIGHT_010(new Profile(0, "full-right-profile_10", true, true,true
                , "full-right-profile", ZONE.COMMON_TEST_ZONE_010.getZone())),
        FULL_RIGHT_011(new Profile(0, "full-right-profile_11", true, true,true
                , "full-right-profile", ZONE.COMMON_TEST_ZONE_011.getZone())),

        NO_REPEAT_RIGHT(new Profile(0, "Repeat_Profile_Manager", true,false
                , false, "Repeat Profile Manager", ZONE.REPEAT_ZONE.getZone())),
        APPROVAL_RIGHT(new Profile(0, "approval-profile", true, true,false
                , "approval profile", ZONE.APPROVAL_ZONE.getZone())),
        DEFAULT_PROFILE(new Profile(0, "def-right-profile", true, true,false
                , "Profile with full Rights", ZONE.DEFAULT_ZONE.getZone())),
        FOUR_EYES_PROFILE(new Profile(0, "4EyesProfile", true, true,false
                , "FourEyesProfile", ZONE.FOUR_EYES_ZONE.getZone())),
        ADMINISTRATOR_PROFILE(new Profile(0, "[Administrators Profile]", true, true,false,
                "Administrators Profile", ZONE.SYSTEM_ZONE.getZone())),
        DEFULT_PROFILE_SYSTEM(new Profile(0, "[Default Profile]", true, true,false,
                "Default Profile", ZONE.DEFAULT_ZONE.getZone()));


        private final Profile profile;

        PROFILE(Profile profile) {
            this.profile = profile;
        }

        public Profile getProfile() throws CsvValidationException, IOException {
            return extractFromCsvResourceFile();
        }

        private Profile extractFromCsvResourceFile() throws CsvValidationException, IOException {
            EnCsvExtractor enCsvExtractor = new EnCsvExtractor();
            if (profile.isClone())
                profile.setProfileRights(enCsvExtractor.extractProfileRight(profile.getDescription()));
            else
                profile.setProfileRights(enCsvExtractor.extractProfileRight(profile.getName()));
            if (profile.getProfileRights().isEmpty())
                throw new IllegalStateException("No profile rights provided for this profile");
            return profile;
        }
    }

    public enum OPERATOR {
        FULL_RIGHT_1(new Operator("operator-01", "Operator", "With full rights", "hello00", ZONE.COMMON_TEST_ZONE_001.getZone())),
        FULL_RIGHT_2(new Operator("operator-02", "Operator", "With full rights", "hello00", ZONE.COMMON_TEST_ZONE_002.getZone())),
        FULL_RIGHT_3(new Operator("operator-03", "Operator", "With full rights", "hello00", ZONE.COMMON_TEST_ZONE_003.getZone())),
        FULL_RIGHT_4(new Operator("operator-04", "Operator", "With full rights", "hello00", ZONE.COMMON_TEST_ZONE_004.getZone())),
        FULL_RIGHT_5(new Operator("operator-05", "Operator", "With full rights", "hello00", ZONE.COMMON_TEST_ZONE_005.getZone())),
        FULL_RIGHT_6(new Operator("operator-06", "Operator", "With full rights", "hello00", ZONE.COMMON_TEST_ZONE_006.getZone())),
        FULL_RIGHT_7(new Operator("operator-07", "Operator", "With full rights", "hello00", ZONE.COMMON_TEST_ZONE_007.getZone())),
        FULL_RIGHT_8(new Operator("operator-08", "Operator", "With full rights", "hello00", ZONE.COMMON_TEST_ZONE_008.getZone())),
        FULL_RIGHT_9(new Operator("operator-09", "Operator", "With full rights", "hello00", ZONE.COMMON_TEST_ZONE_009.getZone())),
        FULL_RIGHT_10(new Operator("operator-10", "Operator", "With full rights", "hello00", ZONE.COMMON_TEST_ZONE_010.getZone())),
        FULL_RIGHT_11(new Operator("operator-11", "Operator", "With full rights", "hello00", ZONE.COMMON_TEST_ZONE_011.getZone())),
        DeFAULT_ZONE_OPERATOR_1(new Operator("def-operator-1", "Operator", "With full rights", "hello00", ZONE.DEFAULT_ZONE.getZone())),
        DeFAULT_ZONE_OPERATOR_2(new Operator("def-operator-2", "Operator", "With full rights", "hello00", ZONE.DEFAULT_ZONE.getZone())),
        NO_REPEAT_MANAGER_RIGHT(new Operator("RepeatUser1", "Operator", "No Repeat Manager right", "hello00", ZONE.REPEAT_ZONE.getZone())),
        APPROVAL_RIGHT(new Operator("approval-operator", "approval-operator", "approval-operator", "hello00", ZONE.APPROVAL_ZONE.getZone())),
        APPROVAL_RIGHT_1(new Operator("approval-operator-1", "approval-operator-1", "approval-operator", "hello00", ZONE.APPROVAL_ZONE.getZone())),
        FOUR_EYES_01(new Operator("4eyes01", "4eyes01", "4eyes01", "hello00", ZONE.FOUR_EYES_ZONE.getZone())),
        FOUR_EYES_02(new Operator("4eyes02", "4eyes02", "4eyes02", "hello00", ZONE.FOUR_EYES_ZONE.getZone())),
        FOUR_EYES_03(new Operator("4eyes03", "4eyes03", "4eyes03", "hello00", ZONE.FOUR_EYES_ZONE.getZone()));


        private final Operator operator;

        OPERATOR(Operator operator) {
            this.operator = operator;
        }

        public Operator getOperator() {
            return operator;
        }


    }

    public enum GROUP {
        FULL_RIGHT_GROUP_01(new Group("full-right-group_01", "Full right group for Selenium testing", "", true),
                PROFILE.FULL_RIGHT_001,
                List.of(OPERATOR.FULL_RIGHT_1, OPERATOR.DeFAULT_ZONE_OPERATOR_1, OPERATOR.DeFAULT_ZONE_OPERATOR_2), false),
        FULL_RIGHT_GROUP_02(new Group("full-right-group_02", "Full right group for Selenium testing", "", true),
                PROFILE.FULL_RIGHT_002,
                List.of(OPERATOR.FULL_RIGHT_2, OPERATOR.DeFAULT_ZONE_OPERATOR_1, OPERATOR.DeFAULT_ZONE_OPERATOR_2), false),
        FULL_RIGHT_GROUP_03(new Group("full-right-group_03", "Full right group for Selenium testing", "", true),
                PROFILE.FULL_RIGHT_003,
                List.of(OPERATOR.FULL_RIGHT_3), false),
        FULL_RIGHT_GROUP_04(new Group("full-right-group_04", "Full right group for Selenium testing", "", true),
                PROFILE.FULL_RIGHT_004,
                List.of(OPERATOR.FULL_RIGHT_4), false),
        FULL_RIGHT_GROUP_05(new Group("full-right-group_05", "Full right group for Selenium testing", "", true),
                PROFILE.FULL_RIGHT_005,
                List.of(OPERATOR.FULL_RIGHT_5), false),
        FULL_RIGHT_GROUP_06(new Group("full-right-group_06", "Full right group for Selenium testing", "", true),
                PROFILE.FULL_RIGHT_006,
                List.of(OPERATOR.FULL_RIGHT_6), false),
        FULL_RIGHT_GROUP_07(new Group("full-right-group_07", "Full right group for Selenium testing", "", true),
                PROFILE.FULL_RIGHT_007,
                List.of(OPERATOR.FULL_RIGHT_7), false),
        FULL_RIGHT_GROUP_08(new Group("full-right-group_08", "Full right group for Selenium testing", "", true),
                PROFILE.FULL_RIGHT_008,
                List.of(OPERATOR.FULL_RIGHT_8), false),
        FULL_RIGHT_GROUP_09(new Group("full-right-group_09", "Full right group for Selenium testing", "", true),
                PROFILE.FULL_RIGHT_009,
                List.of(OPERATOR.FULL_RIGHT_9), false),
        FULL_RIGHT_GROUP_10(new Group("full-right-group_10", "Full right group for Selenium testing", "", true),
                PROFILE.FULL_RIGHT_010,
                List.of(OPERATOR.FULL_RIGHT_10), false),
        FULL_RIGHT_GROUP_11(new Group("full-right-group_11", "Full right group for Selenium testing", "", true),
                PROFILE.FULL_RIGHT_011,
                List.of(OPERATOR.FULL_RIGHT_11), false),
        NOT_REPEAT_FULL_RIGHT_GROUP(new Group("No Repeat Group", "No Repeat Group", "", true),
                PROFILE.NO_REPEAT_RIGHT,
                List.of(OPERATOR.NO_REPEAT_MANAGER_RIGHT), false),

        FOUR_EYES_GROUP(new Group("4eyes_NewGroup", "4eyes_NewGroup", "", true),
                PROFILE.FOUR_EYES_PROFILE,
                List.of(OPERATOR.FOUR_EYES_01, OPERATOR.FOUR_EYES_02), false),
        APPROVAL_GROUP(new Group("approval_group", "approval_group", "", true),
                PROFILE.APPROVAL_RIGHT,
                List.of(OPERATOR.APPROVAL_RIGHT, OPERATOR.APPROVAL_RIGHT_1), false),

        DEFAULT_GROUP(new Group("def-group", "Default full right group for Selenium testing", "", true),
                PROFILE.DEFAULT_PROFILE,
                List.of(OPERATOR.DeFAULT_ZONE_OPERATOR_1, OPERATOR.DeFAULT_ZONE_OPERATOR_2), false),

        UNASSGINED_GROUP(new Group("[Unassign Group]", "Unassign Group", "", true),
                PROFILE.DEFAULT_PROFILE,
                List.of(OPERATOR.FULL_RIGHT_10), true),
        ADMINISTRATOR_GROUP(new Group("[Administrators Group]", "Default group for administrators", "", true),
                PROFILE.DEFAULT_PROFILE,
                List.of(OPERATOR.FULL_RIGHT_1)
                , true),
        DISABLED_GROUP(new Group("Disable-group", "Disable  group for defect testing", "", false),
                PROFILE.DEFAULT_PROFILE,
                List.of(OPERATOR.DeFAULT_ZONE_OPERATOR_1), false),
        ;

        private final Group group;
        private final PROFILE profile;

        GROUP(Group group, PROFILE profile, List<OPERATOR> groupMembers, boolean isSystemGroup) {
            this.group = group;
            this.profile = profile;
            this.group.setGroupMembers(groupMembers.stream().map(OPERATOR::getOperator).collect(Collectors.toList()));
            this.group.setSystemGroup(isSystemGroup);
        }

        public Group get() throws CsvValidationException, IOException {
            this.group.setProfile(this.profile.getProfile());
            return group;
        }
    }
}
