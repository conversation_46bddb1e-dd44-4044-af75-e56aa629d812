package eastnets.admin.entity;

import com.opencsv.exceptions.CsvValidationException;
import core.util.Randomizer;
import eastnets.admin.control.EnCsvExtractor;

import java.io.IOException;
import java.util.List;
import java.util.Map;

public class Profile {

    public static final String COUNT_ON_NAME = "SELECT COUNT(*) FROM tProfiles WHERE tProfiles.DELETED = 0 AND tProfiles.NAME = ? ";
    private String testCaseID;
    private String testCaseName;
    private String scriptName;

    private long id;
    private long grantId;
    private long grantRightId;
    private long grantTypeId;
    private String name;
    private boolean enabled;
    private boolean writeRight;
    private String description;
    private Zone zone;
    private Map<String, Map<String, List<String>>> profileRights;
    private String functionToCheck;
    private String permissionName;
    private boolean clone;

    public Profile(long id, String name, boolean enabled, boolean writeRight,boolean isClone, String description, Zone zone) {
        this.id = id;
        this.name = name;
        this.enabled = enabled;
        this.writeRight = writeRight;
        this.description = description;
        this.zone = zone;
        this.clone = isClone;
    }

    public Profile() {
    }

    public boolean isWriteRight() {
        return writeRight;
    }

    public boolean isClone() {
        return clone;
    }

    public void setClone(boolean clone) {
        this.clone = clone;
    }

    public String getScriptName() {
        return scriptName;
    }

    public void setScriptName(String scriptName) {
        this.scriptName = scriptName;
    }

    public long getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public Zone getZone() {
        return zone;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public boolean hasWriteRight() {
        return writeRight;
    }

    public Map<String, Map<String, List<String>>> getProfileRights() {
        return profileRights;
    }

    public void setProfileRights(Map<String, Map<String, List<String>>> profileRights) {
        this.profileRights = profileRights;
    }

    public void setId(long id) {
        this.id = id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public void setWriteRight(boolean writeRight) {
        this.writeRight = writeRight;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public void setZone(Zone zone) {
        this.zone = zone;
    }

    public long getGrantId() {
        return grantId;
    }

    public void setGrantId(long grantId) {
        this.grantId = grantId;
    }

    public long getGrantRightId() {
        return grantRightId;
    }

    public void setGrantRightId(long grantRightId) {
        this.grantRightId = grantRightId;
    }

    public long getGrantTypeId() {
        return grantTypeId;
    }

    public void setGrantTypeId(long grantTypeId) {
        this.grantTypeId = grantTypeId;
    }

    public String getTestCaseID() {
        return testCaseID;
    }

    public void setTestCaseID(String testCaseID) {
        this.testCaseID = testCaseID;
    }

    public String getTestCaseName() {
        return testCaseName;
    }

    public void setTestCaseName(String testCaseName) {
        this.testCaseName = testCaseName;
    }

    public String getFunctionToCheck() {
        return functionToCheck;
    }

    public void setFunctionToCheck(String functionToCheck) {
        this.functionToCheck = functionToCheck;
    }

    public String getPermissionName() {
        return permissionName;
    }

    public void setPermissionName(String permissionName) {
        this.permissionName = permissionName;
    }

    public static Profile getRandom(String profileRightToExtract) throws CsvValidationException, IOException {
        return getRandom(Zone.getRandom(), profileRightToExtract);
    }

    public static Profile getRandom(Zone zone, String profileRightToExtract) throws CsvValidationException, IOException {
        Profile profile = new Profile(0, "Test-Profile-" + new Randomizer().getInt(), true, true, false,"Random profile for Selenium testing", zone);
        profile.setProfileRights(new EnCsvExtractor().extractProfileRight(profileRightToExtract));
        return profile;
    }

    @Override
    public String toString() {
        return "Profile{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", enabled=" + enabled +
                ", writeRight=" + writeRight +
                ", description='" + description + '\'' +
                ", zone=" + zone +
                ", profileRights=" + profileRights +
                '}';
    }
}
