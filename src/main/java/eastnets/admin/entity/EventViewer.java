package eastnets.admin.entity;

public class EventViewer {
    private String creationDate;
    private String code;
    private String severity;
    private String category;
    private String type;
    private String message;
    private String application;
    private String module;
    private String zone;

    public String getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(String creationDate) {
        this.creationDate = creationDate;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getSeverity() {
        return severity;
    }

    public void setSeverity(String severity) {
        this.severity = severity;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getApplication() {
        return application;
    }

    public void setApplication(String application) {
        this.application = application;
    }

    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }

    public String getZone() {
        return zone;
    }

    public void setZone(String zone) {
        this.zone = zone;
    }

    @Override
    public String toString() {
        return "EventViewer{" +
                "severity='" + severity + '\'' +
                ", category='" + category + '\'' +
                ", type='" + type + '\'' +
                ", message='" + message + '\'' +
                ", application='" + application + '\'' +
                ", module='" + module + '\'' +
                ", zone='" + zone + '\'' +
                '}';
    }
}
