package eastnets.admin.entity;

import core.util.Randomizer;

public class Operator {

    public static final String COUNT_ON_LOGIN_NAME = "SELECT COUNT(*) FROM tOperators WHERE tOperators.DELETED = 0 AND tOperators.LOGIN_NAME = ?";
    private long id;
    private String scriptName;
    private final String loginName;
    private final String firstName;
    private final String lastName;
    private String password;
    private String testCaseTitle;
    private String permissionName;
    private String functionToCheck;
    private String profileName;

    private final Zone zone;

    public Operator(String loginName, String firstName, String lastName, String password, Zone zone) {
        this.loginName = loginName;
        this.firstName = firstName;
        this.lastName = lastName;
        this.password = password;
        this.zone = zone;
    }

    public String getScriptName() {
        return scriptName;
    }

    public void setScriptName(String scriptName) {
        this.scriptName = scriptName;
    }

    public String getLoginName() {
        return loginName;
    }

    public String getFirstName() {
        return firstName;
    }

    public String getLastName() {
        return lastName;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public long getId() {
        return id;
    }

    public void setId(long id) {
        this.id = id;
    }

    public Zone getZone() {
        return zone;
    }

    public String getTestCaseTitle() {
        return testCaseTitle;
    }

    public void setTestCaseTitle(String testCaseTitle) {
        this.testCaseTitle = testCaseTitle;
    }

    public String getPermissionName() {
        return permissionName;
    }

    public void setPermissionName(String permissionName) {
        this.permissionName = permissionName;
    }

    public String getFunctionToCheck() {
        return functionToCheck;
    }

    public void setFunctionToCheck(String functionToCheck) {
        this.functionToCheck = functionToCheck;
    }

    public String getProfileName() {
        return profileName;
    }

    public void setProfileName(String profileName) {
        this.profileName = profileName;
    }

    @Override
    public String toString() {
        return "Operator{" +
                "id=" + id +
                ", loginName='" + loginName + '\'' +
                ", firstName='" + firstName + '\'' +
                ", lastName='" + lastName + '\'' +
                ", password='" + password + '\'' +
                ", zone=" + zone +
                '}';
    }

    public Operator getRandom() {
        return getRandom(Common.ZONE.COMMON_TEST_ZONE_001.getZone());
    }

    public static Operator getRandom(Zone zone) {
        int randomNumber = new Randomizer().getInt();
        return new Operator(
                "selenium-user-" + randomNumber,
                "Random",
                "User",
                "hello00",
                zone
        );
    }
}
