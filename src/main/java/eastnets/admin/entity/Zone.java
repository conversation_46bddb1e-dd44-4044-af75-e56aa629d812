package eastnets.admin.entity;

import core.util.Randomizer;


public class Zone {

    public static final String COUNT_ON_NAME = "SELECT COUNT(*) FROM tZones WHERE tZones.NAME = ?";

    private long id;
    private String name;
    private String displayName;
    private String description;

    public Zone(long id, String name, String displayName, String description) {
        this.id = id;
        this.name = name;
        this.displayName = displayName;
        this.description = description;
    }

    public long getId() {
        if (Long.valueOf(id) == null)
            id = 0;
        return id;
    }

    public String getName() {
        return name;
    }

    public String getDisplayName() {
        return displayName;
    }


    public String getDescription() {
        return description;
    }

    public void setId(long id) {
        this.id = id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return "Zone{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", displayName='" + displayName + '\'' +
                ", description='" + description + '\'' +
                '}';
    }

    public static Zone getRandom() {
        int randomNumber = new Randomizer().getInt();
        return new Zone(
                0,
                String.format("Zone%d", randomNumber),
                String.format("Zone (%d)", randomNumber),
                String.format("Zone created with random number '%d'", randomNumber)
        );
    }
}
