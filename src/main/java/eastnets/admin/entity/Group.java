package eastnets.admin.entity;

import core.util.Randomizer;

import java.util.List;

public class Group {

    public static final String COUNT_ON_NAME = "SELECT COUNT(*) FROM tGroups  WHERE tGroups.DELETED = 0 AND tGroups.NAME = ?";
    public static final String COUNT_MEMBER_BASED_ON_OPERATOR_NAME = "SELECT COUNT(*) FROM tGroupMembers " +
            "WHERE tGroupMembers.GROUP_ID = (SELECT tGroups.ID FROM tGroups WHERE tGroups.NAME = ?) " +
            "AND tGroupMembers.GROUP_MEMBER_ID = (SELECT tOperators.ID FROM tOperators WHERE tOperators.LOGIN_NAME = ?)";

    private long id;
    private String name;
    private String description;
    private String mail;
    private boolean enabled;
    private Profile profile;
    private List<Operator> groupMembers;
    private boolean isSystemGroup;


    public Group(String name, String description, String mail, boolean enabled) {
        this.id = 0;
        this.name = name;
        this.description = description;
        this.mail = mail;
        this.enabled = enabled;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public void setMail(String mail) {
        this.mail = mail;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public boolean isSystemGroup() {
        return isSystemGroup;
    }

    public void setSystemGroup(boolean systemGroup) {
        isSystemGroup = systemGroup;
    }

    public long getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public String getMail() {
        return mail;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public Profile getProfile() {
        return profile;
    }

    public void setId(long id) {
        this.id = id;
    }

    public void setProfile(Profile profile) {
        this.profile = profile;
    }

    public List<Operator> getGroupMembers() {
        return groupMembers;
    }

    public void setGroupMembers(List<Operator> groupMembers) {
        this.groupMembers = groupMembers;
    }

    public static Group getRandom(Profile profile, List<Operator> groupMembers) {
        int random =new Randomizer().getInt();
        Group group = new Group("selenium-random-group-" + random, "Random group for selenium testing", "", true);
        group.setGroupMembers(groupMembers);
        group.setProfile(profile);
        return group;
    }

    @Override
    public String toString() {
        return "Group{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", mail='" + mail + '\'' +
                ", enabled=" + enabled +
                ", profile=" + profile +
                ", groupMembers=" + groupMembers +
                '}';
    }
}
