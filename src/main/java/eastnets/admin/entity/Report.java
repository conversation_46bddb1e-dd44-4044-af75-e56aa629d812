package eastnets.admin.entity;

public class Report {

    private String testCaseTitle;
    private String scriptName;
    private String reportName;
    private String userName;
    private String comment;
    private String inactivityDays;
    private String application;
    private String zone;
    private String module;
    private String group;
    private String operator;
    private String profile;
    private String department;
    private String profile1;
    private String profile2;
    private String targetFileName;
    private String action;
    private String validationString;


    public Report(String reportName, String userName, String comment) {
        this.reportName = reportName;
        this.userName = userName;
        this.comment = comment;
    }


    public String getTestCaseTitle() {
        return testCaseTitle;
    }

    public void setTestCaseTitle(String testCaseTitle) {
        this.testCaseTitle = testCaseTitle;
    }

    public String getScriptName() {
        return scriptName;
    }

    public void setScriptName(String scriptName) {
        this.scriptName = scriptName;
    }

    public String getReportName() {
        return reportName;
    }

    public void setReportName(String reportName) {
        this.reportName = reportName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getInactivityDays() {
        return inactivityDays;
    }

    public void setInactivityDays(String inactivityDays) {
        this.inactivityDays = inactivityDays;
    }

    public String getApplication() {
        return application;
    }

    public void setApplication(String application) {
        this.application = application;
    }

    public String getZone() {
        return zone;
    }

    public void setZone(String zone) {
        this.zone = zone;
    }

    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }

    public String getGroup() {
        return group;
    }

    public void setGroup(String group) {
        this.group = group;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getProfile() {
        return profile;
    }

    public void setProfile(String profile) {
        this.profile = profile;
    }

    public String getDepartment() {
        return department;
    }

    public void setDepartment(String department) {
        this.department = department;
    }

    public String getProfile1() {
        return profile1;
    }

    public void setProfile1(String profile1) {
        this.profile1 = profile1;
    }

    public String getProfile2() {
        return profile2;
    }

    public void setProfile2(String profile2) {
        this.profile2 = profile2;
    }

    public String getTargetFileName() {
        return targetFileName;
    }

    public void setTargetFileName(String targetFileName) {
        this.targetFileName = targetFileName;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getValidationString() {
        return validationString;
    }

    public void setValidationString(String validationString) {
        this.validationString = validationString;
    }

    @Override
    public String toString() {
        return "Report{" +
                "reportName='" + reportName + '\'' +
                ", userName='" + userName + '\'' +
                ", comment='" + comment + '\'' +
                ", inactivityDays='" + inactivityDays + '\'' +
                ", application='" + application + '\'' +
                ", zone='" + zone + '\'' +
                ", module='" + module + '\'' +
                ", group='" + group + '\'' +
                ", operator='" + operator + '\'' +
                ", profile='" + profile + '\'' +
                ", department='" + department + '\'' +
                ", profile1='" + profile1 + '\'' +
                ", profile2='" + profile2 + '\'' +
                '}';
    }
}
