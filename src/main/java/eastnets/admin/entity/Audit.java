package eastnets.admin.entity;

public class Audit {

    private String auditId;
    private String action;
    private String modifiedField;
    private String module;
    private String modifiedBy;
    private String zone;
    private String description;
    private String returnedItem;

    public String getAuditId() {
        return auditId;
    }

    public void setAuditId(String auditId) {
        this.auditId = auditId;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public String getModifiedField() {
        return modifiedField;
    }

    public void setModifiedField(String modifiedField) {
        this.modifiedField = modifiedField;
    }

    public String getModule() {
        return module;
    }

    public void setModule(String module) {
        this.module = module;
    }

    public String getModifiedBy() {
        return modifiedBy;
    }

    public void setModifiedBy(String modifiedBy) {
        this.modifiedBy = modifiedBy;
    }

    public String getZone() {
        return zone;
    }

    public void setZone(String zone) {
        this.zone = zone;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getReturnedItem() {
        return returnedItem;
    }

    public void setReturnedItem(String returnedItem) {
        this.returnedItem = returnedItem;
    }

    @Override
    public String toString() {
        return "Audit{" +
                "auditId='" + auditId + '\'' +
                ", action='" + action + '\'' +
                ", modifiedField='" + modifiedField + '\'' +
                ", module='" + module + '\'' +
                ", modifiedBy='" + modifiedBy + '\'' +
                ", zone='" + zone + '\'' +
                ", description='" + description + '\'' +
                ", returnedItem='" + returnedItem + '\'' +
                '}';
    }
}
