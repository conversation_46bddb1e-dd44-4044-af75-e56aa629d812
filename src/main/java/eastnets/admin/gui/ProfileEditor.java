package eastnets.admin.gui;

import core.gui.Controls;
import core.gui.Picker;
import eastnets.admin.entity.Profile;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.util.ArrayList;

public class ProfileEditor {
    private static final By NAME_INPUT_BY_ID = By.id("authorizationProfileDetailForm:detail_business:Name");
    private static final By DESCRIPTION_INPUT_BY_ID = By.id("authorizationProfileDetailForm:detail_business:remark");
    private static final By ZONE_COMBOBOX = By.id("authorizationProfileDetailForm:detail_business:zoneCbx");
    private static final By AVAILABLE_APPLICATION_INPUT_BY_ID = By.id("authorizationProfileDetailForm:detail_business:applicationPickListId_source_filter");
    private static final By SAVE_BUTTON_BY_ID = By.xpath("//*[@id='authorizationProfileDetailForm:detail_business:btnSaveCreate' or @id='authorizationProfileDetailForm:detail_business:btnSaveModify']");
    private static final By CANCEL_BUTTON_BY_ID = By.id("authorizationProfileDetailForm:detail_business:btnCancel");
    private static final By ENABLED_CHECKBOX_LOCATOR = By.xpath("//div[@id='authorizationProfileDetailForm:detail_business:enabledFlag']//div[2]");
    private static final String ENABLED_CHECKBOX_BY_ID = "authorizationProfileDetailForm:detail_business:enabledFlag";
    private static final String WRITE_RIGHT_CHECKBOX_BY_ID = "authorizationProfileDetailForm:detail_business:zoneWritableFlag";
    private static final Picker APPLICATION_PICKER = new Picker("authorizationProfileDetailForm:detail_business:applicationPickListId");
    private static final By CLONED_ZONE_LOCATOR = By.id("authorizationProfileDetailForm:detail_business:zoneClonedCbx");
    private static final By CLONED_NAME_INPUT_LOCATOR = By.id("authorizationProfileDetailForm:detail_business:NameClone");

    private final Controls controls;

    public ProfileEditor() {
        this.controls = new Controls();
    }

    @Step("Create Profile")
    public void createProfile(RemoteWebDriver driver, Profile profile) throws Exception {
        Allure.step(String.format("Set name = %s ", profile.getName()));
        controls.setTextBoxValue(driver, NAME_INPUT_BY_ID, profile.getName());
        Allure.step(String.format("Set description = %s ", profile.getDescription()));
        controls.setTextBoxValue(driver, DESCRIPTION_INPUT_BY_ID, profile.getDescription());
        Allure.step(String.format("Check enable checkbox to be %s ", profile.isEnabled()));
        controls.setCheckboxValueById(driver, ENABLED_CHECKBOX_BY_ID, profile.isEnabled());
        Allure.step(String.format("Check write right checkbox to be %s ", profile.getName()));
        controls.setCheckboxValueById(driver, WRITE_RIGHT_CHECKBOX_BY_ID, profile.hasWriteRight());
        Allure.step(String.format("Select zone = %s ", profile.getName()));
        controls.selectOptionByDisplayedText(driver, ZONE_COMBOBOX, profile.getZone().getDisplayName());

        APPLICATION_PICKER.addAvailableItem(driver, new ArrayList<>(profile.getProfileRights().keySet()));
        Allure.step("Click save button.");
        controls.performClickByJS(driver, SAVE_BUTTON_BY_ID);
        Allure.step("Accept alert.");
        driver.switchTo().alert().accept();
    }

    @Step("Set cloned name")
    public void set_cloned_name(RemoteWebDriver driver, String name) {
        controls.setTextBoxValue(driver, CLONED_NAME_INPUT_LOCATOR, name);
    }
    @Step("Set enabled flag")
    public void setEnabledFlag(RemoteWebDriver driver, boolean enabled) {
        controls.setCheckboxValueById(driver, ENABLED_CHECKBOX_BY_ID, enabled);
    }

    @Step("Set cloned zone")
    public void set_cloned_zone(RemoteWebDriver driver, String zone_name) throws Exception {
        controls.selectOptionByDisplayedText(driver, CLONED_ZONE_LOCATOR, zone_name);
    }

    @Step("Click Save Button")
    public void click_save_button(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SAVE_BUTTON_BY_ID);
        driver.switchTo().alert().accept();
    }

    @Step("Get Module Editor")
    public void getModuleEditor(RemoteWebDriver driver, String application) throws InterruptedException {
        APPLICATION_PICKER.clickOnSelectedItem(driver, application);
    }

    @Step("Exit Without Saving")
    public void exitWithoutSaving(RemoteWebDriver driver) {
        controls.performClickByJS(driver, CANCEL_BUTTON_BY_ID);
    }

    @Step("Check If Profile Is Editable")
    public boolean checkIfProfileIsEditable(RemoteWebDriver driver) {
        if (controls.getWebElement(driver, NAME_INPUT_BY_ID).getAttribute("aria-readonly").contains("true")
                && controls.getWebElement(driver, DESCRIPTION_INPUT_BY_ID).getAttribute("aria-readonly").contains("true"))
            return true;

        return false;

    }

    @Step("Verify If Enable Box Enabled")
    public boolean verifyIfEnableBoxEnabled(RemoteWebDriver driver) {
        if (!controls.getWebElement(driver, ENABLED_CHECKBOX_LOCATOR).getAttribute("class").contains("ui-state-disabled"))
            return true;

        return false;

    }
}