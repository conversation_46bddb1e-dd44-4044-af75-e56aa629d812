package eastnets.admin.gui;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.time.Duration;

public class ArchiveViewerManager {

    private static final By BROWSE_BUTTON_LOCATOR = By.id("archiveViewerManagerForm:homepage_business:file_input");
    private static final By OPEN_BUTTON_LOCATOR = By.id("archiveViewerManagerForm:homepage_business:btnOpenFile");
    private static final By CHECKBOX_LOCATOR = By.xpath("//*[@class='ui-selectbooleancheckbox ui-chkbox ui-widget']");
    private static final By RESULT_VIEW_LOCATOR = By.id("archiveViewerManagerForm:homepage_business:_tblResults_data");
    private static final By CHECK_LABEL_LOCATOR = By.xpath("//*[@id='archiveViewerManagerForm:homepage_business:archiveDetail']//tbody");

    private final Controls controls;
    private final Wait wait;

    public ArchiveViewerManager() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    @Step("Browse File")
    public void browseFile(RemoteWebDriver driver, String filePath) {
        Allure.step("Select file to browse.");
        controls.setTextBoxValue(driver, BROWSE_BUTTON_LOCATOR, filePath);
    }

    @Step("Click Open Button")
    public void clickOpenButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, OPEN_BUTTON_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
        wait.waitForJStoLoad(driver);
    }

    @Step("Select first result")
    public void selectFirstResult(RemoteWebDriver driver) {
        Allure.step("Select first result from result table.");
        wait.waitUntilElementToBeVisible(driver, driver.findElements(RESULT_VIEW_LOCATOR).get(0).findElement(By.xpath("//tr[1]//td[2]//a")), Duration.ofSeconds(10));
        WebElement firstResult = driver.findElements(RESULT_VIEW_LOCATOR).get(0).findElement(By.xpath("//tr[1]//td[2]//a"));
        controls.performClick(firstResult);
    }

    @Step("Verify Archive Details Table")
    public boolean verifyArchiveDetailsTable(RemoteWebDriver driver) {
        Allure.step("Check if archive detailed table exist.");
        controls.scrollDown(driver);
        return controls.exists(driver, CHECKBOX_LOCATOR);
    }

}
