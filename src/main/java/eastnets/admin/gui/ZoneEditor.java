package eastnets.admin.gui;

import core.constants.screening.GeneralConstants;
import core.gui.Controls;
import core.util.Property;
import eastnets.admin.entity.Zone;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.io.IOException;


public class ZoneEditor {
    private static final By ID_INPUT_BY_ID = By.id("zoneManagerDetailForm:detail_business:id");
    private static final By NAME_INPUT_BY_ID = By.id("zoneManagerDetailForm:detail_business:name");
    private static final By DISPLAY_NAME_INPUT_BY_ID = By.id("zoneManagerDetailForm:detail_business:displayName");
    private static final By DESCRIPTION_INPUT_BY_ID = By.id("zoneManagerDetailForm:detail_business:zoneDescription");
    private static final By SAVE_BUTTON_BY_ID = By.id("zoneManagerDetailForm:detail_business:btnSaveCreate");
    private static final By CANCEL_BUTTON_BY_ID = By.id("zoneManagerDetailForm:detail_business:btnCancel");

    private final Controls controls;
    private final Property property;
    private final ZoneManager zoneManager;

    public ZoneEditor() {
        this.controls = new Controls();
        this.property = new Property();
        this.zoneManager = new ZoneManager();
    }

    @Step("Create Zone")
    public boolean createZone(RemoteWebDriver driver, Zone zone) throws IOException, InterruptedException {
        Allure.step("Set create new zone data.");
        Allure.step(String.format("Set name = %s", zone.getName()));
        controls.setTextBoxValue(driver, NAME_INPUT_BY_ID, zone.getName());
        Allure.step(String.format("Set display name = %s", zone.getDisplayName()));
        controls.setTextBoxValue(driver, DISPLAY_NAME_INPUT_BY_ID, zone.getDisplayName());
        Allure.step(String.format("Set description = %s", zone.getDescription()));
        controls.setTextBoxValue(driver, DESCRIPTION_INPUT_BY_ID, zone.getDescription());
        long zoneId = Long.parseLong(controls.getTagValue(driver, ID_INPUT_BY_ID));
        property.updateKeyValue(GeneralConstants.SMOKE_TEST_CONFIG_FILE_NAME, "zone.id", String.valueOf(zoneId));
        Allure.step(String.format("Capture zone id from UI = %s", zoneId));
        Allure.step("Click save button.");
        controls.performClickByJS(driver, SAVE_BUTTON_BY_ID);
        driver.switchTo().alert().accept();
        return zoneManager.verifyZoneExist(driver, zone);

    }
}
