package eastnets.admin.gui;

import core.gui.Controls;
import core.gui.Picker;
import eastnets.admin.entity.Group;
import eastnets.admin.entity.Operator;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.util.stream.Collectors;

public class GroupEditor {
    private static final By NAME_INPUT_BY_ID = By.id("operatorGroupDetailForm:detail_business:name");
    private static final By DESCRIPTION_INPUT_BY_ID = By.id("operatorGroupDetailForm:detail_business:description");
    private static final By EMAIL_INPUT_BY_ID = By.id("operatorGroupDetailForm:detail_business:email");
    private static final By PROFILE_SELECT_LOCATOR = By.id("operatorGroupDetailForm:detail_business:profileCbx");
    private static final String ENABLED_CHECKBOX_BY_ID = "operatorGroupDetailForm:detail_business:enabledFlag";
    private static final By MEMBER_INPUT_BY_ID = By.id("operatorGroupDetailForm:detail_business:memberPickList_source_filter");
    private static final By SAVE_BUTTON_BY_ID = By.id("operatorGroupDetailForm:detail_business:btnSaveCreate");
    private static final By SAVE_BUTTON_EDITOR = By.id("operatorGroupDetailForm:detail_business:btnSaveModify");
    private static final By CANCEL_BUTTON_BY_ID = By.id("operatorGroupDetailForm:detail_business:btnCancel");
    private static final By ADD_ALL_BUTTON_BY_XPATH = By.xpath("//div[@class=\"ui-picklist-buttons\"]/div/button[@title=\"Add All\"]");
    private static final Picker GROUP_MEMBER_PICKER = new Picker("operatorGroupDetailForm:detail_business:memberPickList");

    private final Controls controls;

    public GroupEditor() {
        this.controls = new Controls();
    }

    @Step("Click Save Button")
    private void clickSave(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SAVE_BUTTON_BY_ID);
        driver.switchTo().alert().accept();
    }

    @Step("Click Save Button")
    private void clickSaveEditor(RemoteWebDriver driver) throws InterruptedException {
        controls.performClickByJS(driver, SAVE_BUTTON_EDITOR);
        driver.switchTo().alert().accept();
    }

    @Step("Create Group")
    public void createGroup(RemoteWebDriver driver, Group group) throws Exception {
        controls.setTextBoxValue(driver, NAME_INPUT_BY_ID, group.getName());
        controls.setTextBoxValue(driver, DESCRIPTION_INPUT_BY_ID, group.getDescription());
        controls.setTextBoxValue(driver, EMAIL_INPUT_BY_ID, group.getMail());
        controls.setCheckboxValueById(driver, ENABLED_CHECKBOX_BY_ID, group.isEnabled());
        controls.selectOptionByDisplayedText(driver, PROFILE_SELECT_LOCATOR, group.getProfile().getName());
        controls.addElementFromCollection(
                driver,
                MEMBER_INPUT_BY_ID, ADD_ALL_BUTTON_BY_XPATH,
                group.getGroupMembers().stream().map(Operator::getLoginName).collect(Collectors.toList())
        );
        clickSave(driver);
    }

    @Step("Create disabledGroup")
    public void createDisbaledGroup(RemoteWebDriver driver, Group group) throws Exception {
        controls.setTextBoxValue(driver, NAME_INPUT_BY_ID, group.getName());
        controls.setTextBoxValue(driver, DESCRIPTION_INPUT_BY_ID, group.getDescription());
        controls.setTextBoxValue(driver, EMAIL_INPUT_BY_ID, group.getMail());
        // Controls.setCheckboxValueById(driver, ENABLED_CHECKBOX_BY_ID, group.disabled());
        controls.selectOptionByDisplayedText(driver, PROFILE_SELECT_LOCATOR, group.getProfile().getName());
        controls.addElementFromCollection(
                driver,
                MEMBER_INPUT_BY_ID, ADD_ALL_BUTTON_BY_XPATH,
                group.getGroupMembers().stream().map(Operator::getLoginName).collect(Collectors.toList())
        );
        clickSave(driver);
    }

    @Step("Update Group Members")
    public void updateGroupMembers(RemoteWebDriver driver, Group group) throws Exception {
        controls.addElementFromCollection(
                driver,
                MEMBER_INPUT_BY_ID, ADD_ALL_BUTTON_BY_XPATH,
                group.getGroupMembers().stream().map(Operator::getLoginName).collect(Collectors.toList())
        );
        clickSaveEditor(driver);
    }

    @Step("Add Operator")
    public void addOperator(RemoteWebDriver driver, String operator) throws InterruptedException {
        GROUP_MEMBER_PICKER.addAvailableItem(driver, operator);
        clickSaveEditor(driver);
    }
}