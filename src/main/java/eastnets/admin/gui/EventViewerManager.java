package eastnets.admin.gui;

import core.gui.Controls;
import core.util.Log;
import core.util.Wait;
import eastnets.admin.entity.EventViewer;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.util.ArrayList;
import java.util.List;

public class EventViewerManager {

    private static final By SEVERITY_INPUT_LOCATOR = By.id("eventViewerForm:homepage_business:severityCbx_label");
    private static final By CATEGORY_INPUT_LOCATOR = By.id("eventViewerForm:homepage_business:categoryCbx_label");
    private static final By TYPE_INPUT_LOCATOR = By.id("eventViewerForm:homepage_business:eventCode_label");
    private static final By APPLICATION_INPUT_LOCATOR = By.id("eventViewerForm:homepage_business:applicationCbx_label");
    private static final By MODULE_INPUT_LOCATOR = By.id("eventViewerForm:homepage_business:moduleCbx_label");
    private static final By ZONE_INPUT_LOCATOR = By.id("eventViewerForm:homepage_business:zoneCbx_label");
    private static final By MESSAGE_INPUT_LOCATOR = By.id("eventViewerForm:homepage_business:message");
    private static final By RESET_BUTTON_LOCATOR = By.id("eventViewerForm:homepage_business:btnReset");
    private static final By SEARCH_BUTTON_LOCATOR = By.id("eventViewerForm:homepage_business:btnEVEVSearch");
    private static final By RESULT_VIEW_LOCATOR = By.xpath("//tbody[@id='eventViewerForm:homepage_business:_tblResults_data']//tr");

    private final Controls controls;
    private final Wait wait;
    private final Log log;

    public EventViewerManager() {
        this.controls = new Controls();
        this.wait = new Wait();
        this.log = new Log();
    }

    @Step("Click search button")
    public void clickSearchButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SEARCH_BUTTON_LOCATOR);
    }

    @Step("Click reset button")
    public void clickResetButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, RESET_BUTTON_LOCATOR);
    }

    @Step("Search Event Viewer")
    public void search(RemoteWebDriver driver, EventViewer eventViewer) throws Exception {
        controls.selectOptionByDisplayedText(driver, SEVERITY_INPUT_LOCATOR, eventViewer.getSeverity());
        controls.selectOptionByDisplayedText(driver, CATEGORY_INPUT_LOCATOR, eventViewer.getCategory());
        controls.selectOptionByDisplayedText(driver, TYPE_INPUT_LOCATOR, eventViewer.getType());
        controls.setTextBoxValue(driver, MESSAGE_INPUT_LOCATOR, eventViewer.getMessage());
        controls.selectOptionByDisplayedText(driver, APPLICATION_INPUT_LOCATOR, eventViewer.getApplication());
        controls.selectOptionByDisplayedText(driver, MODULE_INPUT_LOCATOR, eventViewer.getModule());
        controls.selectOptionByDisplayedText(driver, ZONE_INPUT_LOCATOR, eventViewer.getZone());

    }

    @Step("Get Event Data From Result Table")
    public List<EventViewer> getEventDataFromResultTable(RemoteWebDriver driver) throws InterruptedException {

        wait.time(Wait.ONE_SECOND * 3);
        List<WebElement> tableRows = driver.findElements(RESULT_VIEW_LOCATOR);
        List<EventViewer> eventViewers = new ArrayList<>();
        EventViewer eventViewer = new EventViewer();
        Allure.step("#################### Event Viewer Table Results ####################");
        for (WebElement row : tableRows) {
            wait.time(Wait.ONE_SECOND);
            List<WebElement> rowCells = row.findElements(By.tagName("td"));
            eventViewer = new EventViewer();
            eventViewer.setCreationDate(rowCells.get(0).getText());
            eventViewer.setCode(rowCells.get(1).getText());
            eventViewer.setSeverity(rowCells.get(2).getText());
            eventViewer.setMessage(rowCells.get(3).getText());
            eventViewer.setApplication(rowCells.get(4).getText());
            eventViewer.setModule(rowCells.get(5).getText());
            eventViewer.setZone(rowCells.get(6).getText());

            log.info(eventViewer.toString());
            eventViewers.add(eventViewer);

        }
        return eventViewers;
    }
}