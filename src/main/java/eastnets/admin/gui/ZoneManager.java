package eastnets.admin.gui;

import core.exception.AlreadyExistsException;
import core.gui.Controls;
import eastnets.admin.entity.Zone;
import eastnets.common.control.CommonAction;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class ZoneManager {
    private static final By RESET_SEARCH_INPUT_BY_ID = By.id("zoneForm:homepage_business:btnReset");
    private static final By ID_SEARCH_INPUT_BY_ID = By.id("zoneForm:homepage_business:zoneId");
    private static final By NAME_SEARCH_INPUT_BY_ID = By.id("zoneForm:homepage_business:zoneName");
    private static final By DISPLAY_NAME_SEARCH_INPUT_BY_ID = By.id("zoneForm:homepage_business:zoneDispName");
    private static final By SEARCH_BUTTON_BY_ID = By.id("zoneForm:homepage_business:btnSearch");
    private static final By NEW_BUTTON_BY_ID = By.id("zoneForm:homepage_business:_tblResults:_btnNew");

    private static final String ZONE_RESULT_FIRST_ENTRY_PREFIX = "//tbody[@id=\"zoneForm:homepage_business:_tblResults_data\"]/tr";
    private static final By FIRST_SEARCH_ENTRY_ID = By.xpath(ZONE_RESULT_FIRST_ENTRY_PREFIX + "/td[1]/a");
    private static final By FIRST_SEARCH_ENTRY_NAME = By.xpath(ZONE_RESULT_FIRST_ENTRY_PREFIX + "/td[2]/a");
    private static final By FIRST_SEARCH_ENTRY_DESCRIPTION = By.xpath(ZONE_RESULT_FIRST_ENTRY_PREFIX + "/td[3]");
    private static final By FIRST_SEARCH_ENTRY_DISPLAY = By.xpath(ZONE_RESULT_FIRST_ENTRY_PREFIX + "/td[4]");

    private final Controls controls;
    private final CommonAction commonAction;

    public ZoneManager() {
        this.controls = new Controls();
        this.commonAction = new CommonAction();
    }

    @Step("Search For Zone By Name")
    private void searchForZoneByName(RemoteWebDriver driver, Zone zone) throws InterruptedException {
        Allure.step("Click reset button.");
        commonAction.resetSearch(driver, RESET_SEARCH_INPUT_BY_ID);

        if (zone.getId() > 0) {
            Allure.step("Enter ID =" + zone.getId());
            controls.setTextBoxValue(driver, ID_SEARCH_INPUT_BY_ID, zone.getId());
        }
        if (zone.getName() != null) {
            Allure.step("Enter name =" + zone.getName());
            controls.setTextBoxValue(driver, NAME_SEARCH_INPUT_BY_ID, zone.getName());
        }
        if (zone.getDisplayName() != null) {
            Allure.step("Enter display Name =" + zone.getDisplayName());
            controls.setTextBoxValue(driver, DISPLAY_NAME_SEARCH_INPUT_BY_ID, zone.getDisplayName());
        }

        Allure.step("Click search button.");
        commonAction.startSearch(driver, SEARCH_BUTTON_BY_ID);
    }

    @Step("Verify Zone Exist")
    public boolean verifyZoneExist(RemoteWebDriver driver, Zone zone) throws InterruptedException {
        waitVisible(driver);
        searchForZoneByName(driver, zone);
        return controls.exists(driver, By.xpath(String.format("//a[.=\"%s\"]", zone.getName())));
    }

    @Step("Create New Zone")
    public void createNewZone(RemoteWebDriver driver, Zone zone) throws AlreadyExistsException, InterruptedException {
        waitVisible(driver);
        Allure.step(String.format("Check if zone with name = '%s' exists", zone.getName()));
        if (verifyZoneExist(driver, zone)) {
            Allure.step("Zone already exists");
            throw new AlreadyExistsException(String.format("Zone '%s' already exists", zone.getName()));
        }
        Allure.step("Zone doesn't exist.");
        Allure.step("Click add new zone button.");
        controls.performClickByJS(driver, NEW_BUTTON_BY_ID);
    }

    @Step("Wait Visible")
    public void waitVisible(RemoteWebDriver driver) {
        commonAction.waitPageVisible(driver, SEARCH_BUTTON_BY_ID);
    }
}
