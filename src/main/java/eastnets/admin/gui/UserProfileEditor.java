package eastnets.admin.gui;

import core.gui.Controls;
import core.util.Wait;
import eastnets.common.control.CommonAction;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.time.Duration;

public class UserProfileEditor {
    //Menu Locators
    private static final By User_PROFILE_LOCATOR = By.cssSelector(".fa-street-view");
    private static final By CHANGE_LANGUAGE_LOCATOR= By.xpath("//ul[@class = 'fadeInDown']/li[2]/a");
    private static final By CHANGE_PASSWORD_LOCATOR = By.linkText("Change Password...");
    private static final By ABOUT_YOUR_SESSION_LOCATOR = By.linkText("About Your Session");
    private static final By THEME_CUSTOMIZATION_LOCATOR = By.linkText("Theme Customization...");
    private static final By LANGUAGE_DDL_LOCATOR = By.id("topbar-right:selectedLanguage_label");
    private static final By ClOSE_BUTTON_LOCATOR = By.xpath("/html/body/div[1]/div[1]/div/div[3]/ul/form[1]/div[4]/div[3]/span/button");

    //Change Password Form Locators
    private static final By OLD_PASSWORD_LOCATOR_BY_ID = By.id("change_password_business_internal:pageChangePasswordForm:oldPassword");
    private static final By NEW_PASSWORD_LOCATOR_BY_ID = By.id("change_password_business_internal:pageChangePasswordForm:newPassword");
    private static final By CONFIRM_NEW_PASSWORD_LOCATOR_BY_ID = By.id("change_password_business_internal:pageChangePasswordForm:confirmNewPassword");
    private static final By SAVE_BUTTON_LOCATOR_BY_ID = By.id("change_password_business_internal:pageChangePasswordForm:applyPasswordChangeBtn");

    private final Controls controls;
    private final Wait wait;
    private final CommonAction commonAction;

    public UserProfileEditor() {
        this.controls = new Controls();
        this.wait = new Wait();
        this.commonAction = new CommonAction();
    }

    @Step("Click User Profile Menu button")
    private void click_User_Profile(RemoteWebDriver driver) {
        controls.performClick(driver, User_PROFILE_LOCATOR);
    }

    @Step("Click Change Language button")
    public String click_Change_Language(RemoteWebDriver driver) {
        click_User_Profile(driver);
        wait.elementVisible(driver, CHANGE_LANGUAGE_LOCATOR, Duration.ofSeconds(5));
        controls.performClick(driver, CHANGE_LANGUAGE_LOCATOR);
        return driver.findElement(LANGUAGE_DDL_LOCATOR).getText();
    }

    @Step("Click Change Password button")
    public void click_Change_Password(RemoteWebDriver driver) {
        driver.navigate().refresh();
        click_User_Profile(driver);
        wait.elementVisible(driver, CHANGE_PASSWORD_LOCATOR, Duration.ofSeconds(5));
        controls.performClick(driver, CHANGE_PASSWORD_LOCATOR);
    }

    @Step("Click About Your Session button")
    public void click_About_Your_Session(RemoteWebDriver driver) {
        click_User_Profile(driver);
        wait.elementVisible(driver, ABOUT_YOUR_SESSION_LOCATOR, Duration.ofSeconds(5));
        controls.performClick(driver, ABOUT_YOUR_SESSION_LOCATOR);
    }

    @Step("Click Theme Customization button")
    public void click_Theme_Customization(RemoteWebDriver driver) {
        click_User_Profile(driver);
        wait.elementVisible(driver, THEME_CUSTOMIZATION_LOCATOR, Duration.ofSeconds(5));
        controls.performClick(driver, THEME_CUSTOMIZATION_LOCATOR);
    }

    @Step("Change Password")
    public String change_Password(RemoteWebDriver driver, String oldPassword, String newPassword) throws InterruptedException {
        Allure.step("Set Old Password.");
        controls.setTextBoxValue(driver, OLD_PASSWORD_LOCATOR_BY_ID, oldPassword);

        Allure.step("Set New Password.");
        controls.setTextBoxValue(driver, NEW_PASSWORD_LOCATOR_BY_ID, newPassword);

        Allure.step("Set Confirm New Password.");
        controls.setTextBoxValue(driver, CONFIRM_NEW_PASSWORD_LOCATOR_BY_ID, newPassword);

        Allure.step("click Save button.");
        controls.performClickByJS(driver, SAVE_BUTTON_LOCATOR_BY_ID);


            String validationMessage = commonAction.getAlertMessageString(driver);
            Allure.step(String.format("Validation message = %s .",validationMessage));
            return validationMessage;
        }
    @Step("change Password button")
    public void change_Language(RemoteWebDriver driver, String language) throws Exception {
        click_Change_Language(driver);
        controls.selectOptionByDisplayedText(driver,LANGUAGE_DDL_LOCATOR, language);
        controls.performClickByJS(driver, ClOSE_BUTTON_LOCATOR);
    }
    }