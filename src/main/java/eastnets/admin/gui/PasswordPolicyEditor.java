package eastnets.admin.gui;

import core.gui.Controls;
import core.util.Wait;
import eastnets.admin.entity.PasswordPolicy;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class PasswordPolicyEditor {

    private static final By MINIMUM_LENGTH_INPUT_LOCATOR = By.id("operatorManagerForm:detail_business:_minimumLength");
    private static final By HISTORY_COUNT_INPUT_LOCATOR = By.id("operatorManagerForm:detail_business:_historyCount");
    private static final By UPPER_CASE_COUNT_INPUT_LOCATOR = By.id("operatorManagerForm:detail_business:_uppercaseCount");
    private static final By LOWER_CASE_COUNT_INPUT_LOCATOR = By.id("operatorManagerForm:detail_business:_lowercaseCount");
    private static final By DIGITS_COUNT_INPUT_LOCATOR = By.id("operatorManagerForm:detail_business:_digitsCount");
    private static final By SPECIAL_CHARACTERS_COUNT_INPUT_LOCATOR = By.id("operatorManagerForm:detail_business:_specialCharsCount");
    private static final String ALLOW_CONSECUTIVE_CHECK_BOX_LOCATOR = "operatorManagerForm:detail_business:_allowConsecutiveFlag";
    private static final String ENABLE_CHECK_BOX_LOCATOR = "operatorManagerForm:detail_business:_enabledFlag";
    private static final By SAVE_BUTTON_LOCATOR = By.id("operatorManagerForm:detail_business:btnSavePass");

    private final Controls controls;
    private final Wait wait;

    public PasswordPolicyEditor() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    @Step("Set Password Policy Details")
    public void setPasswordPolicyDetails(RemoteWebDriver driver, PasswordPolicy passwordPolicy) throws InterruptedException {

        Allure.step(String.format("Set Status = %s .", Boolean.parseBoolean(passwordPolicy.getStatusFlag())));
        controls.setCheckboxValueById(driver, ENABLE_CHECK_BOX_LOCATOR, Boolean.parseBoolean(passwordPolicy.getStatusFlag()));

        Allure.step(String.format("Set Prevent Consecutive = %s .", Boolean.parseBoolean(passwordPolicy.getPreventConsecutiveFlag())));
        controls.setCheckboxValueById(driver, ALLOW_CONSECUTIVE_CHECK_BOX_LOCATOR, Boolean.parseBoolean(passwordPolicy.getPreventConsecutiveFlag()));

        Allure.step(String.format("Set Minimum Length = %s .", passwordPolicy.getMinimumLength()));
        controls.setTextBoxValue(driver, MINIMUM_LENGTH_INPUT_LOCATOR, passwordPolicy.getMinimumLength());

        Allure.step(String.format("Set History Count = %s .", passwordPolicy.getHistoryCount()));
        controls.setTextBoxValue(driver, HISTORY_COUNT_INPUT_LOCATOR, passwordPolicy.getHistoryCount());

        Allure.step(String.format("Set Minimum [A-Z] = %s .", passwordPolicy.getMinimumUpperCaseCount()));
        controls.setTextBoxValue(driver, UPPER_CASE_COUNT_INPUT_LOCATOR, passwordPolicy.getMinimumUpperCaseCount());

        Allure.step(String.format("Set Minimum [a-z] = %s .", passwordPolicy.getMinimumLowerCaseCount()));
        controls.setTextBoxValue(driver, LOWER_CASE_COUNT_INPUT_LOCATOR, passwordPolicy.getMinimumLowerCaseCount());

        Allure.step(String.format("Set Minimum [0-9] = %s .", passwordPolicy.getMinimumDigitsCount()));
        controls.setTextBoxValue(driver, DIGITS_COUNT_INPUT_LOCATOR, passwordPolicy.getMinimumDigitsCount());

        Allure.step(String.format("Set Minimum Specials = %s .", passwordPolicy.getMinimumSpecialCharactersCount()));
        controls.setTextBoxValue(driver, SPECIAL_CHARACTERS_COUNT_INPUT_LOCATOR, passwordPolicy.getMinimumSpecialCharactersCount());

        Allure.step("Click save button.");
        controls.performClickByJS(driver, SAVE_BUTTON_LOCATOR);
        driver.switchTo().alert().accept();
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

}
