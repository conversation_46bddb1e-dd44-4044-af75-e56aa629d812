package eastnets.admin.gui;

import core.gui.Controls;
import core.util.Randomizer;
import eastnets.admin.entity.Report;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class ReportEditor {

    private static final By COMMENT_TEXT_AREA_LOCATOR = By.xpath("//span[contains(text(),'Comment')]/ancestor::div[1]//textarea");
    private static final By USER_NAME_LOCATOR = By.xpath("//span[contains(text(),'UserName')]/ancestor::td[1]//following-sibling::td//input");
    private static final By INACTIVITY_DAYS_INPUT_LOCATOR = By.xpath("//span[contains(text(),'Inactivity days')]/ancestor::td[1]//following-sibling::td//input");
    private static final By APPLICATION_DROP_DOWN_LOCATOR = By.xpath("//span[contains(text(),'Application')]/ancestor::td[1]//following-sibling::td//label");
    private static final By ZONE_DROP_DOWN_LOCATOR = By.xpath("//span[.='Zone' or .='Zone:']/ancestor::td[1]//following-sibling::td//label");
    private static final By MODULE_DROP_DOWN_LOCATOR = By.xpath("//span[.='Module:' or .='Module']/ancestor::td[1]//following-sibling::td//label");
    private static final By GROUP_DROP_DOWN_LOCATOR = By.xpath("//span[.='Group']/ancestor::td[1]//following-sibling::td//label");
    private static final By OPERATOR_INPUT_LOCATOR = By.xpath("//span[.='OperatorName' or .='Operator:']/ancestor::td[1]//following-sibling::td//input");
    private static final By PROFILE_DROP_DOWN_LOCATOR = By.xpath("//span[.='Profile:' or .='Profile']/ancestor::td[1]//following-sibling::td//label");
    private static final By DEPARTMENT_INPUT_LOCATOR = By.xpath("//span[.='Department']/ancestor::td[1]//following-sibling::td//input");
    private static final By PROFILE_1_DROP_DOWN_LOCATOR = By.xpath("//span[.='Profile1' or .='Profile1:']/ancestor::td[1]//following-sibling::td//label");
    private static final By PROFILE_2_DROP_DOWN_LOCATOR = By.xpath("//span[.='Profile2' or .='Profile2:']/ancestor::td[1]//following-sibling::td//label");
    private static final By TARGET_FILE_NAME_LOCATOR = By.xpath("//span[.='Target file name:']//following-sibling::input");
    private static final By EXECUTE_BUTTON_LOCATOR = By.id("reportViewerForm:viewer_business:btnExecute");

    private final Controls controls;
    private final Randomizer randomizer;

    public ReportEditor() {
        this.controls = new Controls();
        this.randomizer = new Randomizer();
    }

    @Step("Create New Report")
    public void createNewReport(RemoteWebDriver driver, Report report) throws Exception {
        Allure.step(String.format("Fill create new %s report form.", report.getReportName()));

        report.setComment(String.format(report.getComment(), randomizer.getInt()));
        Allure.step("Set comment = " + report.getComment());
        controls.setTextBoxValue(driver, COMMENT_TEXT_AREA_LOCATOR, report.getComment());

        if (report.getUserName() != null && report.getUserName() != "") {
            report.setUserName(String.format(report.getUserName(), randomizer.getInt()));
            Allure.step("Set user name = " + report.getUserName());
            controls.setTextBoxValue(driver, USER_NAME_LOCATOR, report.getUserName());
        }

        if (report.getInactivityDays() != null && report.getInactivityDays() != "") {
            Allure.step("Set inactivity days = " + report.getInactivityDays());
            controls.setTextBoxValue(driver, INACTIVITY_DAYS_INPUT_LOCATOR, report.getInactivityDays());
        }

        if (report.getApplication() != null && report.getApplication() != "") {
            Allure.step("Set application = " + report.getApplication());
            controls.selectOptionByDisplayedText(driver, APPLICATION_DROP_DOWN_LOCATOR, report.getApplication());
        }

        if (report.getZone() != null && report.getZone() != "") {
            Allure.step("Set zone = " + report.getZone());
            controls.selectOptionByDisplayedText(driver, ZONE_DROP_DOWN_LOCATOR, report.getZone());
        }

        if (report.getModule() != null && report.getModule() != "") {
            Allure.step("Set module = " + report.getModule());
            controls.selectOptionByDisplayedText(driver, MODULE_DROP_DOWN_LOCATOR, report.getModule());
        }

        if (report.getGroup() != null && report.getGroup() != "") {
            Allure.step("Set group = " + report.getGroup());
            controls.selectOptionByDisplayedText(driver, GROUP_DROP_DOWN_LOCATOR, report.getGroup());
        }

        if (report.getOperator() != null && report.getOperator() != "") {
            Allure.step("Set operator = " + report.getOperator());
            controls.setTextBoxValue(driver, OPERATOR_INPUT_LOCATOR, report.getOperator());
        }

        if (report.getProfile() != null && report.getProfile() != "") {
            Allure.step("Set profile = " + report.getProfile());
            controls.selectOptionByDisplayedText(driver, PROFILE_DROP_DOWN_LOCATOR, report.getProfile());
        }

        if (report.getDepartment() != null && report.getDepartment() != "") {
            Allure.step("Set department = " + report.getDepartment());
            controls.setTextBoxValue(driver, DEPARTMENT_INPUT_LOCATOR, report.getDepartment());
        }

        if (report.getProfile2() != null && report.getProfile2() != "") {
            Allure.step("Set profile2 = " + report.getProfile2());
            controls.selectOptionByDisplayedText(driver, PROFILE_2_DROP_DOWN_LOCATOR, report.getProfile2());
        }

        if (report.getProfile1() != null && report.getProfile1() != "") {
            Allure.step("Set profile1 = " + report.getProfile1());
            controls.selectOptionByDisplayedText(driver, PROFILE_1_DROP_DOWN_LOCATOR, report.getProfile1());
        }

        String fileName = controls.getWebElement(driver, TARGET_FILE_NAME_LOCATOR).getAttribute("value");
        report.setTargetFileName(fileName);

        Allure.step("Click on Execute button.");
        controls.performClickByJS(driver, EXECUTE_BUTTON_LOCATOR);
    }

}
