package eastnets.admin.gui;

import core.gui.Controls;
import core.gui.Picker;
import core.util.Wait;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class ModuleEditor {
    private static final Logger LOG = LoggerFactory.getLogger(ModuleEditor.class);

    private static final By SAVE_BUTTON_BY_ID = By.id("ModulesDetailForm:detail_business:btnSaveModifyModules");
    private static final By CANCEL_BUTTON_BY_ID = By.id("ModulesDetailForm:detail_business:btnCancelModules");
    private static final By FUNCTION_PANEL_BY_ID = By.id("ModulesDetailForm:detail_business:functionPanelId");
    private static final Picker MODULE_PICKER = new Picker("ModulesDetailForm:detail_business:modulePickListId");
    private static final Picker FUNCTION_PICKER = new Picker("ModulesDetailForm:detail_business:functionPickListId");

    private final Controls controls;
    private final Wait wait;

    public ModuleEditor() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    @Step("Edit Module Right")
    private void editModuleRight(RemoteWebDriver driver, Map.Entry<String, List<String>> module) throws InterruptedException {
        Allure.step(String.format("Processing module '%s'", module.getKey()));
        MODULE_PICKER.clickOnSelectedItem(driver, module.getKey());
        wait.waitUntilElementToBeVisible(driver, FUNCTION_PANEL_BY_ID, Wait.ONE_MINUTE_DURATION);

        FUNCTION_PICKER.removeAllIfEnabled(driver);
        List<String> functionList = module.getValue();

        if (functionList.size() == 1 && functionList.get(0).equalsIgnoreCase("ALL")) {
            Allure.step("Selecting all function for module");
            if (!FUNCTION_PICKER.hasAvailableItem(driver))
                return;

            FUNCTION_PICKER.addAllAvailableItem(driver);
            return;
        }

        FUNCTION_PICKER.addAvailableItem(driver, functionList);
    }

    @Step("Edit Module Right")
    public void editModuleRight(RemoteWebDriver driver, Map<String, List<String>> moduleRights) throws InterruptedException {
        Allure.step("Select Module Rights.");
        for (Map.Entry<String, List<String>> module : moduleRights.entrySet()) {
            wait.time(Wait.ONE_SECOND);
            if (!module.getValue().isEmpty())
                editModuleRight(driver, module);
        }
        Allure.step("Click save button.");
        controls.performClickByJS(driver, SAVE_BUTTON_BY_ID);
        Allure.step("Accept alert.");
        driver.switchTo().alert().accept();
    }

    @Step("Edit Application Right")
    public void editApplicationRight(RemoteWebDriver driver, Map<String, List<String>> moduleRights) throws InterruptedException {
        MODULE_PICKER.removeAllIfEnabled(driver);
        MODULE_PICKER.addAvailableItem(driver, new ArrayList<>(moduleRights.keySet()));
        Allure.step("Click save button.");
        wait.waitUntilAjaxLoaderDisappear(driver);
        controls.performClickByJS(driver, SAVE_BUTTON_BY_ID);
        Allure.step("Accept Alert");
        controls.acceptAlert(driver);
        //Wait.time(Wait.ONE_SECOND*5);

    }
}