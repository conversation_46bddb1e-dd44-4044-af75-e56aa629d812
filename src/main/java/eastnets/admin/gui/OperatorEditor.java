package eastnets.admin.gui;

import core.gui.Controls;
import core.util.Wait;
import eastnets.admin.entity.Operator;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class OperatorEditor {
    public static final By LOGIN_NAME_INPUT_BY_ID = By.id("operatorManagerDetailForm:detail_business:loginName");
    public static final By FIRSTNAME_INPUT_BY_ID = By.id("operatorManagerDetailForm:detail_business:firstName");
    public static final By LASTNAME_INPUT_BY_ID = By.id("operatorManagerDetailForm:detail_business:lastName");
    public static final By PASSWORD_INPUT_BY_ID = By.id("operatorManagerDetailForm:detail_business:password");
    public static final By CONF_PASSWORD_INPUT_BY_ID = By.id("operatorManagerDetailForm:detail_business:confPassword");
    public static final String ENABLED_FLAG_ID = "operatorManagerDetailForm:detail_business:enabledFlag";
    public static final String RESET_FLAG_ID = "operatorManagerDetailForm:detail_business:resetFlag";
    public static final By SAVE_BUTTON_BY_ID = By.id("operatorManagerDetailForm:detail_business:btnSaveCreate");
    public static final By SAVE_AFTER_MODIFICATION_BUTTON_BY_ID = By.id("operatorManagerDetailForm:detail_business:btnSaveModify");
    public static final By CANCEL_BUTTON_BY_ID = By.id("operatorManagerDetailForm:detail_business:btnCancel");

    public static final By ZONE_SELECT_LOCATOR = By.id("operatorManagerDetailForm:detail_business:zoneCbx");
    public static final By PASSWORD_POLICY_LOCATOR = By.id("operatorManagerDetailForm:detail_business:changePasswordPolicyId");
    private static final By CHECK_LABEL_LOCATOR = By.id("operatorManagerForm:detail_business:_minimumLength");
    public static final String LOCK_CHECKBOX_LOCATOR = "operatorManagerDetailForm:detail_business:lockedFlag";

    private final Controls controls;
    private final Wait wait;

    public OperatorEditor() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    @Step("Create Operator")
    public void createOperator(RemoteWebDriver driver, Operator operator) throws Exception {
        Allure.step(String.format("Set login name = %s", operator.getLoginName()));
        controls.setTextBoxValue(driver, LOGIN_NAME_INPUT_BY_ID, operator.getLoginName());
        Allure.step(String.format("Set first name = %s", operator.getFirstName()));
        controls.setTextBoxValue(driver, FIRSTNAME_INPUT_BY_ID, operator.getFirstName());
        Allure.step(String.format("Set last name = %s", operator.getLastName()));
        controls.setTextBoxValue(driver, LASTNAME_INPUT_BY_ID, operator.getLastName());
        Allure.step(String.format("Set password = %s", operator.getPassword()));
        controls.setTextBoxValue(driver, PASSWORD_INPUT_BY_ID, operator.getPassword());
        Allure.step(String.format("Set confirm password = %s", operator.getPassword()));
        controls.setTextBoxValue(driver, CONF_PASSWORD_INPUT_BY_ID, operator.getPassword());
        Allure.step(String.format("Set enable flag = %s", true));
        controls.setCheckboxValueById(driver, ENABLED_FLAG_ID, true);
        Allure.step(String.format("Set reset flag = %s", false));
        controls.setCheckboxValueById(driver, RESET_FLAG_ID, false);
        Allure.step(String.format("Select zone  = %s", operator.getZone().getDisplayName()));
        controls.selectOptionByDisplayedText(driver, ZONE_SELECT_LOCATOR, operator.getZone().getDisplayName());
        Allure.step("Click save button.");
        controls.performClickByJS(driver, SAVE_BUTTON_BY_ID);
        Allure.step("Accept alert.");
        driver.switchTo().alert().accept();
        wait.time(Wait.ONE_SECOND * 5);
    }

    @Step("Create disabled Operator")
    public void createDisabledOperator(RemoteWebDriver driver, Operator operator) throws Exception {
        Allure.step(String.format("Set login name = %s", operator.getLoginName()));
        controls.setTextBoxValue(driver, LOGIN_NAME_INPUT_BY_ID, operator.getLoginName());
        Allure.step(String.format("Set first name = %s", operator.getFirstName()));
        controls.setTextBoxValue(driver, FIRSTNAME_INPUT_BY_ID, operator.getFirstName());
        Allure.step(String.format("Set last name = %s", operator.getLastName()));
        controls.setTextBoxValue(driver, LASTNAME_INPUT_BY_ID, operator.getLastName());
        Allure.step(String.format("Set password = %s", operator.getPassword()));
        controls.setTextBoxValue(driver, PASSWORD_INPUT_BY_ID, operator.getPassword());
        Allure.step(String.format("Set confirm password = %s", operator.getPassword()));
        controls.setTextBoxValue(driver, CONF_PASSWORD_INPUT_BY_ID, operator.getPassword());
        Allure.step(String.format("Set enable flag = %s", true));
        controls.setCheckboxValueById(driver, ENABLED_FLAG_ID, false);
        Allure.step(String.format("Set reset flag = %s", false));
        controls.setCheckboxValueById(driver, RESET_FLAG_ID, false);
        Allure.step(String.format("Select zone  = %s", operator.getZone().getDisplayName()));
        controls.selectOptionByDisplayedText(driver, ZONE_SELECT_LOCATOR, operator.getZone().getDisplayName());
        Allure.step("Click save button.");
        controls.performClickByJS(driver, SAVE_BUTTON_BY_ID);
        Allure.step("Accept alert.");
        driver.switchTo().alert().accept();
        wait.time(Wait.ONE_SECOND * 5);
    }

    public void lockOperator(RemoteWebDriver driver) {
        Allure.step("check lock flag = true");
        controls.setCheckboxValueById(driver, LOCK_CHECKBOX_LOCATOR, true);
        Allure.step("Click save button.");
        controls.performClickByJS(driver, SAVE_AFTER_MODIFICATION_BUTTON_BY_ID);
        controls.acceptAlert(driver);
    }

    @Step("Check If Operator Editable")
    public boolean checkIfOperatorEditable(RemoteWebDriver driver) {
        if (!controls.getWebElement(driver, LOGIN_NAME_INPUT_BY_ID).getAttribute("aria-readonly").contains("true")
                && controls.getWebElement(driver, FIRSTNAME_INPUT_BY_ID).getAttribute("aria-readonly").contains("true")
                && controls.getWebElement(driver, LASTNAME_INPUT_BY_ID).getAttribute("aria-readonly").contains("true"))
            return true;

        return false;
    }

    @Step("Verify If Enable Box Enabled")
    public boolean verifyIfEnableBoxEnabled(RemoteWebDriver driver) {
        if (!controls.getWebElement(driver, By.xpath(String.format("//div[@id='%s']//div[2]", ENABLED_FLAG_ID))).getAttribute("class").contains("ui-state-disabled"))
            return true;
        return false;
    }

    @Step("Click on Password Policy Link Text")
    public boolean clickPasswordPolicy(RemoteWebDriver driver) {
        controls.performClick(driver, PASSWORD_POLICY_LOCATOR);
        return controls.exists(driver, CHECK_LABEL_LOCATOR)
                && controls.getWebElement(driver, CHECK_LABEL_LOCATOR).getAttribute("aria-disabled").contains("true");
    }
}