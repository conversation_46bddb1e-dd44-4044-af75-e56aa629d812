package eastnets.admin.gui;

import core.exception.AlreadyExistsException;
import core.exception.EntityNotExistsException;
import core.gui.Controls;
import core.util.Wait;
import eastnets.admin.entity.Group;
import eastnets.common.control.CommonAction;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class GroupManager {

    private static final By NAME_INPUT_BY_ID = By.id("operatorGroupForm:homepage_business:name");
    private static final By DESCRIPTION_INPUT_BY_ID = By.id("operatorGroupForm:homepage_business:description");
    private static final By OPERATOR_NAME_INPUT_BY_ID = By.id("operatorGroupForm:homepage_business:operatorName");
    private static final By OPERATOR_GROUPNAME_INPUT_BY_ID = By.id("operatorGroupForm:homepage_business:operatorGroupName");
    private static final By RESET_BUTTON_BY_ID = By.id("operatorGroupForm:homepage_business:btnReset");
    private static final By SEARCH_BUTTON_BY_ID = By.id("operatorGroupForm:homepage_business:btnSearch");
    private static final By NEW_GROUP_BUTTON_BY_ID = By.id("operatorGroupForm:homepage_business:_tblResults:_btnNewGroup");
    private static final By REMOVE_BUTTON_BY_ID = By.id("operatorGroupForm:homepage_business:_tblResults:_btnDeleteGroup");
    private static final String FIRST_ENTRY_PREFIX = "//tbody[@id=\"operatorGroupForm:homepage_business:_tblResults_data\"]/tr";
    private static final String FIRST_ENTRY_NAME_RESULT_TABLE = FIRST_ENTRY_PREFIX + "/td[3]/a";
    private static final By CHECKBOX_LOCATOR = By.xpath("//*[@class='ui-selectbooleancheckbox ui-chkbox ui-widget']");

    private final Controls controls;
    private final Wait wait;
    private final CommonAction commonAction;

    public GroupManager() {
        this.controls = new Controls();
        this.wait = new Wait();
        this.commonAction = new CommonAction();
    }

    @Step("Search By Group Info")
    public void searchByGroupInfo(RemoteWebDriver driver, Group group) throws InterruptedException {
        commonAction.resetSearch(driver, RESET_BUTTON_BY_ID);
        if (group.getName() != null && !group.getName().equals(""))
            controls.setTextBoxValue(driver, NAME_INPUT_BY_ID, group.getName());
        if (group.getDescription() != null && !group.getDescription().equals(""))
            controls.setTextBoxValue(driver, DESCRIPTION_INPUT_BY_ID, group.getDescription());
        commonAction.startSearch(driver, SEARCH_BUTTON_BY_ID);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Search By Group Info")
    public void searchByGroupDescription(RemoteWebDriver driver, String groupDescription) throws InterruptedException {
        commonAction.resetSearch(driver, RESET_BUTTON_BY_ID);
        controls.setTextBoxValue(driver, DESCRIPTION_INPUT_BY_ID, groupDescription);
        commonAction.startSearch(driver, SEARCH_BUTTON_BY_ID);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Creating New Group")
    public void creatingNewGroup(RemoteWebDriver driver, Group group) throws AlreadyExistsException, InterruptedException {
        waitVisible(driver);
        if (verifyGroupExists(driver, group)) {
            Allure.step(String.format("Group with name '%s' already exists", group.getName()));
            throw new AlreadyExistsException(String.format("Group %s already exists", group.getName()));
        }

        controls.performClickByJS(driver, NEW_GROUP_BUTTON_BY_ID);
    }

    @Step("Verify Group Exists")
    public boolean verifyGroupExists(RemoteWebDriver driver, Group group) throws InterruptedException {
        waitVisible(driver);
        searchByGroupInfo(driver, group);
        return controls.exists(driver, By.xpath(String.format("//tbody//tr//td[.='%s']", group.getName())));
    }


    @Step("Verify Group Exists")
    public boolean verifyGroupExistsByDescription(RemoteWebDriver driver, Group group) throws InterruptedException {
        waitVisible(driver);
        searchByGroupDescription(driver, group.getDescription());
        return controls.exists(driver, By.xpath(String.format("//tbody//tr//td[.='%s']", group.getName())));
    }

    @Step("Wait Visible")
    public void waitVisible(RemoteWebDriver driver) {
        commonAction.waitPageVisible(driver, SEARCH_BUTTON_BY_ID);
    }

    @Step("Edit Group")
    public void editGroup(RemoteWebDriver driver, Group group) throws InterruptedException, EntityNotExistsException {
        waitVisible(driver);
        if (!verifyGroupExistsByDescription(driver, group)) {
            Allure.step(String.format("Group with name '%s' does not exist", group.getName()));
            throw new EntityNotExistsException(String.format("Group with name '%s' does not exist", group.getName()));
        }
        controls.performClickByJS(driver, By.xpath(FIRST_ENTRY_NAME_RESULT_TABLE));
    }

    @Step("Remove Group")
    public boolean removeGroup(RemoteWebDriver driver, Group group) throws InterruptedException {
        searchByGroupInfo(driver, group);
        Allure.step("Select group and click remove button.");
        controls.performClick(driver, CHECKBOX_LOCATOR);
        controls.performClickByJS(driver, REMOVE_BUTTON_BY_ID);
        driver.switchTo().alert().accept();
        return verifyGroupExists(driver, group);
    }
}