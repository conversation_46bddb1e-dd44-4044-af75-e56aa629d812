package eastnets.admin.gui;

import core.exception.AlreadyExistsException;
import core.exception.EntityNotExistsException;
import core.gui.Controls;
import core.util.Wait;
import eastnets.admin.entity.Profile;
import eastnets.common.control.CommonAction;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.time.Duration;

public class ProfileManager {
    private static final By NAME_SEARCH_INPUT_BY_ID = By.id("authorizationProfileForm:homepage_business:name");
    private static final By ZONE_SELECT_INPUT_BY_ID = By.id("authorizationProfileForm:homepage_business:zone_input");
    private static final By RESET_BUTTON_BY_ID = By.id("authorizationProfileForm:homepage_business:btnReset");
    private static final By CLONE_BUTTON_LOCATOR = By.id("authorizationProfileForm:homepage_business:_tblResults:_btnClone");
    private static final By SEARCH_BUTTON_BY_ID = By.id("authorizationProfileForm:homepage_business:btnSearchAPM");
    private static final By NEW_PROFILE_BUTTON_BY_ID = By.id("authorizationProfileForm:homepage_business:_tblResults:_btnNewUser");
    private static final By REMOVE_PROFILE_BUTTON_BY_ID = By.id("authorizationProfileForm:homepage_business:_tblResults:_btnDeleteUser");
    private static final String FIRST_ENTRY_PREFIX = "//tbody[@id='authorizationProfileForm:homepage_business:_tblResults_data']/tr";
    private static final String FIRST_ENTRY_CHECKBOX_RESULT_TABLE = FIRST_ENTRY_PREFIX + "/td[1]/div";
    private static final String FIRST_ENTRY_CHECKBOX_RESULT_TABLE_INPUT = FIRST_ENTRY_CHECKBOX_RESULT_TABLE + "/div[1]/input";
    private static final String FIRST_ENTRY_NAME_RESULT_TABLE = FIRST_ENTRY_PREFIX + "/td[3]/a";
    private static final By CHECKBOX_LOCATOR = By.xpath("//*[@class='ui-selectbooleancheckbox ui-chkbox ui-widget']");

    private final Controls controls;
    private final Wait wait;
    private final CommonAction commonAction;

    public ProfileManager() {
        this.controls = new Controls();
        this.wait = new Wait();
        this.commonAction = new CommonAction();
    }

    @Step("Set Profile Name")
    public void setProfileName(RemoteWebDriver driver, String name) {
        controls.setTextBoxValue(driver, NAME_SEARCH_INPUT_BY_ID, name);
    }

    @Step("Search Profile")
    public void searchProfile(RemoteWebDriver driver, Profile profile) throws Exception {
        Allure.step("Click reset button.");
        commonAction.resetSearch(driver, RESET_BUTTON_BY_ID);

        Allure.step(String.format("Set name = %s ", profile.getName()));
        controls.setTextBoxValue(driver, NAME_SEARCH_INPUT_BY_ID, profile.getName());
        if (profile.getZone() != null) {
            long zoneId = profile.getZone().getId();
            if (zoneId > 0) {
                Allure.step(String.format("Set zone id = %s ", zoneId));
                controls.selectOptionByDisplayedText(driver, ZONE_SELECT_INPUT_BY_ID, Long.toString(zoneId));
            }
        }
        Allure.step("Click search button.");
        commonAction.startSearch(driver, SEARCH_BUTTON_BY_ID);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Click search button")
    public void clickSearchButton(RemoteWebDriver driver) throws InterruptedException {
        commonAction.startSearch(driver, SEARCH_BUTTON_BY_ID);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Verify Profile Exist")
    public boolean verifyProfileExist(RemoteWebDriver driver, Profile profile) throws Exception {
        searchProfile(driver, profile);
        return controls.exists(driver, By.xpath(String.format(FIRST_ENTRY_NAME_RESULT_TABLE + "/span[@title=\"%s\"]", profile.getName())));
    }

    @Step("Select Profile")
    public void selectProfile(RemoteWebDriver driver, Profile profile) throws Exception {
        if (!verifyProfileExist(driver, profile)) {
            Allure.step("Profile doesn't exist");
            throw new EntityNotExistsException(String.format("Profile %s doesn't exist", profile.getName()));
        }

        Allure.step("Select profile from result view.");
        controls.waitAndClick(driver, By.xpath(FIRST_ENTRY_NAME_RESULT_TABLE), Duration.ofSeconds(1));
    }

    @Step("Select Profile")
    public void selectProfile(RemoteWebDriver driver) throws InterruptedException {

        Allure.step("Click search button.");
        commonAction.startSearch(driver, SEARCH_BUTTON_BY_ID);
        wait.waitUntilAjaxLoaderDisappear(driver);
        Allure.step("Select profile from result view.");
        controls.waitAndClick(driver, By.xpath(FIRST_ENTRY_NAME_RESULT_TABLE), Duration.ofSeconds(1));
    }

    @Step("Create New Profile")
    public void creatingNewProfile(RemoteWebDriver driver, Profile profile) throws Exception {
        if (verifyProfileExist(driver, profile)) {
            Allure.step("Profile already exists");
            throw new AlreadyExistsException(String.format("Profile %s already exists", profile));
        }

        clickCreateNewProfileButton(driver);
    }

    @Step("Click Create New Profile Button")
    public void clickCreateNewProfileButton(RemoteWebDriver driver) {
        Allure.step("Click add new profile button.");
        controls.performClickByJS(driver, NEW_PROFILE_BUTTON_BY_ID);
    }

    @Step("Remove Profile")
    public void removeProfile(RemoteWebDriver driver, Profile profile) throws Exception {
        searchProfile(driver, profile);
        if (!controls.isCheckBoxChecked(driver, By.xpath(FIRST_ENTRY_CHECKBOX_RESULT_TABLE_INPUT)))
            controls.performClickByJS(driver, By.xpath(FIRST_ENTRY_CHECKBOX_RESULT_TABLE));
        controls.performClickByJS(driver, REMOVE_PROFILE_BUTTON_BY_ID);
    }

    @Step("Verify Remove Button Exist")
    public boolean verifyRemoveButtonExist(RemoteWebDriver driver) {
        return controls.exists(driver, REMOVE_PROFILE_BUTTON_BY_ID);
    }

    @Step("Verify Clone Button Exist")
    public boolean verifyCloneButtonExist(RemoteWebDriver driver) {
        return controls.exists(driver, CLONE_BUTTON_LOCATOR);
    }

    @Step("Click Clone Profile")
    public void clickCloneProfile(RemoteWebDriver driver) {
        controls.waitAndClick(driver, CHECKBOX_LOCATOR, Duration.ofSeconds(0));
        controls.performClickByJS(driver, CLONE_BUTTON_LOCATOR);
    }

}
