package eastnets.admin.gui;

import core.gui.Controls;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class ReportManager {

    private static final By COMMENT_INPUT_LOCATOR = By.id("reportForm:homepage_business:descField");
    private static final By NEW_BUTTON_LOCATOR = By.id("reportForm:homepage_business:_tblResults:btnShow");
    private static final By DELETE_BUTTON_LOCATOR = By.id("reportForm:homepage_business:_tblResults:_btnDeleteGroup");
    private static final By REPORTS_LIST_LOCATOR = By.id("reportForm:homepage_business:_tblResults:reportListSelectCbx");
    private static final By SEARCH_BUTTON_LOCATOR = By.id("reportForm:homepage_business:btnSearch");
    private static final By RESET_BUTTON_LOCATOR = By.id("reportForm:homepage_business:btnReset");
    private static final By SORT_BY_CREATION_DATE_LOCATOR = By.xpath("//*[contains(@aria-label,'Creation Date')]");
    private static final By STATUS_CELL_LOCATOR = By.xpath("//table/tbody/tr/td[count(//span[.='Status']/ancestor::th/preceding-sibling::th)+1]");
    private static final By NAME_CELL_LOCATOR = By.id("reportForm:homepage_business:_tblResults:0:linkEdit");

    private final Controls controls;

    public ReportManager() {
        this.controls = new Controls();
    }

    @Step("Click Search Button")
    public void clickSearchButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SEARCH_BUTTON_LOCATOR);
    }

    @Step("Search By Comment")
    public void searchByComment(RemoteWebDriver driver, String comment) {
        Allure.step("Search by comment = " + comment);
        controls.setTextBoxValue(driver, COMMENT_INPUT_LOCATOR, comment);
        clickSearchButton(driver);
    }

    @Step("Click New Button")
    public void clickNewButton(RemoteWebDriver driver) {
        Allure.step("Click create new report button.");
        controls.performClickByJS(driver, NEW_BUTTON_LOCATOR);
    }

    @Step("Click Delete Button")
    public void clickDeleteButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, DELETE_BUTTON_LOCATOR);
    }

    @Step("Click Reset Button")
    public void clickResetButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, RESET_BUTTON_LOCATOR);
    }

    @Step("Select Report From List")
    public void selectReportFromList(RemoteWebDriver driver, String reportName) throws Exception {
        Allure.step(String.format("Select report ' %s ' from Reports dropdown", reportName));
        controls.selectOptionByDisplayedText(driver, REPORTS_LIST_LOCATOR, reportName);
    }

    @Step("Verify Report Exist")
    public boolean verifyReportExist(RemoteWebDriver driver, String commentString) {
        Allure.step("check if report Exist");
        if (controls.exists(driver, By.xpath(String.format("//tbody//tr//td[.='%s']", commentString)))) {
            Allure.step("Report Exist In search Results");
            return true;
        }
        return false;
    }

    @Step("Get Report Status")
    public String getReportStatus(RemoteWebDriver driver) {
        String status = controls.getElementText(driver, STATUS_CELL_LOCATOR);
        Allure.step("Report status = " + status);
        return status;
    }

    @Step("Click on report name to export")
    public void clickOnReportToExport(RemoteWebDriver driver) {
        controls.performClickByJS(driver, NAME_CELL_LOCATOR);
    }
}