package eastnets.admin.gui;

import core.exception.AlreadyExistsException;
import core.gui.Controls;
import core.util.Wait;
import eastnets.admin.entity.Operator;
import eastnets.common.control.CommonAction;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class OperatorManager {
    public static final By LOGIN_NAME_BY_ID = By.id("operatorManagerForm:homepage_business:loginName");
    public static final By DEPARTMENT_BY_ID = By.id("operatorManagerForm:homepage_business:department");
    public static final By RESET_BUTTON_BY_ID = By.id("operatorManagerForm:homepage_business:btnReset");
    public static final By SEARCH_BUTTON_BY_ID = By.id("operatorManagerForm:homepage_business:btnSearch");
    public static final By NEW_BUTTON_BY_ID = By.id("operatorManagerForm:homepage_business:_tblResults:_btnNewUser");
    public static final By REMOVE_BUTTON_BY_ID = By.id("operatorManagerForm:homepage_business:_tblResults:_btnDeleteUser");
    public static final String FIRST_ENTRY_PREFIX = "//tbody[@id=\"operatorManagerForm:homepage_business:_tblResults_data\"]/tr";

    public static final By ZONE_SELECT_LOCATOR = By.id("operatorManagerForm:homepage_business:zone");
    private static final By FIRST_SEARCH_RESULT_LOCATOR = By.xpath("//*[@id='operatorManagerForm:homepage_business:_tblResults_data']//tr[1]//td[2]//a");
    private static final By CHECKBOX_LOCATOR = By.xpath("//*[@class='ui-selectbooleancheckbox ui-chkbox ui-widget']");

    private final Controls controls;
    private final Wait wait;
    private final CommonAction commonAction;

    public OperatorManager() {
        this.controls = new Controls();
        this.wait = new Wait();
        this.commonAction = new CommonAction();
    }

    @Step("Search Operator")
    public void searchOperator(RemoteWebDriver driver, Operator operator) throws Exception {
        Allure.step("Click reset button.");
        commonAction.resetSearch(driver, RESET_BUTTON_BY_ID);

        Allure.step(String.format("Set login name = %s", operator.getLoginName()));
        controls.setTextBoxValue(driver, LOGIN_NAME_BY_ID, operator.getLoginName());
        if (operator.getZone() != null) {
            Allure.step(String.format("Set zone = %s", operator.getZone().getDisplayName()));
            controls.selectOptionByDisplayedText(driver, ZONE_SELECT_LOCATOR, operator.getZone().getDisplayName());
        }
        Allure.step("Click search button.");
        commonAction.startSearch(driver, SEARCH_BUTTON_BY_ID);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Search Operator By Login Name")
    public void searchOperatorByLoginName(RemoteWebDriver driver, Operator operator) throws Exception {
        Allure.step("Click reset button.");
        commonAction.resetSearch(driver, RESET_BUTTON_BY_ID);
        Allure.step(String.format("Set login name = %s", operator.getLoginName()));
        controls.setTextBoxValue(driver, LOGIN_NAME_BY_ID, operator.getLoginName());
        Allure.step("Click search button.");
        commonAction.startSearch(driver, SEARCH_BUTTON_BY_ID);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Click search button")
    public void clickSearch(RemoteWebDriver driver) throws Exception {
        commonAction.startSearch(driver, SEARCH_BUTTON_BY_ID);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Select First Result")
    public void selectFirstResult(RemoteWebDriver driver) {
        controls.performClickByJS(driver, FIRST_SEARCH_RESULT_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("verify Exists")
    public boolean verifyExists(RemoteWebDriver driver, Operator operator) throws Exception {
        waitVisible(driver);
        searchOperatorByLoginName(driver, operator);
        return controls.exists(driver, By.xpath(FIRST_ENTRY_PREFIX + "/td[3]"));
    }

    @Step("verify New Button Exists")
    public boolean verifyNewButtonExists(RemoteWebDriver driver) {
        return controls.exists(driver, NEW_BUTTON_BY_ID);
    }

    @Step("verify Remove Button Exists")
    public boolean verifyRemoveButtonExists(RemoteWebDriver driver) {
        return controls.exists(driver, REMOVE_BUTTON_BY_ID);
    }

    @Step("Remove Operator")
    public boolean removeOperator(RemoteWebDriver driver, Operator operator) throws Exception {
        searchOperatorByLoginName(driver, operator);
        Allure.step("Select operator and click remove button.");
        controls.performClick(driver, CHECKBOX_LOCATOR);
        controls.performClickByJS(driver, REMOVE_BUTTON_BY_ID);
        driver.switchTo().alert().accept();
        return verifyExists(driver, operator);
    }

    @Step("Click Create new operator button")
    public void clickCreateNewOperatorButton(RemoteWebDriver driver, Operator operator) throws Exception {
        waitVisible(driver);
        if (verifyExists(driver, operator)) {
            Allure.step("Operator already exists");
            throw new AlreadyExistsException(String.format("Operator %s already exists", operator.getLoginName()));
        }
        Allure.step("Click Create new operator button.");
        controls.performClickByJS(driver, NEW_BUTTON_BY_ID);
    }

    @Step("Click Create new operator button")
    public void clickCreateNewOperatorButton(RemoteWebDriver driver) {
        waitVisible(driver);
        controls.performClickByJS(driver, NEW_BUTTON_BY_ID);
    }

    @Step("Wait Visible")
    public void waitVisible(RemoteWebDriver driver) {
        commonAction.waitPageVisible(driver, SEARCH_BUTTON_BY_ID);

    }


}
