package eastnets.admin.gui;

import core.gui.Controls;
import core.util.Wait;
import eastnets.admin.entity.Audit;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

public class AuditManager {

    private static final By AUDIT_ID_INPUT_LOCATOR = By.id("adminAuditForm:homepage_business:auditId");
    private static final By ACTION_DROP_DOWN_LOCATOR = By.id("adminAuditForm:homepage_business:action_label");
    private static final By MODIFIED_FIELD_DROP_DOWN_LOCATOR = By.id("adminAuditForm:homepage_business:externalSource_label");
    private static final By MODULE_DROP_DOWN_LOCATOR = By.id("adminAuditForm:homepage_business:externalParentSource_label");
    private static final By MODIFIED_BY_INPUT_LOCATOR = By.id("adminAuditForm:homepage_business:modifier");
    private static final By ZONE_DROP_DOWN_LOCATOR = By.id("adminAuditForm:homepage_business:zone_label");
    private static final By SEARCH_BUTTON_LOCATOR = By.id("adminAuditForm:homepage_business:btnSearch");
    private static final By PRINT_BUTTON_LOCATOR = By.id("adminAuditForm:homepage_business:btnPrintFull");
    private static final By RESET_BUTTON_LOCATOR = By.id("adminAuditForm:homepage_business:btnReset");
    private static final By RESULT_VIEW_ROWS_LOCATOR = By.xpath("//tbody[@id ='adminAuditForm:homepage_business:_tblAudit_data']//tr");
    private static final By CHECKBOX_LOCATOR = By.xpath("//*[@class='ui-selectbooleancheckbox ui-chkbox ui-widget']");

    private final Controls controls;
    private final Wait wait;

    public AuditManager() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    @Step("Click search button")
    public void clickSearchButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, SEARCH_BUTTON_LOCATOR);
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Click reset button")
    public void clickResetButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, RESET_BUTTON_LOCATOR);
        driver.switchTo().alert().accept();
        wait.waitUntilAjaxLoaderDisappear(driver);
    }

    @Step("Click print button")
    public void clickPrintButton(RemoteWebDriver driver) {
        controls.performClickByJS(driver, PRINT_BUTTON_LOCATOR);
    }

    @Step("Search Audit Manager")
    public void searchAuditManager(RemoteWebDriver driver, Audit audit) throws Exception {
        Allure.step("Start search on audit manager.");

        Allure.step(String.format("Set audit id = %s", audit.getAuditId()));
        controls.setTextBoxValueByJS(driver, AUDIT_ID_INPUT_LOCATOR, audit.getAuditId());

        Allure.step(String.format("Select modified field = %s", audit.getModifiedBy()));
        controls.selectOptionByDisplayedText(driver, MODIFIED_FIELD_DROP_DOWN_LOCATOR, audit.getModifiedField());

        Allure.step(String.format("Set modified by = %s", audit.getModifiedField()));
        controls.setTextBoxValueByJS(driver, MODIFIED_BY_INPUT_LOCATOR, audit.getModifiedBy());

        Allure.step(String.format("Set action = %s", audit.getAction()));
        controls.selectOptionByDisplayedText(driver, ACTION_DROP_DOWN_LOCATOR, audit.getAction());

        Allure.step(String.format("Set module = %s", audit.getModule()));
        controls.selectOptionByDisplayedText(driver, MODULE_DROP_DOWN_LOCATOR, audit.getModule());

        Allure.step(String.format("Set zone = %s", audit.getZone()));
        controls.selectOptionByDisplayedText(driver, ZONE_DROP_DOWN_LOCATOR, audit.getZone());

        clickSearchButton(driver);
    }

    @Step("Get Audit Data From Result Table")
    public List<Audit> getAuditDataFromResultTable(RemoteWebDriver driver) {

        wait.waitUntilElementToBeVisible((RemoteWebDriver) driver, CHECKBOX_LOCATOR, Duration.ofSeconds(5));
        List<WebElement> tableRows = driver.findElements(RESULT_VIEW_ROWS_LOCATOR);
        List<Audit> audits = new ArrayList<>();
        Audit audit = new Audit();

        for (WebElement row : tableRows) {
            controls.scrollIntoViewJS(driver, row);
            controls.scrollIntoViewJS(driver, RESULT_VIEW_ROWS_LOCATOR);
            List<WebElement> rowCells = row.findElements(By.tagName("td"));
            audit = new Audit();
            audit.setAuditId(rowCells.get(1).getText());
            audit.setAction(rowCells.get(2).getText());
            audit.setModule(rowCells.get(4).getText());
            audit.setModifiedField(rowCells.get(5).getText());
            audit.setDescription(rowCells.get(6).getText());
            audit.setModifiedBy(rowCells.get(7).getText());
            audit.setZone(rowCells.get(8).getText());
            audit.setReturnedItem(rowCells.get(9).getText());

            audits.add(audit);

        }
        return audits;
    }
}
