package eastnets.admin.backendServices.database.daos;

import core.database.DatabaseDriver;
import eastnets.admin.entity.Profile;

import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;

public class ZoneDAO {

    private final DatabaseDriver databaseDriver;

    public ZoneDAO() {
        this.databaseDriver = new DatabaseDriver();
    }

    public int getZoneId(Connection dbConn, String zoneName) throws SQLException {
        //Create DB Query to delete profile from listSet
        Profile profile = null;
        StringBuilder query = new StringBuilder();
        query.append("select id from tZones tz where tz.DISPLAY_NAME  = '" + zoneName + "'");
        // Execute query
        ResultSet resultSet = databaseDriver.executeQueryAndGetRS(dbConn, query.toString());
        resultSet.next();
        return resultSet.getInt("id");
    }
}
