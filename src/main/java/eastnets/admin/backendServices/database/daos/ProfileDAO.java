package eastnets.admin.backendServices.database.daos;

import core.database.DatabaseDriver;
import eastnets.admin.entity.Profile;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

public class ProfileDAO {

    private final DatabaseDriver databaseDriver;

    public ProfileDAO() {
        this.databaseDriver = new DatabaseDriver();
    }

    public List<Profile> getProfileRightLink(Connection dbConn, String profileName, String rightName) throws SQLException {
        //Create DB Query to delete profile from listSet
        Profile profile = null;
        List<Profile> profiles = new ArrayList<>();
        StringBuilder query = new StringBuilder();
        query.append("SELECT P.NAME as PROFILE_NAME " +
                ", PR.PROFILE_ID as PROFILE_ID , PR.GRANT_ID as GRANT_ID " +
                ", PR.GRANT_RIGHT_ID as GRANT_RIGHT_ID , PR.GRANT_TYPE_ID as GRANT_TYPE_ID " +
                "  FROM tModuleFunctions M " +
                "  LEFT JOIN tProfileRights PR On PR.GRANT_ID  = M.ID   " +
                "  LEFT Join tProfiles P ON P.id =PR.PROFILE_ID " +
                "  WHERE DESCRIPTION ='" + rightName + "' and P.NAME = '" + profileName + "'" +
                "  and PR.GRANT_TYPE_ID =3");
        // Execute query
        ResultSet resultSet = databaseDriver.executeQueryAndGetRS(dbConn, query.toString());
        while (resultSet.next()) {
            profile = new Profile();
            profile.setName(resultSet.getString("PROFILE_NAME"));
            profile.setId(resultSet.getLong("PROFILE_ID"));
            profile.setGrantId(resultSet.getLong("GRANT_ID"));
            profile.setGrantRightId(resultSet.getLong("GRANT_RIGHT_ID"));
            profile.setGrantTypeId(resultSet.getLong("GRANT_TYPE_ID"));
            profiles.add(profile);
        }
        return profiles;
    }

    public int removeProfileRightLink(Connection dbConn, String grantId, String profileId) throws SQLException {
        //Create DB Query to delete profile from listSet
        Profile profile = null;
        StringBuilder query = new StringBuilder();
        query.append("DELETE FROM tProfileRights  WHERE GRANT_ID  ='" + grantId + "' and PROFILE_ID ='" + profileId + "' and GRANT_TYPE_ID =3");
        // Execute query
        return databaseDriver.executeUpdate(dbConn, query.toString());
    }


    public int updateProfileRightLinkSQL(Connection dbConn, List<Profile> profile) throws SQLException {
        //Create DB Query to delete profile from listSet
        StringBuilder query = new StringBuilder();
        for (int i = 0; i < profile.size(); i++) {
            query.append(" INSERT INTO tProfileRights (PROFILE_ID, GRANT_ID, GRANT_RIGHT_ID, GRANT_TYPE_ID)" +
                    " VALUES (" + profile.get(i).getId() + ", " + profile.get(i).getGrantId() +
                    ", " + profile.get(i).getGrantRightId() + ", " + profile.get(i).getGrantTypeId() + ");");
        }
        // Execute query
        return databaseDriver.executeUpdate(dbConn, query.toString());
    }

    public int updateProfileRightLinkOracle(Connection dbConn, List<Profile> profile) throws SQLException {
        int effected_rows = 0;
        StringBuilder query = new StringBuilder();
        query.append("SELECT COALESCE(MAX(id), 0) + 1 AS next_id FROM tProfileRights");
        ResultSet resultSet = databaseDriver.executeQueryAndGetRS(dbConn, query.toString());
        resultSet.next();
        int next_id = resultSet.getInt("next_id");
        for (int i = 0; i < profile.size(); i++) {
            String insertSQL = "INSERT INTO tProfileRights (ID,PROFILE_ID, GRANT_ID, GRANT_RIGHT_ID, GRANT_TYPE_ID) " +
                    "                    VALUES (?,?, ?,?,?)";
            PreparedStatement preparedStatement = dbConn.prepareStatement(insertSQL);
            preparedStatement.setLong(1, next_id);
            preparedStatement.setLong(2, profile.get(i).getId());
            preparedStatement.setLong(3, profile.get(i).getGrantId());
            preparedStatement.setLong(4, profile.get(i).getGrantRightId());
            preparedStatement.setLong(5, profile.get(i).getGrantTypeId());
            int rowsInserted = preparedStatement.executeUpdate();
            if (rowsInserted > 0) {
                effected_rows++;
                continue;
            }
            preparedStatement.close();
            next_id++;
        }

        return effected_rows;
    }

    public int getProfileID(Connection dbConn, String profileName) throws SQLException {
        //Create DB Query to delete profile from listSet
        StringBuilder query = new StringBuilder();

        query.append("SELECT ID FROM tProfiles Where NAME ='" + profileName + "' and deleted= 0");
        ResultSet rs = databaseDriver.executeQueryAndGetRS(dbConn, query.toString());

        rs.next();
        return rs.getInt("ID");

    }
}
