package eastnets.admin.backendServices.database.services;

import core.database.DatabaseDriver;
import eastnets.admin.backendServices.database.daos.ProfileDAO;
import eastnets.admin.entity.Profile;
import eastnets.common.control.Application;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.List;

public class ProfileService {

    private final DatabaseDriver databaseDriver;

    public ProfileService() {
        this.databaseDriver = new DatabaseDriver();
    }

    public List<Profile> getProfileRightLink(String profileName, String rightName) throws SQLException {
        //Open connection to users database
        Connection connection = databaseDriver.getConnection(Application.ADMIN);
        ProfileDAO profileDAO = new ProfileDAO();
        List<Profile> profile = profileDAO.getProfileRightLink(connection, profileName, rightName);
        //close db connection
        databaseDriver.closeDBConnection(connection);
        return profile;
    }


    public int removeProfileRightLink(String grantId, String profileId) throws SQLException {
        //Open connection to users database
        Connection connection = databaseDriver.getConnection(Application.ADMIN);
        ProfileDAO profileDAO = new ProfileDAO();
        int effectedRowsNumber = profileDAO.removeProfileRightLink(connection, grantId, profileId);
        //close db connection
        databaseDriver.closeDBConnection(connection);
        return effectedRowsNumber;
    }

    public int updateProfileRightLink(List<Profile> profile) throws SQLException {
        //Open connection to users database
        Connection connection = databaseDriver.getConnection(Application.ADMIN);
        ProfileDAO profileDAO = new ProfileDAO();
        int effectedRowsNumber = 0;
        if (connection.getMetaData().getDriverName().contains("SQL Server"))
            effectedRowsNumber = profileDAO.updateProfileRightLinkSQL(connection, profile);
        else
            effectedRowsNumber = profileDAO.updateProfileRightLinkOracle(connection, profile);

        //close db connection
        databaseDriver.closeDBConnection(connection);
        return effectedRowsNumber;
    }

    public int getProfileID(String profileName) throws SQLException {
        Connection connection = databaseDriver.getConnection(Application.ADMIN);
        ProfileDAO profileDAO = new ProfileDAO();
        int profileID = profileDAO.getProfileID(connection, profileName);
        databaseDriver.closeDBConnection(connection);
        return profileID;
    }
}
