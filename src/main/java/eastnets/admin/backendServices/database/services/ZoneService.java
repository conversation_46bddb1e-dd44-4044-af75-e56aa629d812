package eastnets.admin.backendServices.database.services;

import core.database.DatabaseDriver;
import eastnets.admin.backendServices.database.daos.ZoneDAO;
import eastnets.common.control.Application;

import java.sql.Connection;
import java.sql.SQLException;

public class ZoneService {

    private final DatabaseDriver databaseDriver;

    public ZoneService() {
        this.databaseDriver = new DatabaseDriver();
    }

    public String getZoneId(String zoneName) throws SQLException {
        //Open connection to users database
        Connection connection = databaseDriver.getConnection(Application.ADMIN);
        ZoneDAO zoneDAO = new ZoneDAO();
        String zoneId = String.valueOf(zoneDAO.getZoneId(connection, zoneName));
        //close db connection
        databaseDriver.closeDBConnection(connection);
        return zoneId;
    }

}
