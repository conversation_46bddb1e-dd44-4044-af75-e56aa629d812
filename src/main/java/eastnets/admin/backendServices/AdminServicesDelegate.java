package eastnets.admin.backendServices;

import core.util.Log;
import eastnets.admin.backendServices.database.services.ProfileService;
import eastnets.admin.backendServices.database.services.ZoneService;
import eastnets.admin.entity.Profile;

import java.sql.SQLException;
import java.util.List;

public class AdminServicesDelegate {

    private final Log log;

    public AdminServicesDelegate() {
        this.log = new Log();
        log.info(" *******   Create an instance of ServiceDelegate to access backend DB    ******** ");
    }


    public List<Profile> getProfileRight(String profileName, String rightName) throws SQLException {
        ProfileService profileService = new ProfileService();
        return profileService.getProfileRightLink(profileName, rightName);

    }


    public int removeProfileRightLink(String grantId, String profileId) throws SQLException {
        ProfileService profileService = new ProfileService();
        return profileService.removeProfileRightLink(grantId, profileId);

    }


    public int updateProfileRightLink(List<Profile> profile) throws SQLException {
        ProfileService profileService = new ProfileService();
        return profileService.updateProfileRightLink(profile);

    }

    public String getZoneId(String zoneName) throws SQLException {
        ZoneService zoneService = new ZoneService();
        return zoneService.getZoneId(zoneName);
    }
}
