package eastnets.admin.control;

import eastnets.admin.entity.Operator;
import eastnets.admin.entity.PasswordPolicy;
import eastnets.admin.gui.OperatorEditor;
import eastnets.admin.gui.OperatorManager;
import eastnets.admin.gui.PasswordPolicyEditor;
import eastnets.common.gui.Navigation;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;


public class OperatorControl {

    private final OperatorManager operatorManager;
    private final OperatorEditor operatorEditor;
    private final PasswordPolicyEditor passwordPolicyEditor;

    public OperatorControl() {
        this.operatorManager = new OperatorManager();
        this.operatorEditor = new OperatorEditor();
        this.passwordPolicyEditor = new PasswordPolicyEditor();
    }

    @Step("Navigate to Operator Manager Module")
    public void navigateToOperatorModule(RemoteWebDriver driver) {
        Navigation.OPERATOR.navigate(driver);
    }

    @Step("Create Operator")
    public boolean createOperator(RemoteWebDriver driver, Operator operator) throws Exception {
        navigateToOperatorModule(driver);
        operatorManager.clickCreateNewOperatorButton(driver, operator);
        operatorEditor.createOperator(driver, operator);
        operatorManager.waitVisible(driver);
        return checkOperatorExists(driver, operator);
    }

    @Step("Create Disbled Operator")
    public boolean createDisabledOperator(RemoteWebDriver driver, Operator operator) throws Exception {
        navigateToOperatorModule(driver);
        operatorManager.clickCreateNewOperatorButton(driver, operator);
        operatorEditor.createDisabledOperator(driver, operator);
        operatorManager.waitVisible(driver);
        return checkOperatorExists(driver, operator);
    }

    @Step("Lock Operator")
    public void lockOperator(RemoteWebDriver driver, Operator operator) throws Exception {
        navigateToOperatorModule(driver);
        operatorManager.searchOperatorByLoginName(driver, operator);
        operatorManager.selectFirstResult(driver);
        operatorEditor.lockOperator(driver);
    }


    @Step("Check Operator Exists")
    public boolean checkOperatorExists(RemoteWebDriver driver, Operator operator) throws Exception {
        navigateToOperatorModule(driver);
        return operatorManager.verifyExists(driver, operator);
    }

    @Step("Check Permission")
    public boolean checkPermission(RemoteWebDriver driver, String functionToCheck, Operator operator) throws Exception {
        navigateToOperatorModule(driver);
        boolean result = false;
        switch (functionToCheck) {
            case "EditUser":
                Allure.step("Check if user can edit user.");
                operatorManager.clickSearch(driver);
                operatorManager.selectFirstResult(driver);
                result = !operatorEditor.checkIfOperatorEditable(driver);
                break;
            case "CreateUser":
                Allure.step("Check if user can create new operator.");
                result = !operatorManager.verifyNewButtonExists(driver);
                break;
            case "DeleteUser":
                Allure.step("Check if user can delete operator.");
                result = !operatorManager.verifyRemoveButtonExists(driver);
                break;
            case "EditOwnOperator":
                Allure.step("Check if user can edit own operator.");
                operatorManager.searchOperatorByLoginName(driver, operator);
                operatorManager.selectFirstResult(driver);
                result = !operatorEditor.checkIfOperatorEditable(driver);
                break;
            case "EnableOperatorWhileCreation":
                Allure.step("Check if user can enable operator while creating an operator.");
                operatorManager.clickCreateNewOperatorButton(driver);
                result = !operatorEditor.verifyIfEnableBoxEnabled(driver);
                break;
            case "EnableOperatorWhileEditing":
                Allure.step("Check if user can enable operator while editing an operator.");
                operatorManager.clickSearch(driver);
                operatorManager.selectFirstResult(driver);
                result = !operatorEditor.verifyIfEnableBoxEnabled(driver);
                break;
            case "LockUser":
                Allure.step("Check if user can lock operator.");
                operatorManager.searchOperatorByLoginName(driver, operator);
                operatorManager.selectFirstResult(driver);
                operatorEditor.lockOperator(driver);
                break;

        }
        return result;
    }

    @Step("Change Password Policy")
    public void changePasswordPolicy(RemoteWebDriver driver, PasswordPolicy passwordPolicy) throws InterruptedException {
        this.navigateToOperatorModule(driver);
        operatorManager.clickCreateNewOperatorButton(driver);
        operatorEditor.clickPasswordPolicy(driver);
        passwordPolicyEditor.setPasswordPolicyDetails(driver, passwordPolicy);
    }


}
