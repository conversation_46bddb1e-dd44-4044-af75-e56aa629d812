package eastnets.admin.control;

import core.exception.AlreadyExistsException;
import eastnets.admin.entity.Zone;
import eastnets.admin.gui.ZoneEditor;
import eastnets.admin.gui.ZoneManager;
import eastnets.common.gui.Navigation;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.io.IOException;

public class ZoneControl {

    private final ZoneManager zoneManager;
    private final ZoneEditor zoneEditor;

    public ZoneControl() {
        this.zoneManager = new ZoneManager();
        this.zoneEditor = new ZoneEditor();
    }

    @Step("Navigate To Zone Module")
    private void navigateToZoneModule(RemoteWebDriver driver) {
        Navigation.ZONE.navigate(driver);
    }

    @Step("Create Zone")
    public boolean createZone(RemoteWebDriver driver, Zone zone) throws AlreadyExistsException, InterruptedException, IOException {
        navigateToZoneModule(driver);
        zoneManager.createNewZone(driver, zone);
        boolean isZoneCreated = zoneEditor.createZone(driver, zone);
        zoneManager.waitVisible(driver);
        return isZoneCreated;
    }

    @Step("Check Zone Exists")
    public boolean checkZoneExists(RemoteWebDriver driver, Zone zone) throws InterruptedException {
        navigateToZoneModule(driver);
        return zoneManager.verifyZoneExist(driver, zone);
    }
}
