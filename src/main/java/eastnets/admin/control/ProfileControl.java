package eastnets.admin.control;

import core.util.Wait;
import eastnets.admin.entity.Profile;
import eastnets.admin.gui.ModuleEditor;
import eastnets.admin.gui.ProfileEditor;
import eastnets.admin.gui.ProfileManager;
import eastnets.admin.gui.UserProfileEditor;
import eastnets.common.gui.Navigation;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.util.List;
import java.util.Map;

public class ProfileControl {

    private final ProfileManager profileManager;
    private final ProfileEditor profileEditor;
    private final ModuleEditor moduleEditor;
    private final UserProfileEditor userProfileEditor;
    private final Wait wait;

    public ProfileControl() {
        this.profileManager = new ProfileManager();
        this.profileEditor = new ProfileEditor();
        this.moduleEditor = new ModuleEditor();
        this.userProfileEditor = new UserProfileEditor();
        this.wait = new Wait();
    }

    @Step("Navigate to Profile Module")
    public void navigateToProfileManager(RemoteWebDriver driver) {
        Navigation.PROFILE.navigate(driver);
    }

    @Step("Create Profile")
    public Boolean createProfile(RemoteWebDriver driver, Profile profile) throws Exception {
        navigateToProfileManager(driver);
        profileManager.creatingNewProfile(driver, profile);
        profileEditor.createProfile(driver, profile);
        profileManager.selectProfile(driver, profile);

        for (Map.Entry<String, Map<String, List<String>>> entry : profile.getProfileRights().entrySet()) {
            if (entry.getValue().isEmpty())
                continue;

            profileEditor.getModuleEditor(driver, entry.getKey());
            moduleEditor.editApplicationRight(driver, entry.getValue());
            wait.waitUntilAjaxLoaderDisappear(driver);
            wait.waitForJStoLoad(driver);
            profileEditor.getModuleEditor(driver, entry.getKey());
            moduleEditor.editModuleRight(driver, entry.getValue());
        }

        profileEditor.exitWithoutSaving(driver);
        Allure.step("Check if profile exist.");
        boolean profileExist = profileManager.verifyProfileExist(driver, profile);
        Allure.step("Profile exist = " + profileExist);
        return profileExist;
    }

    @Step("Check Profile Exists")
    public boolean checkProfileExists(RemoteWebDriver driver, Profile profile) throws Exception {
        navigateToProfileManager(driver);
        return profileManager.verifyProfileExist(driver, profile);
    }

    @Step("Remove Profile")
    public boolean removeProfile(RemoteWebDriver driver, Profile profile) throws Exception {
        navigateToProfileManager(driver);
        profileManager.removeProfile(driver, profile);
        return !profileManager.verifyProfileExist(driver, profile);
    }

    @Step("Clone Profile")
    public boolean cloneProfile(RemoteWebDriver driver, Profile profile) throws Exception {
        navigateToProfileManager(driver);
        profileManager.setProfileName(driver, profile.getDescription());
        profileManager.clickSearchButton(driver);
        profileManager.clickCloneProfile(driver);
        profileEditor.set_cloned_name(driver, profile.getName());
        profileEditor.set_cloned_zone(driver, profile.getZone().getDisplayName());
        profileEditor.click_save_button(driver);
        profileManager.setProfileName(driver, profile.getName());
        profileManager.selectProfile(driver);
        profileEditor.setEnabledFlag(driver, true);
        profileEditor.click_save_button(driver);
        return profileManager.verifyProfileExist(driver, profile);
    }

    @Step("Enable Profile")
    public boolean enableProfile(RemoteWebDriver driver, Profile profile) throws Exception {
        navigateToProfileManager(driver);
        profileManager.removeProfile(driver, profile);
        return !profileManager.verifyProfileExist(driver, profile);
    }

    @Step("Check Permission")
    public boolean checkPermission(RemoteWebDriver driver, Profile profile) throws InterruptedException {
        navigateToProfileManager(driver);
        boolean result = false;
        switch (profile.getFunctionToCheck()) {
            case "EditProfile":
                Allure.step("Check if user can edit profile.");
                profileManager.selectProfile(driver);
                result = profileEditor.checkIfProfileIsEditable(driver);
                break;
            case "DeleteProfile":
                Allure.step("Check if user can delete profile.");
                result = !profileManager.verifyRemoveButtonExist(driver);
                break;
            case "EnableProfileByClone":
                Allure.step("Check if user can enable profile.");
                profileManager.clickSearchButton(driver);
                profileManager.clickCloneProfile(driver);
                result = !profileEditor.verifyIfEnableBoxEnabled(driver);
                break;
            case "CloneProfile":
                Allure.step("Check if user can clone profile.");
                result = !profileManager.verifyCloneButtonExist(driver);
                break;
            case "EnableNewlyCreatedProfile":
                Allure.step("Check if user can Enable a newly created Profile.");
                profileManager.clickCreateNewProfileButton(driver);
                result = !profileEditor.verifyIfEnableBoxEnabled(driver);
                break;

        }
        return result;
    }



}
