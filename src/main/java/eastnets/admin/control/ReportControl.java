package eastnets.admin.control;

import core.util.TextFilesHandler;
import core.util.Wait;
import eastnets.admin.entity.Report;
import eastnets.admin.gui.ReportEditor;
import eastnets.admin.gui.ReportManager;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.net.MalformedURLException;

public class ReportControl {

    private final ReportManager reportManager;
    private final ReportEditor reportEditor;
    private final TextFilesHandler textFilesHandler;
    private final Wait wait;

    public ReportControl() {
        this.reportManager = new ReportManager();
        this.reportEditor = new ReportEditor();
        this.textFilesHandler = new TextFilesHandler();
        this.wait = new Wait();
    }

    @Step("Create New Report")
    public boolean createNewReport(RemoteWebDriver driver, Report report) throws Exception {
        reportManager.selectReportFromList(driver, report.getReportName());
        reportManager.clickNewButton(driver);
        reportEditor.createNewReport(driver, report);
        reportManager.searchByComment(driver, report.getComment());
        return reportManager.verifyReportExist(driver, report.getComment());
    }

    @Step("Get Report Status")
    public String getReportStatus(RemoteWebDriver driver) throws InterruptedException {
        wait.waitUntilAjaxLoaderDisappear(driver);
        reportManager.clickSearchButton(driver);
        wait.waitUntilAjaxLoaderDisappear(driver);

        String status = reportManager.getReportStatus(driver);
        int trials = 5;
        while (!status.equalsIgnoreCase("Done") && trials != 0) {
            trials--;
            wait.time(Wait.ONE_SECOND * 2);
            reportManager.clickSearchButton(driver);
            status = reportManager.getReportStatus(driver);
            if (status.equalsIgnoreCase("Done")) {
                return status;
            }
        }
        return status;
    }

    @Step("Export Report")
    public boolean exportReport(RemoteWebDriver driver, String fileName, String hub) throws InterruptedException, MalformedURLException {
        reportManager.clickOnReportToExport(driver);
        return textFilesHandler.exist(driver, fileName + ".pdf", hub);
    }


}
