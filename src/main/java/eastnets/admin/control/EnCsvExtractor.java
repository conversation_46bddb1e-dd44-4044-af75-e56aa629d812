package eastnets.admin.control;

import com.opencsv.exceptions.CsvValidationException;
import core.util.CSVHandler;
import core.util.DirectoryUtil;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class EnCsvExtractor {

    private final CSVHandler csvHandler;
    private final DirectoryUtil directoryUtil;

    public EnCsvExtractor() {
        this.csvHandler = new CSVHandler();
        this.directoryUtil = new DirectoryUtil();
    }

    public Map<String, Map<String, List<String>>> extractProfileRight(String profileName) throws CsvValidationException, IOException {
        List<List<String>> extractedValues = csvHandler.readFromFile(DirectoryUtil.TEST_RESOURCE + "/Profile.csv");

        Map<String, Map<String, List<String>>> profileRights = new HashMap<>();
        for (List<String> line : extractedValues) {

            if (!line.get(0).equalsIgnoreCase(profileName)) continue;

            String application = line.get(1);
            String module = line.get(2);
            String function = line.get(3);

            if (!profileRights.containsKey(application) && !application.isEmpty())
                profileRights.put(application, new HashMap<>());
            if (!profileRights.get(application).containsKey(module) && !module.isEmpty())
                profileRights.get(application).put(module, new ArrayList<>());
            if (!function.isEmpty())
                profileRights.get(application).get(module).add(function);
        }

        return profileRights;
    }
}
