package eastnets.admin.control;

import eastnets.admin.gui.UserProfileEditor;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;

public class UserProfileControl {

    private final UserProfileEditor userProfileEditor;

    public UserProfileControl() {
        this.userProfileEditor = new UserProfileEditor();
    }

    @Step("Change Password")
    public String Change_Password(RemoteWebDriver driver, String oldPassword, String newPassword) throws InterruptedException {
        userProfileEditor.click_Change_Password(driver);
        return userProfileEditor.change_Password(driver, oldPassword, newPassword);
    }
    public void changeLanguage(RemoteWebDriver driver, String Language) throws Exception {
        userProfileEditor.click_Change_Language(driver);
        userProfileEditor.change_Language(driver, Language);
    }
    public String getLanguage(RemoteWebDriver driver){
        userProfileEditor.click_Change_Language(driver);
        return userProfileEditor.click_Change_Language(driver);
    }
}