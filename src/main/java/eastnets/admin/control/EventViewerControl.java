package eastnets.admin.control;

import eastnets.admin.entity.EventViewer;
import eastnets.admin.gui.EventViewerManager;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.util.List;

public class EventViewerControl {

    private final EventViewerManager eventViewerManager;

    public EventViewerControl() {
        this.eventViewerManager = new EventViewerManager();
    }

    @Step("Search Event Viewer")
    public List<EventViewer> searchEventViewer(RemoteWebDriver driver) throws Exception {
        eventViewerManager.clickSearchButton(driver);
        return eventViewerManager.getEventDataFromResultTable(driver);
    }
}
