package eastnets.admin.control;

import eastnets.admin.entity.Group;
import eastnets.admin.gui.GroupEditor;
import eastnets.admin.gui.GroupManager;
import eastnets.common.gui.Navigation;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;

public class GroupControl {

    private final GroupManager groupManager;
    private final GroupEditor groupEditor;

    public GroupControl() {
        this.groupManager = new GroupManager();
        this.groupEditor = new GroupEditor();
    }

    @Step("Navigate To Group Module")
    private void navigateToGroupModule(RemoteWebDriver driver) {
        Navigation.GROUP.navigate(driver);
    }

    @Step("Create Group")
    public boolean createGroup(RemoteWebDriver driver, Group group) throws Exception {
        navigateToGroupModule(driver);
        groupManager.creatingNewGroup(driver, group);
        groupEditor.createGroup(driver, group);
        groupManager.waitVisible(driver);
        return checkGroupExists(driver, group);
    }

    @Step("Create Group with disabled user")
    public boolean createDisabledGroup(RemoteWebDriver driver, Group group) throws Exception {
        navigateToGroupModule(driver);
        groupManager.creatingNewGroup(driver, group);
        groupEditor.createDisbaledGroup(driver, group);
        groupManager.waitVisible(driver);
        return checkGroupExists(driver, group);
    }

    @Step("Check Group Exists")
    public boolean checkGroupExists(RemoteWebDriver driver, Group group) throws InterruptedException {
        navigateToGroupModule(driver);
        return groupManager.verifyGroupExists(driver, group);
    }

    @Step("Add Operator To Group")
    public void add_operator_to_group(RemoteWebDriver driver, Group group) throws Exception {
        navigateToGroupModule(driver);
        groupManager.editGroup(driver, group);
        groupEditor.updateGroupMembers(driver, group);
    }

    @Step("Add Operator To Group")
    public void add_operator_to_group(RemoteWebDriver driver, Group group, String operator) throws Exception {
        navigateToGroupModule(driver);
        groupManager.editGroup(driver, group);
        groupEditor.addOperator(driver, operator);
    }

}
