package eastnets.admin.control;

import eastnets.admin.entity.Audit;
import eastnets.admin.gui.AuditManager;
import eastnets.common.gui.Navigation;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.util.List;

public class AuditControl {

    private final AuditManager auditManager;

    public AuditControl() {
        this.auditManager = new AuditManager();
    }

    @Step("Navigate To Audit Manager Module")
    public void navigateToAuditManager(RemoteWebDriver driver) {
        Navigation.AUDIT.navigate(driver);
    }

    @Step("Search Audit Manager")
    public List<Audit> searchAuditManager(RemoteWebDriver driver) throws InterruptedException {
        auditManager.clickSearchButton(driver);
        return auditManager.getAuditDataFromResultTable(driver);
    }
}
