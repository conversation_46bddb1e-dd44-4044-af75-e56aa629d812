package eastnets.admin.control;

import eastnets.admin.gui.ArchiveViewerManager;
import eastnets.common.gui.Navigation;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;

public class ArchiveViewerControl {

    private final ArchiveViewerManager archiveViewerManager;

    public ArchiveViewerControl() {
        this.archiveViewerManager = new ArchiveViewerManager();
    }

    @Step("Navigate To Archive Viewer Module")
    public void navigateToArchiveViewer(RemoteWebDriver driver) {
        Navigation.ARCHIVE.navigate(driver);
    }

    @Step("Browse Archive File")
    public boolean browseArchiveFile(RemoteWebDriver driver, String filePath) {
        navigateToArchiveViewer(driver);
        archiveViewerManager.browseFile(driver, filePath);
        archiveViewerManager.clickOpenButton(driver);
        archiveViewerManager.selectFirstResult(driver);
        return archiveViewerManager.verifyArchiveDetailsTable(driver);
    }
}
