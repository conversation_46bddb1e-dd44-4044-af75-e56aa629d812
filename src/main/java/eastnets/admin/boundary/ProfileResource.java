package eastnets.admin.boundary;

import core.exception.EntityNotExistsException;
import eastnets.admin.control.ProfileControl;
import eastnets.admin.control.ZoneControl;
import eastnets.admin.entity.Profile;
import org.openqa.selenium.remote.RemoteWebDriver;


public class ProfileResource {

    private final ZoneControl zoneControl;
    private final ProfileControl profileControl;

    public ProfileResource() {
        this.zoneControl = new ZoneControl();
        this.profileControl = new ProfileControl();
    }

    public boolean createProfileAndZoneIfNotExist(RemoteWebDriver driver, Profile profile) throws Exception {
        if (!zoneControl.checkZoneExists(driver, profile.getZone()))
            zoneControl.createZone(driver, profile.getZone());
        profileControl.createProfile(driver, profile);
        return profileControl.checkProfileExists(driver, profile);
    }

    public boolean createProfile(RemoteWebDriver driver, Profile profile) throws Exception {
        return profileControl.createProfile(driver, profile);
    }


    public boolean removeProfile(RemoteWebDriver driver, Profile profile) throws Exception {
        if (!profileControl.checkProfileExists(driver, profile))
            throw new EntityNotExistsException(String.format("Cannot remove: Profile '%s' doesn't exist", profile.getName()));
        return profileControl.removeProfile(driver, profile);
    }
}
