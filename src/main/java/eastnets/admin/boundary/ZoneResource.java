package eastnets.admin.boundary;

import core.exception.AlreadyExistsException;
import eastnets.admin.control.ZoneControl;
import eastnets.admin.entity.Zone;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.io.IOException;

public class ZoneResource {

    private final ZoneControl zoneControl;

    public ZoneResource() {
        this.zoneControl = new ZoneControl();
    }

    public boolean createZone(RemoteWebDriver driver, Zone zone) throws AlreadyExistsException, InterruptedException, IOException {
        zoneControl.createZone(driver, zone);
        return zoneControl.checkZoneExists(driver, zone);
    }
}
