package core.util;

import com.google.common.io.CharStreams;
import com.jcraft.jsch.*;
import core.constants.screening.GeneralConstants;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Properties;

public class ServerUtil {

    //Initialize instances of properties files to be used in all tests
    private static final Property property = new Property();
    static Properties swfGeneralCofigsProps = property.fromFile(GeneralConstants.GENERAL_CONFIG_FILE_NAME);
    static Properties saaGeneralCofigsProps = property.fromFile(core.constants.saa.GeneralConstants.GENERAL_CONFIG_FILE_NAME);

    private final Log log;

    public ServerUtil() {
        this.log = new Log();
    }

    public String transferFileToServer(String filePath, String destinationPath, String app) throws JSchException, IOException {

        log.info("Initializing connection to remote server");
        //Create session to the application's remote server
        Session session = setupSshSession(app);
        session.connect();
        if (session.isConnected()) {
            log.info("Connection to SFTP server is successfully");
        } else {
            log.info("Connection to SFTP server not connected");
            return GeneralConstants.FAILED;
        }

        //Open sftp channel to server to securely transfer files. MAKE SURE THAT SFTP SERVICE IS UP AND RUNNING ON SERVER
        ChannelSftp sftpChannel = (ChannelSftp) session.openChannel("sftp");
        sftpChannel.setInputStream(null);
        InputStream output = sftpChannel.getInputStream();
        sftpChannel.connect();
        log.info("uploading file to server");
        try {
            destinationPath = runCommand("echo " + destinationPath, app, false).trim().replace("\\", "/");
            sftpChannel.put(filePath, destinationPath);
            log.info("File uploaded");
        } catch (Exception e) {
            log.info(e.toString());
            return GeneralConstants.FAILED;
        }
        closeConnection(sftpChannel, session);
        return GeneralConstants.SUCCESS;
    }

    public Session setupSshSession(String application) throws JSchException {
        JSch jsch = new JSch();
        String rmUsername = "";
        String rmHost = "";
        String rmPassword = "";
        String rmPort = "";
        //Get server credentials from config file based on required application
        switch (application) {
            case (GeneralConstants.SWF_APP_NAME):
                rmUsername = swfGeneralCofigsProps.getProperty(GeneralConstants.REMOTE_SERVER_USERNAME);
                rmHost = swfGeneralCofigsProps.getProperty(GeneralConstants.REMOTE_SERVER_IP);
                rmPassword = swfGeneralCofigsProps.getProperty(GeneralConstants.REMOTE_SERVER_PASSWORD);
                rmPort = swfGeneralCofigsProps.getProperty(GeneralConstants.REMOTE_SERVER_PORT);
                break;
            case (GeneralConstants.SAA_APP_NAME):
                rmUsername = saaGeneralCofigsProps.getProperty(core.constants.saa.GeneralConstants.REMOTE_SERVER_USERNAME);
                rmHost = saaGeneralCofigsProps.getProperty(core.constants.saa.GeneralConstants.REMOTE_SERVER_IP);
                rmPassword = saaGeneralCofigsProps.getProperty(core.constants.saa.GeneralConstants.REMOTE_SERVER_PASSWRD);
                rmPort = saaGeneralCofigsProps.getProperty(core.constants.saa.GeneralConstants.REMOTE_SERVER_PORT);
                break;
        }


        Session session = jsch.getSession(rmUsername, rmHost, Integer.parseInt(rmPort));
        session.setPassword(rmPassword);
        Properties config = new Properties();
        config.put("PreferredAuthentications", "publickey,keyboard-interactive,password");
        config.put("StrictHostKeyChecking", "no");
        session.setConfig(config);
        return session;
    }

    public String runCommand(String command, String app, boolean loadDJList) throws JSchException, IOException, InterruptedException {
        log.info("Run command = " + command);
        Session session = setupSshSession(app);
        session.connect();
        ChannelExec channel = (ChannelExec) session.openChannel("exec");
        channel.setCommand(command);

        channel.setErrStream(System.err);
        channel.setInputStream(null);
        log.info("Connect to session...");

        ByteArrayOutputStream outputBuffer = new ByteArrayOutputStream();

        InputStream in = channel.getInputStream();

        channel.connect();
        byte[] tmp = new byte[1024];
        while (true) {
            while (in.available() > 0) {
                int i = in.read(tmp, 0, 1024);
                if (i < 0) break;
                outputBuffer.write(tmp, 0, i);
                System.out.println("output: " + outputBuffer.toString("UTF-8"));
                System.out.println("exit-status: " + channel.getExitStatus());


            }
            if (channel.isClosed() || outputBuffer.toString("UTF-8").contains("Press any key to continue . . . ")) {
                if ((in.available() > 0)) continue;
                System.out.println("exit-status: " + channel.getExitStatus());
                break;
            }

            try {
                Thread.sleep(1000);
            } catch (Exception ee) {
            }
        }


        return outputBuffer.toString("UTF-8");

    }

    public String runCommand2(String command, ChannelExec channel) throws JSchException, IOException, InterruptedException {
        log.info("Run command = " + command);
        channel.setCommand(command);
        channel.setErrStream(System.err);
        channel.setInputStream(null);
        channel.connect();
        InputStream output = channel.getInputStream();
        byte[] tmp = new byte[1024];
        Thread.sleep(10000);
        String result = null;
        while (output.available() > 0) {
            int i = output.read(tmp, 0, 1024);
            if (i < 0) {
                break;
            }
            result = result + new String(tmp, 0, i);
            Thread.sleep(3000);
        }
        log.info("Response from command = " + result);
        return result;
    }

    public ChannelExec openChannel(Session session) throws JSchException {
        session.connect();
        ChannelExec channel = (ChannelExec) session.openChannel("exec");
        return channel;
    }

    public String moveFileInServer(String from, String to, String app) throws JSchException, SftpException, IOException, InterruptedException {

        log.info("Initializing remote connection to server...");
        //Create session to the application's remote server
        Session session = setupSshSession(app);
        session.connect();
        if (session.isConnected()) {
            log.info("Connection to SFTP server is successfully");
        } else {
            log.info("Connection to SFTP server not connected");
            return GeneralConstants.FAILED;
        }

        //Open sftp channel to server to securely transfer files. MAKE SURE THAT SFTP SERVICE IS UP AND RUNNING ON SERVER
        ChannelSftp sftpChannel = (ChannelSftp) session.openChannel("sftp");
        sftpChannel.connect();
        from = runCommand("echo " + from, app, false).trim().replace("\\", "/");
        to = "/" + runCommand("echo " + to, app, false).trim().replace("\\", "/");

        sftpChannel.rename(from, to);
        closeConnection(sftpChannel, session);
        return GeneralConstants.SUCCESS;
    }

    private void closeConnection(ChannelSftp channel, Session session) {
        try {
            log.info("Closing Sftp connection");
            channel.quit();
        } catch (Exception e) {
            log.error("Failed to close Sftp connection", e);
        }
        session.disconnect();
    }

    public void closeConnection(ChannelExec channel, Session session) {
        try {
            channel.disconnect();
        } catch (Exception e) {
            log.error("Failed to close Execution connection", e);
        }
        session.disconnect();
    }

    public String runProcessAndReturnPid(String Process, String app) throws JSchException, IOException {
        log.info("Run command = " + Process);
        Session session = setupSshSession(app);
        session.connect();
        ChannelExec channel = (ChannelExec) session.openChannel("exec");
        channel.setCommand("powershell.exe \"Start-Process " + Process + " -PassThru | Select-Object -ExpandProperty Id\"");
        channel.setErrStream(System.err);
        channel.setInputStream(null);
        log.info("Connect to session...");
        channel.connect();
        InputStream output = channel.getInputStream();
        String result = CharStreams.toString(new InputStreamReader(output));
        String pid = result.replace(System.getProperty("line.separator"), "");
        closeConnection(channel, session);
        log.info("PID = " + result);
        return pid;
    }

    public String endProcessWithID(String pid, String app) throws JSchException, IOException {
        log.info("Ending Process with ID = " + pid);
        Session session = setupSshSession(app);
        session.connect();
        ChannelExec channel = (ChannelExec) session.openChannel("exec");
        channel.setCommand("powershell.exe \"taskkill /PID " + pid + " /F\"");
        channel.setErrStream(System.err);
        channel.setInputStream(null);
        System.out.println("Connect to session...");
        channel.connect();
        InputStream output = channel.getInputStream();
        String result = CharStreams.toString(new InputStreamReader(output));
        closeConnection(channel, session);
        if (!result.isEmpty()) {
            log.info("Failed to terminate process with id: " + pid + " command output:" + result);
            return GeneralConstants.FAILED;
        } else {
            log.info("Process terminated successfully");
            result = GeneralConstants.SUCCESS;
        }
        return result;
    }

    public boolean isProcessRunning(String processName, String app) throws JSchException, IOException {
        log.info("finding Process with name = " + processName);
        Session session = setupSshSession(app);
        session.connect();
        ChannelExec channel = (ChannelExec) session.openChannel("exec");
        channel.setCommand("powershell.exe \"get-process " + processName + " |select -expand id \"");
        channel.setErrStream(System.err);
        channel.setInputStream(null);
        System.out.println("Connect to session...");
        channel.connect();
        InputStream output = channel.getInputStream();
        String result = CharStreams.toString(new InputStreamReader(output));
        closeConnection(channel, session);
        if (result.contains("Cannot find a process with the name")) {
            log.info("Process is not running " + processName + " command output:" + result);
            return false;
        } else {
            log.info("Process is running with ID: " + result);
            return true;
        }
    }

    public boolean isProcessRunning(int pid, String app) throws JSchException, IOException {
        log.info("finding Process with name = " + pid);
        Session session = setupSshSession(app);
        session.connect();
        ChannelExec channel = (ChannelExec) session.openChannel("exec");
        channel.setCommand("powershell.exe \"Get-Process -Id " + pid + " | Select-Object -ExpandProperty ProcessName\"");
        channel.setErrStream(System.err);
        channel.setInputStream(null);
        System.out.println("Connect to session...");
        channel.connect();
        InputStream output = channel.getInputStream();
        String result = CharStreams.toString(new InputStreamReader(output));
        closeConnection(channel, session);
        if (result.contains("Cannot find a process with the name")) {
            log.info("Process is not running " + pid + " command output:" + result);
            return false;
        } else {
            log.info("Process is running with Name: " + result);
            return true;
        }
    }
}
