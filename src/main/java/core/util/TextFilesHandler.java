package core.util;

import com.google.common.collect.ImmutableMap;
import core.constants.screening.GeneralConstants;
import org.apache.commons.io.IOUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.openqa.selenium.io.Zip;
import org.openqa.selenium.json.Json;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.openqa.selenium.remote.SessionId;
import org.openqa.selenium.remote.http.HttpClient;
import org.openqa.selenium.remote.http.HttpRequest;
import org.openqa.selenium.remote.http.HttpResponse;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.openqa.selenium.remote.http.Contents.asJson;
import static org.openqa.selenium.remote.http.Contents.string;
import static org.openqa.selenium.remote.http.HttpMethod.*;

public class TextFilesHandler {

    private final Log log;
    private final Wait wait;

    public TextFilesHandler() {
        this.log = new Log();
        this.wait = new Wait();
    }

    public String getTextFileContentAsString(String textFilePath) throws IOException {
        FileInputStream inputStream = new FileInputStream(textFilePath);
        String fileContent = IOUtils.toString(inputStream);
        IOUtils.close(inputStream);
        return fileContent;
    }

    public void createFile(String fileName) throws IOException {

        File file = new File(fileName);

        if (file.createNewFile()) {
            log.info("File created: " + file.getName());
        } else {
            log.info("File already exists.");
        }
    }

    public void writeToFile(String filePath, String fileContent) throws IOException {
        FileWriter myWriter = new FileWriter(filePath);
        myWriter.write(fileContent);
        myWriter.close();
        log.info("Successfully wrote to the file.");
    }

    public void listFilesForFolder(final File folder) throws InterruptedException {
        wait.time(50000);
        log.info("List of Files in the Folder");
        log.info("================================");
        log.info("Current path: " + folder.getAbsolutePath());
        log.info("Is Directory Exist: " + folder.exists());
        for (final File fileEntry : folder.listFiles()) {
            if (fileEntry.isDirectory()) {
                listFilesForFolder(fileEntry);
            } else {
                log.info(fileEntry.getName());
            }
        }
    }


    public boolean exist(RemoteWebDriver driver, String fileName, String hub) throws MalformedURLException, InterruptedException {
        wait.time(Wait.THIRTY_SECOND);
        URL gridUrl = new URL(hub);
        SessionId sessionId = driver.getSessionId();
        HttpRequest request = new HttpRequest(GET, String.format("/session/%s/se/files", sessionId));
        HttpClient client = HttpClient.Factory.createDefault().createClient(gridUrl);
        HttpResponse response = client.execute(request);
        Map<String, Object> jsonResponse = new Json().toType(string(response), Json.MAP_TYPE);
        Map<String, Object> value = (Map<String, Object>) jsonResponse.get("value");
        List<String> names = (List<String>) value.get("names");
        return names.contains(fileName);
    }

    public boolean deleteFileOnRemoteNode(RemoteWebDriver driver, String hub) throws MalformedURLException, InterruptedException {
        URL gridUrl = new URL(hub);
        SessionId sessionId = driver.getSessionId();
        HttpClient client = HttpClient.Factory.createDefault().createClient(gridUrl);
        HttpRequest deleteRequest = new HttpRequest(DELETE, String.format("/session/%s/se/files", sessionId));
        HttpResponse deleteResponse = client.execute(deleteRequest);

        return deleteResponse.isSuccessful();
    }

    public boolean checkExistOfFileWithDynamicName(RemoteWebDriver driver, String fileName, String hub) throws MalformedURLException, InterruptedException {
        wait.time(Wait.THIRTY_SECOND);
        URL gridUrl = new URL(hub+"/wd/hub");
        SessionId sessionId = driver.getSessionId();
        String url = String.format(hub+"/wd/hub/session/%s/se/files", sessionId);
        HttpRequest request = new HttpRequest(GET, url);
        HttpClient client = HttpClient.Factory.createDefault().createClient(new URL(url));
        HttpResponse response = client.execute(request);
        log.info("response.getStatus()"+response.getStatus());
        log.info(""+response.getContentEncoding());
        Map<String, Object> jsonResponse = new Json().toType(string(response), Json.MAP_TYPE);
        @SuppressWarnings("unchecked")
        Map<String, Object> value = (Map<String, Object>) jsonResponse.get("value");
        List<String> names = new ArrayList<>();
        names = (List<String>) value.get("names");
        log.info(String.valueOf(names.size()));
        for (String name : names) {
            if (name.contains(fileName)) {
                return true;
            }
        }
        return false;
    }
  /*  public static boolean exist(RemoteWebDriver driver ,String fileName ) throws InterruptedException {
        Thread.sleep(5000);

        String  browserDefaultDownloadPath =  Property.fromFile(GeneralConstants.GENERAL_CONFIG_FILE_NAME).getProperty(GeneralConstants.DEFAULT_DOWNLOAD_PATH);
        String filePath = browserDefaultDownloadPath+fileName;
        Log.info(String.format("Check if File with Path = %s is exist.",filePath));
        File f = new File(filePath);
        FluentWait<RemoteWebDriver> wait = new FluentWait<RemoteWebDriver>(driver).withTimeout(Duration.ofSeconds(25)).pollingEvery(Duration.ofMillis(100));
        wait.until( x -> f.exists());
        if (f.exists() && !f.isDirectory()) {
            Log.info(String.format("File with name = %s downloaded successfully" , fileName));
            return true;
        }
        return false;
    }*/


   /* public static boolean checkExistOfFileWithDynamicName(String prefix , String extension) throws InterruptedException {
        String  downloadFolderPath = System.getProperty("user.dir")
                + Property.fromFile(GeneralConstants.GENERAL_CONFIG_FILE_NAME).getProperty(GeneralConstants.DEFAULT_DOWNLOAD_PATH);
        System.out.println(downloadFolderPath+"/n");
        File folder = new File(downloadFolderPath);

        int tryCount = 0;
        while(tryCount < 10)
        {
            System.out.println("tryCount = " + tryCount+"/n");
            Wait.time(Wait.ONE_SECOND*5);
            File[] files = folder.listFiles();
            if (files.length != 0) {
                System.out.println(files.length+"/n");
                if (files[0].isFile()) {
                    String fileName = files[0].getName();
                    System.out.println(fileName+"/n");
                    if (fileName.startsWith(prefix)
                            && fileName.endsWith(extension)) {
                        return true;
                    }
                }
            }
            tryCount ++;
        }
    return false;
    }*/

    public String readPDF(String filePath) throws IOException {
        File file = new File(filePath);
        PDDocument document = PDDocument.load(file);
        //Instantiate PDFTextStripper class
        PDFTextStripper pdfStripper = new PDFTextStripper();
        //Retrieving text from PDF document
        String text = pdfStripper.getText(document);
        document.close();
        return text;
    }

    public String get_downloaded_file_name(RemoteWebDriver driver, String hub) throws InterruptedException, MalformedURLException {
        wait.time(Wait.THIRTY_SECOND);
        URL gridUrl = new URL(hub);
        SessionId sessionId = driver.getSessionId();
        HttpRequest request = new HttpRequest(GET, String.format("/session/%s/se/files", sessionId));
        HttpClient client = HttpClient.Factory.createDefault().createClient(gridUrl);
        HttpResponse response = client.execute(request);
        Map<String, Object> jsonResponse = new Json().toType(string(response), Json.MAP_TYPE);
        Map<String, Object> value = (Map<String, Object>) jsonResponse.get("value");
        List<String> names = (List<String>) value.get("names");
        return names.get(0);
    }

    public String renameFile(String oldFilePath, String oldFileName) {

        File file = new File(oldFilePath + oldFileName);
        String newFileName = "File" + new Randomizer().timestamp;
        File rename = new File(oldFilePath + newFileName);
        boolean flag = file.renameTo(rename);
        if (flag == true) {
            return newFileName;
        }

        return GeneralConstants.FAILED;
    }

    public File move_file_to_local_dir(RemoteWebDriver driver, String fileName, String filePath, String hub) throws Exception {
        // To retrieve a specific file from one or more files that were downloaded by the current session
        // on a remote node, we use a POST request.
        URL gridUrl = new URL(hub);
        SessionId sessionId = driver.getSessionId();
        HttpRequest request = new HttpRequest(POST, String.format("/session/%s/se/files", sessionId));
        HttpClient client = HttpClient.Factory.createDefault().createClient(gridUrl);
        request.setContent(asJson(ImmutableMap.of("name", fileName)));
        HttpResponse response = client.execute(request);
        Map<String, Object> jsonResponse = new Json().toType(string(response), Json.MAP_TYPE);
        Map<String, Object> value = (Map<String, Object>) jsonResponse.get("value");
        // The returned map would contain 2 keys,
        // filename - This represents the name of the file (same as what was provided by the test)
        // contents - Base64 encoded String which contains the zipped file.
        String zippedContents = value.get("contents").toString();
        // The file contents would always be a zip file and has to be unzipped.
        Zip.unzip(zippedContents, new File(filePath));
        File downloadDir = new File(filePath);
        // Read the file contents
        File downloadedFile = Optional.ofNullable(downloadDir.listFiles()).orElse(new File[]{})[0];
        return downloadedFile;
    }

    public String get_pdf_file_content(RemoteWebDriver driver, String fileName, String filePath, String hub) throws Exception {

        move_file_to_local_dir(driver, fileName, filePath, hub);
        String fileContent = readPDF(filePath + "/" + fileName);
        log.info("File Content :" + fileContent);
        return fileContent;
    }

    public String get_txt_file_content(RemoteWebDriver driver, String fileName, String filePath, String hub) throws Exception {

        move_file_to_local_dir(driver, fileName, filePath, hub);
        String fileContent = getTextFileContentAsString(filePath + "/" + fileName);
        log.info("File Content :" + fileContent);
        return fileContent;
    }

}
