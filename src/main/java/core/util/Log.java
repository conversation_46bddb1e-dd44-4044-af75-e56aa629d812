package core.util;

import com.aventstack.extentreports.ExtentTest;
import com.aventstack.extentreports.Status;
import core.constants.screening.GeneralConstants;
import io.qameta.allure.Allure;
import io.qameta.allure.Step;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.apache.xmlbeans.impl.xb.xsdschema.All;

import java.util.Properties;

public class Log {

    public static ExtentTest test;

    //Initialize instances of properties files to be used in all tests
    static Properties generalConfigsProps = new Property().fromFile(GeneralConstants.GENERAL_CONFIG_FILE_NAME);
    static boolean addLogToExtentReportFlag = (generalConfigsProps.getProperty(GeneralConstants.ADD_LOG_TO_EXTENT_REPORT).toLowerCase().equals(GeneralConstants.TRUE.toLowerCase()));


    // Initialize Log4j logs

    private static Logger LOG = LogManager.getLogger(Log.class.getName());//

    // This is to print log for the beginning of the test case, as we usually run so many test cases as a test suite

    public void startTestCase(String sTestCaseName) {

        if (test != null && addLogToExtentReportFlag)
            test.log(Status.INFO, sTestCaseName + " Test is CREATED");


        LOG.info("****************************************************************************************");

        LOG.info("****************************************************************************************");

        LOG.info("$$$$$$$$$$$$$$$$$$$$$           " + sTestCaseName + "       $$$$$$$$$$$$$$$$$$$$$$$$$");

        LOG.info("****************************************************************************************");

        LOG.info("****************************************************************************************");

    }

    //This is to print log for the ending of the test case

    public void endTestCase(String sTestCaseName) {

        if (test != null && addLogToExtentReportFlag)
            test.log(Status.INFO, sTestCaseName + " Test has ENDED");

        LOG.info("XXXXXXXXXXXXXXXXXXXXXXX      " + "      -E---N---D-     " + sTestCaseName + "             XXXXXXXXXXXXXXXXXXXXXX");

        LOG.info("X");

        LOG.info("X");

        LOG.info("X");

        LOG.info("X");

    }

    // Need to create these methods, so that they can be called

    private void checkForExtentTest(String message, Status status) {
        if (test != null && addLogToExtentReportFlag)
            test.log(status, message);
    }

    public void info(String message) {
        LOG.info(message);
        Allure.step(message);
        checkForExtentTest(message, Status.INFO);
    }

    public void warn(String message, Exception e) {
        LOG.warn(message, e);
        Allure.step(message);
        checkForExtentTest(message, Status.WARNING);
    }

    public void error(String message, Exception e) {
        LOG.error(message, e);
        Allure.step(message + " ----> "+ e.getMessage());
        checkForExtentTest(message, Status.ERROR);
        checkForExtentTest(e.getMessage(), Status.ERROR);
    }

    public void fatal(String message) {
        LOG.fatal(message);
        Allure.step(message);
        checkForExtentTest(message, Status.FATAL);
    }

    public void debug(String message) {
        LOG.debug(message);
        Allure.step(message);
        checkForExtentTest(message, Status.DEBUG);
    }

}
