package core.util;

import core.gui.Controls;
import org.openqa.selenium.*;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.FluentWait;
import org.openqa.selenium.support.ui.WebDriverWait;

import java.time.Duration;

public class Wait {
    public static final long ONE_SECOND = Duration.ofSeconds(1).toMillis();
    public static final long ONE_MINUTE = Duration.ofMinutes(1).toMillis();
    public static final long THIRTY_SECOND = Duration.ofSeconds(30).toMillis();
    public static final Duration TEN_SECOND_DURATION = Duration.ofSeconds(10);
    public static final Duration ONE_SECOND_DURATION = Duration.ofSeconds(1);
    public static final Duration THIRTY_SECOND_DURATION = Duration.ofSeconds(30);
    public static final Duration ONE_MINUTE_DURATION = Duration.ofSeconds(60);
    public static final Duration TWO_MINUTE_DURATION = Duration.ofSeconds(120);
    public static final By AJAX_LOADER_LOCATOR = By.xpath("//*[@class='pi pi-spin pi-spinner ajax-loader']");
    public static final By LOADING_LOGO_LOCATOR = By.xpath("//*[.='Loading...']");
    private static final By ALERT_MESSAGE_LOCATOR = By.xpath("//*[@class='ui-growl-title']");


    public static void time(long millis) throws InterruptedException {
        Thread.sleep(millis);
    }

    public static void elementVisible(RemoteWebDriver driver, By by, Duration duration) {
        new WebDriverWait(driver, duration).until(ExpectedConditions.visibilityOf(new Controls().getWebElement(driver, by)));
    }


    public static WebElement waitUntilElementToBeVisible(RemoteWebDriver driver, By locator, Duration duration) {

            FluentWait<RemoteWebDriver> wait = new FluentWait<>(driver)
                    .withTimeout(duration)
                    .pollingEvery(Duration.ofMillis(5))
                    .ignoring(NoSuchElementException.class, StaleElementReferenceException.class);

            return wait.until(ExpectedConditions.visibilityOfElementLocated(locator));

    }

    public static  Boolean waitUntilElementTextContains(RemoteWebDriver driver, By locator, String value, Duration duration) {
        return new WebDriverWait(driver, duration).until(ExpectedConditions.textToBe(locator, value));
    }

    public static WebElement waitUntilElementToBeVisible(RemoteWebDriver driver, WebElement webElement, Duration duration) {
        FluentWait<RemoteWebDriver> wait = new FluentWait<>(driver)
                .withTimeout(duration)
                .pollingEvery(Duration.ofMillis(5))
                .ignoring(NoSuchElementException.class, StaleElementReferenceException.class);

        return wait.until(ExpectedConditions.visibilityOf(webElement));
    }

    public static WebElement waitUntilElementToBeClickable(RemoteWebDriver driver, By locator, Duration duration) {
        FluentWait<RemoteWebDriver> wait = new FluentWait<>(driver)
                .withTimeout(duration)
                .pollingEvery(Duration.ofMillis(5))
                .ignoring(NoSuchElementException.class, StaleElementReferenceException.class);

        return wait.until(ExpectedConditions.elementToBeClickable(locator));
    }

    public static  Boolean waitUntilElementToDisappear(RemoteWebDriver driver, By locator, Duration duration) {
        FluentWait<RemoteWebDriver> wait = new FluentWait<>(driver)
                .withTimeout(duration)
                .pollingEvery(Duration.ofMillis(5))
                .ignoring(NoSuchElementException.class, StaleElementReferenceException.class);

        return wait.until(ExpectedConditions.invisibilityOfElementLocated(locator));
    }

    public static Alert waitUntilAlertAppear(RemoteWebDriver driver, Duration duration) {
        return new WebDriverWait(driver, duration).until(ExpectedConditions.alertIsPresent());
    }

    public static void waitUntilAlertMessageDisappear(RemoteWebDriver driver) {
        waitUntilElementToDisappear(driver, ALERT_MESSAGE_LOCATOR, Wait.TEN_SECOND_DURATION);
    }

    public static Boolean waitUntilAjaxLoaderDisappear(RemoteWebDriver driver) {
        try {
            FluentWait<RemoteWebDriver> wait = new FluentWait<>(driver)
                    .withTimeout(Duration.ofMinutes(2))
                    .pollingEvery(Duration.ofMillis(5))
                    .ignoring(NoSuchElementException.class, StaleElementReferenceException.class);

            return wait.until(ExpectedConditions.invisibilityOfElementLocated(AJAX_LOADER_LOCATOR));
        }catch (Exception e)
        {
            return true;
        }
    }

    public static Boolean waitUntilLoadingLogoDisappear(RemoteWebDriver driver) {
        return new WebDriverWait(driver, ONE_MINUTE_DURATION).until(ExpectedConditions.invisibilityOfElementLocated(LOADING_LOGO_LOCATOR));
    }

    public static Boolean waitUntilLoadersDisappear(RemoteWebDriver driver) {
        return waitUntilAjaxLoaderDisappear(driver) && waitUntilLoadingLogoDisappear(driver);
    }

    public static void waitUntilAttributeValueEqual(RemoteWebDriver driver, By by, String attributeName, String attributeValue, Duration duration) {
        new WebDriverWait(driver, duration).until(ExpectedConditions.attributeContains(new Controls().getWebElement(driver, by), attributeName, attributeValue));
    }


    public static boolean waitForJStoLoad(RemoteWebDriver driver) {

		/*WebDriverWait wait = new WebDriverWait(driver, Duration.ofSeconds(60));
		JavascriptExecutor executor = (JavascriptExecutor)driver;
		// wait for jQuery to load
		ExpectedCondition<Boolean> jQueryLoad = new ExpectedCondition<Boolean>() {

			@Override
			public Boolean apply(RemoteWebDriver driver) {
				try {
					return ((Long)executor.executeScript("return jQuery.active") == 0);
				}
				catch (Exception e) {
					return true;
				}
			}
		};

		// wait for Javascript to load
		ExpectedCondition<Boolean> jsLoad = new ExpectedCondition<Boolean>() {

			@Override
			public Boolean apply(RemoteWebDriver driver) {
				return executor.executeScript("return document.readyState")
						.toString().equals("complete");
			}
		};

		return wait.until(jQueryLoad) && wait.until(jsLoad);*/
        return true;
    }


}
