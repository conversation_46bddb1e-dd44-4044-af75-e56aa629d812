package core.util;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Random;

public class Randomizer {
    private Random RANDOM = new Random();
    public String dateTime = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss").format(new Date());
    public String dateTime1 = new SimpleDateFormat("yyyyMMddhhmmss").format(new Date());

    public String date = new SimpleDateFormat("yyyyMMdd").format(new Date());
    public String timestamp = new SimpleDateFormat("MMddmmsssss").format(new Date());
    public String timestamp1 = new SimpleDateFormat("mmsssssss").format(new Date());

    public String currentDate = new SimpleDateFormat("yyyy/MM/dd").format(new Date());
    public String currentDate2 = new SimpleDateFormat("MM/dd/yyyy").format(new Date());

    public int getInt() {
        return RANDOM.nextInt(999999999);
    }

    public String yesterday(String format) {
        final Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -1);
        DateFormat dateFormat = new SimpleDateFormat("yyyy/MM/dd");
        return dateFormat.format(cal.getTime());
    }
}

