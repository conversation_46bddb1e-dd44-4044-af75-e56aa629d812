package core.database;

import core.constants.screening.GeneralConstants;
import core.util.Log;
import core.util.Property;
import eastnets.common.control.Application;

import java.sql.*;
import java.util.Collections;
import java.util.List;
import java.util.Properties;

public class DatabaseDriver {

    private Property property = new Property();
    private  Log log = new Log();

    private String getUrlFromProperties(Properties connectionProperties, Application application) {
        String url = connectionProperties.getProperty("db.url");
        DatabaseVendor vendor = DatabaseVendor.SQLSERVER.ofName(url.split(":")[1]);
        if (vendor == DatabaseVendor.SQLSERVER)
            url += String.format("databaseName=%s", application.getDatabaseName());
        //  url +=";Encrypt=true;";
        System.out.println("=====================" + url);

        return url;
    }

    public Connection getConnection(Application application) throws SQLException {
        Properties connectionProperties = property.fromFile(GeneralConstants.GENERAL_CONFIG_FILE_NAME);
        String enDatabaseUser = application.getDatabaseUserName();

        return DriverManager.getConnection(
                getUrlFromProperties(connectionProperties, application),
                enDatabaseUser,
                connectionProperties.getProperty(String.format("db.%s.password", enDatabaseUser))
        );
    }


    public ResultSet executeQueryAndGetRS(Connection conn, String sqlQuery) throws SQLException {
        log.info("*********      Executing DB Query     ************");
        log.info(sqlQuery);

        Statement statement = conn.createStatement();
        ;
        ResultSet rs = statement.executeQuery(sqlQuery);

        log.info("************     DB Query executed successfully     ***************");

        return rs;
    }

    public void executeQuery(Application application, String sqlQuery) throws SQLException {
        log.info("*********      Executing DB Query     ************");
        log.info(sqlQuery);
        Connection connection = this.getConnection(application);
        Statement statement = connection.createStatement();
        ;
        statement.executeQuery(sqlQuery);

        log.info("************     DB Query executed successfully     ***************");
    }

    public int executeUpdate(Connection conn, String sqlQuery) throws SQLException {
        log.info("*********      Executing DB Query     ************");
        log.info(sqlQuery);

        Statement statement = conn.createStatement();
        ;
        int effectedRowsNumber = statement.executeUpdate(sqlQuery);
        //conn.commit();

        log.info("************     DB Query executed successfully     ***************");

        return effectedRowsNumber;
    }

    public void closeDBConnection(Connection con) throws SQLException {
        log.info("******     Closing DB Connection      *******");
        con.close();
        log.info("******      DB Connection closed successfully     *******");
    }

    public int getCountResult(Connection connection, String query, List<Object> parameters) throws SQLException {
        PreparedStatement statement = connection.prepareStatement(query);
        int index = 1;
        for (Object parameter : parameters) {
            statement.setObject(index++, parameter);
        }
        try (ResultSet resultSet = statement.executeQuery()) {
            if (!resultSet.next()) throw new SQLException("Should have returned at least the count value");
            return resultSet.getInt(1);
        }
    }

    public int getCountResult(Connection connection, String query, Object parameter) throws SQLException {
        log.info(String.format("Executing query '%s' with parameter '%s'", query, parameter));
        return getCountResult(connection, query, Collections.singletonList(parameter));
    }

}
