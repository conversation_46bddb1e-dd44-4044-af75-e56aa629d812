package core.constants.saa;

public class GeneralConstants {


    //  **********************   Test Data config file and its properties key names ***************************

    //Test data configs file and its properties key names
    public static final String TEST_DATA_CONFIG_FILE_NAME = System.getProperty("user.dir") + "/src/test/resources/configFiles/saa/TestDataConfig.properties";

    // Test data strategy to get test data source type and implementing classes
    public static final String TEST_DATA_TYPE = "TestDataType";
    public static final String TEST_DATA_TYPE_CLASS_PATH = "TestDataStrategyClassPath_";

    //  **********************   General config file and its properties key names ***************************
    public static final String GENERAL_CONFIG_FILE_NAME = System.getProperty("user.dir") + "/src/test/resources/configFiles/saa/GeneralConfigs.properties";
    public static final String SAA_SAMPLE_MESSAGES_DIRECTORY = "/src/test/resources/testDataFiles/saaMessagesTD";

    public static final String FIN_INPUT_MESSAGE_DIRECTORY_ON_MESSAGE_PARTNER_SERVER = "/tmp/FileInput/";
    public static final String ISO_INPUT_MESSAGE_DIRECTORY_ON_MESSAGE_PARTNER_SERVER = "/tmp/ISOFileInput/";
    public static final String DEFAULT_DOWNLOAD_PATH = "defaultDownloadPath";
    public static final String DEFAULT_UPLOAD_PATH = "defaultUploadPath";

    // Extent report configs
    public static final String SCREENSHOT_FAILD_TESTS_PATH = "screenshotsOfFailedTestsPath";
    public static final String EXTENT_REPORT_FILE_PATH = "extentReportFilepath";
    public static final String EXTENT_REPORT_TITLE = "extentReportTitle";
    public static final String EXTENT_REPORT_NAME = "extentReportName";
    public static final String ADD_LOG_TO_EXTENT_REPORT = "addLogToExtentReport";


    // *****************     Database config file and its properties key names     **************************
    public static final String DB_CONFIG_FILE_NAME = "configFiles//saa//DBConfigs.properties";

    //Different DB configs
    public static final String FILTERING_DB_NAME = "FILTERING";
    public static final String FILTERING_DB_URL_KEY = "FILTERING_DB_URL";
    public static final String FILTERING_DB_USERNAME_KEY = "FILTERING_DB_Username";
    public static final String FILTERING_DB_PASSWORD_KEY = "FILTERING_DB_Password";

    public static final String ADMIN_DB_NAME = "ADMIN";
    public static final String ADMIN_DB_URL_KEY = "ADMIN_DB_URL";
    public static final String ADMIN_DB_USERNAME_KEY = "ADMIN_DB_Username";
    public static final String ADMIN_DB_PASSWORD_KEY = "ADMIN_DB_Password";

    // Login Credentials

    public static final String SCREENING_LOGIN_URL = "screening.login.url";
    public static final String SAA_LOGIN_URL = "saa.login.url";

    public static final String LOGIN_TYPE_ADMIN = "admin";
    public static final String VALID_ADMIN_MAIL = "login.admin.mail";
    public static final String VALID_ADMIN_PASSWORD = "login.admin.password";


    public static final String VALID_FILTERING_MAIL = "login.filtering.userMail";
    public static final String VALID_FILTERING_PASSWORD = "login.filtering.password";

    public static final String VALID_SAA_MAIL = "login.saa.userMail";
    public static final String VALID_SAA_PASSWORD = "login.saa.password";
    public static final String SAA_SERVER_INSTANCE = "login.saa.server.instance";

    public static final String SWF_APP_NAME = "SWF";
    public static final String SAA_APP_NAME = "SAA";

    public static final String REMOTE_SERVER_IP = "remoteServerIp";
    public static final String REMOTE_SERVER_USERNAME = "remoteServerUserName";
    public static final String REMOTE_SERVER_PASSWRD = "remoteServerPassword";
    public static final String REMOTE_SERVER_PORT = "remoteServerPort";

    public static final String FAILED = "Failed";

    public static final String VALID = "valid";
    public static final String INVALID = "InValid";
    public static final String REAL_VIOLATION = "realViolation";
    public static final String FALSE_POSITIVE = "falsePositive";
    public static final String PENDING = "pending";
    public static final String GOOD_GUY = "goodGuy";
    public static final String DONT_KNOW = "dontKnow";
    public static final String CLEAN = "clean";
    public static final String STATIC_LIST_NAME = "UN Al-Qaida";

    public static final String ACTUAL_EXPECTED_ERR_MSG = "Test Failed due to an actual and expected results mismatched";


    public static final String SEARCH_MESSAGE_URL = "#com.swift.Access.samSearch";

}
