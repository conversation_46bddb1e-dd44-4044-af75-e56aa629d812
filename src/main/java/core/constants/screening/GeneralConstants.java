package core.constants.screening;

import core.util.Randomizer;

import java.util.ArrayList;
import java.util.List;

public class GeneralConstants {


    //  **********************   Test Data config file and its properties key names ***************************

    //Test data configs file and its properties key names

    public static final String SCREENING_TEST_DATA_CONFIG_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/configFiles/screening/TestDataConfig.properties";
    public static final String SMOKE_TEST_CONFIG_FILE_NAME = System.getProperty("user.dir") + "/src/test/resources/configFiles/screening/SmokeTestConfigs.properties";

    // Test data strategy to get test data source type and implementing classes
    public static final String TEST_DATA_TYPE = "TestDataType";
    public static final String TEST_DATA_TYPE_CLASS_PATH = "TestDataStrategyClassPath_";

    //  **********************   General config file and its properties key names ***************************
    public static final String GENERAL_CONFIG_FILE_NAME = System.getProperty("user.dir") + "/src/test/resources/configFiles/screening/GeneralConfigs.properties";

    public static final String DEFAULT_DOWNLOAD_PATH = "defaultDownloadPath";
    public static final String DEFAULT_UPLOAD_PATH = "defaultUploadPath";
    public static final String SCREENSHOT_FAILD_TESTS_PATH = "screenshotsOfFailedTestsPath";


    // Extent report configs
    public static final String EXTENT_REPORT_FILE_PATH = "extentReportFilepath";
    public static final String EXTENT_REPORT_TITLE = "extentReportTitle";
    public static final String EXTENT_REPORT_NAME = "extentReportName";
    public static final String ADD_LOG_TO_EXTENT_REPORT = "addLogToExtentReport";

    // Login Credentials
    public static final String SCREENING_LOGIN_URL = "screening.login.url";

    public static final String VALID_ADMIN_MAIL = "login.admin.mail";
    public static final String VALID_ADMIN_PASSWORD = "login.admin.password";


    // General constants
    public static final String SUCCESS = "Success";
    public static final String FAILED = "Failed";
    public static final String TRUE = "TRUE";
    public static final String FALSE = "FALSE";
    public static final String SAVE = "Save";
    public static final String STRING_DELIMETER = "#";
    public static final String FILE_DELIMETER = "/";
    public static final String MISMATCH_ERR_MSG = " Value in DB Mismatched the VALUE in page";
    public static final String POM_EXCEPTION_ERR_MSG = "Test Failed due to an exception occurred in POM's method";
    public static final String ACTUAL_EXPECTED_ERR_MSG = "Test Failed due to an actual and expected results mismatched";
    public static final String DB_ERROR_MSG = "No results found in DB or DB error occurred";

    // Deployment server's configs to get server
    public static final String REMOTE_SERVER_IP = "remoteServerIp";
    public static final String REMOTE_SERVER_USERNAME = "remoteServerUserName";
    public static final String REMOTE_SERVER_PASSWORD = "remoteServerPassword";
    public static final String REMOTE_SERVER_PORT = "remoteServerPort";

    public static final String DB_URL = "db.url";
    public static final String DB_HOST = "db.host";
    public static final String DB_PORT = "db.port";
    public static final String DB_NAME = "db.name";
    public static final String DB_USERNAME = "db.username";
    public static final String DB_PASSWORD = "db.swserver.password";
    public static final String DB_TYPE = "db.type";

    //Dow-Jones Data
    //checkPath
    public static final String LOGIN_FILE_NAME = "LoginFileName" + new Randomizer().getInt() + ".swl";
    public static final String DJ_LOGIN_FILE_CREATOR_PATH = "%EASTNETS_CONFIG_HOME%/../swtools/JLoginFileCreator";
    public static final String UTILS_FOLDER_PATH = "%EASTNETS_CONFIG_HOME%\\..\\services/services/utilities";
    public static final String DJ_JARFILE_NAME = "dj-cli-5.2.1";
    public static final String WC_JARFILE_NAME = "wc-cli-5.2.1";

    public static final String SWF_APP_NAME = "SWF";
    public static final String SAA_APP_NAME = "SAA";
    public static final String FATF_MESSAGES_DIRECTORY = "/src/test/resources/testDataFiles/fileScanTD/";

    //Browse Archive
    public static final String ARCHIVE_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/DataFiles/Events_2012-11-14_1026_valid.xml";

    public static final String UPLOAD_FILE_PATH = "/src/test/resources/uploadsAndDownloads/uploads";
    public static final String DOWNLOAD_FILE_PATH = "/src/test/resources/uploadsAndDownloads/downloads";
    public static final String LIST_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/DataFiles/List";

    //Private List Names and Files
    public static final String BUG_94456_Test1_Name = "TEST1.ofc";
    public static final String BUG_94456_Test2_Name = "TEST2.ofc";

    public static final String BUG_94456_TEST1_LIST = LIST_FILE_PATH + "/OFC/TEST1.ofc";
    public static final String BUG_94456_TEST2_LIST = LIST_FILE_PATH + "/OFC/TEST2.ofc";
    public static final String BUG_94456_FORMAT = System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/ExportFormat_94456_List[2024_03_05_10_46].xml";
    public static final String BUG_94456_ENTRIES = System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/FN.txt";
    public static final String BUG_94456_ENTRIES_2 = System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/FN2.txt";
    public static final String BUG_94456_UFC_LIST = System.getProperty("user.dir") + "/src/test/resources/DataFiles/List/UFC/AyahList-20231030.ufc";


    //Korean List Name And File Name
    public static final String KOREAN_LIST_NAME = "UN Democratic People Republic of Korea";
    public static final String KOREAN_LIST_FILE_PATH = LIST_FILE_PATH + "/OFC/un_dprkorea.ofc";

    //Iran List Name And File Name
    public static final String IRAN_LIST_NAME = "UN Iran";
    public static final String IRAN_LIST_FILE_PATH = LIST_FILE_PATH + "/OFC/un_iran.ofc";

    public static final String TEST_ARMENIA_LIST_NAME = "Test Armenia";
    public static final String TEST_ARMENIA_LIST_FILE_PATH = LIST_FILE_PATH + "/UFC/Test-Armenia-20221212.ufc";
    public static final String RJE_KOREA_FILE_PATH = System.getProperty("user.dir") + UPLOAD_FILE_PATH + "/RJE - korea.txt";


    public static final String ENTRY_LIST_FILE_PATH = UPLOAD_FILE_PATH + "/Entities.txt";
    public static final String IMPORT_ENTRY_INDIVIDUAL_LIST_FILE_PATH = UPLOAD_FILE_PATH + "/EntryList-Individual.txt";
    public static final String IMPORT_ENTRY_NAMES_AKA_LIST_FILE_PATH = UPLOAD_FILE_PATH + "/EntryListNamesAndAKA.txt";

    public static final String IMPORT_ENTRY_GROUP_LIST_FILE_PATH = UPLOAD_FILE_PATH + "/EntryList-Group.txt";
    public static final String IMPORT_ENTRY_NAME_TYPE_LIST_FILE_PATH = UPLOAD_FILE_PATH + "/ImportEntryAndType.txt";
    public static final String BICS_SCAN_FORMAT_FILE_PATH = UPLOAD_FILE_PATH + "/ExportFormat_Payment.xml";

    public static String IMPORT_FORMAT_SAMPLE_FILE = UPLOAD_FILE_PATH + "/SinglePacsFormatSample.xml";
    public static String IMPORT_FORMAT_FILE = UPLOAD_FILE_PATH + "/SinglePacsFormat.xml";
    public static String FILE_SCAN_PATH = UPLOAD_FILE_PATH + "/V_06_Scan.xml";

    //checkPath
    public static String START_SEGREGATE_SCAN_SERVICE_COMMAND = "start /D %EASTNETS_CONFIG_HOME%\\..\\services\\Scan_Segregation services-start.bat && echo started";
    public static String START_DEFAULT_SCAN_SERVICE_COMMAND = "start /D %EASTNETS_SWFNG_HOME% services-start.bat && echo Started";
    public static String STOP_SCAN_SERVICE_COMMAND = "start /D %EASTNETS_SWFNG_HOME% stop-all.bat && echo Started";
    public static String STOP_ALL_COMMAND = "start /D %EASTNETS_CONFIG_HOME%\\..\\services stop-all.bat && echo Started";


    public static String STOP_ALL_SERVICES_COMMAND = "start /D %EASTNETS_CONFIG_HOME%\\..\\services stop-services.bat && echo Started";

    public static String STOP_SWS_SERVER_COMMAND = "TASKKILL /IM swserver_sqlserver_unicode.exe /t /f && echo Started";


    public static String STOP_APPLICATION_MANAGER_COMMAND = "wmic Path win32_process Where \"commandLine like '%%side-applimanager%%'\" Call Terminate";

    public static String START_APPLICATION_MANAGER_COMMAND = "start /D %EASTNETS_CONFIG_HOME%\\..\\ApplicationManager java -jar side-applimanager.jar && echo Started";
    public static String START_SWS_SERVER_COMMAND = "start /D %EASTNETS_CONFIG_HOME%\\..\\swserver Start.cmd && echo Started";
    public static String START_REPORT_SERVER_COMMAND = "start /D %EASTNETS_CONFIG_HOME%\\..\\ReportServer enReportSrv.bat && echo Started";

    public static String START_KAFKA_COMMAND = "start /D %EASTNETS_CONFIG_HOME%\\..\\services kafka-start.bat && echo Started";
    public static String START_ZOO_KEEPER_COMMAND = "start /D %EASTNETS_CONFIG_HOME%\\..\\services zookeeper-start.bat && echo Started";
    public static String START_SERVICES_COMMAND = "start /D %EASTNETS_CONFIG_HOME%\\..\\services services-start.bat && echo Started";
    public static String START_TOMCAT_COMMAND = "start /D %EASTNETS_CONFIG_HOME%\\..\\apache-tomcat\\bin startup.bat && echo Started";
    public static String STOP_TOMCAT_COMMAND = "start /D %EASTNETS_CONFIG_HOME%\\..\\apache-tomcat\\bin stop.bat && echo Started";

    public static final String SCAN_FILE_FOR_FORMAT = System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/ContextTestFileScan.txt";
    public static final String RJE_REPEAT_SCAN_FILE = System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/RJE_Repeat.txt";
    public static final String IGNORE_MATCHES_FILE = "/src/test/resources/uploadsAndDownloads/uploads/IgnoreMatches.ufc";

    //Lock and upgrade black list
    public static String CONGO_LIST_NAME = "UN Democratic Republic of Congo";
    public static String CONGO_LIST_FILE_PATH = LIST_FILE_PATH + "/OFC/un_drcongo_2007.ofc";
    public static String UPGRADE_CONGO_LIST_FILE_PATH = LIST_FILE_PATH + "/OFC/un_drcongo.ofc";

    public static String UN_YAMEN_LIST_NAME = "UN Yemen";
    public static String UN_YAMEN_LIST_FILE_PATH = "/src/test/resources/DataFiles/List/OFC/un_yemen.ofc";
    public static String UN_YAMEN2_LIST_FILE_PATH = "/src/test/resources/DataFiles/List/OFC/UN_Yemen2.ofc";

    public static String GG_MIGRATION_ENTRY_LIST_FILE_PATH = UPLOAD_FILE_PATH + "/GGMigration-EntryList.txt";
    public static String MT103_BOTH_FILE_PATH = System.getProperty("user.dir") + UPLOAD_FILE_PATH + "/MT103-both.txt";
    public static String MT103_NO_FILE_PATH = System.getProperty("user.dir") + UPLOAD_FILE_PATH + "/MT103-no.txt";

    public static String IMPORT_NON_SHARED_GOOD_GUY_FILE_PATH = UPLOAD_FILE_PATH + "/Goodguy_Import23.xml";
    public static String IMPORT_NON_SHARED_GOOD_GUY_WITH_ACCENTATA_PATH = UPLOAD_FILE_PATH + "/good-guys-éé_Import.xml";
    public static String IMPORT_SHARED_GOOD_GUY_FILE_PATH = UPLOAD_FILE_PATH + "/Goodguy_Import24.xml";
    public static String FILE_SCANNER_FORMAT_FILE_PATH = System.getProperty("user.dir") + UPLOAD_FILE_PATH + "/FileScannerFormat.txt";
    public static String FILE_SCANNER_FORMAT_DUPLICATE_FILE_PATH = System.getProperty("user.dir") + UPLOAD_FILE_PATH + "/FileScannerFormatDuplicateRecords.txt";
    public static String FILE_SCANNER_FORMAT_DUPLICATE_NAME = "FileScannerFormatDuplicateRecords.txt";
    public static String COMPARE_SESSION_INFO_FILE_PATH = System.getProperty("user.dir") + UPLOAD_FILE_PATH + "/CompareSessionsInfo.txt";
    public static String COMPARE_SESSION_INFO1_FILE_PATH = System.getProperty("user.dir") + UPLOAD_FILE_PATH + "/CompareSessionsInfo1.txt";
    public static String SCAN_FILE_DETECTION_GROUPED_BY_STATUS_FILE_PATH = System.getProperty("user.dir") + UPLOAD_FILE_PATH + "/scanFileForDetectionsGroupedByStatus.txt";
    public static String COMPARE_SESSION_INFO_FILE_NAME = "CompareSessionsInfo";


    public static String INSERT_BICS_QUERY_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/DataFiles/sqlQueries/InsertBicsQuery.txt";
    public static String COMPLEX_VIOLATION_FILTER_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/DataFiles/ViolationFilter";

    public static String BICS_SCAN_FILE_PATH = UPLOAD_FILE_PATH + "/Scan_BIC.xml";
    public static String BICS_SCAN_IOS_FILE_PATH = UPLOAD_FILE_PATH + "/Test IBAN.xml";
    public static String RJE_SWIFT_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/fileScanTD/RJE_Swift.txt";


    public static String IMPORT_ISO_GROUP_CONFIGURATIONS_FILE_PATH = System.getProperty("user.dir") + UPLOAD_FILE_PATH + "/ExportISO20022Format_[pacs.008.001.08].xml";
    public static String PACS_008_001_08_FILE_PATH = System.getProperty("user.dir") + UPLOAD_FILE_PATH + "/ISO/ISO20022Format_[pacs.008.001.08].xml";
    public static String PAYMENT_FORMAT_FILE_PATH = System.getProperty("user.dir") + UPLOAD_FILE_PATH + "/ISO/ExportFormat_PaymentLT.xml";


    public static String ISO_WITHOUT_EXTENSION_FILENAME = "/ISO/WithoutExtension";

    //Ignore Matches
    public static String IGNORE_MATCHES_FILE_PATH = UPLOAD_FILE_PATH + "/IgnoreMatches.ufc";
    public static String ISLAMIC_NAMES_FILE_PATH = "/src/test/resources/testDataFiles/IslamicNames.csv";
    public static String ISLAMIC_NAMES_ENTRY_LIST_FILE_PATH = "/src/test/resources/testDataFiles/IslamicNamesEntryList.txt";
    public static String ARABIC_NAMES_ENTRY_LIST_FILE_PATH = "/src/test/resources/testDataFiles/ImportEntriesFiles/ArabicNames.txt";
    public static String SCAN_ARABIC_NAMES_RESULTS_FILE_PATH = "/src/test/resources/testDataFiles/ScanComparision/ScanArabicPhonetics.csv";
    public static String RUSSIAN_NAMES_ENTRY_LIST_FILE_PATH = "/src/test/resources/testDataFiles/ImportEntriesFiles/RussianNames.txt";
    public static String SCAN_RUSSIAN_NAMES_RESULTS_FILE_PATH = "/src/test/resources/testDataFiles/ScanComparision/ScanRussianPhonetics.csv";

    public static String NEUTRAL_WORDS_FILE_PATH = "/src/test/resources/testDataFiles/NeutralWords.csv";
    public static String NEUTRAL_WORDS_ENTRY_LIST_FILE_PATH = "/src/test/resources/testDataFiles/NeutralWordsEntryList.txt";


    public static String SYNONYMS_ENTRY_LIST_FILE_PATH = "/src/test/resources/testDataFiles/SynonymsEntryList.txt";
    public static String SYNONYMS_IGNORE_MATCHES_FILE_PATH = "/src/test/resources/testDataFiles/Synonyms.csv";

    public static String EXTRA_WORDS_PATH = UPLOAD_FILE_PATH + "/Extra_Words.xlsx";
    public static String EXTRA_WORDS_IGNORE_MATCHES_PATH = "/src/test/resources/testDataFiles/ExtraWords.csv";
    public static String COMPOUND_LAST_NAME_PATH = UPLOAD_FILE_PATH + "/Compound_Last_Name.xlsx";
    public static String COMPOUND_LAST_NAME_IGNORE_MATCHES_PATH = "/src/test/resources/testDataFiles/CompoundLastName.csv";
    public static String MIDDLE_NAME_PATH = UPLOAD_FILE_PATH + "/Middle_Name.xlsx";
    public static String MIDDLE_NAME_IGNORE_MATCHES_PATH = "/src/test/resources/testDataFiles/MiddleName.csv";
    public static String COMPOUND_FIRST_LAST_PATH = UPLOAD_FILE_PATH + "/Compound_First_Name.xlsx";
    public static String COMPOUND_FIRST_LAST_IGNORE_MATCHES_PATH = "/src/test/resources/testDataFiles/CompoundFirstLast.csv";
    public static String ONE_WORD_AKA_PATH = UPLOAD_FILE_PATH + "/One_Word_AKA.xlsx";
    public static String ONE_WORD_AKA_IGNORE_MATCHES_PATH = "/src/test/resources/testDataFiles/OneWordAKA.csv";

    public static String OFC_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/DataFiles/List/OFC";
    public static String RJE_BICS_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/DataFiles/FileScan/RJE_BICS.txt";
    public static String FILE_SCANNER_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/DataFiles/FileScan/FileScanner.txt";
    public static String XML_SCANNER_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/DataFiles/FileScan/XML.xml";
    public static String RJE_SINGAPORE_SCANNER_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/DataFiles/FileScan/SWIFT_RJE_Singapore.txt";
    public static String RJE_BICS_IN_BODY_SCANNER_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/DataFiles/FileScan/RJE_BICS - In Body.txt";
    public static String RJE_BICS_NO_LICENSE_SCANNER_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/DataFiles/FileScan/RJE_BICS_NoLicense.txt";

    public static String SWIFT_01_SCAN_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/SwiftImprovement/SWIFT_Inhancement_001.txt";
    public static String SWIFT_02_SCAN_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/SwiftImprovement/SWIFT_Inhancement_002.txt";
    public static String SWIFT_03_SCAN_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/SwiftImprovement/SWIFT_Inhancement_003.txt";
    public static String SWIFT_04_SCAN_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/SwiftImprovement/SWIFT_Inhancement_004.txt";
    public static String SWIFT_05_SCAN_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/SwiftImprovement/SWIFT_Inhancement_005.txt";
    public static String SWIFT_06_SCAN_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/SwiftImprovement/SWIFT_Inhancement_006.txt";
    public static String SWIFT_07_SCAN_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/SwiftImprovement/SWIFT_Inhancement_007.txt";
    public static String SWIFT_08_SCAN_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/SwiftImprovement/SWIFT_Inhancement_008.txt";
    public static String SWIFT_09_SCAN_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/SwiftImprovement/SWIFT_Inhancement_009.txt";
    public static String SWIFT_10_SCAN_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/SwiftImprovement/SWIFT_Inhancement_010.txt";
    public static String SWIFT_11_SCAN_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/SwiftImprovement/SWIFT_Inhancement_011.txt";
    public static String SWIFT_12_SCAN_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/SwiftImprovement/SWIFT_Inhancement_012.txt";
    public static String SWIFT_13_SCAN_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/SwiftImprovement/SWIFT_Inhancement_013.txt";
    public static String SWIFT_14_SCAN_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/SwiftImprovement/SWIFT_Inhancement_014.txt";
    public static String SWIFT_15_SCAN_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/SwiftImprovement/SWIFT_Inhancement_015.txt";
    public static String SWIFT_16_SCAN_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/SwiftImprovement/SWIFT_Inhancement_016.txt";
    public static String SWIFT_17_SCAN_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/SwiftImprovement/SWIFT_Inhancement_017.txt";
    public static String SWIFT_18_SCAN_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/SwiftImprovement/SWIFT_Inhancement_018.txt";
    public static String SWIFT_19_SCAN_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/SwiftImprovement/SWIFT_Inhancement_019.txt";
    public static String SWIFT_20_SCAN_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/SwiftImprovement/SWIFT_Inhancement_020.txt";


    public static String DB_SCAN_FILE_PATH = "/src/test/resources/testDataFiles/csvScanTable.csv";
    public static String DB_SCAN_DETECTION_MANAGER_FILE_PATH = "/src/test/resources/testDataFiles/DBScanFile.csv";
    public static String LT_ISO_001_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/92216/LT_ISO_001.xml";
    public static String LT_ISO_002_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/92216/LT_ISO_002.xml";
    public static String RJE_LESS_THAN_1000_EXTERNAL_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/92216/test_newgens_LessThan1000_ExternalViolation.txt";
    public static String RJE_MORE_THAN_1000_REPORTED_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/92216/MT304_005_OF_013_GreaterThan1000_ReportedViolations.txt";
    public static String CUSTOM_XML_LESS_THAN_1000_EXTERNAL_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/92216/TEST_BIC_LessThan1000_External.xml";
    public static String CUSTOM_XML_MORE_THAN_1000_REPORTED_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/92216/TEST_BIC_GreaterThan1000_Reported.xml";


    // Docker configuration details
    public static final String DOCKER_CONFIG_FILE = System.getProperty("user.dir") + "/src/test/resources/configFiles/screening/DockerConfig.properties";
    public static final String DOCKER_SERVER_IP = "serverIp";

    //insert Bics Query
    public static String[] DB_QUERY = {
            /*Q0*/  "update tblacklists set Shared = 0 where shared = 1 AND display_name != '[cmlist]'",
            /*Q1*/    "select comments from tAlerts where id =10",
            /*Q2*/    "update tGoodGuys set Shared = 0 where shared =1",
            /*Q3*/    "update tReplayViolation set last_update_time = '2018-08-21 12:01:02.343'",
            /*Q4*/    "update tConfig set variable_value = 'Yes' where variable_name ='Replay feature'",
            /*Q5*/    "insert into tswiftbics values ('ANDPFRP1XXX','ASSOCIATION NATIONALE D','ENTRAIDE ET DE PREVOYANCE','PARIS','23 RUE PARADIS','75010 PARIS','FRANCE',0,0)",
            /*Q6*/    "insert into tswiftbics values ('ALEIGB22CAF','SANTANDER UK PLC (FORMERLY ALLIANCE AND LEICESTER PLC)','(COMMERCIAL FINANCE)','MANCHESTER','298 DEANSGATE','MANCHESTER M3 4HH','UNITED KINGDOM',0,0)",
            /*Q7*/    "delete from tSwiftBics",
            /*Q8*/    "update tConfig set variable_value = 'TRUE' where variable_name = 'ENHANCED_GLUEDWORDS'",
            /*Q9*/  "update tConfig set variable_value = 'BASIC' where variable_name = 'ENHANCED_GLUEDWORDS_MODE'",
            /*Q10*/ "update tConfig set variable_value = 'FALSE' where variable_name = 'ENHANCED_GLUEDWORDS'",
            /*Q11*/    "update tConfig set variable_value = 'TRUE' where variable_name = 'ARABIC_PHONETICS_ON'",
            /*Q12*/    "update tConfig set variable_value = 'FALSE' where variable_name = 'ARABIC_PHONETICS_ON'",
            /*Q13*/ "update tConfig set variable_value = 'TRUE' where variable_name = 'RUSSIAN_PHONETICS_ON'",
            /*Q14*/    "update tConfig set variable_value = 'FALSE' where variable_name = 'RUSSIAN_PHONETICS_ON'",
            /*Q15*/    "insert into tswiftbics values  ('PETASGS1XXX', 'PEGASUS TAIWAN' ,'null' , 'SINGAPORE','PRUDENTIAL TOWER FLOOR 14 30 CECIL STREET', 'SINGAPORE 49712' ,'SINGAPORE',0 ,0)",
            /*Q16*/    "update tReplayViolation set last_update_time = '21-Aug-18'",
            /*Q17*/    "insert into tswiftbics values ('ABNAMXMMXXX','ABC','ABD','ABDD','CBDA','M3 4HH','ARUBA',0,0)",
            /*Q18*/    "select count(*) from tBlackListEntries where black_list_id=(select id from tBlackListVersions where black_list_id =(select id from tBlackLists where display_name='%s'))",
            /*Q19*/ "select variable_value from tConfig where variable_name ='sw_server_port'",
            /*Q20*/ "update tblacklists set shared = 1 where display_name = '[cmlist]'",
            /*Q21*/ "update tPasswordPolicy set enabled = 0 where id = 1",
            /*Q22*/    "update tConfig set variable_value = 'Yes' where variable_name = 'Change detection status to DontKnow'",
            /*Q23*/    "update tConfig set variable_value = 'Yes' where variable_name = 'AutomaticSearch'",
            /*Q24*/    "update tConfig set variable_value = 'Yes' where variable_name = 'DetectionActionButtons'",
            /*Q25*/    "update tConfig set variable_value = 'Yes' where variable_name = 'EnableAuditTrailForAllScanRequest'",
            /*Q26*/    "update tConfig set variable_value = 'Yes' where variable_name = 'GroupFilterBlockRelease'",
            /*Q27*/    "update tConfig set variable_value = 'Yes' where variable_name = 'GroupFilterAssignPending'",
            /*Q28*/    "update tConfig set variable_value = 'Yes' where variable_name = 'AlternateDetectionVisibility'",
            /*Q29*/    "update tConfig set variable_value = 'Yes' where variable_name = 'Enable improved interpretation of SWIFT Fields'",
            /*Q30*/    "update tConfig set variable_value = 'Yes' where variable_name = 'EnableReasonCode'",
            /*Q31*/    "update tConfig set variable_value = 'Yes' where variable_name = 'AutoResetInvestigatorAssignation'",
            /*Q32*/    "update tConfig set variable_value = 'Yes' where variable_name = 'EnableReadColumnOnAlert'",
            /*Q33*/    "update tConfig set variable_value = 'Yes' where variable_name = 'EnableAdvancedChecksumCalculation'",
            /*Q34*/    "update tConfig set variable_value = 'No' where variable_name = 'pessimistic_mode'",
            /*Q35*/    "update tConfig set variable_value = 'No' where variable_name = 'enable_on_all_detections'",
            /*Q36*/    "update tConfig set variable_value = 'MANDATORY' where variable_name = 'Alert_Comments_Req'",
            /*Q37*/ "INSERT INTO tSwiftBics (BIC, institution, branch, city, address, location, country, checksum, internal)" +
            " VALUES(N'ARMCAM22XXX', N'ARARATBANK OJSC', NULL, N'YEREVAN', N'19 PUSHKIN STR.', N'0002 YEREVAN', 'Armenia', 0, 0)",
            /*Q38*/ "INSERT INTO tSwiftBics" +
            "(BIC, institution, branch, city, address, location, country, checksum, internal)\n" +
            "VALUES(N'ARMCAM22XXX', N'ARARATBANK OJSC', NULL, N'YEREVAN', N'19 PUSHKIN STR.', N'0002 YEREVAN', N'Armenia', 0, 0);",
            /*Q39*/ "INSERT INTO dbo.tSwiftBics (BIC,institution,branch,city,address,location,country,checksum,internal) VALUES ('ABDIIQBAXXX','ABU DHABI ISLAMIC BANK',NULL,'BAGHDAD','ARASAT AL HINDIA','SHOP NO 148','Iraq',0,0);",
            /*Q40*/ "INSERT INTO dbo.tSwiftBics (BIC,institution,branch,city,address,location,country,checksum,internal) VALUES ('BTEJFRPPXXX','BANK TEJARAT (PARIS)',NULL,'RIGA','VESETAS STREET 7','LV-1013 RIGA','LATVIA',0,0);",
            /*Q41*/ "INSERT INTO dbo.tSwiftBics (BIC,institution,branch,city,address,location,country,checksum,internal) VALUES ('ANDPFRP1XXX','ASSOCIATION NATIONALE D',NULL, 'PARIS','23 RUE PARADIS','75010 PARIS','FRANCE',0,0);",
            /*Q42*/"update tConfiguration set variable_value = 'No' where variable_name = 'EXPOSE_PATHS'",
            /*Q43*/"update tConfiguration set variable_value = 'Yes' where variable_name = 'EXPOSE_PATHS'",
            /*Q44*/ "if not exists (select * from tConfiguration where variable_name = 'CSV_QUOTE')\n" +
            "begin\n" +
            "insert into tconfiguration(APPLICATION, USER_NAME, SECTION_NAME, VARIABLE_NAME, VARIABLE_VALUE) values ('SafeWatch Server', NULL, 'Engine', 'CSV_QUOTE', '\"');\n" +
            "end",
            /*Q45*/"update tConfiguration set variable_value = '%s' where variable_name = 'fileSizeLimit'",
            /*Q46*/"update tConfiguration set variable_value = 'true' where variable_name = 'detectionManager' and SECTION_NAME= 'SearchOnLoad'",
            /*Q47*/"update tConfiguration set variable_value = 'true' where variable_name = 'listSetManager' and SECTION_NAME= 'SearchOnLoad'",
            /*Q48*/"\n",
    };


    public static String SET_SYMSPELL_PREFIX_LENGTH_QUERY = "update tConfiguration set variable_value = %s where variable_name = 'SYMSPELL_PREFIX_LENGTH'";


    public static String WC_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/WorldCheckTD/WorldCheckAutomation.bat";
    public static String[] WORLD_CHECK_COMMANDS = {
            /*CMD0*/  "java -jar #JAR_FILE_NAME#.jar connectString=localhost:2181,localhost:2182,localhost:2183 swlUri=#LOGIN_FILE# data=#DATA_FILE#.csv selectionSetName=#WORLD_CHECK_LIST# loadLowQualityAka=true",
            /*CMD1*/  "java -jar #JAR_FILE_NAME#.jar connectString=localhost:2181,localhost:2182,localhost:2183 swlUri=#LOGIN_FILE# data=#DATA_FILE#.csv selectionSetName=#WORLD_CHECK_LIST#",
            /*CMD2*/  "java -jar #JAR_FILE_NAME#.jar connectString=localhost:2181,localhost:2182,localhost:2183 swlUri=#LOGIN_FILE# deleteFileName=#DATA_FILE#.csv selectionSetName=#WORLD_CHECK_LIST#",
            /*CMD3*/  "java -jar #JAR_FILE_NAME#.jar connectString=localhost:2181,localhost:2182,localhost:2183 swlUri=#LOGIN_FILE#  nativeFileName=\"#NATIVE_FILE#.csv\" selectionSetName=#WORLD_CHECK_LIST#  data=#DATA_FILE#.csv",
            /*CMD4*/  "java -jar #JAR_FILE_NAME#.jar connectString=localhost:2181,localhost:2182,localhost:2183 swlUri=#LOGIN_FILE# ref=\"#REF_FILE#.txt\"",
            /*CMD5*/  "java -jar #JAR_FILE_NAME#.jar connectString=localhost:2181,localhost:2182,localhost:2183 swlUri=#LOGIN_FILE# umbrella=\"#REF_FILE#.csv\"",
            /*CMD6*/  "java -jar #JAR_FILE_NAME#.jar connectString=localhost:2181,localhost:2182,localhost:2183 swlUri=#LOGIN_FILE# data=#DATA_FILE#.csv selectionSetName=#WORLD_CHECK_LIST# upgradeBlackListVersion=true"

    };

    public static final String FILE_SCAN_92732 = System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/File Scan/92732_FileScan.txt";
    public static final String Format_92732 = System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/formats/ExportFormat_BNP_test.xml";
    public static final String FILE_SCAN_93418 = System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/File Scan/93418_FileScan.txt";

    public static String TKT87047_Message_1 = System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/ISO/Pacs008-ctry_4_char.xml";
    public static String TKT87047_Message_2 = System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/ISO/DAZ_FATF5-IBAN-COuntryISOcode.xml";
    public static String TKT89427_Message = System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/TEST_BIC.xml";
    public static String TKT89687_Message = System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/23018083.txt";
    public static String TKT90302_Message = System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/760_2.txt";
    public static String TKT89627_Format = System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/ISO/ExportISO20022Format_[pacs.008.001.08]_05-09-2024_103457.xml";
    public static String TKT89627_Message = System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/ISO/ISOAdvice-NoDocumentTag.xml";
    public static String TKT84163_Format = System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/ISO/ExportISO20022Format_[pacs.008.001.08]_28-08-2024_152431.xml";
    public static String TKT95112_Format = System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/ISO/ExportISO20022Format_[pacs.008.001.08]_27-02-2024_103146.xml";
    public static String TKT84163_Message = System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/ISO/ISOAdvice-NoDocumentTag.xml";
    public static String TKT93629_Message_STP = System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/MT103-STP.txt";
    public static String TKT93629_Message_REMIT = System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/MT103-REMIT.txt";
    public static String TKT92810_MESSAGE = System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/ISO/" + "LT_ISO_002 2.xml";
    public static String TKT93989_FORMAT_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/93989/Format_BNP_Circumflex.xml";
    public static String TKT93989_SCAN_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/93989/Scan_File_BNP_Circumflex.txt";
    public static String TKT74566_SCAN_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/74566/5000RJE-100PercHits_blocked.rje";

    public static String GG_CONTEXT_CONDITON_FORMAT_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/GG_CONTEXTCONDITION_FORMAT.xml";
    public static String GG_CONTEXT_CONDITON_SCANFILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/GG_ContextCondition_ScanFile.xml";

    public static String BULK_ASSIGN_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/BulkAssign.txt";
    public static String DEFECT_97255 = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/Defect97255.txt";

    public static String FORMAT_92732 = System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/formats/ExportFormat_BNP_Circumflex[2023_12_27_09_49].xml";
    public static String TKT92735_Message = System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/BNP_Circumflex.txt";
    public static String TKT95666_LICENSE = System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/expired-lic-2.xml";
    public static String TKT87047_SCHEMA_1 = System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/pacs.008.001.02_1.xsd";
    public static String TKT87047_SCHEMA_2 = System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/pacs.008.001.08_1.xsd";
    public static String TKT95112_MESSAGE = System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/Pacs008_Defect95112.xml";


    public static String TKT95932_Format = System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/ISO/ExportISO20022Format_[pacs.008.001.08].xml";
    public static String TKT95932_Message = System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/ISO/pacs.008.xml";

    public static String TKT89277_SET_SOUNDEX_DICTIONARY_SQL_QUERY = "INSERT INTO tConfiguration(application, user_name, section_name, variable_name, variable_value, ZONE_ID, CAN_SEGREGATE_PER_ZONE)\n" +
            "SELECT 'SafeWatch Server', 'user_name','Scanning', 'SOUNDEX_DICTIONNARY_ENABLE', 'true', NULL, 0\n" +
            "WHERE NOT EXISTS (\n" +
            "SELECT 1 FROM tConfiguration WHERE variable_name = 'SOUNDEX_DICTIONNARY_ENABLE'\n" +
            ");";

    public static String TKT89277_SET_SOUNDEX_DICTIONARY_ORCALE_QUERY = "MERGE INTO tConfiguration target\n" +
            "USING (SELECT 'SOUNDEX_DICTIONNARY_ENABLE' AS variable_name FROM dual) source\n" +
            "ON (target.variable_name = source.variable_name)\n" +
            "WHEN NOT MATCHED THEN\n" +
            "INSERT  (ID,application, user_name, section_name, variable_name, variable_value, ZONE_ID, CAN_SEGREGATE_PER_ZONE)\n" +
            "    VALUES (300,'SafeWatch Server', 'user_name','Scanning', 'SOUNDEX_DICTIONNARY_ENABLE', 'true', NULL, 0);";

    public static String TKT91115_CUSTOM_LIST_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/91115/custom-list.txt";
    public static String TKT91115_CUSTOM_LIST_UPGRADE_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/91115/custom-list-upgrade.txt";
    public static String TKT95148_Format = System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/formats/ExportFormat_CAS_IPIN[2024_01_11_15_37].xml";
    public static String TKT95148_MESSAGE_SINGLE = System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/CAS_singleline.xml";
    public static String TKT95148_MESSAGE_SINGLE_WITHOUT_XML = System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/CAS_singleline_Without-XML.xml";
    public static String TKT95148_MESSAGE_MULTIPLE = System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/CAS_multilines.xml";
    public static String TKT95148_MESSAGE_MULTIPLE_WITHOUT_XML = System.getProperty("user.dir") + "/src/test/resources/uploadsAndDownloads/uploads/CAS_multilines_Without-XML.xml";

    public static ArrayList<String> DB_FIELED_TYPES = new ArrayList<String>() {{
        add("[SKIPPED]");
        add("FULL_NAME");
        add("LAST_NAME");
        add("FIRST_NAME");
        add("AKA_FULL_NAME");
        add("AKA_LAST_NAME");
        add("AKA_FIRST_NAME");
        add("ADDRESS");
        add("CITY");
        add("COUNTRY");
    }};


    public static String TKT116804_ENTITIES_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/116804/imported_entities.txt";
    public static String TKT126488_ENTITIES_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/126488/imported_entities.txt";
    public static String TKT126488_SCAN_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/126488/scanFile.txt";

}