package core.constants.screening;

public class ISOConstants {


    //  **********************   Test Data config file and its properties key names ***************************
    //Test data configs file and its properties key names
    public static String ISO_WITHOUT_EXTENSION_FILENAME = "/ISO/WithoutExtension";


    //  **********************   ISO20022 config file and its properties key names ***************************
    public static String ISO_NOWRAPPER_NOXML_FILENAME =System.getProperty("user.dir")+core.constants.screening.GeneralConstants.UPLOAD_FILE_PATH+ "/ISO/ISO_Nohdr-NoWrapper-NOXML.xml";
    public static String IMPORT_ISO_GROUP_CONFIGURATIONS_FILE_PATH =System.getProperty("user.dir")+core.constants.screening.GeneralConstants.UPLOAD_FILE_PATH+"/ExportISO20022Format_[pacs.008.001.08].xml";
    public static String MERGE_ISO_FORMAT_CONFIGURATIONS_FILE_PATH1 =System.getProperty("user.dir")+ GeneralConstants.UPLOAD_FILE_PATH+"/ISO/02_BAHTNET_Schema_FIToFIFinancialInstitutionCreditTransfer_(pacs.009.001.08)_V.1.4.xsd";
    public static String MERGE_ISO_FORMAT_CONFIGURATIONS_FILE_PATH2 =System.getProperty("user.dir")+ GeneralConstants.UPLOAD_FILE_PATH+"/ISO/03_BAHTNET_Schema_FIToFIFinancialInstitutionCreditTransfer_(pacs.009.001.08_COV)_V.1.4.xsd";

    public static String ISO_VALID_MESSAGEFORMAT_FILENAME =  System.getProperty("user.dir")+ GeneralConstants.UPLOAD_FILE_PATH+"/ISO/pacs.008.001.04.xsd";
    public static String ISO_INVALID_MESSAGEFORMAT_FILENAME =  System.getProperty("user.dir")+ GeneralConstants.UPLOAD_FILE_PATH+"/ISO/WithoutExtension";
    public static String ISO_VALID_JPEG_MESSAGEFORMAT_FILENAME =  System.getProperty("user.dir")+ GeneralConstants.UPLOAD_FILE_PATH+"/ISO/pacs.008.001.04.xsd";
    public static String ISO_VALID_EXE_MESSAGEFORMAT_FILENAME =  System.getProperty("user.dir")+ GeneralConstants.UPLOAD_FILE_PATH+"/ISO/pacs.008.001.04.xsd";
    public static String ISO_VALID_BIG_PDF_MESSAGEFORMAT_FILENAME =  System.getProperty("user.dir")+ GeneralConstants.UPLOAD_FILE_PATH+"/ISO/Eastnets SafeWatch Screening 5.0 - User Guide.pdf";
    public static String ISO_VALID_DOCX_MESSAGEFORMAT_FILENAME =  System.getProperty("user.dir")+ GeneralConstants.UPLOAD_FILE_PATH+"/ISO/Defect94486.odt";
    public static String ISO_VALID_HTML_MESSAGEFORMAT_FILENAME =  System.getProperty("user.dir")+ GeneralConstants.UPLOAD_FILE_PATH+"/ISO/EastnetsLogo.webp";


}
