package core.constants.screening;

import java.util.HashMap;

public class MDI_91682_Constants {

    //create hashmap by using below keys and null values
    public static final HashMap<String, String> EXPECTED_SCAN_RESULT_MAP = new HashMap<String, String>() {
        {
            put("<PERSON>", "97");
            put("<PERSON>", "98");
            put("<PERSON><PERSON>", "100");
            put("Abdel Fatah Abo Bak<PERSON>", "90");
            put("<PERSON><PERSON><PERSON>", "100");
            put("<PERSON><PERSON>", "100");
            put(" <PERSON>", "99");
            put("<PERSON><PERSON>", "83");
            put("<PERSON><PERSON><PERSON>", "97");
            put("<PERSON>", "76");
            put("Esmail Aly Nasr Elden ABBAS", "81");
            put("<PERSON>", "100");
            put("<PERSON>", "92");
            put("<PERSON>", "95");
            put("<PERSON><PERSON>", "88");
            put("<PERSON><PERSON><PERSON>", "98");
            put("<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "95");
            put("<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>", "99");
            put("<PERSON><PERSON>d <PERSON><PERSON><PERSON> <PERSON><PERSON> <PERSON><PERSON><PERSON>", "96");
            put("<PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "98");
            put("<PERSON> <PERSON> <PERSON>", "98");
            put("<PERSON><PERSON>s <PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON>", "92");
            put("<PERSON><PERSON><PERSON> <PERSON> <PERSON><PERSON><PERSON><PERSON> <PERSON> ElTirity", "92");
            put("Ebrahim Saleh Muhammad Yaqob", "95");
            put("Mohamed Mahdy Naged Nour", "98");
            put("Sayed Heshmat Allah Hayat Alghaeb", "92");
            put("Mohamed Abd Alkarim Alghamri", "94");
            put("Mohamed Taher Hamed", "99");
            put("ADEL ABDEL REHEM FOUAD YOUSEF AFIFY", "92");
            put("MIZAR ABDEL WAHAB ABDEL AAL ABD EL GELEL", "80");
            put("Mohamed Ebrahem Omar", "98");
            put("Usma Rushdi Asskkar", "90");
            put("Wael Mahmoud Abd Alal", "85");
        }
    };

    public static String TKT91682_BLACK_LIST_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/91682/blackList1.xml";
    public static String TKT91682_BLACK_LIST2_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/91682/blackList2.xml";
    public static String TKT91682_SCAN_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/91682/scan_file.txt";
    public static String TKT91682_SCAN_FORMAT_FILE_PATH = System.getProperty("user.dir") + "/src/test/resources/testDataFiles/91682/Format_MDI.xml";
    public static String TKT91682_SET_METAPHONE_BOOST_SQL_QUERY = "INSERT INTO tConfiguration(application, user_name, section_name, variable_name, variable_value, ZONE_ID, CAN_SEGREGATE_PER_ZONE)\n" +
            "SELECT 'SafeWatch Server', 'user_name','Scanning', 'METAPHONE_BOOST', 'true', NULL, 0\n" +
            "WHERE NOT EXISTS (\n" +
            "    SELECT 1 FROM tConfiguration WHERE variable_name = 'METAPHONE_BOOST'\n" +
            ");";

    public static String TKT91682_SET_METAPHONE_BOOST_ORCALE_QUERY = "MERGE INTO tConfiguration target\n" +
            "USING (SELECT 'METAPHONE_BOOST' AS variable_name FROM dual) source\n" +
            "ON (target.variable_name = source.variable_name)\n" +
            "WHEN NOT MATCHED THEN\n" +
            "INSERT  (ID,application, user_name, section_name, variable_name, variable_value, ZONE_ID, CAN_SEGREGATE_PER_ZONE)\n" +
            "    VALUES (300,'SafeWatch Server', 'user_name','Scanning', 'METAPHONE_BOOST', 'true', NULL, 0);";

    public static String TKT91682_INSERT_ARABIC_PREFIX_QUERY = "INSERT INTO tNeutralWords\n" +
            "            (neutral_word, category, custom, zone_id)\n" +
            "    VALUES(N'ABU', 8, 1, NULL);\n" +
            "    INSERT INTO tNeutralWords\n" +
            "            (neutral_word, category, custom, zone_id)\n" +
            "    VALUES(N'ABDEL', 8, 1, NULL);\n" +
            "    INSERT INTO .tNeutralWords\n" +
            "            (neutral_word, category, custom, zone_id)\n" +
            "    VALUES(N'ABO', 8, 1, NULL);\n" +
            "    INSERT INTO tNeutralWords\n" +
            "            (neutral_word, category, custom, zone_id)\n" +
            "    VALUES(N'EL', 8, 1, NULL);\n" +
            "    INSERT INTO tNeutralWords\n" +
            "            (neutral_word, category, custom, zone_id)\n" +
            "    VALUES(N'ABD', 8, 1, NULL);\n" +
            "    INSERT INTO tNeutralWords\n" +
            "            (neutral_word, category, custom, zone_id)\n" +
            "    VALUES(N'AL', 8, 1, NULL);\n" +
            "    INSERT INTO tNeutralWords\n" +
            "            (neutral_word, category, custom, zone_id)\n" +
            "    VALUES( N'ALDIN', 8, 1, NULL);";

    public static String TKT91682_INSERT_CUSTOM_PHONETICS_QUERY = "SET IDENTITY_INSERT tCustomPhonetics ON;" +
            "INSERT INTO tCustomPhonetics\n" +
            "(ID, SOUND1, SOUND2, SIMILARITY, ENABLED, zone_id)\n" +
            "VALUES(1, N'Jaafar', N'Gaffar', 100, 1, NULL);\n" +
            "INSERT INTO tCustomPhonetics\n" +
            "(ID, SOUND1, SOUND2, SIMILARITY, ENABLED, zone_id)\n" +
            "VALUES(2, N'p', N'b', 100, 1, NULL);\n" +
            "INSERT INTO tCustomPhonetics\n" +
            "(ID, SOUND1, SOUND2, SIMILARITY, ENABLED, zone_id)\n" +
            "VALUES(3, N'e', N'i', 100, 1, NULL);\n" +
            "INSERT INTO tCustomPhonetics\n" +
            "(ID, SOUND1, SOUND2, SIMILARITY, ENABLED, zone_id)\n" +
            "VALUES(4, N'Ragab', N'Rajab', 100, 1, NULL);\n" +
            "INSERT INTO tCustomPhonetics\n" +
            "(ID, SOUND1, SOUND2, SIMILARITY, ENABLED, zone_id)\n" +
            "VALUES(5, N'K', N'Q', 100, 1, NULL);\n" +
            "INSERT INTO tCustomPhonetics\n" +
            "(ID, SOUND1, SOUND2, SIMILARITY, ENABLED, zone_id)\n" +
            "VALUES(7, N'd', N'id', 100, 1, NULL);\n" +
            "INSERT INTO tCustomPhonetics\n" +
            "(ID, SOUND1, SOUND2, SIMILARITY, ENABLED, zone_id)\n" +
            "VALUES(8, N'e', N'ee', 100, 1, NULL);\n" +
            "INSERT INTO tCustomPhonetics\n" +
            "(ID, SOUND1, SOUND2, SIMILARITY, ENABLED, zone_id)\n" +
            "VALUES(9, N'AAL', N'Alal', 100, 1, NULL);\n" +
            "INSERT INTO tCustomPhonetics\n" +
            "(ID, SOUND1, SOUND2, SIMILARITY, ENABLED, zone_id)\n" +
            "VALUES(10, N'h', N'hh', 100, 1, NULL);\n" +
            "INSERT INTO tCustomPhonetics\n" +
            "(ID, SOUND1, SOUND2, SIMILARITY, ENABLED, zone_id)\n" +
            "VALUES(11, N'GELEL', N'galeel', 100, 1, NULL);\n" +
            "INSERT INTO tCustomPhonetics\n" +
            "(ID, SOUND1, SOUND2, SIMILARITY, ENABLED, zone_id)\n" +
            "VALUES(12, N'h', N'hh', 100, 1, NULL);\n" +
            "INSERT INTO tCustomPhonetics\n" +
            "(ID, SOUND1, SOUND2, SIMILARITY, ENABLED, zone_id)\n" +
            "VALUES(13, N'y', N'i', 100, 1, NULL);\n" +
            "INSERT INTO tCustomPhonetics\n" +
            "(ID, SOUND1, SOUND2, SIMILARITY, ENABLED, zone_id)\n" +
            "VALUES(14, N'a', N'e', 80, 1, NULL);\n" +
            "INSERT INTO tCustomPhonetics\n" +
            "(ID, SOUND1, SOUND2, SIMILARITY, ENABLED, zone_id)\n" +
            "VALUES(15, N'i', N'ee', 100, 1, NULL);\n" +
            "INSERT INTO tCustomPhonetics\n" +
            "(ID, SOUND1, SOUND2, SIMILARITY, ENABLED, zone_id)\n" +
            "VALUES(16, N'th', N's', 100, 1, NULL);\n" +
            "INSERT INTO tCustomPhonetics\n" +
            "(ID, SOUND1, SOUND2, SIMILARITY, ENABLED, zone_id)\n" +
            "VALUES(17, N'JR', N'JAR', 100, 1, NULL);\n" +
            "INSERT INTO tCustomPhonetics\n" +
            "(ID, SOUND1, SOUND2, SIMILARITY, ENABLED, zone_id)\n" +
            "VALUES(18, N'O', N'U', 80, 1, NULL);\n" +
            "INSERT INTO tCustomPhonetics\n" +
            "(ID, SOUND1, SOUND2, SIMILARITY, ENABLED, zone_id)\n" +
            "VALUES(19, N'm', N'mm', 100, 1, NULL);\n" +
            "INSERT INTO tCustomPhonetics\n" +
            "(ID, SOUND1, SOUND2, SIMILARITY, ENABLED, zone_id)\n" +
            "VALUES(20, N's', N'ss', 100, 1, NULL);" +
            "INSERT INTO tCustomPhonetics\n" +
            "(ID, SOUND1, SOUND2, SIMILARITY, ENABLED, zone_id)\n" +
            "VALUES(21, N'w', N'v', 100, 1, NULL);" +
            "INSERT INTO tCustomPhonetics\n" +
            "(ID, SOUND1, SOUND2, SIMILARITY, ENABLED, zone_id)\n" +
            "VALUES(21, N'i', N'y', 100, 1, NULL);" +
            "SET IDENTITY_INSERT tCustomPhonetics OFF;";

    public static String TKT91682_DELETE_CUSTOM_PHONETICS_QUERY = "DELETE FROM tCustomPhonetics where SOUND1 In" +
            " ('Jaafar','p','e','Ragab','K','d','e','AAL','h','GELEL','h','y','GELEL','h','y','a','i','th','JR','O','m','s','v','i')";

    public static String TKT91682_DELETE_ARABIC_PREFIX_QUERY = "DELETE FROM tNeutralWords where neutral_word In " +
            "('ABU','ABDEL','ABO','EL','ABD','AL','ALDIN')";


}
