package core.gui;

import core.util.Log;
import core.util.Wait;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.time.Duration;
import java.util.List;

public class Picker {
    private static final String FORMATTED_BUTTON_XPATH = "//div[@id=\"%s\"]/div[2]/div/button[@title=\"%s\"]";
    private static final String FORMATTED_PICKER_TABLE_XPATH = "//div[@id=\"%s\"]/div[%d]";
    private static final String FORMATTED_PICKER_TABLE_ITEM_XPATH = FORMATTED_PICKER_TABLE_XPATH + "/ul/li[@data-item-label=\"%s\"]";

    private final String divId;
    private final Controls controls;
    private final Wait wait;
    private final Log log;

    public Picker(String divId) {
        this.divId = divId;
        this.controls = new Controls();
        this.wait = new Wait();
        this.log = new Log();
    }

    public By getButtonBy(String divId, Button button) {
        return By.xpath(String.format(FORMATTED_BUTTON_XPATH, divId, button.getName()));
    }

    public By getAvailableItem(String divId, String itemName) {
        return By.xpath(String.format(FORMATTED_PICKER_TABLE_ITEM_XPATH, divId, 1, itemName));
    }

    public By getSelectedItem(String divId, String itemName) {
        return By.xpath(String.format(FORMATTED_PICKER_TABLE_ITEM_XPATH, divId, 3, itemName));
    }

    private void waitForPicker() throws InterruptedException {
        wait.time(Wait.ONE_SECOND / 2);
    }

    public void removeAllIfEnabled(RemoteWebDriver driver) throws InterruptedException {
        log.info("Remove all selected options if exist.");
        controls.clickButtonIfEnabled(driver, this.getButtonBy(divId, Picker.Button.REMOVE_ALL));
    }

    public boolean hasAvailableItem(RemoteWebDriver driver) {
        return controls.exists(driver, By.xpath(String.format(FORMATTED_PICKER_TABLE_XPATH + "/ul/li[1]", divId, 1)));
    }

    public void clickOnSelectedItem(RemoteWebDriver driver, String item) throws InterruptedException {
        log.info(String.format("Click on %s", item));
        controls.performClickByJS(driver, this.getSelectedItem(divId, item));
     /*   if (!controls.getWebElement(driver,this.getSelectedItem(divId, item)).getAttribute("class").contains("ui-state-highlight"))
            controls.performClickByJS(driver, this.getSelectedItem(divId, item));*/
        waitForPicker();
    }

    public void addAvailableItem(RemoteWebDriver driver, String item) throws InterruptedException {
        log.info(String.format("Click on the %s item", item));
        controls.performClick(driver, this.getAvailableItem(divId, item));
        log.info("Click on Add button");
        controls.performClick(driver, this.getButtonBy(divId, Picker.Button.ADD));
        waitForPicker();
    }

    public void addAvailableItem(RemoteWebDriver driver, List<String> itemList) throws InterruptedException {
        for (String item : itemList) {
            log.info(String.format("Add %s item to selected list.", item));
            addAvailableItem(driver, item);
        }
    }

    public void addAllAvailableItem(RemoteWebDriver driver) throws InterruptedException {
        controls.waitAndClick(driver, this.getButtonBy(divId, Picker.Button.ADD_ALL), Duration.ofMillis(750));
        waitForPicker();
    }

    public enum Button {
        ADD("Add"),
        ADD_ALL("Add All"),
        REMOVE("Remove"),
        REMOVE_ALL("Remove All");

        private final String name;

        Button(String name) {
            this.name = name;
        }

        public String getName() {
            return name;
        }
    }

}
