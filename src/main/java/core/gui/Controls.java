package core.gui;

import core.util.Wait;
import org.openqa.selenium.*;
import org.openqa.selenium.interactions.Actions;
import org.openqa.selenium.remote.RemoteWebDriver;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.FluentWait;
import org.openqa.selenium.support.ui.Select;
import org.openqa.selenium.support.ui.WebDriverWait;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;

public class Controls {


    public Controls() {
    }

    public WebElement getWebElement(RemoteWebDriver driver, By by) {
        Wait.waitUntilAjaxLoaderDisappear(driver);
        FluentWait<RemoteWebDriver> wait = new FluentWait<>(driver)
                .withTimeout(Duration.ofMinutes(2))
                .pollingEvery(Duration.ofMillis(10))
                .ignoring(NoSuchElementException.class, StaleElementReferenceException.class);

        return wait.until(ExpectedConditions.numberOfElementsToBeMoreThan(by,0)).get(0);
       }

    private Select getSelect(RemoteWebDriver driver, By by) {
        return new Select(driver.findElement(by));
    }

    public void setTextBoxValue(RemoteWebDriver driver, By by, String textToSet) {
        if (textToSet != null && !textToSet.isEmpty()) {
            clearTextBoxValue(driver, by);
            getWebElement(driver, by).sendKeys(textToSet);
        }
    }

    public void setTextBoxValueByJS(RemoteWebDriver driver, By by, String textToSet) {
        ((JavascriptExecutor) driver).executeScript("arguments[0].value = arguments[1]", getWebElement(driver, by), textToSet);
    }

    public void setAttributeValueByJS(RemoteWebDriver driver, String by, String attributeName, String value) {
        ((JavascriptExecutor) driver).executeScript("document.getElementById(arguments[0]).setAttribute(arguments[1], arguments[2])", by, attributeName, value);
    }


    public void clearTextBoxValue(RemoteWebDriver driver, By by) {
        getWebElement(driver, by).clear();
    }

    public void clickTextBox(RemoteWebDriver driver, By by) {
        getWebElement(driver, by).click();
    }

    public String getLabelValue(RemoteWebDriver driver, By by) {
        Wait.waitForJStoLoad(driver);
        return getWebElement(driver, by).getText();
    }

    public void setTextBoxValue(RemoteWebDriver driver, By by, long textToSet) {
        getWebElement(driver, by).sendKeys(Long.toString(textToSet));
    }

    public void setCheckboxValueById(RemoteWebDriver driver, By alternateBy, By by, boolean toCheck) {
        WebElement checkboxInput = getWebElement(driver, by);
        if (checkboxInput.isSelected() != toCheck) {
            WebElement checkbox = getWebElement(driver, alternateBy);
            try {
                checkbox.click();
            } catch (Exception e) {
                checkbox.click();
            }
        }
    }

    public boolean isChecked(RemoteWebDriver driver, String locator) {
        WebElement checkboxInput = getWebElement(driver, By.xpath(locator + "//input"));
        return checkboxInput.isSelected();
    }

    public void setCheckboxValueById(RemoteWebDriver driver, String parentId, boolean toCheck) {
        setCheckboxValueById(driver, By.id(parentId), By.id(parentId + "_input"), toCheck);
    }

    public void setCheckboxValueByXpath(RemoteWebDriver driver, String parentId, boolean toCheck) {
        setCheckboxValueById(driver, By.xpath(parentId), By.xpath(parentId + "//input"), toCheck);
    }

    public void setSelectByVisibleText(RemoteWebDriver driver, By by, String value) {
        getSelect(driver, by).selectByVisibleText(value);
    }

    public void setSelectByTagValue(RemoteWebDriver driver, By by, String value) {
        getSelect(driver, by).selectByValue(value);
    }

    public void waitAndClick(RemoteWebDriver driver, By by, Duration duration) {
        Wait.waitUntilElementToBeClickable(driver, by, duration);
        getWebElement(driver, by).click();
    }

    public void performClick(RemoteWebDriver driver, By by) {
        performClick(getWebElement(driver, by));
    }

    public void performMouseHover(RemoteWebDriver driver, By by) {
        Actions mouseHover = new Actions(driver);
        mouseHover.moveToElement(driver.findElement(by)).perform();
    }

    public void performDoubleClick(RemoteWebDriver driver, By by) {
        Actions action = new Actions(driver);
        action.doubleClick(getWebElement(driver, by)).build().perform();
    }

    public void waitAndAcceptAlert(RemoteWebDriver driver) {
        Wait.waitUntilAlertAppear(driver, Duration.ofSeconds(5)).accept();
        Wait.waitUntilAjaxLoaderDisappear(driver);
    }

    public void performJsClick(RemoteWebDriver driver, WebElement webElement) {
        ((JavascriptExecutor) driver).executeScript("arguments[0].click();", webElement);
    }

    public void performClickByJS(RemoteWebDriver driver, By by) {
        ((JavascriptExecutor) driver).executeScript("arguments[0].click();", getWebElement(driver, by));
    }

    public void scrollIntoViewJS(RemoteWebDriver driver, By by) {
        ((JavascriptExecutor) driver).executeScript("arguments[0].scrollIntoView();", getWebElement(driver, by));
    }

    public void scrollIntoViewJS(RemoteWebDriver driver, WebElement webElement) {
        ((JavascriptExecutor) driver).executeScript("arguments[0].scrollIntoView();", webElement);
    }


    public void scrollUp(RemoteWebDriver driver) {
        ((JavascriptExecutor) driver).executeScript("window.scrollBy(0,-250)");

    }

    public void scrollDown(RemoteWebDriver driver) {
        ((JavascriptExecutor) driver).executeScript("window.scrollBy(0,300)");
    }

    public void clickButtonIfEnabled(RemoteWebDriver driver, By by)  {
        WebElement element = getWebElement(driver, by);
        if (isButtonClickable(element))
            performClick(element);
    }

    public String getElementText(RemoteWebDriver driver, By by) {
        return getWebElement(driver, by).getText();
    }

    public void performClick(WebElement element) {
        element.click();
    }

    public String getTagValue(RemoteWebDriver driver, By by) {
        return getWebElement(driver, by).getAttribute("value");
    }

    public String getAttributeValue(RemoteWebDriver driver, By by, String attribute) {
        return getWebElement(driver, by).getAttribute(attribute);
    }

    public boolean exists(RemoteWebDriver driver, By by) {
        Wait.waitUntilAjaxLoaderDisappear(driver);
        return  !driver.findElements(by).isEmpty();
    }

    public void addElementFromCollection(RemoteWebDriver driver, By textBox, By addAllButton, List<String> elements) throws InterruptedException {
        for (String element : elements) {
            this.clearTextBoxValue(driver, textBox);
            this.setTextBoxValue(driver, textBox, element);
            Wait.time(Wait.ONE_SECOND / 2);
            this.performClick(driver, addAllButton);
            Wait.time(Wait.ONE_SECOND / 2);
        }
    }

    public boolean isCheckBoxChecked(RemoteWebDriver driver, By by) {
        WebElement checkboxInput = getWebElement(driver, by);
        return checkboxInput.getAttribute("checked") != null &&
                checkboxInput.getAttribute("checked").equalsIgnoreCase("checked");
    }

    public boolean isButtonClickable(WebElement element) {
        boolean disabled = element.getAttribute("disabled") != null ||
                element.getAttribute("disabled").equalsIgnoreCase("disabled");
        return !disabled;
    }

    public boolean isButtonChecked(WebElement element) {
        boolean checked = element.getAttribute("checked") != null ||
                element.getAttribute("checked").equalsIgnoreCase("checked");
        return !checked;
    }

    public void setFile(RemoteWebDriver driver, By by, String filePath) {
        getWebElement(driver, by).sendKeys(filePath);
    }

    private final By optionsLocator = By.xpath("//*[@role='option']");

    public boolean selectOptionByDisplayedText(RemoteWebDriver driver, By select, String displayedText) throws Exception {
        if (displayedText != null && !displayedText.isEmpty()) {
            if (select != null) {
                getWebElement(driver, select).click();
                Wait.time(Wait.ONE_SECOND);
                List<WebElement> options = driver.findElements(optionsLocator);
                if (!options.isEmpty()) {
                    if (!driver.findElements(By.xpath(String.format("//*[@role =  'option' and .='%s']", displayedText))).isEmpty()) {
                        this.scrollIntoViewJS(driver, By.xpath(String.format("//*[@role =  'option' and .='%s']", displayedText)));
                        driver.findElement(By.xpath(String.format("//*[@role =  'option' and .='%s']", displayedText))).click();
                        return true;
                    } else {
                        getWebElement(driver, select).click();
                        return false;
                    }
                } else {
                    throw new Exception("Drop down list is empty and has no listed options");
                }
            } else
                throw new Exception("Web element 'dropDown' is null .. it could not be located");
        }
        return false;
    }

    //get drop down options list string
    public List<String> getDropDownOptionsList(RemoteWebDriver driver, By select , By options) {
        getWebElement(driver, select).click();
        List<WebElement> options_list = driver.findElements(options);
        List<String> optionsList = new ArrayList<>();
        for (WebElement option : options_list) {
            optionsList.add(option.getText());
        }
        getWebElement(driver, select).click();
        return optionsList;
    }

    public boolean selectOptionByPartialDisplayedText(RemoteWebDriver driver, By select, String displayedText) throws Exception {
        if (displayedText != null && !displayedText.isEmpty()) {
            if (select != null) {
                getWebElement(driver, select).click();
                Wait.time(Wait.ONE_SECOND);
                List<WebElement> options = driver.findElements(optionsLocator);
                if (!options.isEmpty()) {
                    for (WebElement selectOption : options) {
                        if (selectOption.getText().trim().contains(displayedText.trim())) {
                            selectOption.click();
                            return true;
                        }
                    }
                } else {
                    return false;
                }
            } else
                throw new Exception("Web element 'dropDown' is null .. it could not be located");

        }
        return false;
    }

    // select option by index
    public void selectOptionByIndex(RemoteWebDriver driver, By select, String index) throws Exception {
        if (index != null && !index.isEmpty()) {
            if (select != null) {
                Wait.time(Wait.ONE_SECOND );
                getWebElement(driver, select).click();
                Wait.time(Wait.ONE_SECOND);
                List<WebElement> options = driver.findElements(optionsLocator);
                if (options.size() > Integer.parseInt(index)) {
                    options.get(Integer.parseInt(index)).click();
                } else {
                    throw new Exception("Selected index is out of bound");
                }
            } else
                throw new Exception("Web element 'dropDown' is null .. it could not be located");

        }
    }

    public void setOptionInDropDownTable(RemoteWebDriver driver, By select, String displayedText) throws Exception {
        if (displayedText != null && !displayedText.isEmpty()) {
            if (select != null) {
                getWebElement(driver, select).click();
                List<WebElement> options = driver.findElements(By.xpath("//td"));
                if (!options.isEmpty()) {
                    for (WebElement selectOption : options)
                        if (selectOption.getText().trim().equalsIgnoreCase(displayedText.trim())) {
                            selectOption.click();
                            break;
                        }
                } else {
                    throw new Exception("Drop down list is empty and has no listed options");
                }
            } else
                throw new Exception("Web element 'dropDown' is null .. it could not be located");

        }
    }

    public boolean checkIfElementExist(RemoteWebDriver driver, By by) throws Exception {
        List<WebElement> element = driver.findElements(by);
        return !element.isEmpty();
    }

    public void clickOnElementInTableUsingLabel(RemoteWebDriver driver, By by, String label) throws Exception {
        List<WebElement> element = driver.findElements(by);
        if (!element.isEmpty()) {
            for (WebElement webElement : element) {
                if (webElement.getText().equalsIgnoreCase(label)) {
                    webElement.click();
                    break;
                }
            }
        } else {
            throw new Exception("Table is Empty or can't be found");
        }
    }

    public void switchToIframeById(RemoteWebDriver driver, String frameId)  {
        driver.switchTo().frame(frameId);
    }

    public void jsRemoveStyleAttribute(RemoteWebDriver driver) {
        List<WebElement> allStyleElements = driver.findElements(By.xpath("//*[@style]"));
        for (WebElement input : allStyleElements) {
            ((JavascriptExecutor) driver).executeScript(
                    "arguments[0].removeAttribute('style','style')", input);
        }
    }

    public void highlightWebElement(By by, RemoteWebDriver driver) throws InterruptedException {
        //Here i pass values based on css style. Yellow background color with solid red color border.
        Wait.time(Wait.ONE_SECOND * 4);
        ((JavascriptExecutor) driver).executeScript("arguments[0].setAttribute('style', 'border: 2px solid red;');", getWebElement(driver, by));
    }

    public void highlightWebElement(WebElement webElement, RemoteWebDriver driver) throws InterruptedException {
        //Here i pass values based on css style. Yellow background color with solid red color border.
        ((JavascriptExecutor) driver).executeScript("arguments[0].setAttribute('style', 'border: 2px solid red;');", webElement);
    }

    public void clearHighlightWebElement(WebElement webElement, RemoteWebDriver driver) {
        ((JavascriptExecutor) driver).executeScript("arguments[0].setAttribute('style', '');", webElement, "");
    }

    public boolean checkIfButtonSelected(RemoteWebDriver driver, By by) {
        String originalByLocatorWithSubElement = by.toString() + "//div//span";
        String finalLocator = originalByLocatorWithSubElement.replace("By.xpath:", "");
        WebElement element = driver.findElement(By.xpath(finalLocator));
        return element.getAttribute("class").contains("check");
    }

    public void acceptAlert(RemoteWebDriver driver) {
        driver.switchTo().alert().accept();
    }

    public void clearBrowserCache(RemoteWebDriver driver) throws InterruptedException {
        driver.manage().deleteAllCookies(); //delete all cookies
    }

    public String getTableValue(RemoteWebDriver driver, By by, int row, int column) {
        WebElement table = getWebElement(driver, by);
        List<WebElement> rows = table.findElements(By.tagName("tr"));
        WebElement rowElement = rows.get(row);
        List<WebElement> columns = rowElement.findElements(By.tagName("td"));
        WebElement columnElement = columns.get(column);
        return columnElement.getText();
    }

    public boolean isButtonChecked(RemoteWebDriver driver, By by) {
        WebElement element = getWebElement(driver, by);
        return isButtonChecked(element);
    }

    public boolean isButtonClickable(RemoteWebDriver driver, By by) {
        WebElement element = getWebElement(driver, by);
        return isButtonClickable(element);
    }

    public void disable_healing(RemoteWebDriver driver)
    {
        //driver.executeScript("disable_healing_true");
    }

    public void enable_healing(RemoteWebDriver driver)
    {
        //driver.executeScript("disable_healing_false");
    }
}
