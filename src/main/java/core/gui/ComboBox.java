package core.gui;

import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.time.Duration;

public class ComboBox {
    private final By selectBoxLocator;
    private final String itemLocator;
    private final Controls controls = new Controls();

    public ComboBox(String selectBoxLocator) {
        this.selectBoxLocator = By.id(selectBoxLocator);
        this.itemLocator = String.format("//ul[@id=\"%s\"]", selectBoxLocator + "_items") + "/li[@data-label=\"%s\"]";
    }

    public void select(RemoteWebDriver driver, String itemName) {
        controls.performClickByJS(driver, selectBoxLocator);
        controls.waitAndClick(driver, By.xpath(String.format(itemLocator, itemName)), Duration.ofMillis(150));
    }
}
