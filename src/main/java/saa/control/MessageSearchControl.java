package saa.control;

import core.util.Wait;
import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;
import saa.gui.home.HomePage;
import saa.gui.home.MessageSearch;


public class MessageSearchControl {

    private final HomePage homePage;
    private final MessageSearch messageSearch;
    private final Wait wait;

    public MessageSearchControl() {
        this.homePage = new HomePage();
        this.messageSearch = new MessageSearch();
        this.wait = new Wait();
    }

    @Step("Navigate to 'create new fin message tab' tab.")
    public void navigateToCreateNewMessage(RemoteWebDriver driver) throws InterruptedException {
        homePage.moveToFinMessageCreation(driver);
    }

    @Step("Click on Search Button")
    public void clickOnSearch(RemoteWebDriver driver) throws InterruptedException {
        messageSearch.clickOnSearchBtn(driver);
        wait.time(Wait.ONE_SECOND * 3);
    }

    @Step("Switch to search table iframe")
    public void switchToSearchTableIframe(RemoteWebDriver driver) throws Exception {
        messageSearch.switchToTableIframe(driver);
        wait.time(Wait.ONE_SECOND * 3);
    }

    @Step("Click on first listed message")
    public void clickOnFirstListedMessage(RemoteWebDriver driver) throws Exception {
        messageSearch.clickOnFirstListedMessage(driver);
        wait.time(Wait.ONE_SECOND * 3);
    }

    @Step("Click on history tab button")
    public void clickOnHistoryTab(RemoteWebDriver driver) throws Exception {
        messageSearch.clickOnFirstListedMessage(driver);
        wait.time(Wait.ONE_SECOND * 3);
    }
}



