package saa.control;

import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;
import saa.gui.home.HomePage;


public class NewFinMessageControl {

    private final HomePage homePage;

    public NewFinMessageControl() {
        this.homePage = new HomePage();
    }

    @Step("Navigate to 'create new fin message tab' tab.")
    public void navigateToCreateNewFinMessage(RemoteWebDriver driver) throws InterruptedException {
        homePage.moveToFinMessageCreation(driver);
    }

    @Step("Navigate to 'Search message tab' tab.")
    public void navigateToMessageSearch(RemoteWebDriver driver) throws InterruptedException {
        homePage.moveToMessageSearch(driver);
    }

    @Step("Navigate to 'create new fin message tab' tab.")
    public void navigateToCreateNewIsoMessage(RemoteWebDriver driver) throws InterruptedException {
        homePage.moveToFinMessageCreation(driver);
    }
}



