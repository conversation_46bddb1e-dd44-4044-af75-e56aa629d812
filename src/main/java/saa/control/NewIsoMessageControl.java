package saa.control;

import io.qameta.allure.Step;
import org.openqa.selenium.remote.RemoteWebDriver;
import saa.gui.newMessage.NewIsoMessage;


public class NewIsoMessageControl {

    private final NewIsoMessage newIsoMessage;

    public NewIsoMessageControl() {
        this.newIsoMessage = new NewIsoMessage();
    }

    @Step("Navigate to 'create new Iso message tab")
    public void clickOnCbpr(RemoteWebDriver driver) throws Exception {
        newIsoMessage.clickOnFirstMessageTemplate(driver);
    }

    @Step("Insert Requester dn")
    public void insertRequester(RemoteWebDriver driver, String requester) throws Exception {
        newIsoMessage.insertRequester(driver, requester);
    }

    @Step("Insert Responder dn")
    public void insertResponder(RemoteWebDriver driver, String responder) throws Exception {
        newIsoMessage.insertResponder(driver, responder);
    }

    @Step("Insert Bic")
    public void insertBic(RemoteWebDriver driver, String bic) throws Exception {
        newIsoMessage.insertBic(driver, bic);
    }

    @Step("Inserting other mandatory data")
    public void fillOtherData(RemoteWebDriver driver) throws Exception {
        newIsoMessage.insertAllOtherMandatoryData(driver);
    }

    @Step("Click on Route")
    public void clickRoute(RemoteWebDriver driver) {
        newIsoMessage.clickOnRoute(driver);
    }
}



