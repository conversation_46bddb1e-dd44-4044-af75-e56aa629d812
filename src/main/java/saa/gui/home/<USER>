package saa.gui.home;

import core.constants.saa.GeneralConstants;
import core.gui.Controls;
import core.util.Property;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

import java.util.Properties;

public class HomePage {

    private static final By LABEL = By.xpath("//div[contains(@class,'FYC')]");

    private static final By MESSAGE_MANAGER = By.xpath("//h1[contains(text(),'Message Management')]");
    private static final By CREATE_FIN_MESSAGE_NEW = By.xpath("//a[contains(text(),'FIN Message: New')]");

    private static final By CREATE_ISO_MESSAGE_NEW = By.xpath("//a[contains(text(),'CBPR+')]");

    public static Properties saaGeneralConfigsProps = new Property().fromFile(core.constants.saa.GeneralConstants.GENERAL_CONFIG_FILE_NAME);

    private final Controls controls;

    public HomePage() {
        this.controls = new Controls();
    }

    @Step("Move to fin message creation")
    public String moveToFinMessageCreation(RemoteWebDriver driver) throws InterruptedException {
        controls.performMouseHover(driver, MESSAGE_MANAGER);
        controls.performClick(driver, CREATE_FIN_MESSAGE_NEW);
        // String label = controls.getLabelValue(driver,LABEL);
        return "";
    }

    @Step("Move to iso message creation")
    public String moveToIsoMessageCreation(RemoteWebDriver driver) throws InterruptedException {
        controls.performMouseHover(driver, MESSAGE_MANAGER);
        controls.performClick(driver, CREATE_ISO_MESSAGE_NEW);
        // String label = controls.getLabelValue(driver,LABEL);
        return "";
    }

    @Step("Move to message search")
    public void moveToMessageSearch(RemoteWebDriver driver) {
        driver.get(saaGeneralConfigsProps.getProperty(GeneralConstants.SAA_LOGIN_URL + GeneralConstants.SEARCH_MESSAGE_URL));
    }
}