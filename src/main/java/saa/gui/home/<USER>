package saa.gui.home;


import core.gui.Controls;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class MessageSearch {

    private static final By SEARCH_BTN_ID = By.id("(gwt-debug--messenger-messageSearch-criteria-actionSearch");
    private static final String SEARCH_TABLE_IFRAME_ID = "//iframe[@id='gwt-debug-Alliance_Access_Entry-frame']";

    private static final By MESSAGE_NAME_XPATH = By.xpath("/html[1]/body[1]/div[2]/div[2]/div[1]/div[2]/div[1]/div[2]/div[1]/div[2]/div[1]/div[2]/div[1]/div[6]/div[1]/div[3]/div[1]/div[3]/div[1]/div[3]/div[1]/div[3]/div[1]/div[2]/div[1]/div[1]/table[1]/tbody[1]/tr[1]/td[5]");
    private static final By HISTORY_TAB_XPATH = By.xpath("//div[contains(text(),'History')]");

    private final Controls controls;

    public MessageSearch() {
        this.controls = new Controls();
    }

    @Step("Click on search button")
    public void clickOnSearchBtn(RemoteWebDriver driver) {
        controls.performJsClick(driver, driver.findElement(SEARCH_BTN_ID));
    }

    @Step("Switch to search table iframe")
    public void switchToTableIframe(RemoteWebDriver driver) throws Exception {
        controls.switchToIframeById(driver, SEARCH_TABLE_IFRAME_ID);
    }

    @Step("Click on first listed message")
    public void clickOnFirstListedMessage(RemoteWebDriver driver) throws Exception {
        controls.performJsClick(driver, driver.findElement(MESSAGE_NAME_XPATH));
    }

    @Step("Click on history tab button")
    public void clickOnHistoryTab(RemoteWebDriver driver) throws Exception {
        controls.performJsClick(driver, driver.findElement(HISTORY_TAB_XPATH));
    }
}
