package saa.gui.newMessage;

import core.gui.Controls;
import core.util.Wait;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.WebElement;
import org.openqa.selenium.remote.RemoteWebDriver;

public class NewFinMessage {

    private static final String FRAME_ID = "messenger";

    private static final By MESSAGE_LABEL = By.xpath("(//div[contains(text(),'MT103')])[1]");

    private By by;
    private final Controls controls;
    private final Wait wait;

    public NewFinMessage() {
        this.controls = new Controls();
        this.wait = new Wait();
    }

    @Step("Move to fin message creation using message label")
    public void moveToFinMessageCreationUsingMessageLabel(RemoteWebDriver driver, String messageLabel) throws Exception {
        //controls.switchToIframeById(driver,FRAME_ID);
        //controls.jsRemoveStyleAttribute(driver);
        controls.clickOnElementInTableUsingLabel(driver, MESSAGE_LABEL, messageLabel);
    }

    @Step("Click on message using message label")
    public void clickOnMessageUsingMessageLabel(RemoteWebDriver driver, String messageLabel) throws Exception {
        controls.jsRemoveStyleAttribute(driver);
        controls.performClick(driver, MESSAGE_LABEL);
    }

    @Step("Click on message")
    public void clickOnMessage(RemoteWebDriver driver) throws Exception {
        wait.time(3500);

        // 1. Click 'FIN Message: New'
        by = By.id("gwt-debug-desktop-applications-com.swift.Access.samCreationFinMsg");
        WebElement NewFinMessage_WebElement = driver.findElement(by);
        driver.findElement(by).click();
        wait.time(3500);
    }

    @Step("Create new MT103")
    public void createNewMT103(RemoteWebDriver driver) throws Exception {

        //1. Click On 'MT 103'
        by = By.xpath("//*[@id=\"gwt-debug--messenger-finCreation-search-actionTable-Table\"]/div[3]/div/div[2]/div/div/table/tbody/tr[4]/td[1]/div");
        driver.findElement(by).click();
        wait.time(1500);


    }

}
