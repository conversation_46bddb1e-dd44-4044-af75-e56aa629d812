package saa.gui.newMessage;

import core.gui.Controls;
import core.util.Randomizer;
import io.qameta.allure.Step;
import org.openqa.selenium.By;
import org.openqa.selenium.remote.RemoteWebDriver;

public class NewIsoMessage {

    private static final By MESSAGE_LABEL = By.xpath("(//div[contains(text(),'MX Message Templates')])[1]");
    private static final By REQUESTER_TXT = By.xpath("//div[contains(text(),'Requester+')]");
    private static final By RESPONDER_TXT = By.xpath("//div[contains(text(),'Responder')]");
    private static final By BICFI_TXT = By.xpath("(//div[@span='bicFI']");
    private static final By NAME_TXT = By.xpath("(//div[@span='bicFI']");
    private static final By COUNTRY_TXT = By.xpath("(//div[contains(text(),'CBPR+')])[1]");
    private static final By CURRENCY_TXT = By.xpath("(//div[contains(text(),'CBPR+')])[1]");
    private static final By UETR_TXT = By.xpath("(//div[contains(text(),'CBPR+')])[1]");
    private static final By ROUTE_BTN = By.xpath("(//div[contains(text(),'Route+')])[1]");

    private final Controls controls;
    private final Randomizer randomizer;

    public NewIsoMessage() {
        this.controls = new Controls();
        this.randomizer = new Randomizer();
    }

    @Step("Click on first message template")
    public void clickOnFirstMessageTemplate(RemoteWebDriver driver) throws Exception {
        controls.performClick(driver, MESSAGE_LABEL);
    }

    @Step("Click on route")
    public void clickOnRoute(RemoteWebDriver driver) {
        controls.performClick(driver, ROUTE_BTN);
    }

    @Step("Insert requester")
    public void insertRequester(RemoteWebDriver driver, String requester) throws Exception {
        controls.setTextBoxValue(driver, REQUESTER_TXT, requester);
    }

    @Step("Insert responder")
    public void insertResponder(RemoteWebDriver driver, String responder) throws Exception {
        controls.setTextBoxValue(driver, RESPONDER_TXT, responder);
    }

    @Step("Insert bic")
    public void insertBic(RemoteWebDriver driver, String bic) throws Exception {
        controls.setTextBoxValue(driver, BICFI_TXT, bic);
    }

    @Step("Insert all other mandatory data")
    public void insertAllOtherMandatoryData(RemoteWebDriver driver) {
        String random = String.valueOf(randomizer.getInt());
        controls.setTextBoxValue(driver, NAME_TXT, random);
        controls.setTextBoxValue(driver, COUNTRY_TXT, random);
        controls.setTextBoxValue(driver, CURRENCY_TXT, random);
        controls.setTextBoxValue(driver, UETR_TXT, random);
    }

}
