package saa.entity;

import io.qameta.allure.internal.shadowed.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class FinMessage extends Main {

    public String getMessageType() {
        return messageType;
    }

    public void setMessageType(String messageType) {
        this.messageType = messageType;
    }

    public String getReference() {
        return reference;
    }

    public void setReference(String reference) {
        this.reference = reference;
    }

    public String getEntityName() {
        return entityName;
    }

    public void setEntityName(String entityName) {
        this.entityName = entityName;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getDetectionStatus() {
        return detectionStatus;
    }

    public void setDetectionStatus(String detectionStatus) {
        this.detectionStatus = detectionStatus;
    }

    private String detectionStatus;
    private String messageType;
    private String reference;
    private String entityName;
    private String location;
    private String requester;

    public String getRequester() {
        return requester;
    }

    public void setRequester(String requester) {
        this.requester = requester;
    }

    public String getResponder() {
        return responder;
    }

    public void setResponder(String responder) {
        this.responder = responder;
    }

    public String getBic() {
        return bic;
    }

    public void setBic(String bic) {
        this.bic = bic;
    }

    private String responder;
    private String bic;



  /*  @Override
    public String toString() {
        return "EnList{" +
                "listSet=" + listSet +
                ", zoneName='" + zoneName + '\'' +
                ", privateFlag='" + privateFlag + '\'' +
                ", listName='" + listName + '\'' +
                ", officialId='" + officialId + '\'' +
                ", entry=" + entry +
                '}';
    }*/


}
