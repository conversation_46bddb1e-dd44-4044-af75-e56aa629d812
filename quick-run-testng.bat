@echo off
REM Quick TestNG Runner with Failed Test Rerun
REM Usage: quick-run-testng.bat [testng-file.xml]

if "%1"=="" (
    echo Usage: %0 [testng-file.xml]
    echo Example: %0 regressionWithoutRestartServices.xml
    exit /b 1
)

set TESTNG_FILE=%1

echo Running TestNG Suite: %TESTNG_FILE%
echo.

REM Check if file exists
if not exist "%TESTNG_FILE%" (
    echo ERROR: File '%TESTNG_FILE%' not found!
    exit /b 1
)

echo [1/3] Running complete test suite...
call mvn clean test "-DsuiteXmlFile=%TESTNG_FILE%"

echo.
echo [2/3] Checking for failed tests...
if exist "target\surefire-reports\testng-failed.xml" (
    echo Found failed tests! Rerunning them...
    echo.
    echo [3/3] Rerunning failed tests...
    call mvn test "-DsuiteXmlFile=target/surefire-reports/testng-failed.xml"

    echo.
    if exist "target\surefire-reports\testng-failed.xml" (
        echo Some tests still failed. Check: target\surefire-reports\testng-failed.xml
    ) else (
        echo All previously failed tests now pass!
    )
) else (
    echo All tests passed on first run!
)

echo.
echo Execution completed for %TESTNG_FILE%
