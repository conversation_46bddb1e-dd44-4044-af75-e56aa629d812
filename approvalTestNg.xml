<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd" >

<suite name="Safe Watch Filtering" verbose="5" preserve-order="true">
    <parameter name="browserType" value="Chrome"/>
    <parameter name="Application" value="SWF"/>

    <test name="SWF Regression Test - Approval Suite">
        <parameter name="loginType" value="filtering"/>

        <classes>
            <class name="eastnets.screening.ApprovalTests.AddEntity_TCs.TC91914"/>
            <class name="eastnets.screening.ApprovalTests.AddEntity_TCs.TC91915"/>
            <class name="eastnets.screening.ApprovalTests.AddEntity_TCs.TC91922"/>
            <class name="eastnets.screening.ApprovalTests.AddEntity_TCs.TC91926"/>

            <class name="eastnets.screening.ApprovalTests.DeleteEntity_TCs.TC91809"/>
            <class name="eastnets.screening.ApprovalTests.DeleteEntity_TCs.TC91804"/>
            <class name="eastnets.screening.ApprovalTests.DeleteEntity_TCs.TC91805"/>

            <class name="eastnets.screening.ApprovalTests.DisableEntity_TCs.TC91851"/>
            <class name="eastnets.screening.ApprovalTests.DisableEntity_TCs.TC91845"/>
            <class name="eastnets.screening.ApprovalTests.DisableEntity_TCs.TC91844"/>
            <class name="eastnets.screening.ApprovalTests.DisableEntity_TCs.TC91864"/>
            <class name="eastnets.screening.ApprovalTests.DisableEntity_TCs.TC91870"/>

            <class name="eastnets.screening.ApprovalTests.EnableEntity_TCs.TC91820"/>
            <class name="eastnets.screening.ApprovalTests.EnableEntity_TCs.TC91821"/>
            <class name="eastnets.screening.ApprovalTests.EnableEntity_TCs.TC91824"/>
            <class name="eastnets.screening.ApprovalTests.EnableEntity_TCs.TC91831"/>

            <class name="eastnets.screening.ApprovalTests.General_TCs.TC91971"/>
            <class name="eastnets.screening.ApprovalTests.General_TCs.TC92095"/>
            <class name="eastnets.screening.ApprovalTests.General_TCs.TC92663"/>
            <class name="eastnets.screening.ApprovalTests.General_TCs.TC92710"/>
            <class name="eastnets.screening.ApprovalTests.General_TCs.TC94927"/>
            <class name="eastnets.screening.ApprovalTests.General_TCs.TC94933"/>
            <class name="eastnets.screening.ApprovalTests.General_TCs.TC94934"/>
            <class name="eastnets.screening.ApprovalTests.General_TCs.TC94936"/>

            <class name="eastnets.screening.ApprovalTests.Notifications_TCs.TC92081"/>
            <class name="eastnets.screening.ApprovalTests.Notifications_TCs.TC92082"/>

            <class name="eastnets.screening.ApprovalTests.UpdateEntity_TCs.TC91883"/>
            <class name="eastnets.screening.ApprovalTests.UpdateEntity_TCs.TC91884"/>
            <class name="eastnets.screening.ApprovalTests.UpdateEntity_TCs.TC91888"/>
            <class name="eastnets.screening.ApprovalTests.UpdateEntity_TCs.TC91891"/>

            <class name="eastnets.screening.ApprovalTests.ValidationMessages_TCs.TC92107"/>
            <class name="eastnets.screening.ApprovalTests.ValidationMessages_TCs.TC92111"/>
            <class name="eastnets.screening.ApprovalTests.ValidationMessages_TCs.TC92114"/>
            <class name="eastnets.screening.ApprovalTests.ValidationMessages_TCs.TC92661"/>
            <class name="eastnets.screening.ApprovalTests.ValidationMessages_TCs.TC92666"/>
            <class name="eastnets.screening.ApprovalTests.ValidationMessages_TCs.TC92667"/>
            <class name="eastnets.screening.ApprovalTests.ValidationMessages_TCs.TC92668"/>
            <class name="eastnets.screening.ApprovalTests.ValidationMessages_TCs.TC92669"/>
        </classes>
    </test>
</suite>