@echo off
echo ========================================
echo SELENIUM GRID DEBUG MONITORING
echo ========================================
echo Starting monitoring at %date% %time%
echo.

:LOOP
echo ========================================
echo Timestamp: %date% %time%
echo ========================================

echo.
echo === DOCKER CONTAINER STATUS ===
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"

echo.
echo === DOCKER CONTAINER RESOURCE USAGE ===
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}\t{{.NetIO}}\t{{.BlockIO}}"

echo.
echo === SELENIUM HUB STATUS ===
curl -s http://localhost:4444/status | findstr "ready\|value\|message" 2>nul
if errorlevel 1 (
    echo Hub status check failed - Hub may be down
)

echo.
echo === SELENIUM GRID SESSIONS ===
curl -s http://localhost:4444/grid/api/sessions | findstr "session\|capabilities\|inactivityTime" 2>nul
if errorlevel 1 (
    echo Session check failed
)

echo.
echo === SYSTEM MEMORY USAGE ===
wmic OS get TotalVisibleMemorySize,FreePhysicalMemory /format:list | findstr "="

echo.
echo === NETWORK CONNECTIONS ===
netstat -an | findstr ":4444\|:5555\|:7900" | findstr "ESTABLISHED\|LISTEN"

echo.
echo === DOCKER LOGS (Last 5 lines) ===
echo --- HUB LOGS ---
docker logs selenium-hub --tail 5 2>nul
echo --- NODE LOGS ---
docker logs selenium-node-edge --tail 5 2>nul

echo.
echo Waiting 10 seconds before next check...
timeout /t 10 /nobreak >nul
goto LOOP
