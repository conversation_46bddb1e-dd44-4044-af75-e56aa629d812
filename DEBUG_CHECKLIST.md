# Selenium 3-Thread Parallel Execution Debug Checklist

## 1. Pre-Test Setup Monitoring

### A. Start Resource Monitoring
```bash
# Run this in a separate terminal before starting tests
debug_monitor.bat
```

### B. Check Docker Container Limits
```bash
# Check container resource limits
docker inspect selenium-hub | findstr -i "memory\|cpu"
docker inspect selenium-node-edge | findstr -i "memory\|cpu"

# Check if containers have resource limits set
docker stats --no-stream
```

### C. Verify Selenium Grid Configuration
```bash
# Check hub configuration
curl http://localhost:4444/status

# Check available sessions
curl http://localhost:4444/grid/api/sessions
```

## 2. During Test Execution

### A. Monitor Key Metrics
- **Container CPU Usage**: Should not exceed 80%
- **Container Memory Usage**: Watch for memory spikes
- **Network Connections**: Count of ESTABLISHED connections to port 4444
- **Session Count**: Number of active browser sessions

### B. Watch for Warning Signs
- Container restarts during execution
- Memory usage climbing continuously
- Network connection timeouts
- Browser process crashes in container logs

## 3. Log Analysis Points

### A. Application Logs (Check BaseTest output)
Look for these patterns:
```
Thread X: Driver created in Xms
Thread X: Session ID: xxxxx
Thread X: Total initialization time: Xms
```

### B. Docker Container Logs
```bash
# Check for memory/resource errors
docker logs selenium-hub | findstr -i "error\|memory\|timeout"
docker logs selenium-node-edge | findstr -i "error\|memory\|timeout\|crash"
```

### C. System Event Logs
```bash
# Check Windows Event Viewer for system-level issues
eventvwr.msc
# Look in: Windows Logs > System for critical errors during test time
```

## 4. Specific Debug Tests

### A. Test Resource Scaling
1. Run with 1 thread - Record baseline metrics
2. Run with 2 threads - Compare resource usage
3. Run with 3 threads - Identify where it breaks

### B. Test Session Limits
```bash
# Check max sessions per node
curl http://localhost:4444/grid/api/hub | findstr -i "maxSession"
```

### C. Test Network Capacity
```bash
# Monitor network connections during execution
netstat -an | findstr ":4444" | findstr "ESTABLISHED" | find /c "ESTABLISHED"
```

## 5. Common Root Causes & Solutions

### A. Memory Issues
**Symptoms**: Container restarts, "browser may have died" errors
**Check**: 
- Container memory limits
- Host system available memory
- Browser process memory consumption

**Solution**: Increase container memory limits or reduce parallel threads

### B. Network Port Exhaustion
**Symptoms**: Connection timeouts, session creation failures
**Check**: 
- Number of ESTABLISHED connections
- Available ephemeral ports
- Network timeouts in logs

**Solution**: Increase network timeouts or reduce concurrent sessions

### C. Selenium Grid Capacity
**Symptoms**: Sessions queued, "no available nodes" errors
**Check**: 
- Grid node configuration
- Maximum sessions per node
- Node availability

**Solution**: Increase maxSessions or add more nodes

### D. Browser Process Limits
**Symptoms**: Browser crashes, memory errors in container logs
**Check**: 
- Browser process count in containers
- Browser memory usage
- Container resource limits

**Solution**: Add browser stability arguments or increase resources

## 6. Debug Commands Reference

```bash
# Real-time container monitoring
docker stats

# Check container health
docker ps -a

# View container logs
docker logs selenium-hub --follow
docker logs selenium-node-edge --follow

# Check grid status
curl http://localhost:4444/status | jq .

# List active sessions
curl http://localhost:4444/grid/api/sessions | jq .

# Check system resources
wmic OS get TotalVisibleMemorySize,FreePhysicalMemory
wmic process where name="java.exe" get ProcessId,PageFileUsage,WorkingSetSize

# Network monitoring
netstat -an | findstr ":4444"
```

## 7. Expected Behavior Analysis

### 2 Threads (Working)
- Container CPU: ~40-60%
- Memory usage: Stable, no spikes
- Network connections: 2-4 ESTABLISHED to port 4444
- Session creation time: <5 seconds

### 3 Threads (Failing)
- Container CPU: >80% or spikes
- Memory usage: Climbing or hitting limits
- Network connections: >6 or connection failures
- Session creation time: >10 seconds or timeouts

## 8. Action Items Based on Findings

1. **If Memory Issue**: Increase Docker container memory limits
2. **If CPU Issue**: Reduce parallel threads or increase CPU allocation
3. **If Network Issue**: Increase timeouts or check port limits
4. **If Grid Issue**: Increase maxSessions or add nodes
5. **If Browser Issue**: Add stability arguments or reduce resource usage
