@echo off
setlocal enabledelayedexpansion

echo ========================================
echo SELENIUM 3-THREAD DEBUG EXECUTION
echo ========================================
echo.

:: Check prerequisites
echo Checking prerequisites...

:: Check if <PERSON><PERSON> is running
docker ps >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Docker is not running or not accessible
    echo Please start Docker and ensure Selenium Grid is running
    pause
    exit /b 1
)

:: Check if Selenium Grid is accessible
curl -s http://localhost:4444/status >nul 2>&1
if %ERRORLEVEL% NEQ 0 (
    echo ERROR: Selenium Grid is not accessible at localhost:4444
    echo Please ensure Selenium Grid containers are running
    pause
    exit /b 1
)

echo Prerequisites check passed!
echo.

:: Create debug directory
if not exist "debug_logs" mkdir debug_logs

:: Ask user for monitoring preference
echo Choose monitoring option:
echo 1. Run tests with real-time dashboard (recommended)
echo 2. Run tests with background logging only
echo 3. Run tests with no additional monitoring
echo.
set /p MONITOR_CHOICE="Enter choice (1-3): "

if "%MONITOR_CHOICE%"=="1" (
    echo.
    echo Starting real-time dashboard in separate window...
    start "Selenium Dashboard" cmd /k realtime_dashboard.bat
    timeout /t 3 /nobreak >nul
)

if "%MONITOR_CHOICE%"=="1" (
    echo.
    echo Starting comprehensive tracking...
    start "Test Tracker" cmd /k track_3thread_execution.bat
) else if "%MONITOR_CHOICE%"=="2" (
    echo.
    echo Starting background logging...
    start /B track_3thread_execution.bat
) else (
    echo.
    echo Running tests without additional monitoring...
    echo.
    echo ========================================
    echo STARTING TEST EXECUTION
    echo ========================================
    echo.
    
    :: Record start time
    echo Test execution started at: %date% %time%
    
    :: Run the tests
    mvn test -Dsurefire.suiteXmlFiles=regressionWithoutRestartServices.xml
    
    :: Record end time
    echo Test execution completed at: %date% %time%
)

if "%MONITOR_CHOICE%"=="1" (
    echo.
    echo ========================================
    echo MONITORING SETUP COMPLETE
    echo ========================================
    echo.
    echo Real-time dashboard is running in separate window
    echo Test execution tracking is active
    echo.
    echo You can now observe:
    echo - Container resource usage
    echo - Active browser sessions
    echo - Network connections
    echo - Memory usage
    echo - Container logs
    echo - System alerts
    echo.
    echo The tests will start automatically...
    echo Press any key when ready to begin test execution
    pause >nul
)

echo.
echo ========================================
echo FINAL SYSTEM CHECK
echo ========================================

:: Final pre-test check
echo Current Docker containers:
docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
echo.

echo Current Grid status:
curl -s http://localhost:4444/status | findstr "ready\|value"
echo.

echo Current active sessions:
curl -s http://localhost:4444/grid/api/sessions | findstr "sessionId" >nul
if %ERRORLEVEL%==0 (
    echo WARNING: There are already active sessions. Consider restarting Grid.
) else (
    echo Good: No active sessions detected
)
echo.

echo System memory:
wmic OS get FreePhysicalMemory,TotalVisibleMemorySize /format:list | findstr "="
echo.

echo ========================================
echo READY TO START 3-THREAD EXECUTION
echo ========================================
echo.
echo This will run the full regression suite with 3 parallel threads
echo Monitor the dashboard/logs for real-time analysis
echo.
echo Press any key to start the test execution...
pause >nul

echo.
echo Starting test execution NOW...
echo.

goto :EOF
