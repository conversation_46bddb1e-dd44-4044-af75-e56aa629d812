# To execute this docker compose yml file use `docker compose -f docker-compose-v3-video.yml up`
# Add the `-d` flag at the end for detached execution
# To stop the execution, hit Ctrl+C, and then `docker compose -f docker-compose-v3-video.yml down`
version: "3"
name: sara
services:
  chrome:
    image: selenium/node-chrome:4.20.0-20240505
    volumes:
      - ./screenshots:/usr/src/myapp/screenshots
    shm_size: 2gb
    depends_on:
      - selenium-hub
    environment:
      - SE_NODE_SESSION_TIMEOUT= 700
      - SE_EVENT_BUS_HOST=selenium-hub
      - SE_EVENT_BUS_PUBLISH_PORT=4442
      - SE_EVENT_BUS_SUBSCRIBE_PORT=4443
      - SE_OPTS=--enable-managed-downloads true

  edge:
    image: selenium/node-edge:4.20.0-20240505
    volumes:
      - ./screenshots:/usr/src/myapp/screenshots
    shm_size: 2gb
    depends_on:
      - selenium-hub
    environment:
      - SE_NODE_SESSION_TIMEOUT=700
      - SE_EVENT_BUS_HOST=selenium-hub
      - SE_EVENT_BUS_PUBLISH_PORT=4442
      - SE_EVENT_BUS_SUBSCRIBE_PORT=4443
      - SE_OPTS=--enable-managed-downloads true
      - SE_NODE_MAX_SESSIONS=3
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'

  firefox:
    image: selenium/node-firefox:4.20.0-20240505
    volumes:
      - ./screenshots:/usr/src/myapp/screenshots
    shm_size: 2gb
    depends_on:
      - selenium-hub
    environment:
      - SE_NODE_SESSION_TIMEOUT= 700
      - SE_EVENT_BUS_HOST=selenium-hub
      - SE_EVENT_BUS_PUBLISH_PORT=4442
      - SE_EVENT_BUS_SUBSCRIBE_PORT=4443
      - SE_OPTS=--enable-managed-downloads true


  selenium-hub:
    image: selenium/hub:4.20.0-20240505
    container_name: selenium-hub
    ports:
      - "4442:4442"
      - "4443:4443"
      - "4444:4444"
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'
        reservations:
          memory: 512M
          cpus: '0.25'
