# @AfterSuite Failed Test Rerun - Implementation Guide

## Overview
This implementation provides an `@AfterSuite` method that automatically reruns failed test cases after the whole test suite finishes. It uses TestNG's programmatic API to create a new test suite containing only the failed tests.

## Implementation Details

### Added to BaseTest.java
```java
// Track failed tests for rerun
private static List<String> failedTestMethods = new ArrayList<>();
private static boolean hasRerunExecuted = false;

@AfterMethod(alwaysRun = true)
public void trackFailedTests(ITestResult result) {
    // Collect failed test methods during execution
    if (result.getStatus() == ITestResult.FAILURE && !hasRerunExecuted) {
        String testMethod = result.getTestClass().getName() + "." + result.getMethod().getMethodName();
        if (!failedTestMethods.contains(testMethod)) {
            failedTestMethods.add(testMethod);
            new Log().info("Failed test tracked: " + testMethod);
        }
    }
}

@AfterSuite(alwaysRun = true)
public void rerunFailedTestCases() {
    // Prevent multiple executions
    if (hasRerunExecuted) {
        return;
    }
    
    if (failedTestMethods.isEmpty()) {
        new Log().info("*** ALL TESTS PASSED! ***");
        return;
    }
    
    hasRerunExecuted = true;
    
    // Create new TestNG suite for failed tests
    TestNG testNG = new TestNG();
    XmlSuite suite = new XmlSuite();
    suite.setName("Failed Tests Rerun Suite");
    
    // Add failed test classes and methods
    // ... programmatic suite creation logic
    
    // Run the failed tests
    testNG.run();
}
```

## Key Features

### ✅ Automatic Collection
- **@AfterMethod tracking**: Automatically collects failed test information during execution
- **Duplicate prevention**: Avoids collecting the same failed test multiple times
- **Execution control**: Uses `hasRerunExecuted` flag to prevent collection during rerun

### ✅ Programmatic TestNG Suite Creation
- **Dynamic suite**: Creates new TestNG XML suite programmatically
- **Selective inclusion**: Only includes failed test methods
- **Class grouping**: Groups failed methods by their test classes
- **Clean execution**: Runs failed tests in isolation

### ✅ One-Time Execution
- **Single rerun**: `hasRerunExecuted` flag ensures rerun happens only once
- **Loop prevention**: Cannot cause infinite loops
- **Clean state**: Clears failed test list after execution

## Usage from IntelliJ

### Run Any Test Suite
1. **Right-click** on any TestNG XML file (e.g., `regressionWithoutRestartServices.xml`)
2. **Select** "Run 'regressionWithoutRestartServices.xml'"
3. **Watch automatic execution**:
   - Tests run normally
   - Failed tests are tracked automatically
   - After suite completion, failed tests rerun automatically **once**

## Console Output Examples

### When Tests Fail and Get Rerun
```
[Normal test execution...]
Failed test tracked: eastnets.screening.regression.iso20022.ISO20022_TC002.iso20022_TC0012
Failed test tracked: eastnets.screening.regression.listmanager.ListManager_TC001.listManager_TC001

========================================
RERUNNING FAILED TEST CASES
========================================
Found 2 failed test(s) to rerun:
  - eastnets.screening.regression.iso20022.ISO20022_TC002.iso20022_TC0012
  - eastnets.screening.regression.listmanager.ListManager_TC001.listManager_TC001

Creating new TestNG suite for failed tests...
Starting failed test rerun...

[TestNG execution for failed tests...]

========================================
FAILED TEST RERUN COMPLETED
========================================
```

### When All Tests Pass
```
========================================
*** ALL TESTS PASSED! ***
No failed tests to rerun.
========================================
```

## How It Works

### Phase 1: Normal Test Execution
```
Test Suite Starts
├── Test1 → Pass ✓
├── Test2 → Fail ✗ (tracked in failedTestMethods)
├── Test3 → Pass ✓
├── Test4 → Fail ✗ (tracked in failedTestMethods)
└── Test5 → Pass ✓
```

### Phase 2: @AfterSuite Triggered
```
@AfterSuite Method Executes
├── hasRerunExecuted = false → Proceed
├── failedTestMethods.size() = 2 → Create rerun suite
├── hasRerunExecuted = true (set immediately)
└── Create new TestNG suite with only failed tests
```

### Phase 3: Automatic Rerun
```
New TestNG Suite Execution
├── Only Test2 and Test4 are included
├── Tests run in fresh environment
├── Results reported
└── failedTestMethods list cleared
```

## Benefits

### ✅ Fully Automated
- **No manual intervention**: Everything happens automatically
- **Complete workflow**: From detection to rerun completion
- **IntelliJ integration**: Works seamlessly with IDE test runner

### ✅ Reliable Execution
- **Programmatic approach**: Uses TestNG's official API
- **Clean separation**: Original run vs. rerun are distinct
- **Proper setup**: Each rerun gets proper test initialization
- **Exception handling**: Graceful error handling

### ✅ Efficient Resource Usage
- **Selective rerun**: Only failed tests are executed
- **Fresh environment**: Each test gets proper setup/teardown
- **No interference**: Rerun doesn't affect original results

## Troubleshooting

### No Rerun Happening
**Issue**: Tests fail but no rerun occurs
**Solutions**:
1. Check that test classes extend `BaseTest`
2. Verify `@AfterSuite` method is not overridden
3. Look for "Failed test tracked" messages in console

### Compilation Errors
**Issue**: Import conflicts with Optional class
**Solution**: Use specific imports instead of `import java.util.*`
```java
import java.util.HashMap;
import java.util.Map;
import java.util.List;
// etc.
```

### TestNG Suite Creation Fails
**Issue**: Error during programmatic suite creation
**Solutions**:
1. Ensure TestNG version is 7.0+
2. Check that test classes are properly compiled
3. Verify test methods have `@Test` annotations

## Advanced Configuration

### Customize Suite Name
```java
suite.setName("My Custom Failed Tests Rerun - " + new Date());
```

### Add Suite Parameters
```java
suite.setParameters(Map.of("browserType", "Chrome", "environment", "test"));
```

### Enable Parallel Execution for Rerun
```java
suite.setParallel(XmlSuite.ParallelMode.METHODS);
suite.setThreadCount(3);
```

## Best Practices

### For Development
- Use this for rapid development and testing
- Monitor console output for rerun results
- Combine with debugging for failed test analysis

### For CI/CD
- Works well in automated environments
- Provides clear logging for build reports
- Can be combined with external reporting tools

**This implementation provides fully automated failed test rerun functionality directly from IntelliJ using TestNG's programmatic API!**
