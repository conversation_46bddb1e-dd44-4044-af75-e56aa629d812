[Sun 08/03/2025 17:38:11.16] TEST EXECUTION STARTED 
[INFO] Scanning for projects...
[WARNING] 
[WARNING] Some problems were encountered while building the effective model for com.eastnets.automation:filtering:jar:0.0.1-SNAPSHOT
[WARNING] 'dependencies.dependency.(groupId:artifactId:type:classifier)' must be unique: org.testng:testng:jar -> duplicate declaration of version 7.7.0 @ line 140, column 21
[WARNING] 'dependencies.dependency.(groupId:artifactId:type:classifier)' must be unique: org.testng:testng:jar -> duplicate declaration of version 7.7.0 @ line 156, column 15
[WARNING] 'dependencies.dependency.(groupId:artifactId:type:classifier)' must be unique: org.testng:testng:jar -> duplicate declaration of version 7.7.0 @ line 162, column 15
[WARNING] 
[WARNING] It is highly recommended to fix these problems because they threaten the stability of your build.
[WARNING] 
[WARNING] For this reason, future Maven versions might no longer support building such malformed projects.
[WARNING] 
[INFO] 
[INFO] -----------------< com.eastnets.automation:filtering >------------------
[INFO] Building Filtering - Automation 0.0.1-SNAPSHOT
[INFO]   from pom.xml
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- clean:3.2.0:clean (default-clean) @ filtering ---
[INFO] Deleting C:\Users\<USER>\IdeaProjects\SWS_selenium\target
[INFO] 
[INFO] --- resources:3.3.1:resources (default-resources) @ filtering ---
[WARNING] Using platform encoding (UTF-8 actually) to copy filtered resources, i.e. build is platform dependent!
[INFO] Copying 1 resource from src\main\resources to target\classes
[INFO] 
[INFO] --- compiler:3.8.0:compile (default-compile) @ filtering ---
[INFO] Changes detected - recompiling the module!
[WARNING] File encoding has not been set, using platform encoding UTF-8, i.e. build is platform dependent!
[INFO] Compiling 225 source files to C:\Users\<USER>\IdeaProjects\SWS_selenium\target\classes
[INFO] /C:/Users/<USER>/IdeaProjects/SWS_selenium/src/main/java/core/util/TextFilesHandler.java: C:\Users\<USER>\IdeaProjects\SWS_selenium\src\main\java\core\util\TextFilesHandler.java uses or overrides a deprecated API.
[INFO] /C:/Users/<USER>/IdeaProjects/SWS_selenium/src/main/java/core/util/TextFilesHandler.java: Recompile with -Xlint:deprecation for details.
[INFO] /C:/Users/<USER>/IdeaProjects/SWS_selenium/src/main/java/core/util/TextFilesHandler.java: C:\Users\<USER>\IdeaProjects\SWS_selenium\src\main\java\core\util\TextFilesHandler.java uses unchecked or unsafe operations.
[INFO] /C:/Users/<USER>/IdeaProjects/SWS_selenium/src/main/java/core/util/TextFilesHandler.java: Recompile with -Xlint:unchecked for details.
[INFO] 
[INFO] --- resources:3.3.1:testResources (default-testResources) @ filtering ---
[WARNING] Using platform encoding (UTF-8 actually) to copy filtered resources, i.e. build is platform dependent!
[INFO] Copying 336 resources from src\test\resources to target\test-classes
[INFO] 
[INFO] --- compiler:3.8.0:testCompile (default-testCompile) @ filtering ---
[INFO] Changes detected - recompiling the module!
[WARNING] File encoding has not been set, using platform encoding UTF-8, i.e. build is platform dependent!
[INFO] Compiling 275 source files to C:\Users\<USER>\IdeaProjects\SWS_selenium\target\test-classes
[INFO] 
[INFO] --- surefire:3.0.0:test (default-test) @ filtering ---
[INFO] Using auto detected provider org.apache.maven.surefire.testng.TestNGProvider
[INFO] 
[INFO] -------------------------------------------------------
[INFO]  T E S T S
[INFO] -------------------------------------------------------
[INFO] Running TestSuite
SLF4J(W): Class path contains multiple SLF4J providers.
SLF4J(W): Found provider [ch.qos.logback.classic.spi.LogbackServiceProvider@6321e813]
SLF4J(W): Found provider [org.slf4j.simple.SimpleServiceProvider@79be0360]
SLF4J(W): See https://www.slf4j.org/codes.html#multiple_bindings for an explanation.
SLF4J(I): Actual provider is of type [ch.qos.logback.classic.spi.LogbackServiceProvider@6321e813]
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.545 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.548 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid cb0b1cf9-bcfb-47f9-a757-198af0bcf0c0 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.655 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.655 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 0610409b-23f3-407b-b9cf-fe5315226476 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.656 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.656 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid ec1eca88-10cb-463a-9091-d844d27de239 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.657 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.657 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid b29d9336-b7a3-4590-af1d-8842160a884e not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.784 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.785 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 0358bee4-c0f4-4245-b103-598db69050fa not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.787 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.787 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid c935f4aa-8787-4b3d-ad07-f7db2fd06a7f not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.788 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.788 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid e350fc1d-959e-48e8-9b41-77001bd08b7f not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.788 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.789 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid de3b864e-3087-4529-9e81-9404a691e1ad not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.802 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.802 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 9b055c13-00a8-4164-8acb-07a0e6db512d not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.804 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.804 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 9d13740e-e27e-4e35-a05e-04e354b62749 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.804 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.804 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid adae6d6e-ddf9-491c-aec9-8558624f1278 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.805 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.805 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 5f25b93d-eaa1-4f0b-8ae8-b03c99e63838 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.820 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.820 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 7d4f86ff-9ed7-4c2b-a72e-9b5476b61d94 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.821 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.821 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid dc2dd788-43c3-46da-b6e3-890b9e749b9c not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.822 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.822 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid ab563270-ed76-4c27-b7fc-6c1e48af8657 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.823 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.823 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid b7326925-f2cd-44ac-911d-78f273adabd9 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.834 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.835 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 40f3de00-52f6-480a-90b3-84d16944a43f not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.837 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.837 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid a624f439-73b3-4daf-b3ef-156909ff75f7 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.838 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.839 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid b150c9d6-0779-41aa-bed7-298c7f898d24 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.839 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.839 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 52a62283-0f08-42a8-82c5-9b9258ab5a6c not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.852 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.852 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 2e35901a-58fe-431c-a6d2-e941b32618da not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.853 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.853 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid c1b25dd8-393c-4e5f-aaee-58562028e8b9 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.854 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.854 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 653fd62e-1189-4f25-8a09-34fc65d202c5 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.855 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.855 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 7bfc2848-000f-4400-9a6b-1d075cab69b0 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.872 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.872 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid *************-4bf5-8b3e-7df2d66a64b8 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.877 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.877 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 76332b1c-c92c-4ea4-af7a-f7491cd1dd56 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.878 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.878 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid c44e052c-68cb-4e61-bfcd-bda782e8beec not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.881 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.881 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid *************-46cd-8913-25f0b46d22b0 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.882 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.882 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 4c693437-3db9-44c0-a7a5-de6aac52ddc0 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.899 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.899 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 2ab02743-5514-4d5e-aa0b-d89dd5c5e0e8 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.902 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.902 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 682f2460-b4e9-44c1-822e-dbd2e240a195 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.902 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.902 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid c6ac55e0-9174-4452-82ae-68fc8995af9f not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.904 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.905 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 00d34f24-eb02-4423-87a6-cd7100fe5a2e not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.905 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.905 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid acbb4b85-dfde-4878-ac98-15547fc814a3 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.916 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.916 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 83dae03f-590b-4e91-9bbf-fe56aea328c2 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.917 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.917 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 2518b547-5863-4019-9b1f-ef31b4992e74 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.918 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.918 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid b5fab914-f382-463f-8dda-84269efa5544 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.918 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.918 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 25bc69b5-f697-4276-90cd-27c4864565e2 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.930 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.930 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid fa3c13fa-385c-4a86-9859-024c10af2036 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.931 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.932 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid db1e983a-8bbd-4de7-b260-a7fb5b1b5feb not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.932 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.932 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid bba4fe1b-dc06-491f-88b5-e337d9929fba not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.932 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.933 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 25629db7-ea06-434b-90c5-91df8b3407b6 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.934 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.934 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 19706f1f-b24f-422b-9b4b-9010b5722dac not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.934 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.934 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 6ff10361-5111-4375-a27a-44f65ef2149a not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.935 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.935 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid c13a95c1-d0a0-4f92-a0e0-dfdd546fa227 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.946 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.946 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 353c67bb-5fbd-435b-85ec-e24c36321cad not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.947 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.948 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 77d76ce7-bb6a-40f5-ac5f-f371d69af365 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.948 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.948 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid c6fb98be-38a7-4551-bf24-e5bb78852b8e not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.949 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.949 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 270ba8aa-d468-491d-89a8-17a68aec399d not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.950 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.950 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 4a33ba84-37f0-4540-9255-90b0e45c5eff not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.951 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.951 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 4f9364c8-d9ab-4b09-a504-7b7d75ea258e not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.952 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.952 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid b4784b48-3b16-42e3-956e-9208f6c0a1c5 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.961 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.961 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 56beba79-e741-453d-a9a2-8e8f2f3afb5e not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.965 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.965 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 9f54071d-3260-45a3-aced-447ff96bc699 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.967 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.967 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 7989d79f-c332-45fd-98a5-a4e09c02d321 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.967 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.967 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid d9baefd7-412b-4128-8990-a8c1dad825be not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.968 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.968 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 0a58946f-0944-4072-a15e-192f9b4325c8 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.969 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.969 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 5c5f2c98-1a6a-4f39-b9bc-35a9c6aac75f not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.979 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.979 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid f06f862c-a96e-4b46-942a-a92bccb8a807 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.981 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.981 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 63a151e8-7d8c-4729-b7d9-4af418e61fcc not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.981 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.982 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid b8bc3e6d-b4f2-4240-a146-7d5aa5ebad03 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.982 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.982 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 873d7087-b169-4351-b2fe-903a57607c10 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.993 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.994 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 86fcc874-43b9-43b0-a593-f69ce6fbb18b not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.994 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.994 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid bb81d9c4-fcee-49d3-9ee4-d61b0240b4c9 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.995 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.996 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 37e234d5-e44f-47ad-bcd8-a77db8c60484 not found
[INFO ] 20250803-173847 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:47.996 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:47.996 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid fb9bfdee-53a6-45d4-83c2-23bb87c15b80 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.018 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.025 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 70ee7090-98fd-4aeb-bfe8-f99fd3454e0c not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.029 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.029 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid b84d0074-cac0-461d-b60a-7626bc5662f4 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.030 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.030 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid b67ea758-ed14-4ebd-92d9-934a0dad245a not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.030 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.030 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid e9c3dfeb-c059-404e-bd19-175f4a47ac5e not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.031 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.031 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 859f7097-dccf-41a3-9cd6-77fbe9ffb24e not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.032 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.032 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 58ddd842-96f7-44ef-b5ae-c00e87260ef1 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.045 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.045 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 72bf81fd-71a6-488d-a4ae-8a23bef1760c not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.047 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.047 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 916bcec1-49be-497e-8ced-63323a42e626 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.047 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.047 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 38b57b54-8c5d-4238-97c1-ee2963c6005f not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.048 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.048 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid a3c3b816-31ce-4c8c-998e-7927548accfe not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.048 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.048 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 54b57d12-819c-4cf5-83c9-7ddbf5376bc2 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.056 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.057 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid c73eebd2-fe3a-4310-bfea-d5ab6b0e8f37 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.057 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.057 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 45a04707-a5b5-4907-aea2-49578b530c41 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.058 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.058 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 001839e2-23d7-41e2-834c-52a5a81998b2 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.058 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.058 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 1f274f53-bc12-4d19-a1e1-4b4adf29d18b not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.058 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.058 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid bd4ec6bd-6ec9-4190-96ae-1723b1031aa0 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.076 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.077 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid a36ad32c-a93c-46c4-ac62-a4cd3d31453d not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.078 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.079 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 826db83d-73db-4104-ad19-877a9292999d not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.079 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.079 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid a8a62739-e537-405e-a71f-eb73899da65a not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.079 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.079 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 6d2c6353-0bb7-4cf3-beaa-1d3bfd949de0 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.080 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.081 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid cd255971-9f41-4d37-b50a-ef4a62ceb39e not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.081 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.082 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 851427f3-7cc4-401a-8a19-f5be4fec7174 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.104 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.104 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 98fc3b44-25c5-4aee-8cb2-176aafa07b88 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.105 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.105 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 6288c9c6-90bb-4a13-a116-d409230b7de4 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.105 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.106 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 555e8866-36b5-4bdd-b076-c4922cb078ef not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.106 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.109 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 6044e30a-9ca1-48cc-a479-85eb55d66ed9 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.109 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.110 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 07700473-70ca-40dc-b47d-c727ca8b69cc not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.110 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.110 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 47b81e83-6baa-4410-b3ef-9e0633ee9d1a not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.118 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.119 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 49385043-ee77-4819-8a88-f305384dfc91 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.120 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.120 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid ca2569fe-8f62-421e-bc3d-ea69cec2beb6 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.120 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.120 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid e08481ae-e66e-43b8-b62a-bc881ff2eb46 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.120 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.120 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid ba9a8e26-5455-4fde-8e2d-37032f4346c2 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.121 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.121 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 6c0e4659-de33-47a8-97f7-27dfbe6e7f3a not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.121 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.121 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid f5515d84-9bb6-4590-8a86-cb46fe22fab4 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.122 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.122 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 3ee9cd2d-49fb-4711-b6ae-394f8d0fa685 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.131 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.131 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 4f9f5f1a-c6e4-46e5-81e6-64207d9fdf91 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.132 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.132 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 61b38fac-200b-475c-9d39-bebe61425fdd not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.132 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.132 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid a2fd84c1-5818-4d98-8557-a27489d38bd6 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.132 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.132 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid ba65b99a-1f71-448f-8421-e3fbeb22fab5 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.133 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.133 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 35010a10-9a48-4a58-b852-a50d81971618 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.133 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.133 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid bb05517c-f646-4777-a417-297f7e9bca75 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.147 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.147 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 2c3b98d3-9616-4229-b30b-cc446e88f8cd not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.147 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.148 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid df79fe8f-2b43-43bf-bf3e-6cb4b23c0bb7 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.148 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.148 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 1381a860-093f-45b5-aca0-572ad469d56a not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.148 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.148 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid e144bc25-d800-45a6-822c-0b838731caab not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.150 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.150 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid ac7e2d22-ed30-4965-8bf0-2870c4cb6d94 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.152 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.152 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 434583cb-032f-491f-8eeb-101cfae7545d not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.159 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.159 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid b02fac00-0246-459d-a603-a54a7792621e not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.160 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.160 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 6c13447e-0c7d-47a4-8757-59866899bc8d not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.160 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.160 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 687c691f-dcd3-470e-af3d-1637373ee8f7 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.160 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.161 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid e484cbf3-ed05-45f9-8d48-7a5b83e25036 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.167 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.167 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 9f445f1b-dd65-4e31-b1ca-37fcca29d0c3 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.168 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.168 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid db4f64d7-7f3b-46ef-b5f1-f80c29df6902 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.169 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.169 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid c400b5ad-23c5-47dd-8872-74062713395f not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.176 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.176 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid ed8ac9a0-d189-42c3-9712-570977073b98 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.177 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.177 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 821f21e4-54a5-419b-8aed-0e9801fca3b6 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.178 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.178 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 122522fd-36fe-49f2-babd-f21fcb4fac3e not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.185 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.185 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid a77ccdaf-f2c5-4084-8a34-494c433a56c1 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.186 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.186 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid faa5287b-9b2b-4bf7-bb01-9640f48f0c72 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.186 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.186 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 94a38c96-14d0-4b6f-9fa2-1989e5afc3da not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.273 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.274 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid bb227a39-5767-43c1-827b-6240ea453c52 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.274 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.274 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid eb0b1026-0d03-4567-80de-833af3b5033a not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.276 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.276 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid bf673eee-31e3-45ce-a3ae-b566a58d7d36 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.276 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.276 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 7c44267a-a08d-4e9d-b49d-826f7d440a12 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.289 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.289 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 6e878218-a155-49ee-8480-2f0be90bf6fe not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.290 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.290 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 4eab55df-8097-4c44-80c9-cb54c40041f6 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.291 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.291 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 5eb51b2f-e443-4821-b21b-639bbcf1744c not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.291 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.291 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 6abafffd-5811-4ea8-8f32-f7b939c4d0da not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.297 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.297 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid baf8bd01-e2f6-4f10-9e1e-015a34516c05 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.298 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.298 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid db9cdd80-43b1-44f4-b7e6-b815b846450f not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.298 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.298 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid d0e79e3b-1749-43bb-a4ee-a9723d8da320 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.299 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.299 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid a13b1391-fc03-48df-b2f5-8a8651e18438 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.303 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.303 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 8e7c3921-495f-4475-affb-9ac711812e18 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.304 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.304 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid c76ef72e-63e9-4ac9-935e-e40d1b41094c not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.304 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.304 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 11ff0c94-0214-4d9d-98f9-5b64cc591a7b not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.305 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.305 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 49c3bb64-0f41-4e70-9543-30e753776f7e not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.318 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.319 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 9b774def-49ec-4d57-b326-9535f7ddfdf0 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.320 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.321 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 6a646163-4755-4d15-851d-9bc710663964 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.321 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.321 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 2b2fd761-0edf-48da-ab10-f157b61621f5 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.321 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.321 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 071b94f6-3918-4e6e-884b-4af5191f82e5 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.339 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.339 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 3b71024f-e4c0-4183-95f2-931aa4ff0eed not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.340 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.340 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid d984a3eb-7bc1-4771-adad-52588c956953 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.341 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.341 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 0f8428ad-0d95-4ec8-ab33-0bdfa2c5307f not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.341 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.341 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 99eb85fa-aa51-4146-8062-48d5f382dc55 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.349 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.349 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid a282eb4f-8a20-4fd0-ab76-5d6f6da6a085 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.350 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.350 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid d8dfed48-4fa4-48ee-bffe-e08147d56bc6 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.350 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.350 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 90c3180a-4b74-467c-9c3e-6d40ee404bf5 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.350 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.350 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid d895bd99-92d8-4a96-abc9-d0a5bbadf77c not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.355 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.355 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 941fccc6-19d0-43f2-acf3-9760c5c8c195 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.356 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.356 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 6889d9fe-7b7a-4c6d-a33e-149f8446fe14 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.357 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.357 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 57aa3de0-5b11-4286-9f48-b73ad48a1b83 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.357 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.357 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 25a4aa75-044d-4f0b-9f99-1d585f8d9709 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.366 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.366 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid d82325f0-f40f-4a3e-ad3b-923190b85930 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.367 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.367 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid a1f44412-b672-473c-8401-4e7f72f6ee9b not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.367 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.367 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 6f2f28f0-4375-468d-94d4-edcb8109a439 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.367 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.367 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 254a3ed7-7158-45da-b01d-3b7185c01daa not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.375 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.375 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 6d5654fd-98e9-45bf-959b-cfc414372e93 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.376 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.376 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 5828c908-5cb7-4786-bb89-fcf4f6c45ba7 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.376 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.376 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 6c3cd574-560d-408d-aecd-b564fb579f2d not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.377 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.377 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid bf63880f-752c-417f-8b9b-d87b77515344 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.382 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.382 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 40e7d3b0-9c40-42f2-9a93-ba4475380386 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.383 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.383 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 2f457844-033c-4d86-a8eb-65a5a28f9669 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.383 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.383 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 55b95189-d109-4c5d-9b34-7261dda5c132 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.384 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.384 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 33374b9b-d47b-4101-92d7-6b22ec7eeb6d not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.389 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.390 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid *************-4beb-b3c5-ebec195d08f5 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.390 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.390 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid d329a400-2a83-4e64-a97a-6ca26b798387 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.390 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.390 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 98c66cba-71ba-48a5-bac7-2479c2404bf2 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.391 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.391 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 555142be-15b9-4024-8005-b2e95b3e3b4b not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.396 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.396 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 7882c7a0-509f-4b7b-9713-c2844425f07b not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.396 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.396 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 9e30f3d4-f90d-45db-9d75-5cb7b6399413 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.397 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.397 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid b62c900f-3776-4439-954b-0c96e04a5664 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.398 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.399 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 00d8c494-baeb-42ec-b9f7-32c199aed779 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.404 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.404 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 8edac794-f741-4965-9b82-86dca16b5775 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.404 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.404 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid fdec0a7e-5200-405e-8576-d0527495b063 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.405 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.405 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid db26d36a-4654-49c6-a8e4-9fb6187dfc5a not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.405 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.405 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid ff40c52c-b8b4-476e-8aab-0975a8c51615 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.431 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.431 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid cf3c61f2-266b-4e3d-8aa8-61e849a93fb2 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.435 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.435 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid ac5fe564-9be5-42b6-933e-f916c62e34fc not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.436 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.436 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 2f03b3ac-b627-420c-bc3a-22fb452bd76d not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.436 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.436 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid ffaa5b9f-96d1-4bd4-b42a-5ff96666accc not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.442 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.442 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 7df73154-fef9-41e7-97a8-b8aa793455dd not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.443 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.443 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 2b79463c-272e-4a10-8eb2-05d156389ee3 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.443 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.443 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 7b9485a4-895e-4088-b46e-d3b5453d38ab not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.448 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.448 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid b73cb161-a72a-4aec-9a59-0eaafefabecb not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.448 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.448 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid f078c0ec-ee0d-4d97-a86f-1732a1f98446 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.449 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.449 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 68ff0b8f-ae7a-40a7-b50a-f5bfcb86ab3a not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.454 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.454 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 6c321b6e-8b11-49f9-bf89-215dad9ceecd not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.455 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.455 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid a1a22727-6e7a-478d-a2b0-892f31a04c75 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.456 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.456 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 3fd2c3e9-f435-4d1d-8cbc-b63a7bba811b not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.474 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.474 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 4b87e2e2-9b9b-42f0-9972-92cfd33f18cf not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.474 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.474 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 24b776a2-5bdc-4d27-93fc-bf71a7376880 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.479 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.479 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid d78f8cb5-640f-48a6-bfdb-67a0f7be549e not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.480 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.480 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid cf452038-bc7a-4f3c-9e32-8153eacfc61f not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.480 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.480 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 7b23c05c-3618-4191-80d4-3466918e72c7 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.481 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.481 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 508196f7-e6d4-42ce-93f2-c4628555ce83 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.516 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.516 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 14db4410-f2a3-4851-927d-60bffc27fd26 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.517 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.517 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 04c0c1a3-b5f7-470d-93f1-2cc422484bf2 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.517 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.517 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 44346266-eb3b-4586-9a40-67c5289b40b2 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.517 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.517 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 98b272e8-e83b-46c0-9c29-87e321b2e911 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.527 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.527 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 8869463d-ab2d-4957-b550-d0b387c7405d not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.528 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.528 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid be851453-6b43-444f-a861-15be45c0e0bf not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.528 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.528 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid d7042ed6-3406-47ec-9e2b-e144dbf6f2cd not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.529 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.529 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 8a5e5bef-eaed-4b01-bc26-a55d6bd22d6e not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.535 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.535 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 5ac0cc33-d663-44be-805e-d52a93db42e9 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.536 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.536 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 53fa3a2c-6e4f-46b0-a4cb-b778328bfe35 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.537 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.537 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 2a6722f5-f900-4a22-bff8-75f1f4610d20 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.539 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.540 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 67d774d7-a0d9-48e9-bce0-a94aa2b7f440 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.544 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.544 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid e5d8273e-044c-4cfe-89eb-c668796f00b0 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.545 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.545 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid a7821e2d-54e1-414d-91d6-d4a1981c2d50 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.545 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.545 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid c6b90e76-b8f3-49e2-89eb-52559c4f8002 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.545 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.545 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 5d761033-fbeb-4225-8115-3e7cfb3656bf not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.550 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.550 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 7f925b11-99af-4b69-ba9a-4f7f5b54259e not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.550 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.550 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid b954f20c-0928-43b4-97fc-dce1303785ad not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.550 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.550 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 579bcbe7-c206-4c34-bedd-c54dafc1057b not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.551 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.551 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid ee81ddab-0d6a-4330-a658-0a6335c9a67b not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.556 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.556 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 1a8a81d4-64a5-4ac3-bcd4-d970e53a21ca not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.556 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.556 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid e07b5385-5b81-4d43-addd-564f8707dc2a not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.556 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.556 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 1c73f884-79b5-4dbf-ab46-f65bc9f95ed3 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.556 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.557 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 3343eb27-b90f-431c-9132-9c9f09cc52a1 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.561 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.561 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 5f8353dc-9db2-4bd9-8d78-075a5ab3d417 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.561 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.561 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid ce183001-4047-47d8-b43c-d5f1aa85e0e9 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.561 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.562 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 18dbfdc7-149c-4764-ab06-479bb2ba44de not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.562 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.562 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 84975770-476a-44bd-ac95-3238e5aba60f not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.566 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.566 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 2f184f40-93bc-453d-a441-3c65a573f64a not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.566 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.566 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 25c93881-cbeb-4e4d-b0c6-abb5e9cfb518 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.567 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.567 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid b4d9f8d7-cd1f-4e3e-b82a-542ab969cc9d not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.571 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.571 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 12d6057e-51ed-4188-90d6-df886fdbee09 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.571 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.571 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 099e6786-8792-49eb-909b-3c34ed55aa2b not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.571 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.571 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 64481cdb-a69f-49ec-a5bc-c3ec16a11b49 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.572 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.572 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid cdea5222-42ed-425b-84c9-7080b00bebc6 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.578 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.578 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid b2d3141a-d6e5-4a9c-9178-9a19660e3a96 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.579 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.579 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 0c20c483-b2c3-47dd-88af-17d74f6a49c5 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.579 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.579 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid aea94cc2-94fa-47ea-a44f-83b1a31de77c not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.582 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.582 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 118bf181-2cb1-421f-bd94-b2b360f1866e not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.583 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.583 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 3abf33fd-1929-49df-8006-1b95d1b14655 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.583 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.583 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid cfe427ef-1dbd-4b67-8ec4-675a8e88ed34 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.586 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.586 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 05ae65dd-0fa4-4810-871b-3accbc61a307 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.587 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.587 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid ece9343d-f25c-4ff2-b641-4debb4a59ad7 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.587 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.587 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 3c604ee9-4da3-4d85-96e5-************ not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.590 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.590 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid e3ba5508-88eb-438e-b3b1-a04ed37504f9 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.591 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.591 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 7ac1d353-86b1-454e-af30-cdae9ce2dc4d not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.591 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.591 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 2cc259ea-20ec-4588-b9ef-d4e7d4bb5bf8 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.595 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.595 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid d8855208-c9de-4688-a886-46fa9e0bcecd not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.596 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.596 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 43bc4e78-f9ab-4ce6-a814-e2b21151c761 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.596 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.596 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 4b494392-3e09-42b4-a3a9-5ad3b010107c not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.599 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.599 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid dd3412bb-2ddf-4b9c-a2cd-5d5682423bc3 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.600 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.600 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 7efb4f57-dfff-4492-ba2d-d81a0d78f70f not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.601 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.601 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid b2c67c9f-a8de-4e06-bcad-feaf7ce7390b not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.604 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.604 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid eada7cc0-0ea5-4b41-968d-1073bea8ed32 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.605 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.605 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 193779c4-2d72-49c6-8854-9e7cdbc437f9 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.605 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.605 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 5d55d618-b934-433f-93cf-84f2617dee5e not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.619 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.619 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 76183243-e1a2-49dc-89ee-f71ef1794aef not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.619 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.619 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 8067241f-9d01-4512-9be7-4a30bb036763 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.620 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.620 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 34f97bcd-a9ee-45af-8193-3bfbdd0e5baf not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.623 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.623 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid a7c4394e-46f1-41a7-8033-2adabc25f1e2 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.623 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.624 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid d300c033-311f-4b84-a3c3-743e7d4ccaaf not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.624 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.624 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 4666a4ea-3fc0-42c8-83af-6d09c7a81406 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.628 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.628 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 200f43c1-3a59-44ba-b965-8059bf9248f3 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.628 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.628 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 2769994e-1e15-4c92-8f03-755d4a708685 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.629 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.629 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 633a116f-fcd9-4eb0-a2cd-74509f7ba1a4 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.688 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.688 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 0d5583ab-94d9-437a-818c-55da260e654a not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.688 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.688 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid fd95caa2-edaf-40f5-8213-004e25085f9b not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.689 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.689 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 511e451f-f350-4c86-bc19-ffa11b69df9f not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.694 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.694 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid c9833272-3539-405c-aded-c4ac899d9c5c not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.695 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.695 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 67781018-01a6-4598-93d5-7b22d5abc711 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.695 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.695 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid e5fa3ed7-77b1-46e9-ac01-408b573b4a9a not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.695 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.695 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 34e117c9-dc96-4520-be10-03c40b683520 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.701 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.701 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 83c888d9-1157-4c9b-b5f2-df2dea2dae93 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.702 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.702 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 89f547ed-cb01-43a3-bb37-2c0920040d81 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.702 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.702 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 253dae47-2730-4a79-8f91-83711377ba8e not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.702 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.702 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 495f797e-8b63-4163-b554-91f414030ee5 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.707 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.707 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid af673c9d-acbf-46cf-8286-d7fed548dd23 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.708 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.708 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid cb3391bf-ce01-4f48-a4a8-91ca79eb34e4 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.708 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.708 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid c7d4621d-09c5-4dfb-8a7e-f1cc938c054a not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.708 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.708 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 0cca4a15-7ae5-4a79-b153-1705029e900e not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.720 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.720 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid b0184aff-9cf9-4d22-a3b2-45c869831898 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.721 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.721 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 26548106-f67f-48c0-92d1-1783cadf28d8 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.721 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.721 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid a5f91d14-a53f-47ea-bbb7-cd09eb57bbb1 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.721 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.721 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid b8a1dd3d-93ee-45db-8631-0652571b9ea0 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.726 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.726 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 479443d1-57ba-4649-aa9a-b603c40f4048 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.727 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.727 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 9b907488-e80b-468a-8bee-3324cc0e31c0 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.727 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.727 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 68291e08-048d-44ac-b08a-30b66ffd5b50 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.727 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.727 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 264ed745-091d-4ee1-b5b2-0477e916347a not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.732 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.732 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 78d531c9-cd1b-4624-b11e-840c1911b6af not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.733 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.733 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid e97583f3-19cc-4064-8659-3c8cb1c4fcd8 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.733 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.733 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 45721235-fe03-4673-805a-aaa00381d9d7 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.733 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.733 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 5f1c4c21-f343-43b9-bed2-fe74940f8494 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.740 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.740 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 7b558612-ebb1-4d59-a4d1-0def78213e09 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.740 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.741 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 2fc5a836-a0d0-4965-96f7-6500ce14c817 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.741 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.741 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 1360c96d-9a5f-4f7e-95ee-b3efe4f2109d not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.741 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.741 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid c95f73a0-7acf-4765-b7d1-aa69d672cca3 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.747 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.747 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 1c150d53-041c-4a4a-bca6-54be1dc4a345 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.747 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.747 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 036e8e89-f9d3-4a25-bef9-89fa2f222bcd not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.747 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.747 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid e205cab2-2fb0-4ff6-af8d-f3e373541015 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.747 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.748 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 2c0855db-f836-4551-b5d5-8868b8071de0 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.755 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.755 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 81f68bb5-7c32-4e6f-aa7c-4f73674803ff not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.756 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.756 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 5f499a95-36af-4367-ab9e-82a24520ffe3 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.756 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.756 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 0beab53e-8749-48ba-b3d4-312de5c80d01 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.756 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.756 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 37cfd367-fef8-4780-926c-11e65f6a76cb not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.761 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.762 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid b8eb6feb-ce53-40c5-ad3b-abf6436537bb not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.762 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.762 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 1abb8f37-9ceb-4148-91e6-58799868bc8b not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.763 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.763 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid ca22a672-8a58-4e9e-9cbb-8e970552bd47 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.763 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.763 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid dac963d1-f0b2-43a7-a732-6a08e186aff5 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.769 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.770 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 95d99ad4-b69e-467c-b09b-fec5695c5d0d not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.771 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.771 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 85d89144-3799-4c77-a7a3-24f593303ac6 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.771 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.771 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 517eaab6-7bf0-4e5e-81be-86b57031bebf not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.771 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.771 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 1ca1120f-eceb-4546-a983-cd02bed3946f not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.784 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.784 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 1a319398-696e-4c9d-9cb6-f19f30db5f95 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.785 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.785 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 3003b19f-9792-42ef-9884-4a5cfaf07987 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.785 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.785 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid f8bf6bde-b4a9-44d5-9590-12e02a93943c not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.785 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.785 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 496fa621-07b1-447c-8ece-df6964d1c1a5 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.790 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.790 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 4a331556-980d-4429-89b3-e70ebc3b1ed2 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.791 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.791 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid e66a7475-beb0-45b0-aadb-0ba20b4df3c2 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.792 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.792 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 5730d011-554a-408c-88f1-6dd0c0ef43cb not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.792 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.792 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 22121843-2e8a-445f-9f64-407d257cf1fd not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.801 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.801 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid db7eab2b-1a1e-4c15-b6d2-53806159670a not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.801 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.802 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid e668c5dd-216c-4356-b9b5-71af625ac7d3 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.802 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.802 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 1769bee8-1d55-4e65-b5bd-74e3764007b5 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.802 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.802 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 2c886543-a4eb-43f0-9ceb-aaead7077f18 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.808 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.808 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid aadf5072-faf8-453e-b3b9-1ebb7bb6767f not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.809 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.809 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid b4c496d1-a150-4975-8ed9-de84f7ae32e4 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.809 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.809 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid ed6d9ea5-3023-49e7-97e5-03148c41cd70 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.809 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.809 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid acfaa9ef-fe09-4e29-88bd-dfb5bc44535d not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.815 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.816 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 9d9e9537-a2e8-41ba-8e97-542389cba57c not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.816 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.816 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid ea0858ff-187b-4e85-bfc6-ccfbce401e29 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.816 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.816 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 29689448-0d16-4756-88d9-59ddfe4f7364 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.816 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.817 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 50e0032e-8e12-4a00-affe-95e37422c79b not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.822 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.822 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 689717ac-f699-44a3-b866-6ae858bf46f0 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.823 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.823 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 24ae9297-c95b-4da7-a27d-ca92f4151aad not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.823 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.823 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 6b2b4963-0a38-4ed7-80b8-aa8198b6042c not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.823 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.823 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 4c3f936c-de53-4590-b715-ae0b0b9fa46b not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.829 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.830 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid dc50542e-e36e-4c96-afb3-6111c723ba0f not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.831 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.831 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid ab149783-3f57-4418-ab82-06cc39a7c3e7 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.831 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.831 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid c79ebfbf-cbe5-4c20-9c80-1d910c67e4c3 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.832 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.832 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid e69100bd-d1a0-4261-81d7-405cbc6d2e55 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.836 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.836 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid e0f7b84a-2ea4-4bdd-9c85-f4b88b518794 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.837 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.837 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 4cdb4d3d-371c-4d34-ae99-8c4eb6476c1c not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.837 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.837 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 4697cc4c-949b-4cd0-b7c7-f626ce5a91bb not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.837 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.837 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 76dfb117-791a-40d3-8e43-b497a367e33a not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.843 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.844 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid b0f177fa-9491-4be5-b94f-cd8c9be7027c not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.844 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.844 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 2e2396f6-c5ce-4fca-81fd-24c76644a3da not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.844 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.844 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 81e8feaa-075e-474b-8ebb-52ca37478aad not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.844 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.844 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid b86400e7-989e-4a9d-afe0-c1c3b3fc82e1 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.852 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.852 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 8c45fdd8-6ba0-49a3-9ca4-7de64ba09ea6 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.853 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.853 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 3efdd9a3-ead7-46c6-a9a1-dc8216bcd075 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.853 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.853 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 7bf02469-7d11-4dc9-b50b-************ not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.853 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.853 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid d6af8644-4623-4c36-ade5-9fc3c22d7348 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.859 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.859 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 9bec4d88-39f7-46fe-a245-0e4be604d683 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.859 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.859 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 7927ccb3-8598-4edf-aa74-80daef886bd6 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.859 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.859 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 88a078dc-4bb4-4364-a3e8-8155537f4d91 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.859 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.860 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid b3ebc607-47dd-4329-a3b8-f274555e235a not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.865 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.865 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 3d87470e-d3c4-4bee-ae52-eb12113aba64 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.865 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.865 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 1c59cc70-ceb2-4eef-b993-93526b953b90 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.865 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.866 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 08cde9ee-e2af-4d35-8bb6-e92f69439e1d not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.866 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.866 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 0c4054a7-58c5-45a7-b216-3dab752eb099 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.873 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.873 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 90844b3b-b7d9-44c1-bab4-01f41f45b9ce not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.874 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.874 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid e6a2ea12-c43c-4f47-8316-a2b370cc1567 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.874 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.874 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid c6e53ee4-e4c5-433a-a825-6e77a5eb235c not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.874 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.874 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid b740a114-e309-4a7d-bc82-32c881d54afa not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.880 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.880 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 0775abac-9d74-4cfc-847a-7175880fcaf5 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.880 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.880 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 4e87fc80-cf8e-479c-9a90-ef411c0b9fd1 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.880 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.880 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 57258e7f-eda4-40c7-b8de-71030f17ccf9 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.881 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.881 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid bdc93322-c197-424b-87e1-ea7026eebe92 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.887 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.887 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 880026ec-2de3-45ad-b665-90165c5b637f not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.888 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.888 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid d96471b1-0184-4e18-9fa0-73463f4e8f76 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.888 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.888 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid a4e30483-dad3-43b8-ae84-cbc8ab83be97 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.888 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.888 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 18056b70-5224-4ab7-9041-d1eb5bd8b103 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.893 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.894 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 4c901855-a756-4c15-82a4-243105c4b17f not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.895 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.895 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid a9dcfebe-5b10-4ff6-a075-ec90d2594ff9 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.895 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.895 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 582602bf-bbe5-4d59-b6f9-a40397038394 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.938 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.939 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 64afa7df-567b-4258-a564-f271cf9c4a89 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.951 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.951 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 36f53b9a-4ea0-4fe7-a0f5-555dfc75dbe9 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.964 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.964 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 07e62ecb-c22e-4ce0-a1dc-76a29deb4a1c not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.983 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.983 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 763cbdbb-b99f-41b1-96ee-3a2d65c64845 not found
[INFO ] 20250803-173848 [main] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:38:48.983 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:38:48.983 [main] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 9d95c3cc-8e3f-4b2d-9d08-e605696100c3 not found
[INFO ] 20250803-173849 [main] info- Setting up allure report environment data: Edge
Allure environment data saved.
Aug 03, 2025 5:38:49 PM org.openqa.selenium.remote.tracing.opentelemetry.OpenTelemetryTracer createTracer
INFO: Using OpenTelemetry for tracing
Running testOne on thread: 37
17:39:00.630 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not update test case: no test case running
17:39:00.631 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not update test case: no test case running
=====================********************************************************************************
=====================********************************************************************************
=====================********************************************************************************
[INFO ] 20250803-173903 [TestNG-tests-2] info- *********      Executing DB Query     ************
17:39:03.487 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:03.487 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 9a577644-4293-4d58-9d30-7aaabe97021f not found
[INFO ] 20250803-173903 [TestNG-tests-2] info- UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-04'
17:39:03.487 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:03.487 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 0033057d-00b1-4ee8-b257-272ce43e92d7 not found
[INFO ] 20250803-173903 [TestNG-tests-1] info- *********      Executing DB Query     ************
[INFO ] 20250803-173903 [TestNG-tests-3] info- *********      Executing DB Query     ************
17:39:03.488 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:03.488 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 04c1eb57-9148-4b96-85a5-bbad417880e0 not found
[INFO ] 20250803-173903 [TestNG-tests-1] info- UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-02'
17:39:03.488 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:03.488 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 47e9ef64-16f3-4a9b-9230-12bda2fecb86 not found
[INFO ] 20250803-173903 [TestNG-tests-3] info- UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-07'
[INFO ] 20250803-173903 [TestNG-tests-2] info- ************     DB Query executed successfully     ***************
17:39:03.532 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:03.532 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 6d95f16c-398f-4615-a7fa-155a416b0064 not found
[INFO ] 20250803-173903 [TestNG-tests-2] info- ******     Closing DB Connection      *******
17:39:03.532 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:03.532 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 16f24e92-b595-45f9-ab09-caa830421cb2 not found
[INFO ] 20250803-173903 [TestNG-tests-2] info- ******      DB Connection closed successfully     *******
17:39:03.535 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:03.535 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 3783aa63-400c-4726-84ec-8ffe36fb47f4 not found
[INFO ] 20250803-173903 [TestNG-tests-1] info- ************     DB Query executed successfully     ***************
17:39:03.541 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:03.541 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid a34a8202-4551-48ba-b81b-1462c04f75cf not found
[INFO ] 20250803-173903 [TestNG-tests-1] info- ******     Closing DB Connection      *******
17:39:03.541 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:03.541 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid c9ebdae7-202d-4254-ab0a-8fe1fbdce014 not found
[INFO ] 20250803-173903 [TestNG-tests-1] info- ******      DB Connection closed successfully     *******
17:39:03.542 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:03.542 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 52859a23-5663-4590-baeb-545f97bb6011 not found
[INFO ] 20250803-173903 [TestNG-tests-3] info- ************     DB Query executed successfully     ***************
[INFO ] 20250803-173903 [TestNG-tests-3] info- ******     Closing DB Connection      *******
[INFO ] 20250803-173903 [TestNG-tests-3] info- ******      DB Connection closed successfully     *******
17:39:03.804 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:03.804 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 60984aab-d550-407d-9973-2d95c614a86d not found
17:39:03.921 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:03.921 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 851f499c-454a-4faf-8243-437d828b83c4 not found
17:39:11.830 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:11.830 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid af9e701f-f910-49ec-8b0d-6cf5789ada81 not found
17:39:12.650 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:12.650 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 2f6adcda-5658-4e60-9cec-3fd657172bed not found
17:39:12.650 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:12.657 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 898f79ba-4c41-4406-b0c2-dcb26ac47398 not found
17:39:12.667 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:12.669 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 93f71d87-6c43-47b3-9e11-1d140c2f6ac7 not found
17:39:12.684 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:12.684 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 90fcbfa8-c31f-49e5-8ede-e24c995a6a01 not found
[INFO ] 20250803-173912 [TestNG-tests-2] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:39:12.685 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:12.686 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid a74296d2-1010-4c07-8e70-b9bcb3c99e08 not found
=====================********************************************************************************
[INFO ] 20250803-173912 [TestNG-tests-2] info- *********      Executing DB Query     ************
17:39:12.804 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:12.805 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid e96e0ef6-d2aa-411d-b6b4-beaded71f24c not found
[INFO ] 20250803-173912 [TestNG-tests-2] info- select id from tZones tz where tz.DISPLAY_NAME  = 'Common Zone 04'
17:39:12.805 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:12.805 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 4f5230e9-779d-4802-9d0b-220cfa58fe35 not found
[INFO ] 20250803-173913 [TestNG-tests-2] info- ************     DB Query executed successfully     ***************
17:39:13.007 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:13.008 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 857255a4-6b89-4def-9c3c-73e81d3789de not found
[INFO ] 20250803-173913 [TestNG-tests-2] info- ******     Closing DB Connection      *******
17:39:13.055 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:13.055 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid e7434a00-98f1-4664-b7c0-b86eb5c7793c not found
[INFO ] 20250803-173913 [TestNG-tests-2] info- ******      DB Connection closed successfully     *******
17:39:13.072 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:13.073 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid *************-4ea4-9195-b8e1c6a259d2 not found
=====================***************************************************************************************
[INFO ] 20250803-173913 [TestNG-tests-2] info- *********      Executing DB Query     ************
17:39:13.148 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:13.148 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid b1013ebc-36ea-42bf-bfdb-764d97993435 not found
[INFO ] 20250803-173913 [TestNG-tests-2] info- DELETE  FROM tSetContent   WHERE id IN ( SELECT tsc.id FROM tSetContent tsc  LEFT JOIN tBlackListVersions tblv on tblv.id =tsc.black_list_id  LEFT JOIN tListSets tls on tls.id = tsc.list_set_id  LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id  WHERE tbl.zone_id ='6'  AND tbl.display_name ='UN Iran')
17:39:13.154 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:13.154 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 4a8cc921-22ec-4843-9918-4a3ac5b60049 not found
[INFO ] 20250803-173913 [TestNG-tests-2] info- ************     DB Query executed successfully     ***************
17:39:13.203 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:13.203 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 819712e2-2095-426f-8bcf-a69b3d5faf52 not found
[INFO ] 20250803-173913 [TestNG-tests-2] info- ******     Closing DB Connection      *******
17:39:13.204 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:13.204 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 5143492e-e16a-4284-8939-8b6246d64684 not found
[INFO ] 20250803-173913 [TestNG-tests-2] info- ******      DB Connection closed successfully     *******
17:39:13.209 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:13.211 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 1c497713-ed5e-47eb-ac46-678b3b39e72e not found
[INFO ] 20250803-173913 [TestNG-tests-2] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:39:13.211 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:13.211 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid f2223e1c-254a-4e78-ba1e-8a0fb377420a not found
=====================***************************************************************************************
[INFO ] 20250803-173913 [TestNG-tests-2] info- *********      Executing DB Query     ************
17:39:13.269 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:13.269 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 991621cc-81ec-4ac1-a541-b6ee139355f6 not found
[INFO ] 20250803-173913 [TestNG-tests-2] info- DELETE  FROM tGoodGuys  WHERE id IN ( SELECT tgg.id  FROM tGoodGuys tgg LEFT JOIN tBlackListVersions tblv on tblv.id =tgg.black_list_id LEFT JOIN tBlackLists tbl on tbl.id = tblv.black_list_id WHERE tbl.display_name ='UN Iran')
17:39:13.269 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:13.269 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 4060f898-1519-4b56-99ab-af3e2c746af4 not found
[INFO ] 20250803-173913 [TestNG-tests-2] info- ************     DB Query executed successfully     ***************
17:39:13.356 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:13.356 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 3ba031df-51a8-4b5a-b5ef-87dad823f1a4 not found
[INFO ] 20250803-173913 [TestNG-tests-2] info- ******     Closing DB Connection      *******
17:39:13.356 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:13.356 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid ba62abad-6539-43bd-9a80-7275d6be81c6 not found
[INFO ] 20250803-173913 [TestNG-tests-2] info- ******      DB Connection closed successfully     *******
17:39:13.357 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:13.357 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 4d4960f1-247c-493a-a217-10570b2b8d00 not found
[INFO ] 20250803-173913 [TestNG-tests-2] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
17:39:13.357 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:13.358 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid cdf26086-3874-4515-9da6-21a381cd630b not found
=====================***************************************************************************************
[INFO ] 20250803-173913 [TestNG-tests-2] info- *********      Executing DB Query     ************
17:39:13.463 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:13.463 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 553d39f5-1daf-4433-84f0-e17bef31e3c7 not found
[INFO ] 20250803-173913 [TestNG-tests-2] info- DELETE FROM tBlackLists WHERE display_name ='UN Iran'
17:39:13.463 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:13.463 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 26373098-d20c-43d9-a206-00975d97ff88 not found
[INFO ] 20250803-173914 [TestNG-tests-2] info- ************     DB Query executed successfully     ***************
17:39:14.078 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:14.080 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid b2a1d2bf-c4e6-4c33-a2a9-6169f08cc3f4 not found
[INFO ] 20250803-173914 [TestNG-tests-2] info- ******     Closing DB Connection      *******
17:39:14.080 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:14.080 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 844cf5ac-f666-4546-b7ad-0d594ee96ba6 not found
[INFO ] 20250803-173914 [TestNG-tests-2] info- ******      DB Connection closed successfully     *******
17:39:14.080 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:39:14.080 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 0792e2fa-abda-4b98-b4df-30a300bf73b6 not found
[INFO ] 20250803-174041 [TestNG-tests-2] info- Attempting to save screenshot...
17:40:41.616 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:40:41.616 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 95ad873a-dd1e-4237-a116-fde3917065e5 not found
[INFO ] 20250803-174041 [TestNG-tests-2] info- Screenshot saved at: C:\Users\<USER>\IdeaProjects\SWS_selenium\FailedTestsScreenshots\screenshot_1754232041616.png
17:40:41.640 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:40:41.640 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 9fbca7d7-9088-4f89-b0e4-04ac7810455e not found
17:40:41.642 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not add attachment: no test is running
[ERROR] 20250803-174041 [TestNG-tests-2] error- Error occurred While logging in eastnets.screening.regression.scanmanager.ScanManager_PreCondition$3.importList
org.openqa.selenium.TimeoutException: Expected condition failed: waiting for visibility of element located by By.id: listManagerForm:homepage_business:tabViewListManager:Tab_activity:searchDateFrom (tried for 60 second(s) with 5 milliseconds interval)
Build info: version: '4.13.0', revision: 'ba948ece5b*'
System info: os.name: 'Windows Server 2022', os.arch: 'amd64', os.version: '10.0', java.version: '21.0.6'
Driver info: driver.version: unknown
	at org.openqa.selenium.support.ui.FluentWait.timeoutException(FluentWait.java:262) ~[selenium-support-4.13.0.jar:?]
	at org.openqa.selenium.support.ui.FluentWait.until(FluentWait.java:230) ~[selenium-support-4.13.0.jar:?]
	at core.util.Wait.waitUntilElementToBeVisible(Wait.java:42) ~[classes/:?]
	at eastnets.screening.gui.listManager.ListManagerNavigation.navigate(ListManagerNavigation.java:46) ~[classes/:?]
	at eastnets.screening.control.listManager.ActivityControl.get_status(ActivityControl.java:24) ~[classes/:?]
	at eastnets.screening.control.listManager.BlackListControl.import_list(BlackListControl.java:127) ~[classes/:?]
	at eastnets.screening.regression.scanmanager.ScanManager_PreCondition.importList(ScanManager_PreCondition.java:110) ~[test-classes/:?]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:69) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:361) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:296) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.ConfigInvoker.invokeBeforeGroupsConfigurations(ConfigInvoker.java:150) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:585) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:221) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:50) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:969) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:194) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128) ~[testng-7.7.0.jar:7.7.0]
	at java.util.ArrayList.forEach(ArrayList.java:1596) ~[?:?]
	at org.testng.TestRunner.privateRun(TestRunner.java:829) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.TestRunner.run(TestRunner.java:602) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:437) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:475) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.thread.ThreadUtil.lambda$execute$0(ThreadUtil.java:58) ~[testng-7.7.0.jar:7.7.0]
	at java.util.concurrent.FutureTask.run(FutureTask.java:317) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[?:?]
	at java.lang.Thread.run(Thread.java:1583) [?:?]
17:40:41.682 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:40:41.682 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 4eee4fc1-9cce-4420-821f-b28fe81f9485 not found
=====================********************************************************************************
[INFO ] 20250803-174044 [TestNG-tests-2] info- *********      Executing DB Query     ************
[INFO ] 20250803-174044 [TestNG-tests-2] info- UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-04'
[INFO ] 20250803-174044 [TestNG-tests-2] info- ************     DB Query executed successfully     ***************
[INFO ] 20250803-174044 [TestNG-tests-2] info- ******     Closing DB Connection      *******
[INFO ] 20250803-174044 [TestNG-tests-2] info- ******      DB Connection closed successfully     *******
17:40:59.387 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not update test case: test case with uuid 40adf9ed-4875-43a9-b56a-48032273e966 not found
=====================********************************************************************************
[INFO ] 20250803-174100 [TestNG-tests-2] info- *********      Executing DB Query     ************
[INFO ] 20250803-174100 [TestNG-tests-2] info- UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-04'
[INFO ] 20250803-174100 [TestNG-tests-2] info- ************     DB Query executed successfully     ***************
[INFO ] 20250803-174100 [TestNG-tests-2] info- ******     Closing DB Connection      *******
[INFO ] 20250803-174100 [TestNG-tests-2] info- ******      DB Connection closed successfully     *******
17:41:12.930 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not update test case: test case with uuid 2ed4f2ed-f662-49fa-b986-b3a68fdcd9aa not found
=====================********************************************************************************
[INFO ] 20250803-174113 [TestNG-tests-2] info- *********      Executing DB Query     ************
[INFO ] 20250803-174113 [TestNG-tests-2] info- UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-04'
[INFO ] 20250803-174113 [TestNG-tests-2] info- ************     DB Query executed successfully     ***************
[INFO ] 20250803-174113 [TestNG-tests-2] info- ******     Closing DB Connection      *******
[INFO ] 20250803-174113 [TestNG-tests-2] info- ******      DB Connection closed successfully     *******
17:41:24.611 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not update test case: test case with uuid a73bb7bf-c1ae-4c6d-ba78-9297b5c2682e not found
=====================********************************************************************************
[INFO ] 20250803-174125 [TestNG-tests-2] info- *********      Executing DB Query     ************
[INFO ] 20250803-174125 [TestNG-tests-2] info- UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-04'
[INFO ] 20250803-174125 [TestNG-tests-2] info- ************     DB Query executed successfully     ***************
[INFO ] 20250803-174125 [TestNG-tests-2] info- ******     Closing DB Connection      *******
[INFO ] 20250803-174125 [TestNG-tests-2] info- ******      DB Connection closed successfully     *******
[INFO ] 20250803-174145 [TestNG-tests-3] info- Attempting to save screenshot...
[INFO ] 20250803-174145 [TestNG-tests-3] info- Screenshot saved at: C:\Users\<USER>\IdeaProjects\SWS_selenium\FailedTestsScreenshots\screenshot_1754232105880.png
[ERROR] 20250803-174145 [TestNG-tests-3] error- Error occurred While logging in eastnets.screening.regression.iso20022configurations.ISO20022_TC001$7.logout
org.openqa.selenium.TimeoutException: Expected condition failed: waiting for number of elements found by By.xpath: //*[@title='Exit'] to be more than "0". Current number: "0" (tried for 120 second(s) with 10 milliseconds interval)
Build info: version: '4.13.0', revision: 'ba948ece5b*'
System info: os.name: 'Windows Server 2022', os.arch: 'amd64', os.version: '10.0', java.version: '21.0.6'
Driver info: driver.version: unknown
	at org.openqa.selenium.support.ui.FluentWait.timeoutException(FluentWait.java:262) ~[selenium-support-4.13.0.jar:?]
	at org.openqa.selenium.support.ui.FluentWait.until(FluentWait.java:230) ~[selenium-support-4.13.0.jar:?]
	at core.gui.Controls.getWebElement(Controls.java:29) ~[classes/:?]
	at core.util.Wait.elementVisible(Wait.java:31) ~[classes/:?]
	at eastnets.common.control.CommonAction.logout(CommonAction.java:52) ~[classes/:?]
	at eastnets.screening.regression.iso20022configurations.ISO20022_TC001.logout(ISO20022_TC001.java:98) ~[test-classes/:?]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:69) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:361) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:296) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.TestInvoker.runConfigMethods(TestInvoker.java:823) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.TestInvoker.runAfterConfigurations(TestInvoker.java:792) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:768) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:221) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:50) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:969) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:194) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128) ~[testng-7.7.0.jar:7.7.0]
	at java.util.ArrayList.forEach(ArrayList.java:1596) ~[?:?]
	at org.testng.TestRunner.privateRun(TestRunner.java:829) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.TestRunner.run(TestRunner.java:602) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:437) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:475) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.thread.ThreadUtil.lambda$execute$0(ThreadUtil.java:58) ~[testng-7.7.0.jar:7.7.0]
	at java.util.concurrent.FutureTask.run(FutureTask.java:317) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[?:?]
	at java.lang.Thread.run(Thread.java:1583) [?:?]
[INFO ] 20250803-174145 [TestNG-tests-1] info- Attempting to save screenshot...
17:41:45.927 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:41:45.927 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 419cdf86-0f0c-4a84-9266-9595d95428e5 not found
[INFO ] 20250803-174145 [TestNG-tests-3] info- Failed test tracked: eastnets.screening.regression.iso20022configurations.ISO20022_TC001.iso20022_TC001
[INFO ] 20250803-174145 [TestNG-tests-1] info- Screenshot saved at: C:\Users\<USER>\IdeaProjects\SWS_selenium\FailedTestsScreenshots\screenshot_1754232105927.png
17:41:45.934 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:41:45.934 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid b9e63940-f55e-40f6-b64a-ca60ded78175 not found
17:41:45.934 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not add attachment: no test is running
[ERROR] 20250803-174145 [TestNG-tests-1] error- Error occurred While logging in eastnets.screening.regression.listmanager.listmanagertests.ListManager_PreCondition$1.importList
org.openqa.selenium.TimeoutException: Expected condition failed: waiting for number of elements found by By.id: listManagerForm:homepage_business:tabViewListManager:Tab_list_manager:Sub_black_list_creation_viewer:name to be more than "0". Current number: "0" (tried for 120 second(s) with 10 milliseconds interval)
Build info: version: '4.13.0', revision: 'ba948ece5b*'
System info: os.name: 'Windows Server 2022', os.arch: 'amd64', os.version: '10.0', java.version: '21.0.6'
Driver info: driver.version: unknown
	at org.openqa.selenium.support.ui.FluentWait.timeoutException(FluentWait.java:262) ~[selenium-support-4.13.0.jar:?]
	at org.openqa.selenium.support.ui.FluentWait.until(FluentWait.java:230) ~[selenium-support-4.13.0.jar:?]
	at core.gui.Controls.getWebElement(Controls.java:29) ~[classes/:?]
	at core.gui.Controls.clearTextBoxValue(Controls.java:53) ~[classes/:?]
	at core.gui.Controls.setTextBoxValue(Controls.java:38) ~[classes/:?]
	at eastnets.screening.gui.listManager.blackList.BlackListEditor.set_name(BlackListEditor.java:53) ~[classes/:?]
	at eastnets.screening.control.listManager.BlackListControl.create_Black_List(BlackListControl.java:102) ~[classes/:?]
	at core.CommonTestMethods.addListSetAndimportEntries(CommonTestMethods.java:474) ~[test-classes/:?]
	at eastnets.screening.regression.listmanager.listmanagertests.ListManager_PreCondition.importList(ListManager_PreCondition.java:51) ~[test-classes/:?]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:69) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:361) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:296) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.ConfigInvoker.invokeBeforeGroupsConfigurations(ConfigInvoker.java:150) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:585) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:221) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:50) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:969) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:194) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128) ~[testng-7.7.0.jar:7.7.0]
	at java.util.ArrayList.forEach(ArrayList.java:1596) ~[?:?]
	at org.testng.TestRunner.privateRun(TestRunner.java:829) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.TestRunner.run(TestRunner.java:602) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:437) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:475) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.thread.ThreadUtil.lambda$execute$0(ThreadUtil.java:58) ~[testng-7.7.0.jar:7.7.0]
	at java.util.concurrent.FutureTask.run(FutureTask.java:317) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[?:?]
	at java.lang.Thread.run(Thread.java:1583) [?:?]
17:41:45.942 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not start step: no test case running
17:41:45.942 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not stop step: step with uuid 120b274e-8da5-492d-90ab-c298749bd4df not found
/src/test/resources/testDataFiles/ISO20022TD-70749.json
=====================********************************************************************************
[INFO ] 20250803-174150 [TestNG-tests-1] info- *********      Executing DB Query     ************
[INFO ] 20250803-174150 [TestNG-tests-1] info- UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-02'
[INFO ] 20250803-174150 [TestNG-tests-1] info- ************     DB Query executed successfully     ***************
[INFO ] 20250803-174150 [TestNG-tests-1] info- ******     Closing DB Connection      *******
[INFO ] 20250803-174150 [TestNG-tests-1] info- ******      DB Connection closed successfully     *******
=====================********************************************************************************
[INFO ] 20250803-174150 [TestNG-tests-3] info- *********      Executing DB Query     ************
[INFO ] 20250803-174150 [TestNG-tests-3] info- UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-07'
[INFO ] 20250803-174150 [TestNG-tests-3] info- ************     DB Query executed successfully     ***************
[INFO ] 20250803-174150 [TestNG-tests-3] info- ******     Closing DB Connection      *******
[INFO ] 20250803-174150 [TestNG-tests-3] info- ******      DB Connection closed successfully     *******
17:41:51.982 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not update test case: test case with uuid 2abd13d4-2fa7-417d-b8b5-ebbb74d051e1 not found
Running testOne on thread: 37
=====================********************************************************************************
[INFO ] 20250803-174153 [TestNG-tests-2] info- *********      Executing DB Query     ************
[INFO ] 20250803-174153 [TestNG-tests-2] info- UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-04'
[INFO ] 20250803-174153 [TestNG-tests-2] info- ************     DB Query executed successfully     ***************
[INFO ] 20250803-174153 [TestNG-tests-2] info- ******     Closing DB Connection      *******
[INFO ] 20250803-174153 [TestNG-tests-2] info- ******      DB Connection closed successfully     *******
Running testOne on thread: 37
=====================********************************************************************************
[INFO ] 20250803-174220 [TestNG-tests-2] info- *********      Executing DB Query     ************
[INFO ] 20250803-174220 [TestNG-tests-2] info- UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-04'
[INFO ] 20250803-174220 [TestNG-tests-2] info- ************     DB Query executed successfully     ***************
[INFO ] 20250803-174220 [TestNG-tests-2] info- ******     Closing DB Connection      *******
[INFO ] 20250803-174220 [TestNG-tests-2] info- ******      DB Connection closed successfully     *******
Running testOne on thread: 37
=====================********************************************************************************
[INFO ] 20250803-174236 [TestNG-tests-2] info- *********      Executing DB Query     ************
[INFO ] 20250803-174236 [TestNG-tests-2] info- UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-04'
[INFO ] 20250803-174236 [TestNG-tests-2] info- ************     DB Query executed successfully     ***************
[INFO ] 20250803-174236 [TestNG-tests-2] info- ******     Closing DB Connection      *******
[INFO ] 20250803-174236 [TestNG-tests-2] info- ******      DB Connection closed successfully     *******
Running testOne on thread: 37
=====================********************************************************************************
[INFO ] 20250803-174313 [TestNG-tests-2] info- *********      Executing DB Query     ************
[INFO ] 20250803-174313 [TestNG-tests-2] info- UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-04'
[INFO ] 20250803-174313 [TestNG-tests-2] info- ************     DB Query executed successfully     ***************
[INFO ] 20250803-174313 [TestNG-tests-2] info- ******     Closing DB Connection      *******
[INFO ] 20250803-174313 [TestNG-tests-2] info- ******      DB Connection closed successfully     *******
[INFO ] 20250803-174321 [TestNG-tests-3] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
=====================********************************************************************************
=====================********************************************************************************
[INFO ] 20250803-174321 [TestNG-tests-3] info- *********      Executing DB Query     ************
[INFO ] 20250803-174321 [TestNG-tests-3] info- SELECT ID FROM tProfiles Where NAME ='full-right-profile_07' and deleted= 0
[INFO ] 20250803-174321 [TestNG-tests-3] info- ************     DB Query executed successfully     ***************
[INFO ] 20250803-174321 [TestNG-tests-3] info- ******     Closing DB Connection      *******
[INFO ] 20250803-174321 [TestNG-tests-3] info- ******      DB Connection closed successfully     *******
=====================***************************************************************************************
[INFO ] 20250803-174321 [TestNG-tests-3] info- *********      Executing DB Query     ************
[INFO ] 20250803-174321 [TestNG-tests-3] info- Delete From tListSetProfile where profile_id in (10)
[INFO ] 20250803-174321 [TestNG-tests-3] info- ************     DB Query executed successfully     ***************
[INFO ] 20250803-174321 [TestNG-tests-3] info- ******     Closing DB Connection      *******
[INFO ] 20250803-174321 [TestNG-tests-3] info- ******      DB Connection closed successfully     *******
Running testOne on thread: 37
=====================********************************************************************************
[INFO ] 20250803-174326 [TestNG-tests-2] info- *********      Executing DB Query     ************
[INFO ] 20250803-174326 [TestNG-tests-2] info- UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-04'
[INFO ] 20250803-174326 [TestNG-tests-2] info- ************     DB Query executed successfully     ***************
[INFO ] 20250803-174326 [TestNG-tests-2] info- ******     Closing DB Connection      *******
[INFO ] 20250803-174326 [TestNG-tests-2] info- ******      DB Connection closed successfully     *******
17:43:39.144 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not update test case: test case with uuid 39f13764-c6b4-45f6-9754-ab3d04bad888 not found
=====================********************************************************************************
[INFO ] 20250803-174340 [TestNG-tests-2] info- *********      Executing DB Query     ************
[INFO ] 20250803-174340 [TestNG-tests-2] info- UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-04'
[INFO ] 20250803-174340 [TestNG-tests-2] info- ************     DB Query executed successfully     ***************
[INFO ] 20250803-174340 [TestNG-tests-2] info- ******     Closing DB Connection      *******
[INFO ] 20250803-174340 [TestNG-tests-2] info- ******      DB Connection closed successfully     *******
17:44:03.002 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not update test case: test case with uuid dc6dc548-478f-474f-abbd-0b06c6f7124b not found
Running testOne on thread: 37
=====================********************************************************************************
[INFO ] 20250803-174404 [TestNG-tests-2] info- *********      Executing DB Query     ************
[INFO ] 20250803-174404 [TestNG-tests-2] info- UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-04'
[INFO ] 20250803-174404 [TestNG-tests-2] info- ************     DB Query executed successfully     ***************
[INFO ] 20250803-174404 [TestNG-tests-2] info- ******     Closing DB Connection      *******
[INFO ] 20250803-174404 [TestNG-tests-2] info- ******      DB Connection closed successfully     *******
[INFO ] 20250803-174407 [TestNG-tests-3] info- Validation message = Operation completed successfully.
17:44:18.987 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not update test case: test case with uuid 397baead-018a-469f-9114-7a9ac2a9498d not found
=====================********************************************************************************
[INFO ] 20250803-174420 [TestNG-tests-2] info- *********      Executing DB Query     ************
[INFO ] 20250803-174420 [TestNG-tests-2] info- UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-04'
[INFO ] 20250803-174420 [TestNG-tests-2] info- ************     DB Query executed successfully     ***************
[INFO ] 20250803-174420 [TestNG-tests-2] info- ******     Closing DB Connection      *******
[INFO ] 20250803-174420 [TestNG-tests-2] info- ******      DB Connection closed successfully     *******
17:44:32.117 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not update test case: test case with uuid b8b153e6-ea62-48c6-b14c-745e7ceee09d not found
=====================********************************************************************************
[INFO ] 20250803-174433 [TestNG-tests-2] info- *********      Executing DB Query     ************
[INFO ] 20250803-174433 [TestNG-tests-2] info- UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-04'
[INFO ] 20250803-174433 [TestNG-tests-2] info- ************     DB Query executed successfully     ***************
[INFO ] 20250803-174433 [TestNG-tests-2] info- ******     Closing DB Connection      *******
[INFO ] 20250803-174433 [TestNG-tests-2] info- ******      DB Connection closed successfully     *******
17:45:00.662 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not update test case: test case with uuid 9bf4bf41-0948-498e-be89-b60e1295699a not found
=====================********************************************************************************
[INFO ] 20250803-174501 [TestNG-tests-2] info- *********      Executing DB Query     ************
[INFO ] 20250803-174501 [TestNG-tests-2] info- UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-04'
[INFO ] 20250803-174501 [TestNG-tests-2] info- ************     DB Query executed successfully     ***************
[INFO ] 20250803-174501 [TestNG-tests-2] info- ******     Closing DB Connection      *******
[INFO ] 20250803-174501 [TestNG-tests-2] info- ******      DB Connection closed successfully     *******
17:45:12.124 [TestNG-tests-2] ERROR io.qameta.allure.AllureLifecycle -- Could not update test case: test case with uuid 66ee2d68-3b26-4e0d-a4c2-0dc2b0e3ee24 not found
Running testOne on thread: 37
=====================********************************************************************************
[INFO ] 20250803-174512 [TestNG-tests-2] info- *********      Executing DB Query     ************
[INFO ] 20250803-174512 [TestNG-tests-2] info- UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-04'
[INFO ] 20250803-174513 [TestNG-tests-2] info- ************     DB Query executed successfully     ***************
[INFO ] 20250803-174513 [TestNG-tests-2] info- ******     Closing DB Connection      *******
[INFO ] 20250803-174513 [TestNG-tests-2] info- ******      DB Connection closed successfully     *******
Running testOne on thread: 37
=====================********************************************************************************
[INFO ] 20250803-174524 [TestNG-tests-2] info- *********      Executing DB Query     ************
[INFO ] 20250803-174524 [TestNG-tests-2] info- UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-04'
[INFO ] 20250803-174524 [TestNG-tests-2] info- ************     DB Query executed successfully     ***************
[INFO ] 20250803-174524 [TestNG-tests-2] info- ******     Closing DB Connection      *******
[INFO ] 20250803-174524 [TestNG-tests-2] info- ******      DB Connection closed successfully     *******
[INFO ] 20250803-174534 [TestNG-tests-3] info- Validation Message = Operation completed successfully.
[INFO ] 20250803-174534 [TestNG-tests-3] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
=====================********************************************************************************
[INFO ] 20250803-174535 [TestNG-tests-3] info- *********      Executing DB Query     ************
[INFO ] 20250803-174535 [TestNG-tests-3] info- SELECT ID FROM tProfiles Where NAME ='full-right-profile_07'
[INFO ] 20250803-174535 [TestNG-tests-3] info- ************     DB Query executed successfully     ***************
[INFO ] 20250803-174535 [TestNG-tests-3] info- ******     Closing DB Connection      *******
[INFO ] 20250803-174535 [TestNG-tests-3] info- ******      DB Connection closed successfully     *******
[INFO ] 20250803-174732 [TestNG-tests-1] info- Failed test tracked: eastnets.screening.regression.listmanager.listmanagertests.ListManager_TC003.listManager_TC003
17:47:32.198 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not update test case: test case with uuid 2b2e1051-7b26-43fe-9ec4-1c7cf9be1c5a not found
17:47:32.212 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not update test case: test case with uuid 963afaab-466d-4dfd-8944-7514c8cd92f8 not found
17:47:32.245 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not update test case: test case with uuid 428eeb16-ffb1-4648-a45c-1754b23369d2 not found
17:47:32.247 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not update test case: test case with uuid 950daa1d-97e0-45b5-96a4-ba26cea4a7cf not found
17:47:32.259 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not update test case: test case with uuid 7f9239d2-86ed-4181-ba76-b79bb7b6f17e not found
17:47:32.310 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not update test case: test case with uuid b14fb0bb-5c84-49f0-9501-557a5e7a27f4 not found
17:47:32.338 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not update test case: test case with uuid 912816a6-ab11-4e1b-a0ed-2152e444b9b2 not found
17:47:32.350 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not update test case: test case with uuid e780173b-be84-4f02-8def-cf5e98e32528 not found
17:47:32.362 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not update test case: test case with uuid faa816f9-9f8d-478b-9713-f41f0762e884 not found
17:47:32.427 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not update test case: test case with uuid 0d110e7e-16ff-4031-a1a1-72786b66afed not found
17:47:32.437 [TestNG-tests-1] ERROR io.qameta.allure.AllureLifecycle -- Could not update test case: test case with uuid 72f12125-fc8d-417c-a993-664e36aec400 not found
=====================********************************************************************************
[INFO ] 20250803-174734 [TestNG-tests-1] info- *********      Executing DB Query     ************
[INFO ] 20250803-174734 [TestNG-tests-1] info- UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-02'
[INFO ] 20250803-174734 [TestNG-tests-1] info- ************     DB Query executed successfully     ***************
[INFO ] 20250803-174734 [TestNG-tests-1] info- ******     Closing DB Connection      *******
[INFO ] 20250803-174734 [TestNG-tests-1] info- ******      DB Connection closed successfully     *******
[INFO ] 20250803-174737 [TestNG-tests-2] info- Attempting to save screenshot...
[INFO ] 20250803-174737 [TestNG-tests-2] info- Screenshot saved at: C:\Users\<USER>\IdeaProjects\SWS_selenium\FailedTestsScreenshots\screenshot_1754232457328.png
[ERROR] 20250803-174737 [TestNG-tests-2] error- Error occurred While logging in eastnets.screening.regression.scanmanager.ScanManager_TC013$5.afterMethod
org.openqa.selenium.TimeoutException: Expected condition failed: waiting for number of elements found by By.id: pageLoginForm:login_business:UserName to be more than "0". Current number: "0" (tried for 120 second(s) with 10 milliseconds interval)
Build info: version: '4.13.0', revision: 'ba948ece5b*'
System info: os.name: 'Windows Server 2022', os.arch: 'amd64', os.version: '10.0', java.version: '21.0.6'
Driver info: driver.version: unknown
	at org.openqa.selenium.support.ui.FluentWait.timeoutException(FluentWait.java:262) ~[selenium-support-4.13.0.jar:?]
	at org.openqa.selenium.support.ui.FluentWait.until(FluentWait.java:230) ~[selenium-support-4.13.0.jar:?]
	at core.gui.Controls.getWebElement(Controls.java:29) ~[classes/:?]
	at core.util.Wait.elementVisible(Wait.java:31) ~[classes/:?]
	at eastnets.common.control.CommonAction.logout(CommonAction.java:55) ~[classes/:?]
	at eastnets.screening.regression.scanmanager.ScanManager_TC013.afterMethod(ScanManager_TC013.java:101) ~[test-classes/:?]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.MethodInvocationHelper.invokeMethodConsideringTimeout(MethodInvocationHelper.java:69) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.ConfigInvoker.invokeConfigurationMethod(ConfigInvoker.java:361) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.ConfigInvoker.invokeConfigurations(ConfigInvoker.java:296) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.TestInvoker.runConfigMethods(TestInvoker.java:823) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.TestInvoker.runAfterConfigurations(TestInvoker.java:792) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:613) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:221) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:50) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:969) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:194) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128) ~[testng-7.7.0.jar:7.7.0]
	at java.util.ArrayList.forEach(ArrayList.java:1596) ~[?:?]
	at org.testng.TestRunner.privateRun(TestRunner.java:829) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.TestRunner.run(TestRunner.java:602) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:437) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:475) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.thread.ThreadUtil.lambda$execute$0(ThreadUtil.java:58) ~[testng-7.7.0.jar:7.7.0]
	at java.util.concurrent.FutureTask.run(FutureTask.java:317) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[?:?]
	at java.lang.Thread.run(Thread.java:1583) [?:?]
Running testOne on thread: 37
=====================********************************************************************************
[INFO ] 20250803-174740 [TestNG-tests-2] info- *********      Executing DB Query     ************
[INFO ] 20250803-174740 [TestNG-tests-2] info- UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-04'
[INFO ] 20250803-174740 [TestNG-tests-2] info- ************     DB Query executed successfully     ***************
[INFO ] 20250803-174740 [TestNG-tests-2] info- ******     Closing DB Connection      *******
[INFO ] 20250803-174740 [TestNG-tests-2] info- ******      DB Connection closed successfully     *******
[INFO ] 20250803-174743 [TestNG-tests-1] info-  *******   Create an instance of ServiceDelegate to access backend DB    ******** 
=====================********************************************************************************
=====================********************************************************************************
[INFO ] 20250803-174744 [TestNG-tests-1] info- *********      Executing DB Query     ************
[INFO ] 20250803-174744 [TestNG-tests-1] info- SELECT ID FROM tProfiles Where NAME ='full-right-profile_002' and deleted= 0
[INFO ] 20250803-174744 [TestNG-tests-1] info- ************     DB Query executed successfully     ***************
[INFO ] 20250803-174744 [TestNG-tests-1] info- ******     Closing DB Connection      *******
[INFO ] 20250803-174744 [TestNG-tests-1] info- ******      DB Connection closed successfully     *******
=====================***************************************************************************************
[INFO ] 20250803-174744 [TestNG-tests-1] info- *********      Executing DB Query     ************
[INFO ] 20250803-174744 [TestNG-tests-1] info- Delete From tListSetProfile where profile_id in (5)
[INFO ] 20250803-174744 [TestNG-tests-1] info- ************     DB Query executed successfully     ***************
[INFO ] 20250803-174744 [TestNG-tests-1] info- ******     Closing DB Connection      *******
[INFO ] 20250803-174744 [TestNG-tests-1] info- ******      DB Connection closed successfully     *******
=====================********************************************************************************
[INFO ] 20250803-174804 [TestNG-tests-2] info- *********      Executing DB Query     ************
[INFO ] 20250803-174804 [TestNG-tests-2] info- DELETE FROM tRecordFormatFields  where tRecordFormatFields.format_id  in( Select trf.id from tRecordFormats trf Left join tZones tz on tz.ID = trf.zone_id where tz.DISPLAY_NAME = 'Common Zone 06')
[INFO ] 20250803-174804 [TestNG-tests-2] info- ************     DB Query executed successfully     ***************
[INFO ] 20250803-174804 [TestNG-tests-2] info- *********      Executing DB Query     ************
[INFO ] 20250803-174804 [TestNG-tests-2] info- DELETE FROM tRecordFormats where tRecordFormats.id in( Select trf.id from tRecordFormats trf Left join tZones tz on tz.ID = trf.zone_id where tz.DISPLAY_NAME = 'Common Zone 06')
[INFO ] 20250803-174804 [TestNG-tests-2] info- ************     DB Query executed successfully     ***************
[INFO ] 20250803-174804 [TestNG-tests-2] info- ******     Closing DB Connection      *******
[INFO ] 20250803-174804 [TestNG-tests-2] info- ******      DB Connection closed successfully     *******
=====================********************************************************************************
[INFO ] 20250803-174805 [TestNG-tests-2] info- *********      Executing DB Query     ************
[INFO ] 20250803-174805 [TestNG-tests-2] info- UPDATE tOperators  set  LOCKED ='0' , FAILED_LOGIN_COUNT ='0' WHERE LOGIN_NAME ='operator-06'
[INFO ] 20250803-174805 [TestNG-tests-2] info- ************     DB Query executed successfully     ***************
[INFO ] 20250803-174805 [TestNG-tests-2] info- ******     Closing DB Connection      *******
[INFO ] 20250803-174805 [TestNG-tests-2] info- ******      DB Connection closed successfully     *******
[INFO ] 20250803-174821 [TestNG-tests-3] info- Attempting to save screenshot...
[INFO ] 20250803-174821 [TestNG-tests-3] info- Screenshot saved at: C:\Users\<USER>\IdeaProjects\SWS_selenium\FailedTestsScreenshots\screenshot_1754232501278.png
[ERROR] 20250803-174821 [TestNG-tests-3] error- Error occurred While logging in eastnets.screening.regression.iso20022configurations.ISO20022_TC002$5.iso20022_TC0012
org.openqa.selenium.TimeoutException: Expected condition failed: waiting for number of elements found by By.xpath: //a[.='pacs.009.001'] to be more than "0". Current number: "0" (tried for 120 second(s) with 10 milliseconds interval)
Build info: version: '4.13.0', revision: 'ba948ece5b*'
System info: os.name: 'Windows Server 2022', os.arch: 'amd64', os.version: '10.0', java.version: '21.0.6'
Driver info: driver.version: unknown
	at org.openqa.selenium.support.ui.FluentWait.timeoutException(FluentWait.java:262) ~[selenium-support-4.13.0.jar:?]
	at org.openqa.selenium.support.ui.FluentWait.until(FluentWait.java:230) ~[selenium-support-4.13.0.jar:?]
	at core.gui.Controls.getWebElement(Controls.java:29) ~[classes/:?]
	at core.gui.Controls.performClickByJS(Controls.java:131) ~[classes/:?]
	at eastnets.screening.gui.iso20022Manager.ISO20022SchemaConfigurationManager.deleteSchemaVersion(ISO20022SchemaConfigurationManager.java:64) ~[classes/:?]
	at eastnets.screening.control.ISO20022Control.deleteSchema(ISO20022Control.java:66) ~[classes/:?]
	at core.ISOTestMethods.importISOSchema(ISOTestMethods.java:28) ~[test-classes/:?]
	at eastnets.screening.regression.iso20022configurations.ISO20022_TC002.iso20022_TC0012(ISO20022_TC002.java:124) ~[test-classes/:?]
	at jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103) ~[?:?]
	at java.lang.reflect.Method.invoke(Method.java:580) ~[?:?]
	at org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:677) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:221) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:50) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:969) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:194) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128) ~[testng-7.7.0.jar:7.7.0]
	at java.util.ArrayList.forEach(ArrayList.java:1596) ~[?:?]
	at org.testng.TestRunner.privateRun(TestRunner.java:829) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.TestRunner.run(TestRunner.java:602) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:437) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.SuiteRunner$SuiteWorker.run(SuiteRunner.java:475) ~[testng-7.7.0.jar:7.7.0]
	at org.testng.internal.thread.ThreadUtil.lambda$execute$0(ThreadUtil.java:58) ~[testng-7.7.0.jar:7.7.0]
	at java.util.concurrent.FutureTask.run(FutureTask.java:317) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144) ~[?:?]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642) ~[?:?]
	at java.lang.Thread.run(Thread.java:1583) [?:?]
[INFO ] 20250803-174939 [TestNG-tests-1] info- Validation message = Operation completed successfully.
