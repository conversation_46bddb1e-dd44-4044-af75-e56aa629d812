<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.eastnets.automation</groupId>
	<artifactId>filtering</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>Filtering - Automation</name>
	<description>Selenium Automation Project for Filtering</description>

	<dependencies>
		<!-- https://mvnrepository.com/artifact/org.seleniumhq.selenium/selenium-java -->
		<dependency>
			<groupId>org.seleniumhq.selenium</groupId>
			<artifactId>selenium-java</artifactId>
			<version>4.13.0</version>
		</dependency>

		<dependency>
			<groupId>org.seleniumhq.selenium</groupId>
			<artifactId>selenium-opera-driver</artifactId>
			<version>4.4.0</version>
			<scope>test</scope>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.testng/testng -->
		<dependency>
			<groupId>org.testng</groupId>
			<artifactId>testng</artifactId>
			<version>7.7.0</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>io.github.bonigarcia</groupId>
			<artifactId>webdrivermanager</artifactId>
			<version>5.5.3</version>
			<scope>test</scope>
		</dependency>

		<!-- https://mvnrepository.com/artifact/com.microsoft.sqlserver/mssql-jdbc -->
		<dependency>
			<groupId>com.microsoft.sqlserver</groupId>
			<artifactId>mssql-jdbc</artifactId>
			<version>12.6.1.jre11</version>
		</dependency>


		<!-- https://mvnrepository.com/artifact/com.oracle.database.jdbc/ojdbc8 -->
		<dependency>
			<groupId>com.oracle.database.jdbc</groupId>
			<artifactId>ojdbc8</artifactId>
			<version>21.5.0.0</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/com.opencsv/opencsv -->
		<dependency>
			<groupId>com.opencsv</groupId>
			<artifactId>opencsv</artifactId>
			<version>5.7.1</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/com.aventstack/extentreports -->
		<dependency>
			<groupId>com.aventstack</groupId>
			<artifactId>extentreports</artifactId>
			<version>4.0.9</version>
		</dependency>


		<!-- https://mvnrepository.com/artifact/org.apache.logging.log4j/log4j-core -->
		<dependency>
			<groupId>org.apache.logging.log4j</groupId>
			<artifactId>log4j-core</artifactId>
			<version>2.17.1</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/com.jcraft/jsch -->
		<dependency>
			<groupId>com.jcraft</groupId>
			<artifactId>jsch</artifactId>
			<version>0.1.55</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/com.paypal.selion/SeLion-DataProviders -->
		<dependency>
			<groupId>com.paypal.selion</groupId>
			<artifactId>SeLion-DataProviders</artifactId>
			<version>1.2.0</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.apache.pdfbox/pdfbox -->
		<dependency>
			<groupId>org.apache.pdfbox</groupId>
			<artifactId>pdfbox</artifactId>
			<version>2.0.1</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/io.qameta.allure/allure-testng -->
		<dependency>
			<groupId>io.qameta.allure</groupId>
			<artifactId>allure-testng</artifactId>
			<version>2.23.0</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.aspectj/aspectjweaver -->
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjweaver</artifactId>
			<version>1.9.19</version>
			<scope>runtime</scope>
		</dependency>
		<dependency>
			<groupId>com.github.automatedowl</groupId>
			<artifactId>allure-environment-writer</artifactId>
			<version>1.0.0</version>
		</dependency>

		<dependency>
			<groupId>io.qameta.allure</groupId>
			<artifactId>allure-plugin-api</artifactId>
			<version>2.23.0</version>
		</dependency>

		<dependency>
			<groupId>io.qameta.allure</groupId>
			<artifactId>allure-commandline</artifactId>
			<version>2.25.0</version>
			<type>pom</type>
		</dependency>

		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>31.0.1-jre</version>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-csv</artifactId>
			<version>1.5</version>
		</dependency>
        <dependency>
            <groupId>org.testng</groupId>
            <artifactId>testng</artifactId>
            <version>7.7.0</version>
            <scope>compile</scope>
        </dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<version>2.0.12</version>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-simple</artifactId>
			<version>2.0.12</version>
		</dependency>
		<dependency>
			<groupId>org.testng</groupId>
			<artifactId>testng</artifactId>
			<version>7.7.0</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>org.testng</groupId>
			<artifactId>testng</artifactId>
			<version>7.7.0</version>
			<scope>compile</scope>
		</dependency>

		<dependency>
			<groupId>org.apache.ant</groupId>
			<artifactId>ant-compress</artifactId>
			<version>1.2</version>
		</dependency>

	</dependencies>


	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.8.0</version>
				<configuration>
					<release>11</release>
					<source>11</source>
					<target>11</target>
				</configuration>
			</plugin>
			<plugin>
				<groupId>io.qameta.allure</groupId>
				<artifactId>allure-maven</artifactId>
				<version>2.10.0</version>
				<configuration>
					<reportVersion>2.14.0</reportVersion>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<version>3.0.0</version>
				<configuration>
					<testFailureIgnore>true</testFailureIgnore>
					<!-- Remove rerunFailingTestsCount to allow proper testng-failed.xml generation -->
					<!-- Configurable TestNG suite files -->
					<suiteXmlFiles>
						<suiteXmlFile>${suiteXmlFile}</suiteXmlFile>
					</suiteXmlFiles>
					<!-- Enable TestNG failed suite generation -->
					<properties>
						<property>
							<name>usedefaultlisteners</name>
							<value>true</value>
						</property>
					</properties>
				</configuration>
				<dependencies>
					<dependency>
						<groupId>org.aspectj</groupId>
						<artifactId>aspectjweaver</artifactId>
						<version>${aspectj.version}</version>
					</dependency>
				</dependencies>


			</plugin>
		</plugins>
	</build>

	<properties>
		<!-- specify which version of Java to build against-->
		<maven.compiler.source>18</maven.compiler.source>
		<maven.compiler.target>18</maven.compiler.target>
		<aspectj.version>1.9.19</aspectj.version>
	</properties>

</project>