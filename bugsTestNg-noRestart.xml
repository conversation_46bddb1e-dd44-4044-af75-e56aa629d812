<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE suite SYSTEM "https://testng.org/testng-1.0.dtd" >

<suite name="Safe Watch Filtering"  verbose="5" preserve-order="true" >
    <parameter name="browserType" value="Chrome" />
    <parameter name="Application" value="SWF" />
    <test name="SWF Regression Test - Bugs Suite">
        <parameter name="loginType" value="filtering" />
        <classes >
            
            <class name="eastnets.screening.regression.bugs.restartServicesNotRequired.Tkt77840Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesNotRequired.Tkt84163Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesNotRequired.Tkt87047Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesNotRequired.Tkt87770Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesNotRequired.Tkt89627Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesNotRequired.Tkt89687Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesNotRequired.Tkt90302Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesNotRequired.Tkt90446Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesNotRequired.Tkt91031Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesNotRequired.Tkt91459Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesNotRequired.Tkt91527Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesNotRequired.Tkt91894Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesNotRequired.Tkt92735Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesNotRequired.Tkt93444Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesNotRequired.Tkt94456Test"/>
            <class name="eastnets.screening.regression.bugs.restartServicesNotRequired.Tkt94486Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesNotRequired.Tkt94729Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesNotRequired.Tkt94952Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesNotRequired.Tkt95112Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesNotRequired.Tkt95666Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesNotRequired.Tkt95849Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesNotRequired.Tkt95932Test"/>
            <class name="eastnets.screening.regression.bugs.restartServicesNotRequired.Tkt96002Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesNotRequired.Tkt_77995_Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesNotRequired.Tkt_78799_Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesNotRequired.Tkt_91115_Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesNotRequired.Tkt_92732_Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesNotRequired.Tkt_93156_Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesNotRequired.Tkt_93418_Test" />
            <class name="eastnets.screening.regression.bugs.restartServicesNotRequired.Tkt_93989_Test" />


        </classes>
    </test>

</suite>